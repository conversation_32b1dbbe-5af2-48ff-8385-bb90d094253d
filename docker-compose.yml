name: hermes

services:
    caddy-backend:
        image: caddy:2.10.0-alpine@sha256:ae4458638da8e1a91aafffb231c5f8778e964bca650c8a8cb23a7e8ac557aa3c
        ports:
            - '8000:8000'
        volumes:
            - ./docker/local/Caddyfile:/etc/caddy/Caddyfile
            - ./public:/var/www/backend
            - backend-caddy-config:/config
            - backend-caddy-data:/data
        networks: [ hermes_network ]

    backend:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        platform: linux/amd64
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'php-fpm' ]
        depends_on: [ 'db', 'message_queue', 'redis' ]
        networks: [ hermes_network ]
        logging:
            driver: gelf
            options: { gelf-address: 'udp://127.0.0.1:8019' }

    consumer-device-queue:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        platform: linux/amd64
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'symfony', 'run', '--watch=config,src,vendor', 'bin/console', 'messenger:consume', 'device', '-q' ]
        depends_on: [ 'db', 'message_queue', 'redis' ]
        networks: [ hermes_network ]
        logging:
            driver: gelf
            options: { gelf-address: 'udp://127.0.0.1:8019' }

    consumer-amqp-queue:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        platform: linux/amd64
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'symfony', 'run', '--watch=config,src,vendor', 'bin/console', 'messenger:consume', 'amqp', 'external_system', '-q' ]
        depends_on: [ 'db', 'message_queue', 'redis' ]
        networks: [ hermes_network ]
        logging:
            driver: gelf
            options: { gelf-address: 'udp://127.0.0.1:8019' }

    consumer-async-queue:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        platform: linux/amd64
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'symfony', 'run', '--watch=config,src,vendor', 'bin/console', 'messenger:consume', 'async', '-q' ]
        depends_on: [ 'db', 'message_queue', 'redis' ]
        networks: [ hermes_network ]
        logging:
            driver: gelf
            options: { gelf-address: 'udp://127.0.0.1:8019' }

    consumer-domain-schedule:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        platform: linux/amd64
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'symfony', 'run', '--watch=config,src,vendor', 'bin/console', 'messenger:consume', 'scheduler_domain', '-q' ]
        depends_on: [ 'db', 'message_queue', 'redis' ]
        networks: [ hermes_network ]
        logging:
            driver: gelf
            options: { gelf-address: 'udp://127.0.0.1:8019' }

    consumer-sap-common:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        platform: linux/amd64
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'symfony', 'run', '--watch=config,src,vendor', 'bin/console', 'messenger:consume', 'scheduler_sap_common', '-q' ]
        depends_on: [ 'db', 'message_queue', 'redis' ]
        networks: [ hermes_network ]
        logging:
            driver: gelf
            options: { gelf-address: 'udp://127.0.0.1:8019' }

    db:
        image: postgres:16-alpine3.21@sha256:36e8aabaa6fa6037537cff64011fa45a200fe2ba202141b9aca48cff3df7ad42
        env_file:
            - .env
        environment:
            POSTGRES_DB: hermes
            POSTGRES_USER: user
            POSTGRES_PASSWORD: userpwd
            PGDATA: /var/lib/postgresql/data/pgdata
        volumes:
            - database-data:/var/lib/postgresql/data
        ports:
            - '8003:5432'
        networks: [ hermes_network ]

    mailcatcher:
        image: yappabe/mailcatcher@sha256:cc7241dc09fe299f899473cafd96ea68bd8f829485213abcae5b6ced16cfec8a
        ports:
            - '8002:1080'
        networks: [ hermes_network ]

    s3storage:
        image: quay.io/minio/minio:latest@sha256:d249d1fb6966de4d8ad26c04754b545205ff15a62e4fd19ebd0f26fa5baacbc0
        ports:
            - '8004:7550'
            - '8005:7551'
        environment:
            MINIO_ROOT_USER: minio
            MINIO_ROOT_PASSWORD: minio123
        volumes:
            - s3-data:/data:rw
        command:
            - 'server'
            - '/data'
            - '--address'
            - ':7550'
            - '--console-address'
            - ':7551'
        networks: [ hermes_network ]

    message_queue:
        image: rabbitmq:4.1-management@sha256:4c521003d812dd7b33793e2b7e45fbcc323d764b8c3309dfcb0e4c5db30c56ab
        ports:
            - '8006:5672'
            - '8007:15672'
        environment:
            RABBITMQ_DEFAULT_USER: rabbitmq
            RABBITMQ_DEFAULT_PASS: rabbitmq123
        command: rabbitmq-server
        networks: [ hermes_network ]

    wiremock:
        image: holomekc/wiremock-gui:3.13.33@sha256:6c3c7a02468cd6cfade45552d86a6894d5132627c5b4ad25f34d2b1058b8561c
        ports:
            - '8020:8089'
        command:
            '--port 8089 --max-request-journal-entries 1000'
        networks: [ hermes_network ]

    redis:
        image: redis:7.2-alpine3.18@sha256:3ce533b2b057f74b235d1d8697ae08b1b6ff0a5e16827ea6a377b6365693c7ed
        ports:
            - '8012:6379'
        networks: [ hermes_network ]

    centrifugo:
        image: centrifugo/centrifugo:v6@sha256:005dc00656bce75029e9daa62f998e75d3befe55abb90076c5cb3ffcfbbadbd2
        command: centrifugo -c config.json
        environment:
            CENTRIFUGO_HTTP_API_KEY: 'd4d22184-41e9-4da5-96e2-9ffb87961746'
            CENTRIFUGO_CLIENT_TOKEN_HMAC_SECRET_KEY: '747891fb-c4bd-4667-9e7b-1381b1a1735f'
            CENTRIFUGO_CLIENT_TOKEN_AUDIENCE: 'centrifugo'
            CENTRIFUGO_ADMIN_PASSWORD: 'password'
            CENTRIFUGO_ADMIN_ENABLED: 'true'
            CENTRIFUGO_ADMIN_SECRET: '36011f74-1f12-44f8-87b5-03a535098018'
        ports:
            - 8016:8000
        ulimits:
            nofile:
                soft: 65535
                hard: 65535
        configs:
            -   source: centrifugo-config.json
                target: /centrifugo/config.json
                mode: 0444 # Thanks for breaking it - https://github.com/docker/compose/commit/b6f313b8a5b771545f4af9d0f07ae43dbcade2ef
        networks: [ hermes_network ]

    sftp:
        image: atmoz/sftp:alpine@sha256:e1782520938a6ac0762959bd22c8a0f93a3b6547a92570cc05ea0ab02272ff79
        environment:
            SFTP_USERS: 'sap-es:qwerty:::in,out dako-user:qwerty:::dako'
        command:
            - /bin/sh
            - -c
            - 'ulimit -n 65536 && exec /entrypoint'
        volumes:
            - sftp-data:/home/<USER>
        ports:
            - '8017:22'
        networks: [ hermes_network ]

    blackfire:
        image: blackfire/blackfire:2@sha256:e02be50a6b8ad9908e8d49c4fb547d1c1fa4f22305684baacd03293c4e7d6f29
        env_file: [ { path: .env.local, required: false } ]
        environment:
            BLACKFIRE_LOG_LEVEL: '2'
            BLACKFIRE_LOG_FILE: 'stderr'
            BLACKFIRE_MEMORY_LIMIT: '256'
        networks: [ hermes_network ]

    seq:
        image: datalust/seq:latest@sha256:8bc482dff84f76e8f2c04e4ed3e5cc259ecd5422d6b23435865fe031d039336c
        ports: [ "8018:80" ]
        volumes: [ seq_data:/data ]
        environment:
            ACCEPT_EULA: 'Y'
            SEQ_FIRSTRUN_NOAUTHENTICATION: 'True'
        networks: [ hermes_network ]

    seq-gelf:
        image: datalust/seq-input-gelf:latest@sha256:e11e70aee5fdc9971c3ee51658f77533bc39e36292511a5b2dd13d72232740a3
        depends_on: [ seq ]
        ports: [ "8019:12201/udp" ]
        environment:
            SEQ_ADDRESS: 'http://seq:5341'
        networks: [ hermes_network ]

volumes:
    s3-data:
    database-data:
    backend-caddy-data:
    backend-caddy-config:
    sftp-data:
    seq_data:

networks:
    hermes_network:
        name: hermes_network

configs:
    centrifugo-config.json:
        content: |
            {
                "client": {
                    "allowed_origins": [
                        "*"
                    ],
                    "disallow_anonymous_connection_tokens": true,
                    "user_connection_limit": 10,
                    "connection_limit": 5000,
                    "connection_rate_limit": 100
                },
                "channel": {
                    "namespaces": [
                        {
                            "name": "device",
                            "history_size": 1000,
                            "history_ttl": "14h",
                            "presence": true,
                            "force_positioning": true,
                            "force_recovery": true,
                            "force_recovery_mode": "stream",
                            "allow_subscribe_for_client": false,
                            "allow_subscribe_for_anonymous": false,
                            "allow_history_for_subscriber": true,
                            "allow_user_limited_channels": true
                        },
                        {
                            "name": "branch",
                            "history_size": 1000,
                            "history_ttl": "14h",
                            "presence": true,
                            "force_positioning": true,
                            "force_recovery": true,
                            "force_recovery_mode": "stream",
                            "allow_subscribe_for_client": false,
                            "allow_subscribe_for_anonymous": false,
                            "allow_history_for_subscriber": true,
                            "allow_user_limited_channels": false
                        },
                        {
                            "name": "country",
                            "history_size": 1000,
                            "history_ttl": "14h",
                            "presence": true,
                            "force_positioning": true,
                            "force_recovery": true,
                            "force_recovery_mode": "stream",
                            "allow_subscribe_for_client": false,
                            "allow_subscribe_for_anonymous": false,
                            "allow_history_for_subscriber": true,
                            "allow_user_limited_channels": false
                        }
                    ]
                }
            }
