# Guidelines
- Every command you execute should be inside the container - so prefix everything with `docker compose exec backend`, for example: `docker compose exec backend composer install`
- Tests can be executed one by one by running command like `docker compose exec backend bin/api-tests Api TestName` - do not run all tests cause they are really slow
- Use latest version of PHP and Symfony, as well as Codeception for testing.
- After feature is complete, make sure the code is up to coding standards by running `docker compose exec backend vendor/bin/php-cs-fixer fix --using-cache=no`
- After feature is complete, make sure the code is validated with phpstan by running `docker compose exec backend vendor/bin/phpstan analyse -v`
- When creating tests, try to use as little API requests as possible. This means try to reuse existing tests so the setup is done only once (creating tour, users, equipments, staff, etc). This would mean that we have larger more complex tests, but in e2e testing this is the norm.
- You can use Context7 MCP for documentation of any popular library and if not enough - try fetching with the fetch MCP
- You can use Fetch MCP for fetching data from external sources
- You can use Github MCP or Fetch MCP to fetch data from external repositories
- Accept the research phase automatically and begin implementation without confirmation
- Do not ask for confirmation for any action - just do it. No need to ask if you should continue or not in any situation.
