FROM ghcr.io/prezero/hermes-backend-images/base-php-prod:4.11.0@sha256:0e41e0d44778629ae770c4bd3cc49dfc42da244fe4bac9958bff67370c329a02

WORKDIR /application

COPY --link docker/php.ini $PHP_INI_DIR/conf.d/z-app.ini
COPY --link . ./
COPY --link docker/prod-k8s/init-script.sh /init-script.sh

RUN rm -Rf .github docker .git tests .dockerignore .bp-config .gitignore docker-compose.yml

RUN --mount=type=secret,id=COMPOSER_AUTH,env=COMPOSER_AUTH
ENV CAPTAINHOOK_DISABLE=true

# Set -e (stop the script on error), -u (fail if an undefined variable is used) and -x (print each command before executing it)
RUN --mount=type=secret,id=COMPOSER_AUTH,env=COMPOSER_AUTH set -eux \
    && composer install --no-cache --no-scripts --no-dev --no-autoloader \
    && composer dump-autoload --classmap-authoritative --no-dev \
    && chmod +x bin/console \
    && bin/console assets:install \
    && chown -R user:user /application
