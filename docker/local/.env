# Hermes backend
HERMES_BACKEND_URL=http://caddy-backend:8000

# xDebug
PHP_IDE_CONFIG="serverName=app"

# Symfony
APP_ENV=localdev

# Database
DATABASE_URI='pgsql://user:userpwd@db:5432/hermes'

# RabbitMQ
MESSENGER_TRANSPORT_DSN=amqp://rabbitmq:rabbitmq123@message_queue:5672

# Redis
REDIS_URL='redis://redis:6379'

# Keycloak
KEYCLOAK_URL='http://host.docker.internal:11000'
KEYCLOAK_REALM='hermes'
KEYCLOAK_CLIENT_ID='hermes-app'
KEYCLOAK_PORTAL_CLIENT_ID='hermes-portal'
KEYCLOAK_ISSUER='http://host.docker.internal:11000/realms/hermes'

# S3 compatible storage
S3_ACCESS_KEY=minio
S3_SECRET_KEY=minio123
S3_ENDPOINT_URL=http://s3storage:7550
S3_BUCKET_NAME=test-bucket

# SAP Germany
SAP_GERMANY_INGEST_USERNAME=ingest-sap-de
SAP_GERMANY_INGEST_PASSWORD=ingest-sap-de
SAP_GERMANY_URL=http://wiremock:8089/sap-germany/
SAP_GERMANY_DOCUMENT_URL=http://wiremock:8089/sap-germany/
SAP_GERMANY_USERNAME=sap-de
SAP_GERMANY_PASSWORD=sap-de
GERMANY_VIR_ENABLED=true
GERMANY_VIR_URL='http://wiremock:8089/vir-germany/'
GERMANY_VIR_USERNAME=germany-vir
GERMANY_VIR_PASSWORD=germany-vir
GERMANY_VIR_INGEST_USERNAME=germany-vir-ingest
GERMANY_VIR_INGEST_PASSWORD=germany-vir-ingest

# IOT
IOT_URL=http://wiremock:8089/iot/
IOT_USERNAME=iot
IOT_PASSWORD=iot
IOT_ENABLED=true

# SAP Netherlands
SAP_NETHERLANDS_INGEST_USERNAME=ingest-sap-nl
SAP_NETHERLANDS_INGEST_PASSWORD=ingest-sap-nl
SAP_NETHERLANDS_USERNAME=sap-nl
SAP_NETHERLANDS_PASSWORD=sap-nl
SAP_NETHERLANDS_URL=http://wiremock:8089/sap-netherlands/
NETHERLANDS_VIR_ENABLED=true
NETHERLANDS_VIR_URL='http://wiremock:8089/vir-netherlands/'
NETHERLANDS_VIR_USERNAME=netherlands-vir
NETHERLANDS_VIR_PASSWORD=netherlands-vir
NETHERLANDS_VIR_INGEST_USERNAME=netherlands-vir-ingest
NETHERLANDS_VIR_INGEST_PASSWORD=netherlands-vir-ingest

# SAP Spain
SAP_SPAIN_INGEST_USERNAME=ingest-sap-es
SAP_SPAIN_INGEST_PASSWORD=ingest-sap-es
SAP_SPAIN_URL=http://wiremock:8089/sap-spain/
SAP_SPAIN_USERNAME=sap-es
SAP_SPAIN_PASSWORD=sap-es
SAP_SPAIN_FTP_HOST=sftp
SAP_SPAIN_FTP_PORT=22
SAP_SPAIN_FTP_USERNAME=sap-es
SAP_SPAIN_FTP_PASSWORD=qwerty
SAP_SPAIN_FTP_ROOT_PATH=/
SAP_SPAIN_FTP_IN_PATH=in/
SAP_SPAIN_FTP_OUT_PATH=out/
SPAIN_VIR_ENABLED=true
SPAIN_VIR_URL='http://wiremock:8089/vir-spain/'
SPAIN_VIR_USERNAME=spain-vir
SPAIN_VIR_PASSWORD=spain-vir
SPAIN_VIR_INGEST_USERNAME=spain-vir-ingest
SPAIN_VIR_INGEST_PASSWORD=spain-vir-ingest

# Staff Password
STAFF_PASSWORD_LENGTH=5
STAFF_PASSWORD_CHARS=abcdefghjkmnpqrstuvwxyz23456789
STAFF_PASSWORD_EMAIL_FALLBACK=<EMAIL>
STAFF_PASSWORD_EMAIL_GERMANY=0
STAFF_PASSWORD_EMAIL_NETHERLANDS=0
STAFF_PASSWORD_EMAIL_LUXEMBOURG=0
STAFF_PASSWORD_EMAIL_SPAIN=0

# NavigationToken
HERE_TOKEN=abc

# Geotab
GEOTAB_USERNAME='<EMAIL>'
GEOTAB_PASSWORD='Prezero1'
GEOTAB_DATABASE='telefonica_prezero'
GEOTAB_URL='http://wiremock:8089/geotab/'
# GEOTAB_URL='https://my.geotab.com'

# DAKO
DAKO_USERNAME=testuser
DAKO_PASSWORD=test
DAKO_URL='http://wiremock:8089/dako/'

#DAKO_USERNAME=D.PREZERO.32457.CC
#DAKO_PASSWORD=xxxxxxxxx
#DAKO_URL='https://api.tachoweb.eu/Rdl.rest/v2/'

# TACHOPLUS UPLOADS
TACHOPLUS_FTP_HOST=sftp
TACHOPLUS_FTP_PORT=22
TACHOPLUS_FTP_USERNAME=dako-user
TACHOPLUS_FTP_PASSWORD=qwerty
TACHOPLUS_FTP_PATH=/dako/

# Blackfire
BLACKFIRE_AGENT_SOCKET=tcp://blackfire:8307
BLACKFIRE_LOG_LEVEL: '2'
BLACKFIRE_LOG_FILE: '/tmp/blackfire-probe.log'

# Misc
ALLOW_FIXTURE_RESET_ENDPOINT=1
STDOUT_LOG_LEVEL=debug
PHP_CS_FIXER_IGNORE_ENV=1

# Mobile app releases
MOBILE_APP_RELEASES_USERNAME=release-user
MOBILE_APP_RELEASES_PASSWORD=release-pass
