FROM ghcr.io/prezero/hermes-backend-images/base-php-dev:4.11.0@sha256:8d87dc8f39efd35da7ce176a531abe1f5816bc51990f08177ee986e7cdfb5674

ENV XDEBUG_MODE=debug

# Install Symfony CLI
SHELL ["/bin/bash", "-o", "pipefail", "-c"]
RUN wget -q https://get.symfony.com/cli/installer -O - | bash \
    && mv /root/.symfony5/bin/symfony /usr/local/bin/symfony \
    && chmod a+x /root \
    && chmod a+rwx /root/.symfony5
