# Hermes Backend

## Setup development environment

### Hermes IAM Prerequisite

- You need to run Hermes IAM project first, because it's used for hosting keycloak and IAM API

See the detailed instructions here: https://github.com/prezero/hermes-iam

### Project Setup

1. Clone the repo
    ```sh
    <NAME_EMAIL>:prezero/hermes-sf.git
    ```
2. The project makes usage of private PreZero GitHub npm package registry and composer bundles. For this purpose an GitHub personal access token (PAT) with full repo scopes and additional read:packages scope is needed, see this link for generating a new token: https://docs.github.com/en/enterprise-cloud@latest/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token
3. In `docker/local` folder create new `.env.local` file, add line `PREZERO_NPM_TOKEN=<YOURTOKEN>`
4. For access to private container registry authenticate to ghcr.io with PAT token as password
    ```sh
    docker login ghcr.io -u <YOURGITHUBUSER>
    ```
5. Run `docker-compose up -d` to start docker containers\

6. Install composer packages (Attention: On first run composer asks for GitHub personal access token to download private PreZero bundles. Reuse token generated in step 3 to grant access. Token will be stored for later reuse inside container.)
    ```sh
    docker-compose exec backend composer install
    ```
7. Create database, run migrations and load fixtures
    ```sh
    docker-compose exec backend sh reload-fixtures.sh
    ```

Application is accessible on host machine port 8000 (http://localhost:8000)

S3 compatible storage UI is accessible on host machine port 8005 (http://localhost:8005).
Credentials are in `docker-compose.yml` file and `.env.dev`.

RabbitMQ UI is accessible at http://localhost:8007.
Credentials are in `docker-compose.yml` file and `.env.dev`.

## Setup blackfire locally

1. Create .env.local file if it doesn't exist
2. Add the blackfire environment variables from https://app.blackfire.io/my/settings/credentials (ask someone on the team for credentials)
```dotenv
BLACKFIRE_SERVER_ID=xxx
BLACKFIRE_SERVER_TOKEN=xxx
BLACKFIRE_CLIENT_ID=xxx
BLACKFIRE_CLIENT_TOKEN=xxx
```

## Retrying failed amqp/device messages

Run the following command to iterate the messages one by one and choose whether to retry, delete or skip them
```sh
docker-compose exec backend bin/console messenger:failed:retry --transport=dead_letter
```

## Tests

### Run all tests

```bash
docker-compose exec backend bin/api-tests
```

### Generate test groups split by execution time for parallel execution

```bash
docker-compose exec backend vendor/bin/robo tests:split-by-time
```

### Run tests in parallel based on time split done above

```bash
docker-compose exec backend vendor/bin/robo tests
```
---
**!! IMPORTANT !!** If you create new test files and want them to be included in the parallel execution you have to:
1. Run the all tests without splitting (first command above)
2. Run the split command
3. Now you can run the tests in parallel and the new test files will be included

## Miscellaneous

### Fully reset local docker environment

**!!WARNING!!** the following will kill ***ALL*** containers on the machine and remove all their volumes, networks and images. Use with caution.

```bash
docker kill $(docker ps -q)
docker system prune -af && docker volume prune -af && docker network prune -f
docker compose up -d --build
```

### User cleanup by prefix

**IMPORTANT** This commands deletes users and all their associated data, including tours, sessions, messages, etc. Use with caution.
This also deletes the user from Keycloak.

```bash
STDOUT_LOG_LEVEL=error bin/console app:database:delete-user test-staff
```

Expected output:

```text
Are you sure you want to delete users with prefix "test-staff"? (yes/no) yes
Deleting users with prefix "test-staff" in tenant "pzl"...
No more users found with the given prefix.
Deleting users with prefix "test-staff" in tenant "pz"...
Deleting user "pztest-staff-ext-id-67fd03ea679b5"...
Deleting user "pztest-staff-ext-id-67fd03ebae12c"...
Deleting user "pztest-staff-ext-id-67fd050da1c7a"...
No more users found with the given prefix.
Deleting users with prefix "test-staff" in tenant "pzn"...
Deleting user "pzntest-staff-ext-id-67fd04e5c330c"...
Deleting user "pzntest-staff-ext-id-67fd04eb5a153"...
Deleting user "pzntest-staff-ext-id-67fd04eeebca8"...
No more users found with the given prefix.
Deleting users with prefix "test-staff" in tenant "pze"...
Deleting user "pzetest-staff-ext-id-67fd04f1a4377"...
Deleting user "pzetest-staff-ext-id-67fd04f7ec22b"...
No more users found with the given prefix.
```
