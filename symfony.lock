{"artprima/prometheus-metrics-bundle": {"version": "1.18", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.14", "ref": "9522bce04594caddf6c61ec78c88fea5f5ffa121"}, "files": ["config/packages/prometheus_metrics.yaml", "config/routes/metrics.yaml"]}, "codeception/codeception": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "0d213956834c5652a34e4bf9456ef26119132a8a"}, "files": ["codeception.yml", "tests/Acceptance.suite.yml", "tests/Acceptance/.gitignore", "tests/Functional.suite.yml", "tests/Functional/.gitignore", "tests/Support/AcceptanceTester.php", "tests/Support/Data/.gitignore", "tests/Support/FunctionalTester.php", "tests/Support/Helper/.gitignore", "tests/Support/UnitTester.php", "tests/Support/_generated/.gitignore", "tests/Unit.suite.yml", "tests/Unit/.gitignore", "tests/_output/.gitignore"]}, "doctrine/annotations": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.12", "ref": "7b1b0b637b337f6beb895589948cd119da705524"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "friendsofphp/php-cs-fixer": {"version": "3.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "hautelook/alice-bundle": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.2", "ref": "c84e4f2b9d7f436d7d52e8369230b393367607ec"}, "files": ["config/packages/hautelook_alice.yaml", "fixtures/.gitignore"]}, "liip/monitor-bundle": {"version": "2.21", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.6", "ref": "02c7cd93304dead890f9042fc2670dd9b0e0c091"}, "files": ["config/packages/monitor.yaml"]}, "nelmio/alice": {"version": "3.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "42b52d2065dc3fde27912d502c18ca1926e35ae2"}, "files": ["config/packages/nelmio_alice.yaml"]}, "nelmio/cors-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nyholm/psr7": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "4a8c0345442dcca1d8a2c65633dcf0285dd5a5a2"}, "files": ["config/packages/nyholm_psr7.yaml"]}, "phpstan/phpstan": {"version": "1.11", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "prezero/api-bundle": {"version": "dev-master"}, "sensio/framework-extra-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "snc/redis-bundle": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "36b3d9ab65be62de4e085a25e6ca899efa96b1f3"}, "files": ["config/packages/snc_redis.yaml"]}, "symfony/console": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "6356c19b9ae08e7763e4ba2d9ae63043efc75db5"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/lock": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/mailer": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "df66ee1f226c46f01e85c29c2f7acce0596ba35a"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.48", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["config/packages/notifier.yaml"]}, "symfony/phpunit-bridge": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/property-info": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "dae70df71978ae9226ae915ffd5fad817f5ca1f7"}, "files": ["config/packages/property_info.yaml"]}, "symfony/routing": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/scheduler": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "caea3c928ee9e1b21288fd76aef36f16ea355515"}, "files": ["src/Schedule.php"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/translation": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}, "files": []}, "symfony/validator": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webapp-pack": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "7d5c5e282f7e2c36a2c3bbb1504f78456c352407"}, "files": ["config/packages/messenger.yaml"]}, "symfony/workflow": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["config/packages/workflow.yaml"]}, "theofidry/alice-data-fixtures": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fe5a50faf580eb58f08ada2abe8afbd2d4941e05"}}, "twig/extra-bundle": {"version": "v3.5.0"}, "vuryss/doctrine-lazy-json-odm": {"version": "dev-master"}}