tests:
  'Doctrine annotations should be cached in production':
    path:
      - '/.*'
    methods:
      - ANY
    assertions:
      - { label: null, expression: 'metrics.doctrine.annotations.parsed.count + metrics.doctrine.annotations.read.count + metrics.doctrine.entities.metadata.count == 0' }
    description: null
    exclude: {  }

  'Doctrine DQL statements should be cached in production':
    path:
      - '/.*'
    methods:
      - ANY
    assertions:
      - { label: null, expression: 'metrics.doctrine.dql.parsed.count - metrics.doctrine.paginator.fetch_join_collection.count == 0' }
    description: null
    exclude: { }

recommendations:
  php.quality.disable_assert_active:
    enabled: false
