<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250116140447 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accessible_task_group ADD trackable BOOLEAN DEFAULT false NOT NULL');
        $this->addSql('ALTER TABLE default_task_group ADD trackable BOOLEAN DEFAULT false NOT NULL');
        $this->addSql('ALTER TABLE task_group ADD trackable BOOLEAN DEFAULT false NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE default_task_group DROP trackable');
        $this->addSql('ALTER TABLE accessible_task_group DROP trackable');
        $this->addSql('ALTER TABLE task_group DROP trackable');
    }
}
