<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250102152344 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE message DROP CONSTRAINT fk_b6bd307f517fe9fe');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT fk_b6bd307f9033212a');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT fk_b6bd307f99049ece');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT fk_b6bd307fb03a8386');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT fk_b6bd307fd4d57cd');
        $this->addSql('ALTER TABLE mobile_device DROP CONSTRAINT fk_760b5d849033212a');
        $this->addSql('ALTER TABLE mobile_device DROP CONSTRAINT fk_760b5d8499049ece');
        $this->addSql('ALTER TABLE mobile_device DROP CONSTRAINT fk_760b5d84b03a8386');
        $this->addSql('DROP TABLE message');
        $this->addSql('DROP TABLE mobile_device');
        $this->addSql('ALTER TABLE mastertour_progress ALTER date TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
        $this->addSql('ALTER TABLE mastertour_progress ALTER date SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE message (id UUID NOT NULL, staff_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, description TEXT NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_b6bd307fd4d57cd ON message (staff_id)');
        $this->addSql('CREATE INDEX idx_b6bd307fb03a8386 ON message (created_by_id)');
        $this->addSql('CREATE INDEX idx_b6bd307f99049ece ON message (modified_by_id)');
        $this->addSql('CREATE INDEX idx_b6bd307f9033212a ON message (tenant_id)');
        $this->addSql('CREATE INDEX idx_b6bd307f7065677c ON message (modified_at)');
        $this->addSql('CREATE INDEX idx_b6bd307f517fe9fe ON message (equipment_id)');
        $this->addSql('CREATE TABLE mobile_device (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_760b5d84b03a8386 ON mobile_device (created_by_id)');
        $this->addSql('CREATE INDEX idx_760b5d8499049ece ON mobile_device (modified_by_id)');
        $this->addSql('CREATE INDEX idx_760b5d849033212a ON mobile_device (tenant_id)');
        $this->addSql('CREATE INDEX idx_760b5d847065677c ON mobile_device (modified_at)');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT fk_b6bd307f517fe9fe FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT fk_b6bd307f9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT fk_b6bd307f99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT fk_b6bd307fb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT fk_b6bd307fd4d57cd FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_device ADD CONSTRAINT fk_760b5d849033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_device ADD CONSTRAINT fk_760b5d8499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_device ADD CONSTRAINT fk_760b5d84b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_progress ALTER date TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
        $this->addSql('ALTER TABLE mastertour_progress ALTER date DROP NOT NULL');
    }
}
