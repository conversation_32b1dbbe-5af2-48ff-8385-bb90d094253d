<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240719075117 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE device_message (id UUID NOT NULL, thread_id UUID NOT NULL, sender_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, message_from VARCHAR(20) NOT NULL, is_read BOOLEAN NOT NULL, text TEXT NOT NULL, attachments JSON NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_3C0BE9E2E2904019 ON device_message (thread_id)');
        $this->addSql('CREATE INDEX IDX_3C0BE9E2F624B39D ON device_message (sender_id)');
        $this->addSql('CREATE INDEX IDX_3C0BE9E2B03A8386 ON device_message (created_by_id)');
        $this->addSql('CREATE INDEX IDX_3C0BE9E299049ECE ON device_message (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_3C0BE9E29033212A ON device_message (tenant_id)');
        $this->addSql('COMMENT ON COLUMN device_message.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_message.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE device_message_thread (id UUID NOT NULL, branch_id UUID NOT NULL, staff_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, has_unread_messages_for_branch BOOLEAN NOT NULL, has_unread_messages_for_device BOOLEAN NOT NULL, subject VARCHAR(100) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A04A207CDCD6CC49 ON device_message_thread (branch_id)');
        $this->addSql('CREATE INDEX IDX_A04A207CD4D57CD ON device_message_thread (staff_id)');
        $this->addSql('CREATE INDEX IDX_A04A207C517FE9FE ON device_message_thread (equipment_id)');
        $this->addSql('CREATE INDEX IDX_A04A207CB03A8386 ON device_message_thread (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A04A207C99049ECE ON device_message_thread (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A04A207C9033212A ON device_message_thread (tenant_id)');
        $this->addSql('COMMENT ON COLUMN device_message_thread.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_message_thread.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE device_message ADD CONSTRAINT FK_3C0BE9E2E2904019 FOREIGN KEY (thread_id) REFERENCES device_message_thread (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message ADD CONSTRAINT FK_3C0BE9E2F624B39D FOREIGN KEY (sender_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message ADD CONSTRAINT FK_3C0BE9E2B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message ADD CONSTRAINT FK_3C0BE9E299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message ADD CONSTRAINT FK_3C0BE9E29033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ADD CONSTRAINT FK_A04A207CDCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ADD CONSTRAINT FK_A04A207CD4D57CD FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ADD CONSTRAINT FK_A04A207C517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ADD CONSTRAINT FK_A04A207CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ADD CONSTRAINT FK_A04A207C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ADD CONSTRAINT FK_A04A207C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE device_message DROP CONSTRAINT FK_3C0BE9E2E2904019');
        $this->addSql('ALTER TABLE device_message DROP CONSTRAINT FK_3C0BE9E2F624B39D');
        $this->addSql('ALTER TABLE device_message DROP CONSTRAINT FK_3C0BE9E2B03A8386');
        $this->addSql('ALTER TABLE device_message DROP CONSTRAINT FK_3C0BE9E299049ECE');
        $this->addSql('ALTER TABLE device_message DROP CONSTRAINT FK_3C0BE9E29033212A');
        $this->addSql('ALTER TABLE device_message_thread DROP CONSTRAINT FK_A04A207CDCD6CC49');
        $this->addSql('ALTER TABLE device_message_thread DROP CONSTRAINT FK_A04A207CD4D57CD');
        $this->addSql('ALTER TABLE device_message_thread DROP CONSTRAINT FK_A04A207C517FE9FE');
        $this->addSql('ALTER TABLE device_message_thread DROP CONSTRAINT FK_A04A207CB03A8386');
        $this->addSql('ALTER TABLE device_message_thread DROP CONSTRAINT FK_A04A207C99049ECE');
        $this->addSql('ALTER TABLE device_message_thread DROP CONSTRAINT FK_A04A207C9033212A');
        $this->addSql('DROP TABLE device_message');
        $this->addSql('DROP TABLE device_message_thread');
    }
}
