<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240917070129 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE point_of_interest (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, longitude DOUBLE PRECISION NOT NULL, latitude DOUBLE PRECISION NOT NULL, icon VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E67AD359B03A8386 ON point_of_interest (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E67AD35999049ECE ON point_of_interest (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E67AD3599033212A ON point_of_interest (tenant_id)');
        $this->addSql('COMMENT ON COLUMN point_of_interest.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN point_of_interest.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE point_of_interest ADD CONSTRAINT FK_E67AD359B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE point_of_interest ADD CONSTRAINT FK_E67AD35999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE point_of_interest ADD CONSTRAINT FK_E67AD3599033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE point_of_interest DROP CONSTRAINT FK_E67AD359B03A8386');
        $this->addSql('ALTER TABLE point_of_interest DROP CONSTRAINT FK_E67AD35999049ECE');
        $this->addSql('ALTER TABLE point_of_interest DROP CONSTRAINT FK_E67AD3599033212A');
        $this->addSql('DROP TABLE point_of_interest');
    }
}
