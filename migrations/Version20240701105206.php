<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701105206 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE navigation_token DROP CONSTRAINT fk_e10b31feb03a8386');
        $this->addSql('ALTER TABLE navigation_token DROP CONSTRAINT fk_e10b31fe99049ece');
        $this->addSql('DROP TABLE navigation_token');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE navigation_token (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, expiry TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, token VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_e10b31fe99049ece ON navigation_token (modified_by_id)');
        $this->addSql('CREATE INDEX idx_e10b31feb03a8386 ON navigation_token (created_by_id)');
        $this->addSql('COMMENT ON COLUMN navigation_token.expiry IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN navigation_token.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN navigation_token.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE navigation_token ADD CONSTRAINT fk_e10b31feb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE navigation_token ADD CONSTRAINT fk_e10b31fe99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
