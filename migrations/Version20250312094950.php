<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250312094950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Spalte mit Standardwert hinzufügen
        $this->addSql("ALTER TABLE tour ADD tracking_protection VARCHAR(20) NOT NULL DEFAULT 'by_taskgroup'");
        $this->addSql("ALTER TABLE tour_data_config ADD tracking_protection VARCHAR(20) NOT NULL DEFAULT 'by_taskgroup'");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tour DROP COLUMN tracking_protection');
        $this->addSql('ALTER TABLE tour_data_config DROP COLUMN tracking_protection');
    }
}
