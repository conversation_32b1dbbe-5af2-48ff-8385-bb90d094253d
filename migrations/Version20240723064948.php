<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240723064948 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "user" ADD staff_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE "user" ADD user_setting_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE "user" ALTER last_login TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
        $this->addSql('COMMENT ON COLUMN "user".last_login IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE "user" ADD CONSTRAINT FK_8D93D649D4D57CD FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "user" ADD CONSTRAINT FK_8D93D64949527B9E FOREIGN KEY (user_setting_id) REFERENCES user_setting (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8D93D649D4D57CD ON "user" (staff_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8D93D64949527B9E ON "user" (user_setting_id)');
        $this->addSql('ALTER TABLE staff ALTER user_id SET NOT NULL');
        $this->addSql('
            UPDATE "user" u
            SET staff_id = s.id
            FROM staff s
            WHERE u.id = s.user_id
        ');
        $this->addSql('
            UPDATE "user" u
            SET user_setting_id = us.id
            FROM user_setting us
            WHERE u.id = us.user_id
        ');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE staff ALTER user_id DROP NOT NULL');
        $this->addSql('ALTER TABLE "user" DROP CONSTRAINT FK_8D93D649D4D57CD');
        $this->addSql('ALTER TABLE "user" DROP CONSTRAINT FK_8D93D64949527B9E');
        $this->addSql('DROP INDEX UNIQ_8D93D649D4D57CD');
        $this->addSql('DROP INDEX UNIQ_8D93D64949527B9E');
        $this->addSql('ALTER TABLE "user" DROP staff_id');
        $this->addSql('ALTER TABLE "user" DROP user_setting_id');
        $this->addSql('ALTER TABLE "user" ALTER last_login TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
        $this->addSql('COMMENT ON COLUMN "user".last_login IS NULL');
    }
}
