<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240513125814 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE accessible_additional_information (id UUID NOT NULL, accessible_task_group_id UUID DEFAULT NULL, accessible_interruption_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence INT NOT NULL, text VARCHAR(255) NOT NULL, also_front_view BOOLEAN NOT NULL, icon VARCHAR(255) DEFAULT NULL, highlight BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5E2C358132C0FDC9 ON accessible_additional_information (accessible_task_group_id)');
        $this->addSql('CREATE INDEX IDX_5E2C3581C7A42F5E ON accessible_additional_information (accessible_interruption_id)');
        $this->addSql('CREATE INDEX IDX_5E2C3581B03A8386 ON accessible_additional_information (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5E2C358199049ECE ON accessible_additional_information (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5E2C35819033212A ON accessible_additional_information (tenant_id)');
        $this->addSql('CREATE INDEX IDX_5E2C35815286D72B ON accessible_additional_information (sequence)');
        $this->addSql('COMMENT ON COLUMN accessible_additional_information.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_additional_information.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_element (id UUID NOT NULL, accessible_task_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, task_master BOOLEAN NOT NULL, task_group_master BOOLEAN NOT NULL, sequence_number INT NOT NULL, required BOOLEAN NOT NULL, reference_type VARCHAR(150) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, type VARCHAR(20) NOT NULL, expected_value VARCHAR(255) DEFAULT NULL, value_string_minlength INT DEFAULT NULL, value_string_max_length INT DEFAULT NULL, placeholder VARCHAR(100) DEFAULT NULL, label VARCHAR(100) DEFAULT NULL, pattern VARCHAR(100) DEFAULT NULL, pattern_error VARCHAR(100) DEFAULT NULL, description TEXT DEFAULT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, value_number_precision INT DEFAULT NULL, value_number_unit VARCHAR(20) DEFAULT NULL, value_min_items INT DEFAULT NULL, value_max_items INT DEFAULT NULL, value_signature_type VARCHAR(20) DEFAULT NULL, value_photo_type VARCHAR(20) DEFAULT NULL, value_photo_max_size INT DEFAULT NULL, value_date_min_date DATE DEFAULT NULL, value_date_max_date DATE DEFAULT NULL, value_time_min_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_time_max_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_date_format VARCHAR(50) DEFAULT NULL, value_time_format VARCHAR(50) DEFAULT NULL, pattern_hint VARCHAR(100) DEFAULT NULL, has_autocomplete BOOLEAN DEFAULT false NOT NULL, show_additional_input BOOLEAN DEFAULT true NOT NULL, checksum VARCHAR(255) DEFAULT NULL, checksum_formula VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_8FBBC0E7434D9F58 ON accessible_element (accessible_task_id)');
        $this->addSql('CREATE INDEX IDX_8FBBC0E7B03A8386 ON accessible_element (created_by_id)');
        $this->addSql('CREATE INDEX IDX_8FBBC0E799049ECE ON accessible_element (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_8FBBC0E79033212A ON accessible_element (tenant_id)');
        $this->addSql('CREATE INDEX IDX_8FBBC0E7F2803B3D ON accessible_element (sequence_number)');
        $this->addSql('COMMENT ON COLUMN accessible_element.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_element_option (id UUID NOT NULL, accessible_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, org_uuid VARCHAR(255) DEFAULT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A677FE765DA5BED0 ON accessible_element_option (accessible_element_id)');
        $this->addSql('CREATE INDEX IDX_A677FE76B03A8386 ON accessible_element_option (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A677FE7699049ECE ON accessible_element_option (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A677FE769033212A ON accessible_element_option (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_element_option.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element_option.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_element_value (id UUID NOT NULL, accessible_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5501E26F5DA5BED0 ON accessible_element_value (accessible_element_id)');
        $this->addSql('CREATE INDEX IDX_5501E26FB03A8386 ON accessible_element_value (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5501E26F99049ECE ON accessible_element_value (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5501E26F9033212A ON accessible_element_value (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_element_value.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element_value.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_interruption (id UUID NOT NULL, tour_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, description TEXT NOT NULL, type VARCHAR(20) NOT NULL, sequence_number INT NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7EE32A9E15ED8D43 ON accessible_interruption (tour_id)');
        $this->addSql('CREATE INDEX IDX_7EE32A9EB03A8386 ON accessible_interruption (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7EE32A9E99049ECE ON accessible_interruption (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7EE32A9E9033212A ON accessible_interruption (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_interruption.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_interruption.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_note (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_8747C5ACB03A8386 ON accessible_note (created_by_id)');
        $this->addSql('CREATE INDEX IDX_8747C5AC99049ECE ON accessible_note (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_8747C5AC9033212A ON accessible_note (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_note.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_note.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_note_relation (id UUID NOT NULL, accessible_note_id UUID NOT NULL, order_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_FE865515E816968B ON accessible_note_relation (accessible_note_id)');
        $this->addSql('CREATE INDEX IDX_FE8655158D9F6D38 ON accessible_note_relation (order_id)');
        $this->addSql('CREATE INDEX IDX_FE865515B03A8386 ON accessible_note_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_FE86551599049ECE ON accessible_note_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_FE8655159033212A ON accessible_note_relation (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_note_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_note_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_task (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(255) DEFAULT NULL, name VARCHAR(255) NOT NULL, repeatable BOOLEAN NOT NULL, sap_action BOOLEAN DEFAULT false NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_1A84E49DB03A8386 ON accessible_task (created_by_id)');
        $this->addSql('CREATE INDEX IDX_1A84E49D99049ECE ON accessible_task (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_1A84E49D9033212A ON accessible_task (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_task.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_task_group (id UUID NOT NULL, accessible_termination_id UUID DEFAULT NULL, accessible_interruption_id UUID DEFAULT NULL, accessible_note_id UUID DEFAULT NULL, location_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, title VARCHAR(255) NOT NULL, sequence_number INT NOT NULL, rule_logic VARCHAR(10) NOT NULL, rule_effect VARCHAR(15) NOT NULL, type VARCHAR(30) NOT NULL, external_id VARCHAR(50) DEFAULT NULL, to_be_done VARCHAR(10) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E8DECB11DC261C5F ON accessible_task_group (accessible_termination_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB11C7A42F5E ON accessible_task_group (accessible_interruption_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB11E816968B ON accessible_task_group (accessible_note_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB1164D218E ON accessible_task_group (location_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB11B03A8386 ON accessible_task_group (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB1199049ECE ON accessible_task_group (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB119033212A ON accessible_task_group (tenant_id)');
        $this->addSql('CREATE INDEX IDX_E8DECB11F2803B3D ON accessible_task_group (sequence_number)');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_task_group_rule (id UUID NOT NULL, key_accessible_task_group_id UUID NOT NULL, accessible_task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F6816A3C64F12DD ON accessible_task_group_rule (key_accessible_task_group_id)');
        $this->addSql('CREATE INDEX IDX_F6816A3C32C0FDC9 ON accessible_task_group_rule (accessible_task_group_id)');
        $this->addSql('CREATE INDEX IDX_F6816A3CB03A8386 ON accessible_task_group_rule (created_by_id)');
        $this->addSql('CREATE INDEX IDX_F6816A3C99049ECE ON accessible_task_group_rule (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_F6816A3C9033212A ON accessible_task_group_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_task_group_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_task_relation (id UUID NOT NULL, accessible_task_id UUID NOT NULL, accessible_task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, rule_logic VARCHAR(10) NOT NULL, rule_effect VARCHAR(15) NOT NULL, optional BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_AE1D63C7434D9F58 ON accessible_task_relation (accessible_task_id)');
        $this->addSql('CREATE INDEX IDX_AE1D63C732C0FDC9 ON accessible_task_relation (accessible_task_group_id)');
        $this->addSql('CREATE INDEX IDX_AE1D63C7B03A8386 ON accessible_task_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_AE1D63C799049ECE ON accessible_task_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_AE1D63C79033212A ON accessible_task_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_AE1D63C7F2803B3D ON accessible_task_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_task_rule (id UUID NOT NULL, key_accessible_task_id UUID NOT NULL, accessible_task_relation_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_3D67A09E8A34C39D ON accessible_task_rule (key_accessible_task_id)');
        $this->addSql('CREATE INDEX IDX_3D67A09EDF7711E6 ON accessible_task_rule (accessible_task_relation_id)');
        $this->addSql('CREATE INDEX IDX_3D67A09EB03A8386 ON accessible_task_rule (created_by_id)');
        $this->addSql('CREATE INDEX IDX_3D67A09E99049ECE ON accessible_task_rule (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_3D67A09E9033212A ON accessible_task_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_task_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_termination (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, text TEXT NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5E2D48B3B03A8386 ON accessible_termination (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5E2D48B399049ECE ON accessible_termination (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5E2D48B39033212A ON accessible_termination (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_termination.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_termination.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_termination_relation (id UUID NOT NULL, accessible_termination_id UUID NOT NULL, tour_id UUID DEFAULT NULL, order_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5F3616F2DC261C5F ON accessible_termination_relation (accessible_termination_id)');
        $this->addSql('CREATE INDEX IDX_5F3616F215ED8D43 ON accessible_termination_relation (tour_id)');
        $this->addSql('CREATE INDEX IDX_5F3616F28D9F6D38 ON accessible_termination_relation (order_id)');
        $this->addSql('CREATE INDEX IDX_5F3616F2B03A8386 ON accessible_termination_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5F3616F299049ECE ON accessible_termination_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5F3616F29033212A ON accessible_termination_relation (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_termination_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_termination_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE additional_information (id UUID NOT NULL, tour_id UUID DEFAULT NULL, order_id UUID DEFAULT NULL, task_group_id UUID DEFAULT NULL, interruption_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence INT NOT NULL, text VARCHAR(255) NOT NULL, also_front_view BOOLEAN NOT NULL, icon VARCHAR(255) DEFAULT NULL, highlight BOOLEAN NOT NULL, is_from_external_source BOOLEAN DEFAULT false NOT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_19D9524D15ED8D43 ON additional_information (tour_id)');
        $this->addSql('CREATE INDEX IDX_19D9524D8D9F6D38 ON additional_information (order_id)');
        $this->addSql('CREATE INDEX IDX_19D9524DBE94330B ON additional_information (task_group_id)');
        $this->addSql('CREATE INDEX IDX_19D9524D42B4F971 ON additional_information (interruption_id)');
        $this->addSql('CREATE INDEX IDX_19D9524DB03A8386 ON additional_information (created_by_id)');
        $this->addSql('CREATE INDEX IDX_19D9524D99049ECE ON additional_information (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_19D9524D9033212A ON additional_information (tenant_id)');
        $this->addSql('CREATE INDEX IDX_19D9524D5286D72B ON additional_information (sequence)');
        $this->addSql('COMMENT ON COLUMN additional_information.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN additional_information.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE additional_service_config (id UUID NOT NULL, branch_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, default_task_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, country VARCHAR(10) NOT NULL, equipment_type VARCHAR(20) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_AD3C9557DCD6CC49 ON additional_service_config (branch_id)');
        $this->addSql('CREATE INDEX IDX_AD3C9557517FE9FE ON additional_service_config (equipment_id)');
        $this->addSql('CREATE INDEX IDX_AD3C95577F3350C3 ON additional_service_config (default_task_id)');
        $this->addSql('CREATE INDEX IDX_AD3C9557B03A8386 ON additional_service_config (created_by_id)');
        $this->addSql('CREATE INDEX IDX_AD3C955799049ECE ON additional_service_config (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_AD3C95579033212A ON additional_service_config (tenant_id)');
        $this->addSql('COMMENT ON COLUMN additional_service_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN additional_service_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE booking (id UUID NOT NULL, tour_id UUID DEFAULT NULL, order_id UUID DEFAULT NULL, task_group_id UUID DEFAULT NULL, task_id UUID DEFAULT NULL, interruption_id UUID DEFAULT NULL, session_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, latitude DOUBLE PRECISION DEFAULT NULL, longitude DOUBLE PRECISION DEFAULT NULL, timestamp TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(20) NOT NULL, status VARCHAR(20) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E00CEDDE15ED8D43 ON booking (tour_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE8D9F6D38 ON booking (order_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDEBE94330B ON booking (task_group_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE8DB60186 ON booking (task_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE42B4F971 ON booking (interruption_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE613FECDF ON booking (session_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDEB03A8386 ON booking (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE99049ECE ON booking (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE9033212A ON booking (tenant_id)');
        $this->addSql('COMMENT ON COLUMN booking.timestamp IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN booking.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN booking.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE branch (id UUID NOT NULL, location_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_BB861B1F64D218E ON branch (location_id)');
        $this->addSql('CREATE INDEX IDX_BB861B1FB03A8386 ON branch (created_by_id)');
        $this->addSql('CREATE INDEX IDX_BB861B1F99049ECE ON branch (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_BB861B1F9033212A ON branch (tenant_id)');
        $this->addSql('COMMENT ON COLUMN branch.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN branch.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE connected_device (id UUID NOT NULL, equipment_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, mac_address VARCHAR(17) NOT NULL, hardware_type VARCHAR(30) NOT NULL, connection_type VARCHAR(30) NOT NULL, pin VARCHAR(30) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F097D203517FE9FE ON connected_device (equipment_id)');
        $this->addSql('CREATE INDEX IDX_F097D203B03A8386 ON connected_device (created_by_id)');
        $this->addSql('CREATE INDEX IDX_F097D20399049ECE ON connected_device (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_F097D2039033212A ON connected_device (tenant_id)');
        $this->addSql('COMMENT ON COLUMN connected_device.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN connected_device.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE customer (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, customer_ext_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_81398E09B03A8386 ON customer (created_by_id)');
        $this->addSql('CREATE INDEX IDX_81398E0999049ECE ON customer (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_81398E099033212A ON customer (tenant_id)');
        $this->addSql('COMMENT ON COLUMN customer.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN customer.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE customer_version (id UUID NOT NULL, customer_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A3C1777D9395C3F3 ON customer_version (customer_id)');
        $this->addSql('CREATE INDEX IDX_A3C1777DB03A8386 ON customer_version (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A3C1777D99049ECE ON customer_version (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A3C1777D9033212A ON customer_version (tenant_id)');
        $this->addSql('COMMENT ON COLUMN customer_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN customer_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_additional_information (id UUID NOT NULL, default_task_group_id UUID DEFAULT NULL, order_type_config_id UUID DEFAULT NULL, tour_data_config_id UUID DEFAULT NULL, default_interruption_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence INT NOT NULL, text VARCHAR(255) NOT NULL, also_front_view BOOLEAN NOT NULL, icon VARCHAR(255) DEFAULT NULL, highlight BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_4F525BDF88B5AF18 ON default_additional_information (default_task_group_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDFF86D9723 ON default_additional_information (order_type_config_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDF3218A5DB ON default_additional_information (tour_data_config_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDFDAE251AD ON default_additional_information (default_interruption_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDFB03A8386 ON default_additional_information (created_by_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDF99049ECE ON default_additional_information (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDF9033212A ON default_additional_information (tenant_id)');
        $this->addSql('CREATE INDEX IDX_4F525BDF5286D72B ON default_additional_information (sequence)');
        $this->addSql('COMMENT ON COLUMN default_additional_information.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_additional_information.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_element (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, data_source VARCHAR(20) DEFAULT NULL, reference_type VARCHAR(150) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, type VARCHAR(20) NOT NULL, expected_value VARCHAR(255) DEFAULT NULL, value_string_minlength INT DEFAULT NULL, value_string_max_length INT DEFAULT NULL, placeholder VARCHAR(100) DEFAULT NULL, label VARCHAR(100) DEFAULT NULL, pattern VARCHAR(100) DEFAULT NULL, pattern_error VARCHAR(100) DEFAULT NULL, description TEXT DEFAULT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, value_number_precision INT DEFAULT NULL, value_number_unit VARCHAR(20) DEFAULT NULL, value_min_items INT DEFAULT NULL, value_max_items INT DEFAULT NULL, value_signature_type VARCHAR(20) DEFAULT NULL, value_photo_type VARCHAR(20) DEFAULT NULL, value_photo_max_size INT DEFAULT NULL, value_date_min_date DATE DEFAULT NULL, value_date_max_date DATE DEFAULT NULL, value_time_min_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_time_max_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_date_format VARCHAR(50) DEFAULT NULL, value_time_format VARCHAR(50) DEFAULT NULL, pattern_hint VARCHAR(100) DEFAULT NULL, has_autocomplete BOOLEAN DEFAULT false NOT NULL, show_additional_input BOOLEAN DEFAULT true NOT NULL, checksum VARCHAR(255) DEFAULT NULL, checksum_formula VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_B3C50F7CB03A8386 ON default_element (created_by_id)');
        $this->addSql('CREATE INDEX IDX_B3C50F7C99049ECE ON default_element (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_B3C50F7C9033212A ON default_element (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_element.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_element.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_element_option (id UUID NOT NULL, default_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_57196966573C7E47 ON default_element_option (default_element_id)');
        $this->addSql('CREATE INDEX IDX_57196966B03A8386 ON default_element_option (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5719696699049ECE ON default_element_option (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_571969669033212A ON default_element_option (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_element_option.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_element_option.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_element_value (id UUID NOT NULL, default_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_EF74B0BE573C7E47 ON default_element_value (default_element_id)');
        $this->addSql('CREATE INDEX IDX_EF74B0BEB03A8386 ON default_element_value (created_by_id)');
        $this->addSql('CREATE INDEX IDX_EF74B0BE99049ECE ON default_element_value (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_EF74B0BE9033212A ON default_element_value (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_element_value.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_element_value.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_interruption (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, description TEXT NOT NULL, type VARCHAR(20) NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_DB87FE6AB03A8386 ON default_interruption (created_by_id)');
        $this->addSql('CREATE INDEX IDX_DB87FE6A99049ECE ON default_interruption (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_DB87FE6A9033212A ON default_interruption (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_interruption.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_interruption.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_interruption_relation (id UUID NOT NULL, tour_data_config_id UUID DEFAULT NULL, equipment_config_id UUID DEFAULT NULL, staff_config_id UUID DEFAULT NULL, default_interruption_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5AAD37FB3218A5DB ON default_interruption_relation (tour_data_config_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FBF6E640FA ON default_interruption_relation (equipment_config_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FB1597D6CC ON default_interruption_relation (staff_config_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FBDAE251AD ON default_interruption_relation (default_interruption_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FBB03A8386 ON default_interruption_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FB99049ECE ON default_interruption_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FB9033212A ON default_interruption_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_5AAD37FBF2803B3D ON default_interruption_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_interruption_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_interruption_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_material (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, legal_number VARCHAR(50) DEFAULT NULL, material_type VARCHAR(20) NOT NULL, material_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7AFA45DBB03A8386 ON default_material (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7AFA45DB99049ECE ON default_material (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7AFA45DB9033212A ON default_material (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_material.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_material.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_material_relation (id UUID NOT NULL, additional_service_config_id UUID NOT NULL, default_material_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D1D15A742CEBFF70 ON default_material_relation (additional_service_config_id)');
        $this->addSql('CREATE INDEX IDX_D1D15A7437FBBFD9 ON default_material_relation (default_material_id)');
        $this->addSql('CREATE INDEX IDX_D1D15A74B03A8386 ON default_material_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D1D15A7499049ECE ON default_material_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D1D15A749033212A ON default_material_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_D1D15A74F2803B3D ON default_material_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_material_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_material_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_note (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_8A4CC112B03A8386 ON default_note (created_by_id)');
        $this->addSql('CREATE INDEX IDX_8A4CC11299049ECE ON default_note (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_8A4CC1129033212A ON default_note (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_note.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_note.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_note_relation (id UUID NOT NULL, default_note_id UUID NOT NULL, order_type_config_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_44F307C4D4685910 ON default_note_relation (default_note_id)');
        $this->addSql('CREATE INDEX IDX_44F307C4F86D9723 ON default_note_relation (order_type_config_id)');
        $this->addSql('CREATE INDEX IDX_44F307C4B03A8386 ON default_note_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_44F307C499049ECE ON default_note_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_44F307C49033212A ON default_note_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_44F307C4F2803B3D ON default_note_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_note_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_note_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(255) DEFAULT NULL, type VARCHAR(150) NOT NULL, name VARCHAR(255) NOT NULL, only_for_assignment BOOLEAN NOT NULL, repeatable BOOLEAN NOT NULL, sap_action BOOLEAN DEFAULT false NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_178FE023B03A8386 ON default_task (created_by_id)');
        $this->addSql('CREATE INDEX IDX_178FE02399049ECE ON default_task (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_178FE0239033212A ON default_task (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_task.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_element_relation (id UUID NOT NULL, default_task_id UUID NOT NULL, default_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, task_master BOOLEAN NOT NULL, task_group_master BOOLEAN NOT NULL, sequence_number INT NOT NULL, required BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A64CE0D77F3350C3 ON default_task_element_relation (default_task_id)');
        $this->addSql('CREATE INDEX IDX_A64CE0D7573C7E47 ON default_task_element_relation (default_element_id)');
        $this->addSql('CREATE INDEX IDX_A64CE0D7B03A8386 ON default_task_element_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A64CE0D799049ECE ON default_task_element_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A64CE0D79033212A ON default_task_element_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_A64CE0D7F2803B3D ON default_task_element_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_task_element_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_element_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_group (id UUID NOT NULL, order_type_config_id UUID DEFAULT NULL, tour_data_config_id UUID DEFAULT NULL, default_interruption_id UUID DEFAULT NULL, default_termination_id UUID DEFAULT NULL, default_note_id UUID DEFAULT NULL, equipment_config_id UUID DEFAULT NULL, staff_config_id UUID DEFAULT NULL, location_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, title VARCHAR(255) NOT NULL, sequence_number INT NOT NULL, rule_logic VARCHAR(10) NOT NULL, rule_effect VARCHAR(15) NOT NULL, type VARCHAR(30) NOT NULL, only_for_manual_creation BOOLEAN DEFAULT false NOT NULL, to_be_done VARCHAR(10) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E2470B86F86D9723 ON default_task_group (order_type_config_id)');
        $this->addSql('CREATE INDEX IDX_E2470B863218A5DB ON default_task_group (tour_data_config_id)');
        $this->addSql('CREATE INDEX IDX_E2470B86DAE251AD ON default_task_group (default_interruption_id)');
        $this->addSql('CREATE INDEX IDX_E2470B862D488B4F ON default_task_group (default_termination_id)');
        $this->addSql('CREATE INDEX IDX_E2470B86D4685910 ON default_task_group (default_note_id)');
        $this->addSql('CREATE INDEX IDX_E2470B86F6E640FA ON default_task_group (equipment_config_id)');
        $this->addSql('CREATE INDEX IDX_E2470B861597D6CC ON default_task_group (staff_config_id)');
        $this->addSql('CREATE INDEX IDX_E2470B8664D218E ON default_task_group (location_id)');
        $this->addSql('CREATE INDEX IDX_E2470B86B03A8386 ON default_task_group (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E2470B8699049ECE ON default_task_group (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E2470B869033212A ON default_task_group (tenant_id)');
        $this->addSql('CREATE INDEX IDX_E2470B86F2803B3D ON default_task_group (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_task_group.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_group.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_group_rule (id UUID NOT NULL, key_default_task_group_id UUID NOT NULL, default_task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_EBC714CF200620BA ON default_task_group_rule (key_default_task_group_id)');
        $this->addSql('CREATE INDEX IDX_EBC714CF88B5AF18 ON default_task_group_rule (default_task_group_id)');
        $this->addSql('CREATE INDEX IDX_EBC714CFB03A8386 ON default_task_group_rule (created_by_id)');
        $this->addSql('CREATE INDEX IDX_EBC714CF99049ECE ON default_task_group_rule (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_EBC714CF9033212A ON default_task_group_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_task_group_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_group_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_relation (id UUID NOT NULL, default_task_id UUID NOT NULL, default_task_group_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, optional BOOLEAN NOT NULL, rule_logic VARCHAR(10) NOT NULL, rule_effect VARCHAR(15) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_146831167F3350C3 ON default_task_relation (default_task_id)');
        $this->addSql('CREATE INDEX IDX_1468311688B5AF18 ON default_task_relation (default_task_group_id)');
        $this->addSql('CREATE INDEX IDX_14683116B03A8386 ON default_task_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_1468311699049ECE ON default_task_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_146831169033212A ON default_task_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_14683116F2803B3D ON default_task_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_task_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_rule (id UUID NOT NULL, key_default_task_id UUID NOT NULL, default_task_relation_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A4091010F313D71C ON default_task_rule (key_default_task_id)');
        $this->addSql('CREATE INDEX IDX_A4091010FBDEF43E ON default_task_rule (default_task_relation_id)');
        $this->addSql('CREATE INDEX IDX_A4091010B03A8386 ON default_task_rule (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A409101099049ECE ON default_task_rule (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A40910109033212A ON default_task_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_task_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_termination (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, text TEXT NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_304CD794B03A8386 ON default_termination (created_by_id)');
        $this->addSql('CREATE INDEX IDX_304CD79499049ECE ON default_termination (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_304CD7949033212A ON default_termination (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_termination.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_termination.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_termination_relation (id UUID NOT NULL, default_termination_id UUID NOT NULL, tour_data_config_id UUID DEFAULT NULL, order_type_config_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_503976BE2D488B4F ON default_termination_relation (default_termination_id)');
        $this->addSql('CREATE INDEX IDX_503976BE3218A5DB ON default_termination_relation (tour_data_config_id)');
        $this->addSql('CREATE INDEX IDX_503976BEF86D9723 ON default_termination_relation (order_type_config_id)');
        $this->addSql('CREATE INDEX IDX_503976BEB03A8386 ON default_termination_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_503976BE99049ECE ON default_termination_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_503976BE9033212A ON default_termination_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_503976BEF2803B3D ON default_termination_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_termination_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_termination_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_unit_relation (id UUID NOT NULL, additional_service_config_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, unit VARCHAR(20) NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_219631C82CEBFF70 ON default_unit_relation (additional_service_config_id)');
        $this->addSql('CREATE INDEX IDX_219631C8B03A8386 ON default_unit_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_219631C899049ECE ON default_unit_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_219631C89033212A ON default_unit_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_219631C8F2803B3D ON default_unit_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN default_unit_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_unit_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE delivery_service (id UUID NOT NULL, order_document_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, material_number VARCHAR(100) NOT NULL, text VARCHAR(255) NOT NULL, task_external_id VARCHAR(100) DEFAULT NULL, template_field VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_EB83250CD88D07B1 ON delivery_service (order_document_id)');
        $this->addSql('CREATE INDEX IDX_EB83250CB03A8386 ON delivery_service (created_by_id)');
        $this->addSql('CREATE INDEX IDX_EB83250C99049ECE ON delivery_service (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_EB83250C9033212A ON delivery_service (tenant_id)');
        $this->addSql('COMMENT ON COLUMN delivery_service.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN delivery_service.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE device_access (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, device_unique_id VARCHAR(255) NOT NULL, device_access_type VARCHAR(255) NOT NULL, valid_until TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D5795A4FB03A8386 ON device_access (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D5795A4F99049ECE ON device_access (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D5795A4F9033212A ON device_access (tenant_id)');
        $this->addSql('COMMENT ON COLUMN device_access.valid_until IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_access.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_access.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE disposal_site (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E4FACF2DB03A8386 ON disposal_site (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E4FACF2D99049ECE ON disposal_site (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E4FACF2D9033212A ON disposal_site (tenant_id)');
        $this->addSql('COMMENT ON COLUMN disposal_site.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN disposal_site.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE disposal_site_version (id UUID NOT NULL, disposal_site_id UUID NOT NULL, location_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_FA2FF13B47DEC0E4 ON disposal_site_version (disposal_site_id)');
        $this->addSql('CREATE INDEX IDX_FA2FF13B64D218E ON disposal_site_version (location_id)');
        $this->addSql('CREATE INDEX IDX_FA2FF13BB03A8386 ON disposal_site_version (created_by_id)');
        $this->addSql('CREATE INDEX IDX_FA2FF13B99049ECE ON disposal_site_version (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_FA2FF13B9033212A ON disposal_site_version (tenant_id)');
        $this->addSql('COMMENT ON COLUMN disposal_site_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN disposal_site_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE element (id UUID NOT NULL, task_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, task_master BOOLEAN NOT NULL, task_group_master BOOLEAN NOT NULL, sequence_number INT NOT NULL, required BOOLEAN NOT NULL, reference_type VARCHAR(150) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, type VARCHAR(20) NOT NULL, expected_value VARCHAR(255) DEFAULT NULL, value_string_minlength INT DEFAULT NULL, value_string_max_length INT DEFAULT NULL, placeholder VARCHAR(100) DEFAULT NULL, label VARCHAR(100) DEFAULT NULL, pattern VARCHAR(100) DEFAULT NULL, pattern_error VARCHAR(100) DEFAULT NULL, description TEXT DEFAULT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, value_number_precision INT DEFAULT NULL, value_number_unit VARCHAR(20) DEFAULT NULL, value_min_items INT DEFAULT NULL, value_max_items INT DEFAULT NULL, value_signature_type VARCHAR(20) DEFAULT NULL, value_photo_type VARCHAR(20) DEFAULT NULL, value_photo_max_size INT DEFAULT NULL, value_date_min_date DATE DEFAULT NULL, value_date_max_date DATE DEFAULT NULL, value_time_min_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_time_max_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_date_format VARCHAR(50) DEFAULT NULL, value_time_format VARCHAR(50) DEFAULT NULL, pattern_hint VARCHAR(100) DEFAULT NULL, has_autocomplete BOOLEAN DEFAULT false NOT NULL, show_additional_input BOOLEAN DEFAULT true NOT NULL, checksum VARCHAR(255) DEFAULT NULL, checksum_formula VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_41405E398DB60186 ON element (task_id)');
        $this->addSql('CREATE INDEX IDX_41405E39B03A8386 ON element (created_by_id)');
        $this->addSql('CREATE INDEX IDX_41405E3999049ECE ON element (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_41405E399033212A ON element (tenant_id)');
        $this->addSql('CREATE INDEX IDX_41405E39F2803B3D ON element (sequence_number)');
        $this->addSql('COMMENT ON COLUMN element.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN element.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE element_option (id UUID NOT NULL, element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, org_uuid VARCHAR(255) DEFAULT NULL, is_from_external_source BOOLEAN DEFAULT false NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D39109241F1F2A24 ON element_option (element_id)');
        $this->addSql('CREATE INDEX IDX_D3910924B03A8386 ON element_option (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D391092499049ECE ON element_option (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D39109249033212A ON element_option (tenant_id)');
        $this->addSql('COMMENT ON COLUMN element_option.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN element_option.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE element_value (id UUID NOT NULL, element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D9552CAD1F1F2A24 ON element_value (element_id)');
        $this->addSql('CREATE INDEX IDX_D9552CADB03A8386 ON element_value (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D9552CAD99049ECE ON element_value (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D9552CAD9033212A ON element_value (tenant_id)');
        $this->addSql('COMMENT ON COLUMN element_value.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN element_value.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE equipment (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D338D583B03A8386 ON equipment (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D338D58399049ECE ON equipment (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D338D5839033212A ON equipment (tenant_id)');
        $this->addSql('COMMENT ON COLUMN equipment.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE equipment_config (id UUID NOT NULL, branch_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, country VARCHAR(10) DEFAULT NULL, equipment_type VARCHAR(20) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7304503CDCD6CC49 ON equipment_config (branch_id)');
        $this->addSql('CREATE INDEX IDX_7304503C517FE9FE ON equipment_config (equipment_id)');
        $this->addSql('CREATE INDEX IDX_7304503CB03A8386 ON equipment_config (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7304503C99049ECE ON equipment_config (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7304503C9033212A ON equipment_config (tenant_id)');
        $this->addSql('COMMENT ON COLUMN equipment_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE equipment_position (id UUID NOT NULL, tenant_id UUID NOT NULL, equipment_id VARCHAR(36) NOT NULL, longitude DOUBLE PRECISION NOT NULL, latitude DOUBLE PRECISION NOT NULL, recorded_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_76EF60D39033212A ON equipment_position (tenant_id)');
        $this->addSql('COMMENT ON COLUMN equipment_position.recorded_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE equipment_version (id UUID NOT NULL, equipment_id UUID NOT NULL, branch_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, height INT DEFAULT NULL, length INT DEFAULT NULL, width INT DEFAULT NULL, weight INT DEFAULT NULL, minimum_load INT DEFAULT NULL, overload INT DEFAULT NULL, load_capacity INT DEFAULT NULL, total_permissible_weight INT DEFAULT NULL, max_axle_load INT DEFAULT NULL, license_plate VARCHAR(255) NOT NULL, type VARCHAR(20) NOT NULL, container_mounting VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_C9671C2C517FE9FE ON equipment_version (equipment_id)');
        $this->addSql('CREATE INDEX IDX_C9671C2CDCD6CC49 ON equipment_version (branch_id)');
        $this->addSql('CREATE INDEX IDX_C9671C2CB03A8386 ON equipment_version (created_by_id)');
        $this->addSql('CREATE INDEX IDX_C9671C2C99049ECE ON equipment_version (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_C9671C2C9033212A ON equipment_version (tenant_id)');
        $this->addSql('COMMENT ON COLUMN equipment_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE faq (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, iso VARCHAR(10) NOT NULL, question TEXT NOT NULL, answer TEXT NOT NULL, sequence INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E8FF75CCB03A8386 ON faq (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E8FF75CC99049ECE ON faq (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E8FF75CC9033212A ON faq (tenant_id)');
        $this->addSql('COMMENT ON COLUMN faq.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN faq.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE feedback (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, comment TEXT NOT NULL, log_file VARCHAR(36) NOT NULL, screenshot_file VARCHAR(36) DEFAULT NULL, session_id VARCHAR(36) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D2294458B03A8386 ON feedback (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D229445899049ECE ON feedback (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D22944589033212A ON feedback (tenant_id)');
        $this->addSql('COMMENT ON COLUMN feedback.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN feedback.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE file_upload_operation (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, message_class TEXT NOT NULL, file_path TEXT NOT NULL, parameters JSON NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_CFEC562AB03A8386 ON file_upload_operation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_CFEC562A99049ECE ON file_upload_operation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_CFEC562A9033212A ON file_upload_operation (tenant_id)');
        $this->addSql('COMMENT ON COLUMN file_upload_operation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN file_upload_operation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE interruption (id UUID NOT NULL, tour_id UUID DEFAULT NULL, session_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, type VARCHAR(20) NOT NULL, client_uuid VARCHAR(255) NOT NULL, accessible_interruption_id VARCHAR(36) DEFAULT NULL, default_interruption_id VARCHAR(36) DEFAULT NULL, external_id VARCHAR(50) NOT NULL, sent_to_sap VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F9511BC015ED8D43 ON interruption (tour_id)');
        $this->addSql('CREATE INDEX IDX_F9511BC0613FECDF ON interruption (session_id)');
        $this->addSql('CREATE INDEX IDX_F9511BC0B03A8386 ON interruption (created_by_id)');
        $this->addSql('CREATE INDEX IDX_F9511BC099049ECE ON interruption (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_F9511BC09033212A ON interruption (tenant_id)');
        $this->addSql('COMMENT ON COLUMN interruption.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN interruption.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE location (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5E9E89CBB03A8386 ON location (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5E9E89CB99049ECE ON location (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5E9E89CB9033212A ON location (tenant_id)');
        $this->addSql('COMMENT ON COLUMN location.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE location_format_config (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, country VARCHAR(10) NOT NULL, highlight BOOLEAN NOT NULL, sequence INT NOT NULL, icon VARCHAR(255) DEFAULT NULL, also_front_view BOOLEAN NOT NULL, format_string TEXT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5F2289B03A8386 ON location_format_config (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5F228999049ECE ON location_format_config (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5F22899033212A ON location_format_config (tenant_id)');
        $this->addSql('CREATE INDEX IDX_5F22895286D72B ON location_format_config (sequence)');
        $this->addSql('COMMENT ON COLUMN location_format_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location_format_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE location_version (id UUID NOT NULL, location_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, type VARCHAR(20) NOT NULL, phone VARCHAR(100) DEFAULT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) DEFAULT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(40) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, state VARCHAR(60) DEFAULT NULL, latitude DOUBLE PRECISION DEFAULT NULL, longitude DOUBLE PRECISION DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5159C06764D218E ON location_version (location_id)');
        $this->addSql('CREATE INDEX IDX_5159C067B03A8386 ON location_version (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5159C06799049ECE ON location_version (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5159C0679033212A ON location_version (tenant_id)');
        $this->addSql('COMMENT ON COLUMN location_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE message (id UUID NOT NULL, staff_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, description TEXT NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_B6BD307FD4D57CD ON message (staff_id)');
        $this->addSql('CREATE INDEX IDX_B6BD307F517FE9FE ON message (equipment_id)');
        $this->addSql('CREATE INDEX IDX_B6BD307FB03A8386 ON message (created_by_id)');
        $this->addSql('CREATE INDEX IDX_B6BD307F99049ECE ON message (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_B6BD307F9033212A ON message (tenant_id)');
        $this->addSql('COMMENT ON COLUMN message.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN message.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE mobile_device (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_760B5D84B03A8386 ON mobile_device (created_by_id)');
        $this->addSql('CREATE INDEX IDX_760B5D8499049ECE ON mobile_device (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_760B5D849033212A ON mobile_device (tenant_id)');
        $this->addSql('COMMENT ON COLUMN mobile_device.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mobile_device.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE navigation_token (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, expiry TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, token VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E10B31FEB03A8386 ON navigation_token (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E10B31FE99049ECE ON navigation_token (modified_by_id)');
        $this->addSql('COMMENT ON COLUMN navigation_token.expiry IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN navigation_token.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN navigation_token.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE note (id UUID NOT NULL, order_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_CFBDFA148D9F6D38 ON note (order_id)');
        $this->addSql('CREATE INDEX IDX_CFBDFA14B03A8386 ON note (created_by_id)');
        $this->addSql('CREATE INDEX IDX_CFBDFA1499049ECE ON note (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_CFBDFA149033212A ON note (tenant_id)');
        $this->addSql('COMMENT ON COLUMN note.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN note.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE "order" (id UUID NOT NULL, customer_id UUID NOT NULL, tour_id UUID NOT NULL, location_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sorting_order INT NOT NULL, order_position INT NOT NULL, order_ext_id VARCHAR(255) NOT NULL, type VARCHAR(20) NOT NULL, order_type_config_id VARCHAR(36) NOT NULL, contract_number VARCHAR(100) DEFAULT NULL, transport_number VARCHAR(100) DEFAULT NULL, purchase_order VARCHAR(100) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F52993989395C3F3 ON "order" (customer_id)');
        $this->addSql('CREATE INDEX IDX_F529939815ED8D43 ON "order" (tour_id)');
        $this->addSql('CREATE INDEX IDX_F529939864D218E ON "order" (location_id)');
        $this->addSql('CREATE INDEX IDX_F5299398B03A8386 ON "order" (created_by_id)');
        $this->addSql('CREATE INDEX IDX_F529939899049ECE ON "order" (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_F52993989033212A ON "order" (tenant_id)');
        $this->addSql('COMMENT ON COLUMN "order".created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "order".modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE order_document (id UUID NOT NULL, order_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, document_type VARCHAR(100) NOT NULL, document_name VARCHAR(255) DEFAULT NULL, document_path TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_399168C78D9F6D38 ON order_document (order_id)');
        $this->addSql('CREATE INDEX IDX_399168C7B03A8386 ON order_document (created_by_id)');
        $this->addSql('CREATE INDEX IDX_399168C799049ECE ON order_document (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_399168C79033212A ON order_document (tenant_id)');
        $this->addSql('COMMENT ON COLUMN order_document.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_document.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE order_document_text_block (id UUID NOT NULL, order_document_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, block_id VARCHAR(100) NOT NULL, text TEXT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_BB2ABE5BD88D07B1 ON order_document_text_block (order_document_id)');
        $this->addSql('CREATE INDEX IDX_BB2ABE5BB03A8386 ON order_document_text_block (created_by_id)');
        $this->addSql('CREATE INDEX IDX_BB2ABE5B99049ECE ON order_document_text_block (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_BB2ABE5B9033212A ON order_document_text_block (tenant_id)');
        $this->addSql('COMMENT ON COLUMN order_document_text_block.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_document_text_block.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE order_type_config (id UUID NOT NULL, branch_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, country VARCHAR(10) NOT NULL, equipment_type VARCHAR(20) DEFAULT NULL, tour_order_type VARCHAR(20) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_ECEF3BC2DCD6CC49 ON order_type_config (branch_id)');
        $this->addSql('CREATE INDEX IDX_ECEF3BC2517FE9FE ON order_type_config (equipment_id)');
        $this->addSql('CREATE INDEX IDX_ECEF3BC2B03A8386 ON order_type_config (created_by_id)');
        $this->addSql('CREATE INDEX IDX_ECEF3BC299049ECE ON order_type_config (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_ECEF3BC29033212A ON order_type_config (tenant_id)');
        $this->addSql('COMMENT ON COLUMN order_type_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_type_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE sap_luxembourg_container (id VARCHAR(100) NOT NULL, type_id INT NOT NULL, serial_number VARCHAR(100) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE sap_luxembourg_equipment (id VARCHAR(100) NOT NULL, type VARCHAR(255) NOT NULL, height INT NOT NULL, length INT NOT NULL, width INT NOT NULL, weight INT NOT NULL, minimum_load INT NOT NULL, overload INT NOT NULL, total_permissible_weight INT NOT NULL, max_axle_load INT NOT NULL, container_mounting VARCHAR(100) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE sap_luxembourg_tour (id UUID NOT NULL, driver_display_number VARCHAR(100) NOT NULL, truck_id VARCHAR(100) NOT NULL, date DATE NOT NULL, hash VARCHAR(100) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('COMMENT ON COLUMN sap_luxembourg_tour.date IS \'(DC2Type:date_immutable)\'');
        $this->addSql('CREATE TABLE sap_luxembourg_user (id INT NOT NULL, first_name VARCHAR(100) NOT NULL, last_name VARCHAR(100) NOT NULL, is_driver BOOLEAN NOT NULL, display_number VARCHAR(255) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE session (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, device_unique_id VARCHAR(255) NOT NULL, start TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, "end" TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D044D5D4B03A8386 ON session (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D044D5D499049ECE ON session (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D044D5D49033212A ON session (tenant_id)');
        $this->addSql('COMMENT ON COLUMN session.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE session_equipment (id UUID NOT NULL, session_id UUID NOT NULL, equipment_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, start TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, "end" TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_90DA9089613FECDF ON session_equipment (session_id)');
        $this->addSql('CREATE INDEX IDX_90DA9089517FE9FE ON session_equipment (equipment_id)');
        $this->addSql('CREATE INDEX IDX_90DA9089B03A8386 ON session_equipment (created_by_id)');
        $this->addSql('CREATE INDEX IDX_90DA908999049ECE ON session_equipment (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_90DA90899033212A ON session_equipment (tenant_id)');
        $this->addSql('COMMENT ON COLUMN session_equipment.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_equipment.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE session_tour (id UUID NOT NULL, session_id UUID NOT NULL, tour_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, start TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, "end" TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_ACA0F943613FECDF ON session_tour (session_id)');
        $this->addSql('CREATE INDEX IDX_ACA0F94315ED8D43 ON session_tour (tour_id)');
        $this->addSql('CREATE INDEX IDX_ACA0F943B03A8386 ON session_tour (created_by_id)');
        $this->addSql('CREATE INDEX IDX_ACA0F94399049ECE ON session_tour (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_ACA0F9439033212A ON session_tour (tenant_id)');
        $this->addSql('COMMENT ON COLUMN session_tour.start IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_tour."end" IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_tour.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_tour.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE "session_user" (id UUID NOT NULL, session_id UUID NOT NULL, user_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, start TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, "end" TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_4BE2D663613FECDF ON "session_user" (session_id)');
        $this->addSql('CREATE INDEX IDX_4BE2D663A76ED395 ON "session_user" (user_id)');
        $this->addSql('CREATE INDEX IDX_4BE2D663B03A8386 ON "session_user" (created_by_id)');
        $this->addSql('CREATE INDEX IDX_4BE2D66399049ECE ON "session_user" (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_4BE2D6639033212A ON "session_user" (tenant_id)');
        $this->addSql('COMMENT ON COLUMN "session_user".created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "session_user".modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE staff (id UUID NOT NULL, user_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_426EF392A76ED395 ON staff (user_id)');
        $this->addSql('CREATE INDEX IDX_426EF392B03A8386 ON staff (created_by_id)');
        $this->addSql('CREATE INDEX IDX_426EF39299049ECE ON staff (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_426EF3929033212A ON staff (tenant_id)');
        $this->addSql('COMMENT ON COLUMN staff.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN staff.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE staff_config (id UUID NOT NULL, branch_id UUID DEFAULT NULL, staff_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, country VARCHAR(10) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D1F79DC7DCD6CC49 ON staff_config (branch_id)');
        $this->addSql('CREATE INDEX IDX_D1F79DC7D4D57CD ON staff_config (staff_id)');
        $this->addSql('CREATE INDEX IDX_D1F79DC7B03A8386 ON staff_config (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D1F79DC799049ECE ON staff_config (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D1F79DC79033212A ON staff_config (tenant_id)');
        $this->addSql('COMMENT ON COLUMN staff_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN staff_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE staff_version (id UUID NOT NULL, staff_id UUID NOT NULL, branch_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, personnel_type VARCHAR(20) NOT NULL, firstname VARCHAR(255) NOT NULL, lastname VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E3AAC475D4D57CD ON staff_version (staff_id)');
        $this->addSql('CREATE INDEX IDX_E3AAC475DCD6CC49 ON staff_version (branch_id)');
        $this->addSql('CREATE INDEX IDX_E3AAC475B03A8386 ON staff_version (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E3AAC47599049ECE ON staff_version (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E3AAC4759033212A ON staff_version (tenant_id)');
        $this->addSql('COMMENT ON COLUMN staff_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN staff_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, type VARCHAR(150) NOT NULL, external_id VARCHAR(255) DEFAULT NULL, only_for_assignment BOOLEAN NOT NULL, repeatable BOOLEAN NOT NULL, completed_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, sap_action BOOLEAN DEFAULT false NOT NULL, repeated_from VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_527EDB25B03A8386 ON task (created_by_id)');
        $this->addSql('CREATE INDEX IDX_527EDB2599049ECE ON task (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_527EDB259033212A ON task (tenant_id)');
        $this->addSql('COMMENT ON COLUMN task.completed_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task_group (id UUID NOT NULL, order_id UUID DEFAULT NULL, tour_id UUID DEFAULT NULL, location_id UUID DEFAULT NULL, interruption_id UUID DEFAULT NULL, note_id UUID DEFAULT NULL, termination_id UUID DEFAULT NULL, session_equipment_id UUID DEFAULT NULL, session_user_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, title VARCHAR(255) NOT NULL, sequence_number INT NOT NULL, default_task_group_id VARCHAR(36) DEFAULT NULL, rule_logic VARCHAR(10) NOT NULL, rule_effect VARCHAR(15) NOT NULL, type VARCHAR(30) NOT NULL, external_id VARCHAR(50) DEFAULT NULL, source_data_hash VARCHAR(40) DEFAULT NULL, to_be_done VARCHAR(10) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_AA645FE58D9F6D38 ON task_group (order_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE515ED8D43 ON task_group (tour_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE564D218E ON task_group (location_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE542B4F971 ON task_group (interruption_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE526ED0855 ON task_group (note_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE5A9C0EB0D ON task_group (termination_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE59FE2547F ON task_group (session_equipment_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE5B5B651CF ON task_group (session_user_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE5B03A8386 ON task_group (created_by_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE599049ECE ON task_group (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE59033212A ON task_group (tenant_id)');
        $this->addSql('CREATE INDEX IDX_AA645FE5F2803B3D ON task_group (sequence_number)');
        $this->addSql('COMMENT ON COLUMN task_group.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_group.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task_group_rule (id UUID NOT NULL, key_task_group_id UUID NOT NULL, task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7391BC13FCC92519 ON task_group_rule (key_task_group_id)');
        $this->addSql('CREATE INDEX IDX_7391BC13BE94330B ON task_group_rule (task_group_id)');
        $this->addSql('CREATE INDEX IDX_7391BC13B03A8386 ON task_group_rule (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7391BC1399049ECE ON task_group_rule (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7391BC139033212A ON task_group_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN task_group_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_group_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task_relation (id UUID NOT NULL, task_group_id UUID DEFAULT NULL, task_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, rule_logic VARCHAR(10) NOT NULL, rule_effect VARCHAR(15) NOT NULL, optional BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_2249AD05BE94330B ON task_relation (task_group_id)');
        $this->addSql('CREATE INDEX IDX_2249AD058DB60186 ON task_relation (task_id)');
        $this->addSql('CREATE INDEX IDX_2249AD05B03A8386 ON task_relation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_2249AD0599049ECE ON task_relation (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_2249AD059033212A ON task_relation (tenant_id)');
        $this->addSql('CREATE INDEX IDX_2249AD05F2803B3D ON task_relation (sequence_number)');
        $this->addSql('COMMENT ON COLUMN task_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task_rule (id UUID NOT NULL, key_task_id UUID NOT NULL, task_relation_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_356B38B7EF6025B8 ON task_rule (key_task_id)');
        $this->addSql('CREATE INDEX IDX_356B38B774233C69 ON task_rule (task_relation_id)');
        $this->addSql('CREATE INDEX IDX_356B38B7B03A8386 ON task_rule (created_by_id)');
        $this->addSql('CREATE INDEX IDX_356B38B799049ECE ON task_rule (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_356B38B79033212A ON task_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN task_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE tenant (id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, name VARCHAR(50) NOT NULL, shortcut VARCHAR(3) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX shortcut ON tenant (shortcut)');
        $this->addSql('COMMENT ON COLUMN tenant.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE termination (id UUID NOT NULL, tour_id UUID DEFAULT NULL, order_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, text TEXT NOT NULL, external_id VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_E4BFC42215ED8D43 ON termination (tour_id)');
        $this->addSql('CREATE INDEX IDX_E4BFC4228D9F6D38 ON termination (order_id)');
        $this->addSql('CREATE INDEX IDX_E4BFC422B03A8386 ON termination (created_by_id)');
        $this->addSql('CREATE INDEX IDX_E4BFC42299049ECE ON termination (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_E4BFC4229033212A ON termination (tenant_id)');
        $this->addSql('COMMENT ON COLUMN termination.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN termination.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE tour (id UUID NOT NULL, branch_id UUID NOT NULL, start_location_id UUID NOT NULL, end_location_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, planned_start_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, planned_end_date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, forced_order BOOLEAN NOT NULL, sent_to_sap VARCHAR(255) DEFAULT NULL, last_tour_update TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, external_id VARCHAR(100) NOT NULL, tour_data_config_id VARCHAR(36) NOT NULL, disposal_site_hash VARCHAR(40) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_6AD1F969DCD6CC49 ON tour (branch_id)');
        $this->addSql('CREATE INDEX IDX_6AD1F9695C3A313A ON tour (start_location_id)');
        $this->addSql('CREATE INDEX IDX_6AD1F969C43C7F1 ON tour (end_location_id)');
        $this->addSql('CREATE INDEX IDX_6AD1F969B03A8386 ON tour (created_by_id)');
        $this->addSql('CREATE INDEX IDX_6AD1F96999049ECE ON tour (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_6AD1F9699033212A ON tour (tenant_id)');
        $this->addSql('COMMENT ON COLUMN tour.last_tour_update IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE tour_data_config (id UUID NOT NULL, branch_id UUID DEFAULT NULL, equipment_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, country VARCHAR(10) NOT NULL, equipment_type VARCHAR(20) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D4224CA6DCD6CC49 ON tour_data_config (branch_id)');
        $this->addSql('CREATE INDEX IDX_D4224CA6517FE9FE ON tour_data_config (equipment_id)');
        $this->addSql('CREATE INDEX IDX_D4224CA6B03A8386 ON tour_data_config (created_by_id)');
        $this->addSql('CREATE INDEX IDX_D4224CA699049ECE ON tour_data_config (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_D4224CA69033212A ON tour_data_config (tenant_id)');
        $this->addSql('COMMENT ON COLUMN tour_data_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour_data_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE tour_equipment (id UUID NOT NULL, tour_id UUID NOT NULL, equipment_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, dispatched BOOLEAN NOT NULL, sent_to_sap VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_6C724BCC15ED8D43 ON tour_equipment (tour_id)');
        $this->addSql('CREATE INDEX IDX_6C724BCC517FE9FE ON tour_equipment (equipment_id)');
        $this->addSql('CREATE INDEX IDX_6C724BCCB03A8386 ON tour_equipment (created_by_id)');
        $this->addSql('CREATE INDEX IDX_6C724BCC99049ECE ON tour_equipment (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_6C724BCC9033212A ON tour_equipment (tenant_id)');
        $this->addSql('COMMENT ON COLUMN tour_equipment.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour_equipment.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE tour_staff (id UUID NOT NULL, tour_id UUID NOT NULL, staff_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, dispatched BOOLEAN NOT NULL, sent_to_sap VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(20) NOT NULL, status_changed_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7921E3BA15ED8D43 ON tour_staff (tour_id)');
        $this->addSql('CREATE INDEX IDX_7921E3BAD4D57CD ON tour_staff (staff_id)');
        $this->addSql('CREATE INDEX IDX_7921E3BAB03A8386 ON tour_staff (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7921E3BA99049ECE ON tour_staff (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7921E3BA9033212A ON tour_staff (tenant_id)');
        $this->addSql('COMMENT ON COLUMN tour_staff.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour_staff.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE translation (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, locale VARCHAR(255) NOT NULL, tech_name VARCHAR(255) NOT NULL, translation VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_B469456FB03A8386 ON translation (created_by_id)');
        $this->addSql('CREATE INDEX IDX_B469456F99049ECE ON translation (modified_by_id)');
        $this->addSql('COMMENT ON COLUMN translation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN translation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE "user" (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, email VARCHAR(180) DEFAULT NULL, username VARCHAR(255) NOT NULL, last_login TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, firstname VARCHAR(255) DEFAULT NULL, lastname VARCHAR(255) DEFAULT NULL, status INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_8D93D649B03A8386 ON "user" (created_by_id)');
        $this->addSql('CREATE INDEX IDX_8D93D64999049ECE ON "user" (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_8D93D6499033212A ON "user" (tenant_id)');
        $this->addSql('COMMENT ON COLUMN "user".created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "user".modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE user_setting (id UUID NOT NULL, user_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, language VARCHAR(10) DEFAULT NULL, colormode VARCHAR(255) DEFAULT NULL, option_button_location VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_C779A692A76ED395 ON user_setting (user_id)');
        $this->addSql('CREATE INDEX IDX_C779A692B03A8386 ON user_setting (created_by_id)');
        $this->addSql('CREATE INDEX IDX_C779A69299049ECE ON user_setting (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_C779A6929033212A ON user_setting (tenant_id)');
        $this->addSql('COMMENT ON COLUMN user_setting.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN user_setting.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE messenger_messages (id BIGSERIAL NOT NULL, body TEXT NOT NULL, headers TEXT NOT NULL, queue_name VARCHAR(190) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, available_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, delivered_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_75EA56E0FB7336F0 ON messenger_messages (queue_name)');
        $this->addSql('CREATE INDEX IDX_75EA56E0E3BD61CE ON messenger_messages (available_at)');
        $this->addSql('CREATE INDEX IDX_75EA56E016BA31DB ON messenger_messages (delivered_at)');
        $this->addSql('COMMENT ON COLUMN messenger_messages.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.available_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.delivered_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE OR REPLACE FUNCTION notify_messenger_messages() RETURNS TRIGGER AS $$
            BEGIN
                PERFORM pg_notify(\'messenger_messages\', NEW.queue_name::text);
                RETURN NEW;
            END;
        $$ LANGUAGE plpgsql;');
        $this->addSql('DROP TRIGGER IF EXISTS notify_trigger ON messenger_messages;');
        $this->addSql('CREATE TRIGGER notify_trigger AFTER INSERT OR UPDATE ON messenger_messages FOR EACH ROW EXECUTE PROCEDURE notify_messenger_messages();');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT FK_5E2C358132C0FDC9 FOREIGN KEY (accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT FK_5E2C3581C7A42F5E FOREIGN KEY (accessible_interruption_id) REFERENCES accessible_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT FK_5E2C3581B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT FK_5E2C358199049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT FK_5E2C35819033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT FK_8FBBC0E7434D9F58 FOREIGN KEY (accessible_task_id) REFERENCES accessible_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT FK_8FBBC0E7B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT FK_8FBBC0E799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT FK_8FBBC0E79033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT FK_A677FE765DA5BED0 FOREIGN KEY (accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT FK_A677FE76B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT FK_A677FE7699049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT FK_A677FE769033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT FK_5501E26F5DA5BED0 FOREIGN KEY (accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT FK_5501E26FB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT FK_5501E26F99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT FK_5501E26F9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_interruption ADD CONSTRAINT FK_7EE32A9E15ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_interruption ADD CONSTRAINT FK_7EE32A9EB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_interruption ADD CONSTRAINT FK_7EE32A9E99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_interruption ADD CONSTRAINT FK_7EE32A9E9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note ADD CONSTRAINT FK_8747C5ACB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note ADD CONSTRAINT FK_8747C5AC99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note ADD CONSTRAINT FK_8747C5AC9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note_relation ADD CONSTRAINT FK_FE865515E816968B FOREIGN KEY (accessible_note_id) REFERENCES accessible_note (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note_relation ADD CONSTRAINT FK_FE8655158D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note_relation ADD CONSTRAINT FK_FE865515B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note_relation ADD CONSTRAINT FK_FE86551599049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_note_relation ADD CONSTRAINT FK_FE8655159033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task ADD CONSTRAINT FK_1A84E49DB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task ADD CONSTRAINT FK_1A84E49D99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task ADD CONSTRAINT FK_1A84E49D9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB11DC261C5F FOREIGN KEY (accessible_termination_id) REFERENCES accessible_termination (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB11C7A42F5E FOREIGN KEY (accessible_interruption_id) REFERENCES accessible_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB11E816968B FOREIGN KEY (accessible_note_id) REFERENCES accessible_note (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB1164D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB11B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB1199049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group ADD CONSTRAINT FK_E8DECB119033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT FK_F6816A3C64F12DD FOREIGN KEY (key_accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT FK_F6816A3C32C0FDC9 FOREIGN KEY (accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT FK_F6816A3CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT FK_F6816A3C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT FK_F6816A3C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_relation ADD CONSTRAINT FK_AE1D63C7434D9F58 FOREIGN KEY (accessible_task_id) REFERENCES accessible_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_relation ADD CONSTRAINT FK_AE1D63C732C0FDC9 FOREIGN KEY (accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_relation ADD CONSTRAINT FK_AE1D63C7B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_relation ADD CONSTRAINT FK_AE1D63C799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_relation ADD CONSTRAINT FK_AE1D63C79033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT FK_3D67A09E8A34C39D FOREIGN KEY (key_accessible_task_id) REFERENCES accessible_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT FK_3D67A09EDF7711E6 FOREIGN KEY (accessible_task_relation_id) REFERENCES accessible_task_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT FK_3D67A09EB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT FK_3D67A09E99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT FK_3D67A09E9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination ADD CONSTRAINT FK_5E2D48B3B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination ADD CONSTRAINT FK_5E2D48B399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination ADD CONSTRAINT FK_5E2D48B39033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD CONSTRAINT FK_5F3616F2DC261C5F FOREIGN KEY (accessible_termination_id) REFERENCES accessible_termination (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD CONSTRAINT FK_5F3616F215ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD CONSTRAINT FK_5F3616F28D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD CONSTRAINT FK_5F3616F2B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD CONSTRAINT FK_5F3616F299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD CONSTRAINT FK_5F3616F29033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524D15ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524D8D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524DBE94330B FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524D42B4F971 FOREIGN KEY (interruption_id) REFERENCES interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524DB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524D99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT FK_19D9524D9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_service_config ADD CONSTRAINT FK_AD3C9557DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_service_config ADD CONSTRAINT FK_AD3C9557517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_service_config ADD CONSTRAINT FK_AD3C95577F3350C3 FOREIGN KEY (default_task_id) REFERENCES default_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_service_config ADD CONSTRAINT FK_AD3C9557B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_service_config ADD CONSTRAINT FK_AD3C955799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_service_config ADD CONSTRAINT FK_AD3C95579033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE15ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE8D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDEBE94330B FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE8DB60186 FOREIGN KEY (task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE42B4F971 FOREIGN KEY (interruption_id) REFERENCES interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE613FECDF FOREIGN KEY (session_id) REFERENCES session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDEB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT FK_E00CEDDE9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE branch ADD CONSTRAINT FK_BB861B1F64D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE branch ADD CONSTRAINT FK_BB861B1FB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE branch ADD CONSTRAINT FK_BB861B1F99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE branch ADD CONSTRAINT FK_BB861B1F9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE connected_device ADD CONSTRAINT FK_F097D203517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE connected_device ADD CONSTRAINT FK_F097D203B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE connected_device ADD CONSTRAINT FK_F097D20399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE connected_device ADD CONSTRAINT FK_F097D2039033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer ADD CONSTRAINT FK_81398E09B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer ADD CONSTRAINT FK_81398E0999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer ADD CONSTRAINT FK_81398E099033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer_version ADD CONSTRAINT FK_A3C1777D9395C3F3 FOREIGN KEY (customer_id) REFERENCES customer (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer_version ADD CONSTRAINT FK_A3C1777DB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer_version ADD CONSTRAINT FK_A3C1777D99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE customer_version ADD CONSTRAINT FK_A3C1777D9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDF88B5AF18 FOREIGN KEY (default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDFF86D9723 FOREIGN KEY (order_type_config_id) REFERENCES order_type_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDF3218A5DB FOREIGN KEY (tour_data_config_id) REFERENCES tour_data_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDFDAE251AD FOREIGN KEY (default_interruption_id) REFERENCES default_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDFB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDF99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT FK_4F525BDF9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element ADD CONSTRAINT FK_B3C50F7CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element ADD CONSTRAINT FK_B3C50F7C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element ADD CONSTRAINT FK_B3C50F7C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT FK_57196966573C7E47 FOREIGN KEY (default_element_id) REFERENCES default_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT FK_57196966B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT FK_5719696699049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT FK_571969669033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT FK_EF74B0BE573C7E47 FOREIGN KEY (default_element_id) REFERENCES default_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT FK_EF74B0BEB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT FK_EF74B0BE99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT FK_EF74B0BE9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption ADD CONSTRAINT FK_DB87FE6AB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption ADD CONSTRAINT FK_DB87FE6A99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption ADD CONSTRAINT FK_DB87FE6A9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FB3218A5DB FOREIGN KEY (tour_data_config_id) REFERENCES tour_data_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FBF6E640FA FOREIGN KEY (equipment_config_id) REFERENCES equipment_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FB1597D6CC FOREIGN KEY (staff_config_id) REFERENCES staff_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FBDAE251AD FOREIGN KEY (default_interruption_id) REFERENCES default_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FBB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FB99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_interruption_relation ADD CONSTRAINT FK_5AAD37FB9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material ADD CONSTRAINT FK_7AFA45DBB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material ADD CONSTRAINT FK_7AFA45DB99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material ADD CONSTRAINT FK_7AFA45DB9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material_relation ADD CONSTRAINT FK_D1D15A742CEBFF70 FOREIGN KEY (additional_service_config_id) REFERENCES additional_service_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material_relation ADD CONSTRAINT FK_D1D15A7437FBBFD9 FOREIGN KEY (default_material_id) REFERENCES default_material (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material_relation ADD CONSTRAINT FK_D1D15A74B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material_relation ADD CONSTRAINT FK_D1D15A7499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_material_relation ADD CONSTRAINT FK_D1D15A749033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note ADD CONSTRAINT FK_8A4CC112B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note ADD CONSTRAINT FK_8A4CC11299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note ADD CONSTRAINT FK_8A4CC1129033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note_relation ADD CONSTRAINT FK_44F307C4D4685910 FOREIGN KEY (default_note_id) REFERENCES default_note (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note_relation ADD CONSTRAINT FK_44F307C4F86D9723 FOREIGN KEY (order_type_config_id) REFERENCES order_type_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note_relation ADD CONSTRAINT FK_44F307C4B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note_relation ADD CONSTRAINT FK_44F307C499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_note_relation ADD CONSTRAINT FK_44F307C49033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task ADD CONSTRAINT FK_178FE023B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task ADD CONSTRAINT FK_178FE02399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task ADD CONSTRAINT FK_178FE0239033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT FK_A64CE0D77F3350C3 FOREIGN KEY (default_task_id) REFERENCES default_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT FK_A64CE0D7573C7E47 FOREIGN KEY (default_element_id) REFERENCES default_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT FK_A64CE0D7B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT FK_A64CE0D799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT FK_A64CE0D79033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B86F86D9723 FOREIGN KEY (order_type_config_id) REFERENCES order_type_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B863218A5DB FOREIGN KEY (tour_data_config_id) REFERENCES tour_data_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B86DAE251AD FOREIGN KEY (default_interruption_id) REFERENCES default_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B862D488B4F FOREIGN KEY (default_termination_id) REFERENCES default_termination (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B86D4685910 FOREIGN KEY (default_note_id) REFERENCES default_note (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B86F6E640FA FOREIGN KEY (equipment_config_id) REFERENCES equipment_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B861597D6CC FOREIGN KEY (staff_config_id) REFERENCES staff_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B8664D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B86B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B8699049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group ADD CONSTRAINT FK_E2470B869033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT FK_EBC714CF200620BA FOREIGN KEY (key_default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT FK_EBC714CF88B5AF18 FOREIGN KEY (default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT FK_EBC714CFB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT FK_EBC714CF99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT FK_EBC714CF9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_relation ADD CONSTRAINT FK_146831167F3350C3 FOREIGN KEY (default_task_id) REFERENCES default_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_relation ADD CONSTRAINT FK_1468311688B5AF18 FOREIGN KEY (default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_relation ADD CONSTRAINT FK_14683116B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_relation ADD CONSTRAINT FK_1468311699049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_relation ADD CONSTRAINT FK_146831169033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT FK_A4091010F313D71C FOREIGN KEY (key_default_task_id) REFERENCES default_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT FK_A4091010FBDEF43E FOREIGN KEY (default_task_relation_id) REFERENCES default_task_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT FK_A4091010B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT FK_A409101099049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT FK_A40910109033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination ADD CONSTRAINT FK_304CD794B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination ADD CONSTRAINT FK_304CD79499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination ADD CONSTRAINT FK_304CD7949033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination_relation ADD CONSTRAINT FK_503976BE2D488B4F FOREIGN KEY (default_termination_id) REFERENCES default_termination (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination_relation ADD CONSTRAINT FK_503976BE3218A5DB FOREIGN KEY (tour_data_config_id) REFERENCES tour_data_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination_relation ADD CONSTRAINT FK_503976BEF86D9723 FOREIGN KEY (order_type_config_id) REFERENCES order_type_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination_relation ADD CONSTRAINT FK_503976BEB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination_relation ADD CONSTRAINT FK_503976BE99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_termination_relation ADD CONSTRAINT FK_503976BE9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_unit_relation ADD CONSTRAINT FK_219631C82CEBFF70 FOREIGN KEY (additional_service_config_id) REFERENCES additional_service_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_unit_relation ADD CONSTRAINT FK_219631C8B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_unit_relation ADD CONSTRAINT FK_219631C899049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_unit_relation ADD CONSTRAINT FK_219631C89033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE delivery_service ADD CONSTRAINT FK_EB83250CD88D07B1 FOREIGN KEY (order_document_id) REFERENCES order_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE delivery_service ADD CONSTRAINT FK_EB83250CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE delivery_service ADD CONSTRAINT FK_EB83250C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE delivery_service ADD CONSTRAINT FK_EB83250C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_access ADD CONSTRAINT FK_D5795A4FB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_access ADD CONSTRAINT FK_D5795A4F99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_access ADD CONSTRAINT FK_D5795A4F9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site ADD CONSTRAINT FK_E4FACF2DB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site ADD CONSTRAINT FK_E4FACF2D99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site ADD CONSTRAINT FK_E4FACF2D9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT FK_FA2FF13B47DEC0E4 FOREIGN KEY (disposal_site_id) REFERENCES disposal_site (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT FK_FA2FF13B64D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT FK_FA2FF13BB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT FK_FA2FF13B99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT FK_FA2FF13B9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT FK_41405E398DB60186 FOREIGN KEY (task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT FK_41405E39B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT FK_41405E3999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT FK_41405E399033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT FK_D39109241F1F2A24 FOREIGN KEY (element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT FK_D3910924B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT FK_D391092499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT FK_D39109249033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT FK_D9552CAD1F1F2A24 FOREIGN KEY (element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT FK_D9552CADB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT FK_D9552CAD99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT FK_D9552CAD9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment ADD CONSTRAINT FK_D338D583B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment ADD CONSTRAINT FK_D338D58399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment ADD CONSTRAINT FK_D338D5839033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_config ADD CONSTRAINT FK_7304503CDCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_config ADD CONSTRAINT FK_7304503C517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_config ADD CONSTRAINT FK_7304503CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_config ADD CONSTRAINT FK_7304503C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_config ADD CONSTRAINT FK_7304503C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_position ADD CONSTRAINT FK_76EF60D39033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_version ADD CONSTRAINT FK_C9671C2C517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_version ADD CONSTRAINT FK_C9671C2CDCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_version ADD CONSTRAINT FK_C9671C2CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_version ADD CONSTRAINT FK_C9671C2C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE equipment_version ADD CONSTRAINT FK_C9671C2C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE faq ADD CONSTRAINT FK_E8FF75CCB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE faq ADD CONSTRAINT FK_E8FF75CC99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE faq ADD CONSTRAINT FK_E8FF75CC9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE feedback ADD CONSTRAINT FK_D2294458B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE feedback ADD CONSTRAINT FK_D229445899049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE feedback ADD CONSTRAINT FK_D22944589033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE file_upload_operation ADD CONSTRAINT FK_CFEC562AB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE file_upload_operation ADD CONSTRAINT FK_CFEC562A99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE file_upload_operation ADD CONSTRAINT FK_CFEC562A9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE interruption ADD CONSTRAINT FK_F9511BC015ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE interruption ADD CONSTRAINT FK_F9511BC0613FECDF FOREIGN KEY (session_id) REFERENCES session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE interruption ADD CONSTRAINT FK_F9511BC0B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE interruption ADD CONSTRAINT FK_F9511BC099049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE interruption ADD CONSTRAINT FK_F9511BC09033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location ADD CONSTRAINT FK_5E9E89CBB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location ADD CONSTRAINT FK_5E9E89CB99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location ADD CONSTRAINT FK_5E9E89CB9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_format_config ADD CONSTRAINT FK_5F2289B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_format_config ADD CONSTRAINT FK_5F228999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_format_config ADD CONSTRAINT FK_5F22899033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_version ADD CONSTRAINT FK_5159C06764D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_version ADD CONSTRAINT FK_5159C067B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_version ADD CONSTRAINT FK_5159C06799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE location_version ADD CONSTRAINT FK_5159C0679033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FD4D57CD FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307F517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307F99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307F9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_device ADD CONSTRAINT FK_760B5D84B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_device ADD CONSTRAINT FK_760B5D8499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_device ADD CONSTRAINT FK_760B5D849033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE navigation_token ADD CONSTRAINT FK_E10B31FEB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE navigation_token ADD CONSTRAINT FK_E10B31FE99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE note ADD CONSTRAINT FK_CFBDFA148D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE note ADD CONSTRAINT FK_CFBDFA14B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE note ADD CONSTRAINT FK_CFBDFA1499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE note ADD CONSTRAINT FK_CFBDFA149033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ADD CONSTRAINT FK_F52993989395C3F3 FOREIGN KEY (customer_id) REFERENCES customer (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ADD CONSTRAINT FK_F529939815ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ADD CONSTRAINT FK_F529939864D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ADD CONSTRAINT FK_F5299398B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ADD CONSTRAINT FK_F529939899049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ADD CONSTRAINT FK_F52993989033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document ADD CONSTRAINT FK_399168C78D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document ADD CONSTRAINT FK_399168C7B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document ADD CONSTRAINT FK_399168C799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document ADD CONSTRAINT FK_399168C79033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document_text_block ADD CONSTRAINT FK_BB2ABE5BD88D07B1 FOREIGN KEY (order_document_id) REFERENCES order_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document_text_block ADD CONSTRAINT FK_BB2ABE5BB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document_text_block ADD CONSTRAINT FK_BB2ABE5B99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_document_text_block ADD CONSTRAINT FK_BB2ABE5B9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_type_config ADD CONSTRAINT FK_ECEF3BC2DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_type_config ADD CONSTRAINT FK_ECEF3BC2517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_type_config ADD CONSTRAINT FK_ECEF3BC2B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_type_config ADD CONSTRAINT FK_ECEF3BC299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE order_type_config ADD CONSTRAINT FK_ECEF3BC29033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session ADD CONSTRAINT FK_D044D5D4B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session ADD CONSTRAINT FK_D044D5D499049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session ADD CONSTRAINT FK_D044D5D49033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_equipment ADD CONSTRAINT FK_90DA9089613FECDF FOREIGN KEY (session_id) REFERENCES session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_equipment ADD CONSTRAINT FK_90DA9089517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_equipment ADD CONSTRAINT FK_90DA9089B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_equipment ADD CONSTRAINT FK_90DA908999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_equipment ADD CONSTRAINT FK_90DA90899033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_tour ADD CONSTRAINT FK_ACA0F943613FECDF FOREIGN KEY (session_id) REFERENCES session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_tour ADD CONSTRAINT FK_ACA0F94315ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_tour ADD CONSTRAINT FK_ACA0F943B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_tour ADD CONSTRAINT FK_ACA0F94399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE session_tour ADD CONSTRAINT FK_ACA0F9439033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "session_user" ADD CONSTRAINT FK_4BE2D663613FECDF FOREIGN KEY (session_id) REFERENCES session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "session_user" ADD CONSTRAINT FK_4BE2D663A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "session_user" ADD CONSTRAINT FK_4BE2D663B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "session_user" ADD CONSTRAINT FK_4BE2D66399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "session_user" ADD CONSTRAINT FK_4BE2D6639033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff ADD CONSTRAINT FK_426EF392A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff ADD CONSTRAINT FK_426EF392B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff ADD CONSTRAINT FK_426EF39299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff ADD CONSTRAINT FK_426EF3929033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_config ADD CONSTRAINT FK_D1F79DC7DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_config ADD CONSTRAINT FK_D1F79DC7D4D57CD FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_config ADD CONSTRAINT FK_D1F79DC7B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_config ADD CONSTRAINT FK_D1F79DC799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_config ADD CONSTRAINT FK_D1F79DC79033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_version ADD CONSTRAINT FK_E3AAC475D4D57CD FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_version ADD CONSTRAINT FK_E3AAC475DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_version ADD CONSTRAINT FK_E3AAC475B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_version ADD CONSTRAINT FK_E3AAC47599049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE staff_version ADD CONSTRAINT FK_E3AAC4759033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task ADD CONSTRAINT FK_527EDB25B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task ADD CONSTRAINT FK_527EDB2599049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task ADD CONSTRAINT FK_527EDB259033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE58D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE515ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE564D218E FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE542B4F971 FOREIGN KEY (interruption_id) REFERENCES interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE526ED0855 FOREIGN KEY (note_id) REFERENCES note (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE5A9C0EB0D FOREIGN KEY (termination_id) REFERENCES termination (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE59FE2547F FOREIGN KEY (session_equipment_id) REFERENCES session_equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE5B5B651CF FOREIGN KEY (session_user_id) REFERENCES "session_user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE5B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE599049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group ADD CONSTRAINT FK_AA645FE59033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT FK_7391BC13FCC92519 FOREIGN KEY (key_task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT FK_7391BC13BE94330B FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT FK_7391BC13B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT FK_7391BC1399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT FK_7391BC139033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_relation ADD CONSTRAINT FK_2249AD05BE94330B FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_relation ADD CONSTRAINT FK_2249AD058DB60186 FOREIGN KEY (task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_relation ADD CONSTRAINT FK_2249AD05B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_relation ADD CONSTRAINT FK_2249AD0599049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_relation ADD CONSTRAINT FK_2249AD059033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT FK_356B38B7EF6025B8 FOREIGN KEY (key_task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT FK_356B38B774233C69 FOREIGN KEY (task_relation_id) REFERENCES task_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT FK_356B38B7B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT FK_356B38B799049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT FK_356B38B79033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE termination ADD CONSTRAINT FK_E4BFC42215ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE termination ADD CONSTRAINT FK_E4BFC4228D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE termination ADD CONSTRAINT FK_E4BFC422B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE termination ADD CONSTRAINT FK_E4BFC42299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE termination ADD CONSTRAINT FK_E4BFC4229033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour ADD CONSTRAINT FK_6AD1F969DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour ADD CONSTRAINT FK_6AD1F9695C3A313A FOREIGN KEY (start_location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour ADD CONSTRAINT FK_6AD1F969C43C7F1 FOREIGN KEY (end_location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour ADD CONSTRAINT FK_6AD1F969B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour ADD CONSTRAINT FK_6AD1F96999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour ADD CONSTRAINT FK_6AD1F9699033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_data_config ADD CONSTRAINT FK_D4224CA6DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_data_config ADD CONSTRAINT FK_D4224CA6517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_data_config ADD CONSTRAINT FK_D4224CA6B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_data_config ADD CONSTRAINT FK_D4224CA699049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_data_config ADD CONSTRAINT FK_D4224CA69033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_equipment ADD CONSTRAINT FK_6C724BCC15ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_equipment ADD CONSTRAINT FK_6C724BCC517FE9FE FOREIGN KEY (equipment_id) REFERENCES equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_equipment ADD CONSTRAINT FK_6C724BCCB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_equipment ADD CONSTRAINT FK_6C724BCC99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_equipment ADD CONSTRAINT FK_6C724BCC9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_staff ADD CONSTRAINT FK_7921E3BA15ED8D43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_staff ADD CONSTRAINT FK_7921E3BAD4D57CD FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_staff ADD CONSTRAINT FK_7921E3BAB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_staff ADD CONSTRAINT FK_7921E3BA99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tour_staff ADD CONSTRAINT FK_7921E3BA9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE translation ADD CONSTRAINT FK_B469456FB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE translation ADD CONSTRAINT FK_B469456F99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "user" ADD CONSTRAINT FK_8D93D649B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "user" ADD CONSTRAINT FK_8D93D64999049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "user" ADD CONSTRAINT FK_8D93D6499033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_setting ADD CONSTRAINT FK_C779A692A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_setting ADD CONSTRAINT FK_C779A692B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_setting ADD CONSTRAINT FK_C779A69299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_setting ADD CONSTRAINT FK_C779A6929033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT FK_5E2C358132C0FDC9');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT FK_5E2C3581C7A42F5E');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT FK_5E2C3581B03A8386');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT FK_5E2C358199049ECE');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT FK_5E2C35819033212A');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT FK_8FBBC0E7434D9F58');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT FK_8FBBC0E7B03A8386');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT FK_8FBBC0E799049ECE');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT FK_8FBBC0E79033212A');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT FK_A677FE765DA5BED0');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT FK_A677FE76B03A8386');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT FK_A677FE7699049ECE');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT FK_A677FE769033212A');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT FK_5501E26F5DA5BED0');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT FK_5501E26FB03A8386');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT FK_5501E26F99049ECE');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT FK_5501E26F9033212A');
        $this->addSql('ALTER TABLE accessible_interruption DROP CONSTRAINT FK_7EE32A9E15ED8D43');
        $this->addSql('ALTER TABLE accessible_interruption DROP CONSTRAINT FK_7EE32A9EB03A8386');
        $this->addSql('ALTER TABLE accessible_interruption DROP CONSTRAINT FK_7EE32A9E99049ECE');
        $this->addSql('ALTER TABLE accessible_interruption DROP CONSTRAINT FK_7EE32A9E9033212A');
        $this->addSql('ALTER TABLE accessible_note DROP CONSTRAINT FK_8747C5ACB03A8386');
        $this->addSql('ALTER TABLE accessible_note DROP CONSTRAINT FK_8747C5AC99049ECE');
        $this->addSql('ALTER TABLE accessible_note DROP CONSTRAINT FK_8747C5AC9033212A');
        $this->addSql('ALTER TABLE accessible_note_relation DROP CONSTRAINT FK_FE865515E816968B');
        $this->addSql('ALTER TABLE accessible_note_relation DROP CONSTRAINT FK_FE8655158D9F6D38');
        $this->addSql('ALTER TABLE accessible_note_relation DROP CONSTRAINT FK_FE865515B03A8386');
        $this->addSql('ALTER TABLE accessible_note_relation DROP CONSTRAINT FK_FE86551599049ECE');
        $this->addSql('ALTER TABLE accessible_note_relation DROP CONSTRAINT FK_FE8655159033212A');
        $this->addSql('ALTER TABLE accessible_task DROP CONSTRAINT FK_1A84E49DB03A8386');
        $this->addSql('ALTER TABLE accessible_task DROP CONSTRAINT FK_1A84E49D99049ECE');
        $this->addSql('ALTER TABLE accessible_task DROP CONSTRAINT FK_1A84E49D9033212A');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB11DC261C5F');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB11C7A42F5E');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB11E816968B');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB1164D218E');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB11B03A8386');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB1199049ECE');
        $this->addSql('ALTER TABLE accessible_task_group DROP CONSTRAINT FK_E8DECB119033212A');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT FK_F6816A3C64F12DD');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT FK_F6816A3C32C0FDC9');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT FK_F6816A3CB03A8386');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT FK_F6816A3C99049ECE');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT FK_F6816A3C9033212A');
        $this->addSql('ALTER TABLE accessible_task_relation DROP CONSTRAINT FK_AE1D63C7434D9F58');
        $this->addSql('ALTER TABLE accessible_task_relation DROP CONSTRAINT FK_AE1D63C732C0FDC9');
        $this->addSql('ALTER TABLE accessible_task_relation DROP CONSTRAINT FK_AE1D63C7B03A8386');
        $this->addSql('ALTER TABLE accessible_task_relation DROP CONSTRAINT FK_AE1D63C799049ECE');
        $this->addSql('ALTER TABLE accessible_task_relation DROP CONSTRAINT FK_AE1D63C79033212A');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT FK_3D67A09E8A34C39D');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT FK_3D67A09EDF7711E6');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT FK_3D67A09EB03A8386');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT FK_3D67A09E99049ECE');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT FK_3D67A09E9033212A');
        $this->addSql('ALTER TABLE accessible_termination DROP CONSTRAINT FK_5E2D48B3B03A8386');
        $this->addSql('ALTER TABLE accessible_termination DROP CONSTRAINT FK_5E2D48B399049ECE');
        $this->addSql('ALTER TABLE accessible_termination DROP CONSTRAINT FK_5E2D48B39033212A');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP CONSTRAINT FK_5F3616F2DC261C5F');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP CONSTRAINT FK_5F3616F215ED8D43');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP CONSTRAINT FK_5F3616F28D9F6D38');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP CONSTRAINT FK_5F3616F2B03A8386');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP CONSTRAINT FK_5F3616F299049ECE');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP CONSTRAINT FK_5F3616F29033212A');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524D15ED8D43');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524D8D9F6D38');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524DBE94330B');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524D42B4F971');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524DB03A8386');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524D99049ECE');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT FK_19D9524D9033212A');
        $this->addSql('ALTER TABLE additional_service_config DROP CONSTRAINT FK_AD3C9557DCD6CC49');
        $this->addSql('ALTER TABLE additional_service_config DROP CONSTRAINT FK_AD3C9557517FE9FE');
        $this->addSql('ALTER TABLE additional_service_config DROP CONSTRAINT FK_AD3C95577F3350C3');
        $this->addSql('ALTER TABLE additional_service_config DROP CONSTRAINT FK_AD3C9557B03A8386');
        $this->addSql('ALTER TABLE additional_service_config DROP CONSTRAINT FK_AD3C955799049ECE');
        $this->addSql('ALTER TABLE additional_service_config DROP CONSTRAINT FK_AD3C95579033212A');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE15ED8D43');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE8D9F6D38');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDEBE94330B');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE8DB60186');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE42B4F971');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE613FECDF');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDEB03A8386');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE99049ECE');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT FK_E00CEDDE9033212A');
        $this->addSql('ALTER TABLE branch DROP CONSTRAINT FK_BB861B1F64D218E');
        $this->addSql('ALTER TABLE branch DROP CONSTRAINT FK_BB861B1FB03A8386');
        $this->addSql('ALTER TABLE branch DROP CONSTRAINT FK_BB861B1F99049ECE');
        $this->addSql('ALTER TABLE branch DROP CONSTRAINT FK_BB861B1F9033212A');
        $this->addSql('ALTER TABLE connected_device DROP CONSTRAINT FK_F097D203517FE9FE');
        $this->addSql('ALTER TABLE connected_device DROP CONSTRAINT FK_F097D203B03A8386');
        $this->addSql('ALTER TABLE connected_device DROP CONSTRAINT FK_F097D20399049ECE');
        $this->addSql('ALTER TABLE connected_device DROP CONSTRAINT FK_F097D2039033212A');
        $this->addSql('ALTER TABLE customer DROP CONSTRAINT FK_81398E09B03A8386');
        $this->addSql('ALTER TABLE customer DROP CONSTRAINT FK_81398E0999049ECE');
        $this->addSql('ALTER TABLE customer DROP CONSTRAINT FK_81398E099033212A');
        $this->addSql('ALTER TABLE customer_version DROP CONSTRAINT FK_A3C1777D9395C3F3');
        $this->addSql('ALTER TABLE customer_version DROP CONSTRAINT FK_A3C1777DB03A8386');
        $this->addSql('ALTER TABLE customer_version DROP CONSTRAINT FK_A3C1777D99049ECE');
        $this->addSql('ALTER TABLE customer_version DROP CONSTRAINT FK_A3C1777D9033212A');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDF88B5AF18');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDFF86D9723');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDF3218A5DB');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDFDAE251AD');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDFB03A8386');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDF99049ECE');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT FK_4F525BDF9033212A');
        $this->addSql('ALTER TABLE default_element DROP CONSTRAINT FK_B3C50F7CB03A8386');
        $this->addSql('ALTER TABLE default_element DROP CONSTRAINT FK_B3C50F7C99049ECE');
        $this->addSql('ALTER TABLE default_element DROP CONSTRAINT FK_B3C50F7C9033212A');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT FK_57196966573C7E47');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT FK_57196966B03A8386');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT FK_5719696699049ECE');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT FK_571969669033212A');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT FK_EF74B0BE573C7E47');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT FK_EF74B0BEB03A8386');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT FK_EF74B0BE99049ECE');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT FK_EF74B0BE9033212A');
        $this->addSql('ALTER TABLE default_interruption DROP CONSTRAINT FK_DB87FE6AB03A8386');
        $this->addSql('ALTER TABLE default_interruption DROP CONSTRAINT FK_DB87FE6A99049ECE');
        $this->addSql('ALTER TABLE default_interruption DROP CONSTRAINT FK_DB87FE6A9033212A');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FB3218A5DB');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FBF6E640FA');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FB1597D6CC');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FBDAE251AD');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FBB03A8386');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FB99049ECE');
        $this->addSql('ALTER TABLE default_interruption_relation DROP CONSTRAINT FK_5AAD37FB9033212A');
        $this->addSql('ALTER TABLE default_material DROP CONSTRAINT FK_7AFA45DBB03A8386');
        $this->addSql('ALTER TABLE default_material DROP CONSTRAINT FK_7AFA45DB99049ECE');
        $this->addSql('ALTER TABLE default_material DROP CONSTRAINT FK_7AFA45DB9033212A');
        $this->addSql('ALTER TABLE default_material_relation DROP CONSTRAINT FK_D1D15A742CEBFF70');
        $this->addSql('ALTER TABLE default_material_relation DROP CONSTRAINT FK_D1D15A7437FBBFD9');
        $this->addSql('ALTER TABLE default_material_relation DROP CONSTRAINT FK_D1D15A74B03A8386');
        $this->addSql('ALTER TABLE default_material_relation DROP CONSTRAINT FK_D1D15A7499049ECE');
        $this->addSql('ALTER TABLE default_material_relation DROP CONSTRAINT FK_D1D15A749033212A');
        $this->addSql('ALTER TABLE default_note DROP CONSTRAINT FK_8A4CC112B03A8386');
        $this->addSql('ALTER TABLE default_note DROP CONSTRAINT FK_8A4CC11299049ECE');
        $this->addSql('ALTER TABLE default_note DROP CONSTRAINT FK_8A4CC1129033212A');
        $this->addSql('ALTER TABLE default_note_relation DROP CONSTRAINT FK_44F307C4D4685910');
        $this->addSql('ALTER TABLE default_note_relation DROP CONSTRAINT FK_44F307C4F86D9723');
        $this->addSql('ALTER TABLE default_note_relation DROP CONSTRAINT FK_44F307C4B03A8386');
        $this->addSql('ALTER TABLE default_note_relation DROP CONSTRAINT FK_44F307C499049ECE');
        $this->addSql('ALTER TABLE default_note_relation DROP CONSTRAINT FK_44F307C49033212A');
        $this->addSql('ALTER TABLE default_task DROP CONSTRAINT FK_178FE023B03A8386');
        $this->addSql('ALTER TABLE default_task DROP CONSTRAINT FK_178FE02399049ECE');
        $this->addSql('ALTER TABLE default_task DROP CONSTRAINT FK_178FE0239033212A');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT FK_A64CE0D77F3350C3');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT FK_A64CE0D7573C7E47');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT FK_A64CE0D7B03A8386');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT FK_A64CE0D799049ECE');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT FK_A64CE0D79033212A');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B86F86D9723');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B863218A5DB');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B86DAE251AD');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B862D488B4F');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B86D4685910');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B86F6E640FA');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B861597D6CC');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B8664D218E');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B86B03A8386');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B8699049ECE');
        $this->addSql('ALTER TABLE default_task_group DROP CONSTRAINT FK_E2470B869033212A');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT FK_EBC714CF200620BA');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT FK_EBC714CF88B5AF18');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT FK_EBC714CFB03A8386');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT FK_EBC714CF99049ECE');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT FK_EBC714CF9033212A');
        $this->addSql('ALTER TABLE default_task_relation DROP CONSTRAINT FK_146831167F3350C3');
        $this->addSql('ALTER TABLE default_task_relation DROP CONSTRAINT FK_1468311688B5AF18');
        $this->addSql('ALTER TABLE default_task_relation DROP CONSTRAINT FK_14683116B03A8386');
        $this->addSql('ALTER TABLE default_task_relation DROP CONSTRAINT FK_1468311699049ECE');
        $this->addSql('ALTER TABLE default_task_relation DROP CONSTRAINT FK_146831169033212A');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT FK_A4091010F313D71C');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT FK_A4091010FBDEF43E');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT FK_A4091010B03A8386');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT FK_A409101099049ECE');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT FK_A40910109033212A');
        $this->addSql('ALTER TABLE default_termination DROP CONSTRAINT FK_304CD794B03A8386');
        $this->addSql('ALTER TABLE default_termination DROP CONSTRAINT FK_304CD79499049ECE');
        $this->addSql('ALTER TABLE default_termination DROP CONSTRAINT FK_304CD7949033212A');
        $this->addSql('ALTER TABLE default_termination_relation DROP CONSTRAINT FK_503976BE2D488B4F');
        $this->addSql('ALTER TABLE default_termination_relation DROP CONSTRAINT FK_503976BE3218A5DB');
        $this->addSql('ALTER TABLE default_termination_relation DROP CONSTRAINT FK_503976BEF86D9723');
        $this->addSql('ALTER TABLE default_termination_relation DROP CONSTRAINT FK_503976BEB03A8386');
        $this->addSql('ALTER TABLE default_termination_relation DROP CONSTRAINT FK_503976BE99049ECE');
        $this->addSql('ALTER TABLE default_termination_relation DROP CONSTRAINT FK_503976BE9033212A');
        $this->addSql('ALTER TABLE default_unit_relation DROP CONSTRAINT FK_219631C82CEBFF70');
        $this->addSql('ALTER TABLE default_unit_relation DROP CONSTRAINT FK_219631C8B03A8386');
        $this->addSql('ALTER TABLE default_unit_relation DROP CONSTRAINT FK_219631C899049ECE');
        $this->addSql('ALTER TABLE default_unit_relation DROP CONSTRAINT FK_219631C89033212A');
        $this->addSql('ALTER TABLE delivery_service DROP CONSTRAINT FK_EB83250CD88D07B1');
        $this->addSql('ALTER TABLE delivery_service DROP CONSTRAINT FK_EB83250CB03A8386');
        $this->addSql('ALTER TABLE delivery_service DROP CONSTRAINT FK_EB83250C99049ECE');
        $this->addSql('ALTER TABLE delivery_service DROP CONSTRAINT FK_EB83250C9033212A');
        $this->addSql('ALTER TABLE device_access DROP CONSTRAINT FK_D5795A4FB03A8386');
        $this->addSql('ALTER TABLE device_access DROP CONSTRAINT FK_D5795A4F99049ECE');
        $this->addSql('ALTER TABLE device_access DROP CONSTRAINT FK_D5795A4F9033212A');
        $this->addSql('ALTER TABLE disposal_site DROP CONSTRAINT FK_E4FACF2DB03A8386');
        $this->addSql('ALTER TABLE disposal_site DROP CONSTRAINT FK_E4FACF2D99049ECE');
        $this->addSql('ALTER TABLE disposal_site DROP CONSTRAINT FK_E4FACF2D9033212A');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT FK_FA2FF13B47DEC0E4');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT FK_FA2FF13B64D218E');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT FK_FA2FF13BB03A8386');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT FK_FA2FF13B99049ECE');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT FK_FA2FF13B9033212A');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT FK_41405E398DB60186');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT FK_41405E39B03A8386');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT FK_41405E3999049ECE');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT FK_41405E399033212A');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT FK_D39109241F1F2A24');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT FK_D3910924B03A8386');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT FK_D391092499049ECE');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT FK_D39109249033212A');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT FK_D9552CAD1F1F2A24');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT FK_D9552CADB03A8386');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT FK_D9552CAD99049ECE');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT FK_D9552CAD9033212A');
        $this->addSql('ALTER TABLE equipment DROP CONSTRAINT FK_D338D583B03A8386');
        $this->addSql('ALTER TABLE equipment DROP CONSTRAINT FK_D338D58399049ECE');
        $this->addSql('ALTER TABLE equipment DROP CONSTRAINT FK_D338D5839033212A');
        $this->addSql('ALTER TABLE equipment_config DROP CONSTRAINT FK_7304503CDCD6CC49');
        $this->addSql('ALTER TABLE equipment_config DROP CONSTRAINT FK_7304503C517FE9FE');
        $this->addSql('ALTER TABLE equipment_config DROP CONSTRAINT FK_7304503CB03A8386');
        $this->addSql('ALTER TABLE equipment_config DROP CONSTRAINT FK_7304503C99049ECE');
        $this->addSql('ALTER TABLE equipment_config DROP CONSTRAINT FK_7304503C9033212A');
        $this->addSql('ALTER TABLE equipment_position DROP CONSTRAINT FK_76EF60D39033212A');
        $this->addSql('ALTER TABLE equipment_version DROP CONSTRAINT FK_C9671C2C517FE9FE');
        $this->addSql('ALTER TABLE equipment_version DROP CONSTRAINT FK_C9671C2CDCD6CC49');
        $this->addSql('ALTER TABLE equipment_version DROP CONSTRAINT FK_C9671C2CB03A8386');
        $this->addSql('ALTER TABLE equipment_version DROP CONSTRAINT FK_C9671C2C99049ECE');
        $this->addSql('ALTER TABLE equipment_version DROP CONSTRAINT FK_C9671C2C9033212A');
        $this->addSql('ALTER TABLE faq DROP CONSTRAINT FK_E8FF75CCB03A8386');
        $this->addSql('ALTER TABLE faq DROP CONSTRAINT FK_E8FF75CC99049ECE');
        $this->addSql('ALTER TABLE faq DROP CONSTRAINT FK_E8FF75CC9033212A');
        $this->addSql('ALTER TABLE feedback DROP CONSTRAINT FK_D2294458B03A8386');
        $this->addSql('ALTER TABLE feedback DROP CONSTRAINT FK_D229445899049ECE');
        $this->addSql('ALTER TABLE feedback DROP CONSTRAINT FK_D22944589033212A');
        $this->addSql('ALTER TABLE file_upload_operation DROP CONSTRAINT FK_CFEC562AB03A8386');
        $this->addSql('ALTER TABLE file_upload_operation DROP CONSTRAINT FK_CFEC562A99049ECE');
        $this->addSql('ALTER TABLE file_upload_operation DROP CONSTRAINT FK_CFEC562A9033212A');
        $this->addSql('ALTER TABLE interruption DROP CONSTRAINT FK_F9511BC015ED8D43');
        $this->addSql('ALTER TABLE interruption DROP CONSTRAINT FK_F9511BC0613FECDF');
        $this->addSql('ALTER TABLE interruption DROP CONSTRAINT FK_F9511BC0B03A8386');
        $this->addSql('ALTER TABLE interruption DROP CONSTRAINT FK_F9511BC099049ECE');
        $this->addSql('ALTER TABLE interruption DROP CONSTRAINT FK_F9511BC09033212A');
        $this->addSql('ALTER TABLE location DROP CONSTRAINT FK_5E9E89CBB03A8386');
        $this->addSql('ALTER TABLE location DROP CONSTRAINT FK_5E9E89CB99049ECE');
        $this->addSql('ALTER TABLE location DROP CONSTRAINT FK_5E9E89CB9033212A');
        $this->addSql('ALTER TABLE location_format_config DROP CONSTRAINT FK_5F2289B03A8386');
        $this->addSql('ALTER TABLE location_format_config DROP CONSTRAINT FK_5F228999049ECE');
        $this->addSql('ALTER TABLE location_format_config DROP CONSTRAINT FK_5F22899033212A');
        $this->addSql('ALTER TABLE location_version DROP CONSTRAINT FK_5159C06764D218E');
        $this->addSql('ALTER TABLE location_version DROP CONSTRAINT FK_5159C067B03A8386');
        $this->addSql('ALTER TABLE location_version DROP CONSTRAINT FK_5159C06799049ECE');
        $this->addSql('ALTER TABLE location_version DROP CONSTRAINT FK_5159C0679033212A');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT FK_B6BD307FD4D57CD');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT FK_B6BD307F517FE9FE');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT FK_B6BD307FB03A8386');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT FK_B6BD307F99049ECE');
        $this->addSql('ALTER TABLE message DROP CONSTRAINT FK_B6BD307F9033212A');
        $this->addSql('ALTER TABLE mobile_device DROP CONSTRAINT FK_760B5D84B03A8386');
        $this->addSql('ALTER TABLE mobile_device DROP CONSTRAINT FK_760B5D8499049ECE');
        $this->addSql('ALTER TABLE mobile_device DROP CONSTRAINT FK_760B5D849033212A');
        $this->addSql('ALTER TABLE navigation_token DROP CONSTRAINT FK_E10B31FEB03A8386');
        $this->addSql('ALTER TABLE navigation_token DROP CONSTRAINT FK_E10B31FE99049ECE');
        $this->addSql('ALTER TABLE note DROP CONSTRAINT FK_CFBDFA148D9F6D38');
        $this->addSql('ALTER TABLE note DROP CONSTRAINT FK_CFBDFA14B03A8386');
        $this->addSql('ALTER TABLE note DROP CONSTRAINT FK_CFBDFA1499049ECE');
        $this->addSql('ALTER TABLE note DROP CONSTRAINT FK_CFBDFA149033212A');
        $this->addSql('ALTER TABLE "order" DROP CONSTRAINT FK_F52993989395C3F3');
        $this->addSql('ALTER TABLE "order" DROP CONSTRAINT FK_F529939815ED8D43');
        $this->addSql('ALTER TABLE "order" DROP CONSTRAINT FK_F529939864D218E');
        $this->addSql('ALTER TABLE "order" DROP CONSTRAINT FK_F5299398B03A8386');
        $this->addSql('ALTER TABLE "order" DROP CONSTRAINT FK_F529939899049ECE');
        $this->addSql('ALTER TABLE "order" DROP CONSTRAINT FK_F52993989033212A');
        $this->addSql('ALTER TABLE order_document DROP CONSTRAINT FK_399168C78D9F6D38');
        $this->addSql('ALTER TABLE order_document DROP CONSTRAINT FK_399168C7B03A8386');
        $this->addSql('ALTER TABLE order_document DROP CONSTRAINT FK_399168C799049ECE');
        $this->addSql('ALTER TABLE order_document DROP CONSTRAINT FK_399168C79033212A');
        $this->addSql('ALTER TABLE order_document_text_block DROP CONSTRAINT FK_BB2ABE5BD88D07B1');
        $this->addSql('ALTER TABLE order_document_text_block DROP CONSTRAINT FK_BB2ABE5BB03A8386');
        $this->addSql('ALTER TABLE order_document_text_block DROP CONSTRAINT FK_BB2ABE5B99049ECE');
        $this->addSql('ALTER TABLE order_document_text_block DROP CONSTRAINT FK_BB2ABE5B9033212A');
        $this->addSql('ALTER TABLE order_type_config DROP CONSTRAINT FK_ECEF3BC2DCD6CC49');
        $this->addSql('ALTER TABLE order_type_config DROP CONSTRAINT FK_ECEF3BC2517FE9FE');
        $this->addSql('ALTER TABLE order_type_config DROP CONSTRAINT FK_ECEF3BC2B03A8386');
        $this->addSql('ALTER TABLE order_type_config DROP CONSTRAINT FK_ECEF3BC299049ECE');
        $this->addSql('ALTER TABLE order_type_config DROP CONSTRAINT FK_ECEF3BC29033212A');
        $this->addSql('ALTER TABLE session DROP CONSTRAINT FK_D044D5D4B03A8386');
        $this->addSql('ALTER TABLE session DROP CONSTRAINT FK_D044D5D499049ECE');
        $this->addSql('ALTER TABLE session DROP CONSTRAINT FK_D044D5D49033212A');
        $this->addSql('ALTER TABLE session_equipment DROP CONSTRAINT FK_90DA9089613FECDF');
        $this->addSql('ALTER TABLE session_equipment DROP CONSTRAINT FK_90DA9089517FE9FE');
        $this->addSql('ALTER TABLE session_equipment DROP CONSTRAINT FK_90DA9089B03A8386');
        $this->addSql('ALTER TABLE session_equipment DROP CONSTRAINT FK_90DA908999049ECE');
        $this->addSql('ALTER TABLE session_equipment DROP CONSTRAINT FK_90DA90899033212A');
        $this->addSql('ALTER TABLE session_tour DROP CONSTRAINT FK_ACA0F943613FECDF');
        $this->addSql('ALTER TABLE session_tour DROP CONSTRAINT FK_ACA0F94315ED8D43');
        $this->addSql('ALTER TABLE session_tour DROP CONSTRAINT FK_ACA0F943B03A8386');
        $this->addSql('ALTER TABLE session_tour DROP CONSTRAINT FK_ACA0F94399049ECE');
        $this->addSql('ALTER TABLE session_tour DROP CONSTRAINT FK_ACA0F9439033212A');
        $this->addSql('ALTER TABLE "session_user" DROP CONSTRAINT FK_4BE2D663613FECDF');
        $this->addSql('ALTER TABLE "session_user" DROP CONSTRAINT FK_4BE2D663A76ED395');
        $this->addSql('ALTER TABLE "session_user" DROP CONSTRAINT FK_4BE2D663B03A8386');
        $this->addSql('ALTER TABLE "session_user" DROP CONSTRAINT FK_4BE2D66399049ECE');
        $this->addSql('ALTER TABLE "session_user" DROP CONSTRAINT FK_4BE2D6639033212A');
        $this->addSql('ALTER TABLE staff DROP CONSTRAINT FK_426EF392A76ED395');
        $this->addSql('ALTER TABLE staff DROP CONSTRAINT FK_426EF392B03A8386');
        $this->addSql('ALTER TABLE staff DROP CONSTRAINT FK_426EF39299049ECE');
        $this->addSql('ALTER TABLE staff DROP CONSTRAINT FK_426EF3929033212A');
        $this->addSql('ALTER TABLE staff_config DROP CONSTRAINT FK_D1F79DC7DCD6CC49');
        $this->addSql('ALTER TABLE staff_config DROP CONSTRAINT FK_D1F79DC7D4D57CD');
        $this->addSql('ALTER TABLE staff_config DROP CONSTRAINT FK_D1F79DC7B03A8386');
        $this->addSql('ALTER TABLE staff_config DROP CONSTRAINT FK_D1F79DC799049ECE');
        $this->addSql('ALTER TABLE staff_config DROP CONSTRAINT FK_D1F79DC79033212A');
        $this->addSql('ALTER TABLE staff_version DROP CONSTRAINT FK_E3AAC475D4D57CD');
        $this->addSql('ALTER TABLE staff_version DROP CONSTRAINT FK_E3AAC475DCD6CC49');
        $this->addSql('ALTER TABLE staff_version DROP CONSTRAINT FK_E3AAC475B03A8386');
        $this->addSql('ALTER TABLE staff_version DROP CONSTRAINT FK_E3AAC47599049ECE');
        $this->addSql('ALTER TABLE staff_version DROP CONSTRAINT FK_E3AAC4759033212A');
        $this->addSql('ALTER TABLE task DROP CONSTRAINT FK_527EDB25B03A8386');
        $this->addSql('ALTER TABLE task DROP CONSTRAINT FK_527EDB2599049ECE');
        $this->addSql('ALTER TABLE task DROP CONSTRAINT FK_527EDB259033212A');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE58D9F6D38');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE515ED8D43');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE564D218E');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE542B4F971');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE526ED0855');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE5A9C0EB0D');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE59FE2547F');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE5B5B651CF');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE5B03A8386');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE599049ECE');
        $this->addSql('ALTER TABLE task_group DROP CONSTRAINT FK_AA645FE59033212A');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT FK_7391BC13FCC92519');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT FK_7391BC13BE94330B');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT FK_7391BC13B03A8386');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT FK_7391BC1399049ECE');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT FK_7391BC139033212A');
        $this->addSql('ALTER TABLE task_relation DROP CONSTRAINT FK_2249AD05BE94330B');
        $this->addSql('ALTER TABLE task_relation DROP CONSTRAINT FK_2249AD058DB60186');
        $this->addSql('ALTER TABLE task_relation DROP CONSTRAINT FK_2249AD05B03A8386');
        $this->addSql('ALTER TABLE task_relation DROP CONSTRAINT FK_2249AD0599049ECE');
        $this->addSql('ALTER TABLE task_relation DROP CONSTRAINT FK_2249AD059033212A');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT FK_356B38B7EF6025B8');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT FK_356B38B774233C69');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT FK_356B38B7B03A8386');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT FK_356B38B799049ECE');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT FK_356B38B79033212A');
        $this->addSql('ALTER TABLE termination DROP CONSTRAINT FK_E4BFC42215ED8D43');
        $this->addSql('ALTER TABLE termination DROP CONSTRAINT FK_E4BFC4228D9F6D38');
        $this->addSql('ALTER TABLE termination DROP CONSTRAINT FK_E4BFC422B03A8386');
        $this->addSql('ALTER TABLE termination DROP CONSTRAINT FK_E4BFC42299049ECE');
        $this->addSql('ALTER TABLE termination DROP CONSTRAINT FK_E4BFC4229033212A');
        $this->addSql('ALTER TABLE tour DROP CONSTRAINT FK_6AD1F969DCD6CC49');
        $this->addSql('ALTER TABLE tour DROP CONSTRAINT FK_6AD1F9695C3A313A');
        $this->addSql('ALTER TABLE tour DROP CONSTRAINT FK_6AD1F969C43C7F1');
        $this->addSql('ALTER TABLE tour DROP CONSTRAINT FK_6AD1F969B03A8386');
        $this->addSql('ALTER TABLE tour DROP CONSTRAINT FK_6AD1F96999049ECE');
        $this->addSql('ALTER TABLE tour DROP CONSTRAINT FK_6AD1F9699033212A');
        $this->addSql('ALTER TABLE tour_data_config DROP CONSTRAINT FK_D4224CA6DCD6CC49');
        $this->addSql('ALTER TABLE tour_data_config DROP CONSTRAINT FK_D4224CA6517FE9FE');
        $this->addSql('ALTER TABLE tour_data_config DROP CONSTRAINT FK_D4224CA6B03A8386');
        $this->addSql('ALTER TABLE tour_data_config DROP CONSTRAINT FK_D4224CA699049ECE');
        $this->addSql('ALTER TABLE tour_data_config DROP CONSTRAINT FK_D4224CA69033212A');
        $this->addSql('ALTER TABLE tour_equipment DROP CONSTRAINT FK_6C724BCC15ED8D43');
        $this->addSql('ALTER TABLE tour_equipment DROP CONSTRAINT FK_6C724BCC517FE9FE');
        $this->addSql('ALTER TABLE tour_equipment DROP CONSTRAINT FK_6C724BCCB03A8386');
        $this->addSql('ALTER TABLE tour_equipment DROP CONSTRAINT FK_6C724BCC99049ECE');
        $this->addSql('ALTER TABLE tour_equipment DROP CONSTRAINT FK_6C724BCC9033212A');
        $this->addSql('ALTER TABLE tour_staff DROP CONSTRAINT FK_7921E3BA15ED8D43');
        $this->addSql('ALTER TABLE tour_staff DROP CONSTRAINT FK_7921E3BAD4D57CD');
        $this->addSql('ALTER TABLE tour_staff DROP CONSTRAINT FK_7921E3BAB03A8386');
        $this->addSql('ALTER TABLE tour_staff DROP CONSTRAINT FK_7921E3BA99049ECE');
        $this->addSql('ALTER TABLE tour_staff DROP CONSTRAINT FK_7921E3BA9033212A');
        $this->addSql('ALTER TABLE translation DROP CONSTRAINT FK_B469456FB03A8386');
        $this->addSql('ALTER TABLE translation DROP CONSTRAINT FK_B469456F99049ECE');
        $this->addSql('ALTER TABLE "user" DROP CONSTRAINT FK_8D93D649B03A8386');
        $this->addSql('ALTER TABLE "user" DROP CONSTRAINT FK_8D93D64999049ECE');
        $this->addSql('ALTER TABLE "user" DROP CONSTRAINT FK_8D93D6499033212A');
        $this->addSql('ALTER TABLE user_setting DROP CONSTRAINT FK_C779A692A76ED395');
        $this->addSql('ALTER TABLE user_setting DROP CONSTRAINT FK_C779A692B03A8386');
        $this->addSql('ALTER TABLE user_setting DROP CONSTRAINT FK_C779A69299049ECE');
        $this->addSql('ALTER TABLE user_setting DROP CONSTRAINT FK_C779A6929033212A');
        $this->addSql('DROP TABLE accessible_additional_information');
        $this->addSql('DROP TABLE accessible_element');
        $this->addSql('DROP TABLE accessible_element_option');
        $this->addSql('DROP TABLE accessible_element_value');
        $this->addSql('DROP TABLE accessible_interruption');
        $this->addSql('DROP TABLE accessible_note');
        $this->addSql('DROP TABLE accessible_note_relation');
        $this->addSql('DROP TABLE accessible_task');
        $this->addSql('DROP TABLE accessible_task_group');
        $this->addSql('DROP TABLE accessible_task_group_rule');
        $this->addSql('DROP TABLE accessible_task_relation');
        $this->addSql('DROP TABLE accessible_task_rule');
        $this->addSql('DROP TABLE accessible_termination');
        $this->addSql('DROP TABLE accessible_termination_relation');
        $this->addSql('DROP TABLE additional_information');
        $this->addSql('DROP TABLE additional_service_config');
        $this->addSql('DROP TABLE booking');
        $this->addSql('DROP TABLE branch');
        $this->addSql('DROP TABLE connected_device');
        $this->addSql('DROP TABLE customer');
        $this->addSql('DROP TABLE customer_version');
        $this->addSql('DROP TABLE default_additional_information');
        $this->addSql('DROP TABLE default_element');
        $this->addSql('DROP TABLE default_element_option');
        $this->addSql('DROP TABLE default_element_value');
        $this->addSql('DROP TABLE default_interruption');
        $this->addSql('DROP TABLE default_interruption_relation');
        $this->addSql('DROP TABLE default_material');
        $this->addSql('DROP TABLE default_material_relation');
        $this->addSql('DROP TABLE default_note');
        $this->addSql('DROP TABLE default_note_relation');
        $this->addSql('DROP TABLE default_task');
        $this->addSql('DROP TABLE default_task_element_relation');
        $this->addSql('DROP TABLE default_task_group');
        $this->addSql('DROP TABLE default_task_group_rule');
        $this->addSql('DROP TABLE default_task_relation');
        $this->addSql('DROP TABLE default_task_rule');
        $this->addSql('DROP TABLE default_termination');
        $this->addSql('DROP TABLE default_termination_relation');
        $this->addSql('DROP TABLE default_unit_relation');
        $this->addSql('DROP TABLE delivery_service');
        $this->addSql('DROP TABLE device_access');
        $this->addSql('DROP TABLE disposal_site');
        $this->addSql('DROP TABLE disposal_site_version');
        $this->addSql('DROP TABLE element');
        $this->addSql('DROP TABLE element_option');
        $this->addSql('DROP TABLE element_value');
        $this->addSql('DROP TABLE equipment');
        $this->addSql('DROP TABLE equipment_config');
        $this->addSql('DROP TABLE equipment_position');
        $this->addSql('DROP TABLE equipment_version');
        $this->addSql('DROP TABLE faq');
        $this->addSql('DROP TABLE feedback');
        $this->addSql('DROP TABLE file_upload_operation');
        $this->addSql('DROP TABLE interruption');
        $this->addSql('DROP TABLE location');
        $this->addSql('DROP TABLE location_format_config');
        $this->addSql('DROP TABLE location_version');
        $this->addSql('DROP TABLE message');
        $this->addSql('DROP TABLE mobile_device');
        $this->addSql('DROP TABLE navigation_token');
        $this->addSql('DROP TABLE note');
        $this->addSql('DROP TABLE "order"');
        $this->addSql('DROP TABLE order_document');
        $this->addSql('DROP TABLE order_document_text_block');
        $this->addSql('DROP TABLE order_type_config');
        $this->addSql('DROP TABLE sap_luxembourg_container');
        $this->addSql('DROP TABLE sap_luxembourg_equipment');
        $this->addSql('DROP TABLE sap_luxembourg_tour');
        $this->addSql('DROP TABLE sap_luxembourg_user');
        $this->addSql('DROP TABLE session');
        $this->addSql('DROP TABLE session_equipment');
        $this->addSql('DROP TABLE session_tour');
        $this->addSql('DROP TABLE "session_user"');
        $this->addSql('DROP TABLE staff');
        $this->addSql('DROP TABLE staff_config');
        $this->addSql('DROP TABLE staff_version');
        $this->addSql('DROP TABLE task');
        $this->addSql('DROP TABLE task_group');
        $this->addSql('DROP TABLE task_group_rule');
        $this->addSql('DROP TABLE task_relation');
        $this->addSql('DROP TABLE task_rule');
        $this->addSql('DROP TABLE tenant');
        $this->addSql('DROP TABLE termination');
        $this->addSql('DROP TABLE tour');
        $this->addSql('DROP TABLE tour_data_config');
        $this->addSql('DROP TABLE tour_equipment');
        $this->addSql('DROP TABLE tour_staff');
        $this->addSql('DROP TABLE translation');
        $this->addSql('DROP TABLE "user"');
        $this->addSql('DROP TABLE user_setting');
        $this->addSql('DROP TABLE messenger_messages');
    }
}
