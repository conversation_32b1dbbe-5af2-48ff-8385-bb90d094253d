<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240613141904 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accessible_element ADD external_source_system VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE default_task_element_relation ADD external_source_system VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE element ADD external_source_system VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accessible_element DROP external_source_system');
        $this->addSql('ALTER TABLE element DROP external_source_system');
        $this->addSql('ALTER TABLE default_task_element_relation DROP external_source_system');
    }
}
