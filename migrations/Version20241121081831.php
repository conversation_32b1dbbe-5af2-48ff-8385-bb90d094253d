<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241121081831 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE accessible_task ADD element_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE default_task ADD element_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE task ADD element_items JSONB DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE default_task DROP element_items');
        $this->addSql('ALTER TABLE accessible_task DROP element_items');
        $this->addSql('ALTER TABLE task DROP element_items');
    }
}
