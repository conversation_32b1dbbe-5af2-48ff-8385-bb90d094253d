<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240725063901 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE staff DROP CONSTRAINT fk_426ef392a76ed395');
        $this->addSql('DROP INDEX uniq_426ef392a76ed395');
        $this->addSql('ALTER TABLE staff DROP user_id');
        $this->addSql('ALTER TABLE user_setting DROP CONSTRAINT fk_c779a692a76ed395');
        $this->addSql('DROP INDEX uniq_c779a692a76ed395');
        $this->addSql('ALTER TABLE user_setting DROP user_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_setting ADD user_id UUID NOT NULL');
        $this->addSql('ALTER TABLE user_setting ADD CONSTRAINT fk_c779a692a76ed395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX uniq_c779a692a76ed395 ON user_setting (user_id)');
        $this->addSql('ALTER TABLE staff ADD user_id UUID NOT NULL');
        $this->addSql('ALTER TABLE staff ADD CONSTRAINT fk_426ef392a76ed395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX uniq_426ef392a76ed395 ON staff (user_id)');
    }
}
