<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240924094152 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("UPDATE task_group SET type = 'MUNICIPAL_APPROACH' WHERE type = 'MUNICIPALCOLLECTION_APPROACH'");
        $this->addSql("UPDATE task_group SET type = 'MUNICIPAL_SERVICE' WHERE type = 'MUNICIP<PERSON>COLLECTION_COLLECTION'");
        $this->addSql("UPDATE task_group SET type = 'MUNICIPAL_RETURN' WHERE type = 'MUNICIPALCOLLECTION_RETURN'");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("UPDATE task_group SET type = 'MUN<PERSON><PERSON><PERSON>COLLECTION_APPROACH' WHERE type = 'MUNICIPAL_APPROACH'");
        $this->addSql("UPDATE task_group SET type = 'MUNICIPALCOLLECTION_COLLECTION' WHERE type = 'MUNICIPAL_SERVICE'");
        $this->addSql("UPDATE task_group SET type = 'MUNICIPALCOLLECTION_RETURN' WHERE type = 'MUNICIPAL_RETURN'");
    }
}
