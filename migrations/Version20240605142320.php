<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240605142320 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT fk_fa2ff13b64d218e');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT fk_fa2ff13b99049ece');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT fk_fa2ff13bb03a8386');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT fk_fa2ff13b9033212a');
        $this->addSql('ALTER TABLE disposal_site_version DROP CONSTRAINT fk_fa2ff13b47dec0e4');
        $this->addSql('ALTER TABLE disposal_site DROP CONSTRAINT fk_e4facf2d99049ece');
        $this->addSql('ALTER TABLE disposal_site DROP CONSTRAINT fk_e4facf2db03a8386');
        $this->addSql('ALTER TABLE disposal_site DROP CONSTRAINT fk_e4facf2d9033212a');
        $this->addSql('DROP TABLE disposal_site_version');
        $this->addSql('DROP TABLE disposal_site');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE TABLE disposal_site_version (id UUID NOT NULL, disposal_site_id UUID NOT NULL, location_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_fa2ff13b9033212a ON disposal_site_version (tenant_id)');
        $this->addSql('CREATE INDEX idx_fa2ff13b99049ece ON disposal_site_version (modified_by_id)');
        $this->addSql('CREATE INDEX idx_fa2ff13b47dec0e4 ON disposal_site_version (disposal_site_id)');
        $this->addSql('CREATE INDEX idx_fa2ff13bb03a8386 ON disposal_site_version (created_by_id)');
        $this->addSql('CREATE INDEX idx_fa2ff13b64d218e ON disposal_site_version (location_id)');
        $this->addSql('COMMENT ON COLUMN disposal_site_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN disposal_site_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE disposal_site (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, external_id VARCHAR(50) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_e4facf2d99049ece ON disposal_site (modified_by_id)');
        $this->addSql('CREATE INDEX idx_e4facf2db03a8386 ON disposal_site (created_by_id)');
        $this->addSql('CREATE INDEX idx_e4facf2d9033212a ON disposal_site (tenant_id)');
        $this->addSql('COMMENT ON COLUMN disposal_site.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN disposal_site.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT fk_fa2ff13b64d218e FOREIGN KEY (location_id) REFERENCES location (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT fk_fa2ff13b99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT fk_fa2ff13bb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT fk_fa2ff13b9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site_version ADD CONSTRAINT fk_fa2ff13b47dec0e4 FOREIGN KEY (disposal_site_id) REFERENCES disposal_site (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site ADD CONSTRAINT fk_e4facf2d99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site ADD CONSTRAINT fk_e4facf2db03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE disposal_site ADD CONSTRAINT fk_e4facf2d9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
