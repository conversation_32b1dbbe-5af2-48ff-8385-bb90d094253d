<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241016114051 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX idx_a87c621c94a4c7d4517fe9fe15ed8d43');
        $this->addSql('ALTER TABLE tracking ADD equipment_external_id VARCHAR(100) DEFAULT NULL');
        $this->addSql('ALTER TABLE tracking ADD tour_external_id VARCHAR(100) DEFAULT NULL');
        $this->addSql('ALTER TABLE tracking DROP equipment_id');
        $this->addSql('ALTER TABLE tracking DROP tour_id');
        $this->addSql('CREATE INDEX IDX_A87C621C94A4C7D4 ON tracking (device_id, equipment_external_id, tour_external_id)');
        $this->addSql('ALTER INDEX idx_a87c621c94a4c7d4 RENAME TO IDX_A87C621C94A4C7D4FE372397FF238573');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_A87C621C94A4C7D4');
        $this->addSql('ALTER TABLE tracking ADD equipment_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE tracking ADD tour_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE tracking DROP equipment_external_id');
        $this->addSql('ALTER TABLE tracking DROP tour_external_id');
        $this->addSql('CREATE INDEX idx_a87c621c94a4c7d4517fe9fe15ed8d43 ON tracking (device_id, equipment_id, tour_id)');
        $this->addSql('ALTER INDEX idx_a87c621c94a4c7d4fe372397ff238573 RENAME TO idx_a87c621c94a4c7d4');
    }
}
