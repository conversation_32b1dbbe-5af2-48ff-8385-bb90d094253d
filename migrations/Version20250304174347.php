<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version2************* extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE public.branch SET dako_company_id = null');
        $this->addSql('ALTER TABLE branch DROP CONSTRAINT fk_f1686f3f22e880fe');
        $this->addSql('DROP INDEX idx_f1686f3f22e880fe');
        $this->addSql('CREATE TABLE public.dako_account (name VARCHAR(255) NOT NULL, dako_id VARCHAR(255) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_47097C86B03A8386 ON public.dako_account (created_by_id)');
        $this->addSql('CREATE INDEX IDX_47097C8699049ECE ON public.dako_account (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_47097C869033212A ON public.dako_account (tenant_id)');
        $this->addSql('CREATE INDEX IDX_47097C867065677C ON public.dako_account (modified_at)');
        $this->addSql('CREATE TABLE public.dako_process (token VARCHAR(255) NOT NULL, country VARCHAR(20) NOT NULL, company VARCHAR(255) NOT NULL, status VARCHAR(20) NOT NULL, atr VARCHAR(255) NOT NULL, start TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, "end" TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, ddd_file VARCHAR(36) DEFAULT NULL, dako_apdu_exchanges JSONB DEFAULT \'[]\' NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, equipment_id UUID NOT NULL, session_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_BC2232B4517FE9FE ON public.dako_process (equipment_id)');
        $this->addSql('CREATE INDEX IDX_BC2232B4613FECDF ON public.dako_process (session_id)');
        $this->addSql('CREATE INDEX IDX_BC2232B4B03A8386 ON public.dako_process (created_by_id)');
        $this->addSql('CREATE INDEX IDX_BC2232B499049ECE ON public.dako_process (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_BC2232B49033212A ON public.dako_process (tenant_id)');
        $this->addSql('CREATE INDEX IDX_BC2232B47065677C ON public.dako_process (modified_at)');
        $this->addSql('ALTER TABLE public.dako_account ADD CONSTRAINT FK_47097C86B03A8386 FOREIGN KEY (created_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_account ADD CONSTRAINT FK_47097C8699049ECE FOREIGN KEY (modified_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_account ADD CONSTRAINT FK_47097C869033212A FOREIGN KEY (tenant_id) REFERENCES public.tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_process ADD CONSTRAINT FK_BC2232B4517FE9FE FOREIGN KEY (equipment_id) REFERENCES public.equipment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_process ADD CONSTRAINT FK_BC2232B4613FECDF FOREIGN KEY (session_id) REFERENCES public.session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_process ADD CONSTRAINT FK_BC2232B4B03A8386 FOREIGN KEY (created_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_process ADD CONSTRAINT FK_BC2232B499049ECE FOREIGN KEY (modified_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_process ADD CONSTRAINT FK_BC2232B49033212A FOREIGN KEY (tenant_id) REFERENCES public.tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE dako_company DROP CONSTRAINT fk_7580236db03a8386');
        $this->addSql('ALTER TABLE dako_company DROP CONSTRAINT fk_7580236d99049ece');
        $this->addSql('ALTER TABLE dako_company DROP CONSTRAINT fk_7580236d9033212a');
        $this->addSql('DROP TABLE dako_company');


        $this->addSql('ALTER TABLE branch RENAME COLUMN dako_company_id TO dako_account_id');
        $this->addSql('ALTER TABLE branch ADD CONSTRAINT FK_F1686F3F2E18C592 FOREIGN KEY (dako_account_id) REFERENCES public.dako_account (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F1686F3F2E18C592 ON branch (dako_account_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE dako_company (name VARCHAR(255) NOT NULL, dako_id VARCHAR(255) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_7580236d7065677c ON dako_company (modified_at)');
        $this->addSql('CREATE INDEX idx_7580236d9033212a ON dako_company (tenant_id)');
        $this->addSql('CREATE INDEX idx_7580236d99049ece ON dako_company (modified_by_id)');
        $this->addSql('CREATE INDEX idx_7580236db03a8386 ON dako_company (created_by_id)');
        $this->addSql('ALTER TABLE dako_company ADD CONSTRAINT fk_7580236db03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE dako_company ADD CONSTRAINT fk_7580236d99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE dako_company ADD CONSTRAINT fk_7580236d9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_account DROP CONSTRAINT FK_47097C86B03A8386');
        $this->addSql('ALTER TABLE public.dako_account DROP CONSTRAINT FK_47097C8699049ECE');
        $this->addSql('ALTER TABLE public.dako_account DROP CONSTRAINT FK_47097C869033212A');
        $this->addSql('ALTER TABLE public.dako_process DROP CONSTRAINT FK_BC2232B4517FE9FE');
        $this->addSql('ALTER TABLE public.dako_process DROP CONSTRAINT FK_BC2232B4613FECDF');
        $this->addSql('ALTER TABLE public.dako_process DROP CONSTRAINT FK_BC2232B4B03A8386');
        $this->addSql('ALTER TABLE public.dako_process DROP CONSTRAINT FK_BC2232B499049ECE');
        $this->addSql('ALTER TABLE public.dako_process DROP CONSTRAINT FK_BC2232B49033212A');
        $this->addSql('DROP TABLE public.dako_account');
        $this->addSql('DROP TABLE public.dako_process');
        $this->addSql('ALTER TABLE public.branch DROP CONSTRAINT FK_F1686F3F2E18C592');
        $this->addSql('DROP INDEX IDX_F1686F3F2E18C592');
        $this->addSql('ALTER TABLE public.branch RENAME COLUMN dako_account_id TO dako_company_id');
        $this->addSql('ALTER TABLE public.branch ADD CONSTRAINT fk_f1686f3f22e880fe FOREIGN KEY (dako_company_id) REFERENCES dako_company (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_f1686f3f22e880fe ON public.branch (dako_company_id)');
    }
}
