<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250414062706 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document DROP CONSTRAINT fk_399168c78d9f6d38
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document DROP CONSTRAINT fk_399168c79033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document DROP CONSTRAINT fk_399168c799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document DROP CONSTRAINT fk_399168c7b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service DROP CONSTRAINT fk_eb83250c9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service DROP CONSTRAINT fk_eb83250c99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service DROP CONSTRAINT fk_eb83250cb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service DROP CONSTRAINT fk_eb83250cd88d07b1
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block DROP CONSTRAINT fk_bb2abe5b9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block DROP CONSTRAINT fk_bb2abe5b99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block DROP CONSTRAINT fk_bb2abe5bb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block DROP CONSTRAINT fk_bb2abe5bd88d07b1
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE order_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE delivery_service
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE order_document_text_block
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE order_document (id UUID NOT NULL, order_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, document_type VARCHAR(100) NOT NULL, document_name VARCHAR(255) DEFAULT NULL, document_path TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e6cdc4b3b03a8386 ON order_document (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e6cdc4b399049ece ON order_document (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e6cdc4b39033212a ON order_document (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e6cdc4b38d9f6d38 ON order_document (order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e6cdc4b37065677c ON order_document (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE delivery_service (id UUID NOT NULL, order_document_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, material_number VARCHAR(100) NOT NULL, text VARCHAR(255) NOT NULL, task_external_id VARCHAR(100) DEFAULT NULL, template_field VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, unit VARCHAR(255) DEFAULT NULL, element_reference_type VARCHAR(100) DEFAULT 'containerAmount' NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7fb0a961d88d07b1 ON delivery_service (order_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7fb0a961b03a8386 ON delivery_service (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7fb0a96199049ece ON delivery_service (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7fb0a9619033212a ON delivery_service (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_7fb0a9617065677c ON delivery_service (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE order_document_text_block (id UUID NOT NULL, order_document_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, block_id VARCHAR(100) NOT NULL, text TEXT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_deea3a7d88d07b1 ON order_document_text_block (order_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_deea3a7b03a8386 ON order_document_text_block (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_deea3a799049ece ON order_document_text_block (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_deea3a79033212a ON order_document_text_block (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_deea3a77065677c ON order_document_text_block (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document ADD CONSTRAINT fk_399168c78d9f6d38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document ADD CONSTRAINT fk_399168c79033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document ADD CONSTRAINT fk_399168c799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document ADD CONSTRAINT fk_399168c7b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service ADD CONSTRAINT fk_eb83250c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service ADD CONSTRAINT fk_eb83250c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service ADD CONSTRAINT fk_eb83250cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE delivery_service ADD CONSTRAINT fk_eb83250cd88d07b1 FOREIGN KEY (order_document_id) REFERENCES order_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block ADD CONSTRAINT fk_bb2abe5b9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block ADD CONSTRAINT fk_bb2abe5b99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block ADD CONSTRAINT fk_bb2abe5bb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_document_text_block ADD CONSTRAINT fk_bb2abe5bd88d07b1 FOREIGN KEY (order_document_id) REFERENCES order_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }
}
