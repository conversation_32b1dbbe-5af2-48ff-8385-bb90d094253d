<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250714114536 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "user" ADD groups JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE "user" ALTER firstname TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE "user" ALTER firstname SET NOT NULL');
        $this->addSql('ALTER TABLE "user" ALTER lastname TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE "user" ALTER lastname SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public."user" DROP groups');
        $this->addSql('ALTER TABLE public."user" ALTER firstname TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE public."user" ALTER firstname DROP NOT NULL');
        $this->addSql('ALTER TABLE public."user" ALTER lastname TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE public."user" ALTER lastname DROP NOT NULL');
    }
}
