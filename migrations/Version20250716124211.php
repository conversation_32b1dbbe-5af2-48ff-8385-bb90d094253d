<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250716124211 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('TRUNCATE TABLE mobile_app_release');
        $this->addSql('ALTER TABLE mobile_app_release ADD "type" VARCHAR(20) NOT NULL');
        $this->addSql('ALTER TABLE mobile_app_release ADD files JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE mobile_app_release DROP file_path');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.mobile_app_release ADD file_path VARCHAR(150) NOT NULL');
        $this->addSql('ALTER TABLE public.mobile_app_release DROP "type"');
        $this->addSql('ALTER TABLE public.mobile_app_release DROP files');
    }
}
