<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250328101358 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process RENAME COLUMN company TO account
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process ALTER account TYPE VARCHAR(255)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process RENAME COLUMN account TO company
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process ALTER company TYPE VARCHAR(255)
        SQL);
    }
}
