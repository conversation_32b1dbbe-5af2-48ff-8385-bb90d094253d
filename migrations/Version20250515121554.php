<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250515121554 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_interruption DROP CONSTRAINT fk_7ee32a9e9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_295367bd9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_interruption DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_interruption RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_interruption ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_295367BD4E59C462 ON accessible_interruption (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note DROP CONSTRAINT fk_8747c5ac9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d0f02c259033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D0F02C254E59C462 ON accessible_note (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note_relation DROP CONSTRAINT fk_fe8655159033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_5cb6942a9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5CB6942A4E59C462 ON accessible_note_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task DROP CONSTRAINT fk_1a84e49d9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_4d330d149033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4D330D144E59C462 ON accessible_task (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_group DROP CONSTRAINT fk_e8decb119033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1df365b9f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1df365b99033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_group DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_group RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_group ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1DF365B94E59C462 ON accessible_task_group (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_relation DROP CONSTRAINT fk_ae1d63c79033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c2da2f8f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c2da2f89033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C2DA2F84E59C462 ON accessible_task_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination DROP CONSTRAINT fk_5e2d48b39033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_86d54ec79033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_86D54EC74E59C462 ON accessible_termination (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination_relation DROP CONSTRAINT fk_5f3616f29033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dea67509033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9DEA67504E59C462 ON accessible_termination_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE additional_service_config DROP CONSTRAINT fk_ad3c95579033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1bf888ab9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE additional_service_config DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE additional_service_config RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE additional_service_config ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1BF888AB4E59C462 ON additional_service_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_5082e87814f53ecdbf396750
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5082E87814F53ECD ON big_query_sync_status (table_name)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE branch DROP CONSTRAINT fk_bb861b1f9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f1686f3f9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE branch DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE branch RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE branch ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F1686F3F4E59C462 ON branch (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE connected_device DROP CONSTRAINT fk_f097d2039033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_64a45e6e9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE connected_device DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE connected_device RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE connected_device ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_64A45E6E4E59C462 ON connected_device (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer DROP CONSTRAINT fk_81398e099033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_43d555609033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_43D555604E59C462 ON customer (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_version DROP CONSTRAINT fk_a3c1777d9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_37f2fb109033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_version DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_version RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_version ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_37F2FB104E59C462 ON customer_version (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_account DROP CONSTRAINT fk_47097c869033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_47097c869033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_account DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_account RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_account ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_47097C864E59C462 ON dako_account (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process DROP CONSTRAINT fk_bc2232b49033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_bc2232b49033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_BC2232B44E59C462 ON dako_process (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption DROP CONSTRAINT fk_db87fe6a9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f9ed013d9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F9ED013D4E59C462 ON default_interruption (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption_relation DROP CONSTRAINT fk_5aad37fb9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f77aa34f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f77aa349033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F77AA344E59C462 ON default_interruption_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material DROP CONSTRAINT fk_7afa45db9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_eec9c9b69033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EEC9C9B64E59C462 ON default_material (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material_relation DROP CONSTRAINT fk_d1d15a749033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_67154788f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_671547889033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_671547884E59C462 ON default_material_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note DROP CONSTRAINT fk_8a4cc1129033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ca1fbc369033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_CA1FBC364E59C462 ON default_note (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note_relation DROP CONSTRAINT fk_44f307c49033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b1dea96cf2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b1dea96c9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B1DEA96C4E59C462 ON default_note_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task DROP CONSTRAINT fk_178fe0239033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_57dc9d079033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_57DC9D074E59C462 ON default_task (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_group DROP CONSTRAINT fk_e2470b869033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d61a4554f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d61a45549033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_group DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_group RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_group ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D61A45544E59C462 ON default_task_group (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_relation DROP CONSTRAINT fk_146831169033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e1459fbef2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e1459fbe9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E1459FBE4E59C462 ON default_task_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination DROP CONSTRAINT fk_304cd7949033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_58a539229033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_58A539224E59C462 ON default_termination (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination_relation DROP CONSTRAINT fk_503976be9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_8f829609f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_8f8296099033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8F8296094E59C462 ON default_termination_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_unit_relation DROP CONSTRAINT fk_219631c89033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d4bb9f60f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d4bb9f609033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_unit_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_unit_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_unit_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D4BB9F604E59C462 ON default_unit_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_access DROP CONSTRAINT fk_d5795a4f9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e93aede39033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_access DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_access RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_access ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E93AEDE34E59C462 ON device_access (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message DROP CONSTRAINT fk_3c0be9e29033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e35745969033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E35745964E59C462 ON device_message (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread DROP CONSTRAINT fk_a04a207c9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_55678ed4b4f0dba7
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_55678ed49033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_55678ED4B4F0DBA74E59C462 ON device_message_thread USING gin (search gin_trgm_ops, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_55678ED44E59C462 ON device_message_thread (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document DROP CONSTRAINT fk_1a85511f9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511f15ed8d43
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511f8d9f6d38
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511f9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F8D9F6D384E59C462 ON document (order_id, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F15ED8D434E59C462 ON document (tour_id, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F4E59C462 ON document (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment DROP CONSTRAINT fk_d338d5839033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e794e0a49f75d7b0
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e794e0a4b4f0dba7
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e794e0a49033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E794E0A4B4F0DBA74E59C462 ON equipment USING gin (search gin_trgm_ops, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E794E0A49F75D7B04E59C462 ON equipment (external_id, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E794E0A44E59C462 ON equipment (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_config DROP CONSTRAINT fk_7304503c9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e737dc519033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_config DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_config RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_config ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E737DC514E59C462 ON equipment_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_position DROP CONSTRAINT fk_76ef60d39033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_42b22e019033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_position DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_position RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_position ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_42B22E01D74808F5 ON equipment_position (recorded_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_42B22E014E59C462 ON equipment_position (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_version DROP CONSTRAINT fk_c9671c2c9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_faf032459033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_version DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_version RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_version ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FAF032454E59C462 ON equipment_version (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE faq DROP CONSTRAINT fk_e8ff75cc9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d715cdff9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE faq DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE faq RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE faq ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D715CDFF4E59C462 ON faq (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE feedback DROP CONSTRAINT fk_d22944589033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_10c59f319033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE feedback DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE feedback RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE feedback ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_10C59F314E59C462 ON feedback (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE interruption DROP CONSTRAINT fk_f9511bc09033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b90266e49033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE interruption DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE interruption RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE interruption ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B90266E44E59C462 ON interruption (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location DROP CONSTRAINT fk_5e9e89cb9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9c7252a29033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9C7252A24E59C462 ON location (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_format_config DROP CONSTRAINT fk_5f22899033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d8a724fd5286d72b
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d8a724fd9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_format_config DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_format_config RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_format_config ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D8A724FD4E59C462 ON location_format_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_version DROP CONSTRAINT fk_5159c0679033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c56a4c0a9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_version DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_version RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_version ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C56A4C0A4E59C462 ON location_version (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_progress DROP CONSTRAINT fk_7f7db6c39033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_179458759033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_progress DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_progress RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_progress ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_179458754E59C462 ON mastertour_progress (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_template DROP CONSTRAINT fk_ca1c5b069033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a2f5b5b0b4f0dba7
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX uniq_a2f5b5b09033212a9f75d7b0
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a2f5b5b09033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_template DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_template RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_template ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A2F5B5B0B4F0DBA74E59C462 ON mastertour_template USING gin (search gin_trgm_ops, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A2F5B5B04E59C462 ON mastertour_template (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_A2F5B5B04E59C4629F75D7B0 ON mastertour_template (tenant, external_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE note DROP CONSTRAINT fk_cfbdfa149033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_705271ba9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE note DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE note RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE note ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_705271BA4E59C462 ON note (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "order" DROP CONSTRAINT fk_f52993989033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c4f8f2fc9e8baf78
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c4f8f2fc9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "order" DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "order" RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "order" ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C4F8F2FC9E8BAF784E59C462 ON "order" (order_ext_id, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C4F8F2FC4E59C462 ON "order" (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_type_config DROP CONSTRAINT fk_ecef3bc29033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_df7815ab9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_type_config DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_type_config RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_type_config ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DF7815AB4E59C462 ON order_type_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE point_of_interest DROP CONSTRAINT fk_e67ad3599033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d5edfd309033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE point_of_interest DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE point_of_interest RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE point_of_interest ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D5EDFD304E59C462 ON point_of_interest (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE sap_retry_queue DROP CONSTRAINT fk_ca76e3029033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dc10a8b2d8794ba
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dc10a8b9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE sap_retry_queue DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE sap_retry_queue RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE sap_retry_queue ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session DROP CONSTRAINT fk_d044d5d49033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_eb601b689033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EB601B684E59C462 ON session (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_booking DROP CONSTRAINT fk_a563860d9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a563860d613fecdf
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a563860d9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_booking DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_booking RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_booking ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A563860D613FECDF4E59C462 ON session_booking (session_id, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A563860D4E59C462 ON session_booking (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_equipment DROP CONSTRAINT fk_90da90899033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a34dbee09033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_equipment DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_equipment RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_equipment ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A34DBEE04E59C462 ON session_equipment (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_tour DROP CONSTRAINT fk_aca0f9439033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ecf384679033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_tour DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_tour RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_tour ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_ECF384674E59C462 ON session_tour (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "session_user" DROP CONSTRAINT fk_4be2d6639033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_bb1ab479033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "session_user" DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "session_user" RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "session_user" ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_BB1AB474E59C462 ON "session_user" (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff DROP CONSTRAINT fk_426ef3929033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_73bf92f6b4f0dba7
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_73bf92f69033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_73BF92F6B4F0DBA74E59C462 ON staff USING gin (search gin_trgm_ops, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_73BF92F64E59C462 ON staff (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_config DROP CONSTRAINT fk_d1f79dc79033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_91a4e0e39033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_config DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_config RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_config ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_91A4E0E34E59C462 ON staff_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_version DROP CONSTRAINT fk_e3aac4759033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_dfe973d99033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_version DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_version RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_version ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DFE973D94E59C462 ON staff_version (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task DROP CONSTRAINT fk_527edb259033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ed91508b9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_ED91508B4E59C462 ON task (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_group DROP CONSTRAINT fk_aa645fe59033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f5a46bbf2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f5a46bb9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_group DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_group RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_group ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F5A46BB4E59C462 ON task_group (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_relation DROP CONSTRAINT fk_2249ad059033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1e0a1aa9f2803b3d
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1e0a1aa99033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_relation DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_relation RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_relation ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1E0A1AA94E59C462 ON task_relation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE termination DROP CONSTRAINT fk_e4bfc4229033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_68c986c89033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE termination DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE termination RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE termination ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_68C986C84E59C462 ON termination (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour DROP CONSTRAINT fk_6ad1f9699033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d53e72c79f75d7b0
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d53e72c79033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D53E72C79F75D7B04E59C462 ON tour (external_id, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D53E72C74E59C462 ON tour (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_data_config DROP CONSTRAINT fk_d4224ca69033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_4011c0cb9033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_data_config DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_data_config RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_data_config ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4011C0CB4E59C462 ON tour_data_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_equipment DROP CONSTRAINT fk_6c724bcc9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b32ee7b89033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_equipment DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_equipment RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_equipment ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B32EE7B84E59C462 ON tour_equipment (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_staff DROP CONSTRAINT fk_7921e3ba9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_dc1ffae49033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_staff DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_staff RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_staff ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DC1FFAE44E59C462 ON tour_staff (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tracking DROP CONSTRAINT fk_a87c621c9033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_6a90b9759033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tracking DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tracking RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tracking ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6A90B9754E59C462 ON tracking (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" DROP CONSTRAINT fk_8d93d6499033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_327c5de7b4f0dba7
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_327c5de79033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_327C5DE7B4F0DBA74E59C462 ON "user" USING gin (search gin_trgm_ops, tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_327C5DE74E59C462 ON "user" (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_setting DROP CONSTRAINT fk_c779a6929033212a
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_872adbb69033212a
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_setting DROP tenant_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_setting RENAME COLUMN tenant_identifier TO tenant
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_setting ALTER tenant TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_872ADBB64E59C462 ON user_setting (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE tenant
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE tenant (id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, name VARCHAR(50) NOT NULL, shortcut VARCHAR(3) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX shortcut ON tenant (shortcut)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_DF7815AB4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config ADD CONSTRAINT fk_ecef3bc29033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_df7815ab9033212a ON public.order_type_config (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_F5A46BB4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group ADD CONSTRAINT fk_aa645fe59033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f5a46bbf2803b3d ON public.task_group (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f5a46bb9033212a ON public.task_group (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue ADD CONSTRAINT fk_ca76e3029033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dc10a8b2d8794ba ON public.sap_retry_queue (microtime)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dc10a8b9033212a ON public.sap_retry_queue (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_6A90B9754E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking ADD CONSTRAINT fk_a87c621c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6a90b9759033212a ON public.tracking (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_BB1AB474E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" ADD CONSTRAINT fk_4be2d6639033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bb1ab479033212a ON public."session_user" (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_4D330D144E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task ADD CONSTRAINT fk_1a84e49d9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4d330d149033212a ON public.accessible_task (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_179458754E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress ADD CONSTRAINT fk_7f7db6c39033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_179458759033212a ON public.mastertour_progress (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_55678ED4B4F0DBA74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_55678ED44E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ADD CONSTRAINT fk_a04a207c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_55678ed4b4f0dba7 ON public.device_message_thread (search)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_55678ed49033212a ON public.device_message_thread (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_5082E87814F53ECD
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_5082e87814f53ecdbf396750 ON public.big_query_sync_status (table_name, id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_A563860D613FECDF4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_A563860D4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking ADD CONSTRAINT fk_a563860d9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a563860d613fecdf ON public.session_booking (session_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a563860d9033212a ON public.session_booking (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_F77AA344E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation ADD CONSTRAINT fk_5aad37fb9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f77aa34f2803b3d ON public.default_interruption_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f77aa349033212a ON public.default_interruption_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_FAF032454E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version ADD CONSTRAINT fk_c9671c2c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_faf032459033212a ON public.equipment_version (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_EB601B684E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session ADD CONSTRAINT fk_d044d5d49033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_eb601b689033212a ON public.session (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_86D54EC74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination ADD CONSTRAINT fk_5e2d48b39033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_86d54ec79033212a ON public.accessible_termination (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_43D555604E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer ADD CONSTRAINT fk_81398e099033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_43d555609033212a ON public.customer (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E794E0A4B4F0DBA74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E794E0A49F75D7B04E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E794E0A44E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment ADD CONSTRAINT fk_d338d5839033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e794e0a49f75d7b0 ON public.equipment (external_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e794e0a4b4f0dba7 ON public.equipment (search)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e794e0a49033212a ON public.equipment (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E1459FBE4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation ADD CONSTRAINT fk_146831169033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e1459fbef2803b3d ON public.default_task_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e1459fbe9033212a ON public.default_task_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D53E72C79F75D7B04E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D53E72C74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour ADD CONSTRAINT fk_6ad1f9699033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d53e72c79f75d7b0 ON public.tour (external_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d53e72c79033212a ON public.tour (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_47097C864E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account ADD CONSTRAINT fk_47097c869033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_47097c869033212a ON public.dako_account (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_ED91508B4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task ADD CONSTRAINT fk_527edb259033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ed91508b9033212a ON public.task (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1DF365B94E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group ADD CONSTRAINT fk_e8decb119033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1df365b9f2803b3d ON public.accessible_task_group (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1df365b99033212a ON public.accessible_task_group (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D0F02C254E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note ADD CONSTRAINT fk_8747c5ac9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d0f02c259033212a ON public.accessible_note (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_37F2FB104E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version ADD CONSTRAINT fk_a3c1777d9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_37f2fb109033212a ON public.customer_version (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1E0A1AA94E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation ADD CONSTRAINT fk_2249ad059033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1e0a1aa9f2803b3d ON public.task_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1e0a1aa99033212a ON public.task_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_DFE973D94E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version ADD CONSTRAINT fk_e3aac4759033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_dfe973d99033212a ON public.staff_version (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_A34DBEE04E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment ADD CONSTRAINT fk_90da90899033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a34dbee09033212a ON public.session_equipment (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_4011C0CB4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config ADD CONSTRAINT fk_d4224ca69033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4011c0cb9033212a ON public.tour_data_config (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_9C7252A24E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location ADD CONSTRAINT fk_5e9e89cb9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9c7252a29033212a ON public.location (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_10C59F314E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback ADD CONSTRAINT fk_d22944589033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_10c59f319033212a ON public.feedback (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_C56A4C0A4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version ADD CONSTRAINT fk_5159c0679033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c56a4c0a9033212a ON public.location_version (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_C4F8F2FC9E8BAF784E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_C4F8F2FC4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" ADD CONSTRAINT fk_f52993989033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c4f8f2fc9e8baf78 ON public."order" (order_ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c4f8f2fc9033212a ON public."order" (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D5EDFD304E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest ADD CONSTRAINT fk_e67ad3599033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d5edfd309033212a ON public.point_of_interest (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_F9ED013D4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption ADD CONSTRAINT fk_db87fe6a9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f9ed013d9033212a ON public.default_interruption (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_F1686F3F4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch ADD CONSTRAINT fk_bb861b1f9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f1686f3f9033212a ON public.branch (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D4BB9F604E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation ADD CONSTRAINT fk_219631c89033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d4bb9f60f2803b3d ON public.default_unit_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d4bb9f609033212a ON public.default_unit_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D8A724FD4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config ADD CONSTRAINT fk_5f22899033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d8a724fd5286d72b ON public.location_format_config (sequence)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d8a724fd9033212a ON public.location_format_config (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D715CDFF4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq ADD CONSTRAINT fk_e8ff75cc9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d715cdff9033212a ON public.faq (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E93AEDE34E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access ADD CONSTRAINT fk_d5795a4f9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e93aede39033212a ON public.device_access (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_73BF92F6B4F0DBA74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_73BF92F64E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff ADD CONSTRAINT fk_426ef3929033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_73bf92f6b4f0dba7 ON public.staff (search)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_73bf92f69033212a ON public.staff (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_5CB6942A4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation ADD CONSTRAINT fk_fe8655159033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_5cb6942a9033212a ON public.accessible_note_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_872ADBB64E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting ADD CONSTRAINT fk_c779a6929033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_872adbb69033212a ON public.user_setting (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_D61A45544E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group ADD CONSTRAINT fk_e2470b869033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d61a4554f2803b3d ON public.default_task_group (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d61a45549033212a ON public.default_task_group (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_327C5DE7B4F0DBA74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_327C5DE74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" ADD CONSTRAINT fk_8d93d6499033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_327c5de7b4f0dba7 ON public."user" (search)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_327c5de79033212a ON public."user" (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E737DC514E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config ADD CONSTRAINT fk_7304503c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e737dc519033212a ON public.equipment_config (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_8F8296094E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation ADD CONSTRAINT fk_503976be9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_8f829609f2803b3d ON public.default_termination_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_8f8296099033212a ON public.default_termination_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_671547884E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation ADD CONSTRAINT fk_d1d15a749033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_67154788f2803b3d ON public.default_material_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_671547889033212a ON public.default_material_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_57DC9D074E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task ADD CONSTRAINT fk_178fe0239033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_57dc9d079033212a ON public.default_task (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1BF888AB4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config ADD CONSTRAINT fk_ad3c95579033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1bf888ab9033212a ON public.additional_service_config (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_91A4E0E34E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config ADD CONSTRAINT fk_d1f79dc79033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_91a4e0e39033212a ON public.staff_config (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_B32EE7B84E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment ADD CONSTRAINT fk_6c724bcc9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b32ee7b89033212a ON public.tour_equipment (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_CA1FBC364E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note ADD CONSTRAINT fk_8a4cc1129033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ca1fbc369033212a ON public.default_note (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_42B22E01D74808F5
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_42B22E014E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_position ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_position RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_position ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_position ADD CONSTRAINT fk_76ef60d39033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_42b22e019033212a ON public.equipment_position (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_B90266E44E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption ADD CONSTRAINT fk_f9511bc09033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b90266e49033212a ON public.interruption (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_705271BA4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note ADD CONSTRAINT fk_cfbdfa149033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_705271ba9033212a ON public.note (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_E35745964E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message ADD CONSTRAINT fk_3c0be9e29033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e35745969033212a ON public.device_message (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_BC2232B44E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process ADD CONSTRAINT fk_bc2232b49033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bc2232b49033212a ON public.dako_process (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_68C986C84E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination ADD CONSTRAINT fk_e4bfc4229033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_68c986c89033212a ON public.termination (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_B1DEA96C4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation ADD CONSTRAINT fk_44f307c49033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b1dea96cf2803b3d ON public.default_note_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b1dea96c9033212a ON public.default_note_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1A85511F8D9F6D384E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1A85511F15ED8D434E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1A85511F4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD CONSTRAINT fk_1a85511f9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511f15ed8d43 ON public.document (tour_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511f8d9f6d38 ON public.document (order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511f9033212a ON public.document (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_9DEA67504E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation ADD CONSTRAINT fk_5f3616f29033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dea67509033212a ON public.accessible_termination_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_58A539224E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination ADD CONSTRAINT fk_304cd7949033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_58a539229033212a ON public.default_termination (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_A2F5B5B0B4F0DBA74E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_A2F5B5B04E59C462
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.UNIQ_A2F5B5B04E59C4629F75D7B0
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template ADD CONSTRAINT fk_ca1c5b069033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a2f5b5b0b4f0dba7 ON public.mastertour_template (search)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_a2f5b5b09033212a9f75d7b0 ON public.mastertour_template (tenant_id, external_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a2f5b5b09033212a ON public.mastertour_template (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_ECF384674E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour ADD CONSTRAINT fk_aca0f9439033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ecf384679033212a ON public.session_tour (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_C2DA2F84E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation ADD CONSTRAINT fk_ae1d63c79033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c2da2f8f2803b3d ON public.accessible_task_relation (sequence_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c2da2f89033212a ON public.accessible_task_relation (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_64A45E6E4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device ADD CONSTRAINT fk_f097d2039033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_64a45e6e9033212a ON public.connected_device (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_DC1FFAE44E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff ADD CONSTRAINT fk_7921e3ba9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_dc1ffae49033212a ON public.tour_staff (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_EEC9C9B64E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material ADD CONSTRAINT fk_7afa45db9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_eec9c9b69033212a ON public.default_material (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_295367BD4E59C462
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption ADD tenant_id UUID NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption RENAME COLUMN tenant TO tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption ALTER tenant_identifier TYPE VARCHAR(5)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption ADD CONSTRAINT fk_7ee32a9e9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_295367bd9033212a ON public.accessible_interruption (tenant_id)
        SQL);
    }
}
