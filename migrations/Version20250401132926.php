<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250401132926 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE sap_luxembourg_user
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE sap_luxembourg_equipment
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE sap_luxembourg_container
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE sap_luxembourg_tour
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE sap_luxembourg_user (id INT NOT NULL, first_name VARCHAR(100) NOT NULL, last_name VARCHAR(100) NOT NULL, is_driver BOOLEAN NOT NULL, display_number VARCHAR(255) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE sap_luxembourg_equipment (id VARCHAR(100) NOT NULL, type VARCHAR(255) NOT NULL, height INT NOT NULL, length INT NOT NULL, width INT NOT NULL, weight INT NOT NULL, minimum_load INT NOT NULL, overload INT NOT NULL, total_permissible_weight INT NOT NULL, max_axle_load INT NOT NULL, container_mounting VARCHAR(100) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE sap_luxembourg_container (id VARCHAR(100) NOT NULL, type_id INT NOT NULL, serial_number VARCHAR(100) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE sap_luxembourg_tour (id UUID NOT NULL, driver_display_number VARCHAR(100) NOT NULL, truck_id VARCHAR(100) NOT NULL, date DATE NOT NULL, hash VARCHAR(100) NOT NULL, products JSONB DEFAULT NULL, created_tour_id UUID DEFAULT NULL, PRIMARY KEY(id))
        SQL);
    }
}
