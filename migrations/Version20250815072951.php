<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250815072951 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX uniq_9b6a981fbf1cd3c3');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_9B6A981FBF1CD3C378D81AD1 ON mobile_app_release (version, "type")');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX public.UNIQ_9B6A981FBF1CD3C378D81AD1');
        $this->addSql('CREATE UNIQUE INDEX uniq_9b6a981fbf1cd3c3 ON public.mobile_app_release (version)');
    }
}
