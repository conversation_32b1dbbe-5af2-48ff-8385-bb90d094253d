<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250716072615 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE public.order_file_preview (id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by_id UUID DEFAULT NULL, tenant VARCHAR(5) NOT NULL, files JSON DEFAULT \'[]\' NOT NULL, tour_id UUID NOT NULL, order_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_D27E4CB315ED8D438D9F6D384E59C462 ON public.order_file_preview (tour_id, order_id, tenant)');
        $this->addSql('CREATE INDEX IDX_D27E4CB315ED8D434E59C462 ON public.order_file_preview (tour_id, tenant)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE public.order_file_preview');
    }
}
