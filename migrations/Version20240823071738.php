<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240823071738 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE file_upload_operation DROP CONSTRAINT fk_cfec562ab03a8386');
        $this->addSql('ALTER TABLE file_upload_operation DROP CONSTRAINT fk_cfec562a99049ece');
        $this->addSql('ALTER TABLE file_upload_operation DROP CONSTRAINT fk_cfec562a9033212a');
        $this->addSql('DROP TABLE file_upload_operation');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE file_upload_operation (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, message_class TEXT NOT NULL, file_path TEXT NOT NULL, parameters JSON NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_cfec562a9033212a ON file_upload_operation (tenant_id)');
        $this->addSql('CREATE INDEX idx_cfec562a99049ece ON file_upload_operation (modified_by_id)');
        $this->addSql('CREATE INDEX idx_cfec562ab03a8386 ON file_upload_operation (created_by_id)');
        $this->addSql('COMMENT ON COLUMN file_upload_operation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN file_upload_operation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE file_upload_operation ADD CONSTRAINT fk_cfec562ab03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE file_upload_operation ADD CONSTRAINT fk_cfec562a99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE file_upload_operation ADD CONSTRAINT fk_cfec562a9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
