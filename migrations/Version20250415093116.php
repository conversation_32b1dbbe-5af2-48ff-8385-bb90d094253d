<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250415093116 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" ADD search VARCHAR(1000)
        SQL);

        $this->addSql(<<<'SQL'
            UPDATE "user"
            SET search = COALESCE(username, '') || ' ' || COALESCE(lastname, '') || ' ' || COALESCE(email, '')
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE "user" ALTER search SET NOT NULL
        SQL);

        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_327C5DE7B4F0DBA7 ON "user" USING gin (search gin_trgm_ops)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_327C5DE7B4F0DBA7
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" DROP search
        SQL);
    }
}
