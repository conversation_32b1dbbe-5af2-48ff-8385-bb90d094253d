<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241121154443 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE sap_call_fail (tour_id VARCHAR(36) NOT NULL, microtime DOUBLE PRECISION NOT NULL, message_type TEXT NOT NULL, message JSONB NOT NULL, status VARCHAR(20) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_5A2FA122B03A8386 ON sap_call_fail (created_by_id)');
        $this->addSql('CREATE INDEX IDX_5A2FA12299049ECE ON sap_call_fail (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_5A2FA1229033212A ON sap_call_fail (tenant_id)');
        $this->addSql('CREATE INDEX IDX_5A2FA1222D8794BA ON sap_call_fail (microtime)');
        $this->addSql('CREATE INDEX IDX_5A2FA1227065677C ON sap_call_fail (modified_at)');
        $this->addSql('ALTER TABLE sap_call_fail ADD CONSTRAINT FK_5A2FA122B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_call_fail ADD CONSTRAINT FK_5A2FA12299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_call_fail ADD CONSTRAINT FK_5A2FA1229033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_call_fail RENAME COLUMN message_type TO content_object_class');
        $this->addSql('ALTER TABLE sap_call_fail RENAME COLUMN message TO content_object');
        $this->addSql('ALTER TABLE sap_call_fail ALTER content_object TYPE JSONB');
        $this->addSql('CREATE TABLE sap_retry_queue (tour_id VARCHAR(36) NOT NULL, microtime DOUBLE PRECISION NOT NULL, content_object_class TEXT NOT NULL, content_object JSONB NOT NULL, status VARCHAR(20) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_CA76E302B03A8386 ON sap_retry_queue (created_by_id)');
        $this->addSql('CREATE INDEX IDX_CA76E30299049ECE ON sap_retry_queue (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_CA76E3029033212A ON sap_retry_queue (tenant_id)');
        $this->addSql('CREATE INDEX IDX_CA76E3022D8794BA ON sap_retry_queue (microtime)');
        $this->addSql('CREATE INDEX IDX_CA76E3027065677C ON sap_retry_queue (modified_at)');
        $this->addSql('ALTER TABLE sap_retry_queue ADD CONSTRAINT FK_CA76E302B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_retry_queue ADD CONSTRAINT FK_CA76E30299049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_retry_queue ADD CONSTRAINT FK_CA76E3029033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_call_fail DROP CONSTRAINT fk_5a2fa1229033212a');
        $this->addSql('ALTER TABLE sap_call_fail DROP CONSTRAINT fk_5a2fa12299049ece');
        $this->addSql('ALTER TABLE sap_call_fail DROP CONSTRAINT fk_5a2fa122b03a8386');
        $this->addSql('DROP TABLE sap_call_fail');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sap_call_fail RENAME COLUMN content_object_class TO message_type');
        $this->addSql('ALTER TABLE sap_call_fail RENAME COLUMN content_object TO message');
        $this->addSql('ALTER TABLE sap_call_fail ALTER message TYPE JSONB');
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE sap_call_fail DROP CONSTRAINT FK_5A2FA122B03A8386');
        $this->addSql('ALTER TABLE sap_call_fail DROP CONSTRAINT FK_5A2FA12299049ECE');
        $this->addSql('ALTER TABLE sap_call_fail DROP CONSTRAINT FK_5A2FA1229033212A');
        $this->addSql('DROP TABLE sap_call_fail');
        $this->addSql('CREATE TABLE sap_call_fail (tour_id VARCHAR(36) NOT NULL, microtime DOUBLE PRECISION NOT NULL, content_object_class TEXT NOT NULL, content_object JSONB NOT NULL, status VARCHAR(20) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_5a2fa122b03a8386 ON sap_call_fail (created_by_id)');
        $this->addSql('CREATE INDEX idx_5a2fa12299049ece ON sap_call_fail (modified_by_id)');
        $this->addSql('CREATE INDEX idx_5a2fa1229033212a ON sap_call_fail (tenant_id)');
        $this->addSql('CREATE INDEX idx_5a2fa1227065677c ON sap_call_fail (modified_at)');
        $this->addSql('CREATE INDEX idx_5a2fa1222d8794ba ON sap_call_fail (microtime)');
        $this->addSql('ALTER TABLE sap_call_fail ADD CONSTRAINT fk_5a2fa1229033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_call_fail ADD CONSTRAINT fk_5a2fa12299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_call_fail ADD CONSTRAINT fk_5a2fa122b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE sap_retry_queue DROP CONSTRAINT FK_CA76E302B03A8386');
        $this->addSql('ALTER TABLE sap_retry_queue DROP CONSTRAINT FK_CA76E30299049ECE');
        $this->addSql('ALTER TABLE sap_retry_queue DROP CONSTRAINT FK_CA76E3029033212A');
        $this->addSql('DROP TABLE sap_retry_queue');
    }
}
