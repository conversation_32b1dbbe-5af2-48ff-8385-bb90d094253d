<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250219125907 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE equipment ALTER container_mounting TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE equipment ALTER container_mounting DROP NOT NULL');
        $this->addSql('ALTER TABLE equipment_version ALTER container_mounting TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE equipment_version ALTER container_mounting DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.equipment ALTER container_mounting TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE public.equipment ALTER container_mounting SET NOT NULL');
        $this->addSql('ALTER TABLE public.equipment_version ALTER container_mounting TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE public.equipment_version ALTER container_mounting SET NOT NULL');
    }
}
