<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250502114423 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE document DROP order_external_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document DROP tour_external_id
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD order_external_id VARCHAR(40) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD tour_external_id VARCHAR(40) DEFAULT NULL
        SQL);
    }
}
