<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250313123519 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE public.session_booking (session_id VARCHAR(40) NOT NULL, source VARCHAR(20) NOT NULL, source_id VARCHAR(40) NOT NULL, booking JSON NOT NULL, description VARCHAR(150) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, tenant_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A563860D9033212A ON public.session_booking (tenant_id)');
        $this->addSql('CREATE INDEX IDX_A563860DB03A8386 ON public.session_booking (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A563860D99049ECE ON public.session_booking (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A563860D7065677C ON public.session_booking (modified_at)');
        $this->addSql('CREATE INDEX IDX_A563860D613FECDF ON public.session_booking (session_id)');
        $this->addSql('ALTER TABLE public.session_booking ADD CONSTRAINT FK_A563860D9033212A FOREIGN KEY (tenant_id) REFERENCES public.tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.session_booking ADD CONSTRAINT FK_A563860DB03A8386 FOREIGN KEY (created_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.session_booking ADD CONSTRAINT FK_A563860D99049ECE FOREIGN KEY (modified_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde15ed8d43');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde42b4f971');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde613fecdf');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde8d9f6d38');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde8db60186');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde9033212a');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00cedde99049ece');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00ceddeb03a8386');
        $this->addSql('ALTER TABLE booking DROP CONSTRAINT fk_e00ceddebe94330b');
        $this->addSql('DROP TABLE booking');
        $this->addSql('DROP INDEX idx_6a90b97594a4c7d4fe372397ff238573');
        $this->addSql('TRUNCATE TABLE tracking');
        $this->addSql('ALTER TABLE tracking ADD search VARCHAR(250) NOT NULL');
        $this->addSql('ALTER TABLE tracking ADD session_id VARCHAR(40) NOT NULL');
        $this->addSql('CREATE INDEX IDX_6A90B975AA9E377AFF238573613FECDF ON tracking (date, tour_external_id, session_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE booking (id UUID NOT NULL, tour_id UUID DEFAULT NULL, order_id UUID DEFAULT NULL, task_group_id UUID DEFAULT NULL, task_id UUID DEFAULT NULL, interruption_id UUID DEFAULT NULL, session_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, latitude DOUBLE PRECISION DEFAULT NULL, longitude DOUBLE PRECISION DEFAULT NULL, "timestamp" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(20) NOT NULL, status VARCHAR(20) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, label VARCHAR(255) NOT NULL, mileage DOUBLE PRECISION DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_db28236215ed8d43 ON booking (tour_id)');
        $this->addSql('CREATE INDEX idx_db28236242b4f971 ON booking (interruption_id)');
        $this->addSql('CREATE INDEX idx_db282362613fecdf ON booking (session_id)');
        $this->addSql('CREATE INDEX idx_db2823627065677c ON booking (modified_at)');
        $this->addSql('CREATE INDEX idx_db2823628d9f6d38 ON booking (order_id)');
        $this->addSql('CREATE INDEX idx_db2823628db60186 ON booking (task_id)');
        $this->addSql('CREATE INDEX idx_db2823629033212a ON booking (tenant_id)');
        $this->addSql('CREATE INDEX idx_db28236299049ece ON booking (modified_by_id)');
        $this->addSql('CREATE INDEX idx_db282362b03a8386 ON booking (created_by_id)');
        $this->addSql('CREATE INDEX idx_db282362be94330b ON booking (task_group_id)');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde15ed8d43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde42b4f971 FOREIGN KEY (interruption_id) REFERENCES interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde613fecdf FOREIGN KEY (session_id) REFERENCES session (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde8d9f6d38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde8db60186 FOREIGN KEY (task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00cedde99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00ceddeb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE booking ADD CONSTRAINT fk_e00ceddebe94330b FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.session_booking DROP CONSTRAINT FK_A563860D9033212A');
        $this->addSql('ALTER TABLE public.session_booking DROP CONSTRAINT FK_A563860DB03A8386');
        $this->addSql('ALTER TABLE public.session_booking DROP CONSTRAINT FK_A563860D99049ECE');
        $this->addSql('DROP TABLE public.session_booking');
        $this->addSql('DROP INDEX public.IDX_6A90B975AA9E377AFF238573613FECDF');
        $this->addSql('ALTER TABLE public.tracking DROP search');
        $this->addSql('ALTER TABLE public.tracking DROP session_id');
        $this->addSql('CREATE INDEX idx_6a90b97594a4c7d4fe372397ff238573 ON public.tracking (device_id, equipment_external_id, tour_external_id)');
    }
}
