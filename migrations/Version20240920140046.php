<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240920140046 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE tracking (id UUID NOT NULL, tenant_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, device_id VARCHAR(250) NOT NULL, equipment_id UUID DEFAULT NULL, tour_id UUID DEFAULT NULL, tracking_data JSONB DEFAULT \'[]\' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A87C621C9033212A ON tracking (tenant_id)');
        $this->addSql('CREATE INDEX IDX_A87C621CB03A8386 ON tracking (created_by_id)');
        $this->addSql('CREATE INDEX IDX_A87C621C99049ECE ON tracking (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_A87C621C94A4C7D4517FE9FE15ED8D43 ON tracking (device_id, equipment_id, tour_id)');
        $this->addSql('CREATE INDEX IDX_A87C621C7065677C ON tracking (modified_at)');
        $this->addSql('COMMENT ON COLUMN tracking.tracking_data IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN tracking.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tracking.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE tracking ADD CONSTRAINT FK_A87C621C9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tracking ADD CONSTRAINT FK_A87C621CB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tracking ADD CONSTRAINT FK_A87C621C99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_E67AD3597065677C ON point_of_interest (modified_at)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tracking DROP CONSTRAINT FK_A87C621C9033212A');
        $this->addSql('ALTER TABLE tracking DROP CONSTRAINT FK_A87C621CB03A8386');
        $this->addSql('ALTER TABLE tracking DROP CONSTRAINT FK_A87C621C99049ECE');
        $this->addSql('DROP TABLE tracking');
        $this->addSql('DROP INDEX IDX_E67AD3597065677C');
    }
}
