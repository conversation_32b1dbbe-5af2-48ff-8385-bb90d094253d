<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240715074448 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE customer ADD name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD branch_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD height INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD length INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD width INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD weight INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD minimum_load INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD overload INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD load_capacity INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD total_permissible_weight INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD max_axle_load INT DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD license_plate VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD type VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD container_mounting VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE equipment ADD CONSTRAINT FK_D338D583DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_D338D583DCD6CC49 ON equipment (branch_id)');
        $this->addSql('ALTER TABLE location ADD type VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD phone VARCHAR(100) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD street VARCHAR(60) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD house_number VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD postal_code VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD city VARCHAR(40) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD district VARCHAR(40) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD country VARCHAR(3) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD state VARCHAR(60) DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD latitude DOUBLE PRECISION DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD longitude DOUBLE PRECISION DEFAULT NULL');
        $this->addSql('ALTER TABLE location ADD name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE staff ADD branch_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE staff ADD personnel_type VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE staff ADD firstname VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE staff ADD lastname VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE staff ADD CONSTRAINT FK_426EF392DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_426EF392DCD6CC49 ON staff (branch_id)');

        // Migrate all customer data from last version to main customer record
        $this->addSql('
            WITH LatestCustomerVersion AS (
                SELECT
                    cv.customer_id,
                    cv.name
                FROM
                    customer_version cv
                        INNER JOIN (
                        SELECT
                            customer_id,
                            MAX(created_at) AS latest
                        FROM
                            customer_version
                        GROUP BY
                            customer_id
                    ) AS SubQuery ON cv.customer_id = SubQuery.customer_id AND cv.created_at = SubQuery.latest
            )
            UPDATE
                customer c
            SET
                name = lcv.name
            FROM
                LatestCustomerVersion lcv
            WHERE
                c.id = lcv.customer_id;
        ');

        // Migrate all equipment data from last version to main customer record
        $this->addSql('
            WITH LatestEquipmentVersion AS (
                SELECT
                    ev.equipment_id,
                    ev.height,
                    ev.length,
                    ev.width,
                    ev.weight,
                    ev.minimum_load,
                    ev.overload,
                    ev.load_capacity,
                    ev.total_permissible_weight,
                    ev.max_axle_load,
                    ev.license_plate,
                    ev.type,
                    ev.container_mounting,
                    ev.branch_id
                FROM
                    equipment_version ev
                        INNER JOIN (
                        SELECT
                            equipment_id,
                            MAX(created_at) AS latest
                        FROM
                            equipment_version
                        GROUP BY
                            equipment_id
                    ) AS SubQuery ON ev.equipment_id = SubQuery.equipment_id AND ev.created_at = SubQuery.latest
            )
            UPDATE
                equipment e
            SET
                height = lev.height,
                length = lev.length,
                width = lev.width,
                weight = lev.weight,
                minimum_load = lev.minimum_load,
                overload = lev.overload,
                load_capacity = lev.load_capacity,
                total_permissible_weight = lev.total_permissible_weight,
                max_axle_load = lev.max_axle_load,
                license_plate = lev.license_plate,
                type = lev.type,
                container_mounting = lev.container_mounting,
                branch_id = lev.branch_id
            FROM
                LatestEquipmentVersion lev
            WHERE
                e.id = lev.equipment_id;
        ');
        // Migrate all location data from last version to main customer record
        $this->addSql('
            WITH LatestLocationVersion AS (
                SELECT
                    lv.location_id,
                    lv.type,
                    lv.phone,
                    lv.street,
                    lv.house_number,
                    lv.postal_code,
                    lv.city,
                    lv.district,
                    lv.country,
                    lv.state,
                    lv.latitude,
                    lv.longitude,
                    lv.name
                FROM
                    location_version lv
                        INNER JOIN (
                        SELECT
                            location_id,
                            MAX(created_at) AS latest
                        FROM
                            location_version
                        GROUP BY
                            location_id
                    ) AS SubQuery ON lv.location_id = SubQuery.location_id AND lv.created_at = SubQuery.latest
            )
            UPDATE
                location l
            SET
                type = llv.type,
                phone = llv.phone,
                street = llv.street,
                house_number = llv.house_number,
                postal_code = llv.postal_code,
                city = llv.city,
                district = llv.district,
                country = llv.country,
                state = llv.state,
                latitude = llv.latitude,
                longitude = llv.longitude,
                name = llv.name
            FROM
                LatestLocationVersion llv
            WHERE
                l.id = llv.location_id;
        ');

        // Migrate all staff data from last version to main customer record
        $this->addSql('
            WITH LatestStaffVersion AS (
                SELECT
                    sv.staff_id,
                    sv.branch_id,
                    sv.personnel_type,
                    sv.firstname,
                    sv.lastname
                FROM
                    staff_version sv
                        INNER JOIN (
                        SELECT
                            staff_id,
                            MAX(created_at) AS latest
                        FROM
                            staff_version
                        GROUP BY
                            staff_id
                    ) AS SubQuery ON sv.staff_id = SubQuery.staff_id AND sv.created_at = SubQuery.latest
            )
            UPDATE
                staff s
            SET
                branch_id = lsv.branch_id,
                personnel_type = lsv.personnel_type,
                firstname = lsv.firstname,
                lastname = lsv.lastname
            FROM
                LatestStaffVersion lsv
            WHERE
                s.id = lsv.staff_id;
        ');

        $this->addSql('ALTER TABLE customer ALTER name SET NOT NULL');
        $this->addSql('ALTER TABLE equipment ALTER branch_id SET NOT NULL');
        $this->addSql('ALTER TABLE equipment ALTER license_plate SET NOT NULL');
        $this->addSql('ALTER TABLE equipment ALTER type SET NOT NULL');
        $this->addSql('ALTER TABLE equipment ALTER container_mounting SET NOT NULL');
        $this->addSql('ALTER TABLE location ALTER type SET NOT NULL');
        $this->addSql('ALTER TABLE location ALTER street SET NOT NULL');
        $this->addSql('ALTER TABLE location ALTER postal_code SET NOT NULL');
        $this->addSql('ALTER TABLE location ALTER city SET NOT NULL');
        $this->addSql('ALTER TABLE location ALTER country SET NOT NULL');
        $this->addSql('ALTER TABLE staff ALTER branch_id SET NOT NULL');
        $this->addSql('ALTER TABLE staff ALTER personnel_type SET NOT NULL');
        $this->addSql('ALTER TABLE staff ALTER firstname SET NOT NULL');
        $this->addSql('ALTER TABLE staff ALTER lastname SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE location DROP type');
        $this->addSql('ALTER TABLE location DROP phone');
        $this->addSql('ALTER TABLE location DROP street');
        $this->addSql('ALTER TABLE location DROP house_number');
        $this->addSql('ALTER TABLE location DROP postal_code');
        $this->addSql('ALTER TABLE location DROP city');
        $this->addSql('ALTER TABLE location DROP district');
        $this->addSql('ALTER TABLE location DROP country');
        $this->addSql('ALTER TABLE location DROP state');
        $this->addSql('ALTER TABLE location DROP latitude');
        $this->addSql('ALTER TABLE location DROP longitude');
        $this->addSql('ALTER TABLE location DROP name');
        $this->addSql('ALTER TABLE staff DROP CONSTRAINT FK_426EF392DCD6CC49');
        $this->addSql('DROP INDEX IDX_426EF392DCD6CC49');
        $this->addSql('ALTER TABLE staff DROP branch_id');
        $this->addSql('ALTER TABLE staff DROP personnel_type');
        $this->addSql('ALTER TABLE staff DROP firstname');
        $this->addSql('ALTER TABLE staff DROP lastname');
        $this->addSql('ALTER TABLE equipment DROP CONSTRAINT FK_D338D583DCD6CC49');
        $this->addSql('DROP INDEX IDX_D338D583DCD6CC49');
        $this->addSql('ALTER TABLE equipment DROP branch_id');
        $this->addSql('ALTER TABLE equipment DROP height');
        $this->addSql('ALTER TABLE equipment DROP length');
        $this->addSql('ALTER TABLE equipment DROP width');
        $this->addSql('ALTER TABLE equipment DROP weight');
        $this->addSql('ALTER TABLE equipment DROP minimum_load');
        $this->addSql('ALTER TABLE equipment DROP overload');
        $this->addSql('ALTER TABLE equipment DROP load_capacity');
        $this->addSql('ALTER TABLE equipment DROP total_permissible_weight');
        $this->addSql('ALTER TABLE equipment DROP max_axle_load');
        $this->addSql('ALTER TABLE equipment DROP license_plate');
        $this->addSql('ALTER TABLE equipment DROP type');
        $this->addSql('ALTER TABLE equipment DROP container_mounting');
        $this->addSql('ALTER TABLE customer DROP name');
    }
}
