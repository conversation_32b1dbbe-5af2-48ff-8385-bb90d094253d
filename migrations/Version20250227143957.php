<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250227143957 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE equipment RENAME COLUMN last_track_upload TO last_ddd_file_upload');
        $this->addSql('ALTER TABLE equipment ALTER last_ddd_file_upload TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.equipment RENAME COLUMN last_ddd_file_upload TO last_track_upload');
        $this->addSql('ALTER TABLE public.equipment ALTER last_track_upload TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
    }
}
