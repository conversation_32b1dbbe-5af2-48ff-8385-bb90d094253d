<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240917125318 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE big_query_sync_status (id UUID NOT NULL, table_name VARCHAR(50) NOT NULL, sync_started_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, sync_ended_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, total_synced_rows INT DEFAULT NULL, data_modified_from TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, data_modified_to TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, execution_time INT DEFAULT NULL, success BOOLEAN DEFAULT NULL, is_syncing <PERSON><PERSON><PERSON><PERSON>N NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A5AF46D014F53ECDBF396750 ON big_query_sync_status (table_name, id)');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.sync_started_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.sync_ended_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.data_modified_from IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.data_modified_to IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE INDEX IDX_F52993987065677C ON "order" (modified_at)');
        $this->addSql('CREATE INDEX IDX_4BE2D6637065677C ON "session_user" (modified_at)');
        $this->addSql('CREATE INDEX IDX_8D93D6497065677C ON "user" (modified_at)');
        $this->addSql('CREATE INDEX IDX_5E2C35817065677C ON accessible_additional_information (modified_at)');
        $this->addSql('CREATE INDEX IDX_8FBBC0E77065677C ON accessible_element (modified_at)');
        $this->addSql('CREATE INDEX IDX_7EE32A9E7065677C ON accessible_interruption (modified_at)');
        $this->addSql('CREATE INDEX IDX_8747C5AC7065677C ON accessible_note (modified_at)');
        $this->addSql('CREATE INDEX IDX_FE8655157065677C ON accessible_note_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_1A84E49D7065677C ON accessible_task (modified_at)');
        $this->addSql('CREATE INDEX IDX_E8DECB117065677C ON accessible_task_group (modified_at)');
        $this->addSql('CREATE INDEX IDX_AE1D63C77065677C ON accessible_task_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_5E2D48B37065677C ON accessible_termination (modified_at)');
        $this->addSql('CREATE INDEX IDX_5F3616F27065677C ON accessible_termination_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_19D9524D7065677C ON additional_information (modified_at)');
        $this->addSql('CREATE INDEX IDX_AD3C95577065677C ON additional_service_config (modified_at)');
        $this->addSql('CREATE INDEX IDX_E00CEDDE7065677C ON booking (modified_at)');
        $this->addSql('CREATE INDEX IDX_BB861B1F7065677C ON branch (modified_at)');
        $this->addSql('CREATE INDEX IDX_F097D2037065677C ON connected_device (modified_at)');
        $this->addSql('CREATE INDEX IDX_81398E097065677C ON customer (modified_at)');
        $this->addSql('CREATE INDEX IDX_A3C1777D7065677C ON customer_version (modified_at)');
        $this->addSql('CREATE INDEX IDX_4F525BDF7065677C ON default_additional_information (modified_at)');
        $this->addSql('CREATE INDEX IDX_B3C50F7C7065677C ON default_element (modified_at)');
        $this->addSql('CREATE INDEX IDX_DB87FE6A7065677C ON default_interruption (modified_at)');
        $this->addSql('CREATE INDEX IDX_5AAD37FB7065677C ON default_interruption_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_7AFA45DB7065677C ON default_material (modified_at)');
        $this->addSql('CREATE INDEX IDX_D1D15A747065677C ON default_material_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_8A4CC1127065677C ON default_note (modified_at)');
        $this->addSql('CREATE INDEX IDX_44F307C47065677C ON default_note_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_178FE0237065677C ON default_task (modified_at)');
        $this->addSql('CREATE INDEX IDX_A64CE0D77065677C ON default_task_element_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_E2470B867065677C ON default_task_group (modified_at)');
        $this->addSql('CREATE INDEX IDX_146831167065677C ON default_task_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_304CD7947065677C ON default_termination (modified_at)');
        $this->addSql('CREATE INDEX IDX_503976BE7065677C ON default_termination_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_219631C87065677C ON default_unit_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_EB83250C7065677C ON delivery_service (modified_at)');
        $this->addSql('CREATE INDEX IDX_D5795A4F7065677C ON device_access (modified_at)');
        $this->addSql('CREATE INDEX IDX_3C0BE9E27065677C ON device_message (modified_at)');
        $this->addSql('CREATE INDEX IDX_A04A207C7065677C ON device_message_thread (modified_at)');
        $this->addSql('CREATE INDEX IDX_41405E397065677C ON element (modified_at)');
        $this->addSql('CREATE INDEX IDX_D338D5837065677C ON equipment (modified_at)');
        $this->addSql('CREATE INDEX IDX_7304503C7065677C ON equipment_config (modified_at)');
        $this->addSql('CREATE INDEX IDX_C9671C2C7065677C ON equipment_version (modified_at)');
        $this->addSql('CREATE INDEX IDX_E8FF75CC7065677C ON faq (modified_at)');
        $this->addSql('CREATE INDEX IDX_D22944587065677C ON feedback (modified_at)');
        $this->addSql('CREATE INDEX IDX_F9511BC07065677C ON interruption (modified_at)');
        $this->addSql('CREATE INDEX IDX_5E9E89CB7065677C ON location (modified_at)');
        $this->addSql('CREATE INDEX IDX_5F22897065677C ON location_format_config (modified_at)');
        $this->addSql('CREATE INDEX IDX_5159C0677065677C ON location_version (modified_at)');
        $this->addSql('CREATE INDEX IDX_B6BD307F7065677C ON message (modified_at)');
        $this->addSql('CREATE INDEX IDX_760B5D847065677C ON mobile_device (modified_at)');
        $this->addSql('CREATE INDEX IDX_CFBDFA147065677C ON note (modified_at)');
        $this->addSql('CREATE INDEX IDX_399168C77065677C ON order_document (modified_at)');
        $this->addSql('CREATE INDEX IDX_BB2ABE5B7065677C ON order_document_text_block (modified_at)');
        $this->addSql('CREATE INDEX IDX_ECEF3BC27065677C ON order_type_config (modified_at)');
        $this->addSql('CREATE INDEX IDX_D044D5D47065677C ON session (modified_at)');
        $this->addSql('CREATE INDEX IDX_90DA90897065677C ON session_equipment (modified_at)');
        $this->addSql('CREATE INDEX IDX_ACA0F9437065677C ON session_tour (modified_at)');
        $this->addSql('CREATE INDEX IDX_426EF3927065677C ON staff (modified_at)');
        $this->addSql('CREATE INDEX IDX_D1F79DC77065677C ON staff_config (modified_at)');
        $this->addSql('CREATE INDEX IDX_E3AAC4757065677C ON staff_version (modified_at)');
        $this->addSql('CREATE INDEX IDX_527EDB257065677C ON task (modified_at)');
        $this->addSql('CREATE INDEX IDX_AA645FE57065677C ON task_group (modified_at)');
        $this->addSql('CREATE INDEX IDX_2249AD057065677C ON task_relation (modified_at)');
        $this->addSql('CREATE INDEX IDX_E4BFC4227065677C ON termination (modified_at)');
        $this->addSql('CREATE INDEX IDX_6AD1F9697065677C ON tour (modified_at)');
        $this->addSql('CREATE INDEX IDX_D4224CA67065677C ON tour_data_config (modified_at)');
        $this->addSql('CREATE INDEX IDX_6C724BCC7065677C ON tour_equipment (modified_at)');
        $this->addSql('CREATE INDEX IDX_7921E3BA7065677C ON tour_staff (modified_at)');
        $this->addSql('CREATE INDEX IDX_B469456F7065677C ON translation (modified_at)');
        $this->addSql('CREATE INDEX IDX_C779A6927065677C ON user_setting (modified_at)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE big_query_sync_status');
        $this->addSql('DROP INDEX IDX_AA645FE57065677C');
        $this->addSql('DROP INDEX IDX_5E9E89CB7065677C');
        $this->addSql('DROP INDEX IDX_AD3C95577065677C');
        $this->addSql('DROP INDEX IDX_8D93D6497065677C');
        $this->addSql('DROP INDEX IDX_BB861B1F7065677C');
        $this->addSql('DROP INDEX IDX_E3AAC4757065677C');
        $this->addSql('DROP INDEX IDX_D22944587065677C');
        $this->addSql('DROP INDEX IDX_D1D15A747065677C');
        $this->addSql('DROP INDEX IDX_6C724BCC7065677C');
        $this->addSql('DROP INDEX IDX_ACA0F9437065677C');
        $this->addSql('DROP INDEX IDX_A64CE0D77065677C');
        $this->addSql('DROP INDEX IDX_F52993987065677C');
        $this->addSql('DROP INDEX IDX_527EDB257065677C');
        $this->addSql('DROP INDEX IDX_E2470B867065677C');
        $this->addSql('DROP INDEX IDX_DB87FE6A7065677C');
        $this->addSql('DROP INDEX IDX_7304503C7065677C');
        $this->addSql('DROP INDEX IDX_90DA90897065677C');
        $this->addSql('DROP INDEX IDX_8FBBC0E77065677C');
        $this->addSql('DROP INDEX IDX_A04A207C7065677C');
        $this->addSql('DROP INDEX IDX_E8DECB117065677C');
        $this->addSql('DROP INDEX IDX_F9511BC07065677C');
        $this->addSql('DROP INDEX IDX_D4224CA67065677C');
        $this->addSql('DROP INDEX IDX_D1F79DC77065677C');
        $this->addSql('DROP INDEX IDX_A3C1777D7065677C');
        $this->addSql('DROP INDEX IDX_D338D5837065677C');
        $this->addSql('DROP INDEX IDX_D044D5D47065677C');
        $this->addSql('DROP INDEX IDX_D5795A4F7065677C');
        $this->addSql('DROP INDEX IDX_ECEF3BC27065677C');
        $this->addSql('DROP INDEX IDX_7AFA45DB7065677C');
        $this->addSql('DROP INDEX IDX_5F22897065677C');
        $this->addSql('DROP INDEX IDX_2249AD057065677C');
        $this->addSql('DROP INDEX IDX_178FE0237065677C');
        $this->addSql('DROP INDEX IDX_8747C5AC7065677C');
        $this->addSql('DROP INDEX IDX_5E2D48B37065677C');
        $this->addSql('DROP INDEX IDX_8A4CC1127065677C');
        $this->addSql('DROP INDEX IDX_5159C0677065677C');
        $this->addSql('DROP INDEX IDX_1A84E49D7065677C');
        $this->addSql('DROP INDEX IDX_5E2C35817065677C');
        $this->addSql('DROP INDEX IDX_AE1D63C77065677C');
        $this->addSql('DROP INDEX IDX_C779A6927065677C');
        $this->addSql('DROP INDEX IDX_5F3616F27065677C');
        $this->addSql('DROP INDEX IDX_E00CEDDE7065677C');
        $this->addSql('DROP INDEX IDX_7921E3BA7065677C');
        $this->addSql('DROP INDEX IDX_B3C50F7C7065677C');
        $this->addSql('DROP INDEX IDX_219631C87065677C');
        $this->addSql('DROP INDEX IDX_7EE32A9E7065677C');
        $this->addSql('DROP INDEX IDX_44F307C47065677C');
        $this->addSql('DROP INDEX IDX_5AAD37FB7065677C');
        $this->addSql('DROP INDEX IDX_C9671C2C7065677C');
        $this->addSql('DROP INDEX IDX_BB2ABE5B7065677C');
        $this->addSql('DROP INDEX IDX_E8FF75CC7065677C');
        $this->addSql('DROP INDEX IDX_146831167065677C');
        $this->addSql('DROP INDEX IDX_19D9524D7065677C');
        $this->addSql('DROP INDEX IDX_F097D2037065677C');
        $this->addSql('DROP INDEX IDX_4BE2D6637065677C');
        $this->addSql('DROP INDEX IDX_B6BD307F7065677C');
        $this->addSql('DROP INDEX IDX_760B5D847065677C');
        $this->addSql('DROP INDEX IDX_6AD1F9697065677C');
        $this->addSql('DROP INDEX IDX_4F525BDF7065677C');
        $this->addSql('DROP INDEX IDX_304CD7947065677C');
        $this->addSql('DROP INDEX IDX_3C0BE9E27065677C');
        $this->addSql('DROP INDEX IDX_E4BFC4227065677C');
        $this->addSql('DROP INDEX IDX_426EF3927065677C');
        $this->addSql('DROP INDEX IDX_81398E097065677C');
        $this->addSql('DROP INDEX IDX_503976BE7065677C');
        $this->addSql('DROP INDEX IDX_FE8655157065677C');
        $this->addSql('DROP INDEX IDX_B469456F7065677C');
        $this->addSql('DROP INDEX IDX_41405E397065677C');
        $this->addSql('DROP INDEX IDX_EB83250C7065677C');
        $this->addSql('DROP INDEX IDX_CFBDFA147065677C');
        $this->addSql('DROP INDEX IDX_399168C77065677C');
    }
}
