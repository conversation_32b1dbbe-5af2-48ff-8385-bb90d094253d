<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240724074800 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "user" ALTER "roles" TYPE jsonb');
        $this->addSql('ALTER TABLE "user" ALTER "branch_access" TYPE jsonb');
        $this->addSql('ALTER TABLE "user" ALTER "country_access" TYPE jsonb');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "user" ALTER "roles" TYPE json');
        $this->addSql('ALTER TABLE "user" ALTER "branch_access" TYPE json');
        $this->addSql('ALTER TABLE "user" ALTER "country_access" TYPE json');
    }
}
