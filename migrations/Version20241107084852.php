<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241107084852 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT fk_5e2c358132c0fdc9');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT fk_5e2c35819033212a');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT fk_5e2c358199049ece');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT fk_5e2c3581b03a8386');
        $this->addSql('ALTER TABLE accessible_additional_information DROP CONSTRAINT fk_5e2c3581c7a42f5e');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdf3218a5db');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdf88b5af18');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdf9033212a');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdf99049ece');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdfb03a8386');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdfdae251ad');
        $this->addSql('ALTER TABLE default_additional_information DROP CONSTRAINT fk_4f525bdff86d9723');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524d15ed8d43');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524d42b4f971');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524d8d9f6d38');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524d9033212a');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524d99049ece');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524db03a8386');
        $this->addSql('ALTER TABLE additional_information DROP CONSTRAINT fk_19d9524dbe94330b');
        $this->addSql('DROP TABLE accessible_additional_information');
        $this->addSql('DROP TABLE default_additional_information');
        $this->addSql('DROP TABLE additional_information');
        $this->addSql('ALTER TABLE "order" ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE "order" ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE "order" SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE "order" ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_interruption ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE accessible_interruption ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE accessible_interruption SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE accessible_interruption ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_task_group ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE accessible_task_group ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE accessible_task_group SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE accessible_task_group ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE default_interruption ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE default_interruption ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE default_interruption SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE default_interruption ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE default_task_group ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE default_task_group ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE default_task_group SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE default_task_group ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE interruption ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE interruption ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE interruption SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE interruption ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE order_type_config ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE order_type_config ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE order_type_config SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE order_type_config ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE task_group ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE task_group ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE task_group SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE task_group ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE tour ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE tour ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE tour SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE tour ALTER additional_information_items SET NOT NULL');
        $this->addSql('ALTER TABLE tour_data_config ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE tour_data_config ALTER additional_information_items SET DEFAULT \'[]\'');
        $this->addSql('UPDATE tour_data_config SET additional_information_items = \'[]\' WHERE additional_information_items IS NULL');
        $this->addSql('ALTER TABLE tour_data_config ALTER additional_information_items SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE accessible_additional_information (id UUID NOT NULL, accessible_task_group_id UUID DEFAULT NULL, accessible_interruption_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence INT NOT NULL, text VARCHAR(255) NOT NULL, also_front_view BOOLEAN NOT NULL, icon VARCHAR(255) DEFAULT NULL, highlight BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_5e2c358132c0fdc9 ON accessible_additional_information (accessible_task_group_id)');
        $this->addSql('CREATE INDEX idx_5e2c35815286d72b ON accessible_additional_information (sequence)');
        $this->addSql('CREATE INDEX idx_5e2c35817065677c ON accessible_additional_information (modified_at)');
        $this->addSql('CREATE INDEX idx_5e2c35819033212a ON accessible_additional_information (tenant_id)');
        $this->addSql('CREATE INDEX idx_5e2c358199049ece ON accessible_additional_information (modified_by_id)');
        $this->addSql('CREATE INDEX idx_5e2c3581b03a8386 ON accessible_additional_information (created_by_id)');
        $this->addSql('CREATE INDEX idx_5e2c3581c7a42f5e ON accessible_additional_information (accessible_interruption_id)');
        $this->addSql('CREATE TABLE default_additional_information (id UUID NOT NULL, default_task_group_id UUID DEFAULT NULL, order_type_config_id UUID DEFAULT NULL, tour_data_config_id UUID DEFAULT NULL, default_interruption_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence INT NOT NULL, text VARCHAR(255) NOT NULL, also_front_view BOOLEAN NOT NULL, icon VARCHAR(255) DEFAULT NULL, highlight BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_4f525bdf3218a5db ON default_additional_information (tour_data_config_id)');
        $this->addSql('CREATE INDEX idx_4f525bdf5286d72b ON default_additional_information (sequence)');
        $this->addSql('CREATE INDEX idx_4f525bdf7065677c ON default_additional_information (modified_at)');
        $this->addSql('CREATE INDEX idx_4f525bdf88b5af18 ON default_additional_information (default_task_group_id)');
        $this->addSql('CREATE INDEX idx_4f525bdf9033212a ON default_additional_information (tenant_id)');
        $this->addSql('CREATE INDEX idx_4f525bdf99049ece ON default_additional_information (modified_by_id)');
        $this->addSql('CREATE INDEX idx_4f525bdfb03a8386 ON default_additional_information (created_by_id)');
        $this->addSql('CREATE INDEX idx_4f525bdfdae251ad ON default_additional_information (default_interruption_id)');
        $this->addSql('CREATE INDEX idx_4f525bdff86d9723 ON default_additional_information (order_type_config_id)');
        $this->addSql('CREATE TABLE additional_information (id UUID NOT NULL, tour_id UUID DEFAULT NULL, order_id UUID DEFAULT NULL, task_group_id UUID DEFAULT NULL, interruption_id UUID DEFAULT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence INT NOT NULL, text VARCHAR(255) NOT NULL, also_front_view BOOLEAN NOT NULL, icon VARCHAR(255) DEFAULT NULL, highlight BOOLEAN NOT NULL, is_from_external_source BOOLEAN DEFAULT false NOT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_19d9524d15ed8d43 ON additional_information (tour_id)');
        $this->addSql('CREATE INDEX idx_19d9524d42b4f971 ON additional_information (interruption_id)');
        $this->addSql('CREATE INDEX idx_19d9524d5286d72b ON additional_information (sequence)');
        $this->addSql('CREATE INDEX idx_19d9524d7065677c ON additional_information (modified_at)');
        $this->addSql('CREATE INDEX idx_19d9524d8d9f6d38 ON additional_information (order_id)');
        $this->addSql('CREATE INDEX idx_19d9524d9033212a ON additional_information (tenant_id)');
        $this->addSql('CREATE INDEX idx_19d9524d99049ece ON additional_information (modified_by_id)');
        $this->addSql('CREATE INDEX idx_19d9524db03a8386 ON additional_information (created_by_id)');
        $this->addSql('CREATE INDEX idx_19d9524dbe94330b ON additional_information (task_group_id)');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT fk_5e2c358132c0fdc9 FOREIGN KEY (accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT fk_5e2c35819033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT fk_5e2c358199049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT fk_5e2c3581b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_additional_information ADD CONSTRAINT fk_5e2c3581c7a42f5e FOREIGN KEY (accessible_interruption_id) REFERENCES accessible_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdf3218a5db FOREIGN KEY (tour_data_config_id) REFERENCES tour_data_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdf88b5af18 FOREIGN KEY (default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdf9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdf99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdfb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdfdae251ad FOREIGN KEY (default_interruption_id) REFERENCES default_interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_additional_information ADD CONSTRAINT fk_4f525bdff86d9723 FOREIGN KEY (order_type_config_id) REFERENCES order_type_config (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524d15ed8d43 FOREIGN KEY (tour_id) REFERENCES tour (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524d42b4f971 FOREIGN KEY (interruption_id) REFERENCES interruption (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524d8d9f6d38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524d9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524d99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524db03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE additional_information ADD CONSTRAINT fk_19d9524dbe94330b FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE "order" ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE "order" ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE "order" ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE order_type_config ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE order_type_config ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE order_type_config ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_interruption ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE accessible_interruption ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE accessible_interruption ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_task_group ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE accessible_task_group ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE accessible_task_group ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE task_group ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE task_group ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE task_group ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE interruption ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE interruption ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE interruption ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE default_interruption ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE default_interruption ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE default_interruption ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE tour ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE tour ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE tour ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE tour_data_config ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE tour_data_config ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE tour_data_config ALTER additional_information_items DROP NOT NULL');
        $this->addSql('ALTER TABLE default_task_group ALTER additional_information_items TYPE JSONB');
        $this->addSql('ALTER TABLE default_task_group ALTER additional_information_items DROP DEFAULT');
        $this->addSql('ALTER TABLE default_task_group ALTER additional_information_items DROP NOT NULL');
    }
}
