<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240731104902 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE EXTENSION IF NOT EXISTS pg_trgm SCHEMA public');
        $this->addSql('CREATE EXTENSION IF NOT EXISTS btree_gin SCHEMA public');

        $this->addSql('ALTER TABLE equipment ADD search VARCHAR(500)');
        $this->addSql('UPDATE equipment SET search = license_plate || \' \' || external_id || \' \' || type');
        $this->addSql('ALTER TABLE equipment ALTER search SET NOT NULL');
        $this->addSql('CREATE INDEX IDX_D338D583B4F0DBA7 ON equipment USING gin (search gin_trgm_ops)');

        $this->addSql('ALTER TABLE staff ADD search VARCHAR(1000)');
        $this->addSql('UPDATE staff SET search = firstname || \' \' || lastname || \' \' || external_id');
        $this->addSql('ALTER TABLE staff ALTER search SET NOT NULL');
        $this->addSql('CREATE INDEX IDX_426EF392B4F0DBA7 ON staff USING gin (search gin_trgm_ops)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_426EF392B4F0DBA7');
        $this->addSql('ALTER TABLE staff DROP search');
        $this->addSql('DROP INDEX IDX_D338D583B4F0DBA7');
        $this->addSql('ALTER TABLE equipment DROP search');
    }
}
