<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250226133011 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE public.dako_company (name VARCHAR(255) NOT NULL, dako_id VARCHAR(255) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7580236DB03A8386 ON public.dako_company (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7580236D99049ECE ON public.dako_company (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7580236D9033212A ON public.dako_company (tenant_id)');
        $this->addSql('CREATE INDEX IDX_7580236D7065677C ON public.dako_company (modified_at)');
        $this->addSql('ALTER TABLE public.dako_company ADD CONSTRAINT FK_7580236DB03A8386 FOREIGN KEY (created_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_company ADD CONSTRAINT FK_7580236D99049ECE FOREIGN KEY (modified_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE public.dako_company ADD CONSTRAINT FK_7580236D9033212A FOREIGN KEY (tenant_id) REFERENCES public.tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE branch ADD dako_company_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE branch ADD CONSTRAINT FK_F1686F3F22E880FE FOREIGN KEY (dako_company_id) REFERENCES public.dako_company (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F1686F3F22E880FE ON branch (dako_company_id)');
        $this->addSql('ALTER TABLE equipment ADD last_track_upload TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.dako_company DROP CONSTRAINT FK_7580236DB03A8386');
        $this->addSql('ALTER TABLE public.dako_company DROP CONSTRAINT FK_7580236D99049ECE');
        $this->addSql('ALTER TABLE public.dako_company DROP CONSTRAINT FK_7580236D9033212A');
        $this->addSql('DROP TABLE public.dako_company');
        $this->addSql('ALTER TABLE public.branch DROP CONSTRAINT FK_F1686F3F22E880FE');
        $this->addSql('DROP INDEX IDX_F1686F3F22E880FE');
        $this->addSql('ALTER TABLE public.branch DROP dako_company_id');
        $this->addSql('ALTER TABLE public.equipment DROP last_track_upload');
    }
}
