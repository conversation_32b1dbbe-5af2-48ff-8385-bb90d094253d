<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250106082142 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT fk_a64ce0d7573c7e47');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT fk_a64ce0d77f3350c3');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT fk_a64ce0d79033212a');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT fk_a64ce0d799049ece');
        $this->addSql('ALTER TABLE default_task_element_relation DROP CONSTRAINT fk_a64ce0d7b03a8386');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT fk_8fbbc0e7434d9f58');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT fk_8fbbc0e79033212a');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT fk_8fbbc0e799049ece');
        $this->addSql('ALTER TABLE accessible_element DROP CONSTRAINT fk_8fbbc0e7b03a8386');
        $this->addSql('ALTER TABLE default_element DROP CONSTRAINT fk_b3c50f7c9033212a');
        $this->addSql('ALTER TABLE default_element DROP CONSTRAINT fk_b3c50f7c99049ece');
        $this->addSql('ALTER TABLE default_element DROP CONSTRAINT fk_b3c50f7cb03a8386');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT fk_41405e398db60186');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT fk_41405e399033212a');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT fk_41405e3999049ece');
        $this->addSql('ALTER TABLE element DROP CONSTRAINT fk_41405e39b03a8386');
        $this->addSql('DROP TABLE default_task_element_relation');
        $this->addSql('DROP TABLE accessible_element');
        $this->addSql('DROP TABLE default_element');
        $this->addSql('DROP TABLE element');
        $this->addSql('UPDATE accessible_task SET element_items = \'[]\' WHERE element_items IS NULL');
        $this->addSql('ALTER TABLE accessible_task ALTER element_items TYPE JSONB');
        $this->addSql('ALTER TABLE accessible_task ALTER element_items SET DEFAULT \'[]\'');
        $this->addSql('ALTER TABLE accessible_task ALTER element_items SET NOT NULL');
        $this->addSql('UPDATE default_task SET element_items = \'[]\' WHERE element_items IS NULL');
        $this->addSql('ALTER TABLE default_task ALTER element_items TYPE JSONB');
        $this->addSql('ALTER TABLE default_task ALTER element_items SET DEFAULT \'[]\'');
        $this->addSql('ALTER TABLE default_task ALTER element_items SET NOT NULL');
        $this->addSql('UPDATE task SET element_items = \'[]\' WHERE element_items IS NULL');
        $this->addSql('ALTER TABLE task ALTER element_items TYPE JSONB');
        $this->addSql('ALTER TABLE task ALTER element_items SET DEFAULT \'[]\'');
        $this->addSql('ALTER TABLE task ALTER element_items SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE default_task_element_relation (id UUID NOT NULL, default_task_id UUID NOT NULL, default_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, required BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, external_source_system VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_a64ce0d7f2803b3d ON default_task_element_relation (sequence_number)');
        $this->addSql('CREATE INDEX idx_a64ce0d7b03a8386 ON default_task_element_relation (created_by_id)');
        $this->addSql('CREATE INDEX idx_a64ce0d799049ece ON default_task_element_relation (modified_by_id)');
        $this->addSql('CREATE INDEX idx_a64ce0d79033212a ON default_task_element_relation (tenant_id)');
        $this->addSql('CREATE INDEX idx_a64ce0d77f3350c3 ON default_task_element_relation (default_task_id)');
        $this->addSql('CREATE INDEX idx_a64ce0d77065677c ON default_task_element_relation (modified_at)');
        $this->addSql('CREATE INDEX idx_a64ce0d7573c7e47 ON default_task_element_relation (default_element_id)');
        $this->addSql('CREATE TABLE accessible_element (id UUID NOT NULL, accessible_task_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, required BOOLEAN NOT NULL, reference_type VARCHAR(150) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(20) NOT NULL, expected_value VARCHAR(255) DEFAULT NULL, value_string_minlength INT DEFAULT NULL, value_string_max_length INT DEFAULT NULL, placeholder VARCHAR(100) DEFAULT NULL, label VARCHAR(100) DEFAULT NULL, pattern VARCHAR(100) DEFAULT NULL, pattern_error VARCHAR(100) DEFAULT NULL, description TEXT DEFAULT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, value_number_precision INT DEFAULT NULL, value_number_unit VARCHAR(20) DEFAULT NULL, value_min_items INT DEFAULT NULL, value_max_items INT DEFAULT NULL, value_signature_type VARCHAR(20) DEFAULT NULL, value_photo_type VARCHAR(20) DEFAULT NULL, value_photo_max_size INT DEFAULT NULL, value_date_min_date DATE DEFAULT NULL, value_date_max_date DATE DEFAULT NULL, value_time_min_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_time_max_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_date_format VARCHAR(50) DEFAULT NULL, value_time_format VARCHAR(50) DEFAULT NULL, pattern_hint VARCHAR(100) DEFAULT NULL, has_autocomplete BOOLEAN DEFAULT false NOT NULL, show_additional_input BOOLEAN DEFAULT true NOT NULL, checksum VARCHAR(255) DEFAULT NULL, checksum_formula VARCHAR(255) DEFAULT NULL, options JSON DEFAULT \'[]\' NOT NULL, "values" JSON DEFAULT \'[]\' NOT NULL, external_source_system VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_8fbbc0e7f2803b3d ON accessible_element (sequence_number)');
        $this->addSql('CREATE INDEX idx_8fbbc0e7b03a8386 ON accessible_element (created_by_id)');
        $this->addSql('CREATE INDEX idx_8fbbc0e799049ece ON accessible_element (modified_by_id)');
        $this->addSql('CREATE INDEX idx_8fbbc0e79033212a ON accessible_element (tenant_id)');
        $this->addSql('CREATE INDEX idx_8fbbc0e77065677c ON accessible_element (modified_at)');
        $this->addSql('CREATE INDEX idx_8fbbc0e7434d9f58 ON accessible_element (accessible_task_id)');
        $this->addSql('CREATE TABLE default_element (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, data_source VARCHAR(20) DEFAULT NULL, reference_type VARCHAR(150) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(20) NOT NULL, expected_value VARCHAR(255) DEFAULT NULL, value_string_minlength INT DEFAULT NULL, value_string_max_length INT DEFAULT NULL, placeholder VARCHAR(100) DEFAULT NULL, label VARCHAR(100) DEFAULT NULL, pattern VARCHAR(100) DEFAULT NULL, pattern_error VARCHAR(100) DEFAULT NULL, description TEXT DEFAULT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, value_number_precision INT DEFAULT NULL, value_number_unit VARCHAR(20) DEFAULT NULL, value_min_items INT DEFAULT NULL, value_max_items INT DEFAULT NULL, value_signature_type VARCHAR(20) DEFAULT NULL, value_photo_type VARCHAR(20) DEFAULT NULL, value_photo_max_size INT DEFAULT NULL, value_date_min_date DATE DEFAULT NULL, value_date_max_date DATE DEFAULT NULL, value_time_min_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_time_max_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_date_format VARCHAR(50) DEFAULT NULL, value_time_format VARCHAR(50) DEFAULT NULL, pattern_hint VARCHAR(100) DEFAULT NULL, has_autocomplete BOOLEAN DEFAULT false NOT NULL, show_additional_input BOOLEAN DEFAULT true NOT NULL, checksum VARCHAR(255) DEFAULT NULL, checksum_formula VARCHAR(255) DEFAULT NULL, options JSON DEFAULT \'[]\' NOT NULL, "values" JSON DEFAULT \'[]\' NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_b3c50f7cb03a8386 ON default_element (created_by_id)');
        $this->addSql('CREATE INDEX idx_b3c50f7c99049ece ON default_element (modified_by_id)');
        $this->addSql('CREATE INDEX idx_b3c50f7c9033212a ON default_element (tenant_id)');
        $this->addSql('CREATE INDEX idx_b3c50f7c7065677c ON default_element (modified_at)');
        $this->addSql('CREATE TABLE element (id UUID NOT NULL, task_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, sequence_number INT NOT NULL, required BOOLEAN NOT NULL, reference_type VARCHAR(150) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(20) NOT NULL, expected_value VARCHAR(255) DEFAULT NULL, value_string_minlength INT DEFAULT NULL, value_string_max_length INT DEFAULT NULL, placeholder VARCHAR(100) DEFAULT NULL, label VARCHAR(100) DEFAULT NULL, pattern VARCHAR(100) DEFAULT NULL, pattern_error VARCHAR(100) DEFAULT NULL, description TEXT DEFAULT NULL, description_file VARCHAR(100) DEFAULT NULL, description_file_type VARCHAR(100) DEFAULT NULL, description_file_name VARCHAR(100) DEFAULT NULL, value_number_precision INT DEFAULT NULL, value_number_unit VARCHAR(20) DEFAULT NULL, value_min_items INT DEFAULT NULL, value_max_items INT DEFAULT NULL, value_signature_type VARCHAR(20) DEFAULT NULL, value_photo_type VARCHAR(20) DEFAULT NULL, value_photo_max_size INT DEFAULT NULL, value_date_min_date DATE DEFAULT NULL, value_date_max_date DATE DEFAULT NULL, value_time_min_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_time_max_time TIME(0) WITHOUT TIME ZONE DEFAULT NULL, value_date_format VARCHAR(50) DEFAULT NULL, value_time_format VARCHAR(50) DEFAULT NULL, pattern_hint VARCHAR(100) DEFAULT NULL, has_autocomplete BOOLEAN DEFAULT false NOT NULL, show_additional_input BOOLEAN DEFAULT true NOT NULL, checksum VARCHAR(255) DEFAULT NULL, checksum_formula VARCHAR(255) DEFAULT NULL, options JSON DEFAULT \'[]\' NOT NULL, "values" JSON DEFAULT \'[]\' NOT NULL, external_source_system VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_41405e39f2803b3d ON element (sequence_number)');
        $this->addSql('CREATE INDEX idx_41405e39b03a8386 ON element (created_by_id)');
        $this->addSql('CREATE INDEX idx_41405e3999049ece ON element (modified_by_id)');
        $this->addSql('CREATE INDEX idx_41405e399033212a ON element (tenant_id)');
        $this->addSql('CREATE INDEX idx_41405e398db60186 ON element (task_id)');
        $this->addSql('CREATE INDEX idx_41405e397065677c ON element (modified_at)');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT fk_a64ce0d7573c7e47 FOREIGN KEY (default_element_id) REFERENCES default_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT fk_a64ce0d77f3350c3 FOREIGN KEY (default_task_id) REFERENCES default_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT fk_a64ce0d79033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT fk_a64ce0d799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_element_relation ADD CONSTRAINT fk_a64ce0d7b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT fk_8fbbc0e7434d9f58 FOREIGN KEY (accessible_task_id) REFERENCES accessible_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT fk_8fbbc0e79033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT fk_8fbbc0e799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element ADD CONSTRAINT fk_8fbbc0e7b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element ADD CONSTRAINT fk_b3c50f7c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element ADD CONSTRAINT fk_b3c50f7c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element ADD CONSTRAINT fk_b3c50f7cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT fk_41405e398db60186 FOREIGN KEY (task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT fk_41405e399033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT fk_41405e3999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element ADD CONSTRAINT fk_41405e39b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task ALTER element_items TYPE JSONB');
        $this->addSql('ALTER TABLE accessible_task ALTER element_items DROP DEFAULT');
        $this->addSql('ALTER TABLE accessible_task ALTER element_items DROP NOT NULL');
        $this->addSql('ALTER TABLE task ALTER element_items TYPE JSONB');
        $this->addSql('ALTER TABLE task ALTER element_items DROP DEFAULT');
        $this->addSql('ALTER TABLE task ALTER element_items DROP NOT NULL');
        $this->addSql('ALTER TABLE default_task ALTER element_items TYPE JSONB');
        $this->addSql('ALTER TABLE default_task ALTER element_items DROP DEFAULT');
        $this->addSql('ALTER TABLE default_task ALTER element_items DROP NOT NULL');
    }
}
