<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250512095815 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add tenant_identifier column to multiple tables, populate it from tenant.shortcut, and set it to NOT NULL.';
    }

    public function up(Schema $schema): void
    {
        $tables = [
            'accessible_interruption',
            'accessible_note',
            'accessible_note_relation',
            'accessible_task',
            'accessible_task_group',
            'accessible_task_relation',
            'accessible_termination',
            'accessible_termination_relation',
            'additional_service_config',
            'branch',
            'connected_device',
            'customer',
            'customer_version',
            'dako_account',
            'dako_process',
            'default_interruption',
            'default_interruption_relation',
            'default_material',
            'default_material_relation',
            'default_note',
            'default_note_relation',
            'default_task',
            'default_task_group',
            'default_task_relation',
            'default_termination',
            'default_termination_relation',
            'default_unit_relation',
            'device_access',
            'device_message',
            'device_message_thread',
            'document',
            'equipment',
            'equipment_config',
            'equipment_position',
            'equipment_version',
            'faq',
            'feedback',
            'interruption',
            'location',
            'location_format_config',
            'location_version',
            'mastertour_progress',
            'mastertour_template',
            'note',
            '"order"',
            'order_type_config',
            'point_of_interest',
            'sap_retry_queue',
            'session',
            'session_booking',
            'session_equipment',
            'session_tour',
            '"session_user"',
            'staff',
            'staff_config',
            'staff_version',
            'task',
            'task_group',
            'task_relation',
            'termination',
            'tour',
            'tour_data_config',
            'tour_equipment',
            'tour_staff',
            'tracking',
            '"user"',
            'user_setting',
        ];

        foreach ($tables as $tableName) {
            // Add column as nullable
            $this->addSql(<<<SQL
                ALTER TABLE {$tableName} ADD tenant_identifier VARCHAR(5) NULL
            SQL);

            // Populate column
            // Assuming the foreign key column to tenant table is named 'tenant_id'
            // And the tenant table has 'id' as primary key and 'shortcut' for the identifier
            $this->addSql(<<<SQL
                UPDATE {$tableName}
                SET tenant_identifier = t.shortcut
                FROM tenant t
                WHERE {$tableName}.tenant_id = t.id
            SQL);

            // Make column not nullable
            $this->addSql(<<<SQL
                ALTER TABLE {$tableName} ALTER COLUMN tenant_identifier SET NOT NULL
            SQL);
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_position DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest DROP tenant_identifier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" DROP tenant_identifier
        SQL);
    }
}
