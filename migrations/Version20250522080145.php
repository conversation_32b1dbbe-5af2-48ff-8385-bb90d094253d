<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250522080145 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE public.phonebook (name VARCHAR(255) NOT NULL, description TEXT DEFAULT NULL, branches JSONB DEFAULT '[]' NOT NULL, country VARCHAR(255) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by_id UUID DEFAULT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D57B8F647065677C ON public.phonebook (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D57B8F644E59C462 ON public.phonebook (tenant)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE public.phonebook
        SQL);
    }
}
