<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use <PERSON>trine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250328090143 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            TRUNCATE TABLE device_message, device_message_thread
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread ADD search VARCHAR(255) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread DROP subject
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread DROP started_from
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread RENAME COLUMN first_message_excerpt TO last_message_excerpt
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_55678ED4B4F0DBA7 ON device_message_thread USING gin (search gin_trgm_ops)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_55678ED4B4F0DBA7
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ADD subject VARCHAR(400) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ADD started_from VARCHAR(20) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread DROP search
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread RENAME COLUMN last_message_excerpt TO first_message_excerpt
        SQL);
    }
}
