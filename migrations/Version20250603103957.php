<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250603103957 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE public.vehicle_inspection_config (country VARCHAR(255) NOT NULL, equipment_type VARCHAR(255) NOT NULL, equipment_component_groups JSONB NOT NULL, automatic_completion_enabled BOOLEAN DEFAULT false NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by_id UUID DEFAULT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_403AF6A57065677C ON public.vehicle_inspection_config (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_403AF6A54E59C462 ON public.vehicle_inspection_config (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE public.vehicle_inspection_report (equipment_id UUID NOT NULL, equipment_component_group_reports JSONB NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by_id UUID DEFAULT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_509FAE5D7065677C ON public.vehicle_inspection_report (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_509FAE5D4E59C462 ON public.vehicle_inspection_report (tenant)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE public.vehicle_inspection_config
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE public.vehicle_inspection_report
        SQL);
    }
}
