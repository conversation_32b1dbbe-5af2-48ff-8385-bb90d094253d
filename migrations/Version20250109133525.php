<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250109133525 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "order" ADD booking_items JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE interruption ADD booking_items JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE task ADD booking_items JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE task_group ADD booking_items JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE tour ADD booking_items JSONB DEFAULT \'[]\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE interruption DROP booking_items');
        $this->addSql('ALTER TABLE "order" DROP booking_items');
        $this->addSql('ALTER TABLE task_group DROP booking_items');
        $this->addSql('ALTER TABLE task DROP booking_items');
        $this->addSql('ALTER TABLE tour DROP booking_items');
    }
}
