<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250422082420 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511fff238573
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511f84c7a836
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document ADD order_id VARCHAR(36) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document ADD tour_id VARCHAR(36) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F8D9F6D38 ON document (order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F15ED8D43 ON document (tour_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1A85511F8D9F6D38
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_1A85511F15ED8D43
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document DROP order_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document DROP tour_id
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511fff238573 ON public.document (tour_external_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511f84c7a836 ON public.document (order_external_id)
        SQL);
    }
}
