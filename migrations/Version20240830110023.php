<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240830110023 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feedback ALTER log_file TYPE VARCHAR(51)');
        $this->addSql('ALTER TABLE feedback ALTER screenshot_file TYPE VARCHAR(51)');

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feedback ALTER log_file TYPE VARCHAR(36)');
        $this->addSql('ALTER TABLE feedback ALTER screenshot_file TYPE VARCHAR(36)');
    }
}
