<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250725085858 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE feedback SET screenshot_file = \'\' where screenshot_file IS NULL');
        $this->addSql('ALTER TABLE feedback ALTER screenshot_file TYPE VARCHAR(100)');
        $this->addSql('ALTER TABLE feedback ALTER screenshot_file SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.feedback ALTER screenshot_file TYPE VARCHAR(100)');
        $this->addSql('ALTER TABLE public.feedback ALTER screenshot_file DROP NOT NULL');
    }
}
