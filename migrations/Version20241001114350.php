<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241001114350 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE mastertour_progress (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, mastertour_template_id VARCHAR(36) NOT NULL, waypoint_id VARCHAR(36) NOT NULL, date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, equipment_actions JSONB DEFAULT \'[]\' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7F7DB6C3B03A8386 ON mastertour_progress (created_by_id)');
        $this->addSql('CREATE INDEX IDX_7F7DB6C399049ECE ON mastertour_progress (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_7F7DB6C39033212A ON mastertour_progress (tenant_id)');
        $this->addSql('CREATE INDEX IDX_7F7DB6C37065677C ON mastertour_progress (modified_at)');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mastertour_progress ADD CONSTRAINT FK_7F7DB6C3B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_progress ADD CONSTRAINT FK_7F7DB6C399049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_progress ADD CONSTRAINT FK_7F7DB6C39033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE device_message_thread ALTER first_message_excerpt TYPE VARCHAR(51)');
        $this->addSql('CREATE TABLE mastertour_template (id UUID NOT NULL, branch_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, external_id VARCHAR(255) NOT NULL, waypoints JSONB DEFAULT \'[]\' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_CA1C5B06DCD6CC49 ON mastertour_template (branch_id)');
        $this->addSql('CREATE INDEX IDX_CA1C5B06B03A8386 ON mastertour_template (created_by_id)');
        $this->addSql('CREATE INDEX IDX_CA1C5B0699049ECE ON mastertour_template (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_CA1C5B069033212A ON mastertour_template (tenant_id)');
        $this->addSql('CREATE INDEX IDX_CA1C5B067065677C ON mastertour_template (modified_at)');
        $this->addSql('COMMENT ON COLUMN mastertour_template.waypoints IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_template.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_template.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mastertour_template ADD CONSTRAINT FK_CA1C5B06DCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_template ADD CONSTRAINT FK_CA1C5B06B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_template ADD CONSTRAINT FK_CA1C5B0699049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_template ADD CONSTRAINT FK_CA1C5B069033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT fk_800a933a9033212a');
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT fk_800a933a99049ece');
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT fk_800a933ab03a8386');
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT fk_800a933adcd6cc49');
        $this->addSql('DROP TABLE master_tour_template');
        $this->addSql('ALTER TABLE mastertour_progress ADD coordinate JSONB NOT NULL');
        $this->addSql('ALTER TABLE mastertour_progress ALTER date TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.coordinate IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.date IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mastertour_progress ADD mastertour_template_external_id VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE mastertour_progress DROP mastertour_template_id');
        $this->addSql('ALTER TABLE tour RENAME COLUMN master_tours TO mastertours');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mastertour_progress DROP CONSTRAINT FK_7F7DB6C3B03A8386');
        $this->addSql('ALTER TABLE mastertour_progress DROP CONSTRAINT FK_7F7DB6C399049ECE');
        $this->addSql('ALTER TABLE mastertour_progress DROP CONSTRAINT FK_7F7DB6C39033212A');
        $this->addSql('DROP TABLE mastertour_progress');
        $this->addSql('ALTER TABLE device_message_thread ALTER first_message_excerpt TYPE VARCHAR(255)');
        $this->addSql('CREATE TABLE master_tour_template (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, branch_id UUID NOT NULL, name VARCHAR(255) NOT NULL, external_id VARCHAR(255) NOT NULL, waypoints JSON DEFAULT \'[]\' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_800a933adcd6cc49 ON master_tour_template (branch_id)');
        $this->addSql('CREATE INDEX idx_800a933ab03a8386 ON master_tour_template (created_by_id)');
        $this->addSql('CREATE INDEX idx_800a933a99049ece ON master_tour_template (modified_by_id)');
        $this->addSql('CREATE INDEX idx_800a933a9033212a ON master_tour_template (tenant_id)');
        $this->addSql('CREATE INDEX idx_800a933a7065677c ON master_tour_template (modified_at)');
        $this->addSql('COMMENT ON COLUMN master_tour_template.waypoints IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN master_tour_template.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN master_tour_template.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT fk_800a933a9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT fk_800a933a99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT fk_800a933ab03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT fk_800a933adcd6cc49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mastertour_template DROP CONSTRAINT FK_CA1C5B06DCD6CC49');
        $this->addSql('ALTER TABLE mastertour_template DROP CONSTRAINT FK_CA1C5B06B03A8386');
        $this->addSql('ALTER TABLE mastertour_template DROP CONSTRAINT FK_CA1C5B0699049ECE');
        $this->addSql('ALTER TABLE mastertour_template DROP CONSTRAINT FK_CA1C5B069033212A');
        $this->addSql('DROP TABLE mastertour_template');
        $this->addSql('ALTER TABLE mastertour_progress DROP coordinate');
        $this->addSql('ALTER TABLE mastertour_progress ALTER date TYPE TIMESTAMP(0) WITHOUT TIME ZONE');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.date IS NULL');
        $this->addSql('ALTER TABLE mastertour_progress ADD mastertour_template_id VARCHAR(36) NOT NULL');
        $this->addSql('ALTER TABLE mastertour_progress DROP mastertour_template_external_id');
        $this->addSql('ALTER TABLE tour RENAME COLUMN mastertours TO master_tours');
    }
}
