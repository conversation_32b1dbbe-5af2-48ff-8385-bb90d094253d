<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250310184613 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE dako_process ADD staff_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE dako_process ALTER equipment_id DROP NOT NULL');
        $this->addSql('ALTER TABLE dako_process ADD CONSTRAINT FK_BC2232B4D4D57CD FOREIGN KEY (staff_id) REFERENCES public.staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_BC2232B4D4D57CD ON dako_process (staff_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.dako_process DROP CONSTRAINT FK_BC2232B4D4D57CD');
        $this->addSql('DROP INDEX IDX_BC2232B4D4D57CD');
        $this->addSql('ALTER TABLE public.dako_process DROP staff_id');
        $this->addSql('ALTER TABLE public.dako_process ALTER equipment_id SET NOT NULL');
    }
}
