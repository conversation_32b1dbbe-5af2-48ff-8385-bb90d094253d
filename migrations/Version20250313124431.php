<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250313124431 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE dako_process DROP CONSTRAINT fk_bc2232b4d4d57cd');
        $this->addSql('DROP INDEX idx_bc2232b4d4d57cd');
        $this->addSql('ALTER TABLE dako_process DROP staff_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.dako_process ADD staff_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE public.dako_process ADD CONSTRAINT fk_bc2232b4d4d57cd FOREIGN KEY (staff_id) REFERENCES staff (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_bc2232b4d4d57cd ON public.dako_process (staff_id)');
    }
}
