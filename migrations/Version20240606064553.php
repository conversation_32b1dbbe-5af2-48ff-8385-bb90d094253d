<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240606064553 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accessible_element ADD options JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE accessible_element ADD values JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_element.options IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.values IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE default_element ADD options JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE default_element ADD values JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN default_element.options IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN default_element.values IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE element ADD options JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE element ADD values JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN element.options IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN element.values IS \'(DC2Type:json_document)\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE element DROP options');
        $this->addSql('ALTER TABLE element DROP values');
        $this->addSql('ALTER TABLE default_element DROP options');
        $this->addSql('ALTER TABLE default_element DROP values');
        $this->addSql('ALTER TABLE accessible_element DROP options');
        $this->addSql('ALTER TABLE accessible_element DROP values');
    }
}
