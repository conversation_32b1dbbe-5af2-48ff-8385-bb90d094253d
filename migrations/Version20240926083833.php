<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240926083833 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('TRUNCATE TABLE master_tour_template');
        $this->addSql('ALTER TABLE master_tour_template ADD branch_id UUID NOT NULL');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT FK_800A933ADCD6CC49 FOREIGN KEY (branch_id) REFERENCES branch (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_800A933ADCD6CC49 ON master_tour_template (branch_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT FK_800A933ADCD6CC49');
        $this->addSql('DROP INDEX IDX_800A933ADCD6CC49');
        $this->addSql('ALTER TABLE master_tour_template DROP branch_id');
    }
}
