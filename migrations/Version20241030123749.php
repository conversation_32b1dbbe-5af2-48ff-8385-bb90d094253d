<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241030123749 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "order" ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE accessible_interruption ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE accessible_task_group ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE default_interruption ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE default_task_group ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE interruption ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE order_type_config ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE task_group ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE tour ADD additional_information_items JSONB DEFAULT NULL');
        $this->addSql('ALTER TABLE tour_data_config ADD additional_information_items JSONB DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE "order" DROP additional_information_items');
        $this->addSql('ALTER TABLE accessible_interruption DROP additional_information_items');
        $this->addSql('ALTER TABLE default_task_group DROP additional_information_items');
        $this->addSql('ALTER TABLE tour DROP additional_information_items');
        $this->addSql('ALTER TABLE interruption DROP additional_information_items');
        $this->addSql('ALTER TABLE order_type_config DROP additional_information_items');
        $this->addSql('ALTER TABLE accessible_task_group DROP additional_information_items');
        $this->addSql('ALTER TABLE tour_data_config DROP additional_information_items');
        $this->addSql('ALTER TABLE default_interruption DROP additional_information_items');
        $this->addSql('ALTER TABLE task_group DROP additional_information_items');
    }
}
