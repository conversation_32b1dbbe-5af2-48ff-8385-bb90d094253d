<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240705141449 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT fk_f6816a3cf5163172');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT fk_f6816a3c32c0fdc9');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT fk_f6816a3c99049ece');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT fk_f6816a3cb03a8386');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT fk_f6816a3c9033212a');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT fk_d39109241f1f2a24');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT fk_d391092499049ece');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT fk_d3910924b03a8386');
        $this->addSql('ALTER TABLE element_option DROP CONSTRAINT fk_d39109249033212a');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT fk_a4091010fbdef43e');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT fk_a409101079cd4910');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT fk_a409101099049ece');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT fk_a4091010b03a8386');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT fk_a40910109033212a');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT fk_d9552cad99049ece');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT fk_d9552cadb03a8386');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT fk_d9552cad1f1f2a24');
        $this->addSql('ALTER TABLE element_value DROP CONSTRAINT fk_d9552cad9033212a');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT fk_ebc714cf99049ece');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT fk_ebc714cfb03a8386');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT fk_ebc714cf79cd4910');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT fk_ebc714cf88b5af18');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT fk_ebc714cf9033212a');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT fk_ef74b0be573c7e47');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT fk_ef74b0be99049ece');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT fk_ef74b0beb03a8386');
        $this->addSql('ALTER TABLE default_element_value DROP CONSTRAINT fk_ef74b0be9033212a');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT fk_57196966573c7e47');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT fk_5719696699049ece');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT fk_57196966b03a8386');
        $this->addSql('ALTER TABLE default_element_option DROP CONSTRAINT fk_571969669033212a');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT fk_7391bc13be94330b');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT fk_7391bc13afb2749b');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT fk_7391bc1399049ece');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT fk_7391bc13b03a8386');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT fk_7391bc139033212a');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT fk_356b38b7afb2749b');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT fk_356b38b774233c69');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT fk_356b38b799049ece');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT fk_356b38b7b03a8386');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT fk_356b38b79033212a');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT fk_3d67a09ef5163172');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT fk_3d67a09edf7711e6');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT fk_3d67a09e99049ece');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT fk_3d67a09eb03a8386');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT fk_3d67a09e9033212a');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT fk_5501e26f5da5bed0');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT fk_5501e26f99049ece');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT fk_5501e26fb03a8386');
        $this->addSql('ALTER TABLE accessible_element_value DROP CONSTRAINT fk_5501e26f9033212a');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT fk_a677fe765da5bed0');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT fk_a677fe7699049ece');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT fk_a677fe76b03a8386');
        $this->addSql('ALTER TABLE accessible_element_option DROP CONSTRAINT fk_a677fe769033212a');
        $this->addSql('DROP TABLE accessible_task_group_rule');
        $this->addSql('DROP TABLE element_option');
        $this->addSql('DROP TABLE default_task_rule');
        $this->addSql('DROP TABLE element_value');
        $this->addSql('DROP TABLE default_task_group_rule');
        $this->addSql('DROP TABLE default_element_value');
        $this->addSql('DROP TABLE default_element_option');
        $this->addSql('DROP TABLE task_group_rule');
        $this->addSql('DROP TABLE task_rule');
        $this->addSql('DROP TABLE accessible_task_rule');
        $this->addSql('DROP TABLE accessible_element_value');
        $this->addSql('DROP TABLE accessible_element_option');
        $this->addSql('ALTER TABLE accessible_task ADD activated_by_sap_data BOOLEAN NOT NULL DEFAULT false');
        $this->addSql('ALTER TABLE accessible_task_group ADD rules JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE accessible_task_relation ADD rules JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE default_task_group ADD rules JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN default_task_group.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE default_task_relation ADD rules JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN default_task_relation.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE task_group ADD rules JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN task_group.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('ALTER TABLE task_relation ADD rules JSON DEFAULT \'[]\' NOT NULL');
        $this->addSql('COMMENT ON COLUMN task_relation.rules IS \'(DC2Type:json_document)\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE TABLE accessible_task_group_rule (id UUID NOT NULL, key_accessible_element_id UUID NOT NULL, accessible_task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_f6816a3cf5163172 ON accessible_task_group_rule (key_accessible_element_id)');
        $this->addSql('CREATE INDEX idx_f6816a3c9033212a ON accessible_task_group_rule (tenant_id)');
        $this->addSql('CREATE INDEX idx_f6816a3c99049ece ON accessible_task_group_rule (modified_by_id)');
        $this->addSql('CREATE INDEX idx_f6816a3cb03a8386 ON accessible_task_group_rule (created_by_id)');
        $this->addSql('CREATE INDEX idx_f6816a3c32c0fdc9 ON accessible_task_group_rule (accessible_task_group_id)');
        $this->addSql('COMMENT ON COLUMN accessible_task_group_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE element_option (id UUID NOT NULL, element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, org_uuid VARCHAR(255) DEFAULT NULL, is_from_external_source BOOLEAN DEFAULT false NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_d39109241f1f2a24 ON element_option (element_id)');
        $this->addSql('CREATE INDEX idx_d3910924b03a8386 ON element_option (created_by_id)');
        $this->addSql('CREATE INDEX idx_d39109249033212a ON element_option (tenant_id)');
        $this->addSql('CREATE INDEX idx_d391092499049ece ON element_option (modified_by_id)');
        $this->addSql('COMMENT ON COLUMN element_option.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN element_option.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_rule (id UUID NOT NULL, key_default_task_element_relation_id UUID NOT NULL, default_task_relation_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_a409101099049ece ON default_task_rule (modified_by_id)');
        $this->addSql('CREATE INDEX idx_a4091010b03a8386 ON default_task_rule (created_by_id)');
        $this->addSql('CREATE INDEX idx_a409101079cd4910 ON default_task_rule (key_default_task_element_relation_id)');
        $this->addSql('CREATE INDEX idx_a4091010fbdef43e ON default_task_rule (default_task_relation_id)');
        $this->addSql('CREATE INDEX idx_a40910109033212a ON default_task_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN default_task_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE element_value (id UUID NOT NULL, element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_d9552cadb03a8386 ON element_value (created_by_id)');
        $this->addSql('CREATE INDEX idx_d9552cad1f1f2a24 ON element_value (element_id)');
        $this->addSql('CREATE INDEX idx_d9552cad9033212a ON element_value (tenant_id)');
        $this->addSql('CREATE INDEX idx_d9552cad99049ece ON element_value (modified_by_id)');
        $this->addSql('COMMENT ON COLUMN element_value.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN element_value.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_task_group_rule (id UUID NOT NULL, key_default_task_element_relation_id UUID NOT NULL, default_task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_ebc714cfb03a8386 ON default_task_group_rule (created_by_id)');
        $this->addSql('CREATE INDEX idx_ebc714cf79cd4910 ON default_task_group_rule (key_default_task_element_relation_id)');
        $this->addSql('CREATE INDEX idx_ebc714cf9033212a ON default_task_group_rule (tenant_id)');
        $this->addSql('CREATE INDEX idx_ebc714cf88b5af18 ON default_task_group_rule (default_task_group_id)');
        $this->addSql('CREATE INDEX idx_ebc714cf99049ece ON default_task_group_rule (modified_by_id)');
        $this->addSql('COMMENT ON COLUMN default_task_group_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_group_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_element_value (id UUID NOT NULL, default_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_ef74b0beb03a8386 ON default_element_value (created_by_id)');
        $this->addSql('CREATE INDEX idx_ef74b0be9033212a ON default_element_value (tenant_id)');
        $this->addSql('CREATE INDEX idx_ef74b0be573c7e47 ON default_element_value (default_element_id)');
        $this->addSql('CREATE INDEX idx_ef74b0be99049ece ON default_element_value (modified_by_id)');
        $this->addSql('COMMENT ON COLUMN default_element_value.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_element_value.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE default_element_option (id UUID NOT NULL, default_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_571969669033212a ON default_element_option (tenant_id)');
        $this->addSql('CREATE INDEX idx_57196966573c7e47 ON default_element_option (default_element_id)');
        $this->addSql('CREATE INDEX idx_5719696699049ece ON default_element_option (modified_by_id)');
        $this->addSql('CREATE INDEX idx_57196966b03a8386 ON default_element_option (created_by_id)');
        $this->addSql('COMMENT ON COLUMN default_element_option.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_element_option.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task_group_rule (id UUID NOT NULL, key_element_id UUID NOT NULL, task_group_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_7391bc13b03a8386 ON task_group_rule (created_by_id)');
        $this->addSql('CREATE INDEX idx_7391bc139033212a ON task_group_rule (tenant_id)');
        $this->addSql('CREATE INDEX idx_7391bc1399049ece ON task_group_rule (modified_by_id)');
        $this->addSql('CREATE INDEX idx_7391bc13be94330b ON task_group_rule (task_group_id)');
        $this->addSql('CREATE INDEX idx_7391bc13afb2749b ON task_group_rule (key_element_id)');
        $this->addSql('COMMENT ON COLUMN task_group_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_group_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE task_rule (id UUID NOT NULL, key_element_id UUID NOT NULL, task_relation_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_356b38b7b03a8386 ON task_rule (created_by_id)');
        $this->addSql('CREATE INDEX idx_356b38b799049ece ON task_rule (modified_by_id)');
        $this->addSql('CREATE INDEX idx_356b38b774233c69 ON task_rule (task_relation_id)');
        $this->addSql('CREATE INDEX idx_356b38b7afb2749b ON task_rule (key_element_id)');
        $this->addSql('CREATE INDEX idx_356b38b79033212a ON task_rule (tenant_id)');
        $this->addSql('COMMENT ON COLUMN task_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_task_rule (id UUID NOT NULL, key_accessible_element_id UUID NOT NULL, accessible_task_relation_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, rule_operator VARCHAR(10) NOT NULL, value VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_3d67a09edf7711e6 ON accessible_task_rule (accessible_task_relation_id)');
        $this->addSql('CREATE INDEX idx_3d67a09eb03a8386 ON accessible_task_rule (created_by_id)');
        $this->addSql('CREATE INDEX idx_3d67a09e99049ece ON accessible_task_rule (modified_by_id)');
        $this->addSql('CREATE INDEX idx_3d67a09e9033212a ON accessible_task_rule (tenant_id)');
        $this->addSql('CREATE INDEX idx_3d67a09ef5163172 ON accessible_task_rule (key_accessible_element_id)');
        $this->addSql('COMMENT ON COLUMN accessible_task_rule.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_rule.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_element_value (id UUID NOT NULL, accessible_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_5501e26f9033212a ON accessible_element_value (tenant_id)');
        $this->addSql('CREATE INDEX idx_5501e26f99049ece ON accessible_element_value (modified_by_id)');
        $this->addSql('CREATE INDEX idx_5501e26fb03a8386 ON accessible_element_value (created_by_id)');
        $this->addSql('CREATE INDEX idx_5501e26f5da5bed0 ON accessible_element_value (accessible_element_id)');
        $this->addSql('COMMENT ON COLUMN accessible_element_value.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element_value.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE accessible_element_option (id UUID NOT NULL, accessible_element_id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(100) NOT NULL, org_uuid VARCHAR(255) DEFAULT NULL, sequence_number INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_a677fe76b03a8386 ON accessible_element_option (created_by_id)');
        $this->addSql('CREATE INDEX idx_a677fe7699049ece ON accessible_element_option (modified_by_id)');
        $this->addSql('CREATE INDEX idx_a677fe765da5bed0 ON accessible_element_option (accessible_element_id)');
        $this->addSql('CREATE INDEX idx_a677fe769033212a ON accessible_element_option (tenant_id)');
        $this->addSql('COMMENT ON COLUMN accessible_element_option.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element_option.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT fk_f6816a3cf5163172 FOREIGN KEY (key_accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT fk_f6816a3c32c0fdc9 FOREIGN KEY (accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT fk_f6816a3c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT fk_f6816a3cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT fk_f6816a3c9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT fk_d39109241f1f2a24 FOREIGN KEY (element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT fk_d391092499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT fk_d3910924b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_option ADD CONSTRAINT fk_d39109249033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT fk_a4091010fbdef43e FOREIGN KEY (default_task_relation_id) REFERENCES default_task_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT fk_a409101079cd4910 FOREIGN KEY (key_default_task_element_relation_id) REFERENCES default_task_element_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT fk_a409101099049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT fk_a4091010b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT fk_a40910109033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT fk_d9552cad99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT fk_d9552cadb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT fk_d9552cad1f1f2a24 FOREIGN KEY (element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE element_value ADD CONSTRAINT fk_d9552cad9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT fk_ebc714cf99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT fk_ebc714cfb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT fk_ebc714cf79cd4910 FOREIGN KEY (key_default_task_element_relation_id) REFERENCES default_task_element_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT fk_ebc714cf88b5af18 FOREIGN KEY (default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT fk_ebc714cf9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT fk_ef74b0be573c7e47 FOREIGN KEY (default_element_id) REFERENCES default_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT fk_ef74b0be99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT fk_ef74b0beb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_value ADD CONSTRAINT fk_ef74b0be9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT fk_57196966573c7e47 FOREIGN KEY (default_element_id) REFERENCES default_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT fk_5719696699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT fk_57196966b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE default_element_option ADD CONSTRAINT fk_571969669033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT fk_7391bc13be94330b FOREIGN KEY (task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT fk_7391bc13afb2749b FOREIGN KEY (key_element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT fk_7391bc1399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT fk_7391bc13b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT fk_7391bc139033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT fk_356b38b7afb2749b FOREIGN KEY (key_element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT fk_356b38b774233c69 FOREIGN KEY (task_relation_id) REFERENCES task_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT fk_356b38b799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT fk_356b38b7b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT fk_356b38b79033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT fk_3d67a09ef5163172 FOREIGN KEY (key_accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT fk_3d67a09edf7711e6 FOREIGN KEY (accessible_task_relation_id) REFERENCES accessible_task_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT fk_3d67a09e99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT fk_3d67a09eb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT fk_3d67a09e9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT fk_5501e26f5da5bed0 FOREIGN KEY (accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT fk_5501e26f99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT fk_5501e26fb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_value ADD CONSTRAINT fk_5501e26f9033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT fk_a677fe765da5bed0 FOREIGN KEY (accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT fk_a677fe7699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT fk_a677fe76b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE accessible_element_option ADD CONSTRAINT fk_a677fe769033212a FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE task_relation DROP rules');
        $this->addSql('ALTER TABLE accessible_task DROP activated_by_sap_data');
        $this->addSql('ALTER TABLE accessible_task_group DROP rules');
        $this->addSql('ALTER TABLE default_task_group DROP rules');
        $this->addSql('ALTER TABLE task_group DROP rules');
        $this->addSql('ALTER TABLE default_task_relation DROP rules');
        $this->addSql('ALTER TABLE accessible_task_relation DROP rules');
    }
}
