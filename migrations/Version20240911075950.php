<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240911075950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Make sure we do not have any NULL values in the modified_at column
        $this->addSql('UPDATE "accessible_additional_information" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_element" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_interruption" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_note" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_note_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_task" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_task_group" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_task_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_termination" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "accessible_termination_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "additional_information" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "additional_service_config" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "booking" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "branch" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "connected_device" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "customer" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "customer_version" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_additional_information" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_element" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_interruption" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_interruption_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_material" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_material_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_note" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_note_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_task" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_task_element_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_task_group" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_task_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_termination" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_termination_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "default_unit_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "delivery_service" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "device_access" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "device_message" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "device_message_thread" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "element" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "equipment" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "equipment_config" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "equipment_version" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "faq" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "feedback" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "interruption" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "location" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "location_format_config" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "location_version" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "message" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "mobile_device" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "note" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "order" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "order_document" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "order_document_text_block" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "order_type_config" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "session" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "session_equipment" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "session_tour" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "session_user" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "staff" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "staff_config" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "staff_version" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "task" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "task_group" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "task_relation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "termination" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "tour" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "tour_data_config" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "tour_equipment" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "tour_staff" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "translation" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "user" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');
        $this->addSql('UPDATE "user_setting" SET "modified_at" = "created_at" WHERE "modified_at" IS NULL');

        // Make sure the modified_at column is NOT NULL
        $this->addSql('ALTER TABLE "order" ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE "session_user" ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE "user" ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_additional_information ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_element ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_interruption ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_note ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_note_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_task ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_task_group ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_task_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_termination ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE accessible_termination_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE additional_information ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE additional_service_config ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE booking ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE branch ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE connected_device ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE customer ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE customer_version ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_additional_information ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_element ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_interruption ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_interruption_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_material ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_material_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_note ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_note_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_task ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_task_element_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_task_group ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_task_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_termination ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_termination_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE default_unit_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE delivery_service ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE device_access ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE device_message ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE device_message_thread ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE element ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE equipment ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE equipment_config ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE equipment_version ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE faq ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE feedback ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE interruption ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE location ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE location_format_config ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE location_version ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE message ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE mobile_device ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE note ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE order_document ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE order_document_text_block ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE order_type_config ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE session ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE session_equipment ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE session_tour ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE staff ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE staff_config ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE staff_version ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE task ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE task_group ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE task_relation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE termination ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE tour ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE tour_data_config ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE tour_equipment ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE tour_staff ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE translation ALTER modified_at SET NOT NULL');
        $this->addSql('ALTER TABLE user_setting ALTER modified_at SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE default_task ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE device_message_thread ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE tour_staff ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE mobile_device ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE element ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_task_group ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE task_group ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_note_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_task_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE location ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_interruption ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_additional_information ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE device_access ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE tour_data_config ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_note ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE task_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE location_version ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE order_type_config ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_task ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE translation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE device_message ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE additional_information ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE booking ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE message ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE user_setting ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_interruption ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE interruption ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_note ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE session_tour ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_termination_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_element ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_additional_information ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE additional_service_config ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE session_equipment ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE staff ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_task_element_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_element ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE task ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE equipment ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_interruption_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE order_document_text_block ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE location_format_config ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE "user" ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE customer ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE feedback ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE equipment_version ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE order_document ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_termination ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_termination_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE connected_device ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE equipment_config ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE tour_equipment ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE staff_version ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_note_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_task_group ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_unit_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE termination ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE delivery_service ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE faq ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE staff_config ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE "session_user" ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE session ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_material_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE customer_version ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE note ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_material ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE accessible_task_relation ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE tour ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE branch ALTER modified_at DROP NOT NULL');
        $this->addSql('ALTER TABLE default_termination ALTER modified_at DROP NOT NULL');
    }
}
