<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240523134017 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE default_task RENAME COLUMN only_for_assignment TO activated_by_sap_data');
        $this->addSql('ALTER TABLE task RENAME COLUMN only_for_assignment TO activated_by_sap_data');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE default_task RENAME COLUMN activated_by_sap_data TO only_for_assignment');
        $this->addSql('ALTER TABLE task RENAME COLUMN activated_by_sap_data TO only_for_assignment');
    }
}
