<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250310134239 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE mastertour_template ADD search VARCHAR(512) DEFAULT \'\'');
        $this->addSql('UPDATE mastertour_template SET search = name || \' \' || external_id');
        $this->addSql('ALTER TABLE mastertour_template ALTER search SET NOT NULL');
        $this->addSql('CREATE INDEX IDX_A2F5B5B0B4F0DBA7 ON mastertour_template USING gin (search gin_trgm_ops)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_A2F5B5B0B4F0DBA7');
        $this->addSql('ALTER TABLE public.mastertour_template DROP search');
    }
}
