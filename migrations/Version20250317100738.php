<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250317100738 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('TRUNCATE TABLE dako_process');
        $this->addSql('ALTER TABLE dako_process ADD ddd_file_count INT DEFAULT 1 NOT NULL');
        $this->addSql('ALTER TABLE dako_process ADD ddd_files JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE dako_process DROP ddd_file');
        $this->addSql('ALTER TABLE dako_process ADD uploaded_files JSONB DEFAULT \'[]\' NOT NULL');
        $this->addSql('ALTER TABLE dako_process ALTER equipment_id SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE public.dako_process ALTER equipment_id DROP NOT NULL');
        $this->addSql('ALTER TABLE public.dako_process DROP uploaded_files');
        $this->addSql('ALTER TABLE public.dako_process ADD ddd_file VARCHAR(36) DEFAULT NULL');
        $this->addSql('ALTER TABLE public.dako_process DROP ddd_file_count');
        $this->addSql('ALTER TABLE public.dako_process DROP ddd_files');
    }
}
