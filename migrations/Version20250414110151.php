<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250414110151 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_interruption DROP CONSTRAINT fk_7ee32a9e99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_interruption DROP CONSTRAINT fk_7ee32a9eb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_295367bdb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_295367bd99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note DROP CONSTRAINT fk_8747c5ac99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note DROP CONSTRAINT fk_8747c5acb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d0f02c25b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d0f02c2599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note_relation DROP CONSTRAINT fk_fe86551599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_note_relation DROP CONSTRAINT fk_fe865515b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_5cb6942ab03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_5cb6942a99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task DROP CONSTRAINT fk_1a84e49d99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task DROP CONSTRAINT fk_1a84e49db03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_4d330d14b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_4d330d1499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_group DROP CONSTRAINT fk_e8decb1199049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_group DROP CONSTRAINT fk_e8decb11b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1df365b9b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1df365b999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_relation DROP CONSTRAINT fk_ae1d63c799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task_relation DROP CONSTRAINT fk_ae1d63c7b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c2da2f8b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c2da2f899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination DROP CONSTRAINT fk_5e2d48b399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination DROP CONSTRAINT fk_5e2d48b3b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_86d54ec7b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_86d54ec799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination_relation DROP CONSTRAINT fk_5f3616f299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_termination_relation DROP CONSTRAINT fk_5f3616f2b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dea6750b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dea675099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE additional_service_config DROP CONSTRAINT fk_ad3c955799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE additional_service_config DROP CONSTRAINT fk_ad3c9557b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1bf888abb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1bf888ab99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE branch DROP CONSTRAINT fk_bb861b1f99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE branch DROP CONSTRAINT fk_bb861b1fb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f1686f3fb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f1686f3f99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE connected_device DROP CONSTRAINT fk_f097d20399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE connected_device DROP CONSTRAINT fk_f097d203b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_64a45e6eb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_64a45e6e99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer DROP CONSTRAINT fk_81398e0999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer DROP CONSTRAINT fk_81398e09b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_43d55560b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_43d5556099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_version DROP CONSTRAINT fk_a3c1777d99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_version DROP CONSTRAINT fk_a3c1777db03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_37f2fb10b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_37f2fb1099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_account DROP CONSTRAINT fk_47097c8699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_account DROP CONSTRAINT fk_47097c86b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_47097c86b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_47097c8699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process DROP CONSTRAINT fk_bc2232b499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE dako_process DROP CONSTRAINT fk_bc2232b4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_bc2232b4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_bc2232b499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption DROP CONSTRAINT fk_db87fe6a99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption DROP CONSTRAINT fk_db87fe6ab03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f9ed013db03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f9ed013d99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption_relation DROP CONSTRAINT fk_5aad37fb99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_interruption_relation DROP CONSTRAINT fk_5aad37fbb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f77aa34b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f77aa3499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material DROP CONSTRAINT fk_7afa45db99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material DROP CONSTRAINT fk_7afa45dbb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_eec9c9b6b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_eec9c9b699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material_relation DROP CONSTRAINT fk_d1d15a7499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_material_relation DROP CONSTRAINT fk_d1d15a74b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_67154788b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_6715478899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note DROP CONSTRAINT fk_8a4cc11299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note DROP CONSTRAINT fk_8a4cc112b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ca1fbc36b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ca1fbc3699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note_relation DROP CONSTRAINT fk_44f307c499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_note_relation DROP CONSTRAINT fk_44f307c4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b1dea96cb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b1dea96c99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task DROP CONSTRAINT fk_178fe02399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task DROP CONSTRAINT fk_178fe023b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_57dc9d07b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_57dc9d0799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_group DROP CONSTRAINT fk_e2470b8699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_group DROP CONSTRAINT fk_e2470b86b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d61a4554b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d61a455499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_relation DROP CONSTRAINT fk_1468311699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task_relation DROP CONSTRAINT fk_14683116b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e1459fbeb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e1459fbe99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination DROP CONSTRAINT fk_304cd79499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination DROP CONSTRAINT fk_304cd794b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_58a53922b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_58a5392299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination_relation DROP CONSTRAINT fk_503976be99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_termination_relation DROP CONSTRAINT fk_503976beb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_8f829609b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_8f82960999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_unit_relation DROP CONSTRAINT fk_219631c899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_unit_relation DROP CONSTRAINT fk_219631c8b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d4bb9f60b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d4bb9f6099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_access DROP CONSTRAINT fk_d5795a4f99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_access DROP CONSTRAINT fk_d5795a4fb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e93aede3b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e93aede399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message DROP CONSTRAINT fk_3c0be9e299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message DROP CONSTRAINT fk_3c0be9e2b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e3574596b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e357459699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread DROP CONSTRAINT fk_a04a207c99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE device_message_thread DROP CONSTRAINT fk_a04a207cb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_55678ed4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_55678ed499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document DROP CONSTRAINT fk_1a85511f99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document DROP CONSTRAINT fk_1a85511fb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511fb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1a85511f99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment DROP CONSTRAINT fk_d338d58399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment DROP CONSTRAINT fk_d338d583b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e794e0a4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e794e0a499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_config DROP CONSTRAINT fk_7304503c99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_config DROP CONSTRAINT fk_7304503cb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e737dc51b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_e737dc5199049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_version DROP CONSTRAINT fk_c9671c2c99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE equipment_version DROP CONSTRAINT fk_c9671c2cb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_faf03245b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_faf0324599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE faq DROP CONSTRAINT fk_e8ff75cc99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE faq DROP CONSTRAINT fk_e8ff75ccb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d715cdffb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d715cdff99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE feedback DROP CONSTRAINT fk_d229445899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE feedback DROP CONSTRAINT fk_d2294458b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_10c59f31b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_10c59f3199049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE interruption DROP CONSTRAINT fk_f9511bc099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE interruption DROP CONSTRAINT fk_f9511bc0b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b90266e4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b90266e499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location DROP CONSTRAINT fk_5e9e89cb99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location DROP CONSTRAINT fk_5e9e89cbb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9c7252a2b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9c7252a299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_format_config DROP CONSTRAINT fk_5f228999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_format_config DROP CONSTRAINT fk_5f2289b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d8a724fdb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d8a724fd99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_version DROP CONSTRAINT fk_5159c06799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE location_version DROP CONSTRAINT fk_5159c067b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c56a4c0ab03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c56a4c0a99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_progress DROP CONSTRAINT fk_7f7db6c399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_progress DROP CONSTRAINT fk_7f7db6c3b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_17945875b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1794587599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_template DROP CONSTRAINT fk_ca1c5b0699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mastertour_template DROP CONSTRAINT fk_ca1c5b06b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a2f5b5b0b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a2f5b5b099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE note DROP CONSTRAINT fk_cfbdfa1499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE note DROP CONSTRAINT fk_cfbdfa14b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_705271bab03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_705271ba99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "order" DROP CONSTRAINT fk_f529939899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "order" DROP CONSTRAINT fk_f5299398b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c4f8f2fcb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c4f8f2fc99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_type_config DROP CONSTRAINT fk_ecef3bc299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_type_config DROP CONSTRAINT fk_ecef3bc2b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_df7815abb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_df7815ab99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE point_of_interest DROP CONSTRAINT fk_e67ad35999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE point_of_interest DROP CONSTRAINT fk_e67ad359b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d5edfd30b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d5edfd3099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE sap_retry_queue DROP CONSTRAINT fk_ca76e30299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE sap_retry_queue DROP CONSTRAINT fk_ca76e302b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dc10a8bb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_9dc10a8b99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session DROP CONSTRAINT fk_d044d5d499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session DROP CONSTRAINT fk_d044d5d4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_eb601b68b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_eb601b6899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_booking DROP CONSTRAINT fk_a563860d99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_booking DROP CONSTRAINT fk_a563860db03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a563860db03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a563860d99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_equipment DROP CONSTRAINT fk_90da908999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_equipment DROP CONSTRAINT fk_90da9089b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a34dbee0b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_a34dbee099049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_tour DROP CONSTRAINT fk_aca0f94399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE session_tour DROP CONSTRAINT fk_aca0f943b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ecf38467b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ecf3846799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "session_user" DROP CONSTRAINT fk_4be2d66399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "session_user" DROP CONSTRAINT fk_4be2d663b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_bb1ab47b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_bb1ab4799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff DROP CONSTRAINT fk_426ef39299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff DROP CONSTRAINT fk_426ef392b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_73bf92f6b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_73bf92f699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_config DROP CONSTRAINT fk_d1f79dc799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_config DROP CONSTRAINT fk_d1f79dc7b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_91a4e0e3b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_91a4e0e399049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_version DROP CONSTRAINT fk_e3aac47599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE staff_version DROP CONSTRAINT fk_e3aac475b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_dfe973d9b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_dfe973d999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task DROP CONSTRAINT fk_527edb2599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task DROP CONSTRAINT fk_527edb25b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ed91508bb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_ed91508b99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_group DROP CONSTRAINT fk_aa645fe599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_group DROP CONSTRAINT fk_aa645fe5b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f5a46bbb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_f5a46bb99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_relation DROP CONSTRAINT fk_2249ad0599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task_relation DROP CONSTRAINT fk_2249ad05b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1e0a1aa9b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_1e0a1aa999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE termination DROP CONSTRAINT fk_e4bfc42299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE termination DROP CONSTRAINT fk_e4bfc422b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_68c986c8b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_68c986c899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour DROP CONSTRAINT fk_6ad1f96999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour DROP CONSTRAINT fk_6ad1f969b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d53e72c7b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_d53e72c799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_data_config DROP CONSTRAINT fk_d4224ca699049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_data_config DROP CONSTRAINT fk_d4224ca6b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_4011c0cbb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_4011c0cb99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_equipment DROP CONSTRAINT fk_6c724bcc99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_equipment DROP CONSTRAINT fk_6c724bccb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b32ee7b8b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_b32ee7b899049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_staff DROP CONSTRAINT fk_7921e3ba99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tour_staff DROP CONSTRAINT fk_7921e3bab03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_dc1ffae4b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_dc1ffae499049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tracking DROP CONSTRAINT fk_a87c621c99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tracking DROP CONSTRAINT fk_a87c621cb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_6a90b975b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_6a90b97599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE translation DROP CONSTRAINT fk_b469456f99049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE translation DROP CONSTRAINT fk_b469456fb03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_381f0785b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_381f078599049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" DROP CONSTRAINT fk_8d93d64999049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" DROP CONSTRAINT fk_8d93d649b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_327c5de7b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_327c5de799049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_setting DROP CONSTRAINT fk_c779a69299049ece
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_setting DROP CONSTRAINT fk_c779a692b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_872adbb6b03a8386
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_872adbb699049ece
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" ADD CONSTRAINT fk_f529939899049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."order" ADD CONSTRAINT fk_f5299398b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c4f8f2fcb03a8386 ON public."order" (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c4f8f2fc99049ece ON public."order" (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation ADD CONSTRAINT fk_fe86551599049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note_relation ADD CONSTRAINT fk_fe865515b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_5cb6942ab03a8386 ON public.accessible_note_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_5cb6942a99049ece ON public.accessible_note_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task ADD CONSTRAINT fk_527edb2599049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task ADD CONSTRAINT fk_527edb25b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ed91508bb03a8386 ON public.task (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ed91508b99049ece ON public.task (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff ADD CONSTRAINT fk_7921e3ba99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_staff ADD CONSTRAINT fk_7921e3bab03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_dc1ffae4b03a8386 ON public.tour_staff (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_dc1ffae499049ece ON public.tour_staff (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device ADD CONSTRAINT fk_f097d20399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.connected_device ADD CONSTRAINT fk_f097d203b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_64a45e6eb03a8386 ON public.connected_device (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_64a45e6e99049ece ON public.connected_device (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq ADD CONSTRAINT fk_e8ff75cc99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.faq ADD CONSTRAINT fk_e8ff75ccb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d715cdffb03a8386 ON public.faq (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d715cdff99049ece ON public.faq (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue ADD CONSTRAINT fk_ca76e30299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.sap_retry_queue ADD CONSTRAINT fk_ca76e302b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dc10a8bb03a8386 ON public.sap_retry_queue (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dc10a8b99049ece ON public.sap_retry_queue (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation ADD CONSTRAINT fk_44f307c499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note_relation ADD CONSTRAINT fk_44f307c4b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b1dea96cb03a8386 ON public.default_note_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b1dea96c99049ece ON public.default_note_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message ADD CONSTRAINT fk_3c0be9e299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message ADD CONSTRAINT fk_3c0be9e2b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e3574596b03a8386 ON public.device_message (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e357459699049ece ON public.device_message (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config ADD CONSTRAINT fk_d4224ca699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_data_config ADD CONSTRAINT fk_d4224ca6b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4011c0cbb03a8386 ON public.tour_data_config (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4011c0cb99049ece ON public.tour_data_config (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD CONSTRAINT fk_1a85511f99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD CONSTRAINT fk_1a85511fb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511fb03a8386 ON public.document (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1a85511f99049ece ON public.document (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation ADD CONSTRAINT fk_ae1d63c799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_relation ADD CONSTRAINT fk_ae1d63c7b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c2da2f8b03a8386 ON public.accessible_task_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c2da2f899049ece ON public.accessible_task_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version ADD CONSTRAINT fk_c9671c2c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_version ADD CONSTRAINT fk_c9671c2cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_faf03245b03a8386 ON public.equipment_version (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_faf0324599049ece ON public.equipment_version (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session ADD CONSTRAINT fk_d044d5d499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session ADD CONSTRAINT fk_d044d5d4b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_eb601b68b03a8386 ON public.session (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_eb601b6899049ece ON public.session (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour ADD CONSTRAINT fk_aca0f94399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_tour ADD CONSTRAINT fk_aca0f943b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ecf38467b03a8386 ON public.session_tour (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ecf3846799049ece ON public.session_tour (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location ADD CONSTRAINT fk_5e9e89cb99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location ADD CONSTRAINT fk_5e9e89cbb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9c7252a2b03a8386 ON public.location (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9c7252a299049ece ON public.location (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group ADD CONSTRAINT fk_aa645fe599049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_group ADD CONSTRAINT fk_aa645fe5b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f5a46bbb03a8386 ON public.task_group (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f5a46bb99049ece ON public.task_group (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption ADD CONSTRAINT fk_7ee32a9e99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_interruption ADD CONSTRAINT fk_7ee32a9eb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_295367bdb03a8386 ON public.accessible_interruption (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_295367bd99049ece ON public.accessible_interruption (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation ADD CONSTRAINT fk_219631c899049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_unit_relation ADD CONSTRAINT fk_219631c8b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d4bb9f60b03a8386 ON public.default_unit_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d4bb9f6099049ece ON public.default_unit_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config ADD CONSTRAINT fk_5f228999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_format_config ADD CONSTRAINT fk_5f2289b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d8a724fdb03a8386 ON public.location_format_config (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d8a724fd99049ece ON public.location_format_config (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ADD CONSTRAINT fk_a04a207c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_message_thread ADD CONSTRAINT fk_a04a207cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_55678ed4b03a8386 ON public.device_message_thread (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_55678ed499049ece ON public.device_message_thread (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking ADD CONSTRAINT fk_a87c621c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tracking ADD CONSTRAINT fk_a87c621cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6a90b975b03a8386 ON public.tracking (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6a90b97599049ece ON public.tracking (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config ADD CONSTRAINT fk_7304503c99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment_config ADD CONSTRAINT fk_7304503cb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e737dc51b03a8386 ON public.equipment_config (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e737dc5199049ece ON public.equipment_config (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task ADD CONSTRAINT fk_178fe02399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task ADD CONSTRAINT fk_178fe023b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_57dc9d07b03a8386 ON public.default_task (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_57dc9d0799049ece ON public.default_task (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" ADD CONSTRAINT fk_4be2d66399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."session_user" ADD CONSTRAINT fk_4be2d663b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bb1ab47b03a8386 ON public."session_user" (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bb1ab4799049ece ON public."session_user" (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation ADD CONSTRAINT fk_5aad37fb99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption_relation ADD CONSTRAINT fk_5aad37fbb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f77aa34b03a8386 ON public.default_interruption_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f77aa3499049ece ON public.default_interruption_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination ADD CONSTRAINT fk_5e2d48b399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination ADD CONSTRAINT fk_5e2d48b3b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_86d54ec7b03a8386 ON public.accessible_termination (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_86d54ec799049ece ON public.accessible_termination (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback ADD CONSTRAINT fk_d229445899049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.feedback ADD CONSTRAINT fk_d2294458b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_10c59f31b03a8386 ON public.feedback (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_10c59f3199049ece ON public.feedback (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking ADD CONSTRAINT fk_a563860d99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_booking ADD CONSTRAINT fk_a563860db03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a563860db03a8386 ON public.session_booking (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a563860d99049ece ON public.session_booking (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version ADD CONSTRAINT fk_5159c06799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.location_version ADD CONSTRAINT fk_5159c067b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c56a4c0ab03a8386 ON public.location_version (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_c56a4c0a99049ece ON public.location_version (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest ADD CONSTRAINT fk_e67ad35999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.point_of_interest ADD CONSTRAINT fk_e67ad359b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d5edfd30b03a8386 ON public.point_of_interest (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d5edfd3099049ece ON public.point_of_interest (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config ADD CONSTRAINT fk_ecef3bc299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.order_type_config ADD CONSTRAINT fk_ecef3bc2b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_df7815abb03a8386 ON public.order_type_config (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_df7815ab99049ece ON public.order_type_config (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material ADD CONSTRAINT fk_7afa45db99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material ADD CONSTRAINT fk_7afa45dbb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_eec9c9b6b03a8386 ON public.default_material (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_eec9c9b699049ece ON public.default_material (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group ADD CONSTRAINT fk_e2470b8699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_group ADD CONSTRAINT fk_e2470b86b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d61a4554b03a8386 ON public.default_task_group (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d61a455499049ece ON public.default_task_group (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer ADD CONSTRAINT fk_81398e0999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer ADD CONSTRAINT fk_81398e09b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_43d55560b03a8386 ON public.customer (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_43d5556099049ece ON public.customer (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination ADD CONSTRAINT fk_304cd79499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination ADD CONSTRAINT fk_304cd794b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_58a53922b03a8386 ON public.default_termination (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_58a5392299049ece ON public.default_termination (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination ADD CONSTRAINT fk_e4bfc42299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.termination ADD CONSTRAINT fk_e4bfc422b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_68c986c8b03a8386 ON public.termination (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_68c986c899049ece ON public.termination (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template ADD CONSTRAINT fk_ca1c5b0699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_template ADD CONSTRAINT fk_ca1c5b06b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a2f5b5b0b03a8386 ON public.mastertour_template (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a2f5b5b099049ece ON public.mastertour_template (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task ADD CONSTRAINT fk_1a84e49d99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task ADD CONSTRAINT fk_1a84e49db03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4d330d14b03a8386 ON public.accessible_task (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_4d330d1499049ece ON public.accessible_task (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note ADD CONSTRAINT fk_8747c5ac99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_note ADD CONSTRAINT fk_8747c5acb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d0f02c25b03a8386 ON public.accessible_note (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d0f02c2599049ece ON public.accessible_note (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation ADD CONSTRAINT fk_503976be99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_termination_relation ADD CONSTRAINT fk_503976beb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_8f829609b03a8386 ON public.default_termination_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_8f82960999049ece ON public.default_termination_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation ADD CONSTRAINT fk_5f3616f299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_termination_relation ADD CONSTRAINT fk_5f3616f2b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dea6750b03a8386 ON public.accessible_termination_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_9dea675099049ece ON public.accessible_termination_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption ADD CONSTRAINT fk_f9511bc099049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.interruption ADD CONSTRAINT fk_f9511bc0b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b90266e4b03a8386 ON public.interruption (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b90266e499049ece ON public.interruption (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version ADD CONSTRAINT fk_a3c1777d99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.customer_version ADD CONSTRAINT fk_a3c1777db03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_37f2fb10b03a8386 ON public.customer_version (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_37f2fb1099049ece ON public.customer_version (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch ADD CONSTRAINT fk_bb861b1f99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.branch ADD CONSTRAINT fk_bb861b1fb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f1686f3fb03a8386 ON public.branch (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f1686f3f99049ece ON public.branch (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption ADD CONSTRAINT fk_db87fe6a99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_interruption ADD CONSTRAINT fk_db87fe6ab03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f9ed013db03a8386 ON public.default_interruption (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_f9ed013d99049ece ON public.default_interruption (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation ADD CONSTRAINT fk_2249ad0599049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task_relation ADD CONSTRAINT fk_2249ad05b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1e0a1aa9b03a8386 ON public.task_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1e0a1aa999049ece ON public.task_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access ADD CONSTRAINT fk_d5795a4f99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.device_access ADD CONSTRAINT fk_d5795a4fb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e93aede3b03a8386 ON public.device_access (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e93aede399049ece ON public.device_access (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config ADD CONSTRAINT fk_d1f79dc799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_config ADD CONSTRAINT fk_d1f79dc7b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_91a4e0e3b03a8386 ON public.staff_config (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_91a4e0e399049ece ON public.staff_config (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment ADD CONSTRAINT fk_d338d58399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.equipment ADD CONSTRAINT fk_d338d583b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e794e0a4b03a8386 ON public.equipment (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e794e0a499049ece ON public.equipment (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation ADD CONSTRAINT fk_1468311699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task_relation ADD CONSTRAINT fk_14683116b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e1459fbeb03a8386 ON public.default_task_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_e1459fbe99049ece ON public.default_task_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config ADD CONSTRAINT fk_ad3c955799049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.additional_service_config ADD CONSTRAINT fk_ad3c9557b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1bf888abb03a8386 ON public.additional_service_config (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1bf888ab99049ece ON public.additional_service_config (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account ADD CONSTRAINT fk_47097c8699049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_account ADD CONSTRAINT fk_47097c86b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_47097c86b03a8386 ON public.dako_account (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_47097c8699049ece ON public.dako_account (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress ADD CONSTRAINT fk_7f7db6c399049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.mastertour_progress ADD CONSTRAINT fk_7f7db6c3b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_17945875b03a8386 ON public.mastertour_progress (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1794587599049ece ON public.mastertour_progress (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment ADD CONSTRAINT fk_90da908999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.session_equipment ADD CONSTRAINT fk_90da9089b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a34dbee0b03a8386 ON public.session_equipment (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a34dbee099049ece ON public.session_equipment (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.translation ADD CONSTRAINT fk_b469456f99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.translation ADD CONSTRAINT fk_b469456fb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_381f0785b03a8386 ON public.translation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_381f078599049ece ON public.translation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff ADD CONSTRAINT fk_426ef39299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff ADD CONSTRAINT fk_426ef392b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_73bf92f6b03a8386 ON public.staff (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_73bf92f699049ece ON public.staff (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation ADD CONSTRAINT fk_d1d15a7499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_material_relation ADD CONSTRAINT fk_d1d15a74b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_67154788b03a8386 ON public.default_material_relation (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_6715478899049ece ON public.default_material_relation (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version ADD CONSTRAINT fk_e3aac47599049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.staff_version ADD CONSTRAINT fk_e3aac475b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_dfe973d9b03a8386 ON public.staff_version (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_dfe973d999049ece ON public.staff_version (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process ADD CONSTRAINT fk_bc2232b499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.dako_process ADD CONSTRAINT fk_bc2232b4b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bc2232b4b03a8386 ON public.dako_process (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_bc2232b499049ece ON public.dako_process (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note ADD CONSTRAINT fk_cfbdfa1499049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.note ADD CONSTRAINT fk_cfbdfa14b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_705271bab03a8386 ON public.note (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_705271ba99049ece ON public.note (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note ADD CONSTRAINT fk_8a4cc11299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_note ADD CONSTRAINT fk_8a4cc112b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ca1fbc36b03a8386 ON public.default_note (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_ca1fbc3699049ece ON public.default_note (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour ADD CONSTRAINT fk_6ad1f96999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour ADD CONSTRAINT fk_6ad1f969b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d53e72c7b03a8386 ON public.tour (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_d53e72c799049ece ON public.tour (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" ADD CONSTRAINT fk_8d93d64999049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public."user" ADD CONSTRAINT fk_8d93d649b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_327c5de7b03a8386 ON public."user" (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_327c5de799049ece ON public."user" (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment ADD CONSTRAINT fk_6c724bcc99049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.tour_equipment ADD CONSTRAINT fk_6c724bccb03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b32ee7b8b03a8386 ON public.tour_equipment (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_b32ee7b899049ece ON public.tour_equipment (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group ADD CONSTRAINT fk_e8decb1199049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task_group ADD CONSTRAINT fk_e8decb11b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1df365b9b03a8386 ON public.accessible_task_group (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_1df365b999049ece ON public.accessible_task_group (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting ADD CONSTRAINT fk_c779a69299049ece FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.user_setting ADD CONSTRAINT fk_c779a692b03a8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_872adbb6b03a8386 ON public.user_setting (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_872adbb699049ece ON public.user_setting (modified_by_id)
        SQL);
    }
}
