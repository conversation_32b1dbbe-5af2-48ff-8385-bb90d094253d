<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240920134900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE master_tour_template (id UUID NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, name VARCHAR(255) NOT NULL, external_id VARCHAR(255) NOT NULL, waypoints JSONB DEFAULT \'[]\' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_800A933AB03A8386 ON master_tour_template (created_by_id)');
        $this->addSql('CREATE INDEX IDX_800A933A99049ECE ON master_tour_template (modified_by_id)');
        $this->addSql('CREATE INDEX IDX_800A933A9033212A ON master_tour_template (tenant_id)');
        $this->addSql('CREATE INDEX IDX_800A933A7065677C ON master_tour_template (modified_at)');
        $this->addSql('COMMENT ON COLUMN master_tour_template.waypoints IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN master_tour_template.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN master_tour_template.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT FK_800A933AB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT FK_800A933A99049ECE FOREIGN KEY (modified_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE master_tour_template ADD CONSTRAINT FK_800A933A9033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT FK_800A933AB03A8386');
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT FK_800A933A99049ECE');
        $this->addSql('ALTER TABLE master_tour_template DROP CONSTRAINT FK_800A933A9033212A');
        $this->addSql('DROP TABLE master_tour_template');
    }
}
