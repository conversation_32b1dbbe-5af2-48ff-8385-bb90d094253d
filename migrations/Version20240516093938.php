<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240516093938 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('TRUNCATE TABLE task_rule');
        $this->addSql('TRUNCATE TABLE accessible_task_rule');
        $this->addSql('TRUNCATE TABLE default_task_rule');
        $this->addSql('TRUNCATE TABLE task_group_rule');
        $this->addSql('TRUNCATE TABLE accessible_task_group_rule');
        $this->addSql('TRUNCATE TABLE default_task_group_rule');

        $this->addSql('ALTER TABLE accessible_element DROP task_master');
        $this->addSql('ALTER TABLE accessible_element DROP task_group_master');

        $this->addSql('ALTER TABLE accessible_task_group ADD rule_default VARCHAR(15)');
        $this->addSql('UPDATE accessible_task_group SET rule_default = \'enabled\'');
        $this->addSql('ALTER TABLE accessible_task_group ALTER "rule_default" SET NOT NULL');

        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT fk_f6816a3c64f12dd');
        $this->addSql('DROP INDEX idx_f6816a3c64f12dd');
        $this->addSql('ALTER TABLE accessible_task_group_rule RENAME COLUMN key_accessible_task_group_id TO key_accessible_element_id');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT FK_F6816A3CF5163172 FOREIGN KEY (key_accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F6816A3CF5163172 ON accessible_task_group_rule (key_accessible_element_id)');

        $this->addSql('ALTER TABLE accessible_task_relation ADD rule_default VARCHAR(15)');
        $this->addSql('UPDATE accessible_task_relation SET rule_default = \'enabled\'');
        $this->addSql('ALTER TABLE accessible_task_relation ALTER "rule_default" SET NOT NULL');

        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT fk_3d67a09e8a34c39d');
        $this->addSql('DROP INDEX idx_3d67a09e8a34c39d');
        $this->addSql('ALTER TABLE accessible_task_rule RENAME COLUMN key_accessible_task_id TO key_accessible_element_id');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT FK_3D67A09EF5163172 FOREIGN KEY (key_accessible_element_id) REFERENCES accessible_element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_3D67A09EF5163172 ON accessible_task_rule (key_accessible_element_id)');
        $this->addSql('ALTER TABLE default_task_element_relation DROP task_master');
        $this->addSql('ALTER TABLE default_task_element_relation DROP task_group_master');

        $this->addSql('ALTER TABLE default_task_group ADD rule_default VARCHAR(15)');
        $this->addSql('UPDATE default_task_group SET rule_default = \'enabled\'');
        $this->addSql('ALTER TABLE default_task_group ALTER "rule_default" SET NOT NULL');

        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT fk_ebc714cf200620ba');
        $this->addSql('DROP INDEX idx_ebc714cf200620ba');
        $this->addSql('ALTER TABLE default_task_group_rule RENAME COLUMN key_default_task_group_id TO key_default_task_element_relation_id');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT FK_EBC714CF79CD4910 FOREIGN KEY (key_default_task_element_relation_id) REFERENCES default_task_element_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_EBC714CF79CD4910 ON default_task_group_rule (key_default_task_element_relation_id)');

        $this->addSql('ALTER TABLE default_task_relation ADD rule_default VARCHAR(15)');
        $this->addSql('UPDATE default_task_relation SET rule_default = \'enabled\'');
        $this->addSql('ALTER TABLE default_task_relation ALTER "rule_default" SET NOT NULL');

        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT fk_a4091010f313d71c');
        $this->addSql('DROP INDEX idx_a4091010f313d71c');
        $this->addSql('ALTER TABLE default_task_rule RENAME COLUMN key_default_task_id TO key_default_task_element_relation_id');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT FK_A409101079CD4910 FOREIGN KEY (key_default_task_element_relation_id) REFERENCES default_task_element_relation (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_A409101079CD4910 ON default_task_rule (key_default_task_element_relation_id)');
        $this->addSql('ALTER TABLE element DROP task_master');
        $this->addSql('ALTER TABLE element DROP task_group_master');

        $this->addSql('ALTER TABLE task_group ADD rule_default VARCHAR(15)');
        $this->addSql('UPDATE task_group SET rule_default = \'enabled\'');
        $this->addSql('ALTER TABLE task_group ALTER "rule_default" SET NOT NULL');

        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT fk_7391bc13fcc92519');
        $this->addSql('DROP INDEX idx_7391bc13fcc92519');
        $this->addSql('ALTER TABLE task_group_rule RENAME COLUMN key_task_group_id TO key_element_id');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT FK_7391BC13AFB2749B FOREIGN KEY (key_element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_7391BC13AFB2749B ON task_group_rule (key_element_id)');

        $this->addSql('ALTER TABLE task_relation ADD rule_default VARCHAR(15)');
        $this->addSql('UPDATE task_relation SET rule_default = \'enabled\'');
        $this->addSql('ALTER TABLE task_relation ALTER "rule_default" SET NOT NULL');

        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT fk_356b38b7ef6025b8');
        $this->addSql('DROP INDEX idx_356b38b7ef6025b8');
        $this->addSql('ALTER TABLE task_rule RENAME COLUMN key_task_id TO key_element_id');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT FK_356B38B7AFB2749B FOREIGN KEY (key_element_id) REFERENCES element (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_356B38B7AFB2749B ON task_rule (key_element_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE accessible_task_group DROP rule_default');
        $this->addSql('ALTER TABLE accessible_task_rule DROP CONSTRAINT FK_3D67A09EF5163172');
        $this->addSql('DROP INDEX IDX_3D67A09EF5163172');
        $this->addSql('ALTER TABLE accessible_task_rule RENAME COLUMN key_accessible_element_id TO key_accessible_task_id');
        $this->addSql('ALTER TABLE accessible_task_rule ADD CONSTRAINT fk_3d67a09e8a34c39d FOREIGN KEY (key_accessible_task_id) REFERENCES accessible_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_3d67a09e8a34c39d ON accessible_task_rule (key_accessible_task_id)');
        $this->addSql('ALTER TABLE accessible_task_relation DROP rule_default');
        $this->addSql('ALTER TABLE accessible_task_group_rule DROP CONSTRAINT FK_F6816A3CF5163172');
        $this->addSql('DROP INDEX IDX_F6816A3CF5163172');
        $this->addSql('ALTER TABLE accessible_task_group_rule RENAME COLUMN key_accessible_element_id TO key_accessible_task_group_id');
        $this->addSql('ALTER TABLE accessible_task_group_rule ADD CONSTRAINT fk_f6816a3c64f12dd FOREIGN KEY (key_accessible_task_group_id) REFERENCES accessible_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_f6816a3c64f12dd ON accessible_task_group_rule (key_accessible_task_group_id)');
        $this->addSql('ALTER TABLE default_task_group_rule DROP CONSTRAINT FK_EBC714CF79CD4910');
        $this->addSql('DROP INDEX IDX_EBC714CF79CD4910');
        $this->addSql('ALTER TABLE default_task_group_rule RENAME COLUMN key_default_task_element_relation_id TO key_default_task_group_id');
        $this->addSql('ALTER TABLE default_task_group_rule ADD CONSTRAINT fk_ebc714cf200620ba FOREIGN KEY (key_default_task_group_id) REFERENCES default_task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_ebc714cf200620ba ON default_task_group_rule (key_default_task_group_id)');
        $this->addSql('ALTER TABLE default_task_group DROP rule_default');
        $this->addSql('ALTER TABLE default_task_relation DROP rule_default');
        $this->addSql('ALTER TABLE default_task_rule DROP CONSTRAINT FK_A409101079CD4910');
        $this->addSql('DROP INDEX IDX_A409101079CD4910');
        $this->addSql('ALTER TABLE default_task_rule RENAME COLUMN key_default_task_element_relation_id TO key_default_task_id');
        $this->addSql('ALTER TABLE default_task_rule ADD CONSTRAINT fk_a4091010f313d71c FOREIGN KEY (key_default_task_id) REFERENCES default_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_a4091010f313d71c ON default_task_rule (key_default_task_id)');
        $this->addSql('ALTER TABLE task_rule DROP CONSTRAINT FK_356B38B7AFB2749B');
        $this->addSql('DROP INDEX IDX_356B38B7AFB2749B');
        $this->addSql('ALTER TABLE task_rule RENAME COLUMN key_element_id TO key_task_id');
        $this->addSql('ALTER TABLE task_rule ADD CONSTRAINT fk_356b38b7ef6025b8 FOREIGN KEY (key_task_id) REFERENCES task (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_356b38b7ef6025b8 ON task_rule (key_task_id)');
        $this->addSql('ALTER TABLE accessible_element ADD task_master BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE accessible_element ADD task_group_master BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE task_relation DROP rule_default');
        $this->addSql('ALTER TABLE task_group_rule DROP CONSTRAINT FK_7391BC13AFB2749B');
        $this->addSql('DROP INDEX IDX_7391BC13AFB2749B');
        $this->addSql('ALTER TABLE task_group_rule RENAME COLUMN key_element_id TO key_task_group_id');
        $this->addSql('ALTER TABLE task_group_rule ADD CONSTRAINT fk_7391bc13fcc92519 FOREIGN KEY (key_task_group_id) REFERENCES task_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_7391bc13fcc92519 ON task_group_rule (key_task_group_id)');
        $this->addSql('ALTER TABLE element ADD task_master BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE element ADD task_group_master BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE task_group DROP rule_default');
        $this->addSql('ALTER TABLE default_task_element_relation ADD task_master BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE default_task_element_relation ADD task_group_master BOOLEAN NOT NULL');
    }
}
