<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250519080232 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE accessible_task ADD task_actions JSONB DEFAULT '[]' NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE default_task ADD task_actions JSONB DEFAULT '[]' NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE task ADD task_actions JSONB DEFAULT '[]' NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE public.task DROP task_actions
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.default_task DROP task_actions
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.accessible_task DROP task_actions
        SQL);
    }
}
