<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241015085521 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DELETE FROM accessible_additional_information WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_element WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_interruption WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_note WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_note_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_task WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_task_group WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_task_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_termination WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM accessible_termination_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM additional_information WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM additional_service_config WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM booking WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM branch WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM connected_device WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM customer WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM customer_version WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_additional_information WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_element WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_interruption WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_interruption_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_material WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_material_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_note WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_note_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_task WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_task_element_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_task_group WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_task_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_termination WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_termination_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM default_unit_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM device_access WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM device_message WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM device_message_thread WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM element WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM equipment WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM equipment_config WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM equipment_version WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM faq WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM feedback WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM interruption WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM location WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM location_version WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM message WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM mobile_device WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM note WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM "order" WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM order_type_config WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM session WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM session_equipment WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM session_tour WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM "session_user" WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM staff WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM staff_config WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM staff_version WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM task WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM task_group WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM task_relation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM termination WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM tour WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM tour_data_config WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM tour_equipment WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM tour_staff WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM translation WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM "user" WHERE deleted_at IS NOT NULL');
        $this->addSql('DELETE FROM user_setting WHERE deleted_at IS NOT NULL');

        $this->addSql('ALTER TABLE "order" DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN "order".created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN "order".modified_at IS \'\'');
        $this->addSql('ALTER TABLE "session_user" DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN "session_user".created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN "session_user".modified_at IS \'\'');
        $this->addSql('ALTER TABLE "user" DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN "user".last_login IS \'\'');
        $this->addSql('COMMENT ON COLUMN "user".created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN "user".modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_additional_information DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_additional_information.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_additional_information.modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_element DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_element.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.options IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.values IS \'\'');
        $this->addSql('ALTER TABLE accessible_interruption DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_interruption.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_interruption.modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_note DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_note.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_note.modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_note_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_note_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_note_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_task DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_task.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_task.modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_task_group DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.rules IS \'\'');
        $this->addSql('ALTER TABLE accessible_task_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.rules IS \'\'');
        $this->addSql('ALTER TABLE accessible_termination DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_termination.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_termination.modified_at IS \'\'');
        $this->addSql('ALTER TABLE accessible_termination_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN accessible_termination_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN accessible_termination_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE additional_information DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN additional_information.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN additional_information.modified_at IS \'\'');
        $this->addSql('ALTER TABLE additional_service_config DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN additional_service_config.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN additional_service_config.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.sync_started_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.sync_ended_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.data_modified_from IS \'\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.data_modified_to IS \'\'');
        $this->addSql('ALTER TABLE booking DROP deleted_at');
        $this->addSql('ALTER TABLE booking ALTER label DROP DEFAULT');
        $this->addSql('COMMENT ON COLUMN booking.timestamp IS \'\'');
        $this->addSql('COMMENT ON COLUMN booking.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN booking.modified_at IS \'\'');
        $this->addSql('ALTER TABLE branch DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN branch.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN branch.modified_at IS \'\'');
        $this->addSql('ALTER TABLE connected_device DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN connected_device.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN connected_device.modified_at IS \'\'');
        $this->addSql('ALTER TABLE customer DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN customer.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN customer.modified_at IS \'\'');
        $this->addSql('ALTER TABLE customer_version DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN customer_version.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN customer_version.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_additional_information DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_additional_information.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_additional_information.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_element DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_element.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_element.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_element.options IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_element.values IS \'\'');
        $this->addSql('ALTER TABLE default_interruption DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_interruption.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_interruption.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_interruption_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_interruption_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_interruption_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_material DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_material.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_material.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_material_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_material_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_material_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_note DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_note.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_note.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_note_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_note_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_note_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_task DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_task.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_task.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_task_element_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_task_element_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_task_element_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_task_group DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_task_group.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_task_group.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_task_group.rules IS \'\'');
        $this->addSql('ALTER TABLE default_task_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_task_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_task_relation.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_task_relation.rules IS \'\'');
        $this->addSql('ALTER TABLE default_termination DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_termination.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_termination.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_termination_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_termination_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_termination_relation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE default_unit_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN default_unit_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN default_unit_relation.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN delivery_service.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN delivery_service.modified_at IS \'\'');
        $this->addSql('ALTER TABLE device_access DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN device_access.valid_until IS \'\'');
        $this->addSql('COMMENT ON COLUMN device_access.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN device_access.modified_at IS \'\'');
        $this->addSql('ALTER TABLE device_message DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN device_message.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN device_message.modified_at IS \'\'');
        $this->addSql('ALTER TABLE device_message_thread DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN device_message_thread.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN device_message_thread.modified_at IS \'\'');
        $this->addSql('ALTER TABLE element DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN element.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN element.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN element.options IS \'\'');
        $this->addSql('COMMENT ON COLUMN element.values IS \'\'');
        $this->addSql('ALTER TABLE equipment DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN equipment.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN equipment.modified_at IS \'\'');
        $this->addSql('ALTER TABLE equipment_config DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN equipment_config.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN equipment_config.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN equipment_position.recorded_at IS \'\'');
        $this->addSql('ALTER TABLE equipment_version DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN equipment_version.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN equipment_version.modified_at IS \'\'');
        $this->addSql('ALTER TABLE faq DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN faq.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN faq.modified_at IS \'\'');
        $this->addSql('ALTER TABLE feedback DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN feedback.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN feedback.modified_at IS \'\'');
        $this->addSql('ALTER TABLE interruption DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN interruption.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN interruption.modified_at IS \'\'');
        $this->addSql('ALTER TABLE location DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN location.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN location.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN location_format_config.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN location_format_config.modified_at IS \'\'');
        $this->addSql('ALTER TABLE location_version DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN location_version.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN location_version.modified_at IS \'\'');
        $this->addSql('ALTER TABLE mastertour_progress DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.date IS \'\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.coordinate IS \'\'');
        $this->addSql('ALTER TABLE mastertour_template DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN mastertour_template.waypoints IS \'\'');
        $this->addSql('COMMENT ON COLUMN mastertour_template.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN mastertour_template.modified_at IS \'\'');
        $this->addSql('ALTER TABLE message DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN message.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN message.modified_at IS \'\'');
        $this->addSql('ALTER TABLE mobile_device DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN mobile_device.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN mobile_device.modified_at IS \'\'');
        $this->addSql('ALTER TABLE note DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN note.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN note.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN order_document.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN order_document.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN order_document_text_block.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN order_document_text_block.modified_at IS \'\'');
        $this->addSql('ALTER TABLE order_type_config DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN order_type_config.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN order_type_config.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN point_of_interest.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN point_of_interest.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN sap_luxembourg_tour.date IS \'\'');
        $this->addSql('ALTER TABLE session DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN session.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN session.modified_at IS \'\'');
        $this->addSql('ALTER TABLE session_equipment DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN session_equipment.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN session_equipment.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN session_equipment.session_equipment_check IS \'\'');
        $this->addSql('ALTER TABLE session_tour DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN session_tour.start IS \'\'');
        $this->addSql('COMMENT ON COLUMN session_tour."end" IS \'\'');
        $this->addSql('COMMENT ON COLUMN session_tour.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN session_tour.modified_at IS \'\'');
        $this->addSql('ALTER TABLE staff DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN staff.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN staff.modified_at IS \'\'');
        $this->addSql('ALTER TABLE staff_config DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN staff_config.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN staff_config.modified_at IS \'\'');
        $this->addSql('ALTER TABLE staff_version DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN staff_version.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN staff_version.modified_at IS \'\'');
        $this->addSql('ALTER TABLE task DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN task.completed_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN task.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN task.modified_at IS \'\'');
        $this->addSql('ALTER TABLE task_group DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN task_group.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN task_group.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN task_group.rules IS \'\'');
        $this->addSql('ALTER TABLE task_relation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN task_relation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN task_relation.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN task_relation.rules IS \'\'');
        $this->addSql('COMMENT ON COLUMN tenant.created_at IS \'\'');
        $this->addSql('ALTER TABLE termination DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN termination.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN termination.modified_at IS \'\'');
        $this->addSql('ALTER TABLE tour DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN tour.last_tour_update IS \'\'');
        $this->addSql('COMMENT ON COLUMN tour.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tour.modified_at IS \'\'');
        $this->addSql('ALTER TABLE tour_data_config DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN tour_data_config.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tour_data_config.modified_at IS \'\'');
        $this->addSql('ALTER TABLE tour_equipment DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN tour_equipment.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tour_equipment.modified_at IS \'\'');
        $this->addSql('ALTER TABLE tour_staff DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN tour_staff.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tour_staff.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tracking.tracking_data IS \'\'');
        $this->addSql('COMMENT ON COLUMN tracking.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tracking.modified_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN tracking.date IS \'\'');
        $this->addSql('ALTER TABLE translation DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN translation.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN translation.modified_at IS \'\'');
        $this->addSql('ALTER TABLE user_setting DROP deleted_at');
        $this->addSql('COMMENT ON COLUMN user_setting.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN user_setting.modified_at IS \'\'');
        $this->addSql('ALTER TABLE messenger_messages ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE messenger_messages ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN messenger_messages.created_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.available_at IS \'\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.delivered_at IS \'\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE note ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN note.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN note.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_task ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_task.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_task_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE customer_version ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN customer_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN customer_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE element ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN element.options IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN element."values" IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN element.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN element.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE staff_version ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN staff_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN staff_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE task ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN task.completed_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE staff_config ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN staff_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN staff_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE user_setting ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN user_setting.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN user_setting.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_additional_information ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_additional_information.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_additional_information.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment_position.recorded_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_task_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_task_relation.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN default_task_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN point_of_interest.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN point_of_interest.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE interruption ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN interruption.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN interruption.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE branch ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN branch.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN branch.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE equipment ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN equipment.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN delivery_service.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN delivery_service.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_interruption ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_interruption.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_interruption.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_interruption_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_interruption_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_interruption_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE booking ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE booking ALTER label SET DEFAULT \'\'');
        $this->addSql('COMMENT ON COLUMN booking."timestamp" IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN booking.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN booking.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_document.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_document.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE tour_equipment ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN tour_equipment.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour_equipment.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_task_group ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_task_group.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN default_task_group.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_group.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_task_group ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task_group.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE "user" ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN "user".last_login IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "user".created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "user".modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mastertour_template ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN mastertour_template.waypoints IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_template.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_template.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE additional_service_config ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN additional_service_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN additional_service_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE session ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN session.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mobile_device ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN mobile_device.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mobile_device.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE messenger_messages ALTER id SET DEFAULT messenger_messages_id_seq');
        $this->addSql('ALTER TABLE messenger_messages ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN messenger_messages.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.available_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.delivered_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE tour_staff ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN tour_staff.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour_staff.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_termination_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_termination_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_termination_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_note ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_note.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_note.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN sap_luxembourg_tour.date IS \'(DC2Type:date_immutable)\'');
        $this->addSql('ALTER TABLE session_equipment ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN session_equipment.session_equipment_check IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN session_equipment.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_equipment.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mastertour_progress ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.date IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.coordinate IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN mastertour_progress.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_element ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_element.options IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element."values" IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_element.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_element ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_element.options IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN default_element."values" IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN default_element.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_element.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE connected_device ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN connected_device.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN connected_device.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE equipment_config ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN equipment_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE equipment_version ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN equipment_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN equipment_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_note_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_note_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_note_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE device_message ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN device_message.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_message.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_task ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_task.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_task.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE translation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN translation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN translation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE task_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN task_relation.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN task_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_material ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_material.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_material.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE device_message_thread ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN device_message_thread.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_message_thread.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.sync_started_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.sync_ended_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.data_modified_from IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN big_query_sync_status.data_modified_to IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tenant.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE session_tour ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN session_tour.start IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_tour."end" IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_tour.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN session_tour.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location_format_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location_format_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_document_text_block.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_document_text_block.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE tour_data_config ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN tour_data_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour_data_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_unit_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_unit_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_unit_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE order_type_config ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN order_type_config.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN order_type_config.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_note ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_note.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_note.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE tour ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN tour.last_tour_update IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tour.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_interruption ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_interruption.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_interruption.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_termination ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_termination.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_termination.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tracking.date IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tracking.tracking_data IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN tracking.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN tracking.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_additional_information ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_additional_information.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_additional_information.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE customer ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN customer.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN customer.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE device_access ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN device_access.valid_until IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_access.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN device_access.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE task_group ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN task_group.rules IS \'(DC2Type:json_document)\'');
        $this->addSql('COMMENT ON COLUMN task_group.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN task_group.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_note_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_note_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_note_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE message ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN message.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN message.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_task_element_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_task_element_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_task_element_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE feedback ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN feedback.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN feedback.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE "session_user" ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN "session_user".created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "session_user".modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE location_version ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN location_version.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location_version.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE additional_information ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN additional_information.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN additional_information.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_material_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_material_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_material_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE "order" ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN "order".created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN "order".modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE location ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN location.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN location.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE termination ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN termination.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN termination.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE accessible_termination ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN accessible_termination.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN accessible_termination.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE default_termination_relation ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN default_termination_relation.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN default_termination_relation.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE staff ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN staff.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN staff.modified_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE faq ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN faq.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN faq.modified_at IS \'(DC2Type:datetime_immutable)\'');
    }
}
