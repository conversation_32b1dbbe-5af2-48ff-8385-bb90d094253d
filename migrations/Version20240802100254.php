<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240802100254 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE device_message_thread ADD first_message_excerpt VARCHAR(21) DEFAULT \'\' NOT NULL');
        $this->addSql('CREATE INDEX IDX_A04A207C8B8E8428DCD6CC49 ON device_message_thread (created_at, branch_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_A04A207C8B8E8428DCD6CC49');
        $this->addSql('ALTER TABLE device_message_thread DROP first_message_excerpt');
    }
}
