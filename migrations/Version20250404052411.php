<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250404052411 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE public.document (document_type VARCHAR(100) NOT NULL, delivery_services JSONB DEFAULT '[]' NOT NULL, text_blocks JSONB DEFAULT '[]' NOT NULL, order_external_id VARCHAR(40) DEFAULT NULL, tour_external_id VARCHAR(40) DEFAULT NULL, document_name VARCHAR(255) DEFAULT NULL, document_path TEXT DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by_id UUID DEFAULT NULL, modified_by_id UUID DEFAULT NULL, tenant_id UUID NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511FB03A8386 ON public.document (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F99049ECE ON public.document (modified_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F9033212A ON public.document (tenant_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F7065677C ON public.document (modified_at)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511F84C7A836 ON public.document (order_external_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1A85511FFF238573 ON public.document (tour_external_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD CONSTRAINT FK_1A85511FB03A8386 FOREIGN KEY (created_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD CONSTRAINT FK_1A85511F99049ECE FOREIGN KEY (modified_by_id) REFERENCES public."user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document ADD CONSTRAINT FK_1A85511F9033212A FOREIGN KEY (tenant_id) REFERENCES public.tenant (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C4F8F2FC9E8BAF78 ON "order" (order_ext_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP INDEX public.IDX_C4F8F2FC9E8BAF78
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document DROP CONSTRAINT FK_1A85511FB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document DROP CONSTRAINT FK_1A85511F99049ECE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE public.document DROP CONSTRAINT FK_1A85511F9033212A
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE public.document
        SQL);
    }
}
