<?php

declare(strict_types=1);

namespace App;

use App\Infrastructure\Framework\Database\JsonOdm\JsonDocumentType;
use App\Infrastructure\Framework\Validator\AutoCascadeValidationLoader;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Types\Type;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;

class Kernel extends BaseKernel implements CompilerPassInterface
{
    use MicroKernelTrait;

    /**
     * @throws Exception
     */
    public function boot(): void
    {
        parent::boot();

        $this->registerOdmSerializer();
    }

    protected function prepareContainer(ContainerBuilder $container): void
    {
        $this->configureRabbitMqShards($container);

        parent::prepareContainer($container);
    }

    public function process(ContainerBuilder $container): void
    {
        $this->registerValidatorLoader($container);
    }

    private function registerOdmSerializer(): void
    {
        if (!Type::hasType('json_document')) {
            Type::addType('json_document', JsonDocumentType::class);
        }

        assert(null !== $this->container);
        $serializer = $this->container->get('database.odm_serializer');

        /** @var JsonDocumentType $type */
        $type = Type::getType('json_document');
        $type->setSerializer($serializer);
    }

    private function registerValidatorLoader(ContainerBuilder $container): void
    {
        $validatorBuilder = $container->getDefinition('validator.builder');
        $validatorBuilder->addMethodCall('addLoader', [new Reference(AutoCascadeValidationLoader::class)]);
    }

    private function configureRabbitMqShards(ContainerBuilder $container): void
    {
        $shards = $_ENV['DEVICE_MESSAGES_NUMBER_OF_SHARDS'];

        $deviceQueues = [];

        for ($i = 0; $i < $shards; ++$i) {
            $deviceQueues['device-shard-'.$i] = [
                'binding_keys' => ['shard-'.$i],
                'arguments' => [
                    'x-queue-type' => 'quorum',
                    'x-quorum-initial-group-size' => '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%',
                ],
            ];
        }

        $container->prependExtensionConfig(
            'framework',
            [
                'messenger' => [
                    'transports' => [
                        'device' => [
                            'options' => [
                                'queues' => $deviceQueues,
                            ],
                        ],
                    ],
                ],
            ]
        );
    }
}
