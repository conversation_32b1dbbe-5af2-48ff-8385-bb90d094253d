<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Types\ElementType;
use App\Domain\Entity\Enum\Types\TaskGroupType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTaskGroups;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\SessionEquipmentCheck;
use App\Domain\Repository\SessionEquipmentRepository;
use App\Domain\Services\Domain;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: SessionEquipmentRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class SessionEquipment implements EntityInterface, HasTaskGroups, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\ManyToOne(inversedBy: 'sessionEquipments')]
    #[ORM\JoinColumn(nullable: false)]
    private Session $session;

    #[ORM\ManyToOne(inversedBy: 'sessionEquipments')]
    #[ORM\JoinColumn(nullable: false)]
    private Equipment $equipment;

    #[ORM\Column]
    private \DateTimeImmutable $start;

    // end is a reserved keyword in PostgreSQL
    #[ORM\Column(name: '"end"', nullable: true)]
    private ?\DateTimeImmutable $end = null;

    /**
     * @var Collection<int, TaskGroup>
     */
    #[ORM\OneToMany(targetEntity: TaskGroup::class, mappedBy: 'sessionEquipment', cascade: ['persist'])]
    #[ORM\OrderBy(['sequenceNumber' => 'ASC'])]
    private Collection $taskGroups;

    #[ORM\Column(type: 'lazy_json_document', nullable: true, options: ['default' => null, 'jsonb' => true])]
    private ?SessionEquipmentCheck $sessionEquipmentCheck = null;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->taskGroups = new ArrayCollection();
    }

    public function getSession(): Session
    {
        return $this->session;
    }

    public function setSession(Session $session): self
    {
        $this->session = $session;

        return $this;
    }

    public function getEquipment(): Equipment
    {
        return $this->equipment;
    }

    public function setEquipment(Equipment $equipment): self
    {
        $this->equipment = $equipment;

        return $this;
    }

    public function getStart(): ?\DateTimeImmutable
    {
        return $this->start;
    }

    public function setStart(\DateTimeInterface $start): self
    {
        $this->start = \DateTimeImmutable::createFromInterface($start);

        return $this;
    }

    public function getEnd(): ?\DateTimeInterface
    {
        return $this->end;
    }

    public function setEnd(?\DateTimeInterface $end): self
    {
        $this->end = null !== $end ? \DateTimeImmutable::createFromInterface($end) : null;

        return $this;
    }

    /**
     * @return Collection<int, TaskGroup>
     */
    public function getTaskGroups(): Collection
    {
        return $this->taskGroups;
    }

    public function addTaskGroup(TaskGroup $taskGroup): static
    {
        if (!$this->taskGroups->contains($taskGroup)) {
            $this->taskGroups->add($taskGroup);
            $taskGroup->setSessionEquipment($this);
        }

        return $this;
    }

    public function getSessionEquipmentCheck(): ?SessionEquipmentCheck
    {
        return $this->sessionEquipmentCheck;
    }

    public function updateSessionEquipmentCheck(): void
    {
        $countNotAnswered = 0;
        $countPositive = 0;
        $countNegative = 0;
        $checkedTasks = [];

        foreach ($this->getTaskGroups() as $taskGroup) {
            if (TaskGroupType::VEHICLECHECK !== $taskGroup->getType()) {
                continue;
            }
            foreach ($taskGroup->getTaskRelations() as $taskRelation) {
                if (in_array($taskRelation->getTask()->getId(), $checkedTasks)) {
                    continue;
                }
                $checkedTasks[] = $taskRelation->getTask()->getId();
                foreach ($taskRelation->getTask()->getElements() as $element) {
                    if (ElementType::BOOLEAN === $element->type) {
                        match ($element->getValue()) {
                            null => ++$countNotAnswered,
                            '1' => ++$countPositive,
                            '0' => ++$countNegative,
                            default => null,
                        };
                    }

                    if (ElementType::PHOTO === $element->type && count($element->values) > 0) {
                        ++$countNegative;
                    }
                }
            }
        }
        $this->sessionEquipmentCheck = new SessionEquipmentCheck(
            countNegative: $countNegative,
            countPositive: $countPositive,
            countNotAnswered: $countNotAnswered,
        );
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);

        foreach ($this->getTaskGroups() as $taskGroup) {
            $taskGroup->delete();
        }
    }
}
