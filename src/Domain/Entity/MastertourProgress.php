<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\Coordinate;
use App\Domain\Entity\ValueObject\EquipmentAction;
use App\Domain\Repository\MastertourProgressRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: MastertourProgressRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class MastertourProgress implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    /**
     * @var EquipmentAction[]
     */
    #[ORM\Column(type: 'json_document', options: ['default' => '[]', 'jsonb' => true])]
    private array $equipmentActions = [];

    #[ORM\Column(type: 'lazy_json_document', nullable: false, options: ['jsonb' => true])]
    private Coordinate $coordinate;

    public function __construct(
        #[ORM\Column(length: 255, nullable: false)]
        private readonly string $mastertourTemplateExternalId,

        #[ORM\Column(length: 36, nullable: false)]
        private readonly string $waypointId,

        #[ORM\Column]
        private readonly \DateTimeImmutable $date,
    ) {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getMastertourTemplateExternalId(): string
    {
        return $this->mastertourTemplateExternalId;
    }

    public function getWaypointId(): string
    {
        return $this->waypointId;
    }

    public function getDate(): \DateTimeImmutable
    {
        return $this->date;
    }

    /**
     * @return EquipmentAction[]
     */
    public function getEquipmentActions(): array
    {
        return $this->equipmentActions;
    }

    public function addEquipmentAction(string $equipmentId, \DateTimeImmutable $timestamp): self
    {
        $this->equipmentActions[] = new EquipmentAction(
            equipmentId: $equipmentId,
            timestamp: $timestamp,
        );

        return $this;
    }

    public function getCoordinate(): Coordinate
    {
        return $this->coordinate;
    }

    public function setCoordinate(float $longitude, float $latitude): self
    {
        $this->coordinate = new Coordinate(
            longitude: $longitude,
            latitude: $latitude
        );

        return $this;
    }
}
