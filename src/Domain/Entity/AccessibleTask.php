<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Interfaces\TaskTemplate;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TaskTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\Element;
use App\Domain\Repository\AccessibleTaskRepository;
use App\Domain\Services\Domain;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: AccessibleTaskRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class AccessibleTask implements EntityInterface, TaskTemplate, HasTenant
{
    use CommonTrait;
    use TenantTrait;
    use TaskTrait;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $externalId = null;

    #[ORM\Column(length: 255, nullable: false)]
    private string $name;

    #[ORM\Column(length: 150, nullable: false)]
    private string $type;

    #[ORM\Column(nullable: false, options: ['default' => false])]
    private bool $activatedBySapData = false;

    /**
     * @var Collection<int, AccessibleTaskRelation>
     */
    #[ORM\OneToMany(targetEntity: AccessibleTaskRelation::class, mappedBy: 'accessibleTask')]
    private Collection $accessibleTaskRelations;

    #[ORM\Column]
    private bool $repeatable = false;

    #[ORM\Column(nullable: false, options: ['default' => false])]
    private bool $sapAction = false;

    /**
     * @var LazyJsonArray<Element>
     */
    #[ORM\Column(type: 'lazy_json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private LazyJsonArray $elementItems;

    /**
     * @var ValueObject\TaskAction[]
     */
    #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private array $taskActions = [];

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->accessibleTaskRelations = new ArrayCollection();
        $this->elementItems = new LazyJsonArray([]);
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function isActivatedBySapData(): bool
    {
        return $this->activatedBySapData;
    }

    public function setActivatedBySapData(bool $activatedBySapData): self
    {
        $this->activatedBySapData = $activatedBySapData;

        return $this;
    }

    /**
     * @return Collection<int, AccessibleTaskRelation>
     */
    public function getAccessibleTaskRelations(): Collection
    {
        return $this->accessibleTaskRelations;
    }

    public function addAccessibleTaskRelation(AccessibleTaskRelation $accessibleTaskRelation): self
    {
        if (!$this->accessibleTaskRelations->contains($accessibleTaskRelation)) {
            $this->accessibleTaskRelations->add($accessibleTaskRelation);
            $accessibleTaskRelation->setAccessibleTask($this);
        }

        return $this;
    }

    /**
     * @return iterable<Element>
     */
    public function getElements(): iterable
    {
        return $this->elementItems;
    }

    public function addElement(Element $element): self
    {
        foreach ($this->elementItems as $existingElement) {
            if ($element->id === $existingElement->id) {
                return $this;
            }
        }

        $this->elementItems = $this->elementItems
            ->append($element)
            ->sort(static fn (Element $a, Element $b): int => $a->sequenceNumber <=> $b->sequenceNumber);

        return $this;
    }

    /**
     * @param Element[] $elements
     */
    public function replaceElements(array $elements): self
    {
        $this->elementItems = new LazyJsonArray($elements);

        return $this;
    }

    /**
     * @return ValueObject\TaskAction[]
     */
    public function getTaskActions(): array
    {
        return $this->taskActions;
    }

    /**
     * @param ValueObject\TaskAction[] $taskActions
     */
    public function setTaskActions(array $taskActions): self
    {
        $this->taskActions = $taskActions;

        return $this;
    }

    public function createBasicTask(): Task
    {
        return new Task()
            ->setName($this->getName())
            ->setExternalId($this->getExternalId())
            ->setType($this->getType())
            ->setSapAction($this->isSapAction())
            ->setTaskActions($this->getTaskActions())
        ;
    }

    public function isRepeatable(): bool
    {
        return $this->repeatable;
    }

    public function setRepeatable(bool $repeatable): static
    {
        $this->repeatable = $repeatable;

        return $this;
    }

    public function isSapAction(): bool
    {
        return $this->sapAction;
    }

    public function setSapAction(bool $sapAction): self
    {
        $this->sapAction = $sapAction;

        return $this;
    }

    public function delete(): void
    {
        foreach ($this->getAccessibleTaskRelations() as $accessibleTaskRelation) {
            if (!$accessibleTaskRelation->hasDeleteFlag()) {
                return;
            }
        }

        Domain::events()->dispatchEntityDeleteEvent($this);
    }
}
