<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Annotations\StatusIn;
use App\Domain\Entity\Enum\Status\TemplateStatus;
use App\Domain\Entity\Enum\Types\InterruptionType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTaskGroupTemplates;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\StatusTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\AdditionalInformation;
use App\Domain\Repository\AccessibleInterruptionRepository;
use App\Domain\Services\Domain;
use App\Exception\BadRequestException;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;

/**
 * @implements HasTaskGroupTemplates<AccessibleTaskGroup>
 */
#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: AccessibleInterruptionRepository::class)]
#[StatusIn(TemplateStatus::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class AccessibleInterruption implements EntityInterface, HasTaskGroupTemplates, HasTenant
{
    use CommonTrait;
    use TenantTrait;
    /** @use StatusTrait<TemplateStatus> */
    use StatusTrait;

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    private string $description;

    #[ORM\Column(length: 20, nullable: false, enumType: InterruptionType::class)]
    private InterruptionType $type;

    #[ORM\ManyToOne(inversedBy: 'accessibleInterruptions')]
    #[ORM\JoinColumn(nullable: false)]
    private Tour $tour;

    /**
     * @var Collection<int, AccessibleTaskGroup>
     */
    #[ORM\OneToMany(targetEntity: AccessibleTaskGroup::class, mappedBy: 'accessibleInterruption', cascade: ['persist'])]
    #[ORM\OrderBy(['sequenceNumber' => 'ASC'])]
    private Collection $accessibleTaskGroups;

    #[ORM\Column]
    private int $sequenceNumber = 0;

    #[ORM\Column(length: 50, nullable: false)]
    private string $externalId;

    /**
     * @var LazyJsonArray<AdditionalInformation>
     */
    #[ORM\Column(type: 'lazy_json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private LazyJsonArray $additionalInformationItems;

    /**
     * @throws BadRequestException
     */
    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->accessibleTaskGroups = new ArrayCollection();
        $this->setStatus(TemplateStatus::ACTIVE);
        $this->additionalInformationItems = new LazyJsonArray([]);
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getType(): InterruptionType
    {
        return $this->type;
    }

    public function setType(InterruptionType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getTour(): Tour
    {
        return $this->tour;
    }

    public function setTour(Tour $tour): self
    {
        $this->tour = $tour;

        return $this;
    }

    /**
     * @return Collection<int, AccessibleTaskGroup>
     */
    public function getAccessibleTaskGroups(): Collection
    {
        return $this->accessibleTaskGroups;
    }

    public function addAccessibleTaskGroup(AccessibleTaskGroup $accessibleTaskGroup): self
    {
        if (!$this->accessibleTaskGroups->contains($accessibleTaskGroup)) {
            $this->accessibleTaskGroups->add($accessibleTaskGroup);
            $accessibleTaskGroup->setAccessibleInterruption($this);
        }

        return $this;
    }

    public function removeAccessibleTaskGroup(AccessibleTaskGroup $accessibleTaskGroup): self
    {
        // set the owning side to null (unless already changed)
        if (
            $this->accessibleTaskGroups->removeElement($accessibleTaskGroup)
            && $accessibleTaskGroup->getAccessibleInterruption() === $this
        ) {
            $accessibleTaskGroup->setAccessibleInterruption(null);
        }

        return $this;
    }

    /**
     * @return iterable<AdditionalInformation>
     */
    public function getAdditionalInformation(): iterable
    {
        return $this->additionalInformationItems;
    }

    public function addAdditionalInformation(AdditionalInformation $additionalInformation): self
    {
        $this->additionalInformationItems = $this->additionalInformationItems
            ->append($additionalInformation)
            ->sort(static fn (AdditionalInformation $a, AdditionalInformation $b): int => $a->sequence <=> $b->sequence);

        return $this;
    }

    public function getTaskGroupTemplates(): Collection
    {
        return $this->getAccessibleTaskGroups();
    }

    public function createInterruption(): Interruption
    {
        $interruption = new Interruption()
            ->setType($this->getType())
            ->setTour($this->getTour())
            ->setExternalId($this->getExternalId())
            ->setAccessibleInterruptionId($this->getId())
            ->setDescription($this->getDescription())
        ;

        foreach ($this->getAdditionalInformation() as $additionalInformation) {
            $interruption->addAdditionalInformation($additionalInformation);
        }

        return $interruption;
    }

    public function getSequenceNumber(): int
    {
        return $this->sequenceNumber;
    }

    public function setSequenceNumber(int $sequenceNumber): static
    {
        $this->sequenceNumber = $sequenceNumber;

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);

        foreach ($this->getAccessibleTaskGroups() as $accessibleTaskGroup) {
            $accessibleTaskGroup->delete();
        }
    }
}
