<?php

declare(strict_types=1);

namespace App\Infrastructure\SapCommon\Dto\Output;

use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Domain\Entity\Enum\Status\TaskGroupStatus;
use App\Domain\Entity\Enum\Status\TaskStatus;
use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Entity\Enum\Types\ElementType;
use App\Domain\Entity\Interruption;
use App\Domain\Entity\Order;
use App\Domain\Entity\Session;
use App\Domain\Entity\SessionEquipment;
use App\Domain\Entity\SessionUser;
use App\Domain\Entity\TaskGroup;
use App\Domain\Entity\Termination;
use App\Domain\Entity\Tour;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Uid\Uuid;

class ObjectConfirmationDto
{
    public string $idempotencyKey;

    /**
     * @param TaskGroupConfirmationDto[] $objectOrderLocations
     * @param NoteConfirmationDto[]|null $notes
     * @param string[]                   $staff
     * @param string[]                   $equipments
     */
    public function __construct(
        public ObjectType $objectType,

        public string $objectExternalId,

        public string $uuid,

        public array $staff,

        public array $equipments,

        public array $objectOrderLocations,

        public ?ObjectSubType $subType = null,

        public ?string $subTypeExternalId = null,

        public ?TerminationConfirmationDto $termination = null,

        public ?array $notes = null,

        ?string $idempotencyKey = null,
    ) {
        $this->idempotencyKey = $idempotencyKey ?? Uuid::v4()->toRfc4122();
    }

    public static function fromTour(
        Tour $tour,
        Session $session,
    ): self {
        $orderLocations = self::getOrderLocations($tour->getTaskGroups());
        $terminationDto = null;

        if (TourStatus::TERMINATED === $tour->getStatus()) {
            $termination = $tour->getTerminations()->first();

            if (!$termination instanceof Termination) {
                throw new \RuntimeException('Tour terminated without a termination');
            }

            $terminationDto = new TerminationConfirmationDto(
                terminationExternalId: $termination->getExternalId(),
                orderLocations: self::getOrderLocations($termination->getTaskGroups()),
            );
        }

        return new ObjectConfirmationDto(
            objectType: ObjectType::TOUR,
            objectExternalId: $tour->getExternalId(),
            uuid: $tour->getId(),
            staff: self::getSessionStaffExternalIds($session),
            equipments: self::getSessionEquipmentsExternalIds($session),
            objectOrderLocations: $orderLocations,
            subType: null,
            subTypeExternalId: null,
            termination: $terminationDto,
            notes: null,
        );
    }

    public static function fromOrder(
        Order $order,
        Session $session,
    ): self {
        $orderLocations = self::getOrderLocations($order->getTaskGroups());
        $terminationDto = null;

        if (OrderStatus::TERMINATED === $order->getStatus()) {
            $termination = $order->getTerminations()->first();

            if (!$termination instanceof Termination) {
                throw new \RuntimeException('Tour terminated without a termination');
            }

            $terminationDto = new TerminationConfirmationDto(
                terminationExternalId: $termination->getExternalId(),
                orderLocations: self::getOrderLocations($termination->getTaskGroups()),
            );
        }

        $noteDtos = [];

        foreach ($order->getNotes() as $note) {
            $noteDtos[] = new NoteConfirmationDto(
                externalId: $note->getExternalId(),
                orderLocations: self::getOrderLocations($note->getTaskGroups()),
            );
        }

        return new ObjectConfirmationDto(
            objectType: ObjectType::ORDER,
            objectExternalId: $order->getOrderExtId(),
            uuid: $order->getId(),
            staff: self::getSessionStaffExternalIds($session),
            equipments: self::getSessionEquipmentsExternalIds($session),
            objectOrderLocations: $orderLocations,
            subType: null,
            subTypeExternalId: null,
            termination: $terminationDto,
            notes: $noteDtos,
        );
    }

    public static function fromInterruptionOfTour(
        Interruption $interruption,
        Tour $tour,
        Session $session,
    ): self {
        return new ObjectConfirmationDto(
            objectType: ObjectType::TOUR,
            objectExternalId: $tour->getExternalId(),
            uuid: $interruption->getId(),
            staff: self::getSessionStaffExternalIds($session),
            equipments: self::getSessionEquipmentsExternalIds($session),
            objectOrderLocations: self::getOrderLocations($interruption->getTaskGroups()),
            subType: ObjectSubType::INTERRUPTION,
            subTypeExternalId: $interruption->getExternalId(),
            termination: null,
            notes: null,
        );
    }

    public static function fromStaffForTour(
        Tour $tour,
        SessionUser $sessionUser,
        Session $session,
    ): self {
        return new ObjectConfirmationDto(
            objectType: ObjectType::TOUR,
            objectExternalId: $tour->getExternalId(),
            uuid: $sessionUser->getStaff()->getId(),
            staff: self::getSessionStaffExternalIds($session),
            equipments: self::getSessionEquipmentsExternalIds($session),
            objectOrderLocations: self::getOrderLocations($sessionUser->getTaskGroups()),
            subType: ObjectSubType::STAFF,
            subTypeExternalId: $sessionUser->getStaff()->getExternalId(),
            termination: null,
            notes: null,
        );
    }

    public static function fromEquipmentInTour(
        Tour $tour,
        SessionEquipment $sessionEquipment,
        Session $session,
    ): self {
        return new ObjectConfirmationDto(
            objectType: ObjectType::TOUR,
            objectExternalId: $tour->getExternalId(),
            uuid: $sessionEquipment->getEquipment()->getId(),
            staff: self::getSessionStaffExternalIds($session),
            equipments: self::getSessionEquipmentsExternalIds($session),
            objectOrderLocations: self::getOrderLocations($sessionEquipment->getTaskGroups()),
            subType: ObjectSubType::EQUIPMENT,
            subTypeExternalId: $sessionEquipment->getEquipment()->getExternalId(),
            termination: null,
            notes: null,
        );
    }

    /**
     * @return array<string>
     */
    private static function getSessionEquipmentsExternalIds(Session $session): array
    {
        $ret = array_values($session->getSessionEquipments()
            ->filter(fn (SessionEquipment $sessionEquipment): bool => null === $sessionEquipment->getEnd())
            ->map(fn (SessionEquipment $sessionEquipment): string => $sessionEquipment->getEquipment()->getExternalId())
            ->toArray());
        sort($ret);

        return $ret;
    }

    /**
     * @return array<string>
     */
    private static function getSessionStaffExternalIds(Session $session): array
    {
        $ret = array_values($session->getSessionUsers()
            ->filter(fn (SessionUser $sessionUser): bool => null === $sessionUser->getEnd())
            ->map(fn (SessionUser $sessionUser): string => $sessionUser->getStaff()->getExternalId())
            ->toArray());
        sort($ret);

        return $ret;
    }

    /**
     * @param Collection<int, TaskGroup> $taskGroups
     *
     * @return TaskGroupConfirmationDto[]
     */
    private static function getOrderLocations(Collection $taskGroups): array
    {
        $orderLocations = [];
        $tasksByLocationType = [];
        $taskIdsByLocationType = [];

        foreach ($taskGroups as $taskGroup) {
            if (TaskGroupStatus::COMPLETED !== $taskGroup->getStatus()) {
                continue;
            }

            foreach ($taskGroup->getTaskRelations() as $taskRelation) {
                $task = $taskRelation->getTask();

                if (TaskStatus::COMPLETED !== $task->getStatus()) {
                    continue;
                }

                $elements = [];

                foreach ($task->getElements() as $element) {
                    $value = $element->getSourceSystemValues();

                    if (ElementType::WEIGHING_DATA === $element->type) {
                        $value = self::getSapWeightingData($value);
                    }

                    $elements[] = new ElementConfirmationDto(
                        elementType: $element->type->value,
                        elementValues: $value,
                        referenceType: $element->referenceType,
                    );
                }

                $orderLocationType = $taskGroup->getType()->value;
                $tgInterruption = $taskGroup->getInterruption();

                if (null !== $tgInterruption) {
                    $orderLocationType = $tgInterruption->getType()->value;
                }

                if (isset($taskIdsByLocationType[$orderLocationType][$task->getId()])) {
                    continue;
                }

                $tasksByLocationType[$orderLocationType][] = new TaskConfirmationDto(
                    taskType: $task->getType(),
                    elements: $elements,
                    taskName: $task->getName(),
                    taskExternalId: $task->getExternalId(),
                    latitude: $task->getLatitude(),
                    longitude: $task->getLongitude(),
                    timestamp: $task->getCompletedAt(),
                );
                $taskIdsByLocationType[$orderLocationType][$task->getId()] = true;
            }
        }

        foreach ($tasksByLocationType as $orderLocationType => $tasks) {
            $orderLocations[] = new TaskGroupConfirmationDto(
                orderLocationType: $orderLocationType,
                tasks: $tasks,
            );
        }

        return $orderLocations;
    }

    /**
     * @param string[] $value
     *
     * @return string[]
     */
    private static function getSapWeightingData(array $value): array
    {
        $encode = function (string $weightingJsonString): string {
            $json = json_encode(ScaleDefinition::fromSerializedValue($weightingJsonString));
            assert(false !== $json);

            return $json;
        };

        return array_map($encode, $value);
    }
}
