<?php

declare(strict_types=1);

namespace App\Infrastructure\SapCommon\Dto\Output;

use App\Domain\Entity\Session;
use App\Domain\Entity\SessionEquipment;
use App\Domain\Entity\SessionUser;
use Symfony\Component\Uid\Uuid;

class ObjectStatusDto
{
    public string $idempotencyKey;

    /**
     * @param string[] $staff
     * @param string[] $equipments
     */
    public function __construct(
        public ObjectType $objectType,

        public string $objectExternalId,

        public string $uuid,

        public array $staff,

        public array $equipments,

        public string $status,

        public \DateTimeImmutable $timestamp,

        public ?ObjectSubType $subType = null,

        public ?string $subTypeExternalId = null,

        public ?float $latitude = null,

        public ?float $longitude = null,

        public ?float $mileage = null,

        ?string $idempotencyKey = null,
    ) {
        $this->idempotencyKey = $idempotencyKey ?? Uuid::v4()->toRfc4122();
    }

    public static function createObjectStatusDto(
        Session $session,
        ObjectType $objectType,
        string $objectExternalId,
        ?ObjectSubType $subType,
        ?string $subTypeExternalId,
        string $uuid,
        string $status,
        ?float $latitude,
        ?float $longitude,
        ?float $mileage,
        \DateTimeImmutable $timestamp,
    ): self {
        return new self(
            objectType: $objectType,
            objectExternalId: $objectExternalId,
            uuid: $uuid,
            staff: self::getSessionStaffExternalIds($session),
            equipments: self::getSessionEquipmentsExternalIds($session),
            status: $status,
            timestamp: $timestamp,
            subType: $subType,
            subTypeExternalId: $subTypeExternalId,
            latitude: $latitude,
            longitude: $longitude,
            mileage: $mileage,
        );
    }

    /**
     * @return array<string>
     */
    private static function getSessionStaffExternalIds(Session $session): array
    {
        $ret = array_values(
            $session->getSessionUsers()
                ->filter(fn (SessionUser $sessionUser): bool => null === $sessionUser->getEnd())
                ->map(fn (SessionUser $sessionUser): string => $sessionUser->getStaff()->getExternalId())
                ->toArray()
        );
        sort($ret);

        return $ret;
    }

    /**
     * @return array<string>
     */
    private static function getSessionEquipmentsExternalIds(Session $session): array
    {
        $ret = array_values(
            $session->getSessionEquipments()
                ->filter(fn (SessionEquipment $sessionEquipment): bool => null === $sessionEquipment->getEnd())
                ->map(fn (SessionEquipment $sessionEquipment): string => $sessionEquipment->getEquipment()->getExternalId())
                ->toArray()
        );
        sort($ret);

        return $ret;
    }
}
