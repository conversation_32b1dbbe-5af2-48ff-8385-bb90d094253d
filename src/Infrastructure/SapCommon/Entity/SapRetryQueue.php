<?php

declare(strict_types=1);

namespace App\Infrastructure\SapCommon\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Infrastructure\SapCommon\Repository\SapRetryQueueRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: SapRetryQueueRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])]
class SapRetryQueue implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 36, nullable: false)]
    private string $tourId;

    #[ORM\Column(type: Types::FLOAT, nullable: false)]
    private float $microtime;

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    private string $contentObjectClass;

    #[ORM\Column(type: 'lazy_json_document', nullable: false, options: ['jsonb' => true])]
    private object $contentObject;

    #[ORM\Column(length: 20, enumType: SapRetryQueueStatus::class)]
    private SapRetryQueueStatus $status;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getTourId(): string
    {
        return $this->tourId;
    }

    public function setTourId(string $tourId): self
    {
        $this->tourId = $tourId;

        return $this;
    }

    public function getMicrotime(): float
    {
        return $this->microtime;
    }

    public function setMicrotime(float $microtime): self
    {
        $this->microtime = $microtime;

        return $this;
    }

    public function getContentObjectClass(): string
    {
        return $this->contentObjectClass;
    }

    public function setContentObjectClass(string $contentObjectClass): self
    {
        $this->contentObjectClass = $contentObjectClass;

        return $this;
    }

    public function getContentObject(): object
    {
        return $this->contentObject;
    }

    public function setContentObject(object $contentObject): self
    {
        $this->contentObject = $contentObject;

        return $this;
    }

    public function getStatus(): SapRetryQueueStatus
    {
        return $this->status;
    }

    public function setStatus(SapRetryQueueStatus $status): self
    {
        $this->status = $status;

        return $this;
    }
}
