<?php

declare(strict_types=1);

namespace App\Infrastructure\ObjectStorage;

use App\Domain\Context\TenantContext;
use App\Domain\Services\ObjectStorage\FileObject;
use App\Domain\Services\ObjectStorage\ObjectMetadata;
use App\Domain\Services\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Services\ObjectStorage\ObjectStorageException;
use Aws\Api\DateTimeResult;
use Aws\S3\Exception\S3Exception;
use Aws\S3\S3Client;
use Psr\Http\Message\StreamInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\HeaderUtils;

readonly class S3ObjectRepository implements ObjectRepositoryInterface
{
    public function __construct(
        #[Autowire('%app.storage.bucket_name%')]
        private string $bucketName,
        #[Autowire('@s3_client')]
        private S3Client $s3Client,
        private TenantContext $tenantContext,
        private LoggerInterface $logger,
    ) {
    }

    public function store(FileObject $object): void
    {
        $this->ensureBucketExists();

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'putObject',
                'path' => $object->identifier,
            ]);

            $this->s3Client->putObject([
                'Bucket' => $this->bucketName,
                'Key' => $this->setTenantIdentifier($object->identifier),
                'Body' => $object->content,
                'ContentType' => $object->mimeType,
                'Metadata' => $object->objectMetadata?->toArray() ?? [],
            ]);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }
    }

    public function getSignedUrl(
        string $identifier,
        bool $automaticTenant = true,
        int $validMinutes = 5,
    ): string {
        $this->ensureBucketExists();
        $identifier = $automaticTenant ? $this->setTenantIdentifier($identifier) : $identifier;

        // I guess the library adds automatically a slash at the beginning, so it becomes double slash if we keep it
        $identifier = ltrim($identifier, '/');

        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            basename($identifier),
        );

        $command = $this->s3Client->getCommand('GetObject', [
            'Bucket' => $this->bucketName,
            'Key' => $identifier,
            'ResponseContentDisposition' => $disposition,
        ]);

        $request = $this->s3Client->createPresignedRequest($command, sprintf('+%s minutes', $validMinutes));

        return (string) $request->getUri();
    }

    public function get(string $identifier, bool $automaticTenant = true): ?FileObject
    {
        $this->ensureBucketExists();
        $identifier = $automaticTenant ? $this->setTenantIdentifier($identifier) : $identifier;

        // I guess the library adds automatically a slash at the beginning, so it becomes double slash if we keep it
        $identifier = ltrim($identifier, '/');

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'getObject',
                'path' => $identifier,
            ]);

            /**
             * @var array{
             *     Metadata: array<string, string>,
             *     Body: ?StreamInterface,
             *     ContentType?: string,
             *     LastModified: DateTimeResult,
             * } $result
             */
            $result = $this->s3Client->getObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return null;
            }

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        $userIdentifier = null;
        $tenantIdentifier = null;

        $miscMetadata = [];
        foreach ($result['Metadata'] as $key => $value) {
            match ($key) {
                'user_identifier' => $userIdentifier = $value,
                'tenant_identifier' => $tenantIdentifier = $value,
                default => $miscMetadata[$key] = $value,
            };
        }

        return new FileObject(
            identifier: $identifier,
            content: $result['Body'] ? $result['Body']->getContents() : '',
            lastModified: $result['LastModified'],
            mimeType: $result['ContentType'] ?? 'application/octet-stream',
            objectMetadata: new ObjectMetadata(
                tenantIdentifier: $tenantIdentifier,
                userIdentifier: $userIdentifier,
                misc: $miscMetadata
            ),
        );
    }

    public function exists(string $identifier, bool $automaticTenant = true): bool
    {
        $this->ensureBucketExists();
        $identifier = $automaticTenant ? $this->setTenantIdentifier($identifier) : $identifier;

        // I guess the library adds automatically a slash at the beginning, so it becomes double slash if we keep it
        $identifier = ltrim($identifier, '/');

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'headObject',
                'path' => $identifier,
            ]);

            $this->s3Client->headObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return false;
            }

            $this->logger->critical(
                sprintf('Cannot check if object exists in S3, file: %s, exception: %s', $identifier, $e->getMessage()),
                [
                    'bucket' => $this->bucketName,
                    'key' => $identifier,
                    'exception' => $e,
                ],
            );

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            $this->logger->critical(
                sprintf('Cannot check if object exists in S3, file: %s, exception: %s', $identifier, $e->getMessage()),
                [
                    'bucket' => $this->bucketName,
                    'key' => $identifier,
                    'exception' => $e,
                ],
            );

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        return true;
    }

    private function setTenantIdentifier(string $identifier): string
    {
        $tenantIdentifier = $this->tenantContext->getTenant()->value;

        if (!str_starts_with($identifier, $tenantIdentifier.'/')) {
            return $tenantIdentifier.'/'.$identifier;
        }

        return $identifier;
    }

    public function metadata(string $identifier): ?ObjectMetadata
    {
        $this->ensureBucketExists();

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'headObject',
                'path' => $identifier,
            ]);

            /**
             * @var array{
             *     Metadata: array<string, string>,
             *     ContentType?: string,
             *     LastModified: DateTimeResult,
             * } $result
             */
            $result = $this->s3Client->headObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return null;
            }

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        $userIdentifier = null;
        $tenantIdentifier = null;

        $miscMetadata = [];
        foreach ($result['Metadata'] as $key => $value) {
            match ($key) {
                'user_identifier' => $userIdentifier = $value,
                'tenant_identifier' => $tenantIdentifier = $value,
                default => $miscMetadata[$key] = $value,
            };
        }

        return new ObjectMetadata(
            tenantIdentifier: $tenantIdentifier,
            userIdentifier: $userIdentifier,
            misc: $miscMetadata
        );
    }

    public function delete(string $identifier): void
    {
        $this->ensureBucketExists();

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'deleteObject',
                'path' => $identifier,
            ]);

            $this->s3Client->deleteObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return;
            }

            $this->logger->error(
                'Cannot delete object from S3',
                [
                    'bucket' => $this->bucketName,
                    'key' => $identifier,
                    'exception' => $e,
                ],
            );

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            $this->logger->error(
                'Cannot delete object from S3',
                [
                    'bucket' => $this->bucketName,
                    'key' => $identifier,
                    'exception' => $e,
                ],
            );
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }
    }

    /**
     * @throws ObjectStorageException
     */
    private function ensureBucketExists(): void
    {
        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'listBuckets',
            ]);

            $buckets = $this->s3Client->listBuckets();
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        assert(is_array($buckets['Buckets']));

        /** @var array{Name: string} $bucket */
        foreach ($buckets['Buckets'] as $bucket) {
            if ($bucket['Name'] === $this->bucketName) {
                return;
            }
        }

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'createBucket',
            ]);

            $this->s3Client->createBucket([
                'Bucket' => $this->bucketName,
            ]);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }
    }
}
