<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\HttpEndpoint;

use App\Domain\Repository\EquipmentRepository;
use App\Domain\VehicleInspection\Config\VehicleInspectionConfigRepository;
use App\Domain\VehicleInspection\Report\VehicleInspectionReport;
use App\Domain\VehicleInspection\Report\VehicleInspectionReportRepository;
use App\Infrastructure\VehicleInspectionReport\Dto\VirReportComponentDto;
use App\Infrastructure\VehicleInspectionReport\Germany\Resource\RepairReport;
use App\Infrastructure\VehicleInspectionReport\Service\VehicleReportService;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class RepairReportEndpoint
{
    public function __construct(
        private EquipmentRepository $equipmentRepository,
        private VehicleInspectionConfigRepository $configRepository,
        private VehicleReportService $reportService,
        private VehicleInspectionReportRepository $reportRepository,
    ) {
    }

    public function repairReport(RepairReport $repairReportDto): void
    {
        if (empty($repairReportDto->repairedComponents)) {
            return;
        }

        $equipment = $this->equipmentRepository->findOneBy(['externalId' => $repairReportDto->equipmentExternalId]);

        if (null === $equipment) {
            throw new NotFoundHttpException('Equipment not found');
        }

        $lastReport = $this->reportRepository->getLastReportByEquipmentId($equipment->getId());

        if (null === $lastReport) {
            throw new NotFoundHttpException('no report for equipment found');
        }

        $config = $this->configRepository->findForEquipment($equipment);

        if (null === $config) {
            throw new NotFoundHttpException('Vehicle inspection config not found');
        }

        $this->reportRepository->save(
            new VehicleInspectionReport(
                equipmentId: $equipment->getId(),
                equipmentComponentGroupReports: $this->reportService->createRepairReportComponentGroups(
                    config: $config,
                    lastReport: $lastReport,
                    repairedComponents: array_map(fn (VirReportComponentDto $dto): string => $dto->externalId, $repairReportDto->repairedComponents)
                ),
                submittedBy: 'vir-system',
            )
        );
    }
}
