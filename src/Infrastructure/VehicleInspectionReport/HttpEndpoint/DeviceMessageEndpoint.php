<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\HttpEndpoint;

use App\Domain\Application\DeviceMessageService;
use App\Domain\Repository\DeviceMessageThreadRepository;
use App\Domain\Repository\EquipmentRepository;
use App\Infrastructure\VehicleInspectionReport\Germany\Resource\DeviceMessage;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class DeviceMessageEndpoint
{
    public function __construct(
        private EquipmentRepository $equipmentRepository,
        private DeviceMessageThreadRepository $deviceMessageThreadRepository,
        private DeviceMessageService $deviceMessageService,
    ) {
    }

    public function sendDeviceMessage(DeviceMessage $deviceMessage): void
    {
        $equipment = $this->equipmentRepository->findByExternalId($deviceMessage->equipmentExternalId);

        if (null === $equipment) {
            throw new NotFoundHttpException('Equipment not found');
        }

        $thread = $this->deviceMessageThreadRepository->findThreadByRecipient($equipment);

        if (null === $thread) {
            $this->deviceMessageService->startThreadFromVIR(
                equipment: $equipment,
                from: $deviceMessage->from,
                message: $deviceMessage->message,
            );

            return;
        }

        $thread->replyFromVIR(
            from: $deviceMessage->from,
            text: $deviceMessage->message,
        );
    }
}
