<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Netherlands\HttpClient;

use App\Infrastructure\VehicleInspectionReport\Dto\DefectReportDto;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

readonly class NetherlandsHttpClient
{
    public function __construct(
        private HttpClientInterface $netherlandsVirClient,
        private NormalizerInterface $normalizer,
        private LoggerInterface $logger,
    ) {
    }

    public function sendReport(DefectReportDto $dto): void
    {
        try {
            $payload = $this->normalizer->normalize($dto);

            $this->logger->debug('Sending Netherlands VIR', ['payload' => $payload]);

            $response = $this->netherlandsVirClient->request(
                method: 'POST',
                url: 'VehicleInspection',
                options: [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Idempotency-Key' => $dto->idempotencyKey,
                    ],
                    'json' => $payload,
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: 'Netherlands VIR: Error while sending report.',
                    context: [
                        'responseCode' => $response->getStatusCode(),
                        'responseHeaders' => $response->getHeaders(false),
                        'responseBody' => $response->getContent(false),
                        'report' => $dto,
                    ],
                );

                throw new \Exception('Netherlands VIR Message sending failed with status code: '.$response->getStatusCode());
            }
        } catch (\Throwable $e) {
            $this->logger->critical(
                message: 'Netherlands VIR: Exception while sending report.',
                context: [
                    'exception' => $e,
                    'report' => $dto,
                ],
            );

            throw $e;
        }
    }
}
