<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Netherlands\MessageQueue;

use App\Infrastructure\VehicleInspectionReport\Netherlands\HttpClient\NetherlandsHttpClient;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class NetherlandsVehicleInspectionReportHandler
{
    public function __construct(
        private NetherlandsHttpClient $httpClient,
    ) {
    }

    /**
     * @throws \Throwable
     */
    public function __invoke(NetherlandsVehicleInspectionReportMessage $message): void
    {
        $this->httpClient->sendReport($message->defectReportDto);
    }
}
