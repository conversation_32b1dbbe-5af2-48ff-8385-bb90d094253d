<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Germany\Resource;

use App\Infrastructure\VehicleInspectionReport\HttpEndpoint\DeviceMessageEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'germany_vir',
    operations: [
        new Post(
            controller: [DeviceMessageEndpoint::class, 'sendDeviceMessage'],
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'Device Messages',
)]
class DeviceMessage
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    public string $equipmentExternalId;

    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    public string $from;

    #[Assert\NotBlank]
    #[Assert\Length(max: 500)]
    public string $message;
}
