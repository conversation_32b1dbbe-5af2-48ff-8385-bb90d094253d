<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Germany\MessageQueue;

use App\Infrastructure\VehicleInspectionReport\Germany\HttpClient\GermanHttpClient;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class GermanyVehicleInspectionReportHandler
{
    public function __construct(
        private GermanHttpClient $httpClient,
    ) {
    }

    /**
     * @throws \Throwable
     */
    public function __invoke(GermanyVehicleInspectionReportMessage $message): void
    {
        $this->httpClient->sendReport($message->defectReportDto);
    }
}
