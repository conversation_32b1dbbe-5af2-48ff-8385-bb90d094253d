<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Germany\MessageQueue;

use App\Domain\MessageQueue\ExternalSystemMessage;
use App\Infrastructure\VehicleInspectionReport\Dto\DefectReportDto;

readonly class GermanyVehicleInspectionReportMessage implements ExternalSystemMessage
{
    public function __construct(
        public DefectReportDto $defectReportDto,
    ) {
    }
}
