<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\EventListener;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Tenant;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener(priority: 9000)]
readonly class LoadTenantContextOnIngestRequest
{
    public function __construct(
        private TenantContext $tenantContext,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();
        $requestPath = $request->getPathInfo();

        if (!$event->isMainRequest()) {
            return;
        }

        if (str_starts_with($requestPath, '/ingest/germany-vir')) {
            $this->tenantContext->setTenant(Tenant::GERMANY);
        } elseif (str_starts_with($requestPath, '/ingest/netherlands-vir')) {
            $this->tenantContext->setTenant(Tenant::NETHERLANDS);
        } elseif (str_starts_with($requestPath, '/ingest/spain-vir')) {
            $this->tenantContext->setTenant(Tenant::SPAIN);
        }
    }
}
