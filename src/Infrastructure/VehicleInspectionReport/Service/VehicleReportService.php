<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Service;

use App\Domain\VehicleInspection\Config\EquipmentComponentConfig;
use App\Domain\VehicleInspection\Config\VehicleInspectionConfig;
use App\Domain\VehicleInspection\Report\EquipmentComponentGroupReport;
use App\Domain\VehicleInspection\Report\EquipmentComponentReport;
use App\Domain\VehicleInspection\Report\ReportValue;
use App\Domain\VehicleInspection\Report\VehicleInspectionReport;
use App\Domain\VehicleInspection\ReportValueType;

readonly class VehicleReportService
{
    /**
     * @param string[] $repairedComponents
     *
     * @return EquipmentComponentGroupReport[]
     */
    public function createRepairReportComponentGroups(
        VehicleInspectionConfig $config,
        VehicleInspectionReport $lastReport,
        array $repairedComponents,
    ): array {
        $groups = [];

        $oldWorkingStates = $this->getComponentStatusMapFromReport($lastReport);

        foreach ($config->getEquipmentComponentGroups() as $configEquipmentComponentGroup) {
            $components = [];

            foreach ($configEquipmentComponentGroup->components as $configEquipmentComponent) {
                if (ReportValueType::WORKING_STATE !== $configEquipmentComponent->valueType) {
                    continue;
                }
                $components[] = new EquipmentComponentReport(
                    title: $configEquipmentComponent->title,
                    label: $configEquipmentComponent->label,
                    critical: $configEquipmentComponent->critical,
                    externalId: $configEquipmentComponent->externalId,
                    value: $this->determineComponentState(
                        $configEquipmentComponent,
                        $repairedComponents,
                        $oldWorkingStates,
                    ),
                );
            }

            $groups[] = new EquipmentComponentGroupReport(
                title: $configEquipmentComponentGroup->title,
                components: $components,
            );
        }

        return $groups;
    }

    /**
     * @return array<string, ReportValue\WorkingState>
     */
    private function getComponentStatusMapFromReport(VehicleInspectionReport $report): array
    {
        $ret = [];
        foreach ($report->getEquipmentComponentGroupReports() as $componentGroupReport) {
            foreach ($componentGroupReport->components as $component) {
                if ($component->value instanceof ReportValue\WorkingStateValue) {
                    $ret[$component->externalId] = $component->value->value;
                } else {
                    throw new \RuntimeException('value-type not implemented yet');
                }
            }
        }

        return $ret;
    }

    /**
     * @param string[]                                $repairedComponents
     * @param array<string, ReportValue\WorkingState> $oldWorkingStates
     */
    private function determineComponentState(
        EquipmentComponentConfig $equipmentComponentConfig,
        array $repairedComponents,
        array $oldWorkingStates,
    ): ReportValue {
        if (in_array($equipmentComponentConfig->externalId, $repairedComponents)) {
            return new ReportValue\WorkingStateValue(ReportValue\WorkingState::WORKING, ReportValueType::WORKING_STATE);
        }
        if (isset($oldWorkingStates[$equipmentComponentConfig->externalId])) {
            return new ReportValue\WorkingStateValue($oldWorkingStates[$equipmentComponentConfig->externalId], ReportValueType::WORKING_STATE);
        }

        return new ReportValue\WorkingStateValue(ReportValue\WorkingState::WORKING, ReportValueType::WORKING_STATE);
    }
}
