<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Spain\MessageQueue;

use App\Infrastructure\VehicleInspectionReport\Spain\HttpClient\SpainHttpClient;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SpainVehicleInspectionReportHandler
{
    public function __construct(
        private SpainHttpClient $httpClient,
    ) {
    }

    /**
     * @throws \Throwable
     */
    public function __invoke(SpainVehicleInspectionReportMessage $message): void
    {
        $this->httpClient->sendReport($message->defectReportDto);
    }
}
