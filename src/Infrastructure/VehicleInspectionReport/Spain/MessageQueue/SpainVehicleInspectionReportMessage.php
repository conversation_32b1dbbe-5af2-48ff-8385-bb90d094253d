<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Spain\MessageQueue;

use App\Domain\MessageQueue\ExternalSystemMessage;
use App\Infrastructure\VehicleInspectionReport\Dto\DefectReportDto;

readonly class SpainVehicleInspectionReportMessage implements ExternalSystemMessage
{
    public function __construct(
        public DefectReportDto $defectReportDto,
    ) {
    }
}
