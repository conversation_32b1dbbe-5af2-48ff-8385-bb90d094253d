<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Spain\Resource;

use App\Infrastructure\VehicleInspectionReport\Dto\VirReportComponentDto;
use App\Infrastructure\VehicleInspectionReport\HttpEndpoint\RepairReportEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'spain_vir',
    operations: [
        new Post(
            controller: [RepairReportEndpoint::class, 'repairReport'],
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'Repair Report',
)]
class RepairReport
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    public string $equipmentExternalId;

    /**
     * @var VirReportComponentDto[]
     */
    #[Assert\All([
        new Assert\Type(type: VirReportComponentDto::class),
    ])]
    public array $repairedComponents;
}
