<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Spain\Resource;

use App\Infrastructure\VehicleInspectionReport\HttpEndpoint\DeviceMessageEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'spain_vir',
    operations: [
        new Post(
            controller: [DeviceMessageEndpoint::class, 'sendDeviceMessage'],
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'Device Messages',
)]
class DeviceMessage extends \App\Infrastructure\VehicleInspectionReport\Germany\Resource\DeviceMessage
{
}
