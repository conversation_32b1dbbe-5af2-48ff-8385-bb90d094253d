<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Spain\HttpClient;

use App\Infrastructure\VehicleInspectionReport\Dto\DefectReportDto;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

readonly class SpainHttpClient
{
    public function __construct(
        private HttpClientInterface $spainVirClient,
        private NormalizerInterface $normalizer,
        private LoggerInterface $logger,
    ) {
    }

    public function sendReport(DefectReportDto $dto): void
    {
        try {
            $payload = $this->normalizer->normalize($dto);

            $this->logger->debug('Sending Spain VIR', ['payload' => $payload]);

            $response = $this->spainVirClient->request(
                method: 'POST',
                url: 'report',
                options: [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Idempotency-Key' => $dto->idempotencyKey,
                    ],
                    'json' => $payload,
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: 'Spain VIR: Error while sending report.',
                    context: [
                        'responseCode' => $response->getStatusCode(),
                        'responseHeaders' => $response->getHeaders(false),
                        'responseBody' => $response->getContent(false),
                        'report' => $dto,
                    ],
                );

                throw new \Exception('Spain VIR Message sending failed with status code: '.$response->getStatusCode());
            }
        } catch (\Throwable $e) {
            $this->logger->critical(
                message: 'Spain VIR: Exception while sending report.',
                context: [
                    'exception' => $e,
                    'report' => $dto,
                ],
            );

            throw $e;
        }
    }
}
