<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Dto;

use App\Domain\Context\TenantContext;
use App\Domain\MessageQueue\MessageDispatcherInterface;
use App\Domain\Repository\EquipmentRepository;
use App\Domain\VehicleInspection\Report\ReportValue;
use App\Domain\VehicleInspection\Report\VehicleInspectionReport;
use App\Domain\VehicleInspection\Report\VehicleInspectionReportRepository;
use App\Domain\VehicleInspection\Report\VehicleInspectionReportSubmittedEvent;
use App\Infrastructure\VehicleInspectionReport\Germany\MessageQueue\GermanyVehicleInspectionReportMessage;
use App\Infrastructure\VehicleInspectionReport\Netherlands\MessageQueue\NetherlandsVehicleInspectionReportMessage;
use App\Infrastructure\VehicleInspectionReport\Spain\MessageQueue\SpainVehicleInspectionReportMessage;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener]
readonly class VehicleInspectionReportListener
{
    public function __construct(
        private MessageDispatcherInterface $messageDispatcher,
        private VehicleInspectionReportRepository $vehicleInspectionReportRepository,
        private EquipmentRepository $equipmentRepository,
        private TenantContext $tenantContext,
        private LoggerInterface $logger,
        #[Autowire('%env(GERMANY_VIR_ENABLED)%')]
        private string $isGermanyVirEnabled,
        #[Autowire('%env(NETHERLANDS_VIR_ENABLED)%')]
        private string $isNetherlandsVirEnabled,
        #[Autowire('%env(SPAIN_VIR_ENABLED)%')]
        private string $isSpainVirEnabled,
    ) {
    }

    public function __invoke(VehicleInspectionReportSubmittedEvent $event): void
    {
        if ($this->tenantContext->getTenant()->isGermany() && 'true' === $this->isGermanyVirEnabled) {
            $this->sendGermanyVehicleInspectionReport($event);

            return;
        }

        if ($this->tenantContext->getTenant()->isNetherlands() && 'true' === $this->isNetherlandsVirEnabled) {
            $this->sendNetherlandsVehicleInspectionReport($event);

            return;
        }

        if ($this->tenantContext->getTenant()->isSpain() && 'true' === $this->isSpainVirEnabled) {
            $this->sendSpainVehicleInspectionReport($event);

            return;
        }

        $this->logger->notice('Vehicle inspection report not enabled for tenant', [
            'tenant' => $this->tenantContext->getTenant()->getName(),
            'reportId' => $event->report->getId(),
        ]);
    }

    private function sendGermanyVehicleInspectionReport(VehicleInspectionReportSubmittedEvent $event): void
    {
        $message = $this->createReportDto($event->report);

        if (null !== $message) {
            $this->messageDispatcher->dispatch(new GermanyVehicleInspectionReportMessage(defectReportDto: $message));
        }
    }

    private function sendNetherlandsVehicleInspectionReport(VehicleInspectionReportSubmittedEvent $event): void
    {
        $report = $this->createReportDto($event->report);

        if (null !== $report) {
            $this->messageDispatcher->dispatch(new NetherlandsVehicleInspectionReportMessage(defectReportDto: $report));
        }
    }

    private function sendSpainVehicleInspectionReport(VehicleInspectionReportSubmittedEvent $event): void
    {
        $report = $this->createReportDto($event->report);

        if (null !== $report) {
            $this->messageDispatcher->dispatch(new SpainVehicleInspectionReportMessage(defectReportDto: $report));
        }
    }

    private function createReportDto(VehicleInspectionReport $report): ?DefectReportDto
    {
        $equipment = $this->equipmentRepository->find($report->getEquipmentId());

        if (null === $equipment) {
            throw new \RuntimeException('Equipment not found for ID: '.$report->getEquipmentId());
        }

        $penultimateReport = $this->vehicleInspectionReportRepository->getLastReportByEquipmentId(
            equipmentId: $report->getEquipmentId(),
            excludeId: $report->getId(),
        );

        $lastComponentState = $this->extractComponentState($report);
        $penultimateComponentState = null !== $penultimateReport ? $this->extractComponentState($penultimateReport) : null;
        $newlyBrokenComponents = [];

        foreach ($lastComponentState as $externalId => $componentState) {
            if (!$componentState instanceof ReportValue\WorkingStateValue) {
                throw new \RuntimeException('Unexpected component state value type: '.get_class($componentState));
            }

            if (ReportValue\WorkingState::BROKEN !== $componentState->value) {
                continue;
            }

            if (
                !isset($penultimateComponentState[$externalId])
                || !$penultimateComponentState[$externalId] instanceof ReportValue\WorkingStateValue
                || ReportValue\WorkingState::BROKEN !== $penultimateComponentState[$externalId]->value
            ) {
                $newlyBrokenComponents[] = new VirReportComponentDto(
                    externalId: $externalId,
                    datetime: $report->getCreatedAt(),
                );
            }
        }

        if ([] === $newlyBrokenComponents) {
            return null;
        }

        return new DefectReportDto(
            virId: $report->getId(),
            equipmentExternalId: $equipment->getExternalId(),
            reportedBy: $report->getSubmittedBy() ?? 'unknown',
            defectComponents: $newlyBrokenComponents,
        );
    }

    /**
     * @return array<string, ReportValue>
     */
    private function extractComponentState(VehicleInspectionReport $report): array
    {
        $componentState = [];

        foreach ($report->getEquipmentComponentGroupReports() as $equipmentComponentGroupReport) {
            foreach ($equipmentComponentGroupReport->components as $component) {
                $componentState[$component->externalId] = $component->value;
            }
        }

        return $componentState;
    }
}
