<?php

declare(strict_types=1);

namespace App\Infrastructure\VehicleInspectionReport\Dto;

use Symfony\Component\Uid\Uuid;

class DefectReportDto
{
    public string $idempotencyKey;

    /**
     * @param array<VirReportComponentDto> $defectComponents
     */
    public function __construct(
        public string $virId,
        public string $equipmentExternalId,
        public string $reportedBy,
        public array $defectComponents,
    ) {
        $this->idempotencyKey = Uuid::v4()->toRfc4122();
    }
}
