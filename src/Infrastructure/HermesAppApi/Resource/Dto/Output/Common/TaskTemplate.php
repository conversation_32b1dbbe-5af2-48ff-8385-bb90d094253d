<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common;

use App\Domain\Entity\AccessibleTaskRelation;
use App\Domain\Entity\DefaultTaskRelation;
use App\Domain\Entity\Enum\RuleEffect;
use App\Domain\Entity\Enum\RuleLogic;
use App\Domain\Entity\Enum\Status\TaskStatus;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\Element\Element;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\Element\ElementInterface;
use App\Infrastructure\HermesAppApi\Service\MapContext;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class TaskTemplate
{
    /**
     * @param array<Rule>             $rules
     * @param array<ElementInterface> $inputs
     * @param array<TaskAction>       $taskActions
     */
    public function __construct(
        #[Groups(['default'])]
        #[SerializedName('template_uuid')]
        public string $templateUuid,

        #[Groups(['default'])]
        public string $name,

        #[Groups(['default'])]
        #[SerializedName('is_required')]
        public bool $isRequired,

        #[Groups(['default'])]
        public array $rules,

        #[Groups(['default'])]
        #[SerializedName('rule_logic')]
        public RuleLogic $ruleLogic,

        #[Groups(['default'])]
        #[SerializedName('rule_effect')]
        public RuleEffect $ruleEffect,

        #[Groups(['default'])]
        #[SerializedName('rule_default')]
        public RuleEffect $ruleDefault,

        #[Groups(['default'])]
        public ?TaskStatus $status = null,

        #[Groups(['default'])]
        public array $inputs = [],

        #[Groups(['default'])]
        #[SerializedName('task_actions')]
        public array $taskActions = [],
    ) {
    }

    public static function fromAccessibleTaskRelationEntity(
        AccessibleTaskRelation $accessibleTaskRelation,
        MapContext $context,
    ): self {
        $accessibleTask = $accessibleTaskRelation->getAccessibleTask();

        return new self(
            templateUuid: $accessibleTask->getId(),
            name: $accessibleTask->getName(),
            isRequired: false === $accessibleTaskRelation->isOptional(),
            rules: array_map(Rule::fromValueObject(...), $accessibleTaskRelation->getRules()),
            ruleLogic: $accessibleTaskRelation->getRuleLogic(),
            ruleEffect: $accessibleTaskRelation->getRuleEffect(),
            ruleDefault: $accessibleTaskRelation->getRuleDefault(),
            inputs: array_map(
                static fn (\App\Domain\Entity\ValueObject\Element $element): ElementInterface
                    => Element::fromElementValueObject($element, $context, isTemplate: true),
                $accessibleTask->getElements()->getItems(),
            ),
            taskActions: array_map(
                static fn (\App\Domain\Entity\ValueObject\TaskAction $taskActionObject): TaskAction
                => TaskAction::fromValueObject($taskActionObject),
                $accessibleTask->getTaskActions(),
            ),
        );
    }

    public static function fromDefaultTaskRelationEntity(
        DefaultTaskRelation $defaultTaskRelation,
        MapContext $context,
    ): self {
        $defaultTask = $defaultTaskRelation->getDefaultTask();

        return new self(
            templateUuid: $defaultTask->getId(),
            name: $defaultTask->getName(),
            isRequired: false === $defaultTaskRelation->isOptional(),
            rules: array_map(Rule::fromValueObject(...), $defaultTaskRelation->getRules()),
            ruleLogic: $defaultTaskRelation->getRuleLogic(),
            ruleEffect: $defaultTaskRelation->getRuleEffect(),
            ruleDefault: $defaultTaskRelation->getRuleDefault(),
            inputs: array_map(
                static fn (\App\Domain\Entity\ValueObject\Element $elementObject): ElementInterface
                    => Element::fromElementValueObject($elementObject, $context, isTemplate: true),
                $defaultTask->getElements(),
            ),
            taskActions: array_map(
                static fn (\App\Domain\Entity\ValueObject\TaskAction $taskActionObject): TaskAction
                => TaskAction::fromValueObject($taskActionObject),
                $defaultTask->getTaskActions(),
            ),
        );
    }
}
