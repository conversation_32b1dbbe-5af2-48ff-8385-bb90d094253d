<?php

declare(strict_types=1);

namespace App\Infrastructure\SapGermany\Service;

use App\Domain\Entity\AccessibleInterruption;
use App\Domain\Entity\AccessibleTask;
use App\Domain\Entity\AccessibleTaskGroup;
use App\Domain\Entity\AccessibleTaskRelation;
use App\Domain\Entity\Branch;
use App\Domain\Entity\Customer;
use App\Domain\Entity\DefaultTaskGroup;
use App\Domain\Entity\Document;
use App\Domain\Entity\Enum\AdditionalInformationSource;
use App\Domain\Entity\Enum\Country;
use App\Domain\Entity\Enum\RuleEffect;
use App\Domain\Entity\Enum\RuleOperator;
use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Domain\Entity\Enum\Status\TourEquipmentStatus;
use App\Domain\Entity\Enum\Status\TourStaffStatus;
use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Entity\Enum\Types\ElementType;
use App\Domain\Entity\Enum\Types\InterruptionType;
use App\Domain\Entity\Enum\Types\LocationType;
use App\Domain\Entity\Enum\Types\TaskGroupType;
use App\Domain\Entity\Equipment;
use App\Domain\Entity\Location;
use App\Domain\Entity\SessionTour;
use App\Domain\Entity\Staff;
use App\Domain\Entity\Tour;
use App\Domain\Entity\TourDataConfig;
use App\Domain\Entity\TourStaff;
use App\Domain\Entity\Trait\RuleBuilderTrait;
use App\Domain\Entity\Util\EntityContainer;
use App\Domain\Entity\ValueObject\AdditionalInformation;
use App\Domain\Entity\ValueObject\DeliveryService;
use App\Domain\Entity\ValueObject\DocumentTextBlock;
use App\Domain\Entity\ValueObject\Element;
use App\Domain\Entity\ValueObject\ElementOption;
use App\Domain\Entity\ValueObject\InfoFile;
use App\Domain\Entity\ValueObject\Rule;
use App\Domain\Event\DomainEvents;
use App\Domain\Repository\DocumentRepository;
use App\Domain\Repository\MastertourTemplateRepository;
use App\Domain\Repository\TourDataConfigRepository;
use App\Domain\Repository\TourRepository;
use App\Domain\Services\Lock\LockException;
use App\Domain\Services\Lock\LockManagerInterface;
use App\Domain\Workflow\Workflow;
use App\Exception\BadRequestException;
use App\Exception\FinishedTourUpdateException;
use App\Infrastructure\SapGermany\Dto\Input\AdditionalInformationDto;
use App\Infrastructure\SapGermany\Dto\Input\AddressDto;
use App\Infrastructure\SapGermany\Dto\Input\CustomerDto;
use App\Infrastructure\SapGermany\Dto\Input\DeliveryServiceDto;
use App\Infrastructure\SapGermany\Dto\Input\DisposalSiteDto;
use App\Infrastructure\SapGermany\Dto\Input\DocumentTextBlockDto;
use App\Infrastructure\SapGermany\Dto\Input\EquipmentDto;
use App\Infrastructure\SapGermany\Dto\Input\InfoFileDto;
use App\Infrastructure\SapGermany\Dto\Input\StaffDto;
use App\Infrastructure\SapGermany\Dto\Input\TourDto;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;

readonly class TourService
{
    use RuleBuilderTrait;

    private const string LOCK_TOUR_UPSERT_PER_EXTID = 'tour-upsert-started';
    private const int TOUR_UPDATE_LOCK_DURATION = 60;

    public function __construct(
        private CustomerService $customerService,
        private BranchService $branchService,
        private EquipmentService $equipmentService,
        private StaffService $staffService,
        private AddressService $addressService,
        private TourRepository $tourRepository,
        private TourDataConfigRepository $tourDataRepository,
        private OrderService $orderService,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger,
        private Workflow $workflow,
        private DomainEvents $domainEvents,
        private LockManagerInterface $lockManager,
        private MastertourTemplateRepository $mastertourTemplateRepository,
        private DocumentRepository $documentRepository,
    ) {
    }

    public function upsert(TourDto $tourDto): Tour
    {
        $lock = null;
        try {
            $lock = $this->lockManager->acquireLockWithoutRetry(
                self::LOCK_TOUR_UPSERT_PER_EXTID,
                $tourDto->tourExtId,
                self::TOUR_UPDATE_LOCK_DURATION,
            );

            if (null === $lock) {
                $this->logger->warning(
                    'Tour upsert aborted - already in progress.',
                    ['tour' => $tourDto->tourExtId],
                );

                throw new ServiceUnavailableHttpException('Tour update already in progress');
            }
        } catch (LockException $e) {
            $this->logger->warning(
                'Allowing tour upsert even though the lock could not be acquired',
                ['tourExtId' => $tourDto->tourExtId, 'exception' => $e],
            );
        }

        try {
            return $this->handleTourUpsert($tourDto);
        } finally {
            $lock?->release();
        }
    }

    private function handleTourUpsert(TourDto $tourDto): Tour
    {
        $addressDtoIndexedByExternalId = [];

        foreach ($tourDto->addresses as $addressDto) {
            $addressDtoIndexedByExternalId[$addressDto->addressExtId] = $addressDto;
        }

        $tours = $this->tourRepository->findNonObsoleteByExternalId($tourDto->tourExtId);
        $tour = null;

        if (null !== $tours && [] !== $tours) {
            $tour = $tours[0];
        }

        $isNew = false;

        if (null === $tour) {
            $tour = new Tour()
                ->setExternalId($tourDto->tourExtId)
                ->setStatus(TourStatus::CREATED)
            ;
            $isNew = true;
        }

        if (!$tour->isMasterToursOverwritten()) {
            $this->upsertMastertours($tourDto, $tour);
        }

        $branch = $this->upsertBranch(
            branchExternalId: $tourDto->branchExternalId,
            addressExternalId: $tourDto->startAddressExtId,
            addressDtoIndexedByExternalId: $addressDtoIndexedByExternalId,
            tourId: $tour->getId(),
        );

        $staffs = $this->upsertStaffs($tourDto->staff, $branch);
        $customers = $this->upsertCustomers($tourDto->customers);
        $equipments = $this->upsertEquipments($tourDto->equipments, $branch);

        $this->upsertDisposalSiteLocation($tourDto->disposalSites, $addressDtoIndexedByExternalId, $tour->getId());
        $startLocation = $this->getLocation($addressDtoIndexedByExternalId, $tourDto->startAddressExtId, $tour->getId());
        $endLocation = $this->getLocation($addressDtoIndexedByExternalId, $tourDto->endAddressExtId, $tour->getId());

        if (!in_array($tour->getStatus(), [
            TourStatus::CREATED,
            TourStatus::STARTED,
            TourStatus::RETURNED])
        ) {
            throw new FinishedTourUpdateException('Cannot make changes to a tour that is not in CREATED, RETURNED or STARTED status.');
        }

        if (TourStatus::CREATED === $tour->getStatus()) {
            $tour
                ->setPlannedStartDate($tourDto->startTimestamp)
                ->setPlannedEndDate($tourDto->endTimestamp)
                ->setForcedOrder($tourDto->forcedOrder)
                ->setBranch($branch)
                ->setStartLocation($startLocation);
        }

        $tour
            ->setEndLocation($endLocation)
            ->setName($tourDto->name)
            ->setLastTourUpdate(new \DateTimeImmutable())
        ;

        $activeTourSessions = $tour->getSessionTours()
            ->filter(fn (SessionTour $sessionTour): bool => null === $sessionTour->getEnd());
        if (false === $isNew && $activeTourSessions->count() > 0) {
            $this->domainEvents->dispatchTourLastUpdateChanged($tour);
        }

        $this->setTourStaffs($tour, $staffs);
        $this->setTourEquipment($tour, $equipments);

        $tourConfig = $this->getTourDataConfig($tour, $tourDto);

        $tour->removeAdditionalInformationFromSource(AdditionalInformationSource::SAP);
        $tour->removeAdditionalInformationFromSource(AdditionalInformationSource::DOCUMENT);
        $tour->addAdditionalInformation(array_map(
            static fn (AdditionalInformationDto $additionalInformationDto): AdditionalInformation => $additionalInformationDto->toDatabaseValueObject(),
            $tourDto->additionalInformation,
        ));

        $this->setTourDocuments($tour, $tourDto);

        if ($isNew) {
            $this->createTourAccessibleTerminations($tour, $tourConfig);
            $this->createTourAccessibleInterruptions($tour, $tourConfig);
            $this->createTourTaskGroups($tour, $tourConfig);
            $this->createTourDisposalSiteInterruptions(
                addressDtoIndexedByExternalId: $addressDtoIndexedByExternalId,
                tour: $tour,
                tourDto: $tourDto,
                tourDataConfig: $tourConfig,
            );
            $tour->setTrackingProtection($tourConfig->getTrackingProtection());
            $tour->addAdditionalInformation($tourConfig->getAdditionalInformation());
        } elseif ($tour->getTourDataConfigId() !== $tourConfig->getId()) {
            $tour->markAccessibleTerminationsAsObsolete();
            $tour->markAccessibleInterruptionsAsObsolete();
            $tour->markTaskGroupsAsObsolete($this->workflow);
            $tour->removeAdditionalInformationFromSource(AdditionalInformationSource::CONFIG);

            $this->createTourAccessibleTerminations($tour, $tourConfig);
            $this->createTourAccessibleInterruptions($tour, $tourConfig);
            $this->createTourTaskGroups($tour, $tourConfig);
            $this->createTourDisposalSiteInterruptions(
                addressDtoIndexedByExternalId: $addressDtoIndexedByExternalId,
                tour: $tour,
                tourDto: $tourDto,
                tourDataConfig: $tourConfig,
            );
            $tour->addAdditionalInformation($tourConfig->getAdditionalInformation());
        } elseif ($tour->getDisposalSiteHash() !== $tourDto->createDisposalSiteHash()) {
            $tour->markDisposalSiteInterruptionsAsObsolete();

            $this->createTourDisposalSiteInterruptions(
                addressDtoIndexedByExternalId: $addressDtoIndexedByExternalId,
                tour: $tour,
                tourDto: $tourDto,
                tourDataConfig: $tourConfig,
            );
        }

        $country = Country::tryFrom(mb_strtolower($tourDto->country));

        if (null === $country) {
            throw new BadRequestException(sprintf('Country %s is not supported', $tourDto->country));
        }

        $this->handleTourOrders(
            tour: $tour,
            tourDto: $tourDto,
            customers: $customers,
            addressDtoIndexedByExternalId: $addressDtoIndexedByExternalId,
            country: $country,
        );

        $tour->loadNewInfoFiles(array_map(
            static fn (InfoFileDto $infoFileDto): InfoFile => $infoFileDto->toValueObject(),
            $tourDto->infoFiles,
        ));

        $tour->setTourDataConfigId($tourConfig->getId());
        $tour->setDisposalSiteHash($tourDto->createDisposalSiteHash());
        $this->tourRepository->save($tour);

        return $tour;
    }

    public function invalidate(string $tourExternalId): void
    {
        $tours = $this->tourRepository->findNonObsoleteByExternalId($tourExternalId);

        if (null === $tours) {
            throw new NotFoundHttpException(sprintf('Tour with external id %s not found', $tourExternalId));
        }

        foreach ($tours as $tour) {
            if (TourStatus::OBSOLETE === $tour->getStatus()) {
                continue;
            }

            $tour->abandon();
        }
    }

    /**
     * @param array<string, AddressDto> $addressDtoIndexedByExternalId
     */
    private function upsertBranch(
        string $branchExternalId,
        string $addressExternalId,
        array $addressDtoIndexedByExternalId,
        string $tourId,
    ): Branch {
        if (!isset($addressDtoIndexedByExternalId[$addressExternalId])) {
            $message = sprintf('Address with external id %s not found', $addressExternalId);

            throw new BadRequestException($message);
        }

        return $this->branchService->upsert(
            branchExternalId: $branchExternalId,
            addressDto: $addressDtoIndexedByExternalId[$addressExternalId],
            tourId: $tourId,
        );
    }

    /**
     * @param array<StaffDto> $staffDtos
     *
     * @return array<Staff>
     */
    private function upsertStaffs(array $staffDtos, Branch $branch): array
    {
        $staffs = [];

        foreach ($staffDtos as $staffDto) {
            $staffs[] = $this->staffService->upsert($staffDto, $branch);
        }

        return $staffs;
    }

    /**
     * @param CustomerDto[] $customerDtos
     *
     * @return array<string, Customer>
     */
    private function upsertCustomers(array $customerDtos): array
    {
        $customers = [];

        foreach ($customerDtos as $customerDto) {
            $customer = $this->customerService->upsert(
                externalId: $customerDto->customerExtId,
                name: $customerDto->customerName,
            );

            $customers[$customer->getCustomerExtId()] = $customer;
        }

        return $customers;
    }

    /**
     * @param EquipmentDto[] $equipmentDtos
     *
     * @return Equipment[]
     */
    private function upsertEquipments(array $equipmentDtos, Branch $branch): array
    {
        $equipments = [];

        foreach ($equipmentDtos as $equipmentDto) {
            $equipments[] = $this->equipmentService->upsert($equipmentDto, $branch);
        }

        return $equipments;
    }

    /**
     * @param DisposalSiteDto[]         $disposalSitesDtos
     * @param array<string, AddressDto> $addressDtoIndexedByExternalId
     */
    private function upsertDisposalSiteLocation(
        array $disposalSitesDtos,
        array $addressDtoIndexedByExternalId,
        string $tourId,
    ): void {
        foreach ($disposalSitesDtos as $disposalSiteDto) {
            if (!isset($addressDtoIndexedByExternalId[$disposalSiteDto->addressExtId])) {
                $message = sprintf('Address with external id %s not found', $disposalSiteDto->addressExtId);

                throw new BadRequestException($message);
            }

            $this->addressService->upsert(
                addressDto: $addressDtoIndexedByExternalId[$disposalSiteDto->addressExtId],
                locationType: LocationType::DISPOSALSITE,
                tourId: $tourId
            );
        }
    }

    /**
     * @param Staff[] $sapStaffs
     */
    private function setTourStaffs(Tour $tour, array $sapStaffs): void
    {
        $sapStaffUuids = array_map(static fn (Staff $staff): string => $staff->getId(), $sapStaffs);
        $existingTourStaffUuids = [];

        foreach ($tour->getTourStaff() as $tourStaff) {
            $staffUuid = $tourStaff->getStaff()->getId();

            if (
                !in_array($staffUuid, $sapStaffUuids, true)
                && TourStaffStatus::DISPATCHED === $tourStaff->getStatus()
                && $tourStaff->isDispatched()
            ) {
                $tour->removeTourStaff($tourStaff);
                $this->entityManager->remove($tourStaff);
                continue;
            }

            $tourStaff->setDispatched(true);
            $existingTourStaffUuids[] = $staffUuid;
        }

        foreach ($sapStaffs as $staff) {
            $staffUuid = $staff->getId();

            if (!in_array($staffUuid, $existingTourStaffUuids, true)) {
                $tourStaff = new TourStaff()
                    ->setStaff($staff)
                    ->setTour($tour)
                    ->setDispatched(true)
                ;

                $tour->addTourStaff($tourStaff);
            }
        }
    }

    /**
     * @param Equipment[] $sapEquipments
     */
    private function setTourEquipment(Tour $tour, array $sapEquipments): void
    {
        $sapEquipmentUuids = array_map(static fn (Equipment $equipment): string => $equipment->getId(), $sapEquipments);
        $existingTourEquipmentUuids = [];

        foreach ($tour->getTourEquipments() as $tourEquipment) {
            $equipmentUuid = $tourEquipment->getEquipment()->getId();

            if (
                !in_array($equipmentUuid, $sapEquipmentUuids, true)
                && TourEquipmentStatus::DISPATCHED === $tourEquipment->getStatus()
                && $tourEquipment->isDispatched()
            ) {
                $tour->removeTourEquipment($tourEquipment);
                continue;
            }

            $tourEquipment->setDispatched(true);
            $existingTourEquipmentUuids[] = $equipmentUuid;
        }

        foreach ($sapEquipments as $equipment) {
            $equipmentUuid = $equipment->getId();

            if (!in_array($equipmentUuid, $existingTourEquipmentUuids, true)) {
                $tour->addEquipment($equipment, isDispatched: true);
            }
        }
    }

    /**
     * @param array<string, AddressDto> $addressDtoIndexedByExternalId
     */
    private function getLocation(
        array $addressDtoIndexedByExternalId,
        string $locationExtId,
        string $tourId,
    ): Location {
        if (!isset($addressDtoIndexedByExternalId[$locationExtId])) {
            $message = sprintf('Address with external id %s not found', $locationExtId);
            $this->logger->critical($message);

            throw new BadRequestException($message);
        }

        return $this->addressService->upsert(
            addressDto: $addressDtoIndexedByExternalId[$locationExtId],
            locationType: LocationType::DEPOT,
            tourId: $tourId,
        );
    }

    private function getTourDataConfig(?Tour $tour, TourDto $tourDto): TourDataConfig
    {
        $tourConfig = $this->tourDataRepository->findForTour($tour);

        if (null === $tourConfig) {
            $this->logger->critical(
                'Could not find configuration for tour',
                ['tour' => $tourDto->tourExtId]
            );

            throw new BadRequestException('Could not find configuration for tour');
        }

        return $tourConfig;
    }

    private function createTourTaskGroups(Tour $tour, TourDataConfig $tourConfig): void
    {
        $createdEntities = new EntityContainer();

        foreach ($tourConfig->getDefaultTaskGroups() as $defaultTaskGroup) {
            if ($defaultTaskGroup->isOnlyForManualCreation()) {
                continue;
            }

            $taskGroup = $defaultTaskGroup->createFullTaskGroup($createdEntities);
            $taskGroup->setDefaultTaskGroupId($defaultTaskGroup->getId());

            $createdEntities->add($defaultTaskGroup->getId(), $taskGroup);

            $tour->addTaskGroup($taskGroup);
        }

        $this->mapRules(
            defaultTaskGroupCollection: $tourConfig->getDefaultTaskGroups()
                ->filter(fn (DefaultTaskGroup $defaultTaskGroup): bool => false === $defaultTaskGroup->isOnlyForManualCreation()),
            createdEntities: $createdEntities,
        );
    }

    /**
     * @param array<string, AddressDto> $addressDtoIndexedByExternalId
     */
    private function createTourDisposalSiteInterruptions(
        array $addressDtoIndexedByExternalId,
        Tour $tour,
        TourDto $tourDto,
        TourDataConfig $tourDataConfig,
    ): void {
        if ([] === $tourDto->disposalSites) {
            return;
        }

        $defaultTaskGroup = $tourDataConfig->getDefaultTaskGroups()
            ->filter(
                fn (DefaultTaskGroup $defaultTaskGroup): bool
                    => TaskGroupType::DISPOSAL_SITE_TEMPLATE === $defaultTaskGroup->getType()
            )
            ->first();

        if (!$defaultTaskGroup instanceof DefaultTaskGroup) {
            $this->logger->info(
                'No disposalsite-interruption-template set for tour',
                ['tour' => $tourDto->tourExtId]
            );

            return;
        }

        $taskGroupSequenceNumber = 0;

        $selectElement = new Element(
            referenceType: 'disposal-site-picker',
            type: ElementType::SELECT,
            required: true,
            sequenceNumber: 0,
            label: '{{disposal_site_picker}}',
        );

        $selectTask = new AccessibleTask()
            ->setName('{{disposal_site_choice}}')
            ->setType('disposal-site-picker')
            ->setActivatedBySapData(true)
        ;

        $selectTaskGroup = new AccessibleTaskGroup()
            ->setTitle('{{disposal_site}}')
            ->setSequenceNumber($taskGroupSequenceNumber++)
            ->setType(TaskGroupType::DISPOSALSITE)
            ->addAccessibleTaskRelation(
                new AccessibleTaskRelation()
                    ->setSequenceNumber(0)
                    ->setOptional(false)
                    ->setAccessibleTask($selectTask)
            );

        $accessibleInterruption = new AccessibleInterruption()
            ->setSequenceNumber(0)
            ->setDescription('{{disposal_site}}')
            ->setType(InterruptionType::DISPOSALSITE)
            ->setExternalId($tourDto->interruptionExternalId)
            ->addAccessibleTaskGroup($selectTaskGroup)
        ;

        $elementOptions = [];

        foreach ($tourDto->disposalSites as $disposalSiteDto) {
            $location = $this->getLocation(
                addressDtoIndexedByExternalId: $addressDtoIndexedByExternalId,
                locationExtId: $disposalSiteDto->addressExtId,
                tourId: $tour->getId(),
            );

            $elementOption = new ElementOption(name: $disposalSiteDto->name, sourceSystemId: $disposalSiteDto->disposalSiteExtId);
            $elementOptions[] = $elementOption;

            $createdEntities = new EntityContainer();
            $accessibleTaskGroup = $defaultTaskGroup
                ->createAccessibleTaskGroup($createdEntities, $disposalSiteDto->weigh)
                ->setSequenceNumber($taskGroupSequenceNumber++)
                ->setLocation($location)
                ->setType(TaskGroupType::DISPOSALSITE)
                ->setTitle($disposalSiteDto->name)
                ->setExternalId($disposalSiteDto->disposalSiteExtId)
                ->setRuleDefault(RuleEffect::ENABLED)
                ->setRuleEffect(RuleEffect::INVISIBLE)
                ->addRule(new Rule(
                    elementId: $selectElement->id,
                    operator: RuleOperator::NOT_EQUAL,
                    value: $elementOption->id,
                ))
            ;

            $this->setDisposalSiteWastePicker($disposalSiteDto, $accessibleTaskGroup);

            $this->mapAccessibleTaskRules(
                $tourDataConfig->getDefaultTaskGroups()
                    ->filter(
                        fn (DefaultTaskGroup $defaultTaskGroup): bool
                        => TaskGroupType::DISPOSAL_SITE_TEMPLATE === $defaultTaskGroup->getType()
                    ),
                $createdEntities,
                $disposalSiteDto->weigh,
            );
            $accessibleInterruption->addAccessibleTaskGroup($accessibleTaskGroup);
        }

        $selectElement = $selectElement->withOptions($elementOptions);
        if (1 === count($selectElement->options)) {
            $selectElement = $selectElement->withExpectedValues($selectElement->options[0]->getId());
        }

        $selectTask->addElement($selectElement);

        $tour->addAccessibleInterruption($accessibleInterruption);
    }

    private function setDisposalSiteWastePicker(DisposalSiteDto $disposalSiteDto, AccessibleTaskGroup $accessibleTaskGroup): void
    {
        $wastePickerTask = $accessibleTaskGroup->getFirstAccessibleTaskByType('wastePicker');

        if (null === $wastePickerTask) {
            return;
        }

        if (0 === count($disposalSiteDto->materialOptions)) {
            $accessibleTaskGroup->removeAccessibleTasksByType('wastePicker');

            return;
        }

        $wastePickerOptions = [];
        $sequence = 0;
        foreach ($disposalSiteDto->materialOptions as $materialOption) {
            $wastePickerOptions[] = new ElementOption(
                name: $materialOption->name,
                isFromExternalSource: true,
                sequenceNumber: $sequence += 10,
                id: null,
                sourceSystemId: $materialOption->sourceSystemId
            );
        }

        $elementItems = $wastePickerTask->getElements()->getItems();
        $counter = count($elementItems);
        for ($i = 0; $i < $counter; ++$i) {
            if ('wastePicker' === $elementItems[$i]->referenceType) {
                $changeElement = $elementItems[$i]->withOptions($wastePickerOptions);
                if (1 === count($changeElement->options)) {
                    $changeElement = $changeElement->withExpectedValues($changeElement->options[0]->id);
                }
                $elementItems[$i] = $changeElement;
            }
        }

        $wastePickerTask->replaceElements($elementItems);
    }

    private function createTourAccessibleTerminations(Tour $tour, TourDataConfig $tourConfig): void
    {
        foreach ($tourConfig->getDefaultTerminationRelations() as $defaultTerminationRelation) {
            $tour->addAccessibleTerminationRelation($defaultTerminationRelation->createAccessible());
        }
    }

    private function createTourAccessibleInterruptions(Tour $tour, TourDataConfig $tourConfig): void
    {
        foreach ($tourConfig->getDefaultInterruptionRelations() as $defaultInterruptionRelation) {
            $tour->addAccessibleInterruption($defaultInterruptionRelation->createAccessibleInterruption());
        }
    }

    /**
     * @param array<string, Customer>   $customers
     * @param array<string, AddressDto> $addressDtoIndexedByExternalId
     */
    private function handleTourOrders(
        Tour $tour,
        TourDto $tourDto,
        array $customers,
        array $addressDtoIndexedByExternalId,
        Country $country,
    ): void {
        $orderExternalIds = [];
        $tourStarted = TourStatus::STARTED === $tour->getStatus();

        foreach ($tourDto->orders as $position => $orderDto) {
            if (!isset($customers[$orderDto->customerExtId])) {
                throw new BadRequestException('Customer for order not found');
            }

            if (in_array($orderDto->orderExtId, $orderExternalIds, true)) {
                $this->logger->error(
                    'Order with external id sent multiple times by SAP. Skipping order processing.',
                    ['orderExtId' => $orderDto->orderExtId],
                );

                continue;
            }

            $orderExternalIds[] = $orderDto->orderExtId;

            $tour->addOrder(
                $this->orderService->upsert(
                    orderDto: $orderDto,
                    tour: $tour,
                    customer: $customers[$orderDto->customerExtId],
                    country: $country,
                    orderAddressExternalId: $orderDto->locationExtId,
                    addressDtoByAddressExternalId: $addressDtoIndexedByExternalId,
                    tourHasStarted: $tourStarted,
                    positionInTour: ((int) $position) + 1,
                )
            );
        }

        foreach ($tour->getOrders() as $order) {
            if (
                !in_array($order->getOrderExtId(), $orderExternalIds, true)
                && OrderStatus::CREATED === $order->getStatus()
            ) {
                $this->workflow->applyForOrder($order, 'abandon');
            }
        }
    }

    private function upsertMastertours(TourDto $tourDto, Tour $tour): void
    {
        $receivedMastertourExternalIds = array_map(trim(...), array_unique($tourDto->mastertours));
        $existingMastertourTemplates = $this->mastertourTemplateRepository->findByExternalIds($receivedMastertourExternalIds);
        $existingMastertourExternalIds = array_map(static fn ($mastertour): string => $mastertour->getExternalId(), $existingMastertourTemplates);

        if (count($existingMastertourExternalIds) !== count($receivedMastertourExternalIds)) {
            $this->logger->error(
                'One or more master tours not found!',
                [
                    'receivedMastertourExternalIds' => $receivedMastertourExternalIds,
                    'existingMastertourExternalIds' => $existingMastertourExternalIds,
                ]
            );
        }

        $tour->setMastertours($existingMastertourExternalIds);
    }

    private function setTourDocuments(Tour $tour, TourDto $tourDto): void
    {
        $this->documentRepository->clearTourDocuments($tour);

        foreach ($tourDto->tourDocuments as $tourDocumentDto) {
            $this->documentRepository->save(Document::createTourDocument(
                tourId: $tour->getId(),
                documentType: $tourDocumentDto->documentType,
                documentName: $tourDocumentDto->documentName,
                deliveryServices: array_map(
                    static fn (DeliveryServiceDto $dto): DeliveryService => $dto->toDatabaseValueObject(),
                    $tourDocumentDto->deliveryServices,
                ),
                textBlocks: array_map(
                    static fn (DocumentTextBlockDto $dto): DocumentTextBlock => $dto->toDatabaseValueObject(),
                    $tourDocumentDto->textBlocks,
                ),
            ));
        }
    }
}
