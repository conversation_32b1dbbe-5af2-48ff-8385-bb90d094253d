<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi;

use App\Infrastructure\UserManagementApi\DTO\User;

interface UserManagementApiInterface
{
    public function test(): void;

    public function usernameExists(string $username): bool;

    public function emailExists(string $email): bool;

    public function getUser(string $username): User;

    /**
     * @param array<Group>          $groups
     * @param array<RequiredAction> $requiredActions
     */
    public function createUser(
        string $username,
        string $firstName,
        string $lastName,
        string $email,
        string $password,
        array $groups,
        array $requiredActions = [],
    ): void;

    /**
     * @param array<Group>|null $groups
     */
    public function updateUser(
        string $username,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $email = null,
        ?array $groups = null,
    ): void;

    public function setPassword(string $username, string $password): void;
}
