<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi\DTO;

class User
{
    public string $username;

    public string $email;

    public string $firstName;

    public string $lastName;

    /**
     * @var string[]
     */
    public array $realmRoles;

    /**
     * @var array<string, string[]>
     */
    public array $clientRoles;

    /**
     * @var array<string>
     */
    public array $groups;
}
