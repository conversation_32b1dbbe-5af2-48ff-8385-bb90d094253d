<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi;

use App\Domain\Entity\Enum\Tenant;
use App\Domain\Entity\Enum\UserGroup;

enum Group: string
{
    case PORTAL_ADMIN = 'hermes-portal-admin';
    case COUNTRY_ADMIN = 'hermes-portal-country-admin';
    case FAQ_ADMIN = 'hermes-portal-faq-admin';
    case SUPPORT_USER = 'hermes-portal-support-user';
    case PORTAL_USER = 'hermes-portal-user';
    case DRIVER = 'hermes-driver';
    case TENANT_DE = 'tenant-de';
    case TENANT_ES = 'tenant-es';
    case TENANT_LU = 'tenant-lu';
    case TENANT_NL = 'tenant-nl';

    private const array DOMAIN_GROUP_MAP = [
        self::PORTAL_ADMIN->value => UserGroup::PORTAL_ADMIN,
        self::COUNTRY_ADMIN->value => UserGroup::COUNTRY_ADMIN,
        self::FAQ_ADMIN->value => UserGroup::FAQ_ADMIN,
        self::SUPPORT_USER->value => UserGroup::SUPPORT_USER,
        self::PORTAL_USER->value => UserGroup::PORTAL_USER,
        self::DRIVER->value => UserGroup::DRIVER,
    ];

    public static function fromDomainGroup(UserGroup $domainGroup): self
    {
        return match ($domainGroup) {
            UserGroup::DRIVER => self::DRIVER,
            UserGroup::PORTAL_ADMIN => self::PORTAL_ADMIN,
            UserGroup::COUNTRY_ADMIN => self::COUNTRY_ADMIN,
            UserGroup::FAQ_ADMIN => self::FAQ_ADMIN,
            UserGroup::SUPPORT_USER => self::SUPPORT_USER,
            UserGroup::PORTAL_USER => self::PORTAL_USER,
        };
    }

    public function toDomainGroup(): ?UserGroup
    {
        return self::DOMAIN_GROUP_MAP[$this->value] ?? null;
    }

    public static function fromTenant(Tenant $tenant): self
    {
        return match ($tenant) {
            Tenant::GERMANY => self::TENANT_DE,
            Tenant::LUXEMBOURG => self::TENANT_LU,
            Tenant::NETHERLANDS => self::TENANT_NL,
            Tenant::SPAIN => self::TENANT_ES,
        };
    }
}
