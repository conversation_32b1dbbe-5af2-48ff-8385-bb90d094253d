<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi;

use App\Infrastructure\Framework\Serializer\SerializerException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\Framework\Symfony\Context\RequestContextInterface;
use App\Infrastructure\Keycloak\Dto\AccessTokenResponse;
use App\Infrastructure\UserManagementApi\DTO\User;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface as HttpClientExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class UserManagementApi implements UserManagementApiInterface
{
    private ?string $accessToken = null;
    private ?int $accessTokenExpiresAt = null;

    public function __construct(
        private readonly HttpClientInterface $keycloakClient,

        private readonly HttpClientInterface $userManagementClient,

        private readonly LoggerInterface $logger,

        private readonly SerializerInterface $serializer,

        #[Autowire('%env(KEYCLOAK_REALM)%')]
        private readonly string $keycloakRealm,

        #[Autowire('%env(KEYCLOAK_BACKEND_CLIENT_ID)%')]
        private readonly string $clientId,

        #[Autowire('%env(KEYCLOAK_BACKEND_CLIENT_SECRET)%')]
        private readonly string $clientSecret,

        private readonly RequestContextInterface $requestContext,
    ) {
    }

    public function test(): void
    {
        $this->authenticateWithKeycloak();

        echo sprintf('Access token: %s'.PHP_EOL, $this->accessToken);
        echo sprintf('Expires at: %s'.PHP_EOL, $this->accessTokenExpiresAt);
    }

    public function usernameExists(string $username): bool
    {
        $this->authenticateWithKeycloak();
        $expectedStatusCodes = [200, 404];

        try {
            $response = $this->userManagementClient->request(
                method: 'GET',
                url: sprintf('/user-management-api/v1/user/check-username/%s', $username),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'X-Request-Id' => $this->requestContext->getRequestId(),
                    ],
                ],
            );

            $statusCode = $response->getStatusCode();

            if (!in_array($statusCode, $expectedStatusCodes, true)) {
                $this->logger->critical(
                    'UserManagementApi: Failed to check if username exists.',
                    [
                        'status_code' => $statusCode,
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to check if username exists.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('UserManagementApi: Failed to check if username exists.', ['exception' => $e]);

            throw new \RuntimeException('Failed to check if username exists.', 0, $e);
        }

        return 200 === $statusCode;
    }

    public function emailExists(string $email): bool
    {
        $this->authenticateWithKeycloak();
        $expectedStatusCodes = [200, 404];

        try {
            $response = $this->userManagementClient->request(
                method: 'GET',
                url: sprintf('/user-management-api/v1/user/check-email/%s', $email),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'X-Request-Id' => $this->requestContext->getRequestId(),
                    ],
                ],
            );

            $statusCode = $response->getStatusCode();

            if (!in_array($statusCode, $expectedStatusCodes, true)) {
                $this->logger->critical(
                    'UserManagementApi: Failed to check if email exists.',
                    [
                        'status_code' => $statusCode,
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to check if email exists.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('UserManagementApi: Failed to check if email exists.', ['exception' => $e]);

            throw new \RuntimeException('Failed to check if email exists.', 0, $e);
        }

        return 200 === $statusCode;
    }

    public function getUser(string $username): User
    {
        $this->authenticateWithKeycloak();

        try {
            $response = $this->userManagementClient->request(
                method: 'GET',
                url: '/user-management-api/v1/user/'.$username,
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'X-Request-Id' => $this->requestContext->getRequestId(),
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'UserManagementApi: Failed to get user.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to get user.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('UserManagementApi: Failed to get user.', ['exception' => $e]);

            throw new \RuntimeException('Failed to get user.', 0, $e);
        }

        $this->logger->debug(
            'UserManagementApi: Retrieved user by username.',
            [
                'username' => $username,
                'response' => $responseContent,
            ]
        );

        try {
            return $this->serializer->deserializeIntoObject(
                data: $responseContent,
                className: User::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize UserManagementApi user response.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize UserManagementApi user response.', 0, $e);
        }
    }

    public function createUser(
        string $username,
        string $firstName,
        string $lastName,
        string $email,
        string $password,
        array $groups,
        array $requiredActions = [],
    ): void {
        $this->authenticateWithKeycloak();

        $this->logger->debug(
            'UserManagementApi: Creating user.',
            [
                'username' => $username,
                'firstname' => $firstName,
                'lastname' => $lastName,
                'email' => $email,
                'groups' => $groups,
                'required_actions' => $requiredActions,
            ]
        );

        try {
            $response = $this->userManagementClient->request(
                method: 'POST',
                url: '/user-management-api/v1/users',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                        'X-Request-Id' => $this->requestContext->getRequestId(),
                    ],
                    'json' => [
                        'username' => $username,
                        'firstname' => $firstName,
                        'lastname' => $lastName,
                        'email' => $email,
                        'password' => $password,
                        'groups' => $groups,
                        'required_actions' => $requiredActions,
                    ],
                ],
            );

            if (201 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'UserManagementApi: Failed to create user.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to create user.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('UserManagementApi: Failed to create user.', ['exception' => $e]);

            throw new \RuntimeException('Failed to create user.', 0, $e);
        }
    }

    public function updateUser(
        string $username,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $email = null,
        ?array $groups = null,
    ): void {
        $this->authenticateWithKeycloak();

        $this->logger->debug(
            'UserManagementApi: Updating user.',
            [
                'username' => $username,
                'firstname' => $firstName,
                'lastname' => $lastName,
                'email' => $email,
                'groups' => $groups,
            ]
        );

        try {
            $response = $this->userManagementClient->request(
                method: 'PUT',
                url: '/user-management-api/v1/users/'.$username,
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                        'X-Request-Id' => $this->requestContext->getRequestId(),
                    ],
                    'json' => array_filter([
                        'firstname' => $firstName,
                        'lastname' => $lastName,
                        'email' => $email,
                        'groups' => $groups,
                    ]),
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'UserManagementApi: Failed to update user.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to update user.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('UserManagementApi: Failed to update user.', ['exception' => $e]);

            throw new \RuntimeException('Failed to update user.', 0, $e);
        }
    }

    public function setPassword(string $username, string $password): void
    {
        $this->authenticateWithKeycloak();

        try {
            $response = $this->userManagementClient->request(
                method: 'PUT',
                url: '/user-management-api/v1/users/'.$username.'/password',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                        'X-Request-Id' => $this->requestContext->getRequestId(),
                    ],
                    'json' => [
                        'password' => $password,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'UserManagementApi: Failed to set password.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to set password.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('UserManagementApi: Failed to set password.', ['exception' => $e]);

            throw new \RuntimeException('Failed to set password.', 0, $e);
        }
    }

    private function authenticateWithKeycloak(): void
    {
        if (
            null !== $this->accessToken
            && null !== $this->accessTokenExpiresAt
            && $this->accessTokenExpiresAt > time() - 10
        ) {
            $this->logger->debug(
                'Using cached Keycloak admin access token.',
                ['expires_at' => $this->accessTokenExpiresAt]
            );

            return;
        }

        try {
            $this->logger->debug('Authenticating with Keycloak client.');

            $response = $this->keycloakClient->request(
                method: 'POST',
                url: '/realms/'.$this->keycloakRealm.'/protocol/openid-connect/token',
                options: [
                    'body' => [
                        'client_id' => $this->clientId,
                        'client_secret' => $this->clientSecret,
                        'grant_type' => 'client_credentials',
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to authenticate with Keycloak client.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to authenticate with Keycloak client.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to authenticate with Keycloak client.', ['exception' => $e]);

            throw new \RuntimeException('Failed to authenticate with Keycloak client.', 0, $e);
        }

        try {
            $accessTokenResponse = $this->serializer->deserializeIntoObject(
                data: $responseContent,
                className: AccessTokenResponse::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak client authentication response.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak client authentication response.', 0, $e);
        }

        $this->accessToken = $accessTokenResponse->accessToken;
        $this->accessTokenExpiresAt = time() + $accessTokenResponse->expiresIn;
    }
}
