<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand('app:user-management:test')]
class TestCommand extends Command
{
    public function __construct(
        private UserManagementApiInterface $userManagementApi,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->userManagementApi->test();

        return Command::SUCCESS;
    }
}
