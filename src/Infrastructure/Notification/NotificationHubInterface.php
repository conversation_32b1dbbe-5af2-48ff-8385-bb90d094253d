<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification;

interface NotificationHubInterface
{
    public function getDeviceAccessToken(string $deviceId): string;

    /**
     * @param array<string> $branches
     */
    public function getBranchesAccessToken(string $userId, array $branches): string;

    /**
     * @param array<string> $countries
     */
    public function getCountriesAccessToken(string $userId, array $countries): string;

    public function getDeviceChannel(string $deviceId): string;

    public function getBranchChannel(string $branch): string;

    public function getCountryChannel(string $country): string;

    /**
     * @throws NotificationException
     */
    public function notifyDevice(string $deviceId, NotificationDtoInterface $data): void;

    /**
     * @throws NotificationException
     */
    public function notifyBranch(string $branch, NotificationDtoInterface $data): void;

    /**
     * @throws NotificationException
     */
    public function notifyCountry(string $country, NotificationDtoInterface $data): void;
}
