<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification\Command;

use App\Infrastructure\Notification\CentrifugoApi;
use App\Infrastructure\Notification\Dto\PublishDto;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:notification:test', description: 'Test notification command')]
class NotificationTestCommand extends Command
{
    public function __construct(
        private readonly CentrifugoApi $centrifugoApi,
    ) {
        parent::__construct();
    }

    #[\Override]
    protected function configure(): void
    {
        $this
            ->addOption('channel', 'c', InputOption::VALUE_OPTIONAL, 'Channel name', 'test-channel')
        ;
    }

    #[\Override]
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $channel = $input->getOption('channel');

        if (!is_string($channel)) {
            throw new \InvalidArgumentException('Channel name must be a string');
        }

        $this->centrifugoApi->publish(
            new PublishDto(
                channel: $channel,
                data: ['test' => 'data', 'timestamp' => time()],
            ),
        );

        return Command::SUCCESS;
    }
}
