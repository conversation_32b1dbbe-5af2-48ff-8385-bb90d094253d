<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification\Command;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Tenant;
use App\Domain\Repository\UserRepository;
use App\Infrastructure\Notification\NotificationHubInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:notification:generate-portal-user-token',
    description: 'Generates JWT token for portal user subscription'
)]
class NotificationGeneratePortalUserTokenCommand extends Command
{
    public function __construct(
        private readonly NotificationHubInterface $notificationHub,
        private readonly UserRepository $userRepository,
        private readonly TenantContext $tenantContext,
    ) {
        parent::__construct();
    }

    #[\Override]
    protected function configure(): void
    {
        $this
            ->addArgument('userId', InputArgument::REQUIRED, 'User ID')
        ;
    }

    #[\Override]
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $user = null;

        foreach (Tenant::LIST as $tenant) {
            $this->tenantContext->setTenant($tenant);
            $user = $this->userRepository->find($input->getArgument('userId'));

            if (null !== $user) {
                break; // Exit loop if user is found
            }
        }

        if (null === $user) {
            $output->writeln('<error>User not found.</error>');

            return Command::FAILURE;
        }

        if ($user->isPortalAdmin() || $user->isCountryAdmin()) {
            $userCountry = $user->getCountry();

            $output->writeln('Token: '.$this->notificationHub->getCountriesAccessToken(
                userId: $user->getId(),
                countries: null === $userCountry ? [] : [$userCountry->value],
            ));

            return Command::SUCCESS;
        }

        if ($user->isSupportUser() || $user->isPortalUser()) {
            $output->writeln('Token: '.$this->notificationHub->getBranchesAccessToken(
                userId: $user->getId(),
                branches: $user->getBranchAccess(),
            ));

            return Command::SUCCESS;
        }

        $output->writeln('<error>User does not have access to notifications.</error>');

        return Command::FAILURE;
    }
}
