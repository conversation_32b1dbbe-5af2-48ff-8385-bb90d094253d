<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification\Command;

use App\Infrastructure\Notification\NotificationHubInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:notification:generate-device-token',
    description: 'Generates JWT token for client device subscription'
)]
class NotificationGenerateDeviceTokenCommand extends Command
{
    public function __construct(
        private readonly NotificationHubInterface $notificationHub,
    ) {
        parent::__construct();
    }

    #[\Override]
    protected function configure(): void
    {
        $this
            ->addArgument('deviceId', InputArgument::REQUIRED, 'Device ID')
        ;
    }

    #[\Override]
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $deviceId = $input->getArgument('deviceId');

        $output->writeln('Token: '.$this->notificationHub->getDeviceAccessToken($deviceId));

        return Command::SUCCESS;
    }
}
