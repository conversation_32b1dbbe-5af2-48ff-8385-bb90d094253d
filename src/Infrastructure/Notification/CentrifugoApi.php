<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification;

use App\Infrastructure\Framework\Serializer\SerializerException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\Notification\Dto\PublishDto;
use Jose\Component\Core\AlgorithmManager;
use Jose\Component\KeyManagement\JWKFactory;
use Jose\Component\Signature\Algorithm\HS256;
use Jose\Component\Signature\JWSBuilder;
use Jose\Component\Signature\Serializer\CompactSerializer;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

readonly class CentrifugoApi
{
    public function __construct(
        private HttpClientInterface $centrifugoClient,
        private SerializerInterface $serializer,
        private LoggerInterface $logger,
        #[Autowire('%env(CENTRIFUGO_HMAC_SECRET_KEY)%')]
        private string $hmacSecretKey,
    ) {
    }

    public function publish(PublishDto $publishDto): void
    {
        try {
            $this->logger->debug(
                'Publishing message to Centrifugo',
                ['dto' => $publishDto],
            );
            $payload = $this->serializer->serialize($publishDto, 'json');
        } catch (SerializerException $e) {
            $this->logger->error(
                'Failed to serialize message for Centrifugo',
                ['dto' => $publishDto, 'exception' => $e],
            );

            throw new NotificationRequestException(
                'Failed to serialize message for Centrifugo',
                previous: $e,
            );
        }

        try {
            $this->centrifugoClient->request(
                'POST',
                '/api/publish',
                ['body' => $payload],
            );
        } catch (ExceptionInterface $e) {
            $this->logger->error(
                'Failed to publish message to Centrifugo',
                ['dto' => $publishDto, 'exception' => $e],
            );

            throw new NotificationRequestException(
                'Failed to publish message to Centrifugo',
                previous: $e,
            );
        }
    }

    /**
     * @param array<string> $channels
     */
    public function generateClientJWT(string $userId, array $channels): string
    {
        $algorithmManager = new AlgorithmManager([new HS256()]);
        $jwsBuilder = new JWSBuilder($algorithmManager);
        $jwk = JWKFactory::createFromSecret($this->hmacSecretKey, ['alg' => 'HS256', 'use' => 'sig']);
        $payload = json_encode([
            'iat' => time(),
            'nbf' => time(),
            'exp' => time() + 57600, // 16 hours
            'aud' => 'centrifugo',
            'sub' => $userId,
            'channels' => $channels,
        ]);
        assert(false !== $payload);

        $jws = $jwsBuilder
            ->create()
            ->withPayload($payload)
            ->addSignature($jwk, ['alg' => 'HS256'])
            ->build();

        $serializer = new CompactSerializer();

        return $serializer->serialize($jws, 0);
    }
}
