<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification\WebTester;

use App\Infrastructure\Notification\CentrifugoApi;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
class WebTestEndpoint extends AbstractController
{
    public function __construct(
        private readonly CentrifugoApi $centrifugoApi,
    ) {
    }

    #[Route('/test', methods: ['GET'])]
    public function index(): Response
    {
        $token = $this->centrifugoApi->generateClientJWT('12345', ['test-channel']);

        return $this->render('notification-test/index.html.twig', ['token' => $token]);
    }
}
