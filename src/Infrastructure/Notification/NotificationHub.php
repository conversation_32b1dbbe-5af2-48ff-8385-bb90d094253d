<?php

declare(strict_types=1);

namespace App\Infrastructure\Notification;

use App\Infrastructure\Notification\Dto\PublishDto;

readonly class NotificationHub implements NotificationHubInterface
{
    public function __construct(
        private CentrifugoApi $centrifugoApi,
    ) {
    }

    public function getDeviceAccessToken(string $deviceId): string
    {
        $encodedUserId = bin2hex($deviceId);

        return $this->centrifugoApi->generateClientJWT($encodedUserId, [$this->getDeviceChannel($deviceId)]);
    }

    public function getBranchesAccessToken(string $userId, array $branches): string
    {
        $encodedUserId = bin2hex($userId);
        $channels = array_map(
            fn (string $branch) => $this->getBranchChannel($branch),
            $branches
        );

        return $this->centrifugoApi->generateClientJWT($encodedUserId, $channels);
    }

    public function getCountriesAccessToken(string $userId, array $countries): string
    {
        $encodedUserId = bin2hex($userId);
        $channels = array_map(
            fn (string $country) => $this->getCountryChannel($country),
            $countries
        );

        return $this->centrifugoApi->generateClientJWT($encodedUserId, $channels);
    }

    public function getDeviceChannel(string $deviceId): string
    {
        return 'device:channel#'.bin2hex($deviceId);
    }

    public function getBranchChannel(string $branch): string
    {
        return 'branch:'.bin2hex($branch);
    }

    public function getCountryChannel(string $country): string
    {
        return 'country:'.bin2hex($country);
    }

    public function notifyDevice(string $deviceId, NotificationDtoInterface $data): void
    {
        $this->centrifugoApi->publish(new PublishDto(
            channel: $this->getDeviceChannel($deviceId),
            data: $data,
        ));
    }

    public function notifyBranch(string $branch, NotificationDtoInterface $data): void
    {
        $this->centrifugoApi->publish(new PublishDto(
            channel: $this->getBranchChannel($branch),
            data: $data,
        ));
    }

    public function notifyCountry(string $country, NotificationDtoInterface $data): void
    {
        $this->centrifugoApi->publish(new PublishDto(
            channel: $this->getCountryChannel($country),
            data: $data,
        ));
    }
}
