<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue;

use App\Infrastructure\Blackfire\BlackfireIntegrationInterface;
use App\Infrastructure\Framework\Serializer\SerializerException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Stamp\NonSendableStampInterface;
use Symfony\Component\Messenger\Stamp\SerializedMessageStamp;
use Symfony\Component\Messenger\Stamp\StampInterface;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

readonly class ExternalSerializer implements SerializerInterface
{
    final public const string MESSENGER_SERIALIZATION_CONTEXT = 'messenger_serialization';

    private const string STAMP_HEADER_PREFIX = 'X-Message-Stamp-';
    private const string BLACKFIRE_QUERY_HEADER = 'X-Blackfire-Query';

    public function __construct(
        private \App\Infrastructure\Framework\Serializer\SerializerInterface $serializer,
        private LoggerInterface $logger,
        private BlackfireIntegrationInterface $blackfireIntegration,
    ) {
    }

    /**
     * @param array{body: string, headers: array<string, mixed>} $encodedEnvelope
     */
    public function decode(array $encodedEnvelope): Envelope
    {
        if (empty($encodedEnvelope['body']) || empty($encodedEnvelope['headers'])) {
            throw new MessageDecodingFailedException(
                'Encoded envelope should have at least a "body" and some "headers", or maybe you should implement your own serializer.'
            );
        }

        if (empty($encodedEnvelope['headers']['type']) || !is_string($encodedEnvelope['headers']['type'])) {
            throw new MessageDecodingFailedException('Encoded envelope does not have a string "type" header.');
        }

        if (!class_exists($encodedEnvelope['headers']['type'])) {
            $message = sprintf(
                'Encoded envelope has an invalid "type" header, expected existing class name. Got: %s',
                $encodedEnvelope['headers']['type']
            );
            throw new MessageDecodingFailedException($message);
        }

        if (
            array_key_exists(self::BLACKFIRE_QUERY_HEADER, $encodedEnvelope['headers'])
            && is_string($encodedEnvelope['headers'][self::BLACKFIRE_QUERY_HEADER])
        ) {
            $this->blackfireIntegration->startProfileFromQuery($encodedEnvelope['headers'][self::BLACKFIRE_QUERY_HEADER]);
        }

        $stamps = $this->decodeStamps($encodedEnvelope);
        $stamps[] = new SerializedMessageStamp($encodedEnvelope['body']);

        try {
            return new Envelope(
                message: $this->serializer->deserializeIntoObject(
                    data: $encodedEnvelope['body'],
                    className: $encodedEnvelope['headers']['type'],
                    context: [self::MESSENGER_SERIALIZATION_CONTEXT => true],
                ),
                stamps: $stamps,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Could not decode message: '.$e->getMessage(),
                ['message' => $encodedEnvelope['body'], 'type' => $encodedEnvelope['headers']['type'], 'exception' => $e],
            );

            throw new MessageDecodingFailedException('Could not decode message.', previous: $e);
        }
    }

    /**
     * @return array{body: string, headers: array<string, mixed>}
     *
     * @throws SerializerException
     */
    public function encode(Envelope $envelope): array
    {
        $envelope = $envelope->withoutStampsOfType(NonSendableStampInterface::class);
        $messageClassName = $envelope->getMessage()::class;
        $headers = ['type' => $messageClassName];
        $blackfireSubQuery = $this->blackfireIntegration->getSubQuery();

        if (null !== $blackfireSubQuery) {
            $headers[self::BLACKFIRE_QUERY_HEADER] = $blackfireSubQuery;
        }

        return [
            'body' => $this->serializer->serialize(
                data: $envelope->getMessage(),
                format: 'json',
                context: [self::MESSENGER_SERIALIZATION_CONTEXT => true],
            ),
            'headers' => $headers + $this->encodeStamps($envelope),
        ];
    }

    /**
     * @param array{body: string, headers: array<string|int, mixed>} $encodedEnvelope
     *
     * @return array<StampInterface>
     */
    private function decodeStamps(array $encodedEnvelope): array
    {
        $stamps = [];

        foreach ($encodedEnvelope['headers'] as $name => $value) {
            if (!str_starts_with((string) $name, self::STAMP_HEADER_PREFIX) || !is_string($value)) {
                continue;
            }

            $stampClassName = substr((string) $name, \strlen(self::STAMP_HEADER_PREFIX));

            if (!class_exists($stampClassName) || !is_subclass_of($stampClassName, StampInterface::class)) {
                $this->logger->critical(
                    'Message stamp class not found.',
                    [
                        'stampClassName' => $stampClassName,
                        'messageClassName' => $encodedEnvelope['headers']['type'],
                    ]
                );

                continue;
            }

            try {
                $stampCollection = $this->serializer->deserializeIntoArrayOfObjects(
                    data: $value,
                    className: $stampClassName,
                    context: [self::MESSENGER_SERIALIZATION_CONTEXT => true],
                );

                $stamps[] = $stampCollection;
            } catch (SerializerException $e) {
                $this->logger->critical(
                    'Could not decode stamp: '.$e->getMessage(),
                    ['stampClassName' => $stampClassName, 'value' => $value, 'exception' => $e],
                );

                throw new MessageDecodingFailedException('Could not decode stamp: '.$e->getMessage(), $e->getCode(), $e);
            }
        }

        if ([] !== $stamps) {
            $stamps = array_merge(...$stamps);
        }

        return $stamps;
    }

    /**
     * @return array<string, string>
     *
     * @throws SerializerException
     */
    private function encodeStamps(Envelope $envelope): array
    {
        if (!$allStamps = $envelope->all()) {
            return [];
        }

        $headers = [];
        foreach ($allStamps as $class => $stamps) {
            $headers[self::STAMP_HEADER_PREFIX.$class] = $this->serializer->serialize(
                data: $stamps,
                format: 'json',
                context: [self::MESSENGER_SERIALIZATION_CONTEXT => true],
            );
        }

        return $headers;
    }
}
