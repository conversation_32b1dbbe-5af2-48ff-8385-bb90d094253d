<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue;

use Symfony\Component\Messenger\Envelope;

class InMemoryMessageQueue
{
    /**
     * @var Envelope[]
     */
    private array $envelopes = [];

    public function scheduleEnvelopeForSending(Envelope $envelope): void
    {
        $this->envelopes[] = $envelope;
    }

    /**
     * @return Envelope[]
     */
    public function getEnvelopes(): array
    {
        return $this->envelopes;
    }
}
