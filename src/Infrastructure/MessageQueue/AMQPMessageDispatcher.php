<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue;

use App\Domain\MessageQueue\AsyncMessage;
use App\Domain\MessageQueue\DeviceMessage;
use App\Domain\MessageQueue\ExternalSystemMessage;
use App\Domain\MessageQueue\MessageDispatcherInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

#[AsEventListener(event: KernelEvents::RESPONSE, method: 'onKernelResponse', priority: 1)]
readonly class AMQPMessageDispatcher implements MessageDispatcherInterface
{
    private InMemoryMessageQueue $inMemoryMessageQueue;

    public function __construct(
        private MessageBusInterface $messageBus,
        private RequestStack $requestStack,
        private EnvelopeContextSetterInterface $envelopeContextSetter,
    ) {
        $this->inMemoryMessageQueue = new InMemoryMessageQueue();
    }

    public function dispatch(AsyncMessage|DeviceMessage|ExternalSystemMessage|Envelope $message, array $stamps = []): void
    {
        $envelope = Envelope::wrap($message, $stamps);

        $envelope = $this->envelopeContextSetter->withContext($envelope);

        if ($this->isHttpRequest()) {
            $this->inMemoryMessageQueue->scheduleEnvelopeForSending($envelope);

            return;
        }

        if (null === $envelope->last(DispatchAfterCurrentBusStamp::class)) {
            $envelope = $envelope->with(new DispatchAfterCurrentBusStamp());
        }

        $this->messageBus->dispatch($envelope);
    }

    public function onKernelResponse(ResponseEvent $responseEvent): void
    {
        if (
            !$this->isHttpRequest()
            || !$responseEvent->getResponse()->isSuccessful()
        ) {
            return;
        }

        foreach ($this->inMemoryMessageQueue->getEnvelopes() as $envelope) {
            $this->messageBus->dispatch($envelope);
        }
    }

    private function isHttpRequest(): bool
    {
        return null !== $this->requestStack->getCurrentRequest();
    }
}
