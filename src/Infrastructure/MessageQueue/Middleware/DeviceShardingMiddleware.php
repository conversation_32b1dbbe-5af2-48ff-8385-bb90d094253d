<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Middleware;

use App\Domain\Context\DeviceContext;
use App\Domain\MessageQueue\DeviceMessage;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Messenger\Bridge\Amqp\Transport\AmqpStamp;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;
use Symfony\Component\Messenger\Stamp\ConsumedByWorkerStamp;
use Symfony\Component\Messenger\Stamp\ReceivedStamp;

readonly class DeviceShardingMiddleware implements MiddlewareInterface
{
    public function __construct(
        private DeviceContext $deviceContext,
        #[Autowire('%env(int:DEVICE_MESSAGES_NUMBER_OF_SHARDS)%')]
        private int $numberOfShards,
    ) {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        if ($this->isMessageBeingConsumed($envelope)) {
            // No action needed when message is being consumed
            return $stack->next()->handle($envelope, $stack);
        }

        $message = $envelope->getMessage();

        if ($message instanceof DeviceMessage) {
            if (!$this->deviceContext->hasDeviceId()) {
                throw new \RuntimeException('Device ID is required for DeviceMessage');
            }

            $deviceId = $this->deviceContext->getDeviceId();
            $shardId = $this->calculateShardId($deviceId);
            $amqpStamp = $envelope->last(AmqpStamp::class);

            if (null !== $amqpStamp) {
                $envelope = $envelope->withoutAll(AmqpStamp::class);
                $amqpStamp = new AmqpStamp(
                    routingKey: 'shard-'.$shardId,
                    flags: $amqpStamp->getFlags(),
                    attributes: $amqpStamp->getAttributes(),
                );
            } else {
                $amqpStamp = new AmqpStamp(routingKey: 'shard-'.$shardId);
            }

            $envelope = $envelope->with($amqpStamp);
        }

        return $stack->next()->handle($envelope, $stack);
    }

    public function isMessageBeingConsumed(Envelope $envelope): bool
    {
        return $envelope->last(ReceivedStamp::class) || $envelope->last(ConsumedByWorkerStamp::class);
    }

    private function calculateShardId(string $deviceId): int
    {
        // Algorithm choice based on:
        // https://medium.com/miro-engineering/choosing-a-hash-function-to-solve-a-data-sharding-problem-c656259e2b54
        return hexdec(hash('murmur3a', $deviceId)) % $this->numberOfShards;
    }
}
