<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Middleware;

use App\Domain\Context\ActionContext;
use App\Domain\Context\DeviceContext;
use App\Domain\Context\TenantContext;
use App\Domain\MessageQueue\RequireActionContext;
use App\Domain\MessageQueue\RequireDeviceContext;
use App\Domain\MessageQueue\RequireTenantIdentifier;
use App\Domain\MessageQueue\RequireUserIdentifier;
use App\Exception\BadRequestException;
use App\Infrastructure\Framework\Symfony\Context\RequestContextInterface;
use App\Infrastructure\MessageQueue\EnvelopeContextSetterInterface;
use App\Infrastructure\MessageQueue\Stamp\ActionMetadataStamp;
use App\Infrastructure\MessageQueue\Stamp\DeviceMetadataStamp;
use App\Infrastructure\MessageQueue\Stamp\RequestIdStamp;
use App\Infrastructure\MessageQueue\Stamp\RequestMicroTimeStamp;
use App\Infrastructure\MessageQueue\Stamp\TenantStamp;
use App\Infrastructure\MessageQueue\Stamp\UserIdentifierStamp;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Bridge\Amqp\Transport\AmqpStamp;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;
use Symfony\Component\Messenger\Stamp\ConsumedByWorkerStamp;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;
use Symfony\Component\Messenger\Stamp\ReceivedStamp;

readonly class ContextSetterMiddleware implements MiddlewareInterface, EnvelopeContextSetterInterface
{
    public function __construct(
        private RequestContextInterface $requestContext,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private DeviceContext $deviceContext,
        private LoggerInterface $logger,
    ) {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        if ($envelope->last(ReceivedStamp::class) || $envelope->last(ConsumedByWorkerStamp::class)) {
            // This only sets the context when message is send. If it's received, we do not need to do anything.
            return $stack->next()->handle($envelope, $stack);
        }

        $envelope = $this->withContext($envelope);

        // Go through the rest of the middleware chain (including sending of message)
        $envelope = $stack->next()->handle($envelope, $stack);

        $this->logger->debug(
            'Sending message for async handling',
            [
                'message' => $envelope->getMessage(),
                'stamps' => $envelope->all(),
                'message-class' => $envelope->getMessage()::class,
            ]
        );

        return $envelope;
    }

    public function withContext(Envelope $envelope): Envelope
    {
        $envelope = $this->setRequestIdStamp($envelope);
        $envelope = $this->setRequestMicroTimeStamp($envelope);
        $envelope = $this->setTenantStamp($envelope);
        $envelope = $this->setUserStamp($envelope);
        $envelope = $this->setActionMetadataStamp($envelope);
        $envelope = $this->setDeviceMetadataStamp($envelope);
        $envelope = $this->setDispatchAfterCurrentBusStamp($envelope);
        $envelope = $this->setMandatoryFlag($envelope);

        return $envelope;
    }

    private function setRequestIdStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(RequestIdStamp::class)) {
            return $envelope;
        }

        return $envelope->with(new RequestIdStamp($this->requestContext->getRequestId()));
    }

    private function setRequestMicroTimeStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(RequestMicroTimeStamp::class)) {
            return $envelope;
        }

        return $envelope->with(new RequestMicroTimeStamp($this->requestContext->getRequestMicroTimestamp()));
    }

    private function setUserStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(UserIdentifierStamp::class)) {
            return $envelope;
        }

        $user = $this->requestContext->getUser();

        if ($envelope->getMessage() instanceof RequireUserIdentifier && null === $user) {
            $this->logger->critical(
                'User identifier required but not available',
                [
                    'messageClass' => $envelope->getMessage()::class,
                    'message' => $envelope->getMessage(),
                    'stamps' => $envelope->all(),
                ]
            );
            throw new \RuntimeException('User identifier required but not available');
        }

        if (null !== $user) {
            $envelope = $envelope
                ->withoutAll(TenantStamp::class)
                ->with(new UserIdentifierStamp($user->getUserIdentifier()))
                ->with(new TenantStamp($user->getTenant()));
        }

        return $envelope;
    }

    private function setTenantStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(TenantStamp::class)) {
            return $envelope;
        }

        if ($envelope->getMessage() instanceof RequireTenantIdentifier && !$this->tenantContext->hasTenant()) {
            $this->logger->critical(
                'Tenant identifier required but not available',
                [
                    'messageClass' => $envelope->getMessage()::class,
                    'message' => $envelope->getMessage(),
                    'stamps' => $envelope->all(),
                ]
            );
            throw new \RuntimeException('Tenant identifier required but not available');
        }

        if ($this->tenantContext->hasTenant()) {
            $envelope = $envelope->with(new TenantStamp($this->tenantContext->getTenant()));
        }

        return $envelope;
    }

    private function setActionMetadataStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(ActionMetadataStamp::class)) {
            return $envelope;
        }

        try {
            $this->actionContext->getTimestamp();
        } catch (BadRequestException) {
            if ($envelope->getMessage() instanceof RequireActionContext) {
                $this->logger->critical(
                    'Action metadata required but not available',
                    [
                        'messageClass' => $envelope->getMessage()::class,
                        'message' => $envelope->getMessage(),
                        'stamps' => $envelope->all(),
                    ]
                );
                throw new \RuntimeException('Action metadata required but not available');
            }

            return $envelope;
        }

        return $envelope->with(
            new ActionMetadataStamp(
                timestamp: $this->actionContext->getTimestamp(),
                latitude: $this->actionContext->getLatitude(),
                longitude: $this->actionContext->getLongitude(),
                mileage: $this->actionContext->getMileage(),
                appServerGapMillis: $this->actionContext->getAppServerGapMillis(),
            )
        );
    }

    private function setDeviceMetadataStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(DeviceMetadataStamp::class)) {
            return $envelope;
        }

        if ($this->deviceContext->hasDeviceId()) {
            return $envelope->with(
                new DeviceMetadataStamp(
                    deviceTimestamp: $this->deviceContext->getDeviceTimestamp(),
                    deviceId: $this->deviceContext->getDeviceId(),
                )
            );
        }

        if ($envelope->getMessage() instanceof RequireDeviceContext) {
            $this->logger->critical(
                'Device metadata required but not available',
                [
                    'messageClass' => $envelope->getMessage()::class,
                    'message' => $envelope->getMessage(),
                    'stamps' => $envelope->all(),
                ]
            );
            throw new \RuntimeException('Device metadata required but not available');
        }

        // This is fake device metadata stamp, for ingest SAP requests cases
        if ($this->deviceContext->hasDeviceTimestamp()) {
            return $envelope->with(
                new DeviceMetadataStamp(
                    deviceTimestamp: $this->deviceContext->getDeviceTimestamp(),
                    deviceId: null,
                )
            );
        }

        return $envelope;
    }

    private function setDispatchAfterCurrentBusStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(DispatchAfterCurrentBusStamp::class)) {
            return $envelope;
        }

        return $envelope->with(new DispatchAfterCurrentBusStamp());
    }

    private function setMandatoryFlag(Envelope $envelope): Envelope
    {
        $amqpStamp = $envelope->last(AmqpStamp::class) ?? new AmqpStamp();

        $amqpStamp = new AmqpStamp(
            routingKey: $amqpStamp->getRoutingKey(),
            flags: AMQP_MANDATORY,
            attributes: $amqpStamp->getAttributes(),
        );

        return $envelope->withoutAll(AmqpStamp::class)->with($amqpStamp);
    }
}
