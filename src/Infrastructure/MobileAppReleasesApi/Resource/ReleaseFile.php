<?php

declare(strict_types=1);

namespace App\Infrastructure\MobileAppReleasesApi\Resource;

use App\Domain\Entity\Enum\Types\ReleaseFileType;
use Symfony\Component\Validator\Constraints as Assert;

class ReleaseFile
{
    #[Assert\NotBlank]
    public ReleaseFileType $type;

    #[Assert\NotBlank]
    #[Assert\Length(min: 2, max: 150)]
    #[Assert\Regex(
        pattern: '/^\/mobile-app-releases\/[^\/]+$/',
        message: 'Invalid file path. Must start with /mobile-app-releases/',
    )]
    public string $path;
}
