<?php

declare(strict_types=1);

namespace App\Infrastructure\MobileAppReleasesApi\Resource;

use App\Domain\Entity\Enum\Types\MobileAppType;
use App\Infrastructure\MobileAppReleasesApi\HttpEndpoint\ReleaseEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'mobile_app_releases',
    operations: [
        new Post(
            controller: [ReleaseEndpoint::class, 'ingest'],
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: null,
    tag: 'Releases',
)]
class Release
{
    #[Assert\NotBlank]
    #[Assert\Length(min: 2, max: 20)]
    public string $version;

    #[Assert\NotBlank]
    #[Assert\Length(min: 2, max: 25000)]
    public string $releaseNotes;

    #[Assert\NotBlank]
    public MobileAppType $type;

    /**
     * @var ReleaseFile[]
     */
    #[Assert\NotBlank]
    #[Assert\Valid]
    public array $files;
}
