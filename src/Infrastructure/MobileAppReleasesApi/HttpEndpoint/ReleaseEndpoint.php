<?php

declare(strict_types=1);

namespace App\Infrastructure\MobileAppReleasesApi\HttpEndpoint;

use App\Domain\Entity\MobileAppRelease;
use App\Domain\Entity\ValueObject\ReleaseFile as ReleaseFileValueObject;
use App\Domain\Repository\MobileAppReleaseRepository;
use App\Domain\Services\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Services\ObjectStorage\ObjectStorageException;
use App\Infrastructure\MobileAppReleasesApi\Resource\Release;
use App\Infrastructure\MobileAppReleasesApi\Resource\ReleaseFile;
use App\Infrastructure\MobileAppReleasesApi\Service\ReleaseNoteProcessor;
use Composer\Semver\VersionParser;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

#[AsController]
readonly class ReleaseEndpoint
{
    public function __construct(
        private MobileAppReleaseRepository $mobileAppReleaseRepository,
        private ReleaseNoteProcessor $releaseNoteProcessor,
        private ObjectRepositoryInterface $objectRepository,
    ) {
    }

    public function ingest(Release $release): void
    {
        $this->validateSemVer($release->version);
        $this->validateVersionDoesNotExist($release->version);

        foreach ($release->files as $file) {
            $this->validateFileExists($file->path);
        }

        $this->mobileAppReleaseRepository->save(new MobileAppRelease(
            version: $release->version,
            releaseNotes: $this->releaseNoteProcessor->cleanReleaseNotes($release->releaseNotes),
            type: $release->type,
            files: array_map(
                fn (ReleaseFile $file): ReleaseFileValueObject => $this->mapDtoToValueObject($file),
                $release->files,
            ),
        ));
    }

    private function mapDtoToValueObject(ReleaseFile $releaseFile): ReleaseFileValueObject
    {
        return ReleaseFileValueObject::create(
            type: $releaseFile->type,
            path: $releaseFile->path,
        );
    }

    private function validateSemVer(string $version): void
    {
        $versionParser = new VersionParser();

        try {
            $versionParser->normalize($version);
        } catch (\Exception $e) {
            throw new UnprocessableEntityHttpException(sprintf(
                'Invalid semantic version: %s',
                $version,
            ), previous: $e);
        }
    }

    private function validateFileExists(string $filePath): void
    {
        try {
            if (!$this->objectRepository->exists($filePath, automaticTenant: false)) {
                throw new UnprocessableEntityHttpException(
                    sprintf(
                        'File not found: %s',
                        $filePath,
                    )
                );
            }
        } catch (ObjectStorageException $e) {
            throw new ServiceUnavailableHttpException(previous: $e);
        }
    }

    public function validateVersionDoesNotExist(string $version): void
    {
        if ($this->mobileAppReleaseRepository->findOneBy(['version' => $version])) {
            throw new UnprocessableEntityHttpException(sprintf(
                'Version already exists: %s',
                $version,
            ));
        }
    }
}
