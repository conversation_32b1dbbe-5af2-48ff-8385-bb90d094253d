<?php

declare(strict_types=1);

namespace App\Infrastructure\MobileAppReleasesApi\Service;

readonly class ReleaseNoteProcessor
{
    public function cleanReleaseNotes(string $releaseNotes): string
    {
        // Remove links like ([5e6975e](https://github.com/prezero/hermes-flutter/commit/5e6975e54b0033270e1b0b7efd11f6f611f444cc))
        $cleanedReleaseNotes = preg_replace('/\s*\(\[[^\]]+\]\([^)]+\)\)/', '', $releaseNotes);

        // Remove leading JIRA ticket numbers like **HERMES-1234:**
        $cleanedReleaseNotes = preg_replace('/\*\*(HERMES-\d+):\*\*\s?/', '', (string) $cleanedReleaseNotes);

        // Remove links like [Link text](https://jira.example.com/browse/HERMES-1234) replacing with just the link text
        $cleanedReleaseNotes = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '$1', (string) $cleanedReleaseNotes);

        return (string) $cleanedReleaseNotes;
    }
}
