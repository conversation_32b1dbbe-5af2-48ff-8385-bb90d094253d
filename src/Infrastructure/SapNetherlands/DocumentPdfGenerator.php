<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands;

use App\Domain\Entity\Document;

class DocumentPdfGenerator
{
    private const int FONT_SIZE = 10;

    private const string FONT = 'tahoma';
    private const string FONT_STRONG = 'tahomabd';

    private const int HORIZONTAL_MARGIN = 10;

    private const int BLOCK_1_VERTICAL_POSITION = 10;
    private const int COLUMN_1_HORIZONTAL_POSITION = 10;

    private const int COLUMN_1_WIDTH = 45;
    private const int COLUMN_2_WIDTH = 140;
    private const int COLUMN_HEIGHT = 6;

    private const string INFO_LINE = 'Indien ZZS informatie van ontdoener is ontvangen dan is deze informatie beschikbaar en op te vragen bij de PZ locatie.';

    public function generateForDocument(Document $orderDocument): string
    {
        $pdf = new \TCPDF(orientation: 'P');
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        $pdf->setMargins(self::HORIZONTAL_MARGIN, 10, self::HORIZONTAL_MARGIN, true);
        $pdf->setAutoPageBreak(false);
        $pdf->AddPage();

        $pdf->setXY(self::COLUMN_1_HORIZONTAL_POSITION, self::BLOCK_1_VERTICAL_POSITION);
        $pdf->setFont(self::FONT, '', self::FONT_SIZE);

        $this->dataLine($pdf, 'Referentie', $this->getOrderDocumentTextBlock($orderDocument, 'tourRef'));

        $sender = implode(
            ' ',
            [
                $this->getOrderDocumentTextBlock($orderDocument, 'senderName'),
                $this->getOrderDocumentTextBlock($orderDocument, 'senderStreet'),
                $this->getOrderDocumentTextBlock($orderDocument, 'senderCity'),
                $this->getOrderDocumentTextBlock($orderDocument, 'senderVIHBnumber'),
            ]
        );
        $this->dataLine($pdf, 'Afzender', $sender);

        $disposer = implode(
            ' ',
            [
                $this->getOrderDocumentTextBlock($orderDocument, 'disposerName'),
                $this->getOrderDocumentTextBlock($orderDocument, 'disposerStreet'),
                $this->getOrderDocumentTextBlock($orderDocument, 'disposerCity'),
            ]
        );
        $this->dataLine($pdf, 'Ontdoener', $disposer);

        $origin = implode(
            ' ',
            [
                $this->getOrderDocumentTextBlock($orderDocument, 'originName'),
                $this->getOrderDocumentTextBlock($orderDocument, 'originStreet'),
                $this->getOrderDocumentTextBlock($orderDocument, 'originCity'),
            ]
        );
        $this->dataLine($pdf, 'Locatie van herkomst', $origin);

        $subcontractor = implode(
            ' ',
            [
                $this->getOrderDocumentTextBlock($orderDocument, 'subcontractorName'),
                $this->getOrderDocumentTextBlock($orderDocument, 'subcontractorStreet'),
                $this->getOrderDocumentTextBlock($orderDocument, 'subcontractorCity'),
                $this->getOrderDocumentTextBlock($orderDocument, 'subcontractorVIHBnumber'),
            ]
        );
        $this->dataLine($pdf, 'Uitbesteed vervoerder', $subcontractor);

        $wdplant = implode(
            ' ',
            [
                $this->getOrderDocumentTextBlock($orderDocument, 'wdplantName'),
                $this->getOrderDocumentTextBlock($orderDocument, 'wdplantStreet'),
                $this->getOrderDocumentTextBlock($orderDocument, 'wdplantCity'),
            ]
        );
        $this->dataLine($pdf, 'Locatie van bestemming', $wdplant);

        $transporter = implode(
            ' ',
            [
                $this->getOrderDocumentTextBlock($orderDocument, 'transporter'),
                $this->getOrderDocumentTextBlock($orderDocument, 'transporterName'),
                $this->getOrderDocumentTextBlock($orderDocument, 'transporterStreet'),
                $this->getOrderDocumentTextBlock($orderDocument, 'transporterCity'),
                $this->getOrderDocumentTextBlock($orderDocument, 'transporterVIHBnumber'),
            ]
        );
        $this->dataLine($pdf, 'Getransporteerd door', $transporter);

        $this->dataLine($pdf, 'Route-inzameling', $this->getOrderDocumentTextBlock($orderDocument, 'routeCollection'));
        $this->dataLine($pdf, 'Inzamelaarsregeling', $this->getOrderDocumentTextBlock($orderDocument, 'collectionScheme'));
        $this->dataLine($pdf, 'Repeterende vrachten', $this->getOrderDocumentTextBlock($orderDocument, 'repetiveLoads'));
        $this->dataLine($pdf, 'Afvalstroomnummer', $this->getOrderDocumentTextBlock($orderDocument, 'wastestreamnumber'));
        $this->dataLine($pdf, 'Gebruikelijke benaming', $this->getOrderDocumentTextBlock($orderDocument, 'commonWasteName'));
        $this->dataLine($pdf, 'Euralcode', $this->getOrderDocumentTextBlock($orderDocument, 'euralcode'));
        $this->dataLine($pdf, 'Verwerkingsmethode', $this->getOrderDocumentTextBlock($orderDocument, 'method'));
        $this->dataLine($pdf, 'Aanvang transport', $this->getOrderDocumentTextBlock($orderDocument, 'startDate'));
        $this->dataLine($pdf, 'Ontvangst transport', $this->getOrderDocumentTextBlock($orderDocument, 'receiptDate'));

        $pdf->setFont(self::FONT_STRONG, '', self::FONT_SIZE);
        $pdf->MultiCell(
            w: self::COLUMN_1_WIDTH + self::COLUMN_2_WIDTH,
            h: self::COLUMN_HEIGHT,
            txt: self::INFO_LINE,
            align: 'L',
        );

        return $pdf->Output('order-document.pdf', 'S');
    }

    private function dataLine(\TCPDF $pdf, string $title, string $value): void
    {
        $pdf->Cell(
            w: self::COLUMN_1_WIDTH,
            h: self::COLUMN_HEIGHT,
            txt: $title,
            align: 'L',
        );
        $pdf->MultiCell(
            w: self::COLUMN_2_WIDTH,
            h: self::COLUMN_HEIGHT,
            txt: $value,
            align: 'L'
        );
    }

    private function getOrderDocumentTextBlock(Document $orderDocument, string $textBlockId): string
    {
        foreach ($orderDocument->getTextBlocks() as $textBlock) {
            if ($textBlock->blockId === $textBlockId) {
                return $textBlock->text;
            }
        }

        return '';
    }
}
