<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\SapClient;

use App\Domain\Entity\Enum\Tenant;
use App\Infrastructure\SapCommon\SapClient\SapClientConfig;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class SapClientConfigNetherlands implements SapClientConfig
{
    public function __construct(
        #[Autowire(env: 'SAP_NETHERLANDS_URL')]
        private string $url,
        #[Autowire(env: 'SAP_NETHERLANDS_USERNAME')]
        private string $username,
        #[Autowire(env: 'SAP_NETHERLANDS_PASSWORD')]
        private string $password,
    ) {
    }

    public function getLogName(): string
    {
        return 'SAP-Netherlands';
    }

    public function getObjectStatusUrl(): string
    {
        return $this->url.'Status';
    }

    public function getObjectConfirmationUrl(): string
    {
        return $this->url.'ObjectConfirmation';
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getTenantShortcut(): string
    {
        return Tenant::NETHERLANDS->value;
    }
}
