<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\SapClient;

use App\Infrastructure\SapNetherlands\Dto\Output\FileUploadDto;
use App\Infrastructure\SapNetherlands\Dto\Output\TrackRecordingDto;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

readonly class SapNetherlandsDataClient
{
    public function __construct(
        private HttpClientInterface $sapNetherlandsDataClient,
        private LoggerInterface $logger,
        private NormalizerInterface $normalizer,
    ) {
    }

    public function sendDocument(
        string $file,
        string $contentType,
        FileUploadDto $fileUploadDto,
    ): void {
        $headers = [
            'X-Requested-With' => 'JSONHttpRequest',
            'Accept' => 'application/json',
            'Content-Type' => $contentType,
            'Accept-Language' => 'de-DE',
            'Idempotency-Key' => $fileUploadDto->idempotencyKey,
        ];

        try {
            $queryParams = http_build_query(array_filter([
                'tourExternalId' => $fileUploadDto->tourExternalId,
                'orderExternalId' => $fileUploadDto->orderExternalId,
                'interruptionId' => $fileUploadDto->interruptionId,
                'tasktype' => $fileUploadDto->taskType,
                'elementReferenceType' => $fileUploadDto->elementReferenceType,
            ]));
            $queryParams = '?'.$queryParams;

            $this->logger->debug(
                'Send SAP-Netherlands Files to: FileUpload'.$queryParams,
                ['headers' => $headers, 'fileUploadDto' => $fileUploadDto]
            );

            $response = $this->sapNetherlandsDataClient->request(
                method: 'POST',
                url: 'FileUpload'.$queryParams,
                options: [
                    'headers' => $headers,
                    'body' => $file,
                ],
            );

            $this->logger->debug(
                message: 'Response received from SAP Netherlands while sending files.',
                context: [
                    'responseCode' => $response->getStatusCode(),
                    'responseHeaders' => $response->getHeaders(false),
                    'responseBody' => $response->getContent(false),
                    'fileUploadDto' => $fileUploadDto,
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: 'Error received from SAP Netherlands while sending files.',
                    context: [
                        'responseCode' => $response->getStatusCode(),
                        'responseHeaders' => $response->getHeaders(false),
                        'responseBody' => $response->getContent(false),
                        'fileUploadDto' => $fileUploadDto,
                    ],
                );
            }
        } catch (ExceptionInterface $e) {
            $this->logger->critical(
                message: 'Error while sending files to SAP Netherlands.',
                context: [
                    'exception' => $e,
                    'fileUploadDto' => $fileUploadDto,
                ],
            );
        }
    }

    public function sendTrackRecording(
        TrackRecordingDto $trackRecordingDto,
    ): void {
        $headers = [
            'X-Requested-With' => 'JSONHttpRequest',
            'Content-Type' => 'application/json',
            'Idempotency-Key' => $trackRecordingDto->idempotencyKey,
        ];

        $this->logger->debug(
            'Send SAP-Netherlands TrackRecording',
            ['headers' => $headers, 'trackRecordingDto' => $trackRecordingDto],
        );

        try {
            $payload = $this->normalizer->normalize($trackRecordingDto);

            $response = $this->sapNetherlandsDataClient->request(
                method: 'POST',
                url: 'Tracks',
                options: [
                    'headers' => $headers,
                    'json' => $payload,
                ],
            );

            $this->logger->debug(
                message: 'Response received from SAP Netherlands while sending TrackRecording.',
                context: [
                    'responseCode' => $response->getStatusCode(),
                    'responseHeaders' => $response->getHeaders(false),
                    'responseBody' => $response->getContent(false),
                    'trackRecordingDto' => $trackRecordingDto,
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: 'Error received from SAP Netherlands while sending TrackRecording.',
                    context: [
                        'responseCode' => $response->getStatusCode(),
                        'responseHeaders' => $response->getHeaders(false),
                        'responseBody' => $response->getContent(false),
                        'trackRecordingDto' => $trackRecordingDto,
                    ],
                );
            }
        } catch (ExceptionInterface $e) {
            $this->logger->critical(
                message: 'Error while sending TrackRecording to SAP Netherlands.',
                context: [
                    'exception' => $e,
                    'trackRecordingDto' => $trackRecordingDto,
                ],
            );
        }
    }
}
