<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\Dto\Output;

use Symfony\Component\Uid\Uuid;

class TrackRecordingDto
{
    public string $idempotencyKey;

    /**
     * @param array<TrackLocationDto> $tracks
     */
    public function __construct(
        public string $tourExternalId,
        public ?string $equipmentExternalId,
        public array $tracks,
    ) {
        $this->idempotencyKey = Uuid::v4()->toRfc4122();
    }
}
