<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\Dto\Output;

use Symfony\Component\Uid\Uuid;

class FileUploadDto
{
    public string $idempotencyKey;

    public function __construct(
        public string $objectStoragePath,
        public string $tourExternalId,
        public string $taskType,
        public string $elementReferenceType,
        public string $elementType,
        public ?string $orderExternalId = null,
        public ?string $interruptionId = null,
    ) {
        $this->idempotencyKey = Uuid::v4()->toRfc4122();
    }
}
