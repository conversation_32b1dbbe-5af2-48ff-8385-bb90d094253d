<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\HttpEndpoint;

use App\Domain\Context\ActionContext;
use App\Infrastructure\SapGermany\Dto\Input\TourDto;
use App\Infrastructure\SapGermany\Dto\Output\TourIngested;
use App\Infrastructure\SapGermany\Service\TourService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class TourEndpoint
{
    public function __construct(
        private TourService $tourService,
        private ActionContext $actionContext,
    ) {
    }

    public function tour(TourDto $tourDto): TourIngested
    {
        $this->actionContext->disableSapSync();
        $tour = $this->tourService->upsert($tourDto);

        return new TourIngested($tour->getId());
    }

    public function invalidateTour(string $tourExternalId): void
    {
        $this->tourService->invalidate($tourExternalId);
    }

    public function ping(): void
    {
    }
}
