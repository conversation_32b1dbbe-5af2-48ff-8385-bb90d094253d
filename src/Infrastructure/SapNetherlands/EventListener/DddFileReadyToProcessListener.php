<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\TenantContext;
use App\Infrastructure\Dako\Event\DddFileReadyToProcessEvent;
use App\Infrastructure\Dako\Service\DakoService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener]
readonly class DddFileReadyToProcessListener
{
    public function __construct(
        private TenantContext $tenantContext,
        private DakoService $dakoService,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(DddFileReadyToProcessEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands()) {
            return;
        }

        $this->logger->debug(
            sprintf('DDD-File has no external upload defined for file %s process %s', $event->fileUuid, $event->dakoProcess->getId()),
        );
        $event->dakoProcess->addUploadedFile($event->fileUuid);
        $this->dakoService->checkDakoFileUploads($event->dakoProcess);
    }
}
