<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Event\StaffFocusedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: StaffFocusedEvent::NAME)]
class StaffFocusListener
{
    public function __construct(
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
        private SessionService $sessionService,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(StaffFocusedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $tour = $this->sessionService->getLastActiveTour();

        if (null === $tour) {
            $this->logger->notice('NL Staff Focus not processed - no active tour', [
                'staff' => $event->getStaff()->getId(),
            ]);

            return;
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $tour->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $tour->getExternalId(),
                subType: ObjectSubType::STAFF,
                subTypeExternalId: $event->getStaff()->getExternalId(),
                uuid: $event->getStaff()->getId(),
                status: 'user_switch',
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $event->getTimestamp(),
            )
        );
    }
}
