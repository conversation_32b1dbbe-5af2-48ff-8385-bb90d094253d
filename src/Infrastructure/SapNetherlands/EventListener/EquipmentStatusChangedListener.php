<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\EquipmentStatus;
use App\Domain\Entity\Enum\Status\TourEquipmentStatus;
use App\Domain\Entity\Equipment;
use App\Domain\Event\EquipmentStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: EquipmentStatusChangedEvent::NAME)]
readonly class EquipmentStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(EquipmentStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $equipment = $event->getEquipment();

        // Only on change to available (meaning it was in use before that, and now it's logged-out)
        if (EquipmentStatus::AVAILABLE === $equipment->getStatus()) {
            $this->reportToSapOnEquipmentLogout($equipment, $event);
        }
    }

    public function reportToSapOnEquipmentLogout(Equipment $equipment, EquipmentStatusChangedEvent $event): void
    {
        $reportedTourEquipment = null;
        $lastTour = null;
        $toursSortedByCompletionTime = $this->sessionService->getLastActiveToursSortedByCompletionTimeDescending();

        foreach ($toursSortedByCompletionTime as $tour) {
            foreach ($tour->getTourEquipments() as $tourEquipment) {
                if (
                    $tourEquipment->getEquipment()->getId() === $equipment->getId()
                    // Equipment has been used in this tour (started or completed)
                    && TourEquipmentStatus::DISPATCHED !== $tourEquipment->getStatus()
                    && TourEquipmentStatus::ABORTED !== $tourEquipment->getStatus()
                ) {
                    $reportedTourEquipment = $tourEquipment;
                    $lastTour = $tour;
                    break 2;
                }
            }
        }

        if (null === $lastTour || null === $reportedTourEquipment) {
            return;
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $lastTour->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $lastTour->getExternalId(),
                subType: ObjectSubType::EQUIPMENT,
                subTypeExternalId: $equipment->getExternalId(),
                uuid: $equipment->getId(),
                status: TourEquipmentStatus::COMPLETED->value,
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $event->getTimestamp(),
            )
        );

        $session = $this->sessionService->getActiveDeviceSession();
        $matchedSessionEquipment = null;

        foreach ($session->getSessionEquipments() as $sessionEquipment) {
            if ($sessionEquipment->getEquipment()->getId() === $equipment->getId()
                && null !== $sessionEquipment->getEnd()
            ) {
                $matchedSessionEquipment = $sessionEquipment;
                break;
            }
        }

        if (null === $matchedSessionEquipment) {
            $this->logger->critical(
                'Cannot send ObjectConfirmation to SAP when equipment has logged out, equipment not found in session.',
                ['equipmentId' => $equipment->getId()]
            );

            return;
        }

        $this->sapMessageScheduler->dispatchObjectConfirmationMessage(
            tourId: $lastTour->getId(),
            objectConfirmationDto: ObjectConfirmationDto::fromEquipmentInTour(
                tour: $lastTour,
                sessionEquipment: $matchedSessionEquipment,
                session: $session,
            ),
        );

        $reportedTourEquipment->setSentToSap(TourEquipmentStatus::COMPLETED);
    }
}
