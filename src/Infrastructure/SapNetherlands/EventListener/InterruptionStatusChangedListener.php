<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\InterruptionStatus;
use App\Domain\Event\InterruptionStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: InterruptionStatusChangedEvent::NAME)]
readonly class InterruptionStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function __invoke(InterruptionStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $interruption = $event->getInterruption();
        if ($interruption->getSentToSap() === $interruption->getStatus()) {
            return;
        }

        $interruptionTour = $interruption->getTour();

        if (null === $interruptionTour) {
            $interruptionTour = $this->sessionService->getLastActiveTour($interruption->getSession());
        }

        if (null === $interruptionTour) {
            return;
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $interruptionTour->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $interruption->getSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $interruptionTour->getExternalId(),
                subType: ObjectSubType::INTERRUPTION,
                subTypeExternalId: $interruption->getExternalId(),
                uuid: $interruption->getId(),
                status: $interruption->getStatus()->value,
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $event->getTimestamp(),
            )
        );

        if (InterruptionStatus::COMPLETED === $interruption->getStatus()) {
            $this->sapMessageScheduler->dispatchObjectConfirmationMessage(
                tourId: $interruptionTour->getId(),
                objectConfirmationDto: ObjectConfirmationDto::fromInterruptionOfTour(
                    interruption: $interruption,
                    tour: $interruptionTour,
                    session: $interruption->getSession(),
                )
            );
        }

        $interruption->setSentToSap($interruption->getStatus());
    }
}
