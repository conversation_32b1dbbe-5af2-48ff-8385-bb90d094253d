<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\TourStaffStatus;
use App\Domain\Entity\TourStaff;
use App\Domain\Event\TourStaffStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: TourStaffStatusChangedEvent::NAME)]
readonly class TourStaffStatusChangedListener
{
    public function __construct(
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private SessionService $sessionService,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function __invoke(TourStaffStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $tourStaff = $event->getTourStaff();

        // Meaning: This should happen when a tour starts (for all logged in staff in session) and if staff logs in
        // after the tour has started. And only if we have not reported that yet.
        if (
            TourStaffStatus::STARTED !== $tourStaff->getStatus()
            || $tourStaff->hasBeenSentToSap([TourStaffStatus::STARTED, TourStaffStatus::COMPLETED])
        ) {
            return;
        }

        $this->reportTourStaffStartToSap($event, $tourStaff);
    }

    private function reportTourStaffStartToSap(TourStaffStatusChangedEvent $event, TourStaff $tourStaff): void
    {
        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $tourStaff->getTour()->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $tourStaff->getTour()->getExternalId(),
                subType: ObjectSubType::STAFF,
                subTypeExternalId: $tourStaff->getStaff()->getExternalId(),
                uuid: $tourStaff->getStaff()->getId(),
                status: $tourStaff->getStatus()->value,
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $this->getTourStaffLogin($tourStaff) ?: $event->getTimestamp(),
            )
        );

        $tourStaff->setSentToSap(TourStaffStatus::STARTED);
    }

    private function getTourStaffLogin(TourStaff $tourStaff): ?\DateTimeImmutable
    {
        $session = $this->sessionService->getActiveDeviceSession();
        $user = $tourStaff->getStaff()->getUser();
        $activeTourStartTime = null;
        $previousTourEndTime = null;
        $userStartedWorkingTime = null;

        // Find the start date of the current active tour and the end date of the previous tour (if any)
        foreach ($session->getSessionTours() as $sessionTour) {
            if (null === $sessionTour->getEnd()) {
                $activeTourStartTime = $sessionTour->getStart();

                continue;
            }

            if (null === $previousTourEndTime || $previousTourEndTime < $sessionTour->getEnd()) {
                $previousTourEndTime = $sessionTour->getEnd();
            }
        }

        // Find the start date of the user
        foreach ($session->getSessionUsers() as $sessionUser) {
            if ($sessionUser->getUser() === $user && null === $sessionUser->getEnd()) {
                $userStartedWorkingTime = $sessionUser->getStart();
            }
        }

        // User hasn't started working or tour has not started, we cannot determine the start time
        if (null === $userStartedWorkingTime || null === $activeTourStartTime) {
            return null;
        }

        // If the user start is BEFORE an end date of previous tour, then send the current active tour start date
        if (null !== $previousTourEndTime && $userStartedWorkingTime < $previousTourEndTime) {
            return $activeTourStartTime;
        }

        // Otherwise send the start date of the user
        return $userStartedWorkingTime;
    }
}
