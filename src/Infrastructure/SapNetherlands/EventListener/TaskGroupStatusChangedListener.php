<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\TaskGroupStatus;
use App\Domain\Entity\Enum\Status\TaskStatus;
use App\Domain\Entity\TaskGroup;
use App\Domain\Event\TaskGroupStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: TaskGroupStatusChangedEvent::NAME)]
readonly class TaskGroupStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function __invoke(TaskGroupStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $taskGroup = $event->getTaskGroup();
        $order = $taskGroup->getOrder();
        $tour = $taskGroup->getTour();

        if (TaskGroupStatus::COMPLETED !== $taskGroup->getStatus() && TaskGroupStatus::STARTED !== $taskGroup->getStatus()) {
            return;
        }

        if (null !== $order) {
            $this->sendSapTasks(
                event: $event,
                taskGroup: $taskGroup,
                objectType: ObjectType::ORDER,
                externalId: $order->getOrderExtId(),
                tourId: $order->getTour()->getId(),
            );

            $this->sapMessageScheduler->dispatchObjectStatusMessage(
                tourId: $order->getTour()->getId(),
                objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                    session: $this->sessionService->getActiveDeviceSession(),
                    objectType: ObjectType::ORDER,
                    objectExternalId: $order->getOrderExtId(),
                    subType: ObjectSubType::ORDER_LOCATION,
                    subTypeExternalId: $taskGroup->getType()->value,
                    uuid: $taskGroup->getId(),
                    status: $taskGroup->getStatus()->value,
                    latitude: $event->getLatitude(),
                    longitude: $event->getLongitude(),
                    mileage: $event->getMileage(),
                    timestamp: $event->getTimestamp(),
                )
            );
        } elseif (null !== $tour) {
            $this->sendSapTasks(
                event: $event,
                taskGroup: $taskGroup,
                objectType: ObjectType::TOUR,
                externalId: $tour->getExternalId(),
                tourId: $tour->getId(),
            );

            $this->sapMessageScheduler->dispatchObjectStatusMessage(
                tourId: $tour->getId(),
                objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                    session: $this->sessionService->getActiveDeviceSession(),
                    objectType: ObjectType::TOUR,
                    objectExternalId: $tour->getExternalId(),
                    subType: ObjectSubType::ORDER_LOCATION,
                    subTypeExternalId: $taskGroup->getType()->value,
                    uuid: $taskGroup->getId(),
                    status: $taskGroup->getStatus()->value,
                    latitude: $event->getLatitude(),
                    longitude: $event->getLongitude(),
                    mileage: $event->getMileage(),
                    timestamp: $event->getTimestamp(),
                )
            );
        }
    }

    private function sendSapTasks(
        TaskGroupStatusChangedEvent $event,
        TaskGroup $taskGroup,
        ObjectType $objectType,
        string $externalId,
        string $tourId,
    ): void {
        if (TaskGroupStatus::COMPLETED !== $taskGroup->getStatus()) {
            return;
        }

        foreach ($taskGroup->getTaskRelations() as $taskRelation) {
            $task = $taskRelation->getTask();

            if (!$task->isSapAction() || TaskStatus::COMPLETED !== $task->getStatus()) {
                continue;
            }

            $this->sapMessageScheduler->dispatchObjectStatusMessage(
                tourId: $tourId,
                objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                    session: $this->sessionService->getActiveDeviceSession(),
                    objectType: $objectType,
                    objectExternalId: $externalId,
                    subType: ObjectSubType::ORDER_LOCATION,
                    subTypeExternalId: $taskGroup->getType()->value,
                    uuid: $task->getId(),
                    status: $task->getType(),
                    latitude: $event->getLatitude(),
                    longitude: $event->getLongitude(),
                    mileage: $event->getMileage(),
                    timestamp: $task->getCompletedAt() ?? $event->getTimestamp(),
                )
            );
        }
    }
}
