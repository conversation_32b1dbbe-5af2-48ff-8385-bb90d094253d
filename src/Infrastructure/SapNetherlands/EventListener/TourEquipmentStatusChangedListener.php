<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\TourEquipmentStatus;
use App\Domain\Entity\TourEquipment;
use App\Domain\Event\TourEquipmentStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: TourEquipmentStatusChangedEvent::NAME)]
readonly class TourEquipmentStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function __invoke(TourEquipmentStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $tourEquipment = $event->getTourEquipment();
        // Meaning: This should happen when a tour starts (for all logged in equipments in session) and if equipment has not been reported yet
        // for the tour
        if (TourEquipmentStatus::STARTED !== $tourEquipment->getStatus()
            || $tourEquipment->hasBeenSentToSap([TourEquipmentStatus::STARTED, TourEquipmentStatus::COMPLETED])) {
            return;
        }

        $this->reportEquipmentHasStartedToSap($event, $tourEquipment);
    }

    private function reportEquipmentHasStartedToSap(
        TourEquipmentStatusChangedEvent $event,
        TourEquipment $tourEquipment,
    ): void {
        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $tourEquipment->getTour()->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $tourEquipment->getTour()->getExternalId(),
                subType: ObjectSubType::EQUIPMENT,
                subTypeExternalId: $tourEquipment->getEquipment()->getExternalId(),
                uuid: $tourEquipment->getEquipment()->getId(),
                status: $tourEquipment->getStatus()->value,
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $this->getTourEquipmentLogin($tourEquipment) ?: $event->getTimestamp(),
            )
        );

        $tourEquipment->setSentToSap(TourEquipmentStatus::STARTED);
    }

    /**
     * Logic should be the following:
     * - If the equipment has been booked before the current tour has started, and this is the first tour of the day,
     *   we have to send the equipment's booking start time
     * - If the equipment is working on a second tour of the day (has been booked before the previous tour ended),
     *   we have to send the start time of the current tour
     */
    private function getTourEquipmentLogin(TourEquipment $tourEquipment): ?\DateTimeImmutable
    {
        $session = $this->sessionService->getActiveDeviceSession();
        $equipment = $tourEquipment->getEquipment();
        $activeTourStartTime = null;
        $previousTourEndTime = null;
        $equipmentBookingStartTime = null;

        // Find the start date of the current active tour and the end date of the previous tour (if any)
        foreach ($session->getSessionTours() as $sessionTour) {
            if (null === $sessionTour->getEnd()) {
                $activeTourStartTime = $sessionTour->getStart();

                continue;
            }

            if (null === $previousTourEndTime || $previousTourEndTime < $sessionTour->getEnd()) {
                $previousTourEndTime = $sessionTour->getEnd();
            }
        }

        // Find the start booking of the equipment
        foreach ($session->getSessionEquipments() as $sessionEquipment) {
            if ($sessionEquipment->getEquipment() === $equipment && null === $sessionEquipment->getEnd()) {
                $equipmentBookingStartTime = $sessionEquipment->getStart();
            }
        }

        // Equipment hasn't been booked or tour has not started, we cannot determine the start time
        if (null === $equipmentBookingStartTime || null === $activeTourStartTime) {
            return null;
        }

        // If the equipment booking start is BEFORE an end date of previous tour, then send the current active tour start date
        if (null !== $previousTourEndTime && $equipmentBookingStartTime < $previousTourEndTime) {
            return $activeTourStartTime;
        }

        // Otherwise send the booking start of the equipment
        return $equipmentBookingStartTime;
    }
}
