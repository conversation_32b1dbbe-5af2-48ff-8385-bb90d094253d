<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Event\UserSessionEndEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: UserSessionEndEvent::NAME)]
readonly class UserSessionEndListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(UserSessionEndEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $user = $event->getUser();
        $staff = $user->getStaff();

        if (null === $staff) {
            return;
        }

        $lastTour = $this->sessionService->getLastActiveTour();

        if (null === $lastTour) {
            return;
        }

        $session = $this->sessionService->getActiveDeviceSession();
        $matchedSessionUser = null;

        foreach ($session->getSessionUsers() as $sessionUser) {
            if ($sessionUser->getStaff()->getId() === $staff->getId()) {
                $matchedSessionUser = $sessionUser;
                break;
            }
        }

        if (null === $matchedSessionUser) {
            $this->logger->critical(
                'Cannot send ObjectConfirmation to SAP when staff has logged out, staff not found in session.',
                ['staffId' => $staff->getId()]
            );

            return;
        }

        $this->sapMessageScheduler->dispatchObjectConfirmationMessage(
            tourId: $lastTour->getId(),
            objectConfirmationDto: ObjectConfirmationDto::fromStaffForTour(
                tour: $lastTour,
                sessionUser: $matchedSessionUser,
                session: $this->sessionService->getActiveDeviceSession(),
            )
        );
    }
}
