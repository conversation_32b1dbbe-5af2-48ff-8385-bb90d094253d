<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\StaffStatus;
use App\Domain\Entity\Enum\Status\TourStaffStatus;
use App\Domain\Event\StaffStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: StaffStatusChangedEvent::NAME)]
readonly class StaffStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function __invoke(StaffStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $staff = $event->getStaff();

        // Meaning: Staff was working and changed to idle (happens when staff has logged out).
        if (StaffStatus::IDLE !== $staff->getStatus()) {
            return;
        }

        $reportedTourStaff = null;
        $targetTour = null;
        $toursInSession = $this->sessionService->getLastActiveToursSortedByCompletionTimeDescending();

        foreach ($toursInSession as $tour) {
            foreach ($tour->getTourStaff() as $tourStaff) {
                // Meaning: If the staff is/was part of the tour without being aborted.
                if (
                    $tourStaff->getStaff()->getId() === $staff->getId()
                    && TourStaffStatus::ABORTED !== $tourStaff->getStatus()
                    && TourStaffStatus::DISPATCHED !== $tourStaff->getStatus()
                ) {
                    $reportedTourStaff = $tourStaff;
                    $targetTour = $tour;
                    break 2;
                }
            }
        }

        if (null === $targetTour || null === $reportedTourStaff) {
            return;
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $targetTour->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $targetTour->getExternalId(),
                subType: ObjectSubType::STAFF,
                subTypeExternalId: $staff->getExternalId(),
                uuid: $staff->getId(),
                status: TourStaffStatus::COMPLETED->value,
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $event->getTimestamp(),
            )
        );

        $reportedTourStaff->setSentToSap(TourStaffStatus::COMPLETED);
    }
}
