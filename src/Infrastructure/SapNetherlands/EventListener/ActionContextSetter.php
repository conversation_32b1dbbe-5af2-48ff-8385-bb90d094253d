<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener(priority: 1)]
readonly class ActionContextSetter
{
    public function __construct(
        private ActionContext $actionContext,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();

        if (str_starts_with($request->getPathInfo(), '/ingest/sap-netherlands')) {
            $this->actionContext->setAdjustedTimestamp(new \DateTimeImmutable());
        }
    }
}
