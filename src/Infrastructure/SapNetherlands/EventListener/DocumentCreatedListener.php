<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Document;
use App\Domain\Entity\Enum\Types\DocumentType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\Events;

#[AsEntityListener(event: Events::postPersist, entity: Document::class)]
readonly class DocumentCreatedListener
{
    public function __construct(
        private TenantContext $tenantContext,
        private AsyncDispatcher $asyncDispatcher,
        private ActionContext $actionContext,
    ) {
    }

    public function __invoke(Document $document): void
    {
        if (
            !$this->tenantContext->hasTenant()
            || !$this->tenantContext->getTenant()->isNetherlands()
            || DocumentType::NL_DTD1 !== $document->getDocumentType()
            || $this->actionContext->isSystemAction()
        ) {
            return;
        }

        $this->asyncDispatcher->dispatchGenerateDocumentPdf($document);
    }
}
