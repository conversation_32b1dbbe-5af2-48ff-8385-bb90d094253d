<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\DeviceContext;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener(priority: 3)]
readonly class DeviceContextSetter
{
    public function __construct(
        private DeviceContext $deviceContext,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();

        if (str_starts_with($request->getPathInfo(), '/ingest/sap-netherlands')) {
            $this->deviceContext->setDeviceTimestamp(new \DateTimeImmutable());
        }
    }
}
