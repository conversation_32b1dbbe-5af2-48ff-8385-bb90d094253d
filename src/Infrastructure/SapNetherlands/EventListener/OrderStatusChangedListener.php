<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Event\OrderStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: OrderStatusChangedEvent::NAME)]
readonly class OrderStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function __invoke(OrderStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $order = $event->getOrder();

        // If the tour is also terminated that means that the order has been terminated as a result of terminating the
        // tour. So we don't need to send the order status to SAP.
        // Also if order is obsolete, no request is sent to SAP
        if (
            (
                OrderStatus::TERMINATED === $order->getStatus()
                && TourStatus::TERMINATED === $order->getTour()->getStatus()
            )
            || OrderStatus::OBSOLETE === $order->getStatus()
        ) {
            return;
        }

        if (OrderStatus::STARTED === $order->getStatus()) {
            $order->getTour()->setOrderFocus($order->getId());
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $order->getTour()->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::ORDER,
                objectExternalId: $order->getOrderExtId(),
                subType: null,
                subTypeExternalId: null,
                uuid: $order->getId(),
                status: $order->getStatus()->value,
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $event->getTimestamp(),
            )
        );

        if (OrderStatus::TERMINATED === $order->getStatus() || OrderStatus::COMPLETED === $order->getStatus()) {
            $this->sapMessageScheduler->dispatchObjectConfirmationMessage(
                tourId: $order->getTour()->getId(),
                objectConfirmationDto: ObjectConfirmationDto::fromOrder(
                    order: $order,
                    session: $this->sessionService->getActiveDeviceSession(),
                ),
            );
        }

        $order->setSentToSap($order->getStatus());
    }
}
