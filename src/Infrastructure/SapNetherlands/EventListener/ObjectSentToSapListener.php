<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Types\ElementType;
use App\Domain\Entity\Order;
use App\Domain\Entity\ValueObject\TrackLocation;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\TrackingRepository;
use App\Domain\ValueObject\FileDefinition;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapCommon\Dto\Output\TaskConfirmationDto;
use App\Infrastructure\SapCommon\Event\ObjectSentToSapEvent;
use App\Infrastructure\SapNetherlands\Dto\Output\FileUploadDto;
use App\Infrastructure\SapNetherlands\Dto\Output\TrackLocationDto;
use App\Infrastructure\SapNetherlands\Dto\Output\TrackRecordingDto;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: ObjectSentToSapEvent::class)]
readonly class ObjectSentToSapListener
{
    public function __construct(
        private TenantContext $tenantContext,
        private LoggerInterface $logger,
        private AsyncDispatcher $asyncDispatcher,
        private TrackingRepository $trackingRepository,
        private OrderRepository $orderRepository,
    ) {
    }

    public function __invoke(ObjectSentToSapEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands()) {
            return;
        }
        $objectConfirmationDto = $event->getSentObject();

        if ($objectConfirmationDto instanceof ObjectConfirmationDto && ObjectType::ORDER == $objectConfirmationDto->objectType) {
            $order = $this->orderRepository->find($objectConfirmationDto->uuid);
            assert($order instanceof Order);
            $orderExternalId = $objectConfirmationDto->objectExternalId;
            $tourExternalId = $order->getTour()->getExternalId();

            $this->scheduleFilesForUpload(
                dto: $objectConfirmationDto,
                tourExternalId: $tourExternalId,
                orderExternalId: $orderExternalId,
            );
        }

        if ($objectConfirmationDto instanceof ObjectConfirmationDto && ObjectType::TOUR == $objectConfirmationDto->objectType) {
            if (ObjectSubType::INTERRUPTION === $objectConfirmationDto->subType) {
                $this->scheduleFilesForUpload(
                    dto: $objectConfirmationDto,
                    tourExternalId: $objectConfirmationDto->objectExternalId,
                );
            }

            $this->sendTrackRecordings($objectConfirmationDto);
        }
    }

    private function scheduleFilesForUpload(
        ObjectConfirmationDto $dto,
        string $tourExternalId,
        ?string $orderExternalId = null,
    ): void {
        foreach ($dto->objectOrderLocations as $orderLocation) {
            foreach ($orderLocation->tasks as $task) {
                foreach ($task->elements as $element) {
                    if (
                        ElementType::PHOTO->value !== $element->elementType
                        && ElementType::SIGNATURE->value !== $element->elementType
                    ) {
                        continue;
                    }

                    foreach ($element->elementValues as $value) {
                        try {
                            $fileDefinition = FileDefinition::fromSerializedValue($value);
                        } catch (\Throwable $e) {
                            $this->logger->error(
                                'Cannot schedule file for upload, invalid serialized value',
                                [
                                    'elementValue' => $value,
                                    'exception' => $e,
                                ]
                            );
                            continue;
                        }

                        $this->asyncDispatcher->dispatchFileUploadMessage(
                            new FileUploadDto(
                                objectStoragePath: $fileDefinition->file,
                                tourExternalId: $tourExternalId,
                                taskType: $task->taskType,
                                elementReferenceType: $element->referenceType,
                                elementType: $element->elementType,
                                orderExternalId: $orderExternalId,
                                interruptionId: ObjectSubType::INTERRUPTION === $dto->subType ? $dto->uuid : null,
                            )
                        );
                    }
                }
            }
        }

        if (null === $orderExternalId) {
            return;
        }

        foreach ($dto->notes ?? [] as $note) {
            foreach ($note->orderLocations as $noteOrderLocation) {
                $this->dispatchFileUploadMessagesForSubTasks(
                    tasks: $noteOrderLocation->tasks,
                    tourExternalId: $tourExternalId,
                    orderExternalId: $orderExternalId,
                );
            }
        }

        foreach ($dto->termination->orderLocations ?? [] as $orderLocation) {
            $this->dispatchFileUploadMessagesForSubTasks(
                tasks: $orderLocation->tasks,
                tourExternalId: $tourExternalId,
                orderExternalId: $orderExternalId,
            );
        }
    }

    /**
     * @param TaskConfirmationDto[] $tasks
     */
    private function dispatchFileUploadMessagesForSubTasks(
        array $tasks,
        string $tourExternalId,
        ?string $orderExternalId = null,
    ): void {
        foreach ($tasks as $task) {
            foreach ($task->elements as $element) {
                if (
                    ElementType::PHOTO->value !== $element->elementType
                    && ElementType::SIGNATURE->value !== $element->elementType
                ) {
                    continue;
                }

                foreach ($element->elementValues as $value) {
                    try {
                        $fileDefinition = FileDefinition::fromSerializedValue($value);
                    } catch (\Throwable $e) {
                        $this->logger->error(
                            'Cannot schedule file for upload, invalid serialized value',
                            [
                                'elementValue' => $value,
                                'exception' => $e,
                            ]
                        );
                        continue;
                    }

                    $this->asyncDispatcher->dispatchFileUploadMessage(
                        new FileUploadDto(
                            objectStoragePath: $fileDefinition->file,
                            tourExternalId: $tourExternalId,
                            taskType: $task->taskType,
                            elementReferenceType: $element->referenceType,
                            elementType: $element->elementType,
                            orderExternalId: $orderExternalId,
                            interruptionId: null,
                        )
                    );
                }
            }
        }
    }

    private function sendTrackRecordings(ObjectConfirmationDto $objectConfirmationDto): void
    {
        $tourTrackRecordings = $this->trackingRepository->findBy(['tourExternalId' => $objectConfirmationDto->objectExternalId]);

        foreach ($tourTrackRecordings as $trackRecording) {
            if (null === $trackRecording->getTourExternalId()) {
                continue;
            }
            $trackLocations = array_map(
                fn (TrackLocation $trackLocation): TrackLocationDto => new TrackLocationDto(
                    latitude: strval($trackLocation->latitude),
                    longitude: strval($trackLocation->longitude),
                    timestamp: $trackLocation->timestamp->format(\DateTimeInterface::RFC3339_EXTENDED),
                ),
                $trackRecording->getTrackingData(),
            );
            $trackRecordingDto = new TrackRecordingDto(
                tourExternalId: $trackRecording->getTourExternalId(),
                equipmentExternalId: $trackRecording->getEquipmentExternalId(),
                tracks: $trackLocations,
            );

            $this->asyncDispatcher->dispatchTrackRecordingMessage($trackRecordingDto);
        }
    }
}
