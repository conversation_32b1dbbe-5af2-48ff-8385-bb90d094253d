<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\InterruptionStatus;
use App\Domain\Entity\Enum\Status\TourEquipmentStatus;
use App\Domain\Entity\Enum\Status\TourStaffStatus;
use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Entity\Enum\Types\BookingType;
use App\Domain\Entity\Tour;
use App\Domain\Entity\ValueObject\Booking;
use App\Domain\Event\TourStatusChangedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\HermesAppApi\Event\TourStatusChangedFromDeviceAsyncEvent;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectSubType;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: TourStatusChangedEvent::NAME, method: 'syncTourStatusChanged')]
#[AsEventListener(event: TourStatusChangedFromDeviceAsyncEvent::class, method: 'asyncTourStatusChanged')]
readonly class TourStatusChangedListener
{
    public function __construct(
        private SessionService $sessionService,
        private LoggerInterface $logger,
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
    ) {
    }

    public function syncTourStatusChanged(TourStatusChangedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $tour = $event->getTour();

        if (TourStatus::STARTED !== $tour->getStatus() || $tour->getStatus() === $tour->getSentToSap()) {
            return;
        }

        $this->process($tour);
    }

    public function asyncTourStatusChanged(TourStatusChangedFromDeviceAsyncEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isNetherlands() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $tour = $event->getTour();

        if (!in_array($tour->getStatus(), [TourStatus::COMPLETED, TourStatus::TERMINATED]) || $tour->getStatus() === $tour->getSentToSap()) {
            return;
        }

        $this->process($tour);
    }

    public function process(Tour $tour): void
    {
        if (TourStatus::STARTED === $tour->getStatus()) {
            $this->sendPendingSessionInterruptions($tour);
            $this->reportPreviousTourStaffEndStatuses();
            $this->reportPreviousTourEquipmentEndStatuses();
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $tour->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::TOUR,
                objectExternalId: $tour->getExternalId(),
                subType: null,
                subTypeExternalId: null,
                uuid: $tour->getId(),
                status: $tour->getStatus()->value,
                latitude: $this->actionContext->getLatitude(),
                longitude: $this->actionContext->getLongitude(),
                mileage: $this->actionContext->getMileage(),
                timestamp: $this->actionContext->getTimestamp(),
            )
        );

        if (TourStatus::COMPLETED === $tour->getStatus() || TourStatus::TERMINATED === $tour->getStatus()) {
            $this->sapMessageScheduler->dispatchObjectConfirmationMessage(
                tourId: $tour->getId(),
                objectConfirmationDto: ObjectConfirmationDto::fromTour(
                    tour: $tour,
                    session: $this->sessionService->getActiveDeviceSession(),
                )
            );
        }

        $tour->setSentToSap($tour->getStatus());
    }

    private function sendPendingSessionInterruptions(Tour $tour): void
    {
        $activeSession = $this->sessionService->getActiveDeviceSession();

        foreach ($activeSession->getInterruptions() as $interruption) {
            if (
                InterruptionStatus::COMPLETED === $interruption->getSentToSap()
                || InterruptionStatus::CREATED === $interruption->getStatus()
            ) {
                continue;
            }

            if (InterruptionStatus::STARTED !== $interruption->getSentToSap()) {
                $bookings = array_values(array_filter(
                    $interruption->getBookings(),
                    fn (Booking $booking): bool => BookingType::START === $booking->type
                ));
                $booking = count($bookings) > 0 ? $bookings[0] : null;

                if (null === $booking) {
                    $this->logger->error(
                        'Booking is not found for interruption start transition. SAP object status will not be sent.',
                        ['interruption' => $interruption->getId()],
                    );
                    continue;
                }

                $this->sapMessageScheduler->dispatchObjectStatusMessage(
                    tourId: $tour->getId(),
                    objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                        session: $this->sessionService->getActiveDeviceSession(),
                        objectType: ObjectType::TOUR,
                        objectExternalId: $tour->getExternalId(),
                        subType: ObjectSubType::INTERRUPTION,
                        subTypeExternalId: $interruption->getExternalId(),
                        uuid: $interruption->getId(),
                        status: InterruptionStatus::STARTED->value,
                        latitude: $booking->latitude,
                        longitude: $booking->longitude,
                        mileage: $booking->mileage,
                        timestamp: $booking->timestamp,
                    )
                );

                $interruption->setSentToSap(InterruptionStatus::STARTED);
            }

            if (InterruptionStatus::COMPLETED === $interruption->getStatus()) {
                $bookings = array_values(array_filter(
                    $interruption->getBookings(),
                    fn (Booking $booking): bool => BookingType::END === $booking->type
                ));
                $booking = count($bookings) > 0 ? $bookings[0] : null;

                if (null === $booking) {
                    $this->logger->error(
                        'Booking is not found for interruption end transition. SAP object status will not be sent.',
                        ['interruption' => $interruption->getId()],
                    );
                    continue;
                }

                $this->sapMessageScheduler->dispatchObjectStatusMessage(
                    tourId: $tour->getId(),
                    objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                        session: $this->sessionService->getActiveDeviceSession(),
                        objectType: ObjectType::TOUR,
                        objectExternalId: $tour->getExternalId(),
                        subType: ObjectSubType::INTERRUPTION,
                        subTypeExternalId: $interruption->getExternalId(),
                        uuid: $interruption->getId(),
                        status: InterruptionStatus::COMPLETED->value,
                        latitude: $booking->latitude,
                        longitude: $booking->longitude,
                        mileage: $booking->mileage,
                        timestamp: $booking->timestamp,
                    )
                );

                $this->sapMessageScheduler->dispatchObjectConfirmationMessage(
                    tourId: $tour->getId(),
                    objectConfirmationDto: ObjectConfirmationDto::fromInterruptionOfTour(
                        interruption: $interruption,
                        tour: $tour,
                        session: $this->sessionService->getActiveDeviceSession(),
                    )
                );

                $interruption->setSentToSap(InterruptionStatus::COMPLETED);
            }
        }
    }

    private function reportPreviousTourStaffEndStatuses(): void
    {
        $toursThisSession = $this->sessionService->getLastActiveToursSortedByCompletionTimeDescending();

        foreach ($toursThisSession as $tour) {
            // Skip tours that are not completed yet.
            foreach ($tour->getSessionTours() as $sessionTour) {
                if (null === $sessionTour->getEnd()) {
                    continue 2;
                }
            }

            foreach ($tour->getTourStaff() as $tourStaff) {
                if (TourStaffStatus::STARTED === $tourStaff->getSentToSap()) {
                    $this->sapMessageScheduler->dispatchObjectStatusMessage(
                        tourId: $tour->getId(),
                        objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                            session: $this->sessionService->getActiveDeviceSession(),
                            objectType: ObjectType::TOUR,
                            objectExternalId: $tour->getExternalId(),
                            subType: ObjectSubType::STAFF,
                            subTypeExternalId: $tourStaff->getStaff()->getExternalId(),
                            uuid: $tourStaff->getStaff()->getId(),
                            status: TourStaffStatus::COMPLETED->value,
                            latitude: $this->actionContext->getLatitude(),
                            longitude: $this->actionContext->getLongitude(),
                            mileage: $this->actionContext->getMileage(),
                            timestamp: $this->actionContext->getTimestamp(),
                        )
                    );

                    $tourStaff->setSentToSap(TourStaffStatus::COMPLETED);
                }
            }
        }
    }

    private function reportPreviousTourEquipmentEndStatuses(): void
    {
        $toursThisSession = $this->sessionService->getLastActiveToursSortedByCompletionTimeDescending();

        foreach ($toursThisSession as $tour) {
            // Skip tours that are not completed yet.
            foreach ($tour->getSessionTours() as $sessionTour) {
                if (null === $sessionTour->getEnd()) {
                    continue 2;
                }
            }

            foreach ($tour->getTourEquipments() as $tourEquipment) {
                if (TourEquipmentStatus::STARTED === $tourEquipment->getSentToSap()) {
                    $this->sapMessageScheduler->dispatchObjectStatusMessage(
                        tourId: $tour->getId(),
                        objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                            session: $this->sessionService->getActiveDeviceSession(),
                            objectType: ObjectType::TOUR,
                            objectExternalId: $tour->getExternalId(),
                            subType: ObjectSubType::EQUIPMENT,
                            subTypeExternalId: $tourEquipment->getEquipment()->getExternalId(),
                            uuid: $tourEquipment->getEquipment()->getId(),
                            status: TourEquipmentStatus::COMPLETED->value,
                            latitude: $this->actionContext->getLatitude(),
                            longitude: $this->actionContext->getLongitude(),
                            mileage: $this->actionContext->getMileage(),
                            timestamp: $this->actionContext->getTimestamp(),
                        )
                    );

                    $tourEquipment->setSentToSap(TourEquipmentStatus::COMPLETED);
                }
            }
        }
    }
}
