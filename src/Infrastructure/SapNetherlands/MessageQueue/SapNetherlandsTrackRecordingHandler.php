<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Infrastructure\SapNetherlands\SapClient\SapNetherlandsDataClient;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SapNetherlandsTrackRecordingHandler
{
    public function __construct(
        private SapNetherlandsDataClient $sapClient,
    ) {
    }

    public function __invoke(SapNetherlandsTrackRecordingMessage $message): void
    {
        $this->sapClient->sendTrackRecording($message->trackRecordingDto);
    }
}
