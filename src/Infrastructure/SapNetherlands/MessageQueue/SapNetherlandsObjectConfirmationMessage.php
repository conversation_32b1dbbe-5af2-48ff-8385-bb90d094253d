<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Domain\MessageQueue\DeviceMessage;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;

class SapNetherlandsObjectConfirmationMessage implements DeviceMessage
{
    public function __construct(
        public string $contextTourId,
        public ObjectConfirmationDto $objectConfirmationDto,
    ) {
    }
}
