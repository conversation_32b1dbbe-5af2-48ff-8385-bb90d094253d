<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Domain\MessageQueue\DeviceMessage;
use App\Domain\MessageQueue\RequireActionContext;
use App\Domain\MessageQueue\RequireUserIdentifier;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;

readonly class SapNetherlandsObjectStatusMessage implements
    DeviceMessage,
    RequireUserIdentifier,
    RequireActionContext
{
    public function __construct(
        public string $contextTourId,
        public ObjectStatusDto $objectStatusDto,
    ) {
    }
}
