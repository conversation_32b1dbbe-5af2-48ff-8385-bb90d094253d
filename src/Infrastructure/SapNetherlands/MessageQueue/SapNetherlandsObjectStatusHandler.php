<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Infrastructure\SapCommon\Exception\SapCallException;
use App\Infrastructure\SapCommon\SapClient\SapClient;
use App\Infrastructure\SapCommon\Service\SapRetryService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SapNetherlandsObjectStatusHandler
{
    public function __construct(
        private SapClient $sapClient,
        private LoggerInterface $logger,
        private SapRetryService $sapRetryService,
    ) {
    }

    public function __invoke(SapNetherlandsObjectStatusMessage $message): void
    {
        try {
            $this->sapClient->sendObjectStatus($message->objectStatusDto);
        } catch (SapCallException $e) {
            $this->logger->error(
                sprintf('sap-call-failed: blocking sap calls for tour %s', $message->contextTourId),
                ['exception' => $e],
            );
            $this->sapRetryService->blockTourSapCalls($message->contextTourId);
            $this->sapRetryService->addToSapFailQueue(
                contextTourId: $message->contextTourId,
                sapCallDto: $message->objectStatusDto,
            );
        }
    }
}
