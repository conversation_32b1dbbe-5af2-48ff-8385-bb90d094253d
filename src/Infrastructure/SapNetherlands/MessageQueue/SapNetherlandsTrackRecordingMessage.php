<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Domain\MessageQueue\AsyncMessage;
use App\Infrastructure\SapNetherlands\Dto\Output\TrackRecordingDto;

class SapNetherlandsTrackRecordingMessage implements AsyncMessage
{
    public function __construct(
        public string $contextTourId,
        public TrackRecordingDto $trackRecordingDto,
    ) {
    }
}
