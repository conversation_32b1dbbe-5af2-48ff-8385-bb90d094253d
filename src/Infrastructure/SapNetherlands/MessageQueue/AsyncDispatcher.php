<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Domain\Entity\Document;
use App\Domain\MessageQueue\MessageDispatcherInterface;
use App\Infrastructure\SapCommon\Dto\Output\ObjectConfirmationDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Service\SapRetryService;
use App\Infrastructure\SapNetherlands\Dto\Output\FileUploadDto;
use App\Infrastructure\SapNetherlands\Dto\Output\TrackRecordingDto;
use Psr\Log\LoggerInterface;

readonly class AsyncDispatcher
{
    public function __construct(
        private MessageDispatcherInterface $messageDispatcher,
        private SapRetryService $sapRetryService,
        private LoggerInterface $logger,
    ) {
    }

    public function dispatchGenerateDocumentPdf(Document $document): void
    {
        $this->messageDispatcher->dispatch(
            new SapNetherlandsGenerateDocumentPdfMessage(documentId: $document->getId()),
        );
    }

    public function dispatchObjectStatusMessage(string $tourId, ObjectStatusDto $objectStatusDto): void
    {
        if ($this->sapRetryService->tourSapCallsAreBlocked($tourId)) {
            $this->logger->error(
                sprintf('sap-call-failed: tour is blocked %s - queueing call', $tourId),
            );
            $this->sapRetryService->addToSapFailQueue(
                contextTourId: $tourId,
                sapCallDto: $objectStatusDto,
            );

            return;
        }
        $this->messageDispatcher->dispatch(
            new SapNetherlandsObjectStatusMessage(
                contextTourId: $tourId,
                objectStatusDto: $objectStatusDto,
            ),
        );
    }

    public function dispatchFileUploadMessage(FileUploadDto $fileUploadDto): void
    {
        $this->messageDispatcher->dispatch(
            new SapNetherlandsFileUploadMessage(fileUploadDto: $fileUploadDto),
        );
    }

    public function dispatchObjectConfirmationMessage(string $tourId, ObjectConfirmationDto $objectConfirmationDto): void
    {
        if ($this->sapRetryService->tourSapCallsAreBlocked($tourId)) {
            $this->logger->error(
                sprintf('sap-call-failed: tour is blocked %s - queueing call', $tourId),
            );
            $this->sapRetryService->addToSapFailQueue(
                contextTourId: $tourId,
                sapCallDto: $objectConfirmationDto,
            );

            return;
        }
        $this->messageDispatcher->dispatch(
            new SapNetherlandsObjectConfirmationMessage(
                contextTourId: $tourId,
                objectConfirmationDto: $objectConfirmationDto,
            )
        );
    }

    public function dispatchTrackRecordingMessage(TrackRecordingDto $trackRecordingDto): void
    {
        $this->messageDispatcher->dispatch(
            new SapNetherlandsTrackRecordingMessage(
                contextTourId: $trackRecordingDto->tourExternalId,
                trackRecordingDto: $trackRecordingDto
            ),
        );
    }
}
