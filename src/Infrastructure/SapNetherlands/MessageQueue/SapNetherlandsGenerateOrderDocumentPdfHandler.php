<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\AdditionalInformationSource;
use App\Domain\Entity\Enum\Types\DescriptionFileType;
use App\Domain\Entity\ValueObject\AdditionalInformation;
use App\Domain\Repository\DocumentRepository;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\TourRepository;
use App\Domain\Services\ObjectStorage\FileObject;
use App\Domain\Services\ObjectStorage\ObjectMetadata;
use App\Domain\Services\ObjectStorage\ObjectPrefix;
use App\Domain\Services\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Services\ObjectStorage\ObjectStorageException;
use App\Infrastructure\SapNetherlands\DocumentPdfGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SapNetherlandsGenerateOrderDocumentPdfHandler
{
    public function __construct(
        private DocumentPdfGenerator $documentPdfGenerator,
        private ObjectRepositoryInterface $objectRepository,
        private TenantContext $tenantContext,
        private DocumentRepository $documentRepository,
        private OrderRepository $orderRepository,
        private TourRepository $tourRepository,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @throws ObjectStorageException
     */
    public function __invoke(SapNetherlandsGenerateDocumentPdfMessage $message): void
    {
        $document = $this->documentRepository->find($message->documentId);

        if (null === $document) {
            $this->logger->debug(
                'Cannot generate Netherlands document PDF because document not found.',
                ['documentId' => $message->documentId],
            );

            return;
        }

        $identifier = ObjectPrefix::APP_DOCUMENTS->value.$document->getId();
        $fileObject = new FileObject(
            identifier: $identifier,
            content: $this->documentPdfGenerator->generateForDocument($document),
            mimeType: 'application/pdf',
            objectMetadata: new ObjectMetadata(
                tenantIdentifier: $document->getTenant()->value,
            )
        );

        try {
            $this->objectRepository->store($fileObject);
        } catch (ObjectStorageException $e) {
            $this->logger->error(
                'Error storing document in object storage',
                [
                    'message' => $message,
                    'exception' => $e,
                ]
            );

            throw $e;
        }

        $document->setDocumentPath($this->tenantContext->getTenant()->value.'/'.$identifier);

        if (null !== $document->getOrderId()) {
            $order = $this->orderRepository->find($document->getOrderId());

            if (null === $order) {
                $this->logger->error(
                    'Document order not found in database',
                    [
                        'orderDocumentId' => $message->documentId,
                        'orderId' => $document->getOrderId(),
                    ]
                );

                return;
            }

            $order->addAdditionalInformation(
                new AdditionalInformation(
                    text: $document->getDocumentName() ?? '{{orderDocument}}',
                    sequence: 0,
                    alsoFrontView: true,
                    icon: 'feed_outlined',
                    descriptionFile: $document->getDocumentPath(),
                    descriptionFileType: DescriptionFileType::PDF,
                    descriptionFileName: $document->getDocumentName(),
                    source: AdditionalInformationSource::DOCUMENT,
                ),
            );
        }

        if (null !== $document->getTourId()) {
            $tour = $this->tourRepository->find($document->getTourId());

            if (null === $tour) {
                $this->logger->error(
                    'Document tour not found in database',
                    [
                        'orderDocumentId' => $message->documentId,
                        'tourId' => $document->getTourId(),
                    ]
                );

                return;
            }

            $tour->addAdditionalInformation(
                new AdditionalInformation(
                    text: $document->getDocumentName() ?? '{{tourDocument}}',
                    sequence: 0,
                    alsoFrontView: true,
                    icon: 'feed_outlined',
                    descriptionFile: $document->getDocumentPath(),
                    descriptionFileType: DescriptionFileType::PDF,
                    descriptionFileName: $document->getDocumentName(),
                    source: AdditionalInformationSource::DOCUMENT,
                ),
            );
        }
    }
}
