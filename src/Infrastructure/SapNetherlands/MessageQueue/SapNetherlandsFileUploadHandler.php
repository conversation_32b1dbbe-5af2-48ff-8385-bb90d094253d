<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\MessageQueue;

use App\Domain\Entity\Enum\Types\ElementType;
use App\Domain\Services\ObjectStorage\FileObject;
use App\Domain\Services\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Services\ObjectStorage\ObjectStorageException;
use App\Infrastructure\SapNetherlands\SapClient\SapNetherlandsDataClient;
use Intervention\Image\ImageManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SapNetherlandsFileUploadHandler
{
    public function __construct(
        private ObjectRepositoryInterface $objectRepository,
        private SapNetherlandsDataClient $sapNetherlandsDataClient,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(SapNetherlandsFileUploadMessage $message): void
    {
        $fileUploadDto = $message->fileUploadDto;

        try {
            $fileObject = $this->objectRepository->get($fileUploadDto->objectStoragePath);
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Failed to get file from object storage for Netherlands File upload',
                ['exception' => $e, 'file_path' => $fileUploadDto->objectStoragePath]
            );

            throw new \RuntimeException('Failed to get file from object storage for Netherlands File upload', $e->getCode(), $e);
        }

        if (null === $fileObject) {
            $this->logger->critical(
                'File not found in object storage for Netherlands File upload',
                ['file_path' => $fileUploadDto->objectStoragePath]
            );

            throw new \RuntimeException('File not found in object storage for Netherlands File upload');
        }

        $fileObject = $this->convertNetherlandsFile($fileObject, $fileUploadDto->elementType);

        $this->sapNetherlandsDataClient->sendDocument(
            file: $fileObject->content,
            contentType: $fileObject->mimeType,
            fileUploadDto: $fileUploadDto
        );
    }

    private function convertNetherlandsFile(FileObject $orgFile, string $elementType): FileObject
    {
        if (ElementType::SIGNATURE->value !== $elementType) {
            return $orgFile;
        }

        $this->logger->debug(sprintf('converting signature file for Netherlands: %s', $orgFile->identifier));
        $image = ImageManager::gd()->read($orgFile->content)->toJpeg();

        return new FileObject(
            identifier: $orgFile->identifier,
            content: $image->toString(),
            lastModified: $orgFile->lastModified,
            mimeType: $image->mimetype(),
            objectMetadata: $orgFile->objectMetadata,
        );
    }
}
