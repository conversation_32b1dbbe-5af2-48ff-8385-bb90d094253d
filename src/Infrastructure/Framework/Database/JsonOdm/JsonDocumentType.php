<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Database\JsonOdm;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\JsonType;
use Vuryss\Serializer\SerializerInterface;

final class JsonDocumentType extends JsonType
{
    public const string NAME = 'json_document';

    private SerializerInterface $serializer;

    public function setSerializer(SerializerInterface $serializer): void
    {
        $this->serializer = $serializer;
    }

    private function getSerializer(): SerializerInterface
    {
        return $this->serializer;
    }

    #[\Override]
    public function convertToDatabaseValue(mixed $value, AbstractPlatform $platform): ?string
    {
        if (null === $value) {
            return null;
        }

        return $this->getSerializer()->serialize($value, 'json');
    }

    #[\Override]
    public function convertToPHPValue(mixed $value, AbstractPlatform $platform): mixed
    {
        if ('' === $value || !is_string($value)) {
            return null;
        }

        return $this->getSerializer()->deserialize($value, type: '', format: 'json');
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }

    public function getName(): string
    {
        return self::NAME;
    }
}
