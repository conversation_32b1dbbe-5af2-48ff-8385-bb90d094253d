<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Database\Command;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Tenant;
use App\Domain\Repository\AccessibleTaskRepository;
use App\Domain\Repository\MastertourProgressRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:database:test',
    description: 'clears position-data',
)]
class Test extends Command
{
    public function __construct(
        private readonly TenantContext $tenantContext,
        private readonly AccessibleTaskRepository $accessibleTaskRepository,
        private readonly MasterTourProgressRepository $masterTourProgressRepository,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->test1();
        $this->test2();

        return Command::SUCCESS;
    }

    public function test1(): void
    {
        $this->tenantContext->setTenant(Tenant::GERMANY);

        echo 'Testing that lazy ghost is not initialized until we read the array...'.PHP_EOL;
        echo 'Fetching task.'.PHP_EOL;

        $task = $this->accessibleTaskRepository->find('eae0772e-060e-4102-a8c4-d25681c9ca64');
        assert(null !== $task);

        echo 'Task fetched, fetching elements.'.PHP_EOL;

        $elements = $task->getElements();

        echo 'Elements fetched. Iterating...'.PHP_EOL;

        foreach ($elements as $element) {
            echo 'Found element: '.$element->id.PHP_EOL;
        }

        echo 'Done.'.PHP_EOL.PHP_EOL.PHP_EOL;

        echo 'Testing that flush does not initialize lazy ghost...'.PHP_EOL;

        $task = $this->accessibleTaskRepository->find('a4177f62-6125-44db-a0a0-faf581ee4c4a');
        assert(null !== $task);

        echo 'Task fetched, fetching elements.'.PHP_EOL;

        $elements = $task->getElements();

        echo 'No element iteration. Change different task property...'.PHP_EOL;

        $task->setName(uniqid());

        $this->entityManager->flush();

        echo 'Done.'.PHP_EOL;
    }

    public function test2(): void
    {
        $this->tenantContext->setTenant(Tenant::GERMANY);

        echo 'Testing that lazy ghost is not initialized until we read the array...'.PHP_EOL;
        echo 'Fetching entity.'.PHP_EOL;

        $entity = $this->masterTourProgressRepository->find('85e91aa5-93d1-4ef2-8df2-2bae2cd20617');
        assert(null !== $entity);

        echo 'Entity fetched, fetching property.'.PHP_EOL;

        $coordinate = $entity->getCoordinate();

        echo 'Property fetched. Getting inside data...'.PHP_EOL;

        echo 'Latitude: '.$coordinate->latitude.PHP_EOL;

        echo 'Done.'.PHP_EOL.PHP_EOL.PHP_EOL;



        echo 'Testing that flush does not initialize lazy ghost...'.PHP_EOL;

        $entity = $this->masterTourProgressRepository->find('d058ba8c-cf56-4e8b-bfca-56896c752b9e');
        assert(null !== $entity);

        echo 'Entity fetched, fetching property.'.PHP_EOL;

        $entity->getCoordinate();

        echo 'No property iteration. Change different task property...'.PHP_EOL;

        $entity->setModifiedAt(new \DateTime());

        $this->entityManager->flush();

        echo 'Done.'.PHP_EOL.PHP_EOL.PHP_EOL;


        echo 'Testing lazy ghost update...'.PHP_EOL;

        $entity = $this->masterTourProgressRepository->find('4bed1eb1-672a-44da-9c84-cf1ab723f1c1');
        assert(null !== $entity);

        echo 'Entity fetched, updating property.'.PHP_EOL;

        $entity->setCoordinate(1, 1);

        $this->entityManager->flush();

        echo 'Done.'.PHP_EOL;
    }
}
