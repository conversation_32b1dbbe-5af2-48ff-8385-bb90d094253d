App\Domain\Entity\ValueObject\AdditionalInformation:
  pz_sub_DefAddInfo_SUB_DE_1:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_2:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Remove_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_3:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_4:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_5:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_6:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_7:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_8:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_9:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_10:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_11:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_12:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_sub_DefAddInfo_SUB_DE_13:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false }
