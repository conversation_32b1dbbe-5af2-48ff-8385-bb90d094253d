App\Domain\VehicleInspection\Config\EquipmentComponentConfig:
  nl_EquipmentComponentConf_1:
    __construct: { title: '{{VIR_tires_pressure_profile_damage}}', label: '{{VIR_label_tires_pressure_profile_damage}}', externalId: SPE1, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_2:
    __construct: { title: '{{VIR_treads_steps_handles}}', label: '{{VIR_label_treads_steps_handles}}', externalId: SPE131, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_3:
    __construct: { title: '{{VIR_windshield_dashboard}}', label: '{{VIR_label_windshield_dashboard}}', externalId: SPE132, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_4:
    __construct: { title: '{{VIR_seats_seatbelts}}', label: '{{VIR_label_seats_seatbelts}}', externalId: SPE133, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_5:
    __construct: { title: '{{VIR_condition_mirrors}}', label: '{{VIR_label_condition_mirrors}}', externalId: SPE134, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_6:
    __construct: { title: '{{VIR_oil_cooland_level}}', label: '{{VIR_label_oil_cooland_level}}', externalId: SPE2, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_7:
    __construct: { title: '{{VIR_brake_system_air_suspension}}', label: '{{VIR_label_brake_system_air_suspension}}', externalId: SPE3, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_8:
    __construct: { title: '{{VIR_brake_system}}', label: '{{VIR_label_brake_system}}', externalId: SPE4, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_9:
    __construct: { title: '{{VIR_lighting_and_warning_lights}}', label: '{{VIR_label_lighting_and_warning_lights}}', externalId: SPE6, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_10:
    __construct: { title: '{{VIR_cleanliness_of_cabin_and_others}}', label: '{{VIR_label_cleanliness_of_cabin_and_others}}', externalId: SPE9, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_11:
    __construct: { title: '{{VIR_cab_front}}', label: '{{VIR_label_cab_front}}', externalId: SPE135, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_12:
    __construct: { title: '{{VIR_cabin_left_side}}', label: '{{VIR_label_cabin_left_side}}', externalId: SPE136, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_13:
    __construct: { title: '{{VIR_cabin_right_side}}', label: '{{VIR_label_cabin_right_side}}', externalId: SPE137, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_14:
    __construct: { title: '{{VIR_construction_right_side}}', label: '{{VIR_label_construction_right_side}}', externalId: SPE138, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_15:
    __construct: { title: '{{VIR_construction_left_side}}', label: '{{VIR_label_construction_left_side}}', externalId: SPE139, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_16:
    __construct: { title: '{{VIR_rear_end}}', label: '{{VIR_label_rear_end}}', externalId: SPE140, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_17:
    __construct: { title: '{{VIR_construction_of_other_parts_and_damage}}', label: '{{VIR_label_construction_of_other_parts_and_damage}}', externalId: SPE141, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_18:
    __construct: { title: '{{VIR_windows}}', label: '{{VIR_label_windows}}', externalId: SPE142, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_19:
    __construct: { title: '{{VIR_trailer_semi_trailer}}', label: '{{VIR_label_trailer_semi_trailer}}', externalId: SPE143, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_20:
    __construct: { title: '{{VIR_warning_lights}}', label: '{{VIR_label_warning_lights}}', externalId: SPE20, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_21:
    __construct: { title: '{{VIR_hydraulic_oil_level_and_hoses}}', label: '{{VIR_label_hydraulic_oil_level_and_hoses}}', externalId: SPE21, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_22:
    __construct: { title: '{{VIR_beacon_flash_and_work_lights}}', label: '{{VIR_label_beacon_flash_and_work_lights}}', externalId: SPE22, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_23:
    __construct: { title: '{{VIR_running_boards_km_limit_reverse_blocking_and_yoke_stop_stop}}', label: '{{VIR_label_running_boards_km_limit_reverse_blocking_and_yoke_stop_stop}}', externalId: SPE23, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_24:
    __construct: { title: '{{VIR_chains_and_fasteners}}', label: '{{VIR_label_chains_and_fasteners}}', externalId: SPE24, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_25:
    __construct: { title: '{{VIR_reversing_camera}}', label: '{{VIR_label_reversing_camera}}', externalId: SPE25, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_26:
    __construct: { title: '{{VIR_emergency_stops}}', label: '{{VIR_label_emergency_stops}}', externalId: SPE26, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_27:
    __construct: { title: '{{VIR_reversing_signal}}', label: '{{VIR_label_reversing_signal}}', externalId: SPE27, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_28:
    __construct: { title: '{{VIR_folding_bumper}}', label: '{{VIR_label_folding_bumper}}', externalId: SPE28, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_29:
    __construct: { title: '{{VIR_other}}', label: '{{VIR_label_other}}', externalId: SPE29, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_30:
    __construct: { title: '{{VIR_trailer_tires_pressure_profile_damage}}', label: '{{VIR_label_trailer_tires_pressure_profile_damage}}', externalId: SPE30, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_31:
    __construct: { title: '{{VIR_trailer_brake_system_air_suspension}}', label: '{{VIR_label_trailer_brake_system_air_suspension}}', externalId: SPE31, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_32:
    __construct: { title: '{{VIR_trailer_lights}}', label: '{{VIR_label_trailer_lights}}', externalId: SPE32, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_33:
    __construct: { title: '{{VIR_trailer_abs_braking_system}}', label: '{{VIR_label_trailer_abs_braking_system}}', externalId: SPE33, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_34:
    __construct: { title: '{{VIR_trailer_chains_and_fasteners}}', label: '{{VIR_label_trailer_chains_and_fasteners}}', externalId: SPE34, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_35:
    __construct: { title: '{{VIR_trailer_cargo_secured}}', label: '{{VIR_label_trailer_cargo_secured}}', externalId: SPE35, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_36:
    __construct: { title: '{{VIR_trailer_lighting_and_abs_air_connections}}', label: '{{VIR_label_trailer_lighting_and_abs_air_connections}}', externalId: SPE36, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_37:
    __construct: { title: '{{VIR_parking_brake_wheel_chocks}}', label: '{{VIR_label_parking_brake_wheel_chocks}}', externalId: SPE37, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_38:
    __construct: { title: '{{VIR_trailer_damage}}', label: '{{VIR_label_trailer_damage}}', externalId: SPE38, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  nl_EquipmentComponentConf_39:
    __construct: { title: '{{VIR_trailer_other}}', label: '{{VIR_label_trailer_other}}', externalId: SPE39, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
