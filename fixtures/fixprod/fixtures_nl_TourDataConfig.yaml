App\Domain\Entity\TourDataConfig:
  nl_TourConf_NL_1:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_2:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ6170)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_3:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ6300)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_4:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ6400)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_5:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ6800)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_6:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ6900)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_7:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ7300)>'
    tenant: '<getNetherlandsTenant()>'
  nl_TourConf_NL_8:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ6150)>'
    tenant: '<getNetherlandsTenant()>'
