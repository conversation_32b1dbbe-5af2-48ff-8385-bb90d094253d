App\Domain\Entity\DefaultInterruption:
  nl_DefInt_NL_1:
    description: '{{Break}}'
    externalId: 'BREAK'
    id: 'd903e005-7a41-47ab-81f1-664ece554a37'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_2:
    description: '{{Waiting_time}}'
    externalId: 'WAIT'
    id: '60e508f1-1ad2-4540-bbda-e69a68fc5c2e'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_3:
    description: '{{Other}}'
    externalId: 'COM50'
    id: '5d3e0d72-a8c7-4deb-b466-e75f4318a490'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::OTHER)>'
  nl_DefInt_NL_4:
    description: '{{Obsolet}}'
    externalId: '123'
    id: 'a21ed90e-2217-4473-944d-e8de0c8457bd'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_5:
    description: '{{Breakdown}}'
    externalId: 'DEFEKT'
    id: '424d9f25-6360-4535-af88-98c61226ae4c'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_6:
    description: '{{workshop_maintenance}}'
    externalId: 'WARTUNG'
    id: '3cf7aefd-72b9-4e97-9af5-5267466a6021'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::GARAGE)>'
  nl_DefInt_NL_7:
    description: '{{Traffic_jam}}'
    externalId: 'VERKEHR'
    id: '5122c7b3-d311-4ffc-a58c-da28f8937c4c'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::TRAFFICJAM)>'
  nl_DefInt_NL_8:
    description: '{{Trailer_handling}}'
    externalId: 'COM45'
    id: 'b32c934e-ff7f-4ad1-91fa-74ebef572c89'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_9:
    description: '{{Fueling}}'
    externalId: 'REFUELING'
    id: 'fd30e846-d4ec-41ae-8785-e788295665b9'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::REFUEL)>'
  nl_DefInt_NL_10:
    description: '{{Washing}}'
    externalId: 'COM48'
    id: '6fd5bcc5-5acb-4472-b78d-5829ed138e78'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_11:
    description: '{{Load_Unload}}'
    externalId: 'COM46'
    id: '6dcf33c0-5e1d-491d-ac1b-5f705f11ecd6'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  nl_DefInt_NL_12:
    description: '{{Driver_meeting}}'
    externalId: 'COM29'
    id: '85bb146a-db0a-432c-864b-5a22dbc81b87'
    tenant: '<getNetherlandsTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
