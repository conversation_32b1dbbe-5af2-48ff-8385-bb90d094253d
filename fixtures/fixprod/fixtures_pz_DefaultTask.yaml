App\Domain\Entity\DefaultTask:
  pz_DefTask_1:
    name: '{{Approach_selection}}'
    id: '2bbbd00a-71a0-11ee-b962-0242ac120002'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_1_1']
  pz_DefTask_2:
    name: '{{Arrival}}'
    id: '2bbbd136-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_1']
  pz_DefTask_3:
    name: '{{Ready}}'
    id: '2bbbd244-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_1']
  pz_DefTask_4:
    name: '{{Arrival}}'
    id: '2bbbd352-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_2']
  pz_DefTask_5:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '2bbbd456-71a0-11ee-b962-0242ac120002'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_1']
  pz_DefTask_6:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbbd82a-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_1']
  pz_DefTask_7:
    name: '{{Number_of_containers}}'
    id: '2bbbd960-71a0-11ee-b962-0242ac120002'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_1']
  pz_DefTask_8:
    name: '{{Ready}}'
    id: '2bbbda82-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_2']
  pz_DefTask_9:
    name: '{{Arrival}}'
    id: '2bbbdb9a-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_3']
  pz_DefTask_10:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '2bbbdca8-71a0-11ee-b962-0242ac120002'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_1', '@pz_DefElem_8_1', '@pz_DefElem_9_1', '@pz_DefElem_10_1']
  pz_DefTask_11:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '2bbbddb6-71a0-11ee-b962-0242ac120002'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_2']
  pz_DefTask_12:
    name: '{{Number_of_containers}}'
    id: '2bbbdec4-71a0-11ee-b962-0242ac120002'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_2']
  pz_DefTask_13:
    name: '{{Ready}}'
    id: '2bbbe18a-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_3']
  pz_DefTask_14:
    name: '{{Arrival}}'
    id: '2bbbe2ac-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_4']
  pz_DefTask_15:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2bbbe3ba-71a0-11ee-b962-0242ac120002'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_2', '@pz_DefElem_8_2', '@pz_DefElem_9_2', '@pz_DefElem_10_2']
  pz_DefTask_16:
    name: '{{Ready}}'
    id: '2bbbe4c8-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_21_1']
  pz_DefTask_17:
    name: '{{Arrival}}'
    id: '2bbbe5cc-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_5']
  pz_DefTask_18:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbbeb3a-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_2']
  pz_DefTask_19:
    name: '{{Ready}}'
    id: '2bbbed56-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_4']
  pz_DefTask_20:
    name: '{{Approach_selection}}'
    id: '2bbbef40-71a0-11ee-b962-0242ac120002'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_11_1']
  pz_DefTask_21:
    name: '{{Arrival}}'
    id: '2bbbf10c-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_6']
  pz_DefTask_22:
    name: '{{Ready}}'
    id: '2bbbf22e-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_5']
  pz_DefTask_23:
    name: '{{Ready}}'
    id: '2bbbf33c-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_6']
  pz_DefTask_24:
    name: '{{Arrival}}'
    id: '2bbbf6f2-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_7']
  pz_DefTask_25:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbbf81e-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_3']
  pz_DefTask_26:
    name: '{{Ready}}'
    id: '2bbbf92c-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_7']
  pz_DefTask_27:
    name: '{{Arrival}}'
    id: '2bbbfa3a-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_8']
  pz_DefTask_28:
    name: '{{Ready}}'
    id: '2bbbfb48-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_8']
  pz_DefTask_29:
    name: '{{Arrival}}'
    id: '2bbbfc4c-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_9']
  pz_DefTask_30:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc002a-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_4']
  pz_DefTask_31:
    name: '{{Ready}}'
    id: '2bbc0188-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_9']
  pz_DefTask_32:
    name: '{{Ready}}'
    id: '2bbc02a0-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_22_1']
  pz_DefTask_33:
    name: '{{Arrival}}'
    id: '2bbc03a4-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_10']
  pz_DefTask_34:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc04b2-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_5']
  pz_DefTask_35:
    name: '{{Ready}}'
    id: '2bbc05ca-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_10']
  pz_DefTask_36:
    name: '{{Number_of_containers}}'
    id: '2bbc06e2-71a0-11ee-b962-0242ac120002'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_3']
  pz_DefTask_37:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '2bbc07fa-71a0-11ee-b962-0242ac120002'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_3', '@pz_DefElem_8_3', '@pz_DefElem_9_3', '@pz_DefElem_10_3']
  pz_DefTask_38:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '2bbc0b9c-71a0-11ee-b962-0242ac120002'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_3']
  pz_DefTask_39:
    name: '{{Arrival}}'
    id: '2bbc0d18-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_11']
  pz_DefTask_40:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2bbc0e3a-71a0-11ee-b962-0242ac120002'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_4', '@pz_DefElem_8_4', '@pz_DefElem_9_4', '@pz_DefElem_10_4']
  pz_DefTask_41:
    name: '{{Approach_selection}}'
    id: '2bbc0f66-71a0-11ee-b962-0242ac120002'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_12_1']
  pz_DefTask_42:
    name: '{{Arrival}}'
    id: '2bbc107e-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_12']
  pz_DefTask_43:
    name: '{{Ready}}'
    id: '2bbc118c-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_11']
  pz_DefTask_44:
    name: '{{Ready}}'
    id: '2bbc129a-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_12']
  pz_DefTask_45:
    name: '{{Arrival}}'
    id: '2bbc1556-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_13']
  pz_DefTask_46:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc16b4-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_6']
  pz_DefTask_47:
    name: '{{Ready}}'
    id: '2bbc1812-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_13']
  pz_DefTask_48:
    name: '{{Arrival}}'
    id: '2bbc1948-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_14']
  pz_DefTask_49:
    name: '{{Ready}}'
    id: '2bbc1a56-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_14']
  pz_DefTask_50:
    name: '{{Arrival}}'
    id: '2bbc1d08-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_15']
  pz_DefTask_51:
    name: '{{Ready}}'
    id: '2bbc1e20-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_15']
  pz_DefTask_52:
    name: '{{Arrival}}'
    id: '2bbc215e-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_16']
  pz_DefTask_53:
    name: '{{Ready}}'
    id: '2bbc22da-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_16']
  pz_DefTask_54:
    name: '{{Arrival}}'
    id: '2bbc23f2-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_17']
  pz_DefTask_55:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc26ae-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_7']
  pz_DefTask_56:
    name: '{{Ready}}'
    id: '2bbc28ca-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_17']
  pz_DefTask_57:
    name: '{{Ready}}'
    id: '2bbc29d8-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_23_1']
  pz_DefTask_58:
    name: '{{Arrival}}'
    id: '2bbc2ae6-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_18']
  pz_DefTask_59:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc2e74-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_8']
  pz_DefTask_60:
    name: '{{Ready}}'
    id: '2bbc2fa0-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_18']
  pz_DefTask_61:
    name: '{{Number_of_containers}}'
    id: '2bbc30ae-71a0-11ee-b962-0242ac120002'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_4']
  pz_DefTask_62:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '2bbc31c6-71a0-11ee-b962-0242ac120002'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_5', '@pz_DefElem_8_5', '@pz_DefElem_9_5', '@pz_DefElem_10_5']
  pz_DefTask_63:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '2bbc32d4-71a0-11ee-b962-0242ac120002'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_4']
  pz_DefTask_64:
    name: '{{Arrival}}'
    id: '2bbc33ec-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_19']
  pz_DefTask_65:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2bbc3504-71a0-11ee-b962-0242ac120002'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_6', '@pz_DefElem_8_6', '@pz_DefElem_9_6', '@pz_DefElem_10_6']
  pz_DefTask_66:
    name: '{{Approach_selection}}'
    id: '2bbc361c-71a0-11ee-b962-0242ac120002'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_13_1']
  pz_DefTask_67:
    name: '{{Arrival}}'
    id: '2bbc3a2c-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_20']
  pz_DefTask_68:
    name: '{{Ready}}'
    id: '2bbc3b62-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_19']
  pz_DefTask_69:
    name: '{{Ready}}'
    id: '2bbc3c7a-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_20']
  pz_DefTask_70:
    name: '{{Arrival}}'
    id: '2bbc3d7e-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_21']
  pz_DefTask_71:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc3e96-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_9']
  pz_DefTask_72:
    name: '{{Ready}}'
    id: '2bbc3fa4-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_21']
  pz_DefTask_73:
    name: '{{Arrival}}'
    id: '2bbc40a8-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_22']
  pz_DefTask_74:
    name: '{{Ready}}'
    id: '2bbc4328-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_22']
  pz_DefTask_75:
    name: '{{Arrival}}'
    id: '2bbc4440-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_23']
  pz_DefTask_76:
    name: '{{Ready}}'
    id: '2bbc459e-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_23']
  pz_DefTask_77:
    name: '{{Arrival}}'
    id: '2bbc46ac-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_24']
  pz_DefTask_78:
    name: '{{Ready}}'
    id: '2bbc47ba-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_24']
  pz_DefTask_79:
    name: '{{Arrival}}'
    id: '2bbc48c8-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_25']
  pz_DefTask_80:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc49d6-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_10']
  pz_DefTask_81:
    name: '{{Ready}}'
    id: '2bbc4dd2-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_25']
  pz_DefTask_82:
    name: '{{Ready}}'
    id: '2bbc4efe-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_24_1']
  pz_DefTask_83:
    name: '{{Arrival}}'
    id: '2bbc5016-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_26']
  pz_DefTask_84:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc511a-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_11']
  pz_DefTask_85:
    name: '{{Ready}}'
    id: '2bbc59ee-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_26']
  pz_DefTask_86:
    name: '{{Number_of_containers}}'
    id: '2bbc5b42-71a0-11ee-b962-0242ac120002'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_1']
  pz_DefTask_87:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '2bbc5cf0-71a0-11ee-b962-0242ac120002'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_7', '@pz_DefElem_8_7', '@pz_DefElem_9_7', '@pz_DefElem_10_7']
  pz_DefTask_88:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '2bbc5e08-71a0-11ee-b962-0242ac120002'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_5']
  pz_DefTask_89:
    name: '{{Arrival}}'
    id: '2bbc631c-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_27']
  pz_DefTask_90:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2bbc6448-71a0-11ee-b962-0242ac120002'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_8', '@pz_DefElem_8_8', '@pz_DefElem_9_8', '@pz_DefElem_10_8']
  pz_DefTask_91:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '2bbc6556-71a0-11ee-b962-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_12']
  pz_DefTask_92:
    name: '{{Ready}}'
    id: '2bbc6664-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_27']
  pz_DefTask_93:
    name: '{{Ready}}'
    id: '2bbc6772-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_28']
  pz_DefTask_94:
    name: '{{Free_text}}'
    id: '2bbc6876-71a0-11ee-b962-0242ac120002'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_1']
  pz_DefTask_95:
    name: '{{Ready}}'
    id: '2bbc6984-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_29']
  pz_DefTask_96:
    name: '{{Ready}}'
    id: '2bbc6d08-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_30']
  pz_DefTask_97:
    name: '{{Ready}}'
    id: '2bbc6e2a-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_31']
  pz_DefTask_98:
    name: '{{Ready}}'
    id: '2bbc6f42-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_32']
  pz_DefTask_99:
    name: '{{Diesel}}'
    id: '2bbc7050-71a0-11ee-b962-0242ac120002'
    type: 'diesel'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_15_1']
  pz_DefTask_100:
    name: '{{AdBlue}}'
    id: '2bbc715e-71a0-11ee-b962-0242ac120002'
    type: 'adblue'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_16_1']
  pz_DefTask_101:
    name: '{{Ready}}'
    id: '2bbc7262-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_33']
  pz_DefTask_102:
    name: '{{Ready}}'
    id: '2bbc7370-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_34']
  pz_DefTask_103:
    name: '{{Ready}}'
    id: '2bbc7686-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_35']
  pz_DefTask_104:
    name: '{{Arrival}}'
    id: '2bbc77a8-71a0-11ee-b962-0242ac120002'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_28']
  pz_DefTask_105:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2bbc78b6-71a0-11ee-b962-0242ac120002'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_9', '@pz_DefElem_8_9', '@pz_DefElem_9_9', '@pz_DefElem_10_9']
  pz_DefTask_106:
    name: '{{Ready}}'
    id: '2bbc79c4-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_36']
  pz_DefTask_107:
    name: '{{Ready}}'
    id: '2bbc7ad2-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_37']
  pz_DefTask_108:
    name: '{{Ready}}'
    id: '2bbc7be0-71a0-11ee-b962-0242ac120002'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_38']
  pz_DefTask_109:
    name: '{{Photo}}'
    id: '2bbc7cee-71a0-11ee-b962-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_1']
  pz_DefTask_110:
    name: '{{Free_text}}'
    id: '2bbc7dfc-71a0-11ee-b962-0242ac120002'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_2']
  pz_DefTask_111:
    name: '{{Photo}}'
    id: '2bbc818a-71a0-11ee-b962-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_2']
  pz_DefTask_112:
    name: '{{Free_text}}'
    id: '2bbc82ac-71a0-11ee-b962-0242ac120002'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_3']
  pz_DefTask_113:
    name: '{{Photo}}'
    id: '2bbc83c4-71a0-11ee-b962-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_3']
  pz_DefTask_114:
    name: '{{Free_text}}'
    activatedBySapData: true
    id: '2bbc8522-71a0-11ee-b962-0242ac120002'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_4']
  pz_DefTask_115:
    name: '{{Photo}}'
    id: '2bbc864e-71a0-11ee-b962-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_4']
  pz_DefTask_116:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2bbc875c-71a0-11ee-b962-0242ac120002'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_10', '@pz_DefElem_8_10', '@pz_DefElem_9_10', '@pz_DefElem_10_10']
  pz_DefTask_117:
    name: '{{Free_text}}'
    id: '2bbc88a6-71a0-11ee-b962-0242ac120002'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_5']
  pz_DefTask_118:
    name: '{{Initial_mileage}}'
    id: '2bbc8c20-71a0-11ee-b962-0242ac120002'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_1']
  pz_DefTask_119:
    name: '{{Final_mileage}}'
    id: '2bbc8dba-71a0-11ee-b962-0242ac120002'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_1']
  pz_DefTask_120:
    name: '{{Departure_check}}'
    id: '2bbc8f7c-71a0-11ee-b962-0242ac120002'
    type: 'departure_check'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_20_1']
  pz_DefTask_121:
    name: '{{Additional_service}}'
    id: '2bbc90ee-71a0-11ee-b962-0242ac120002'
    type: 'additional_service'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_25_1', '@pz_DefElem_26_1', '@pz_DefElem_27_1']
  pz_DefTask_122:
    name: '{{Disposal_plant_selection}}'
    id: '2bbc922e-71a0-11ee-b962-0242ac120002'
    type: 'disposalSite_choice'
    tenant: '<getGermanyTenant()>'
  pz_DefTask_123:
    name: '{{Photo}}'
    id: 'edf990ea-934b-11ee-b9d1-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_5']
  pz_DefTask_124:
    name: '{{Photo}}'
    id: 'edf993ec-934b-11ee-b9d1-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_6']
  pz_DefTask_125:
    name: '{{Photo}}'
    id: 'edf995d6-934b-11ee-b9d1-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_7']
  pz_DefTask_126:
    name: '{{Photo}}'
    id: 'edf99770-934b-11ee-b9d1-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_8']
  pz_DefTask_127:
    name: '{{Photo}}'
    id: 'edf99b62-934b-11ee-b9d1-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_9']
  pz_DefTask_128:
    name: '{{Photo}}'
    id: 'edf99c70-934b-11ee-b9d1-0242ac120002'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_10']
  pz_DefTask_129:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: 'edf99fd6-934b-11ee-b9d1-0242ac120002'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_13']
  pz_DefTask_130:
    name: '{{Approach_selection}}'
    id: '0640a724-f2d5-442b-bc5d-154e926ed414'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_28_1']
  pz_DefTask_131:
    name: '{{Arrival}}'
    id: 'd12ffa00-ce5d-4599-a787-f4228559fb7e'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_29']
  pz_DefTask_132:
    name: '{{Ready}}'
    id: 'b532cae0-a444-474a-a116-05430833c029'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_39']
  pz_DefTask_133:
    name: '{{Arrival}}'
    id: 'e0698a72-e0aa-4cf9-ac5c-ad1a36ee106e'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_30']
  pz_DefTask_134:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '574d280b-d058-4bdb-ab41-8fbb4dcd1303'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_11', '@pz_DefElem_8_11', '@pz_DefElem_9_11', '@pz_DefElem_10_11']
  pz_DefTask_135:
    name: '{{Ready}}'
    id: '8df3276f-1b2b-49b5-af09-48ce6187e748'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_40']
  pz_DefTask_136:
    name: '{{Arrival}}'
    id: 'fbe7b7f2-d867-4b72-af02-5dd213fb567b'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_31']
  pz_DefTask_137:
    name: '{{Number_of_containers}}'
    id: '1b44ab02-7253-4717-8e48-8718ae098eda'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_5']
  pz_DefTask_138:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'f97fa525-a1ec-4364-ba70-4083f066cc29'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_12', '@pz_DefElem_8_12', '@pz_DefElem_9_12', '@pz_DefElem_10_12']
  pz_DefTask_139:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '4378f6ea-d8b7-4689-a014-219c74160379'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_6']
  pz_DefTask_140:
    name: '{{Ready}}'
    id: '2c0e5e1c-d1ad-4bda-9490-f3a4215d6799'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_29_1']
  pz_DefTask_141:
    name: '{{Arrival}}'
    id: '85481d46-c3dc-4f99-b61f-0b3dfd5d46d5'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_32']
  pz_DefTask_142:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '726f09a0-d6bf-46be-a999-8d7aab4dccd9'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_14']
  pz_DefTask_143:
    name: '{{Ready}}'
    id: 'c1ba4020-0bff-44c4-8239-9f214391fccc'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_41']
  pz_DefTask_144:
    name: '{{Arrival}}'
    id: '1df855bf-c17b-40f1-bc18-a48bba98665b'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_33']
  pz_DefTask_145:
    name: '{{Number_of_containers}}'
    id: '925bde28-5dde-4739-9317-c4ee27824a81'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_2']
  pz_DefTask_146:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '94352109-ff2f-444a-a92f-0ee20cafb1be'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_7']
  pz_DefTask_147:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '61fa528d-6bb0-4abe-9ee9-5052f9be1d0e'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_13', '@pz_DefElem_8_13', '@pz_DefElem_9_13', '@pz_DefElem_10_13']
  pz_DefTask_148:
    name: '{{Ready}}'
    id: '28ac0e3c-070b-499a-9275-068d0c01de61'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_30_1']
  pz_DefTask_149:
    name: '{{Arrival}}'
    id: '21f895c6-1e99-48c1-a8fa-8916b9d50c95'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_34']
  pz_DefTask_150:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'd8e830ab-ecb3-464e-8503-f5bafd915cfa'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_14', '@pz_DefElem_8_14', '@pz_DefElem_9_14', '@pz_DefElem_10_14']
  pz_DefTask_151:
    name: '{{Ready}}'
    id: '2b8f26a0-507e-4274-8e0f-ea2ffe98f1c4'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_42']
  pz_DefTask_152:
    name: '{{Arrival}}'
    id: '7e34a891-f174-48d8-8e58-975ca23dd706'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_35']
  pz_DefTask_153:
    name: '{{Number_of_containers}}'
    activatedBySapData: true
    id: '3cd9d864-c72f-47ac-a5e6-4be828924e6e'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_48_1']
  pz_DefTask_154:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'aecf5676-ce4d-4f70-82b3-14bf432f54b1'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_8']
  pz_DefTask_155:
    name: '{{Ready}}'
    id: '187c7d69-843d-4404-ae7f-ff7dcc713450'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_43']
  pz_DefTask_156:
    name: '{{Arrival}}'
    id: 'a6f61ecb-ddec-44dc-ae3e-6c24624ead67'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_36']
  pz_DefTask_157:
    name: '{{Number_of_containers}}'
    activatedBySapData: true
    id: '325a1efb-f18c-44c2-a9bc-e7c78b9b6fd7'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_6']
  pz_DefTask_158:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '654e756c-fe73-40bf-85f5-11a1fc41decb'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_9']
  pz_DefTask_159:
    name: '{{Ready}}'
    id: '50f9d0f3-0fdc-47ba-915a-7e5f66cc7071'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_44']
  pz_DefTask_160:
    name: '{{Arrival}}'
    id: '28a7395d-1bf5-41fe-8821-3ec63a8c20e0'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_37']
  pz_DefTask_161:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '26c7e2b0-187b-4a9a-8a19-ec733e98942a'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_10']
  pz_DefTask_162:
    name: '{{Ready}}'
    id: '8dac9506-da66-434a-bd07-339ae0adfc22'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_45']
  pz_DefTask_163:
    name: '{{Arrival}}'
    id: 'd737aed6-fd51-40e7-b4ff-9c11796f8ce0'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_38']
  pz_DefTask_164:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '478df3bc-92a6-45cb-8f30-47110eae3d35'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_15', '@pz_DefElem_8_15', '@pz_DefElem_9_15', '@pz_DefElem_10_15']
  pz_DefTask_165:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'c5bd0beb-45da-4b9c-84c8-5bcb9b78d82f'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_11']
  pz_DefTask_166:
    name: '{{Ready}}'
    id: '84b42a7b-dfe6-43c1-8a8c-a8feeb23a960'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_46']
  pz_DefTask_167:
    name: '{{Arrival}}'
    id: 'c1a5e9fc-8aae-4884-bc29-8eb52b160fc4'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_39']
  pz_DefTask_168:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '949c6e95-6d0e-4c1d-a255-e011d3f9e4c7'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_16', '@pz_DefElem_8_16', '@pz_DefElem_9_16', '@pz_DefElem_10_16']
  pz_DefTask_169:
    name: '{{Ready}}'
    id: '77a21a0d-5c32-4eb4-b58f-2f1fbc8b7859'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_47']
  pz_DefTask_170:
    name: '{{Arrival}}'
    id: '02ec99e4-f0c4-48c4-ba3a-16ac9f8b6c35'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_40']
  pz_DefTask_171:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'f21dc999-98ae-481a-9b85-d7204068e509'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_17', '@pz_DefElem_8_17', '@pz_DefElem_9_17', '@pz_DefElem_10_17']
  pz_DefTask_172:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'de9f66c5-5617-406d-ad84-2a2515da1c5b'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_12']
  pz_DefTask_173:
    name: '{{Ready}}'
    id: '968e5acf-6f38-40aa-b817-52154d52769a'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_48']
  pz_DefTask_174:
    name: '{{Arrival}}'
    id: '2151c623-8fd3-499c-baaf-4a9bb9eb7caf'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_41']
  pz_DefTask_175:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'bab4264f-e53e-4bfe-ada3-9da77e69f1b2'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_18', '@pz_DefElem_8_18', '@pz_DefElem_9_18', '@pz_DefElem_10_18']
  pz_DefTask_176:
    name: '{{Ready}}'
    id: '27276407-a7e3-4751-97b7-f8183e52050f'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_49']
  pz_DefTask_177:
    name: '{{Arrival}}'
    id: '7c863a3d-080e-4faf-8f4e-2d2ba94c45aa'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_42']
  pz_DefTask_178:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'dcfd275c-17f5-486d-852a-b74c4aa94ae8'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_19', '@pz_DefElem_8_19', '@pz_DefElem_9_19', '@pz_DefElem_10_19']
  pz_DefTask_179:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'df4ddb2d-71bf-43c4-a47d-62180aaf4b90'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_13']
  pz_DefTask_180:
    name: '{{Ready}}'
    id: 'da692b23-0190-4031-be57-edc77a8254e3'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_50']
  pz_DefTask_181:
    name: '{{Arrival}}'
    id: '41f92525-69ff-4759-a836-a1cbbd27b58a'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_43']
  pz_DefTask_182:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '990af2bf-67ac-4e5e-b6cf-f36703c87aaf'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_20', '@pz_DefElem_8_20', '@pz_DefElem_9_20', '@pz_DefElem_10_20']
  pz_DefTask_183:
    name: '{{Ready}}'
    id: '1c5df160-77ce-4a5e-8d07-bc1e5a02dd3b'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_51']
  pz_DefTask_184:
    name: '{{Arrival}}'
    id: '7d57dca2-1d0b-4f23-84ca-34ca422429a1'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_44']
  pz_DefTask_185:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'f02e9708-2045-499e-ad01-25c5e20c399a'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_21', '@pz_DefElem_8_21', '@pz_DefElem_9_21', '@pz_DefElem_10_21']
  pz_DefTask_186:
    name: '{{Ready}}'
    id: '9bf05840-0a50-47d8-892c-d32bd07e5937'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_52']
  pz_DefTask_187:
    name: '{{Arrival}}'
    id: '00a325b6-8f71-4fe9-819c-5e771acddadd'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_45']
  pz_DefTask_188:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'f37c99de-1caf-4d5b-bc8b-302ca9eedbb9'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_22', '@pz_DefElem_8_22', '@pz_DefElem_9_22', '@pz_DefElem_10_22']
  pz_DefTask_189:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'af65b7e4-9d07-4b4e-b7ef-58d488777cb8'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_14']
  pz_DefTask_190:
    name: '{{Ready}}'
    id: '3119e522-2498-4945-b48f-86429fdf8c23'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_53']
  pz_DefTask_191:
    name: '{{Number_of_containers}}'
    id: '3b84f49f-0896-40f3-abd4-1aa35863d5ee'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_7']
  pz_DefTask_192:
    name: '{{Number_of_containers}}'
    id: 'c5960e91-dc59-4dc7-9278-788b79588214'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_8']
  pz_DefTask_193:
    name: '{{Number_of_containers}}'
    id: 'ec76946e-fef1-4c74-b4de-26d861143af2'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_9']
  pz_DefTask_194:
    name: '{{Number_of_containers}}'
    id: 'f505d005-1e85-4efb-a74d-b6e65e3b8833'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_10']
  pz_DefTask_195:
    name: '{{Arrival}}'
    id: '9d6b245b-3711-47ce-9297-217cf31a0f18'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_46']
  pz_DefTask_196:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'e9a279f8-f75c-49ef-9158-301f6436547b'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_23', '@pz_DefElem_8_23', '@pz_DefElem_9_23', '@pz_DefElem_10_23']
  pz_DefTask_197:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '2c672450-69c8-4823-ab98-7c92223537aa'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_15']
  pz_DefTask_198:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'b21a8df5-5471-4e65-bc43-a9bcb17ddd76'
    type: 'scale_weighing_data'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_3', '@pz_DefElem_32_1']
  pz_DefTask_200:
    name: '{{Ready}}'
    id: '26176be9-3166-44a3-8ded-207d14689ac5'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_54']
  pz_DefTask_201:
    name: '{{Number_of_containers}}'
    id: 'f3da91f3-5cd2-4db9-a854-31ec0f768977'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_11']
  pz_DefTask_202:
    name: 'scaleWeighingData1'
    activatedBySapData: true
    id: '94d5878f-5448-493e-8c18-fa0a83a8bd0f'
    type: 'gp_scale_weighing_data_1'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_4', '@pz_DefElem_32_2']
  pz_DefTask_203:
    name: 'scaleWeighingData2'
    activatedBySapData: true
    id: 'f59f24ba-f448-49a8-a357-d6c576e69887'
    type: 'gp_scale_weighing_data_2'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_5', '@pz_DefElem_32_3']
  pz_DefTask_204:
    name: 'scaleWeighingData3'
    activatedBySapData: true
    id: '330748c3-641d-4c9b-863b-985be1df7ae6'
    type: 'gp_scale_weighing_data_3'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_6', '@pz_DefElem_32_4']
  pz_DefTask_205:
    name: 'scaleWeighingData4'
    activatedBySapData: true
    id: '4f9d755c-4a8f-4865-a7a8-0838d413c6fe'
    type: 'gp_scale_weighing_data_4'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_7', '@pz_DefElem_32_5']
  pz_DefTask_206:
    name: 'scaleWeighingData5'
    activatedBySapData: true
    id: 'b3642f15-8635-4c82-8a9d-28ae58eed01b'
    type: 'gp_scale_weighing_data_5'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_8', '@pz_DefElem_32_6']
  pz_DefTask_207:
    name: 'scaleWeighingData6'
    activatedBySapData: true
    id: '40ec825c-22b0-47a6-99dd-a37f4cde184d'
    type: 'gp_scale_weighing_data_6'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_9', '@pz_DefElem_32_7']
  pz_DefTask_208:
    name: 'scaleWeighingData7'
    activatedBySapData: true
    id: 'd6b6aa53-1153-48dc-929f-3710a3dd7d12'
    type: 'gp_scale_weighing_data_7'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_10', '@pz_DefElem_32_8']
  pz_DefTask_209:
    name: 'scaleWeighingData8'
    activatedBySapData: true
    id: '8b5aba0f-253c-4671-b9df-9be3009f2ecd'
    type: 'gp_scale_weighing_data_8'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_11', '@pz_DefElem_32_9']
  pz_DefTask_210:
    name: '{{Final_mileage}}'
    id: '70ffce66-3f9f-483e-b06d-91399797351c'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_2']
  pz_DefTask_211:
    name: '{{Final_mileage}}'
    id: '2dee2414-f6ad-46aa-bf26-5ca8c2fb0ccf'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_3']
  pz_DefTask_212:
    name: '{{Final_mileage}}'
    id: '91553bec-d880-4aa4-a691-3f2ce8fbed0e'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_4']
  pz_DefTask_214:
    name: '{{Approach_selection}}'
    id: 'af47854d-3ad5-4ee9-91ae-3d3c49c02b5f'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_33_1']
  pz_DefTask_215:
    name: '{{Ready}}'
    id: '3a248372-d1af-4b97-8758-9dd251d32c90'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_55']
  pz_DefTask_216:
    name: '{{Final_collection}}'
    id: 'e42a51c9-2e8f-41d9-b40e-4c8d9c5c4877'
    type: 'municipal_collection_end'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_35_1']
  pz_DefTask_217:
    name: '{{Ready}}'
    id: '3831df73-68f6-4c3c-bd3a-3e87caee6dc6'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_34_1']
  pz_DefTask_218:
    name: '{{Ready}}'
    id: '00fef7e9-d187-41c8-bbfa-dcef40b211c3'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_56']
  pz_DefTask_219:
    name: '{{Approach_selection}}'
    id: '9f87f391-27cb-4cc5-9145-e89daf3bc8b2'
    type: 'start'
    tenant: '<getGermanyTenant()>'
  pz_DefTask_220:
    name: '{{Ready}}'
    id: '8c3ef21e-65ae-4afe-a6b9-d5029ef625a0'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
  pz_DefTask_221:
    name: '{{Final_collection}}'
    id: '58d65dde-be74-4881-8fcb-ce0d6ba50b20'
    type: 'municipal_collection_end'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_35_2']
  pz_DefTask_222:
    name: '{{Number_of_containers}}'
    id: '41edafed-480f-43e5-abf3-1c6272097200'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_58_1']
  pz_DefTask_223:
    name: '{{Ready}}'
    id: '07f976d2-8617-430d-ae04-1967791ccb61'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_3_57']
  pz_DefTask_224:
    name: '{{Ready}}'
    id: '201f61b1-0e13-40c7-923b-e5f2b87361f2'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
  pz_DefTask_225:
    name: '{{Approach_selection}}'
    id: '06f17629-8c30-45cb-8290-340c58ee53f5'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_38_1']
  pz_DefTask_226:
    name: '{{Ready}}'
    id: '0bbc27f9-9cb4-44cd-8a20-06022719c811'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_58']
  pz_DefTask_227:
    name: '{{Finish_sweeping}}'
    id: '2d7d7fbd-8667-4cdf-b57a-dfce3fa7e92e'
    type: 'municipal_collection_end'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_40_1']
  pz_DefTask_228:
    name: '{{Ready}}'
    id: '5bdd4f48-964a-4ea0-aa44-9a3a9cbda1cb'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_39_1']
  pz_DefTask_229:
    name: '{{Ready}}'
    id: 'd1b9c709-9f03-479e-a553-d5d747497b2a'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_3_59']
  pz_DefTask_230:
    name: '{{Approach_selection}}'
    id: '5099bf06-6587-4821-b9c1-f6ea88926d5c'
    type: 'start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_41_1']
  pz_DefTask_231:
    name: '{{Ready}}'
    id: 'c3695952-a64f-4aef-a513-6606b4124eac'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_60']
  pz_DefTask_232:
    name: '{{Finish_winterservice}}'
    id: '85acb278-f094-43d8-8587-4f21580320ee'
    type: 'municipal_collection_end'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_43_1']
  pz_DefTask_233:
    name: '{{Ready}}'
    id: 'e4891619-b014-417d-88fd-1b0d6533bbf3'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_42_1']
  pz_DefTask_234:
    name: '{{Ready}}'
    id: 'e5d03567-37ae-4741-8638-36cc6c9a8246'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_3_61']
  pz_DefTask_235:
    name: '{{Arrival}}'
    id: '9b6273e0-0bb4-4b0e-8973-724f9ee78249'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_47']
  pz_DefTask_236:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'c3096e17-d289-47ef-b33f-e280239eb3e7'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_16']
  pz_DefTask_237:
    name: '{{Ready}}'
    id: 'ba16da00-105c-4572-8a21-116d61a7a5b6'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_62']
  pz_DefTask_238:
    name: '{{Arrival}}'
    id: '37da4e78-b25b-470d-947f-d0661e3d3007'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_48']
  pz_DefTask_239:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'c69e78f4-aa79-4d8e-9d18-313773f2616c'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_17']
  pz_DefTask_240:
    name: '{{Ready}}'
    id: 'ad04ed81-cb8e-4749-b1eb-45f9d6c84bd9'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_63']
  pz_DefTask_241:
    name: '{{Arrival}}'
    id: 'b3768c5c-6733-4f20-958f-0dfd3dc0811d'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_49']
  pz_DefTask_242:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '476174ae-3b43-45fa-9e9b-de7761923a2b'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_18']
  pz_DefTask_243:
    name: '{{Ready}}'
    id: '154aa359-00f4-486b-b578-cf067b1f33ac'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_64']
  pz_DefTask_244:
    name: '{{Arrival}}'
    id: 'cd37fb4f-33be-4f08-820e-835f9b24032f'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_50']
  pz_DefTask_245:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '68e0ea31-0e41-499b-ad51-96c4852d328f'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_19']
  pz_DefTask_246:
    name: '{{Ready}}'
    id: '6aa1a14b-a775-413f-94c7-b6a95d4260ad'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_65']
  pz_DefTask_247:
    name: '{{Arrival}}'
    id: '0d9d7e5c-2cda-45da-b477-d3e46bc2f790'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_51']
  pz_DefTask_248:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '87bfc900-5d33-492d-aaf5-b3769d140db8'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_20']
  pz_DefTask_249:
    name: '{{Ready}}'
    id: 'b9dc9855-720a-4fc8-af47-94bab81750c0'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_66']
  pz_DefTask_250:
    name: '{{Arrival}}'
    id: '35ff6157-a59a-4798-9bd5-4c50ccb25f0c'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_52']
  pz_DefTask_251:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '27a4e1d7-22e7-411f-9dae-81e126762ac9'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_21']
  pz_DefTask_252:
    name: '{{Ready}}'
    id: '54a05d8d-650d-48e1-8c03-dd6921ad0f3f'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_67']
  pz_DefTask_253:
    name: '{{Arrival}}'
    id: 'cf1640e7-7d71-48f0-b351-080639522441'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_53']
  pz_DefTask_254:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'bb283eb8-b035-42ee-8504-4a32edecaa09'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_22']
  pz_DefTask_255:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '77672579-6280-48bb-89b5-c8d89b400441'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_24', '@pz_DefElem_8_24', '@pz_DefElem_9_24', '@pz_DefElem_10_24']
  pz_DefTask_256:
    name: '{{suction_cubicmeter}}'
    id: 'ddd917d2-59cb-4ccb-959e-e41e326bdacc'
    type: 'cubicmeter'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_44_1']
  pz_DefTask_257:
    name: '{{Ready}}'
    id: '8be4664d-5d88-494e-9ada-379999c5e0c5'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_45_1']
  pz_DefTask_258:
    name: '{{Arrival}}'
    id: 'f2cb7c3c-26c5-4def-ad15-2ade3fcd40e3'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_54']
  pz_DefTask_259:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '7123b8f4-0692-46a3-a7d5-d6494d85b36a'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_25', '@pz_DefElem_8_25', '@pz_DefElem_9_25', '@pz_DefElem_10_25']
  pz_DefTask_260:
    name: '{{Ready}}'
    id: '58df3d91-9825-43ab-b3f8-fd0ae854fff1'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_68']
  pz_DefTask_261:
    name: '{{Arrival}}'
    id: '1a63cb3a-577f-459f-ab80-1a9657a18647'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_55']
  pz_DefTask_262:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '38706393-9887-47d8-9074-fadc6b8f3938'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_15']
  pz_DefTask_263:
    name: '{{Number_of_containers}}'
    id: 'f6dd10cf-16be-4921-b2da-c8beb7d93795'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_12']
  pz_DefTask_264:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '7c644295-bfb1-454d-aa23-c5ca94bad1d8'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_23']
  pz_DefTask_265:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '2b24d868-81df-41b1-a6fb-89b5e2a44125'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_26', '@pz_DefElem_8_26', '@pz_DefElem_9_26', '@pz_DefElem_10_26']
  pz_DefTask_266:
    name: '{{Ready}}'
    id: '83f25861-e51e-4d7a-8b22-5970b1db3998'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_46_1']
  pz_DefTask_267:
    name: '{{Arrival}}'
    id: 'de2491af-4385-448a-9452-960e309479bb'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_56']
  pz_DefTask_268:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '144541d4-f80a-4a0d-afe0-4cd32d7e7e11'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_27', '@pz_DefElem_8_27', '@pz_DefElem_9_27', '@pz_DefElem_10_27']
  pz_DefTask_269:
    name: '{{Ready}}'
    id: '672bb904-68fc-4324-8898-2cca5f308ad4'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_69']
  pz_DefTask_270:
    name: '{{Arrival}}'
    id: '3956d4c3-9bc6-484d-9aef-9b3a307b2f37'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_57']
  pz_DefTask_271:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '1cbe01e3-c4e7-4f59-ba62-555b52e4d611'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_28', '@pz_DefElem_8_28', '@pz_DefElem_9_28', '@pz_DefElem_10_28']
  pz_DefTask_272:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '6897a8e3-506a-468f-b170-80b428a7e5f5'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_24']
  pz_DefTask_273:
    name: '{{Ready}}'
    id: '255ee3a8-743d-4e83-b3df-1a1fc9df4141'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_70']
  pz_DefTask_274:
    name: '{{Arrival}}'
    id: '085cd07d-f729-4962-8e1a-98cb662839b4'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_58']
  pz_DefTask_275:
    name: 'glasNumericInput1'
    activatedBySapData: true
    id: 'dbe9223d-3e7d-4b91-b3a2-99caf196a61d'
    type: 'confirm_input'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_1']
  pz_DefTask_276:
    name: 'glasNumericInput2'
    activatedBySapData: true
    id: 'f204f5a7-9b05-40c7-b406-f50fe67afb13'
    type: 'gp_confirm_input_1'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_2']
  pz_DefTask_277:
    name: 'glasNumericInput3'
    activatedBySapData: true
    id: 'ab3cb323-5ee4-4f09-becb-ca5bff67c4f1'
    type: 'gp_confirm_input_2'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_3']
  pz_DefTask_278:
    name: 'glasNumericInput4'
    activatedBySapData: true
    id: '393968e6-ada9-4d11-9931-31053db6da38'
    type: 'gp_confirm_input_3'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_4']
  pz_DefTask_279:
    name: 'glasNumericInput5'
    activatedBySapData: true
    id: '01f057bb-427c-4782-927f-950763788fe0'
    type: 'gp_confirm_input_4'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_5']
  pz_DefTask_280:
    name: 'glasNumericInput6'
    activatedBySapData: true
    id: '4a154247-f023-4ba5-9655-1ad504680759'
    type: 'gp_confirm_input_5'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_6']
  pz_DefTask_281:
    name: 'glasNumericInput7'
    activatedBySapData: true
    id: '9d2d4102-a44d-46aa-aa17-7a1df040092e'
    type: 'gp_confirm_input_6'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_7']
  pz_DefTask_282:
    name: 'glasNumericInput8'
    activatedBySapData: true
    id: '11c9556c-1ff6-4a9a-b810-a15262e2a96a'
    type: 'gp_confirm_input_7'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_8']
  pz_DefTask_283:
    name: 'glasNumericInput9'
    activatedBySapData: true
    id: 'df517aba-83fd-48e0-8306-0f13361175b8'
    type: 'gp_confirm_input_8'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_9']
  pz_DefTask_284:
    name: 'glasNumericInput10'
    activatedBySapData: true
    id: '65bd040e-bc14-44aa-8f09-9b03437ec588'
    type: 'gp_confirm_input_9'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_10']
  pz_DefTask_285:
    name: 'glasNumericInput11'
    activatedBySapData: true
    id: '11baeaad-ced5-4678-b661-50f6584a5f3d'
    type: 'gp_confirm_input_10'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_11']
  pz_DefTask_286:
    name: 'glasNumericInput12'
    activatedBySapData: true
    id: '565fd5e7-47ba-4a9f-9497-f1603cf305b0'
    type: 'gp_confirm_input_11'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_12']
  pz_DefTask_287:
    name: 'glasNumericInput13'
    activatedBySapData: true
    id: '3ab4e50f-e665-4298-96ee-77af2adda02f'
    type: 'gp_confirm_input_12'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_13']
  pz_DefTask_288:
    name: 'glasNumericInput14'
    activatedBySapData: true
    id: '87c10b76-cfa7-441e-b3aa-1c72f8e8675e'
    type: 'gp_confirm_input_13'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_14']
  pz_DefTask_289:
    name: 'glasNumericInput15'
    activatedBySapData: true
    id: '35fbcc29-ce36-47a8-a2ae-092e3cd9018c'
    type: 'gp_confirm_input_14'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_15']
  pz_DefTask_290:
    name: 'glasNumericInput16'
    activatedBySapData: true
    id: '9aee64d3-d712-4908-a749-76a3ddebe5e8'
    type: 'gp_confirm_input_15'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_16']
  pz_DefTask_291:
    name: 'glasNumericInput17'
    activatedBySapData: true
    id: '648fd755-0eb2-45ba-b592-75ddad066144'
    type: 'gp_confirm_input_16'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_17']
  pz_DefTask_292:
    name: 'glasNumericInput18'
    activatedBySapData: true
    id: '74ed938e-45df-44c5-a4f6-8f976726b164'
    type: 'gp_confirm_input_17'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_47_18']
  pz_DefTask_293:
    name: '{{Ready}}'
    id: '1208df6e-854a-45e3-8859-fd0f3c291ba1'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_71']
  pz_DefTask_294:
    name: '{{Free_text}}'
    id: '1db92ae6-dd9f-4a12-99ac-f174b3ca1883'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_6']
  pz_DefTask_295:
    name: '{{Photo}}'
    id: '65c7d3f5-70fd-47dc-b3a3-e604dd354449'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_11']
  pz_DefTask_296:
    name: '{{Change_in_working_hours}}'
    id: '415ca5fc-aed7-4ed7-99c8-2dd2773113b6'
    type: 'workingtime_adjustment'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_49_1', '@pz_DefElem_50_1', '@pz_DefElem_51_1']
  pz_DefTask_297:
    name: '{{Photo}}'
    id: '99ef3df1-1a6e-4624-bf5e-e4474370c220'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_12']
  pz_DefTask_298:
    name: '{{Free_text}}'
    id: 'f09b60b6-7364-415d-b2c5-4ccfef5dfea4'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_7']
  pz_DefTask_299:
    name: '{{Photo}}'
    id: '041cec1f-c7da-4117-b8fe-4acef271b7df'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_13']
  pz_DefTask_300:
    name: '{{Free_text}}'
    id: 'd97a331c-c2e1-44c7-b22c-ebfd44dc2541'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_8']
  pz_DefTask_301:
    name: '{{Photo}}'
    id: '6db5ff05-55ff-4b35-b2ca-163e2e512270'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_14']
  pz_DefTask_302:
    name: '{{Free_text}}'
    id: '5dc5ba0a-8099-4034-ba9d-0fe443ff1596'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_9']
  pz_DefTask_303:
    name: '{{Photo}}'
    id: '8986b256-5535-4889-bd38-c4ab8a7f2b15'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_15']
  pz_DefTask_304:
    name: '{{Free_text}}'
    id: 'da44b70f-b523-4e28-821c-a967e00da2c8'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_10']
  pz_DefTask_305:
    name: '{{Photo}}'
    id: '6c86c658-44cb-4a1c-b945-b5a8b9586d59'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_16']
  pz_DefTask_306:
    name: '{{Free_text}}'
    id: '580c0065-c209-42a5-9288-4c7b8d7b64d8'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_11']
  pz_DefTask_307:
    name: '{{Photo}}'
    id: '01c9f65e-9658-4141-886d-6666c6532ef5'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_17']
  pz_DefTask_308:
    name: '{{Free_text}}'
    id: '2b1e8f08-477b-4553-a3bb-d40a82da0e6d'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_12']
  pz_DefTask_309:
    name: '{{Photo}}'
    id: 'f530ddc7-8f4c-46e4-9fe2-442d9eb751ff'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_18']
  pz_DefTask_310:
    name: '{{Free_text}}'
    id: 'f91fe31c-9c05-4a43-988a-3a806857b466'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_13']
  pz_DefTask_311:
    name: '{{Photo}}'
    id: 'c106ed39-9f6a-402d-9a80-ca565a05310c'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_19']
  pz_DefTask_312:
    name: '{{Free_text}}'
    id: '195e16bd-4571-417d-ae43-756e230e4567'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_14']
  pz_DefTask_313:
    name: '{{Initial_mileage}}'
    id: '913ea3f7-f474-4e60-bbe6-bb8a90e3be86'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_2']
  pz_DefTask_314:
    name: '{{Final_mileage}}'
    id: '1c0eeb7e-8ad3-40dc-b441-bd61828fde81'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_5']
  pz_DefTask_315:
    name: '{{Number_of_containers}}'
    activatedBySapData: true
    id: '0510bfce-35a0-4376-aa45-68c3260c8e5d'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_48_2']
  pz_DefTask_316:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '15449aac-acf1-4734-a0a4-54fda1f4a0fa'
    type: 'scale_weighing_data'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_13', '@pz_DefElem_32_10']
  pz_DefTask_317:
    name: 'scaleWeighingData1'
    activatedBySapData: true
    id: 'f1c3911e-9c41-46e5-aacb-276510aa1753'
    type: 'gp_scale_weighing_data_1'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_14', '@pz_DefElem_32_11']
  pz_DefTask_318:
    name: 'scaleWeighingData2'
    activatedBySapData: true
    id: '1dad5610-130e-4aa3-a479-966ea96cb729'
    type: 'gp_scale_weighing_data_2'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_15', '@pz_DefElem_32_12']
  pz_DefTask_319:
    name: 'scaleWeighingData3'
    activatedBySapData: true
    id: '80f22fee-6751-4f5b-8e9e-4d6fc7e7a188'
    type: 'gp_scale_weighing_data_3'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_16', '@pz_DefElem_32_13']
  pz_DefTask_320:
    name: 'scaleWeighingData4'
    activatedBySapData: true
    id: '6567ae31-736e-47ea-b7f9-0b9f00df0cfd'
    type: 'gp_scale_weighing_data_4'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_17', '@pz_DefElem_32_14']
  pz_DefTask_321:
    name: 'scaleWeighingData5'
    activatedBySapData: true
    id: '313dceeb-2ea0-4f36-bcb5-625b44da4766'
    type: 'gp_scale_weighing_data_5'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_18', '@pz_DefElem_32_15']
  pz_DefTask_322:
    name: 'scaleWeighingData6'
    activatedBySapData: true
    id: '3f7b4dce-c3d6-416f-ba87-c843a437b39f'
    type: 'gp_scale_weighing_data_6'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_19', '@pz_DefElem_32_16']
  pz_DefTask_323:
    name: 'scaleWeighingData7'
    activatedBySapData: true
    id: '13c7a94e-22c3-4859-a5da-3cc9bfc7916a'
    type: 'gp_scale_weighing_data_7'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_20', '@pz_DefElem_32_17']
  pz_DefTask_324:
    name: 'scaleWeighingData8'
    activatedBySapData: true
    id: '7ba63436-f9a2-472b-9e3e-d6e88d113c7b'
    type: 'gp_scale_weighing_data_8'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_21', '@pz_DefElem_32_18']
  pz_DefTask_325:
    name: '{{suction_cubicmeter}}'
    activatedBySapData: true
    id: '77e8f5c2-1d3f-4b02-98e6-308f8db179b5'
    type: 'cubicmeter'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_44_2']
  pz_DefTask_326:
    name: '{{Free_text}}'
    id: '30d36def-d18a-4e88-99df-ea6c199a0c3e'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_15']
  pz_DefTask_327:
    name: '{{Photo}}'
    id: 'f840f3c5-d1c4-4ccc-bec7-7c4caf0d3e6d'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_20']
  pz_DefTask_328:
    name: '{{Ready}}'
    id: '62400ce5-da03-42ac-a2b2-14976af456c5'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_72']
  pz_DefTask_329:
    name: '{{Initial_mileage}}'
    id: 'f2230f2c-f593-4299-95d4-b735161a789b'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_3']
  pz_DefTask_330:
    name: '{{Final_mileage}}'
    id: 'c2e90c90-54e1-4a4d-873b-80cc2aa798df'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_6']
  pz_DefTask_331:
    name: '{{Arrival}}'
    id: 'ffced690-8368-4421-a0bb-058b67d03582'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_59']
  pz_DefTask_332:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '9422899d-df96-4f1a-bfa9-ea72d51d2929'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_29', '@pz_DefElem_8_29', '@pz_DefElem_9_29', '@pz_DefElem_10_29']
  pz_DefTask_333:
    name: '{{Ready}}'
    id: '181f86fa-ce5b-4ec8-9239-373e66de6cdd'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_73']
  pz_DefTask_334:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: 'c9732726-33a3-48e5-99db-353a96938e2e'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_16']
  pz_DefTask_335:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: 'f0e6251a-6dad-487e-ac1f-7824d6683b2b'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_17']
  pz_DefTask_336:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: '278b6367-e3d7-471f-8b3d-3c637f3c7295'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_18']
  pz_DefTask_337:
    name: '{{Container_scan}}'
    activatedBySapData: true
    id: 'a1f16ec9-b9b3-4b4f-ab42-3f4f7438b758'
    type: 'container_scan'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_5_19']
  pz_DefTask_338:
    name: '{{Initial_mileage}}'
    id: '9acbb17d-123e-472c-8484-ec6e3a5ecea7'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_4']
  pz_DefTask_339:
    name: '{{Final_mileage}}'
    id: '465179f7-fda7-4e29-a56c-0e5c113be429'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_7']
  pz_DefTask_340:
    name: '{{Arrival}}'
    id: '3b82e04b-d81c-44b6-a09b-db6cbbd59897'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_60']
  pz_DefTask_341:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '02087261-97a5-486e-b197-fc53483e0d66'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_30', '@pz_DefElem_8_30', '@pz_DefElem_9_30', '@pz_DefElem_10_30']
  pz_DefTask_342:
    name: '{{Ready}}'
    id: 'fbdfd380-0c82-4a87-b74a-8fcee0294605'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_74']
  pz_DefTask_343:
    name: '{{Free_text}}'
    id: '83d3a49d-2a8a-42c2-9b11-d7a910317625'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_14_16']
  pz_DefTask_344:
    name: '{{Photo}}'
    id: '90a8a60a-9c68-4919-a7e2-e4c9c0d37eee'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_17_21']
  pz_DefTask_345:
    name: '{{employeeAmount}}'
    activatedBySapData: true
    id: '02b73d5f-8541-45db-b5ef-8043521a0c12'
    type: 'workerAmount'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_52_1']
  pz_DefTask_346:
    name: '{{Number_of_containers}}'
    activatedBySapData: true
    id: 'a2e5e0aa-9f8c-411f-8c04-f4581d228590'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_31_22']
  pz_DefTask_347:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: 'd2b0eb82-08fc-4c0e-8a11-75274b7d4332'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_1']
  pz_DefTask_348:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '8882f9e4-be15-46be-93c2-9089ec8ee062'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_2']
  pz_DefTask_349:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '6caa0f75-9532-49b3-9952-1875d239b72d'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_3']
  pz_DefTask_350:
    name: '{{Arrival}}'
    id: '94b292d9-4d5e-4033-97fa-c436cdb21505'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_61']
  pz_DefTask_351:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: 'ce32aba4-e7aa-491d-9234-e0a7d6fd5a5a'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_4']
  pz_DefTask_352:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '063e623f-8d02-43ec-a19b-2eb7da8a90b4'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_31', '@pz_DefElem_8_31', '@pz_DefElem_9_31', '@pz_DefElem_10_31']
  pz_DefTask_353:
    name: '{{Ready}}'
    id: '54e95592-d5b2-4a5d-8a63-551dea9983ac'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_75']
  pz_DefTask_354:
    name: '{{Arrival}}'
    id: '8594534c-cfd7-44cc-8648-72170a70c0a5'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_62']
  pz_DefTask_355:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '43aea353-f9d2-4f4f-9f7e-ae9708a8488c'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_5']
  pz_DefTask_356:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '16e9630b-7462-4892-b8c8-fc5c934e8944'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_32', '@pz_DefElem_8_32', '@pz_DefElem_9_32', '@pz_DefElem_10_32']
  pz_DefTask_357:
    name: '{{Ready}}'
    id: '4480bd17-672b-4b65-8675-fbacffedbeff'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_76']
  pz_DefTask_358:
    name: '{{Initial_mileage}}'
    id: 'feb72fa1-c7bf-4841-980d-2c50d2ae122c'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_5']
  pz_DefTask_359:
    name: '{{Final_mileage}}'
    id: '32c192a9-f3da-44b6-bc0f-0a196b4532bd'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_8']
  pz_DefTask_360:
    name: '{{Initial_mileage}}'
    id: '95e9a670-56d8-47b2-a0c8-73457c0a4ede'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_6']
  pz_DefTask_361:
    name: '{{Final_mileage}}'
    id: '4e4eae19-2a25-455e-99ca-fb573c763d15'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_9']
  pz_DefTask_362:
    name: '{{Arrival}}'
    id: 'b9d68e27-08db-4203-8a18-7b13af13700c'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_63']
  pz_DefTask_363:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '26e8d2f4-ceba-4885-9857-5bc207ba4767'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_33', '@pz_DefElem_8_33', '@pz_DefElem_9_33', '@pz_DefElem_10_33']
  pz_DefTask_364:
    name: '{{Ready}}'
    id: 'eeff15de-6b5e-4ac4-9b2d-12343e4a47da'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_77']
  pz_DefTask_365:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: 'a8e0d9df-dcd3-482f-8c0e-d831cc94b717'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_6']
  pz_DefTask_366:
    name: '{{Initial_mileage}}'
    id: '6d25c854-3b49-4637-adf2-d41b78d37ba7'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_7']
  pz_DefTask_367:
    name: '{{Final_mileage}}'
    id: '94dc8598-917a-431d-84a3-8f54459fe667'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_10']
  pz_DefTask_368:
    name: '{{Arrival}}'
    id: '993ba7ad-7af3-4936-8cfe-d7b13b39d940'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_64']
  pz_DefTask_369:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '9dd8072e-8b2d-4482-942c-3997343ed7a0'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_34', '@pz_DefElem_8_34', '@pz_DefElem_9_34', '@pz_DefElem_10_34']
  pz_DefTask_370:
    name: '{{Ready}}'
    id: '635e0167-2e5f-416d-95a1-dac1901183e3'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_78']
  pz_DefTask_371:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: 'b3f83c36-fe4e-45ec-83e6-5fba0e8aa42b'
    type: 'wastePicker'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_53_7']
  pz_DefTask_372:
    name: '{{Initial_mileage}}'
    id: 'f9923c84-e9f6-4402-aabd-7291c62715a8'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_18_8']
  pz_DefTask_373:
    name: '{{Final_mileage}}'
    id: 'aa0fc747-e21b-4029-ad1c-40dcbfb73c39'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_19_11']
  pz_DefTask_374:
    name: '{{Arrival}}'
    id: '54178b95-67eb-4668-bb5c-7a0518d8368d'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_65']
  pz_DefTask_375:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '17e1d801-d475-4718-8920-bea92f3971c0'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_35', '@pz_DefElem_8_35', '@pz_DefElem_9_35', '@pz_DefElem_10_35']
  pz_DefTask_376:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '3975e8d3-6939-4753-83bb-3e9d960e8616'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_25']
  pz_DefTask_377:
    name: '{{Number_of_containers}}'
    id: '3b9e646f-ea64-4d64-948e-2b84afcf82f5'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_12']
  pz_DefTask_378:
    name: '{{Ready}}'
    id: 'ddf8eefe-7a7a-4c6e-bda7-560685534559'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_54_1']
  pz_DefTask_379:
    name: '{{Arrival}}'
    id: '4282c91d-07c6-4f74-9d80-78c02595543a'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_66']
  pz_DefTask_380:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '078d1af7-e29c-4e74-b1dc-18d6682f82a6'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_36', '@pz_DefElem_8_36', '@pz_DefElem_9_36', '@pz_DefElem_10_36']
  pz_DefTask_381:
    name: '{{Ready}}'
    id: 'd5cd9bac-9a66-4cce-adf0-01e408dd1871'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_79']
  pz_DefTask_382:
    name: '{{Arrival}}'
    id: '1d122b49-aa5b-4c96-823a-60956bed1250'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_67']
  pz_DefTask_383:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'e67920fb-1a48-4151-903c-8ba6b678b122'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_37', '@pz_DefElem_8_37', '@pz_DefElem_9_37', '@pz_DefElem_10_37']
  pz_DefTask_384:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '4a4a71a5-97a8-471a-9bf3-59586dd8088e'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_26']
  pz_DefTask_385:
    name: '{{Number_of_containers}}'
    id: '56e5dd57-ffe7-4d81-b5e9-c07df9ffb197'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_13']
  pz_DefTask_386:
    name: '{{Ready}}'
    id: 'f8ac9544-0509-4dba-b161-3a31fb8281c0'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_55_1']
  pz_DefTask_387:
    name: '{{Arrival}}'
    id: '6fb10470-2674-4859-9a4f-4250cf08efdc'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_68']
  pz_DefTask_388:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'ead414b2-3fbc-4d26-8b48-f7f6b576a9c0'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_38', '@pz_DefElem_8_38', '@pz_DefElem_9_38', '@pz_DefElem_10_38']
  pz_DefTask_389:
    name: '{{Ready}}'
    id: 'cb15c8f6-7fcc-466d-8e5f-88cf6edd0023'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_80']
  pz_DefTask_390:
    name: '{{Arrival}}'
    id: '0136793f-4582-4695-9457-13da1d41a17b'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_69']
  pz_DefTask_391:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'cc22a9fc-7f5b-45e3-b95a-d516bbd57f93'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_39', '@pz_DefElem_8_39', '@pz_DefElem_9_39', '@pz_DefElem_10_39']
  pz_DefTask_392:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '164f50d4-a709-4e4f-8f38-a174fc8e11a5'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_27']
  pz_DefTask_393:
    name: '{{Number_of_containers}}'
    id: '8ac4079b-2822-43eb-b3b3-1b9d8a3cba41'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_14']
  pz_DefTask_394:
    name: '{{Ready}}'
    id: 'b5a25f13-e34c-4aa5-8f7c-c8d052d2a982'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_56_1']
  pz_DefTask_395:
    name: '{{Arrival}}'
    id: 'ab527032-a76d-408c-9e6d-4bfb4b508127'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_70']
  pz_DefTask_396:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'a88c7b44-7730-46b3-8177-d44ef8ef64be'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_40', '@pz_DefElem_8_40', '@pz_DefElem_9_40', '@pz_DefElem_10_40']
  pz_DefTask_397:
    name: '{{Ready}}'
    id: '531315c9-3746-4bc7-9584-01b1116b9603'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_81']
  pz_DefTask_398:
    name: '{{Arrival}}'
    id: '416c4552-b7cd-4936-85b4-cd4ae2b65742'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_71']
  pz_DefTask_399:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '26ed89fd-2ca1-490b-8c7b-4b86a7b8dd2d'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_41', '@pz_DefElem_8_41', '@pz_DefElem_9_41', '@pz_DefElem_10_41']
  pz_DefTask_400:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '52e933a8-9933-40a2-ae87-796eb260280b'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_4_28']
  pz_DefTask_401:
    name: '{{Number_of_containers}}'
    id: '6f3c45ae-07de-44e7-8e96-8f081c6f839d'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_6_15']
  pz_DefTask_402:
    name: '{{Ready}}'
    id: '47f77f7f-d850-4f9a-9dbf-490da2c1e6b2'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_57_1']
  pz_DefTask_403:
    name: '{{Arrival}}'
    id: '00043516-593f-4968-97e2-f00368542f42'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_72']
  pz_DefTask_404:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '5db0e320-f24d-4a48-9b59-2f6ceae445ec'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_7_42', '@pz_DefElem_8_42', '@pz_DefElem_9_42', '@pz_DefElem_10_42']
  pz_DefTask_405:
    name: '{{Ready}}'
    id: '298ee24a-957a-4e28-99b3-84023bd4a561'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_3_82']
  pz_DefTask_406:
    name: '{{Arrival_Collection_Area}}'
    id: '91c224ca-71cb-4f4a-ae12-e595b0301446'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_DefElem_2_73']
  pz_DefTask_407:
    name: '{{End_Return_Journey}}'
    id: 'eea35244-4e00-4897-8737-29a0ff3182fa'
    type: 'municipal_collection_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_DefElem_59_1']
