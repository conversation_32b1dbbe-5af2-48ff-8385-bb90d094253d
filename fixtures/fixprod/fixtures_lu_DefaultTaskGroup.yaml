App\Domain\Entity\DefaultTaskGroup:
  lu_DefTG_LU_1:
    defaultInterruption: '@lu_DefInt_LU_1'
    title: '{{breakdown}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'cc8da43a-a85a-44fa-8933-3d27325fa100'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_2:
    defaultInterruption: '@lu_DefInt_LU_2'
    title: '{{Accident}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '528534e0-4335-4351-a42f-d01c063f06ac'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_3:
    defaultInterruption: '@lu_DefInt_LU_3'
    title: '{{Break}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '994e7d7b-b5b3-4512-ba62-1f3efd8e4666'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_4:
    defaultInterruption: '@lu_DefInt_LU_4'
    title: '{{Other}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'df592abc-82f2-4a6a-9634-97aa3520746e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_5:
    defaultInterruption: '@lu_DefInt_LU_5'
    title: '{{Refuel}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '0f3b1183-7a7d-4656-abfc-5830f7ca7c78'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_6:
    defaultInterruption: '@lu_DefInt_LU_6'
    title: '{{Vehicle_washing}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '8364e19e-50ec-4ca5-a833-946455221f6e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_7:
    defaultInterruption: '@lu_DefInt_LU_7'
    title: '{{Internal_handling}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'bfae0371-bcfe-453e-a7a1-e39b794abbad'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_8:
    defaultInterruption: '@lu_DefInt_LU_8'
    title: '{{Vehicle_maintenance}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'efad4eb5-fdcb-4674-8085-64600dadf98e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_9:
    defaultInterruption: '@lu_DefInt_LU_9'
    title: '{{External_handling}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '142bb7e0-8d4e-466a-a545-5ecfc796eae4'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_10:
    defaultInterruption: '@lu_DefInt_LU_10'
    title: '{{Pre-loading}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '7c1f0a5a-cde6-40bd-acf7-6b9b79057b86'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_11:
    defaultInterruption: '@lu_DefInt_LU_11'
    title: '{{Way_home}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'c0ccee97-5fdd-4a48-8299-c3e4b4da0a4a'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_12:
    defaultInterruption: '@lu_DefInt_LU_12'
    title: '{{Departure_check}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '7272d158-9680-4735-a249-ab6e2d8961c9'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_14:
    defaultTermination: '@lu_DefTerm_LU_1'
    title: '{{Administrative_error}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'dec8be37-5985-4f2c-808f-2a852d035c34'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_15:
    defaultTermination: '@lu_DefTerm_LU_2'
    title: "Fiche 'en trop'"
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '8642f7cb-1105-4406-b64b-5124e4f571d3'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_16:
    defaultTermination: '@lu_DefTerm_LU_3'
    title: '{{No_equipment_available}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '853d2f31-e28e-4ea2-b7fa-233f8b1d608b'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_17:
    defaultTermination: '@lu_DefTerm_LU_4'
    title: '{{Road_blocked_works}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'af203398-9814-42bb-bf1f-37072cbb88bb'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_18:
    defaultTermination: '@lu_DefTerm_LU_5'
    title: '{{Transport_cancelled}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'bcaffadd-0fba-4076-b9c3-dff8031aac1e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_19:
    defaultTermination: '@lu_DefTerm_LU_6'
    title: '{{Other}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '799c9e03-9a8b-435f-a87d-ed81168231b0'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_20:
    defaultTermination: '@lu_DefTerm_LU_7'
    title: '{{Customer_stop_service}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'ad273298-0252-4905-b8e4-7c3cf98ab174'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_21:
    defaultTermination: '@lu_DefTerm_LU_8'
    title: '{{Customer_modifications}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '0bace400-317d-4905-92bf-2430b8437320'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_22:
    defaultTermination: '@lu_DefTerm_LU_9'
    title: '{{Customer_not_on_site}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'cd70ee81-b010-4924-a709-b578b9753d40'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_23:
    defaultTermination: '@lu_DefTerm_LU_10'
    title: '{{Crane_not_available}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '85f72658-8b2e-40aa-8497-d4cc5fbb7b1b'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_24:
    defaultTermination: '@lu_DefTerm_LU_11'
    title: '{{Impossible_placement}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '36350397-f4f6-41a0-947d-d3483bd095d4'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_25:
    defaultTermination: '@lu_DefTerm_LU_12'
    title: '{{Cannot_be_emptied}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '9f0f6a9d-7ccd-4d8e-b242-d06fdba89fbd'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_26:
    defaultTermination: '@lu_DefTerm_LU_13'
    title: '{{Container_waste_not_offered}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'a6f3a05e-c999-412a-a70a-f48886c927f0'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_27:
    defaultTermination: '@lu_DefTerm_LU_14'
    title: '{{Container_not_taken_out}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '1f733e90-6115-4638-8e9f-f0e170617ec7'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_28:
    defaultTermination: '@lu_DefTerm_LU_15'
    title: '{{Container_too_heavy/overfilled}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '809668f9-53f1-4648-80f9-9ba8f14db72b'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_29:
    defaultTermination: '@lu_DefTerm_LU_16'
    title: '{{container_empty}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '170c9b41-7826-4f99-b1ef-d95060cc2e29'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_75:
    defaultTermination: '@lu_DefTerm_LU_17'
    title: '{{Miscellaneous_to_be_billed}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '60325dcb-1984-42b7-a530-467fd406467e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_30:
    orderTypeConfig: '@lu_OrderConf_LU1'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: 'a98f87a7-3598-4f20-b2fb-8325cfe2acb2'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_61:
    orderTypeConfig: '@lu_OrderConf_LU1'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '8fda667a-06c0-48b1-af80-b6174ef75f14'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_1']
    additionalInformationItems: ['@lu_DefAddInfo_LU_1']
  lu_DefTG_LU_31:
    orderTypeConfig: '@lu_OrderConf_LU1'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: 'ccc1e0d8-66b0-47c9-849c-3cfdca23d346'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_2']
    additionalInformationItems: ['@lu_DefAddInfo_LU_2']
  lu_DefTG_LU_32:
    orderTypeConfig: '@lu_OrderConf_LU1'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '1e260d53-3fd8-4463-91b3-eebb4a3fcab1'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_3', '@lu_DefTGR_LU_4', '@lu_DefTGR_LU_36']
    additionalInformationItems: ['@lu_DefAddInfo_LU_3']
  lu_DefTG_LU_72:
    orderTypeConfig: '@lu_OrderConf_LU2'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: 'b2c32610-8020-4151-8ffb-a72f00d68ecf'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_35:
    orderTypeConfig: '@lu_OrderConf_LU2'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '63e48b05-1af6-4f41-b06b-7f137f0b3d51'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_5']
    additionalInformationItems: ['@lu_DefAddInfo_LU_4']
  lu_DefTG_LU_33:
    orderTypeConfig: '@lu_OrderConf_LU2'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '5aeb1cb2-a2ab-4b8d-a808-8c4757fbe730'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_6', '@lu_DefTGR_LU_7']
    additionalInformationItems: ['@lu_DefAddInfo_LU_5']
  lu_DefTG_LU_73:
    orderTypeConfig: '@lu_OrderConf_LU2'
    title: '{{Destination_place}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'aff026c0-f0a8-42dc-b065-8a8747eb72f4'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_8']
    additionalInformationItems: ['@lu_DefAddInfo_LU_6']
  lu_DefTG_LU_62:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '1cb9288f-20f3-4449-b051-f4e62f1d48f1'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_65:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '86a20d78-2997-49c5-9158-76bc81ec1d83'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_9']
    additionalInformationItems: ['@lu_DefAddInfo_LU_9']
  lu_DefTG_LU_36:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'f6fc2583-f22b-4a37-b5f2-2a96b4838704'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_10', '@lu_DefTGR_LU_11']
    additionalInformationItems: ['@lu_DefAddInfo_LU_10']
  lu_DefTG_LU_63:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Destination_place}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '6d241ed3-4797-494e-8899-9c4b3a2b4106'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_12']
    additionalInformationItems: ['@lu_DefAddInfo_LU_11']
  lu_DefTG_LU_38:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Customer}}'
    sequenceNumber: '60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'c1c5c9da-d34f-4b3c-af5f-b2d22c31776e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_13', '@lu_DefTGR_LU_14']
    additionalInformationItems: ['@lu_DefAddInfo_LU_13']
  lu_DefTG_LU_66:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Container_storage}}'
    sequenceNumber: '80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '3a65325e-9e05-4647-b41c-aed0d58c1a8a'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_15']
    additionalInformationItems: ['@lu_DefAddInfo_LU_15']
  lu_DefTG_LU_67:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Container_storage}}'
    sequenceNumber: '90'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '7d2922b4-4ead-47cf-912f-197f113ea110'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_16']
    additionalInformationItems: ['@lu_DefAddInfo_LU_16']
  lu_DefTG_LU_68:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Customer}}'
    sequenceNumber: '100'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '0527dfea-591a-4f13-bd8d-4721242c86c9'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_17', '@lu_DefTGR_LU_18', '@lu_DefTGR_LU_19']
    additionalInformationItems: ['@lu_DefAddInfo_LU_17']
  lu_DefTG_LU_69:
    orderTypeConfig: '@lu_OrderConf_LU3'
    title: '{{Destination_place}}'
    sequenceNumber: '110'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '591b62e8-b5ed-4a14-8df4-de80639adeba'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_20']
    additionalInformationItems: ['@lu_DefAddInfo_LU_18']
  lu_DefTG_LU_44:
    orderTypeConfig: '@lu_OrderConf_LU5'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: 'ec6282a1-c86c-4744-90b6-5e18430c6449'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_45:
    orderTypeConfig: '@lu_OrderConf_LU5'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '9ef3b507-439c-485f-84c3-26dbab86cdd2'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_21']
    additionalInformationItems: ['@lu_DefAddInfo_LU_20']
  lu_DefTG_LU_46:
    orderTypeConfig: '@lu_OrderConf_LU5'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '4cb37d3d-00e4-41dd-bf4f-e7326e450ce8'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_22', '@lu_DefTGR_LU_23']
    additionalInformationItems: ['@lu_DefAddInfo_LU_21']
  lu_DefTG_LU_47:
    orderTypeConfig: '@lu_OrderConf_LU5'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '72e9d3da-4232-4ebe-a999-77cb0c204485'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_24']
    additionalInformationItems: ['@lu_DefAddInfo_LU_22']
  lu_DefTG_LU_59:
    tourDataConfig: '@lu_TourConf_LU_1'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '5a6757af-d644-4f3d-a433-8c7a362d4307'
    tenant: '<getLuxembourgTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_60:
    equipmentConfig: '@lu_EquiConf_LU_1'
    title: '{{Vehicle}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '900a6048-894f-4471-9a86-1fc3d541d5b9'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_76:
    orderTypeConfig: '@lu_OrderConf_LU6'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '304a350c-4488-4372-a233-500477bb186e'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_77:
    orderTypeConfig: '@lu_OrderConf_LU6'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '313cf6a4-6b18-4037-8a53-bd1369f64d7b'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_25']
    additionalInformationItems: ['@lu_DefAddInfo_LU_23']
  lu_DefTG_LU_78:
    orderTypeConfig: '@lu_OrderConf_LU6'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: 'efe18f8e-045d-478b-a0f9-04df39c400c5'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_26']
    additionalInformationItems: ['@lu_DefAddInfo_LU_24']
  lu_DefTG_LU_79:
    orderTypeConfig: '@lu_OrderConf_LU6'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'ca34273b-0dff-44c9-a684-a794afd50f57'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_27', '@lu_DefTGR_LU_28', '@lu_DefTGR_LU_29']
    additionalInformationItems: ['@lu_DefAddInfo_LU_25']
  lu_DefTG_LU_80:
    orderTypeConfig: '@lu_OrderConf_LU6'
    title: '{{Destination_place}}'
    sequenceNumber: '50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'f8db0efb-1623-4170-ba56-25ecc748748d'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_30']
    additionalInformationItems: ['@lu_DefAddInfo_LU_26']
  lu_DefTG_LU_83:
    orderTypeConfig: '@lu_OrderConf_LU7'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '589db758-d667-4442-90e6-9a5ef8710559'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_84:
    orderTypeConfig: '@lu_OrderConf_LU7'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '7a515b91-08ba-46de-9d4a-839cf1ec3ad8'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_31']
  lu_DefTG_LU_85:
    orderTypeConfig: '@lu_OrderConf_LU7'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '37fe8fad-1233-448d-ae87-3877107e49a8'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_32', '@lu_DefTGR_LU_33']
  lu_DefTG_LU_86:
    orderTypeConfig: '@lu_OrderConf_LU8'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '96842734-5092-4b05-97f1-1ad074d36bcc'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_87:
    orderTypeConfig: '@lu_OrderConf_LU8'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '094761ca-ce74-4085-8669-e9053d292438'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_34']
  lu_DefTG_LU_88:
    orderTypeConfig: '@lu_OrderConf_LU9'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'd7fc2538-242e-47b0-8144-5d9a7e5a1135'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_89:
    orderTypeConfig: '@lu_OrderConf_LU10'
    title: '{{Destination_place}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '87afcaf9-c55e-4451-80c0-a29c090ace83'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_DefTG_LU_90:
    orderTypeConfig: '@lu_OrderConf_LU10'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '79d53964-c7a8-4850-88bd-e85251cc3bfb'
    tenant: '<getLuxembourgTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_DefTGR_LU_35']
