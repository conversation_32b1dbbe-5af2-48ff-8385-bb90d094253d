App\Domain\Entity\ValueObject\AdditionalInformation:
  lu_DefAddInfo_LU_1:
    __construct: { sequence: 10, text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_2:
    __construct: { sequence: 10, text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_3:
    __construct: { sequence: 10, text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_4:
    __construct: { sequence: 10, text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_5:
    __construct: { sequence: 10, text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_6:
    __construct: { sequence: 10, text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_9:
    __construct: { sequence: 10, text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_10:
    __construct: { sequence: 10, text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_11:
    __construct: { sequence: 10, text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_13:
    __construct: { sequence: 10, text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_15:
    __construct: { sequence: 10, text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_16:
    __construct: { sequence: 10, text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_17:
    __construct: { sequence: 10, text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_18:
    __construct: { sequence: 10, text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_20:
    __construct: { sequence: 10, text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_21:
    __construct: { sequence: 10, text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_22:
    __construct: { sequence: 10, text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_23:
    __construct: { sequence: 10, text: '{{Pick_up_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_24:
    __construct: { sequence: 10, text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_25:
    __construct: { sequence: 10, text: '{{Fill_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
  lu_DefAddInfo_LU_26:
    __construct: { sequence: 10, text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>' }
