App\Domain\Entity\OrderTypeConfig:
  nl_OrderConf_NL_1:
    name: '{{service}}'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::NL_1)>'
    id: 'd24adb60-b4a3-4c2d-84f7-e1cbf0ce384f'
    tenant: '<getNetherlandsTenant()>'
  nl_OrderConf_NL_2:
    name: '{{service}}'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::NL_2)>'
    id: 'b9ec59c6-570a-4f99-924b-f0cabb17b5cd'
    tenant: '<getNetherlandsTenant()>'
  nl_OrderConf_NL_3:
    name: '{{service}}'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::NL_3)>'
    id: 'cc92f456-6edd-49b6-808c-5c75396ea8ad'
    tenant: '<getNetherlandsTenant()>'
  nl_OrderConf_NL_4:
    name: '{{service}}'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::NL_4)>'
    id: 'ba5fdc18-a3fe-4e0a-ade2-39c0074c56a5'
    tenant: '<getNetherlandsTenant()>'
