App\Domain\Entity\ValueObject\Element:
  lu_DefElem_LU_1_1:
    __construct: { id: 227939cc-e9fd-5b3b-a008-fd88bf99b8f2, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_3', '@lu_DefElemOption_LU_22', '@lu_DefElemOption_LU_4'] }
  lu_DefElem_LU_2_1:
    __construct: { id: bf9f6685-2f09-5029-be9d-26ffee000234, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_2:
    __construct: { id: 4a88b310-db1a-52bc-a5a1-bbf8ea30568b, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_3:
    __construct: { id: 9b02a2b0-4485-590b-9afa-c7415de053b6, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_4:
    __construct: { id: a95f8e41-7638-52e6-9147-6bc548360170, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_5:
    __construct: { id: ec2c75b7-abd1-5f4d-8ea1-38b0c17fed3e, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_6:
    __construct: { id: 65952dd7-5044-5839-9445-a676ee9fae22, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_7:
    __construct: { id: 7d05ee73-c04a-502f-b538-69d5cf1c1f0a, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_8:
    __construct: { id: ae74b470-729c-50e0-bdbc-0a4fabc14968, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_9:
    __construct: { id: 491d18c0-5ad6-51e3-97af-aca4839b2956, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_10:
    __construct: { id: b493a3c1-91ef-5433-870b-9c0f8d221180, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_11:
    __construct: { id: a24f6de9-823e-57c7-b97f-8f5e60a96cb6, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_12:
    __construct: { id: 4ba5bbf7-068c-5909-ac22-cf6a06263420, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_13:
    __construct: { id: 2b105965-d63b-5481-94ca-478cea9bfc45, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_14:
    __construct: { id: 3a74b490-6daf-59aa-be40-20353295367e, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_15:
    __construct: { id: 4f4f5540-329b-5fdb-a407-a4352033c6b2, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_16:
    __construct: { id: 97cb32d9-c2c7-59a4-a9ef-c909c932fddf, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_17:
    __construct: { id: a9522af8-ea90-512c-a15c-3ca2307cb8d4, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_18:
    __construct: { id: 9fae9ae6-87e2-5ceb-b3ec-9b08e9b5e307, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_19:
    __construct: { id: 4a23fa74-66ee-5c53-b17b-1bca7aaaf96a, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_20:
    __construct: { id: f7401133-d0ea-58ae-ba5a-2dcd2fdff6a9, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_21:
    __construct: { id: 8b58bf14-69af-5702-bac4-dd0174efd3df, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_22:
    __construct: { id: f4a552ad-84ce-5bd3-84ac-dbe78370f592, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_23:
    __construct: { id: 6e579ea2-2ac2-5e66-95ae-1cc03741a095, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_24:
    __construct: { id: 1cefeb5d-7efc-588b-9e77-cb0f418e509f, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_25:
    __construct: { id: 11539934-6a66-5939-bfc8-71d29ce1344a, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_26:
    __construct: { id: 0778e80f-10be-5891-b685-b9550153f4d8, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_27:
    __construct: { id: 2b48211e-16e7-5743-9479-add190fcac1a, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_28:
    __construct: { id: 40f2be61-7b6f-5b36-9d29-cc7d1d17914e, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_29:
    __construct: { id: 1aa7152a-312f-5aed-b898-bf8c9f211c65, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_30:
    __construct: { id: 36bc086a-09e1-5300-97a0-46a82fb2bf02, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_31:
    __construct: { id: 39d26367-d79c-5db5-85fb-d11e8e9f23ff, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_2_32:
    __construct: { id: 8b3ba88d-781a-5638-90eb-5e1482b02297, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrival}}', label: '{{Arrival}}', referenceType: arrival, patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_1:
    __construct: { id: b5a570b7-07a7-5fd3-bd51-3e6bb2f860fa, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_2:
    __construct: { id: 19954aa2-c23e-503c-973e-5e2fc1ec15e7, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_3:
    __construct: { id: f3aa72d2-8cf4-5943-a3f3-60f8e340db31, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_4:
    __construct: { id: 304389e5-72cf-5b8b-afee-4f769c2aa83d, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_5:
    __construct: { id: 6617a24a-84a1-5377-adbf-fa3734b9a1c6, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_6:
    __construct: { id: 1b3bd85c-92bb-522e-bcd4-499da54e5a7c, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_7:
    __construct: { id: a224bd95-f894-580e-922b-39b148c4a592, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_8:
    __construct: { id: 040637a0-3a05-5941-83e1-d519ffb9c1be, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_9:
    __construct: { id: 884bf38e-cd79-5e3e-a460-19ea7c8f1403, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_10:
    __construct: { id: 4bc945a8-ceff-55f8-9e76-84787d43e38a, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_11:
    __construct: { id: 4f097c18-c8f6-5636-badf-06ec86d0dfcf, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_12:
    __construct: { id: af60fc3f-858e-5c64-b76f-545b2bd277e5, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_13:
    __construct: { id: c49e7c68-59cd-5964-aef0-b0ce09fd69cc, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_14:
    __construct: { id: 05250b78-8bb8-5e0e-90df-b670311e711b, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_15:
    __construct: { id: b65a0da8-015f-5834-9746-2bd9051ce792, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_16:
    __construct: { id: 762f0f76-080a-58a3-8c07-5efc1dc9b2c9, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_17:
    __construct: { id: 5d48c5a8-0b64-51b0-9a04-a98a51bb1420, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_18:
    __construct: { id: 4bb56a8d-d098-506b-a449-c65396313d80, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_19:
    __construct: { id: d1afe3c0-4cb2-5135-9682-65e6a5a78468, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_20:
    __construct: { id: ad1c4a94-f1bc-5177-9e6d-a3c241c6bf1d, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_21:
    __construct: { id: e2bbec91-3f1d-5151-8f08-456787ca93f0, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_22:
    __construct: { id: a3df850b-ceba-5ff6-8459-3a0946cf22ea, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_23:
    __construct: { id: 6210d1cb-183e-553a-a8a7-981e5a8eaa83, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_24:
    __construct: { id: 209a2a3e-5472-5105-8baa-c47fe6885f0b, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_25:
    __construct: { id: 502d0180-e3f5-557c-9281-458ec571c5db, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_26:
    __construct: { id: 7d596e20-05ca-50ca-805e-fd8981dfff0b, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_27:
    __construct: { id: 1e3db45c-546f-52c6-a2f1-16907348976a, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_28:
    __construct: { id: 039dd3e3-4375-5d87-a88d-e4eeaa1657d0, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_29:
    __construct: { id: 2c5247c4-1277-5def-b0f8-3bb612305a00, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_30:
    __construct: { id: bd4539fd-043d-5136-9896-30e19c0dedec, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_31:
    __construct: { id: c08dd1a7-d6c5-51cb-808f-31f27dff2614, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_32:
    __construct: { id: 53bb0d39-ce9c-5989-99db-c42e86410bf4, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_33:
    __construct: { id: 6ff030a9-5199-552a-bafb-0e23f4c86491, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_34:
    __construct: { id: 1ccad045-0192-52c3-8f1a-cca7323e5d70, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_35:
    __construct: { id: 4458e1bb-da41-566f-b6a3-6493366ab245, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_36:
    __construct: { id: 2f5824e6-fa88-50aa-b319-c0d6bb687781, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_37:
    __construct: { id: 5d0feacf-8a81-5625-9abb-b50ae1cc2059, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_38:
    __construct: { id: e06bdbb8-1875-5028-9df4-a65dbbdd003b, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_39:
    __construct: { id: e7f35537-1b8a-515c-98c2-fde9594fc14c, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_40:
    __construct: { id: 7eb2cc61-5a3d-5e40-841c-532db4ca0179, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_41:
    __construct: { id: d9c6efb7-6fd4-5b3d-a5a5-d2e720d4956f, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_3_42:
    __construct: { id: 6e3713c2-1839-561a-b436-2968e3bbcbac, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Ready}}', label: '{{Ready}}', referenceType: continue, patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_DefElem_LU_4_1:
    __construct: { id: e7000e47-1537-56a4-9bb0-81d063db941b, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_2:
    __construct: { id: bcdb4150-3d19-5f4a-a8cf-bcc989be0aa6, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_3:
    __construct: { id: e4078566-9728-5d8f-8f75-fff27c4f9a28, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_4:
    __construct: { id: 6cf95c6a-c566-54fc-ba17-df85dbb12fd2, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_5:
    __construct: { id: 0cab39bc-ed8b-575d-b149-76eaaa4d2c03, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_6:
    __construct: { id: 9a2fad22-a83d-5f03-a3c4-9a6bccbf8513, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_7:
    __construct: { id: 6ce43406-f5fb-5a91-83b4-d8e69781cb28, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_8:
    __construct: { id: 58662cb0-fe04-5c83-899a-10392c34b921, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_9:
    __construct: { id: 2f8d7889-298c-59a2-bd59-0b28ed07f659, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_10:
    __construct: { id: 16ecf055-f49c-5d28-a923-59304c55803e, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_11:
    __construct: { id: 1266289c-f9ac-5a50-a39e-069a5d2515d3, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_4_12:
    __construct: { id: cd6c793a-f981-557e-b182-8311cd096901, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', referenceType: signature, patternHint: '{{widget/bottom_sheet/signature/name_title}}', showAdditionalInput: false, sequenceNumber: 40 }
  lu_DefElem_LU_7_1:
    __construct: { id: 247c2ed6-69cb-57b4-bc82-f78add99ff41, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', referenceType: ticketCode, patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_7_2:
    __construct: { id: c64332a3-4ce7-57a2-aa5d-6c6d674c102b, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', referenceType: ticketCode, patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_7_3:
    __construct: { id: 23181794-9064-5196-aeae-174aeb59d144, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', referenceType: ticketCode, patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_7_4:
    __construct: { id: a1ef15d9-6b52-549c-beb8-f6e60c5f98b1, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', referenceType: ticketCode, patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_7_5:
    __construct: { id: 3ddae2bc-be1b-55a1-a083-f36460918e0c, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', referenceType: ticketCode, patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_8_1:
    __construct: { id: 1bd519ff-2a91-56be-a89f-ed84f1a0bd49, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_start_weight}}', label: '{{Please_enter_start_weight}}', referenceType: startWeight, patternHint: '{{Quantity}}', sequenceNumber: 20, options: ['@lu_DefElemOption_LU_1', '@lu_DefElemOption_LU_2'] }
  lu_DefElem_LU_8_2:
    __construct: { id: 1786690f-5dcc-529f-8b3c-280a207407f6, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_start_weight}}', label: '{{Please_enter_start_weight}}', referenceType: startWeight, patternHint: '{{Quantity}}', sequenceNumber: 20, options: ['@lu_DefElemOption_LU_1', '@lu_DefElemOption_LU_2'] }
  lu_DefElem_LU_8_3:
    __construct: { id: e6a49f63-c435-5fb8-a53f-9a135e3b3462, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_start_weight}}', label: '{{Please_enter_start_weight}}', referenceType: startWeight, patternHint: '{{Quantity}}', sequenceNumber: 20, options: ['@lu_DefElemOption_LU_1', '@lu_DefElemOption_LU_2'] }
  lu_DefElem_LU_8_4:
    __construct: { id: ba49b248-a1e0-585f-8a3e-96deec92fac9, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_start_weight}}', label: '{{Please_enter_start_weight}}', referenceType: startWeight, patternHint: '{{Quantity}}', sequenceNumber: 20, options: ['@lu_DefElemOption_LU_1', '@lu_DefElemOption_LU_2'] }
  lu_DefElem_LU_8_5:
    __construct: { id: 17574414-0fea-5c75-a1fb-3c9c52401f7a, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_start_weight}}', label: '{{Please_enter_start_weight}}', referenceType: startWeight, patternHint: '{{Quantity}}', sequenceNumber: 20, options: ['@lu_DefElemOption_LU_1', '@lu_DefElemOption_LU_2'] }
  lu_DefElem_LU_9_1:
    __construct: { id: a99a9eba-5a5e-5200-8a3a-9547ea354990, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_end_weight}}', label: '{{Please_enter_end_weight}}', referenceType: endWeight, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_DefElem_LU_9_2:
    __construct: { id: 3a1c54a1-d6be-5ac3-ad9c-14174475c225, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_end_weight}}', label: '{{Please_enter_end_weight}}', referenceType: endWeight, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_DefElem_LU_9_3:
    __construct: { id: d95581cb-aeed-5e99-9e80-def5c14d47a3, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_end_weight}}', label: '{{Please_enter_end_weight}}', referenceType: endWeight, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_DefElem_LU_9_4:
    __construct: { id: e1e5f8bf-a294-5e2e-86b4-dbc280b3c137, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_end_weight}}', label: '{{Please_enter_end_weight}}', referenceType: endWeight, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_DefElem_LU_9_5:
    __construct: { id: b3f4af4d-f32b-5957-b7eb-6eeeeda4ca8c, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_end_weight}}', label: '{{Please_enter_end_weight}}', referenceType: endWeight, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_DefElem_LU_10_1:
    __construct: { id: 51fa2d93-2e30-5a35-b76b-d5d73b122ff6, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 40 }
  lu_DefElem_LU_10_2:
    __construct: { id: d899a8f2-e1bd-5789-8993-46c39f48d496, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 40 }
  lu_DefElem_LU_10_3:
    __construct: { id: 8abd5f17-8981-52ba-9e0f-9995b94527a2, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 40 }
  lu_DefElem_LU_10_4:
    __construct: { id: 4a48ae68-d7ac-5d32-90d0-a430f56e9355, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 40 }
  lu_DefElem_LU_10_5:
    __construct: { id: 1be5e675-03a3-5078-b500-2ce9e2618b49, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 40 }
  lu_DefElem_LU_13_1:
    __construct: { id: 1b091dd6-c3d7-5eb8-bb77-cb177a682f41, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_11', '@lu_DefElemOption_LU_12'] }
  lu_DefElem_LU_14_1:
    __construct: { id: 8dd25c97-067a-5714-8f61-f874bce70652, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_specify_reason}}', label: '{{Please_specify_reason}}', referenceType: freetext, patternHint: '{{Free_text}}', sequenceNumber: 10 }
  lu_DefElem_LU_14_2:
    __construct: { id: e560d02e-a747-5785-84ad-7616adcab633, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_specify_reason}}', label: '{{Please_specify_reason}}', referenceType: freetext, patternHint: '{{Free_text}}', sequenceNumber: 10 }
  lu_DefElem_LU_14_3:
    __construct: { id: 359617b8-9d0e-5242-b1a2-9ff82e71e3e0, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_specify_reason}}', label: '{{Please_specify_reason}}', referenceType: freetext, patternHint: '{{Free_text}}', sequenceNumber: 10 }
  lu_DefElem_LU_15_1:
    __construct: { id: fc612fcc-7306-5c27-95e7-8c7414c63ecc, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_diesel_quantity_in_liters}}', label: '{{Please_enter_diesel_quantity_in_liters}}', referenceType: dieselAmount, patternHint: '{{Diesel}}', sequenceNumber: 10 }
  lu_DefElem_LU_16_1:
    __construct: { id: 9f17e593-0403-5676-a99c-bdad6e7f13dc, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_AdBlue_quantity}}', label: '{{Please_enter_AdBlue_quantity}}', referenceType: adBlueAmount, patternHint: '{{AdBlue}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_1:
    __construct: { id: f5b03ea7-30bf-5ecd-aa29-20db028564cb, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 50 }
  lu_DefElem_LU_17_2:
    __construct: { id: ceb4bbbf-18a4-5e82-a591-c871492a1f6b, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 50 }
  lu_DefElem_LU_17_3:
    __construct: { id: 14100cb5-8c38-5e31-ad74-f423762704bc, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_4:
    __construct: { id: ec15d757-f743-57d5-ae04-258da6953914, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_5:
    __construct: { id: 6927da4e-3cf1-5c9c-936a-7539554a96a6, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_6:
    __construct: { id: 413e25b1-ee8c-5f98-abd5-46c66b8dba43, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_7:
    __construct: { id: 586b38d9-eb48-5b75-9db8-3250bfbef052, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 50 }
  lu_DefElem_LU_17_8:
    __construct: { id: c26b3104-2643-5670-bc6f-f12df4fc777d, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_9:
    __construct: { id: f3ca0d46-52ab-54e5-804d-9886f756ac55, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_10:
    __construct: { id: e7a12891-6771-5762-b8eb-6c727580c77b, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_11:
    __construct: { id: 3ffe0c39-0259-5d2e-b37b-7600fd7abca3, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_12:
    __construct: { id: 9de057e5-14cc-58ab-8e43-fe4bfafadbd1, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_13:
    __construct: { id: 879ac82c-646f-5ed7-bf96-963cc1f37c97, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_14:
    __construct: { id: 4fd56f41-fbc4-5c9f-b8bc-c09d00419b0d, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_15:
    __construct: { id: 9d170673-2ed9-5941-b7ff-b6e3b5da9f8f, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_16:
    __construct: { id: 48f738d5-0f8b-5b2e-8b4a-172875f7738d, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_17:
    __construct: { id: 2cc9a3ce-4965-52fe-b5bb-167f5bc11316, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_18:
    __construct: { id: 931c95a4-4dfb-5745-9f20-49433f3331e3, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_19:
    __construct: { id: c22cf9f2-02bc-5a4d-b59f-3883960adb8f, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_20:
    __construct: { id: a53e997b-235e-52ba-8215-26d960ad303a, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_21:
    __construct: { id: 56cd8c96-abf6-522d-8e16-b2b5789084a1, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_22:
    __construct: { id: b7f1d3a0-2e26-5095-87a3-b408a619deb5, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_23:
    __construct: { id: 337c6d48-df55-5588-a71d-5639ab903660, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_24:
    __construct: { id: b7741462-e8ff-508e-a52e-d58e1302e71d, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_25:
    __construct: { id: fd9615b0-4b34-5ec9-8098-419e215e8121, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_26:
    __construct: { id: 28386d05-dd91-54a7-b677-027394b7014f, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_27:
    __construct: { id: 7be25015-e82f-5f64-8448-3cba96a6cb7e, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_28:
    __construct: { id: 6468a070-9b03-5629-9e1e-f36cb68d1b65, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_29:
    __construct: { id: e5f57733-63f3-53fd-8e19-b15eb43bcfc8, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_30:
    __construct: { id: 2f156adf-5874-51d4-aeaa-48ebc09ed329, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_31:
    __construct: { id: 9fb27284-845e-5c31-beea-10661a583962, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_32:
    __construct: { id: e6999078-983a-5aa9-a51e-a4913f81a119, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_33:
    __construct: { id: f365dfb3-4921-5a2f-8a5d-2549640c3690, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_34:
    __construct: { id: e9ae6e63-487e-5d0f-987f-5786bd6ab6d6, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_35:
    __construct: { id: d4ee59cc-544e-5e13-b0f0-25e74b7ad607, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_17_36:
    __construct: { id: e2e8dfe7-693b-5e37-a519-e32711aadae9, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 50 }
  lu_DefElem_LU_17_37:
    __construct: { id: 473462a8-2f2b-5705-965d-4d6278355fc4, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, referenceType: photo, patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_DefElem_LU_18_1:
    __construct: { id: d3b5bb84-fd12-5728-bba7-10982a3fb0e0, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_record_initial_mileage}}', label: '{{Please_record_initial_mileage}}', referenceType: startMileage, patternHint: '{{Initial_mileage}}', sequenceNumber: 10 }
  lu_DefElem_LU_19_1:
    __construct: { id: 7bb01093-9112-5ffb-a0dc-acecef88c718, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_final_mileage}}', label: '{{Please_enter_final_mileage}}', referenceType: endMileage, patternHint: '{{Final_mileage}}', sequenceNumber: 10 }
  lu_DefElem_LU_20_1:
    __construct: { id: bede070e-f1fd-5350-821e-e31e7430530e, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Departure_check_completed?}}', label: '{{Departure_check_completed?}}', referenceType: vehicleCheck, patternHint: '{{Departure_check_completed?}}', sequenceNumber: 10 }
  lu_DefElem_LU_25_1:
    __construct: { id: 200265d0-45a2-5397-8000-77a3768279fc, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Material}}', label: '{{Material}}', referenceType: additionalServiceMaterial, patternHint: '{{What_is_the_additional_service?}}', sequenceNumber: 10 }
  lu_DefElem_LU_26_1:
    __construct: { id: fabc8845-4d85-54f5-a05b-aaabb3e85df1, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Unit}}', label: '{{Unit}}', referenceType: additionalServiceUnit, patternHint: '{{In_which_unit_should_the_additional_service_be_recorded?}}', sequenceNumber: 20 }
  lu_DefElem_LU_27_1:
    __construct: { id: beb1f466-289c-59ac-8327-3afe1a83fe8e, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Quantity}}', label: '{{Quantity}}', referenceType: additionalServiceValue, patternHint: '{{What_is_the_quantity?}}', sequenceNumber: 30 }
  lu_DefElem_LU_28_1:
    __construct: { id: db54da97-9182-5e42-986d-67c102519cc3, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_2:
    __construct: { id: da5cdf37-8049-5bee-8225-6312bed45d91, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_3:
    __construct: { id: 94b62d48-ba35-5893-89c2-b60164148567, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_4:
    __construct: { id: a17b7647-6d62-5d3f-b8f2-ed7c881e3043, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_5:
    __construct: { id: 4769c003-b13d-5082-8ce2-df70d23f6db9, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_6:
    __construct: { id: 4c83a4b4-456f-50e1-ae51-9b5d5dd6876a, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_7:
    __construct: { id: 8fb8ed14-0946-5f35-bf63-0c736e98562d, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_8:
    __construct: { id: b1741113-7289-522f-95b5-b46ad5a82213, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_9:
    __construct: { id: 91032ec3-7777-5797-aa0b-d51965bef84d, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_10:
    __construct: { id: 742e9521-d881-50cb-98c6-ad0622615621, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_11:
    __construct: { id: bedf8a4d-97ed-5068-8017-33498fedcebb, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_12:
    __construct: { id: ca7fd6cd-ba6b-5127-ab10-c2c7d31ac30d, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_13:
    __construct: { id: 6e346f2b-2e76-5d1c-bbf8-7f2e6cd20363, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_14:
    __construct: { id: 822bbb47-c929-5a20-931a-b5f23814079f, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_15:
    __construct: { id: 82be2436-442d-512a-ae35-b335d8c216a0, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_16:
    __construct: { id: 5e08a25a-5bd5-5168-b2b2-5d24032fc499, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_17:
    __construct: { id: 5a433c63-3253-5509-bc76-085e8c8938bb, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_18:
    __construct: { id: 4e1d7718-a6ac-5b27-843c-406bb72e4211, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_19:
    __construct: { id: 8b5182df-8ce4-5053-9f66-7c95c0b048d7, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_28_20:
    __construct: { id: d4fdeadf-dd51-5ed9-867a-0412c44bf161, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_container_number}}', label: '{{Please_enter_container_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_container_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_31_1:
    __construct: { id: 8b5a600b-8fba-5497-b54e-16a91f01cf7e, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Current_mileage}}', label: '{{Current_mileage}}', referenceType: acutalMileage, patternHint: '{{Current_mileage}}', sequenceNumber: 10 }
  lu_DefElem_LU_31_2:
    __construct: { id: 60af8f5b-f317-5c0b-8618-14143bc5f241, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Current_mileage}}', label: '{{Current_mileage}}', referenceType: acutalMileage, patternHint: '{{Current_mileage}}', sequenceNumber: 10 }
  lu_DefElem_LU_33_1:
    __construct: { id: 0b5ee961-23f8-55c1-8611-54e190ba9f18, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_23', '@lu_DefElemOption_LU_24'] }
  lu_DefElem_LU_34_1:
    __construct: { id: 2f8c5eba-4f7b-54fe-8492-b0fafdad93cf, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_29', '@lu_DefElemOption_LU_30', '@lu_DefElemOption_LU_31', '@lu_DefElemOption_LU_32', '@lu_DefElemOption_LU_33'] }
  lu_DefElem_LU_35_1:
    __construct: { id: 6d391874-fd29-5acd-aaad-bff323e91404, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10 }
  lu_DefElem_LU_36_1:
    __construct: { id: 128c972a-0b34-54b5-8ae6-50a28dac9334, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_25', '@lu_DefElemOption_LU_26'] }
  lu_DefElem_LU_37_1:
    __construct: { id: 62e135fd-e31d-5eca-86fa-959cb8b35815, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Emptying_possible?}}', label: '{{Emptying_possible?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_27', '@lu_DefElemOption_LU_28'] }
  lu_DefElem_LU_38_1:
    __construct: { id: 99554bb1-4444-5c0c-b38b-5b90aa93d3fd, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Emptying_possible?}}', label: '{{Emptying_possible?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_36', '@lu_DefElemOption_LU_37'] }
  lu_DefElem_LU_39_1:
    __construct: { id: fed8689f-b256-5cdc-a865-026631debf68, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Emptying_possible?}}', label: '{{Emptying_possible?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_34', '@lu_DefElemOption_LU_35'] }
  lu_DefElem_LU_40_1:
    __construct: { id: f140239e-9ca1-5a9f-a11f-fdc7f2784867, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{WAITING_NOT_EMPTYING}}', label: '{{WAITING_NOT_EMPTYING}}', referenceType: 'ATTENTE PAS VIDANGE', patternHint: '{{WAITING_NOT_EMPTYING}}', sequenceNumber: 10 }
  lu_DefElem_LU_40_2:
    __construct: { id: 0db24d13-e296-53da-a3c5-fee68a644814, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{WAITING_NOT_EMPTYING}}', label: '{{WAITING_NOT_EMPTYING}}', referenceType: 'ATTENTE PAS VIDANGE', patternHint: '{{WAITING_NOT_EMPTYING}}', sequenceNumber: 10 }
  lu_DefElem_LU_41_1:
    __construct: { id: 5d27aefb-9492-5370-b597-c857db1760a3, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Waiting_time}}', label: '{{Waiting_time}}', referenceType: "TEMPS D'ATTENTE", patternHint: '{{Waiting_time}}', sequenceNumber: 10 }
  lu_DefElem_LU_41_2:
    __construct: { id: ea485cd5-66ce-5982-b7fc-12835ebbc24f, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Waiting_time}}', label: '{{Waiting_time}}', referenceType: "TEMPS D'ATTENTE", patternHint: '{{Waiting_time}}', sequenceNumber: 10 }
  lu_DefElem_LU_41_3:
    __construct: { id: 8f63f596-a0ba-5bf3-9c65-da4ecba7116a, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Waiting_time}}', label: '{{Waiting_time}}', referenceType: "TEMPS D'ATTENTE", patternHint: '{{Waiting_time}}', sequenceNumber: 10 }
  lu_DefElem_LU_42_1:
    __construct: { id: 821829d8-9f1c-537a-8255-81985bbb4b6e, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{LOADING_TIME}}', label: '{{LOADING_TIME}}', referenceType: 'TEMPS DE CHARGEMENT', patternHint: '{{LOADING_TIME}}', sequenceNumber: 10 }
  lu_DefElem_LU_42_2:
    __construct: { id: 1c54402c-6e5a-54d7-a6ae-15b2a006a79c, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{LOADING_TIME}}', label: '{{LOADING_TIME}}', referenceType: 'TEMPS DE CHARGEMENT', patternHint: '{{LOADING_TIME}}', sequenceNumber: 10 }
  lu_DefElem_LU_43_1:
    __construct: { id: 470bb9cf-dbfc-5d99-8cd3-5a4b0bb6a44b, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_2:
    __construct: { id: 596e917e-79e1-5a31-97fd-54571f84f67f, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_3:
    __construct: { id: 7a9e4738-4019-5ba8-8d4d-62f733ce9769, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_4:
    __construct: { id: a9276648-38e6-5123-a4e2-934607075ea0, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_5:
    __construct: { id: ea77d3e8-4451-5e7a-916f-9b3d2a9b6008, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_6:
    __construct: { id: 45103156-800c-5d43-9b17-041e59da11b2, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_7:
    __construct: { id: d5dc4fe4-6276-5fcb-815e-ddcc8b4e8f73, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_8:
    __construct: { id: b6e57975-602a-5d41-8de5-8856f5f3a584, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_9:
    __construct: { id: 7f54516e-d0f9-5a6b-984d-9557e6eab814, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_10:
    __construct: { id: a72b655a-658e-505b-98a8-f01b987e423d, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_11:
    __construct: { id: defb5790-a7be-5d2d-9b91-a7c5eee75734, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_43_12:
    __construct: { id: 043b1d4a-231a-5540-bd5f-1e8938cca251, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{firstname}}', label: '{{firstname}}', referenceType: firstname, patternHint: '{{firstname}}', hasAutocomplete: true, sequenceNumber: 10 }
  lu_DefElem_LU_44_1:
    __construct: { id: 4d757a26-1483-5bb8-b4aa-b0ad1ecfca0e, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_2:
    __construct: { id: 20be5109-3273-588c-b47d-b71f729aab54, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_3:
    __construct: { id: d26ea23c-8869-5615-9840-b7df75890f2b, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_4:
    __construct: { id: ea9fefd4-790d-5037-bb62-ea2590878234, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_5:
    __construct: { id: 12dd482f-b841-5e80-b7a1-8735386e0859, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_6:
    __construct: { id: 9286c505-31fc-5c67-95dd-07a443491c7c, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_7:
    __construct: { id: 3d3f98d2-e599-53a0-91fc-02e6ea39b788, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_8:
    __construct: { id: 52765e03-723e-573d-8c0f-c78f4a61950f, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_9:
    __construct: { id: 6e97607d-e684-512b-85ef-397dd27daec8, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_10:
    __construct: { id: d977b9fa-3b91-5ed1-8b3b-2d9d305a1f16, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_11:
    __construct: { id: 446ead31-37b3-5946-b76e-484340aa1494, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_44_12:
    __construct: { id: 2b75f630-15d3-5871-8502-4f9147dad19f, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{lastname}}', label: '{{lastname}}', referenceType: lastname, patternHint: '{{lastname}}', hasAutocomplete: true, sequenceNumber: 20 }
  lu_DefElem_LU_45_1:
    __construct: { id: 00cecd51-4371-5c58-8bcc-7542ddb22fb0, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_2:
    __construct: { id: 2afd7ee9-d625-5f39-825d-288fb97c21e2, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_3:
    __construct: { id: 7f64d8cb-4a89-51d2-bac3-c26dd799936d, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_4:
    __construct: { id: 242323f5-5716-5b03-8abf-636c16fa0d0e, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_5:
    __construct: { id: fa8608ed-9637-5f28-9ee7-76a1a6110916, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_6:
    __construct: { id: 6576e65f-c88d-5c44-9ac5-cf77b8e0128a, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_7:
    __construct: { id: c067225f-d49c-57c7-80b7-476272ff8541, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_8:
    __construct: { id: fecbd942-fe17-5ddb-bb38-89839b9c6b01, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_9:
    __construct: { id: b4957b86-8471-5f16-95b3-3b5a70c17384, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_10:
    __construct: { id: c5fce745-5598-5cb0-b660-43492727f3b9, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_45_11:
    __construct: { id: 17f7dac1-c7b5-5ae3-b084-00e36ec04500, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{email}}', label: '{{email}}', referenceType: email, patternHint: '{{email}}', hasAutocomplete: true, sequenceNumber: 30 }
  lu_DefElem_LU_46_1:
    __construct: { id: 39072166-3151-54b6-b0ad-43767de87a7a, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_38', '@lu_DefElemOption_LU_39', '@lu_DefElemOption_LU_40'] }
  lu_DefElem_LU_47_1:
    __construct: { id: 3df1c6dd-166c-5c2e-b9a6-60b4336a57bd, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Emptying_possible?}}', label: '{{Emptying_possible?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_41', '@lu_DefElemOption_LU_42'] }
  lu_DefElem_LU_48_1:
    __construct: { id: 1fb8ff92-c23c-5491-b4dd-82172ffb4b72, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', referenceType: taskgroupPicker, patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_43', '@lu_DefElemOption_LU_44'] }
  lu_DefElem_LU_49_1:
    __construct: { id: cbd5cc26-fd6c-51c8-9deb-bda4eefb8d29, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 900f112b-6528-4dcc-973c-4468397f732d, placeholder: '{{How_is_the_condition_of_the_toilet?}}', label: '{{How_is_the_condition_of_the_toilet?}}', referenceType: conditionOfToilet, patternHint: '{{How_is_the_condition_of_the_toilet?}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_45', '@lu_DefElemOption_LU_46', '@lu_DefElemOption_LU_47', '@lu_DefElemOption_LU_48'] }
  lu_DefElem_LU_50_1:
    __construct: { id: e199980d-cf98-5f69-b326-07e3f29f442f, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 4fedb251-b861-4b43-83b9-2a47210f4d67, placeholder: '{{How_is_the_condition_of_the_toilet?}}', label: '{{How_is_the_condition_of_the_toilet?}}', referenceType: conditionOfToilet, patternHint: '{{How_is_the_condition_of_the_toilet?}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_49', '@lu_DefElemOption_LU_50', '@lu_DefElemOption_LU_51', '@lu_DefElemOption_LU_52'] }
  lu_DefElem_LU_52_1:
    __construct: { id: b4de0f5c-1ec1-58ac-bfdc-ed83737c2c16, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 4bb6b866-8785-467f-a7ad-66987a31765e, placeholder: '{{How_is_the_condition_of_the_toilet?}}', label: '{{How_is_the_condition_of_the_toilet?}}', referenceType: conditionOfToilet, patternHint: '{{How_is_the_condition_of_the_toilet?}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_55', '@lu_DefElemOption_LU_56', '@lu_DefElemOption_LU_57', '@lu_DefElemOption_LU_58'] }
  lu_DefElem_LU_53_1:
    __construct: { id: 6fb53a58-698e-5326-8655-bad406ec4445, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Cubic_meter_volume}}', label: '{{Cubic_meter_volume}}', pattern: '^(((1|2|3|4)?\d)|50)$', referenceType: cubicmeter, patternHint: '{{Cubic_meter_volume}}', sequenceNumber: 10 }
  lu_DefElem_LU_54_1:
    __construct: { id: 3bbd6b4b-0726-5d96-a5b7-229a20260bd7, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', referenceType: taskgroupPicker, patternHint: '{{How_should_the_order_be_continued?}}', sequenceNumber: 10, options: ['@lu_DefElemOption_LU_59', '@lu_DefElemOption_LU_60'] }
  lu_DefElem_LU_55_1:
    __construct: { id: f3d10af9-4a68-54de-849a-97e141c1f8a6, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_toilet_number}}', label: '{{Please_enter_toilet_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_toilet_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_55_2:
    __construct: { id: f5d12c46-1b1b-5e02-8939-a830bb3ba273, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_toilet_number}}', label: '{{Please_enter_toilet_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_toilet_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_55_3:
    __construct: { id: ca10a7c1-2d19-591c-bb4d-bcf05e3175a6, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_toilet_number}}', label: '{{Please_enter_toilet_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_toilet_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_55_4:
    __construct: { id: 532f6837-64da-5ea5-af15-d5b03b90798e, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_toilet_number}}', label: '{{Please_enter_toilet_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_toilet_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_55_5:
    __construct: { id: 2df5cd8b-aac5-5e66-b9bf-4a29588107b7, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_toilet_number}}', label: '{{Please_enter_toilet_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_toilet_number}}', sequenceNumber: 10 }
  lu_DefElem_LU_55_6:
    __construct: { id: 8a19a650-4a09-5db1-be74-33e4ca46230e, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_toilet_number}}', label: '{{Please_enter_toilet_number}}', pattern: '^\d{1,4}$', referenceType: containernumber, patternHint: '{{Please_enter_toilet_number}}', sequenceNumber: 10 }
