App\Domain\Entity\DefaultTaskRelation:
  lu_int_DefTaskRel_LU_INT_1:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_1'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7e0c1934-b3ff-4258-9317-440d88242439'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_2:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_2'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '54a9d08d-6740-439c-bad8-a7449502b521'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_3:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_3'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8fe37964-2835-4917-883c-d2dee41a3f39'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_4:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_4'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b58687e8-1003-4636-93f7-2890713da777'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_5:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_5'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c2de9262-b465-4848-b3e9-75884693fdb3'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_107:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_106'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_5'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '87ff6caf-9b12-5464-bc71-49f6b25fb8d9'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_93']
  lu_int_DefTaskRel_LU_INT_6:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_6'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1e7412a5-9f8f-473a-bc44-fbe9c2110cd1'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_1', '@lu_int_DefTaskRule_LU_INT_92']
  lu_int_DefTaskRel_LU_INT_7:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_7'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7dfaee8d-d437-4e9e-b4fc-51f1b482a262'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_8:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_8'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9ad2e08e-0c75-4f5c-9d2b-6a715a94bde8'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_2']
  lu_int_DefTaskRel_LU_INT_9:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_9'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '794c7aed-45a1-4f4f-9bba-7acaccc4a377'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_3']
  lu_int_DefTaskRel_LU_INT_10:
    sequenceNumber: '40'
    defaultTask: '@lu_int_DefTask_LU_INT_10'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4723a445-98bc-44ae-bbfc-1b1cf8ebe7cf'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_4']
  lu_int_DefTaskRel_LU_INT_11:
    sequenceNumber: '50'
    defaultTask: '@lu_int_DefTask_LU_INT_11'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'dbcaaf49-7f49-4e47-8a2f-0c7c54000c1d'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_5']
  lu_int_DefTaskRel_LU_INT_12:
    sequenceNumber: '60'
    defaultTask: '@lu_int_DefTask_LU_INT_12'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5d1882bf-fc49-46fc-880c-ca84783b709a'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_6']
  lu_int_DefTaskRel_LU_INT_13:
    sequenceNumber: '70'
    defaultTask: '@lu_int_DefTask_LU_INT_13'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '66214761-436a-4cce-ac8a-a3aa33ae8136'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_7']
  lu_int_DefTaskRel_LU_INT_14:
    sequenceNumber: '80'
    defaultTask: '@lu_int_DefTask_LU_INT_14'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9864a92d-933d-40c7-94d0-fd62ba15be24'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_8']
  lu_int_DefTaskRel_LU_INT_15:
    sequenceNumber: '90'
    defaultTask: '@lu_int_DefTask_LU_INT_15'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3e4968ff-3076-43cd-9db3-e7086733c4f0'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_9']
  lu_int_DefTaskRel_LU_INT_16:
    sequenceNumber: '100'
    defaultTask: '@lu_int_DefTask_LU_INT_16'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a24de41e-ca9b-45ce-9292-3205c1d582ca'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_10', '@lu_int_DefTaskRule_LU_INT_11', '@lu_int_DefTaskRule_LU_INT_12', '@lu_int_DefTaskRule_LU_INT_13', '@lu_int_DefTaskRule_LU_INT_14', '@lu_int_DefTaskRule_LU_INT_15', '@lu_int_DefTaskRule_LU_INT_16', '@lu_int_DefTaskRule_LU_INT_17', '@lu_int_DefTaskRule_LU_INT_18']
  lu_int_DefTaskRel_LU_INT_17:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_17'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '119767fc-13d9-4f7c-a40c-71e0bc3f90b6'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_18:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_18'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e7ad2e81-9778-4b3d-8d4a-34cc18bc3746'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_19']
  lu_int_DefTaskRel_LU_INT_19:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_19'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'eed979ed-80e9-4d29-9797-b0cff7864bad'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_20:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_20'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f6aabb3a-04ee-44f0-917d-0571500ae919'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_21:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_21'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5d7b95c8-4953-4921-ae64-469409892000'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_22:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_22'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '03b7be00-18a9-4431-98dd-8874bcb31cc4'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_23:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_23'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '566a95b9-245f-4fc3-b9cc-b840fb10bb25'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_24:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_24'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_13'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '993932d5-f7dc-431a-af86-664a9fd2a099'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_25:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_25'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '47906137-32c2-448d-94fc-4055c60ee66f'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_26:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_26'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8841b40c-7af7-4d13-bdd4-cf6ef4e5e5a5'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_27:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_27'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '44e12286-3568-4e2f-9d1f-a3af9ab88787'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_28:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_28'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '42d880e8-86b3-4a95-bd47-117111e64f74'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_29:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_29'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8ccb8e09-df94-4e94-aeee-b1ba364af793'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_30:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_30'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8f636478-c7ce-4b41-a0c6-b0ceb6189b56'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_31:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_31'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '106cdb42-65fc-438d-a0be-3fa10113cbb1'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_32:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_32'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2264d6de-6de0-4d91-8aac-e142b202e6fd'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_33:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_33'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8f53f43a-abe9-4c82-a657-f5f8889b0a03'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_34:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_34'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '886b8134-bae1-4f36-a67f-cbcaa844c9d2'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_35:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_35'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_21'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '84297b0e-67cf-4a58-a61c-29d930d56f1b'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_36:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_36'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_21'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c6c143b9-60bd-4bf7-b06b-3f6dc654702c'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_20']
  lu_int_DefTaskRel_LU_INT_37:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_37'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_21'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1e8c9702-137c-4dd9-9c36-d90d3f849e33'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_21', '@lu_int_DefTaskRule_LU_INT_22', '@lu_int_DefTaskRule_LU_INT_23', '@lu_int_DefTaskRule_LU_INT_24', '@lu_int_DefTaskRule_LU_INT_25']
  lu_int_DefTaskRel_LU_INT_38:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_38'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_22'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c2e724f4-737b-4777-9bfa-e74b30a73467'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_39:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_39'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_23'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c055ef80-397d-468a-8f56-9e6a0f46d7e0'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_40:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_40'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_24'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9ad0b0f0-7e90-4416-b966-72168d6d6ab0'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_41:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_41'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_24'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2c67ce55-f6fd-442f-b832-e74f2fc30499'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_42:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_42'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_25'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c627b78a-9efd-4feb-ad40-08158435bbf3'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_43:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_43'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_25'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ca1e5796-fdcb-4abb-a83d-9f62cf825ba8'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_44:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_44'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_31'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f68db601-ee5d-482d-8434-b97d3f90c117'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_45:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_45'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2b401666-95bc-4b7b-b211-b289199aad66'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_46:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_46'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7ea90d78-cf1f-43a3-aa39-e9312870b833'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_47:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_47'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8b0f035b-099a-4101-a680-ebc74f90c3c1'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_48:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_48'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '81e7e689-491d-45cd-bab6-9b45890f6a1c'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_49:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_49'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '62eef745-ffa2-4f42-8564-ef69516f75e3'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_50:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_50'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e0a2914d-3ae4-483e-bad0-f64dac6a627b'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_51:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_51'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'bc7a6e3c-3a67-4ef3-b9a1-fe5a2165af1e'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_52:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_52'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '843ddeb8-c939-4109-84bf-07dab5953576'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_53:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_53'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0e385eb0-a9f7-4604-91be-c74ed033c9bb'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_54:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_54'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c8c8e4d2-e30e-439a-81e3-f473b6778216'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_26']
  lu_int_DefTaskRel_LU_INT_55:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_55'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'af2c9852-672d-48bc-899c-6409736f4f73'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_27', '@lu_int_DefTaskRule_LU_INT_28', '@lu_int_DefTaskRule_LU_INT_29', '@lu_int_DefTaskRule_LU_INT_30', '@lu_int_DefTaskRule_LU_INT_31']
  lu_int_DefTaskRel_LU_INT_56:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_56'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd3f71f20-1e47-4c3c-8be2-730fec8f7203'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_57:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_57'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_41'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd999ae8e-bdd5-4b15-9a76-1ab1df26a193'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_58:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_58'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '79d46153-105d-418c-98e6-70df37b5ca7a'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_59:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_59'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0fb22a5f-c4e2-47a3-9d17-c807f7c75d28'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_32']
  lu_int_DefTaskRel_LU_INT_60:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_60'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bed52d6e-cb8f-4865-be74-db7d2408e25c'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_33', '@lu_int_DefTaskRule_LU_INT_34', '@lu_int_DefTaskRule_LU_INT_35', '@lu_int_DefTaskRule_LU_INT_36', '@lu_int_DefTaskRule_LU_INT_37']
  lu_int_DefTaskRel_LU_INT_61:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_61'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_43'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '79a85676-e9d3-44b3-947c-aa5f2caabca4'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_62:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_62'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7543a673-fddd-4bf7-8689-e896db77a7e0'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_63:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_63'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3fb9d899-7e03-4c2e-884d-4ea910d8e974'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_64:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_64'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd4d3ec92-d2a2-493c-9fc2-b684f14e4124'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_38']
  lu_int_DefTaskRel_LU_INT_65:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_65'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ee8f69b0-601c-42b8-9d2c-6d4edef8fc3c'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_39', '@lu_int_DefTaskRule_LU_INT_40', '@lu_int_DefTaskRule_LU_INT_41', '@lu_int_DefTaskRule_LU_INT_42', '@lu_int_DefTaskRule_LU_INT_43']
  lu_int_DefTaskRel_LU_INT_66:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_66'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cf741a76-5307-46c1-b476-443c1b7e04e0'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_67:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_67'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '72df1ac3-c605-4a4c-b7ac-a5f8e26ac0a5'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_68:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_68'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7446cef3-0b74-4dd2-b677-d7e7a10e9f79'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_69:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_69'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'aadd4dcb-7695-4f65-9ae3-34aad1ac490d'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_44']
  lu_int_DefTaskRel_LU_INT_70:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_70'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8604a855-ee35-4e84-a523-5798611d7ecb'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_45', '@lu_int_DefTaskRule_LU_INT_46', '@lu_int_DefTaskRule_LU_INT_47', '@lu_int_DefTaskRule_LU_INT_48', '@lu_int_DefTaskRule_LU_INT_49']
  lu_int_DefTaskRel_LU_INT_71:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_71'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e4daed0a-c40b-4312-97bf-ee7bd0362e61'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_72:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_72'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ec14365c-bfb1-4eac-93a7-dcba96b215a1'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_73:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_73'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a9731b8b-e894-4d8f-831e-36d77d5cf0c5'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_74:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_74'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '017d7792-8057-4611-98ad-ebf3f8c4ebdc'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_50']
  lu_int_DefTaskRel_LU_INT_75:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_75'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a0ac9fab-e292-46ee-8fbe-f6ee5ce701a8'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_51', '@lu_int_DefTaskRule_LU_INT_52', '@lu_int_DefTaskRule_LU_INT_53', '@lu_int_DefTaskRule_LU_INT_54', '@lu_int_DefTaskRule_LU_INT_55']
  lu_int_DefTaskRel_LU_INT_76:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_76'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_52'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c09498e2-7bb6-47e7-909e-c72aa3e82baa'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_77:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_77'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_53'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cb19e1b7-58aa-46e7-be01-b87e8463d1d4'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_78:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_78'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_54'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ee849f52-798e-4fc9-ba29-955a2d237932'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_79:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_79'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_54'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '332f710c-aa20-420a-bdf8-2a8cb6832513'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_56']
  lu_int_DefTaskRel_LU_INT_80:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_80'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_54'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0e3e25cf-f9af-417d-93ca-b6c84f02a102'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_57', '@lu_int_DefTaskRule_LU_INT_58', '@lu_int_DefTaskRule_LU_INT_59', '@lu_int_DefTaskRule_LU_INT_60', '@lu_int_DefTaskRule_LU_INT_61']
  lu_int_DefTaskRel_LU_INT_81:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_81'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_55'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '310eda4f-323f-4ba9-a565-63c3c6ce7009'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_82:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_82'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_56'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '34482c2d-e299-4f7d-bf1d-d73ea6248ac8'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_83:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_83'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_57'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '177b7da4-c86e-40e5-bc5e-2bdf5957abd2'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_84:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_84'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_57'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '19d9bed0-8f81-4993-aa4b-54954b1f51e3'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_62']
  lu_int_DefTaskRel_LU_INT_85:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_85'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_57'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '172aa919-891e-454a-a443-c0f58ae3b3aa'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_63', '@lu_int_DefTaskRule_LU_INT_64', '@lu_int_DefTaskRule_LU_INT_65', '@lu_int_DefTaskRule_LU_INT_66', '@lu_int_DefTaskRule_LU_INT_67']
  lu_int_DefTaskRel_LU_INT_86:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_86'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_58'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a7c2a71a-4c96-4855-8ab1-d26d93265d93'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_87:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_87'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_59'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '657dc0e8-d267-47ac-bec3-ad0caacea72d'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_88:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_88'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '73a79353-b66f-4998-af05-68756152aaf0'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_89:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_89'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '152c0fde-a9fa-4e3c-91e2-30f9d6d190c1'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_68']
  lu_int_DefTaskRel_LU_INT_90:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_90'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '593142bf-7298-4a01-9723-0ae27e255212'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_69', '@lu_int_DefTaskRule_LU_INT_70', '@lu_int_DefTaskRule_LU_INT_71', '@lu_int_DefTaskRule_LU_INT_72', '@lu_int_DefTaskRule_LU_INT_73']
  lu_int_DefTaskRel_LU_INT_91:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_91'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2548382a-d8fb-4d3f-aa63-e4f55d29387d'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_92:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_92'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0c4fcfe3-6118-4fde-ad93-9c0d9752aa6b'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_93:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_93'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b69368f7-77b0-44b2-8879-d068f05e94b9'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_94:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_94'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b91fcc0f-a93f-4866-8e95-00ce1562b9ef'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_74']
  lu_int_DefTaskRel_LU_INT_95:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_95'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fe6f5181-f6dc-448c-9b49-3db3858be168'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_75', '@lu_int_DefTaskRule_LU_INT_76', '@lu_int_DefTaskRule_LU_INT_77', '@lu_int_DefTaskRule_LU_INT_78', '@lu_int_DefTaskRule_LU_INT_79']
  lu_int_DefTaskRel_LU_INT_96:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_96'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_64'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '56e012dd-cf9f-4469-b54f-17cee1e3d979'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_97:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_97'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a3a74a2e-c809-4a81-8da4-e9000bd60ca5'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_98:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_98'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1ea10aba-cc73-481a-b601-6e5b1f958d12'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_99:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_99'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2830f486-b901-4b59-8304-1ead166a1215'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_80']
  lu_int_DefTaskRel_LU_INT_100:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_100'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fda5db6b-3972-4118-af87-8880b2a2fff9'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_81', '@lu_int_DefTaskRule_LU_INT_82', '@lu_int_DefTaskRule_LU_INT_83', '@lu_int_DefTaskRule_LU_INT_84', '@lu_int_DefTaskRule_LU_INT_85']
  lu_int_DefTaskRel_LU_INT_101:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_101'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_67'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '09af725f-f5ed-4c7a-a5bf-f8eaa85a2de9'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_102:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_102'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4c3e6774-6c43-42cb-860b-509937477692'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_103:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_103'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1bf7539a-d857-44d4-8d1c-5c1968241a2c'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTaskRel_LU_INT_104:
    sequenceNumber: '20'
    defaultTask: '@lu_int_DefTask_LU_INT_104'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd8349dfa-feca-421e-8717-aa85490e0cda'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_86']
  lu_int_DefTaskRel_LU_INT_105:
    sequenceNumber: '30'
    defaultTask: '@lu_int_DefTask_LU_INT_105'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd0673828-d60c-4091-bd50-d8d9e5226beb'
    tenant: '<getGermanyTenant()>'
    rules: ['@lu_int_DefTaskRule_LU_INT_87', '@lu_int_DefTaskRule_LU_INT_88', '@lu_int_DefTaskRule_LU_INT_89', '@lu_int_DefTaskRule_LU_INT_90', '@lu_int_DefTaskRule_LU_INT_91']
  lu_int_DefTaskRel_LU_INT_108:
    sequenceNumber: '10'
    defaultTask: '@lu_int_DefTask_LU_INT_107'
    defaultTaskGroup: '@lu_int_DefTG_LU_INT_70'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f388d159-0bed-4fae-ae82-b7433f51c8e7'
    tenant: '<getGermanyTenant()>'
