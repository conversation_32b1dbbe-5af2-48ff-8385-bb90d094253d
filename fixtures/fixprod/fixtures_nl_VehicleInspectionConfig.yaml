App\Domain\VehicleInspection\Config\VehicleInspectionConfig:
  nl_VIR_NL_1:
    __construct: { country: '<(App\Domain\Entity\Enum\Country::NL)>', automaticCompletionEnabled: true, equipmentComponentGroups: ['@nl_VIR_Group_NL_1', '@nl_VIR_Group_NL_2', '@nl_VIR_Group_NL_3'] }
    tenant: '<getNetherlandsTenant()>'
  nl_VIR_NL_2:
    __construct: { country: '<(App\Domain\Entity\Enum\Country::NL)>', equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ7800)>', automaticCompletionEnabled: true, equipmentComponentGroups: ['@nl_VIR_Group_NL_4'] }
    tenant: '<getNetherlandsTenant()>'
