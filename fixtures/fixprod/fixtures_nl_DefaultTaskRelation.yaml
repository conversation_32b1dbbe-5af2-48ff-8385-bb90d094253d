App\Domain\Entity\DefaultTaskRelation:
  nl_DefTaskRel_NL_1:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_1'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'edfdd8a5-dfcc-4751-966d-679fcdc9bb7d'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_2:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_2'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'dd15aaee-0062-4a5a-bc64-bebdf47be71c'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_1']
  nl_DefTaskRel_NL_3:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_3'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6fb8f2d5-dff5-4b45-9161-304a24e23326'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_2']
  nl_DefTaskRel_NL_4:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_4'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7bf20a2a-aad6-4db1-97a3-2411d4ae48b4'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_3']
  nl_DefTaskRel_NL_5:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_5'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1408d8f9-f459-40bc-8703-3a66b37dbdd3'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_4']
  nl_DefTaskRel_NL_6:
    sequenceNumber: '60'
    defaultTask: '@nl_DefTask_NL_6'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd90abc2a-a810-413b-8aa2-89ac89220c9b'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_5']
  nl_DefTaskRel_NL_7:
    sequenceNumber: '70'
    defaultTask: '@nl_DefTask_NL_7'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd46f0dbe-4774-476f-a7b3-3f9f768bdbf8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_6']
  nl_DefTaskRel_NL_8:
    sequenceNumber: '80'
    defaultTask: '@nl_DefTask_NL_8'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '38a95759-fc00-4bba-87d0-55f9b0217ad9'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_7']
  nl_DefTaskRel_NL_9:
    sequenceNumber: '90'
    defaultTask: '@nl_DefTask_NL_9'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '91166777-6e27-4cb5-ade8-921b6332143f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_8']
  nl_DefTaskRel_NL_10:
    sequenceNumber: '100'
    defaultTask: '@nl_DefTask_NL_10'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ca865c1e-abad-4a25-a686-b06ac0ee54b8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_9']
  nl_DefTaskRel_NL_11:
    sequenceNumber: '110'
    defaultTask: '@nl_DefTask_NL_11'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9a137ecb-0f84-442d-8193-b809a30f7469'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_10']
  nl_DefTaskRel_NL_12:
    sequenceNumber: '120'
    defaultTask: '@nl_DefTask_NL_12'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '584434e2-836b-405f-af75-d41c05b92ff4'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_11']
  nl_DefTaskRel_NL_13:
    sequenceNumber: '130'
    defaultTask: '@nl_DefTask_NL_13'
    defaultTaskGroup: '@nl_DefTG_NL_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '043a660d-91d7-47ee-b2cf-e40495bfdd6f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_12', '@nl_DefTaskRule_NL_13', '@nl_DefTaskRule_NL_14', '@nl_DefTaskRule_NL_15', '@nl_DefTaskRule_NL_16', '@nl_DefTaskRule_NL_17', '@nl_DefTaskRule_NL_18', '@nl_DefTaskRule_NL_19', '@nl_DefTaskRule_NL_20', '@nl_DefTaskRule_NL_21', '@nl_DefTaskRule_NL_22', '@nl_DefTaskRule_NL_23', '@nl_DefTaskRule_NL_24', '@nl_DefTaskRule_NL_25', '@nl_DefTaskRule_NL_26', '@nl_DefTaskRule_NL_27', '@nl_DefTaskRule_NL_28', '@nl_DefTaskRule_NL_29', '@nl_DefTaskRule_NL_30', '@nl_DefTaskRule_NL_31', '@nl_DefTaskRule_NL_32', '@nl_DefTaskRule_NL_33']
  nl_DefTaskRel_NL_14:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_14'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '43f15f82-37e0-4de5-9fcd-261d97ee54ad'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_15:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_15'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b7b3f07d-3433-434d-89f3-9aabab4aafbd'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_34']
  nl_DefTaskRel_NL_16:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_16'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b705c844-7c8b-42bf-ac7f-fa5be737e59e'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_35']
  nl_DefTaskRel_NL_17:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_17'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd9896529-f7d3-46cc-8350-d2bdfff30fb3'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_36']
  nl_DefTaskRel_NL_18:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_18'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2c1ed857-784b-4a14-8f0a-f6217e419caa'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_37']
  nl_DefTaskRel_NL_19:
    sequenceNumber: '60'
    defaultTask: '@nl_DefTask_NL_19'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8440d736-cac6-4dcb-a14c-225178871a76'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_38']
  nl_DefTaskRel_NL_20:
    sequenceNumber: '70'
    defaultTask: '@nl_DefTask_NL_20'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9ca9d74e-5540-45fd-a49b-e07a606e31b0'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_39']
  nl_DefTaskRel_NL_21:
    sequenceNumber: '80'
    defaultTask: '@nl_DefTask_NL_21'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '57f1abea-7cb1-41ac-b98a-0d976dd68f50'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_40']
  nl_DefTaskRel_NL_22:
    sequenceNumber: '90'
    defaultTask: '@nl_DefTask_NL_22'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f6f7a068-b8d1-48ae-aec8-7bbd018e1c45'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_41']
  nl_DefTaskRel_NL_23:
    sequenceNumber: '100'
    defaultTask: '@nl_DefTask_NL_23'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e30f7ef9-2168-4eab-b08a-bbae0d6976a5'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_42']
  nl_DefTaskRel_NL_24:
    sequenceNumber: '110'
    defaultTask: '@nl_DefTask_NL_24'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fccd79f3-112c-4204-9130-0a05065a87db'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_43']
  nl_DefTaskRel_NL_25:
    sequenceNumber: '120'
    defaultTask: '@nl_DefTask_NL_25'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6b9cab35-0441-411f-8a1b-f78e3d5e5123'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_44']
  nl_DefTaskRel_NL_26:
    sequenceNumber: '130'
    defaultTask: '@nl_DefTask_NL_26'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2c98c4cd-eacf-4130-a4c8-480db8fc1a0f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_45']
  nl_DefTaskRel_NL_27:
    sequenceNumber: '140'
    defaultTask: '@nl_DefTask_NL_27'
    defaultTaskGroup: '@nl_DefTG_NL_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e483071a-0de9-43a0-8c93-e34636744e69'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_46', '@nl_DefTaskRule_NL_47', '@nl_DefTaskRule_NL_48', '@nl_DefTaskRule_NL_49', '@nl_DefTaskRule_NL_50', '@nl_DefTaskRule_NL_51', '@nl_DefTaskRule_NL_52', '@nl_DefTaskRule_NL_53', '@nl_DefTaskRule_NL_54', '@nl_DefTaskRule_NL_55', '@nl_DefTaskRule_NL_56', '@nl_DefTaskRule_NL_57', '@nl_DefTaskRule_NL_58', '@nl_DefTaskRule_NL_59', '@nl_DefTaskRule_NL_60', '@nl_DefTaskRule_NL_61', '@nl_DefTaskRule_NL_62', '@nl_DefTaskRule_NL_63', '@nl_DefTaskRule_NL_64', '@nl_DefTaskRule_NL_65', '@nl_DefTaskRule_NL_66', '@nl_DefTaskRule_NL_67', '@nl_DefTaskRule_NL_68', '@nl_DefTaskRule_NL_69']
  nl_DefTaskRel_NL_28:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_28'
    defaultTaskGroup: '@nl_DefTG_NL_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a65ea823-1281-4ab8-bcee-89925f359893'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_29:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_29'
    defaultTaskGroup: '@nl_DefTG_NL_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '184ae6ab-0ac5-4a22-99d8-5bdcfb3cd05d'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_70']
  nl_DefTaskRel_NL_30:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_30'
    defaultTaskGroup: '@nl_DefTG_NL_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '6f4ec84e-ab8e-43f9-8786-008b584c7c2d'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_76']
  nl_DefTaskRel_NL_31:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_31'
    defaultTaskGroup: '@nl_DefTG_NL_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8fb81f64-0786-4490-a41a-3515d61b84a2'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_71', '@nl_DefTaskRule_NL_72', '@nl_DefTaskRule_NL_73', '@nl_DefTaskRule_NL_74', '@nl_DefTaskRule_NL_75']
  nl_DefTaskRel_NL_32:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_32'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '245998c4-2fec-42de-b381-bae6db81601f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_33:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_15'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cae129fb-9a30-484f-a6a1-0db701668fe7'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_77']
  nl_DefTaskRel_NL_34:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_16'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a37f23a3-3201-4769-910d-5c23d4a85793'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_78']
  nl_DefTaskRel_NL_35:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_17'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0ac28ffe-f587-417e-9c54-75dce23526bd'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_79']
  nl_DefTaskRel_NL_36:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_18'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3d9a2362-7464-45a3-ad3f-0dab5adf3af8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_80']
  nl_DefTaskRel_NL_37:
    sequenceNumber: '60'
    defaultTask: '@nl_DefTask_NL_19'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e876c9e3-26d9-45df-b23f-c9d7dcf22ef3'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_81']
  nl_DefTaskRel_NL_38:
    sequenceNumber: '70'
    defaultTask: '@nl_DefTask_NL_20'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '78e87def-da73-4a58-aa01-95592ad2ed04'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_82']
  nl_DefTaskRel_NL_39:
    sequenceNumber: '80'
    defaultTask: '@nl_DefTask_NL_21'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd4bf9774-cafb-4732-aa38-b3a627914521'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_83']
  nl_DefTaskRel_NL_40:
    sequenceNumber: '90'
    defaultTask: '@nl_DefTask_NL_22'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '10b953cc-2c49-4ef6-b987-7fb0cfd451a0'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_84']
  nl_DefTaskRel_NL_41:
    sequenceNumber: '100'
    defaultTask: '@nl_DefTask_NL_23'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5408531d-6db4-4d89-a9d5-e6b9e2cc1f14'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_85']
  nl_DefTaskRel_NL_42:
    sequenceNumber: '110'
    defaultTask: '@nl_DefTask_NL_24'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '151199ca-cc9c-4790-ae93-4191eb3b28ba'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_86']
  nl_DefTaskRel_NL_43:
    sequenceNumber: '120'
    defaultTask: '@nl_DefTask_NL_25'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5ce0f198-a37e-4d30-936b-952a015b53a9'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_87']
  nl_DefTaskRel_NL_44:
    sequenceNumber: '130'
    defaultTask: '@nl_DefTask_NL_26'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '79b69fdf-38cd-40b5-b024-2f207d9eded5'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_88']
  nl_DefTaskRel_NL_45:
    sequenceNumber: '140'
    defaultTask: '@nl_DefTask_NL_33'
    defaultTaskGroup: '@nl_DefTG_NL_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '94b912cb-62dc-4508-9fcc-d479b06501fb'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_89', '@nl_DefTaskRule_NL_90', '@nl_DefTaskRule_NL_91', '@nl_DefTaskRule_NL_92', '@nl_DefTaskRule_NL_93', '@nl_DefTaskRule_NL_94', '@nl_DefTaskRule_NL_95', '@nl_DefTaskRule_NL_96', '@nl_DefTaskRule_NL_97', '@nl_DefTaskRule_NL_98', '@nl_DefTaskRule_NL_99', '@nl_DefTaskRule_NL_100', '@nl_DefTaskRule_NL_101', '@nl_DefTaskRule_NL_102', '@nl_DefTaskRule_NL_103', '@nl_DefTaskRule_NL_104', '@nl_DefTaskRule_NL_105', '@nl_DefTaskRule_NL_106', '@nl_DefTaskRule_NL_107', '@nl_DefTaskRule_NL_108', '@nl_DefTaskRule_NL_109', '@nl_DefTaskRule_NL_110', '@nl_DefTaskRule_NL_111', '@nl_DefTaskRule_NL_112']
  nl_DefTaskRel_NL_46:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_34'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '407b3bba-bc28-4b07-b274-003664a266b4'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_47:
    sequenceNumber: '140'
    defaultTask: '@nl_DefTask_NL_35'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fe349703-5ecb-4caf-926b-ce305f5924a3'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_113']
  nl_DefTaskRel_NL_48:
    sequenceNumber: '130'
    defaultTask: '@nl_DefTask_NL_36'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f79c4b5d-944d-4138-afce-bb381ef24043'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_114']
  nl_DefTaskRel_NL_49:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_37'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c1c3876a-e235-4c28-b0f4-49345b46a14e'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_115']
  nl_DefTaskRel_NL_50:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_38'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ed7ceaa8-9fcf-47cf-b129-aed82b8695b4'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_116']
  nl_DefTaskRel_NL_51:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_39'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'df3f5c2e-001e-4827-a1b5-af8cdd7b3d5b'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_117']
  nl_DefTaskRel_NL_52:
    sequenceNumber: '60'
    defaultTask: '@nl_DefTask_NL_40'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd7facc49-f2bc-4f26-b779-224eac62ff0f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_118']
  nl_DefTaskRel_NL_53:
    sequenceNumber: '70'
    defaultTask: '@nl_DefTask_NL_41'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1da6bb89-1752-4e99-89e7-66f9872083a3'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_119']
  nl_DefTaskRel_NL_54:
    sequenceNumber: '80'
    defaultTask: '@nl_DefTask_NL_42'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '618b6565-0a2e-404c-9431-9b1402798300'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_120']
  nl_DefTaskRel_NL_55:
    sequenceNumber: '90'
    defaultTask: '@nl_DefTask_NL_43'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bf00bea9-46bc-49e6-9b3d-fdeaebeabe5b'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_121']
  nl_DefTaskRel_NL_56:
    sequenceNumber: '100'
    defaultTask: '@nl_DefTask_NL_44'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e03dc921-54ec-4c10-b233-6a8c1ddd0ab1'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_122']
  nl_DefTaskRel_NL_57:
    sequenceNumber: '110'
    defaultTask: '@nl_DefTask_NL_45'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6cbfa54f-46b7-493c-ac38-4702ebfff975'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_123']
  nl_DefTaskRel_NL_58:
    sequenceNumber: '120'
    defaultTask: '@nl_DefTask_NL_46'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '286314e5-d620-48c9-b5b6-a959c0fdd1a8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_124']
  nl_DefTaskRel_NL_59:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_47'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b80eb206-eb0e-4b82-ae40-94e68a2b3741'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_125']
  nl_DefTaskRel_NL_166:
    sequenceNumber: '145'
    defaultTask: '@nl_DefTask_NL_156'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f807a6bb-9760-4187-8a57-457844524426'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_212']
  nl_DefTaskRel_NL_60:
    sequenceNumber: '150'
    defaultTask: '@nl_DefTask_NL_48'
    defaultTaskGroup: '@nl_DefTG_NL_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6ff684bb-ae14-47a0-ad37-fe959d54f360'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_126', '@nl_DefTaskRule_NL_127', '@nl_DefTaskRule_NL_128', '@nl_DefTaskRule_NL_129', '@nl_DefTaskRule_NL_130', '@nl_DefTaskRule_NL_131', '@nl_DefTaskRule_NL_132', '@nl_DefTaskRule_NL_133', '@nl_DefTaskRule_NL_134', '@nl_DefTaskRule_NL_135', '@nl_DefTaskRule_NL_136', '@nl_DefTaskRule_NL_137', '@nl_DefTaskRule_NL_138', '@nl_DefTaskRule_NL_139', '@nl_DefTaskRule_NL_140', '@nl_DefTaskRule_NL_141', '@nl_DefTaskRule_NL_142', '@nl_DefTaskRule_NL_143', '@nl_DefTaskRule_NL_144', '@nl_DefTaskRule_NL_145', '@nl_DefTaskRule_NL_146', '@nl_DefTaskRule_NL_147', '@nl_DefTaskRule_NL_148', '@nl_DefTaskRule_NL_149', '@nl_DefTaskRule_NL_150', '@nl_DefTaskRule_NL_213']
  nl_DefTaskRel_NL_61:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_49'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4cc4db32-419a-4671-a0fb-fbc2c398292a'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_62:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_50'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a4e2ba64-6282-4b01-92af-c19b387f3b7d'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_151']
  nl_DefTaskRel_NL_63:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_51'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f654abea-8861-48eb-a9e3-6d61a3ec2db5'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_152']
  nl_DefTaskRel_NL_64:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_52'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '684f10a9-1ca6-48aa-a29a-5f9df642abad'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_153']
  nl_DefTaskRel_NL_65:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_53'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '34859910-3363-4eba-bff0-0936939485ef'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_154']
  nl_DefTaskRel_NL_66:
    sequenceNumber: '60'
    defaultTask: '@nl_DefTask_NL_54'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '54c5973c-0ad4-4b35-b0f1-71d61ffaf13f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_155']
  nl_DefTaskRel_NL_67:
    sequenceNumber: '70'
    defaultTask: '@nl_DefTask_NL_55'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3dd5e778-ec36-45e8-88af-c3e42c4389fe'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_156']
  nl_DefTaskRel_NL_68:
    sequenceNumber: '80'
    defaultTask: '@nl_DefTask_NL_56'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9b7f3266-614a-4b9a-95cb-1dbc7fc7016c'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_157']
  nl_DefTaskRel_NL_69:
    sequenceNumber: '90'
    defaultTask: '@nl_DefTask_NL_57'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f25a38e6-ae30-4a23-8e4b-10a2119ed011'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_158']
  nl_DefTaskRel_NL_70:
    sequenceNumber: '100'
    defaultTask: '@nl_DefTask_NL_58'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2740ee06-dcfe-4cb1-b2ab-7d5b49c52ee2'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_159']
  nl_DefTaskRel_NL_71:
    sequenceNumber: '110'
    defaultTask: '@nl_DefTask_NL_59'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c393316d-d7bb-421d-af89-3259db48d357'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_160']
  nl_DefTaskRel_NL_72:
    sequenceNumber: '120'
    defaultTask: '@nl_DefTask_NL_60'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c9a2382b-3fba-4ee2-b1d5-754d05233715'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_161']
  nl_DefTaskRel_NL_73:
    sequenceNumber: '130'
    defaultTask: '@nl_DefTask_NL_61'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3d77ff88-cf01-491f-a863-efabd550643f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_162']
  nl_DefTaskRel_NL_74:
    sequenceNumber: '140'
    defaultTask: '@nl_DefTask_NL_62'
    defaultTaskGroup: '@nl_DefTG_NL_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e23b8786-9ccb-448d-8aa2-f4eb123fa171'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_163', '@nl_DefTaskRule_NL_164', '@nl_DefTaskRule_NL_165', '@nl_DefTaskRule_NL_166', '@nl_DefTaskRule_NL_167', '@nl_DefTaskRule_NL_168', '@nl_DefTaskRule_NL_169', '@nl_DefTaskRule_NL_170', '@nl_DefTaskRule_NL_171', '@nl_DefTaskRule_NL_172', '@nl_DefTaskRule_NL_173', '@nl_DefTaskRule_NL_174', '@nl_DefTaskRule_NL_175', '@nl_DefTaskRule_NL_176', '@nl_DefTaskRule_NL_177', '@nl_DefTaskRule_NL_178', '@nl_DefTaskRule_NL_179', '@nl_DefTaskRule_NL_180', '@nl_DefTaskRule_NL_181', '@nl_DefTaskRule_NL_182', '@nl_DefTaskRule_NL_183', '@nl_DefTaskRule_NL_184', '@nl_DefTaskRule_NL_185', '@nl_DefTaskRule_NL_186']
  nl_DefTaskRel_NL_75:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_63'
    defaultTaskGroup: '@nl_DefTG_NL_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '247f67a9-06fa-4d22-970c-dcb794391650'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_76:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_64'
    defaultTaskGroup: '@nl_DefTG_NL_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'da4172e8-2f99-4b07-a18d-325fe3abcc21'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_187']
  nl_DefTaskRel_NL_77:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_65'
    defaultTaskGroup: '@nl_DefTG_NL_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'd6c91ceb-6eee-420a-be9c-e3d59f594144'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_193']
  nl_DefTaskRel_NL_78:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_66'
    defaultTaskGroup: '@nl_DefTG_NL_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3b4d3129-b5e7-4d3e-9c67-43e7ccdeeeee'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_189', '@nl_DefTaskRule_NL_190', '@nl_DefTaskRule_NL_191', '@nl_DefTaskRule_NL_192']
  nl_DefTaskRel_NL_79:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_67'
    defaultTaskGroup: '@nl_DefTG_NL_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2e48343b-e337-4248-8467-f60aaabac806'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_80:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_68'
    defaultTaskGroup: '@nl_DefTG_NL_9'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '21f12e3e-d468-493c-ac77-34fc8bea59c7'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_152:
    sequenceNumber: '15'
    defaultTask: '@nl_DefTask_NL_142'
    defaultTaskGroup: '@nl_DefTG_NL_9'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a4b851f2-0728-4207-9520-40dd9a53b495'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_81:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_69'
    defaultTaskGroup: '@nl_DefTG_NL_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '37beab4c-3d3a-4eb3-b29a-8bc43cd35e3d'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_194', '@nl_DefTaskRule_NL_201']
  nl_DefTaskRel_NL_82:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_70'
    defaultTaskGroup: '@nl_DefTG_NL_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '693f5345-40af-4900-8213-53a48295553f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_162:
    sequenceNumber: '15'
    defaultTask: '@nl_DefTask_NL_153'
    defaultTaskGroup: '@nl_DefTG_NL_10'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '16cae482-b387-4759-8466-69a8479334e9'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_83:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_71'
    defaultTaskGroup: '@nl_DefTG_NL_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c54210f1-e6c7-4df6-8c78-9fbf71bc7f11'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_195', '@nl_DefTaskRule_NL_211']
  nl_DefTaskRel_NL_84:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_72'
    defaultTaskGroup: '@nl_DefTG_NL_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '80865ad6-3052-4122-9252-695c954658b4'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_85:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_73'
    defaultTaskGroup: '@nl_DefTG_NL_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '012b5ec0-89e6-4394-8632-91c606a4d5c7'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_196']
  nl_DefTaskRel_NL_154:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_144'
    defaultTaskGroup: '@nl_DefTG_NL_12'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c0f2ce63-b91f-4b61-9ab2-420497a5bf49'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_155:
    sequenceNumber: '15'
    defaultTask: '@nl_DefTask_NL_145'
    defaultTaskGroup: '@nl_DefTG_NL_12'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '45ff7618-409f-4482-950c-de82520b12b8'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_86:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_74'
    defaultTaskGroup: '@nl_DefTG_NL_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b49b83fc-ef08-4597-a18a-a2eefc2a85b8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_203', '@nl_DefTaskRule_NL_204']
  nl_DefTaskRel_NL_165:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_147'
    defaultTaskGroup: '@nl_DefTG_NL_13'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '67beecb7-dd73-41a5-bf62-9f34a2b7091b'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_157:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_148'
    defaultTaskGroup: '@nl_DefTG_NL_13'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2a402d70-c193-472c-903e-36b5c3bb39b3'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_87:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_75'
    defaultTaskGroup: '@nl_DefTG_NL_13'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2f4134b8-0e5f-4c07-8a45-bc1776ac39db'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_206', '@nl_DefTaskRule_NL_207']
  nl_DefTaskRel_NL_153:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_143'
    defaultTaskGroup: '@nl_DefTG_NL_14'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4f14db21-3340-411d-adf9-030e2463ed07'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_88:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_76'
    defaultTaskGroup: '@nl_DefTG_NL_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'deb12557-b7ce-4163-add6-9e96ac9eb4dc'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_202']
  nl_DefTaskRel_NL_89:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_77'
    defaultTaskGroup: '@nl_DefTG_NL_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd10270d5-d12a-4a77-a3e0-b64752d946d1'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_90:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_78'
    defaultTaskGroup: '@nl_DefTG_NL_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '75d1a485-1048-4880-9b2c-5e05c5ec3ce5'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_91:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_79'
    defaultTaskGroup: '@nl_DefTG_NL_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cf1ddf34-bbfd-40ce-b7a9-2b2a9b52c3c8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_197', '@nl_DefTaskRule_NL_198', '@nl_DefTaskRule_NL_199']
  nl_DefTaskRel_NL_156:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_146'
    defaultTaskGroup: '@nl_DefTG_NL_17'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2b89585c-da03-4755-8cbe-7a6d08ee0dfb'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_92:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_80'
    defaultTaskGroup: '@nl_DefTG_NL_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0e2f629d-a9d5-45ff-a74a-46ea9d592585'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_205']
  nl_DefTaskRel_NL_158:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_149'
    defaultTaskGroup: '@nl_DefTG_NL_18'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '26fda9ef-2d88-43dd-8694-8f766dc86ab5'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_159:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_150'
    defaultTaskGroup: '@nl_DefTG_NL_18'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '90870b61-6e84-440c-b2e1-d476a10ed3f9'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_93:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_81'
    defaultTaskGroup: '@nl_DefTG_NL_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '336c0331-dac8-4ca3-95dd-c2927cdc52c7'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_208', '@nl_DefTaskRule_NL_209']
  nl_DefTaskRel_NL_94:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_82'
    defaultTaskGroup: '@nl_DefTG_NL_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '26846865-32dc-4919-8bf4-13f20badc1b1'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_95:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_83'
    defaultTaskGroup: '@nl_DefTG_NL_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f94a178c-98e0-4dd7-afaa-25dab5bb5c7e'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_96:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_84'
    defaultTaskGroup: '@nl_DefTG_NL_20'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0d3077e8-14a9-45c0-ab88-e94c90e6d776'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_97:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_85'
    defaultTaskGroup: '@nl_DefTG_NL_21'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd86f5585-e84b-4dd5-a647-5e931b840c8e'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_148:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_138'
    defaultTaskGroup: '@nl_DefTG_NL_21'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f173b469-72eb-4b5b-86b2-454776ea726b'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_98:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_86'
    defaultTaskGroup: '@nl_DefTG_NL_22'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1581aa76-df0d-4cc9-babf-8446a0da63cf'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_99:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_87'
    defaultTaskGroup: '@nl_DefTG_NL_22'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8a69ac0b-c204-4638-9f0d-c82c6ad8ed78'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_100:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_88'
    defaultTaskGroup: '@nl_DefTG_NL_23'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9e44fd4e-0942-48fc-b610-0b8984d89850'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_101:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_89'
    defaultTaskGroup: '@nl_DefTG_NL_23'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '05d0da33-9ccb-49eb-982d-2aaed8e2b9b1'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_102:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_90'
    defaultTaskGroup: '@nl_DefTG_NL_23'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0694e64a-6696-4ff5-94aa-dea249e28006'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_103:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_91'
    defaultTaskGroup: '@nl_DefTG_NL_24'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '91ca04f7-be14-40e8-92ce-f13dca16d03b'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_104:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_92'
    defaultTaskGroup: '@nl_DefTG_NL_24'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '18ece799-ce8c-44be-a5cc-1655ddb465b7'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_200']
  nl_DefTaskRel_NL_105:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_93'
    defaultTaskGroup: '@nl_DefTG_NL_25'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '15921b93-8b49-4b0f-a3da-852998ab5d23'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_149:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_141'
    defaultTaskGroup: '@nl_DefTG_NL_25'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0f267bb1-028b-47f8-b61c-af2da191e3a9'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_106:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_94'
    defaultTaskGroup: '@nl_DefTG_NL_26'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9ea3472c-7d12-48f1-83be-3a461424dc24'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_107:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_95'
    defaultTaskGroup: '@nl_DefTG_NL_26'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e45870e7-61e6-426a-8011-779735d76da9'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_108:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_96'
    defaultTaskGroup: '@nl_DefTG_NL_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3afaae0d-453f-415a-ab82-f745688b5e61'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_109:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_97'
    defaultTaskGroup: '@nl_DefTG_NL_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '6e1c2af6-a890-4f51-ae4d-b52554839e7f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_110:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_98'
    defaultTaskGroup: '@nl_DefTG_NL_34'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '86c38e1a-b3a1-460e-a3b8-72effe083cce'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_111:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_99'
    defaultTaskGroup: '@nl_DefTG_NL_35'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '17f78433-be6f-45ed-b608-9949ef2eef8d'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_112:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_100'
    defaultTaskGroup: '@nl_DefTG_NL_35'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ac9303e7-c69b-4b45-bc3a-f8aacb954229'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_113:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_101'
    defaultTaskGroup: '@nl_DefTG_NL_36'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8ab39d37-8dd4-4739-a4fb-6eaa9c6b63c1'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_114:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_102'
    defaultTaskGroup: '@nl_DefTG_NL_36'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '672d5cb9-1e91-40b0-bfba-4ac307ae23a0'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_115:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_103'
    defaultTaskGroup: '@nl_DefTG_NL_37'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '221cccc8-ae86-4105-bd66-1a591fba01bb'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_116:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_104'
    defaultTaskGroup: '@nl_DefTG_NL_37'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '543516db-8453-4890-b5b6-6021d859f301'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_118:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_106'
    defaultTaskGroup: '@nl_DefTG_NL_38'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '94fdb696-d695-48bd-8389-e8e53a3f0639'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_119:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_107'
    defaultTaskGroup: '@nl_DefTG_NL_39'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '270b94a1-4e2c-4981-9e3a-0b1c824012d3'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_120:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_108'
    defaultTaskGroup: '@nl_DefTG_NL_39'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '46d6b18e-1c92-400b-af09-f5b5b1a718bf'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_121:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_109'
    defaultTaskGroup: '@nl_DefTG_NL_40'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4836156c-ac83-4940-ac69-92e675cda086'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_122:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_110'
    defaultTaskGroup: '@nl_DefTG_NL_40'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '42d5a934-0ce3-4ee2-b77f-dcebac08db8e'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_123:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_111'
    defaultTaskGroup: '@nl_DefTG_NL_41'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '626c6102-6407-43db-b47c-5b62fd355b89'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_124:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_112'
    defaultTaskGroup: '@nl_DefTG_NL_41'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'dd1e498e-ae6e-474e-8f72-7419d4373935'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_125:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_113'
    defaultTaskGroup: '@nl_DefTG_NL_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1c79c2cb-1540-4ebb-b2db-fb85b9bf9364'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_126:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_114'
    defaultTaskGroup: '@nl_DefTG_NL_42'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd689bda1-514d-454f-a0be-fc9ec16f3538'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_127:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_115'
    defaultTaskGroup: '@nl_DefTG_NL_43'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '636dc4a1-d7af-4479-8f3e-99f0343c200e'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_128:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_116'
    defaultTaskGroup: '@nl_DefTG_NL_43'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b669ad1d-231b-47da-a1f4-5bcb7b223fdd'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_129:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_117'
    defaultTaskGroup: '@nl_DefTG_NL_44'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '19ded8bc-87df-46a5-a084-0293783def66'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_130:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_118'
    defaultTaskGroup: '@nl_DefTG_NL_44'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1bb50245-e8f7-4d37-9eab-ea9e3b63cc57'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_131:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_119'
    defaultTaskGroup: '@nl_DefTG_NL_45'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '05fe8f77-aa14-47dd-8109-dcfe2315b761'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_132:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_120'
    defaultTaskGroup: '@nl_DefTG_NL_45'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ddb1dc85-52b5-43dd-9d7a-afbff5515091'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_133:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_121'
    defaultTaskGroup: '@nl_DefTG_NL_46'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '809859bf-6501-4da8-911b-a306b67d935c'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_134:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_122'
    defaultTaskGroup: '@nl_DefTG_NL_46'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e9266556-0e36-4c59-8ec2-f684e509cfb1'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_135:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_123'
    defaultTaskGroup: '@nl_DefTG_NL_47'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'daf175f2-4a10-48eb-8db8-fadb12d42fbb'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_136:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_124'
    defaultTaskGroup: '@nl_DefTG_NL_47'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ce1d6b49-32c2-4674-821e-cd5f1f5e0ad8'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_137:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_125'
    defaultTaskGroup: '@nl_DefTG_NL_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7b2bcfdc-12dc-468c-a52a-52c83eaa87c7'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_138:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_126'
    defaultTaskGroup: '@nl_DefTG_NL_48'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cea45fa2-d468-4a5e-8708-ea521c2ebedb'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_139:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_127'
    defaultTaskGroup: '@nl_DefTG_NL_49'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1ad5b337-292d-4668-bc9f-d71b0b8532d6'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_140:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_128'
    defaultTaskGroup: '@nl_DefTG_NL_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '16abbf3c-fe00-428b-bc74-9904bd0b2cc3'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_141:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_129'
    defaultTaskGroup: '@nl_DefTG_NL_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0106d00c-6828-4207-ac3c-115f977e2133'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_142:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_130'
    defaultTaskGroup: '@nl_DefTG_NL_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3052e34f-ed91-475f-96bc-0965e7593139'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_143:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_131'
    defaultTaskGroup: '@nl_DefTG_NL_52'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '383fb072-cabb-441b-973e-4c2ae39dd273'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_144:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_132'
    defaultTaskGroup: '@nl_DefTG_NL_53'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cd5c707a-0f77-4347-b8be-0d4ec6872169'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_145:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_133'
    defaultTaskGroup: '@nl_DefTG_NL_54'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3fc59266-b3fa-48ae-a947-833f1a6c6468'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_146:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_134'
    defaultTaskGroup: '@nl_DefTG_NL_55'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c99d39b7-6c4e-4be3-a914-ce9347822969'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_150:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_139'
    defaultTaskGroup: '@nl_DefTG_NL_57'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '70ed22a7-08db-4831-be00-cf2e0e0ccc1b'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_151:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_140'
    defaultTaskGroup: '@nl_DefTG_NL_57'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e7dcff01-e1b4-43b5-96b8-7116ebc771d9'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_160:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_151'
    defaultTaskGroup: '@nl_DefTG_NL_58'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1111bbfe-5580-458d-b9f5-f51354901a15'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_161:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_152'
    defaultTaskGroup: '@nl_DefTG_NL_58'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bd62d1a3-6a95-4b6e-9b42-003f2b6b1a6f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_210']
  nl_DefTaskRel_NL_163:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_154'
    defaultTaskGroup: '@nl_DefTG_NL_59'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ed927fb7-bd65-4edc-8fc6-678fb6956a3f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_164:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_155'
    defaultTaskGroup: '@nl_DefTG_NL_59'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4a6ecc8f-5e9b-448f-ac6f-1d3d0563a882'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_167:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_157'
    defaultTaskGroup: '@nl_DefTG_NL_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9acbb17d-123e-472c-8484-ec6e3a5ecea7'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_168:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_158'
    defaultTaskGroup: '@nl_DefTG_NL_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '465179f7-fda7-4e29-a56c-0e5c113be429'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_214']
  nl_DefTaskRel_NL_169:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_159'
    defaultTaskGroup: '@nl_DefTG_NL_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3b82e04b-d81c-44b6-a09b-db6cbbd59897'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_215', '@nl_DefTaskRule_NL_216', '@nl_DefTaskRule_NL_217', '@nl_DefTaskRule_NL_218', '@nl_DefTaskRule_NL_220', '@nl_DefTaskRule_NL_222']
  nl_DefTaskRel_NL_170:
    sequenceNumber: '15'
    defaultTask: '@nl_DefTask_NL_161'
    defaultTaskGroup: '@nl_DefTG_NL_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c261a578-a8b4-4959-a4a1-218120d8acc1'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_219']
  nl_DefTaskRel_NL_171:
    sequenceNumber: '25'
    defaultTask: '@nl_DefTask_NL_162'
    defaultTaskGroup: '@nl_DefTG_NL_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '44a2ae6c-6acc-4edd-9076-18d63648d7f9'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_221']
  nl_DefTaskRel_NL_172:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_163'
    defaultTaskGroup: '@nl_DefTG_NL_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '6278c69b-319f-44f0-84c1-44ac4cdbb20c'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_173:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_164'
    defaultTaskGroup: '@nl_DefTG_NL_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'faf49193-06da-469a-abe6-e1e3171e3c25'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_223']
  nl_DefTaskRel_NL_174:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_165'
    defaultTaskGroup: '@nl_DefTG_NL_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '2c4cb965-16d9-464c-81c6-f7864a1e879f'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_231']
  nl_DefTaskRel_NL_175:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_166'
    defaultTaskGroup: '@nl_DefTG_NL_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4593b2c7-4f62-4ddc-9718-06c3d788ac57'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_224']
  nl_DefTaskRel_NL_176:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_167'
    defaultTaskGroup: '@nl_DefTG_NL_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '66c2fab0-638c-46f8-aecc-f15b8dac526b'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_225', '@nl_DefTaskRule_NL_226', '@nl_DefTaskRule_NL_227', '@nl_DefTaskRule_NL_228', '@nl_DefTaskRule_NL_229', '@nl_DefTaskRule_NL_230']
  nl_DefTaskRel_NL_177:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_168'
    defaultTaskGroup: '@nl_DefTG_NL_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '282ca42f-022b-4793-a9f7-5db6ca89ce89'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_178:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_169'
    defaultTaskGroup: '@nl_DefTG_NL_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3e0b08d8-4e50-4ff3-9112-a51bbdd494a2'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_232']
  nl_DefTaskRel_NL_179:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_170'
    defaultTaskGroup: '@nl_DefTG_NL_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '70a0ed65-08c7-4059-beca-4ed0cdbd979e'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_240']
  nl_DefTaskRel_NL_180:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_171'
    defaultTaskGroup: '@nl_DefTG_NL_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6b9f90c5-bc61-4e1d-8f27-4f4c89e28096'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_233']
  nl_DefTaskRel_NL_181:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_172'
    defaultTaskGroup: '@nl_DefTG_NL_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '12ddc702-b4be-4c10-8150-9bc61592fa59'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_234', '@nl_DefTaskRule_NL_235', '@nl_DefTaskRule_NL_236', '@nl_DefTaskRule_NL_237', '@nl_DefTaskRule_NL_238', '@nl_DefTaskRule_NL_239']
  nl_DefTaskRel_NL_182:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_173'
    defaultTaskGroup: '@nl_DefTG_NL_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3dea553c-943a-4e1e-bcb8-b0529141195d'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_183:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_174'
    defaultTaskGroup: '@nl_DefTG_NL_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9610dc19-b2d7-4a5c-83d8-d29b5e7cb6e2'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_241']
  nl_DefTaskRel_NL_184:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_175'
    defaultTaskGroup: '@nl_DefTG_NL_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'b11d3d5a-b98f-4284-bf96-966b79397c50'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_249']
  nl_DefTaskRel_NL_185:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_176'
    defaultTaskGroup: '@nl_DefTG_NL_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '47774c7c-2a59-4cd5-b548-f6b3b99dff84'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_242']
  nl_DefTaskRel_NL_186:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_177'
    defaultTaskGroup: '@nl_DefTG_NL_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '292bcdfd-431e-48b7-8f0d-81d0f6dc892e'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_243', '@nl_DefTaskRule_NL_244', '@nl_DefTaskRule_NL_245', '@nl_DefTaskRule_NL_246', '@nl_DefTaskRule_NL_247', '@nl_DefTaskRule_NL_248']
  nl_DefTaskRel_NL_187:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_178'
    defaultTaskGroup: '@nl_DefTG_NL_64'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b5bc8b5f-b4e1-4941-8235-66f3d8ab6bf9'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_188:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_179'
    defaultTaskGroup: '@nl_DefTG_NL_64'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '66bcbf6a-dae2-4ff2-8d4b-5215a3073433'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_250']
  nl_DefTaskRel_NL_189:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_180'
    defaultTaskGroup: '@nl_DefTG_NL_64'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'd45a81d1-deef-4158-ba61-5bbe29ecb7fd'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_258']
  nl_DefTaskRel_NL_190:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_181'
    defaultTaskGroup: '@nl_DefTG_NL_64'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bb330c28-fb82-4f72-b719-5cc0bc485ce3'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_251']
  nl_DefTaskRel_NL_191:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_182'
    defaultTaskGroup: '@nl_DefTG_NL_64'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '861b1152-2397-44da-9b64-373e38f15c9a'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_252', '@nl_DefTaskRule_NL_253', '@nl_DefTaskRule_NL_254', '@nl_DefTaskRule_NL_255', '@nl_DefTaskRule_NL_256', '@nl_DefTaskRule_NL_257']
  nl_DefTaskRel_NL_192:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_183'
    defaultTaskGroup: '@nl_DefTG_NL_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8098c4fd-fe11-4755-a8ab-8ef7e2159629'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_193:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_184'
    defaultTaskGroup: '@nl_DefTG_NL_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0db7b431-e653-4630-af02-ddc0f9530579'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_259']
  nl_DefTaskRel_NL_194:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_185'
    defaultTaskGroup: '@nl_DefTG_NL_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '2bf4e40b-f167-4e65-b61f-55799d5263d8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_267']
  nl_DefTaskRel_NL_195:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_186'
    defaultTaskGroup: '@nl_DefTG_NL_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '78c17906-193f-4012-a070-df28676d9deb'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_260']
  nl_DefTaskRel_NL_196:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_187'
    defaultTaskGroup: '@nl_DefTG_NL_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e282a096-da38-4665-aa11-34cc8892efd0'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_261', '@nl_DefTaskRule_NL_262', '@nl_DefTaskRule_NL_263', '@nl_DefTaskRule_NL_264', '@nl_DefTaskRule_NL_265', '@nl_DefTaskRule_NL_266']
  nl_DefTaskRel_NL_197:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_188'
    defaultTaskGroup: '@nl_DefTG_NL_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cda9698c-aa6e-4503-8c86-f6e675d0385f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_198:
    sequenceNumber: '20'
    defaultTask: '@nl_DefTask_NL_189'
    defaultTaskGroup: '@nl_DefTG_NL_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fb89abf5-98a6-4f54-a078-fa46ad1850b8'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_268']
  nl_DefTaskRel_NL_199:
    sequenceNumber: '30'
    defaultTask: '@nl_DefTask_NL_190'
    defaultTaskGroup: '@nl_DefTG_NL_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '76a17cb7-c72e-49b7-b5ad-66bb9e91288b'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_276']
  nl_DefTaskRel_NL_200:
    sequenceNumber: '40'
    defaultTask: '@nl_DefTask_NL_191'
    defaultTaskGroup: '@nl_DefTG_NL_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8a67172f-5daa-412a-b4a1-1d58d1092157'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_269']
  nl_DefTaskRel_NL_201:
    sequenceNumber: '50'
    defaultTask: '@nl_DefTask_NL_192'
    defaultTaskGroup: '@nl_DefTG_NL_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5cf83768-f862-4389-983a-96db79b7a4bb'
    tenant: '<getNetherlandsTenant()>'
    rules: ['@nl_DefTaskRule_NL_270', '@nl_DefTaskRule_NL_271', '@nl_DefTaskRule_NL_272', '@nl_DefTaskRule_NL_273', '@nl_DefTaskRule_NL_274', '@nl_DefTaskRule_NL_275']
  nl_DefTaskRel_NL_202:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_193'
    defaultTaskGroup: '@nl_DefTG_NL_67'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4bbc48c6-91d2-4889-901d-7f128be5a843'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_203:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_194'
    defaultTaskGroup: '@nl_DefTG_NL_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f211c7dc-0c87-4b22-80d2-3c61f6ff5e5c'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_204:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_195'
    defaultTaskGroup: '@nl_DefTG_NL_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '610f1680-217a-42d0-b8e4-ef945f0c8412'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_205:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_196'
    defaultTaskGroup: '@nl_DefTG_NL_70'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e72f1f47-ea85-4fad-9f99-5a9b8439944f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_206:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_197'
    defaultTaskGroup: '@nl_DefTG_NL_71'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '802260a9-0129-4195-8c88-2a59ab9e1714'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_207:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_198'
    defaultTaskGroup: '@nl_DefTG_NL_72'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a6857315-1307-40a4-b5ab-117650dd92ce'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_208:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_199'
    defaultTaskGroup: '@nl_DefTG_NL_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c4e37cd1-22cd-4c44-8653-53357c3043ff'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_209:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_200'
    defaultTaskGroup: '@nl_DefTG_NL_74'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '16a78cec-3b61-49fa-809c-779fb6631c43'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_210:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_201'
    defaultTaskGroup: '@nl_DefTG_NL_75'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '750b123d-d8c1-441c-8c1a-3c12539a168f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_211:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_202'
    defaultTaskGroup: '@nl_DefTG_NL_76'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9e15de35-36c5-488b-a128-ce24c5a6ad4f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_212:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_203'
    defaultTaskGroup: '@nl_DefTG_NL_77'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7869035e-477e-4450-ad16-63070d69308f'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_213:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_204'
    defaultTaskGroup: '@nl_DefTG_NL_78'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ee72c24e-443b-412b-aced-931175b696df'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_214:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_205'
    defaultTaskGroup: '@nl_DefTG_NL_79'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3b145df7-5650-40d9-a9d8-c7542ef24995'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_215:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_206'
    defaultTaskGroup: '@nl_DefTG_NL_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ab795b54-e401-486c-b1cc-729e2f517e96'
    tenant: '<getNetherlandsTenant()>'
  nl_DefTaskRel_NL_216:
    sequenceNumber: '10'
    defaultTask: '@nl_DefTask_NL_207'
    defaultTaskGroup: '@nl_DefTG_NL_81'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7701dae4-e40c-4e3a-b967-3ea95cc27ddb'
    tenant: '<getNetherlandsTenant()>'
