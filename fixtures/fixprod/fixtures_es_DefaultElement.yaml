App\Domain\Entity\ValueObject\Element:
  es_DefElem_ES_1_1:
    __construct: { id: 97bee786-6378-5b2f-9020-bf051af98261, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_2:
    __construct: { id: 45e71256-bc9a-5f24-bf62-00611c7b1f15, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_3:
    __construct: { id: aa6fe30d-43b4-5e77-a5d6-9261066bc147, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_4:
    __construct: { id: 4799a318-10f8-55aa-a8d4-02569525c42e, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_5:
    __construct: { id: 3cf9457b-383f-5934-9cde-9a5fc7f68809, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_6:
    __construct: { id: 63654fd3-5e6f-57f7-8051-4f856deaafac, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_7:
    __construct: { id: fc26adeb-b602-5c5e-8a5e-56c4a86fffe5, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_8:
    __construct: { id: 9e6d10a1-5ca7-53af-a819-51ab577d1003, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_9:
    __construct: { id: a10dc3ac-c44f-5334-b8ff-c4542b22dc4b, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_10:
    __construct: { id: 0f76c66a-4712-5ae5-b509-8a8160e63adc, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_11:
    __construct: { id: 3932f1b8-4aad-5f5e-848c-f971aa13440f, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_12:
    __construct: { id: 9d06f530-8776-58bc-86ca-86c0c92fe772, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_13:
    __construct: { id: 3102b719-f9bf-51fc-b98f-a6bef82d6070, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_14:
    __construct: { id: fe965fac-2380-5efb-adb9-9e74eedba4ac, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_15:
    __construct: { id: 1bd72ed6-6825-5c8f-bdc3-67942fdfeb71, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_16:
    __construct: { id: 4fc26fee-2866-531b-847c-5c1f815ba6e1, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_17:
    __construct: { id: 60017b5a-b753-552d-9569-85e40560d933, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_18:
    __construct: { id: 4e98e9a6-4c38-5777-ba3f-55dc25ca85c1, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_19:
    __construct: { id: 0b009890-0a2c-538d-92aa-e592924b49d7, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_20:
    __construct: { id: 678073f4-2739-5aeb-9de6-71502b593829, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_21:
    __construct: { id: 6d713315-58e7-5e1b-9e11-4ff07d9d5ca5, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_22:
    __construct: { id: bb235ba3-55d7-51b1-a150-98804ba8945e, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_23:
    __construct: { id: 079a4b12-5a7c-55be-b83c-a5fc731a4eab, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_24:
    __construct: { id: 840d2314-cbeb-5c60-92fd-d30f7b838bca, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_25:
    __construct: { id: a65fb8d4-aefa-5a05-b873-f0e492702143, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_26:
    __construct: { id: d7f18d1d-d89c-513e-941d-3f8e92c1f228, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_27:
    __construct: { id: 8e3a47a9-b3c2-52e8-852c-23732e580259, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_28:
    __construct: { id: 2094a15d-0a07-55c7-87e0-25fe73a5eb9c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_29:
    __construct: { id: 64d92711-bf72-5c3c-b5a4-2cafe8e447dd, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_1_30:
    __construct: { id: bbd2d8f3-2665-530f-a44b-64beed759cc1, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  es_DefElem_ES_2_1:
    __construct: { id: 0bf3984a-f072-5859-bc7b-e7821fb0cba3, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_2:
    __construct: { id: c14aa14e-03b6-50a4-afe9-c3a7e682398b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_3:
    __construct: { id: a014f064-0c69-5c51-867b-3ed803696c6b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_4:
    __construct: { id: 16a3ab67-115c-5f96-aa7b-3f7d58bc08b3, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_5:
    __construct: { id: e984338d-7415-5d85-8c9f-5b94fc30bee2, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_6:
    __construct: { id: b8c7534c-2f0d-5070-ae20-868588ea325e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_7:
    __construct: { id: d5953802-87fa-5487-b9a5-001c736882c4, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_8:
    __construct: { id: ae2c651d-3be2-5ded-8c7a-fb62662a3770, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_9:
    __construct: { id: c4569f41-c02c-52d0-ad32-86748052f9d8, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_10:
    __construct: { id: a52c132e-e384-5e68-bf9e-a918a31ca8d9, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_11:
    __construct: { id: 78b2709e-3e51-5673-9038-3125bc9ae4be, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_12:
    __construct: { id: 632ff36e-ecd5-580e-bc6a-aa8dc7279e69, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_13:
    __construct: { id: 71051357-3c0d-53f5-9eb4-b3a43e0ffa25, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_14:
    __construct: { id: 46d5ddd8-15d6-5615-8ee3-37a59ce3859c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_15:
    __construct: { id: 510b412e-8a5f-5143-9098-2a19bf591b49, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_16:
    __construct: { id: 3c5c5c43-5269-551b-8931-152ba76cc5f9, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_17:
    __construct: { id: 2fb1a857-c6c7-562e-879c-bc09382edf44, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_18:
    __construct: { id: 3c6475f5-b65b-5dd3-adcf-65f69b824eac, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_19:
    __construct: { id: c479e4bb-68cf-54a5-9de0-f202c10d8858, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_20:
    __construct: { id: 245594b9-a075-5bf7-a47d-90168774f0a5, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_21:
    __construct: { id: b0f9fb2b-54dc-5b44-9cd6-479b825a5770, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_22:
    __construct: { id: 49864020-fb33-5952-861c-7fa9bc80cea2, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_23:
    __construct: { id: a4c86730-7d71-5b96-9f54-2834839c025c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_2_24:
    __construct: { id: e9f49812-dab4-5c36-af99-06d922c058b5, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_3_1:
    __construct: { id: 75439a76-760b-551e-8a9a-15e6c8aafd8b, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_2:
    __construct: { id: 0ad232fa-9e0d-55bc-b409-8b0cc5d99f9f, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 40 }
  es_DefElem_ES_3_3:
    __construct: { id: 2a1c0cf2-4355-5fd9-8086-1b538a5f2189, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_4:
    __construct: { id: 6f45a618-541e-5046-ad6a-1d2364e92dc2, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 40 }
  es_DefElem_ES_3_5:
    __construct: { id: 7908bd1e-bd8f-58fe-8bfe-f95476e2da5e, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_6:
    __construct: { id: bd8e217c-a9dc-5db1-80be-70c3ffa9226b, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_7:
    __construct: { id: 9f9d5aa2-b7c1-52cf-9381-2e9f34318c25, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 40 }
  es_DefElem_ES_3_8:
    __construct: { id: 0f843323-eea3-522c-a313-d4570face5be, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_9:
    __construct: { id: b3199e5c-b669-5836-b6f2-a27168931ea6, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 40 }
  es_DefElem_ES_3_10:
    __construct: { id: 576559d3-bc99-5fe6-a8a9-0821ab023aaf, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_11:
    __construct: { id: ceefa07f-72dc-5dcc-b34b-3f85d19883f4, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_12:
    __construct: { id: 06fecbcd-2426-56b5-b957-d45e8f2ea2c3, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_13:
    __construct: { id: fd7c6cbb-e8ce-5eba-b8cd-aadb255f78f1, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 40 }
  es_DefElem_ES_3_14:
    __construct: { id: 43770430-7412-52be-93a7-de24129abfd2, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_15:
    __construct: { id: d91f4c25-085d-5cd6-a4bc-18b5c78489c1, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_16:
    __construct: { id: f3250c7e-e15a-56a2-9399-04edea6fd958, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_17:
    __construct: { id: ceebdd48-a39f-5338-a838-1ece0c7d377c, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_18:
    __construct: { id: 7dc8a8c3-2f30-5974-aa59-1b527415e34e, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_19:
    __construct: { id: c8d615a9-395d-5f15-8b78-89a64d0bc081, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_20:
    __construct: { id: ae11edd9-30b1-5c42-ac61-33ebbb513b99, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_21:
    __construct: { id: c97df98b-3390-5d3b-9378-d4e7e7df6082, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_22:
    __construct: { id: 2a8f90b3-8eed-5407-806a-b1295a75e984, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_23:
    __construct: { id: f21d3577-ed35-583d-856e-7db6324f896b, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_3_24:
    __construct: { id: cd64e75e-d2e1-5e78-9753-b09cdaef1c6c, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  es_DefElem_ES_4_1:
    __construct: { id: fd276676-5f0a-52e0-b7cd-a8ec4778aeeb, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_2:
    __construct: { id: 1f2f2f74-8391-5fa6-b31f-578f00b6a324, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_3:
    __construct: { id: 3407eac7-87ea-5376-830f-849826dfd75e, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_4:
    __construct: { id: 26db789e-effc-55ed-bd26-c0f3cb07ca04, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_5:
    __construct: { id: 04522096-b9aa-5203-a83b-73d48602dd25, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_6:
    __construct: { id: bc1fd959-3345-5e0b-89f7-7b129612a61d, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_7:
    __construct: { id: 2afdea7f-b93d-525e-a2bf-4485ecf8ef1e, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_8:
    __construct: { id: aecf0b15-2501-59ec-b041-e43bdc48d068, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_9:
    __construct: { id: 2a7d35b2-69da-5445-8516-662ec4689a2f, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_4_10:
    __construct: { id: 65e79e83-c0cf-58a2-8c58-7b5109f1b17a, referenceType: containerScan_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_take}}', label: '{{Please_scan_container_QR_code_take}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_take}}', containerAction: take, sequenceNumber: 10 }
  es_DefElem_ES_5_1:
    __construct: { id: 3e990528-32f3-5ef1-a825-6d9a1336db8b, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_2:
    __construct: { id: 18ea40ff-0b8a-5283-a2b1-4cafe909ada3, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_3:
    __construct: { id: 16f13fa6-cb72-5ac1-bce2-722dfcff21d0, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_4:
    __construct: { id: 03e129c6-c57d-55cd-9aa8-e737770a1fdd, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_5:
    __construct: { id: 9b16fe4a-4c5c-5706-a285-62a92e2190e0, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_6:
    __construct: { id: 62742b75-4404-5ead-8bb2-502a7530cc5c, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_7:
    __construct: { id: 18a04446-d5ef-5c49-aede-d967fcd12c95, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_8:
    __construct: { id: cd44132d-c46a-5583-9292-5f675aa20d91, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_5_9:
    __construct: { id: ee686d5b-760f-501a-90f6-6eb8fc3d0bc8, referenceType: containerAmount_take, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_take}}', label: '{{Please_enter_number_of_containers_take}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_removed}}', sequenceNumber: 10 }
  es_DefElem_ES_6_1:
    __construct: { id: 44fc5508-6ebf-5c4b-921a-3e15ab6987af, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_2:
    __construct: { id: 3467230c-5ea5-5bf6-9b82-f9ca287c54ee, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_3:
    __construct: { id: 87cfcdc5-9f8f-56e5-9efe-d4961f7c5de5, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_4:
    __construct: { id: f249c7b2-edc1-56f5-8c48-fc3ffe344399, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_5:
    __construct: { id: 2c102179-e1e3-505b-af64-4ddf5bc8788b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_6:
    __construct: { id: 670bb93d-06e1-57f3-84ea-168b0b8c174d, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_7:
    __construct: { id: 7ecb9058-3808-5ae1-ba86-e6c0777f6d14, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_8:
    __construct: { id: e0f702f8-de38-5e99-bc4b-b7b48ea219c1, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_9:
    __construct: { id: 2e42834e-aba6-5e15-8ec8-bac0735ef815, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_10:
    __construct: { id: 0dee6e43-9b6d-558b-994b-daebf038b539, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_11:
    __construct: { id: 20daa4db-94e9-595b-80f1-0ff474698d70, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_12:
    __construct: { id: 7164c722-d198-5a81-9c14-f3bfa7316b81, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_13:
    __construct: { id: fac0dde0-ddd3-5393-a210-e8b37ac90d44, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_6_14:
    __construct: { id: 2edb6581-0cb1-533a-9306-87800da3237d, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  es_DefElem_ES_7_1:
    __construct: { id: 39f67c25-815b-5556-ba50-6f9cd8d4d4b8, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 30, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_2:
    __construct: { id: 1da3a8fb-1774-5465-859a-2eec5d5f3441, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 30, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_3:
    __construct: { id: 72deb537-6af7-517c-90a6-21b67be0b490, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 30, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_4:
    __construct: { id: 4f66c392-339c-5e1c-a5ef-4cb28dad379d, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 30, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_5:
    __construct: { id: 38e095d0-4805-556a-ab51-a7464f71e3ce, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 30, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_6:
    __construct: { id: 0308a8dc-f425-5459-94e4-f79c37fab589, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_7:
    __construct: { id: 037d618f-d081-526d-8c3f-542cc8b8a013, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_8:
    __construct: { id: 8b7a1582-326a-5468-a8f9-5c26c12b43d6, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_9:
    __construct: { id: a989e15c-9313-5e98-883d-0ae4e34e0803, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_10:
    __construct: { id: c3342542-0540-5166-bc75-5adfb640594b, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_11:
    __construct: { id: 14483db8-4e60-5ca2-b146-cdca1366f535, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_12:
    __construct: { id: 25683f1c-92a0-5091-81cc-58bc22cb52db, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_13:
    __construct: { id: 06b72a50-e859-5d6a-b1ed-947b0b1771df, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_7_14:
    __construct: { id: a02748ad-24fc-54a6-86e7-450af29fb6ea, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: a1a55022-634d-45f4-871a-3e83a236dc6d, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@es_DefElemOption_ES_15', '@es_DefElemOption_ES_16'] }
  es_DefElem_ES_8_1:
    __construct: { id: 7d747f7d-5065-5b31-99a1-f9b9cf64f628, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 20 }
  es_DefElem_ES_8_2:
    __construct: { id: 03469c70-b62c-5497-aba8-e2cde48dd032, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 20 }
  es_DefElem_ES_8_3:
    __construct: { id: 381f2a47-7593-5cbd-96fc-e4bdbd22aa2b, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 20 }
  es_DefElem_ES_8_4:
    __construct: { id: af0a4956-ba3b-50b7-8050-17184e13e545, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 20 }
  es_DefElem_ES_8_5:
    __construct: { id: c45c489e-20f7-5a8a-8cf0-fb621454b93b, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 20 }
  es_DefElem_ES_8_6:
    __construct: { id: ea1b89fa-9dfd-5804-a9b5-dc69aa7bf60d, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_7:
    __construct: { id: 950fc181-186b-5ed9-94d3-8c5eb98082cd, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_8:
    __construct: { id: 90d9649d-e444-577c-bff5-8c1885a9f556, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_9:
    __construct: { id: 24c900b8-391b-5375-a603-2967366607ca, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_10:
    __construct: { id: fc998c5f-5629-5fcc-a802-bac0a1172922, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_11:
    __construct: { id: bc335f5e-53ea-5806-8e9e-cbe519261738, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_12:
    __construct: { id: 5eb612bf-65d0-54f5-b5f2-3faede53f68a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_13:
    __construct: { id: 2b0def3b-0e94-5b0a-95ce-e0f16bba61a0, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_8_14:
    __construct: { id: d61be292-56ed-5000-9c0f-3c99d5797696, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', patternHint: '{{Quantity}}', sequenceNumber: 30 }
  es_DefElem_ES_10_1:
    __construct: { id: e47e6b2c-fe1e-5feb-91b4-fa211c4b5031, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  es_DefElem_ES_10_2:
    __construct: { id: 1551866d-40c4-5e2c-a059-693a4a6fdd72, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  es_DefElem_ES_10_3:
    __construct: { id: af1172b2-692d-5f86-a1ae-73d24e14b597, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  es_DefElem_ES_10_4:
    __construct: { id: e5c84e64-21ed-5267-86fd-1984be69dc0c, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  es_DefElem_ES_10_5:
    __construct: { id: 1b83f240-1118-550a-9a83-c46c253812d2, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  es_DefElem_ES_13_1:
    __construct: { id: 46d88368-e25a-5845-a770-d2e5b09a51b2, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_2:
    __construct: { id: 3b64c2d7-c6c6-5b29-92cf-fb7ccdff64b1, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_3:
    __construct: { id: 12248076-480e-5e25-b0e9-88de5a7d74ac, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_4:
    __construct: { id: 07374156-9560-5dc6-a01c-a4767d5e7a36, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_5:
    __construct: { id: bc7e4078-16ea-5d74-afdb-54fd90402186, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_6:
    __construct: { id: d2a0f17d-19ec-50cc-a1e4-58eaf5c70490, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_7:
    __construct: { id: e2b6c1d8-10df-5f5d-a5ff-9f2d3e113ee7, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_8:
    __construct: { id: 93af6a3e-5d1e-5dff-9213-07f1206a8f21, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_9:
    __construct: { id: 134bd998-4618-54ab-8ec7-c0dea4949daf, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_10:
    __construct: { id: 8113cd61-ed0e-57a6-8b4e-adb3c2684652, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_11:
    __construct: { id: 716c5956-4531-5b48-8cb1-e0f10c12a856, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_12:
    __construct: { id: de50b20e-856f-5cb7-ae08-c26841d488a5, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_13:
    __construct: { id: 940e6095-cec0-50e9-bc1f-89cd867a3ebc, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_14:
    __construct: { id: c1f1e649-623d-5a19-8345-68267134c0b4, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_15:
    __construct: { id: 2eae7ca7-afe1-5b8a-838d-31cc83eebd66, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_16:
    __construct: { id: e27bff64-9a83-51eb-9e63-088871d73c51, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_17:
    __construct: { id: da59e590-0b25-5c3e-9d03-6837fa5ea805, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_18:
    __construct: { id: 8bb1dfc4-0568-5d8a-bab8-b382b71f4b78, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_19:
    __construct: { id: 723e3fe8-b8bb-5587-91af-b6af2cb87826, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_20:
    __construct: { id: 3ba8c463-1b13-536e-8817-8947ebcc6486, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_21:
    __construct: { id: 4ef13cf1-c6ff-5980-8564-82318025139c, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_22:
    __construct: { id: 959f72d1-b34b-5769-98c9-6e83cb9dda2b, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_23:
    __construct: { id: 4a156656-fea0-5c78-b501-fd257bf0628d, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_24:
    __construct: { id: c574d193-a534-578b-9872-f7dedde43291, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_25:
    __construct: { id: 740a27f8-1210-5399-998b-a7250e357ad4, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_26:
    __construct: { id: 4d7ff3ae-0001-50c1-bb6c-3aa375e8cbe6, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_13_27:
    __construct: { id: f7cf2b72-6b48-5fb5-9bad-0a873cc3c4e4, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMinItems: 1, valueMaxItems: 5, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_17_1:
    __construct: { id: e468892c-b6a0-5ccc-b17c-3237150053ba, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_2:
    __construct: { id: 596e7c7a-5b02-532b-a197-459a97d2652a, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_3:
    __construct: { id: efee8a61-6e0c-562e-816e-543b756d7ac6, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_4:
    __construct: { id: d6610ea5-80af-5738-a013-4c0f6edf8fde, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_5:
    __construct: { id: c7956d83-0d3c-5fad-91cc-d08cb77c9b65, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_6:
    __construct: { id: 7c6daec8-5b1b-5e7c-bfc9-fc9da2e79722, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_7:
    __construct: { id: 59f8d129-77f5-5f8f-b9b9-6fbe9b99afb0, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_8:
    __construct: { id: a23a1cd0-555b-500f-86f3-56d59db7b8fc, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_9:
    __construct: { id: 3f1246c8-b670-55ae-95fb-8336b701daf9, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_10:
    __construct: { id: f6656c0a-3c5e-5e5a-a4ac-31f50ff45cfd, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_11:
    __construct: { id: 860ad7bd-582d-59f0-8e2d-a3781dd6a5ff, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_12:
    __construct: { id: e11606da-eb13-5162-b38b-7cc6f1d7c614, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_13:
    __construct: { id: 33b26296-52de-5447-a492-41e37ad52116, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_17_14:
    __construct: { id: be91bf2d-d10d-5828-887e-5fecad2d069a, referenceType: containerScan_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code_place}}', label: '{{Please_scan_container_QR_code_place}}', valueMinItems: 1, valueMaxItems: 9999, patternHint: '{{QR_code_place}}', containerAction: place, sequenceNumber: 10 }
  es_DefElem_ES_18_1:
    __construct: { id: 94399218-da80-5080-b176-e0cbacb5ebf8, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_2:
    __construct: { id: 388049a4-252c-563b-8f1d-cc269b0b086c, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_3:
    __construct: { id: dc17c17a-6326-598b-bd8f-21718fd87c69, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_4:
    __construct: { id: 5c09b608-54ce-573e-8cd1-044d56e96cbb, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_5:
    __construct: { id: 86b838a5-63c7-56a3-b1dd-93d4cc987589, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_6:
    __construct: { id: 761ee5d9-048c-56b2-b087-480efc1b7be9, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_7:
    __construct: { id: 841b3019-28c0-5792-9fcf-7ac6aae0c80d, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_8:
    __construct: { id: 00dad083-e9d6-5a24-b91a-ea5e2a4d206f, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_9:
    __construct: { id: acd3b456-9389-548e-8b4b-98941f73b430, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_10:
    __construct: { id: 8991b0ea-6c1d-50a4-ae38-2bcd94a7f55e, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_11:
    __construct: { id: 21879ea3-e8a9-532b-82f2-2377d75b91d6, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_12:
    __construct: { id: 5389b2ff-5049-54ee-be1f-9e57e9a7f180, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_13:
    __construct: { id: 57dd1fc2-284c-5b42-8b08-bff9d21778c9, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_14:
    __construct: { id: ba7793c8-fd84-5014-8745-df855535a08f, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_18_15:
    __construct: { id: 7e741e95-283c-5465-a94d-04d4be729e1b, referenceType: containerAmount_place, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers_place}}', label: '{{Please_enter_number_of_containers_place}}', pattern: '^(?:(?!0)\d{1,5}|9999)$', patternHint: '{{containers_delivered}}', sequenceNumber: 10 }
  es_DefElem_ES_19_1:
    __construct: { id: ffa7031d-23f0-524c-9350-6f2481686c6d, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_1', '@es_DefElemOption_ES_2'] }
  es_DefElem_ES_20_1:
    __construct: { id: 7907f227-2842-5cbf-915b-6d5c2e9eac4e, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_3', '@es_DefElemOption_ES_4'] }
  es_DefElem_ES_22_1:
    __construct: { id: 60fc1570-f8d8-5acb-9dd8-94445bd0c99b, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_7', '@es_DefElemOption_ES_8'] }
  es_DefElem_ES_24_1:
    __construct: { id: 78410ae6-6401-5634-aad9-c0352648b911, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_11', '@es_DefElemOption_ES_12'] }
  es_DefElem_ES_25_1:
    __construct: { id: b714ecdf-70c3-53a4-80de-1e822573ab62, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_13', '@es_DefElemOption_ES_14'] }
  es_DefElem_ES_26_1:
    __construct: { id: 4186bcd0-de6e-57d2-ab04-3d269c5d3ac8, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{what_to_do_next?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_17', '@es_DefElemOption_ES_18', '@es_DefElemOption_ES_19'] }
  es_DefElem_ES_27_1:
    __construct: { id: 2ee1e1d2-fbc7-5aa5-98c4-33309a437880, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_2:
    __construct: { id: 05f5b437-2238-5244-af58-16f7b795b3eb, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_3:
    __construct: { id: bcf52ba8-9b54-55de-bbd9-ee4e9b4c6c53, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_4:
    __construct: { id: ddfc5513-1899-5398-aea7-759005a70bd3, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_5:
    __construct: { id: 06c94a0f-425a-5279-8756-f9aded71f0ca, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_6:
    __construct: { id: f7fea16d-27d9-565b-b388-aa31ad5266c6, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_7:
    __construct: { id: d2c24a65-2cf8-59eb-8681-f7483cd4a329, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_8:
    __construct: { id: 87ba3a62-5419-5fdb-95f3-d7fb2e73b747, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_9:
    __construct: { id: 4b710f05-58b7-55c0-8c4d-cc6a7a57a041, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_10:
    __construct: { id: f5a9d8cd-d9c5-52d9-b8a3-37799211df7b, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_11:
    __construct: { id: d9c1c8ed-dbf1-5359-b8b2-7e2f23c83e9b, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_12:
    __construct: { id: 974467cd-2492-56fa-8ce8-a52bc87b4f94, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_13:
    __construct: { id: caca7afc-548c-57eb-b3b4-9b3a12a6557d, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_14:
    __construct: { id: 5ae819f0-0c15-522a-b34b-db993f073c8a, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_15:
    __construct: { id: de80d239-723b-519b-939c-846731eace42, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_16:
    __construct: { id: 8f25ed60-a5fb-5d1d-bce7-18ae5456b6e9, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_17:
    __construct: { id: e175e518-eee0-5d3d-ba73-4261de1774d8, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_18:
    __construct: { id: 3967aac9-58e5-5994-8279-0312466a76ae, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_19:
    __construct: { id: 409cf276-f324-5718-a471-a43535ae30ae, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_20:
    __construct: { id: 61020d27-3619-5558-9a07-c1e9d21d0490, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_21:
    __construct: { id: 0ac6ac18-441f-58b2-bf5a-aa92311b0f56, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_22:
    __construct: { id: 228461ff-f939-51d9-a517-cd2d0b42631c, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_23:
    __construct: { id: e39d6bcb-54a0-5aa6-af71-121dd072540c, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_24:
    __construct: { id: 41241cdb-088c-5f99-b331-bb74d48b26b3, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_25:
    __construct: { id: 927232da-125f-58e8-a449-5d8ce334df30, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_26:
    __construct: { id: 2067572e-842e-5475-96e8-84c1c0f4cfe3, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_27:
    __construct: { id: 8e62d567-c9cf-5c23-bdc7-ad6f2ea48b5b, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_28:
    __construct: { id: 5cc992ea-aaf6-5fa6-ab1e-d40518f7c617, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_29:
    __construct: { id: be79dbbd-b5c3-56c6-819c-433e30f3016d, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_30:
    __construct: { id: 06f32dd4-2480-5629-9f0c-072a446d8430, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_31:
    __construct: { id: 78abb834-69f2-5fcb-96ec-9909083b9328, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_32:
    __construct: { id: 8846753d-bbcb-5578-be93-9fc6fec13f8a, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_33:
    __construct: { id: dcae5a27-bd53-5620-a346-95ebb37f4c47, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_34:
    __construct: { id: 53365a4c-8fc0-5ebd-b3e9-12bb5032621c, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_35:
    __construct: { id: 1bf32b17-69f1-58bd-90e0-dc2b759119c3, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_36:
    __construct: { id: ed5d4abd-92e4-58a5-91ad-f0ff532c7f9d, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_37:
    __construct: { id: 2f5abce9-ec4b-571d-a7da-3974df65ae5b, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_38:
    __construct: { id: 27bd47f8-5060-56e4-96f7-f05418b0edd6, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_39:
    __construct: { id: 77240ff5-5015-5607-a15b-9c5fcfaf33ba, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_40:
    __construct: { id: ccee602d-21ef-5dfc-8a9e-551a01fa3647, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_41:
    __construct: { id: 2b647871-7cd2-53cf-bc52-9e5bea71c366, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_42:
    __construct: { id: 280f3950-1180-5265-9a95-ed99a58047b0, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_43:
    __construct: { id: 876a2a84-9128-56cd-866b-8bc6745709ae, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_44:
    __construct: { id: 3026ffcd-84dc-5db1-8103-23c2a1153d6d, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_45:
    __construct: { id: a43161f5-9a08-533a-bf84-c83772ed475c, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_46:
    __construct: { id: 9c1d5a50-e02d-5137-aeee-4fe47ac52d2c, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_47:
    __construct: { id: d08fe489-2c08-52e2-bcfc-cbea93e7f279, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_48:
    __construct: { id: 83951829-fb63-514e-8e6e-13db75501664, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_49:
    __construct: { id: 52304a44-58ea-5788-a657-15301b8dd7c3, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_50:
    __construct: { id: 66af9278-73db-5ed5-95b2-a16e16917889, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_51:
    __construct: { id: 02f34c83-d05c-55bf-9548-e38ced66de2c, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_52:
    __construct: { id: 972b94e0-e387-5c1e-bd03-507a7281e15a, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_53:
    __construct: { id: f6b1de3a-5cf6-5cee-a9f7-55ac1ca03081, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_54:
    __construct: { id: c883361d-271c-514d-af6e-79e2c5b5ae4e, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_55:
    __construct: { id: 042c540a-3a54-571e-9516-0dbf78b7d183, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_56:
    __construct: { id: f316a514-cfc4-5d09-8fee-5e41d4257878, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_57:
    __construct: { id: 91f6a52d-16de-5396-b772-60a28aa74409, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_58:
    __construct: { id: 8a20dcde-d0da-5858-9737-78def4a1fd8b, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_59:
    __construct: { id: 77cfa5ad-38dc-54d6-9f7f-8f990644e24d, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_60:
    __construct: { id: fba23f26-7374-53ce-b9c1-c999364bd807, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_61:
    __construct: { id: f273f44b-df91-5ef5-897a-0850f7f05f17, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_62:
    __construct: { id: bda85458-d506-59d6-927f-fae70dab2b3a, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_63:
    __construct: { id: f7b7342f-f778-57ab-98c8-858f2deccc3d, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_27_64:
    __construct: { id: f88489df-0a2e-554c-8311-d17fbf59d7fa, referenceType: vehicleCheck_inspection, type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>', expectedValue: '1', placeholder: '{{No_defects}}', label: '{{No_defects_detected}}', patternHint: '{{No_defects}}', sequenceNumber: 10 }
  es_DefElem_ES_28_1:
    __construct: { id: ef845c3f-5f15-5e25-9021-5bc82c84baa4, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_2:
    __construct: { id: af34b21c-99fe-56c7-8bb0-b2eb5d98bb7e, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_3:
    __construct: { id: e029d804-7ef4-52d0-b011-08fd44bf75ef, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_4:
    __construct: { id: 7e885be3-5785-5c52-af53-700d10c0b736, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_5:
    __construct: { id: a22f687d-abf3-511a-84b0-6ed8d6f508b4, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_6:
    __construct: { id: 833d0385-3dd2-5322-ab59-958e3ed01946, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_7:
    __construct: { id: 07d8fe70-0a0e-59b9-af09-b20fa3495674, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_28_8:
    __construct: { id: 76f5678a-953a-5423-b38b-4599d75a8c45, referenceType: vehicleCheck_inspection_photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', valueMaxItems: 10, patternHint: '{{Photo}}', sequenceNumber: 10 }
  es_DefElem_ES_29_1:
    __construct: { id: f18ebc8a-b4e8-52b3-8390-866f737ade56, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_2:
    __construct: { id: 8f7a3e43-9e0f-51ec-898c-277d76996305, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_3:
    __construct: { id: e0bb575c-861e-5a97-8aa9-a59fb35793a6, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_4:
    __construct: { id: 9dc1f28e-fb40-55e1-9a91-0b06fad68c62, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_5:
    __construct: { id: 1298bbe6-8b72-5449-a99f-4a9fc49c3604, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_6:
    __construct: { id: f11d65f7-ef77-50aa-9f66-dc74b0ae260f, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_7:
    __construct: { id: 84a5c9d2-f8c1-5357-a09d-e99250dd01bd, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_29_8:
    __construct: { id: 8881ef18-7c08-5379-b8e2-c8afbe0d7dd0, referenceType: vehicleCheck_inspection_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  es_DefElem_ES_30_1:
    __construct: { id: 1e03e153-4a01-529f-a06d-fecef4ee38d9, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_2:
    __construct: { id: 7c87cee7-d608-5b8b-b8cf-dcb5d1fd91c6, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_3:
    __construct: { id: 4dfadd4b-f896-579f-9960-b87c0a244d35, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_4:
    __construct: { id: c99d7b7c-be8e-5790-886b-aa115e826b85, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_5:
    __construct: { id: 5b299dbb-1367-5291-91a2-6e789b25dc40, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_6:
    __construct: { id: e9486cb1-1952-5694-be6f-8efa840a0f39, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_7:
    __construct: { id: e53f7e9c-490f-5612-8484-8137bdeecc4e, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_8:
    __construct: { id: 216a9c34-c5a2-5ab5-bb40-7beeea0fc60b, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_9:
    __construct: { id: a0a5d80d-b367-50bb-82fd-33f1b92b0a78, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_10:
    __construct: { id: 9c957375-24b1-5b86-8db3-93520bf55533, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_30_11:
    __construct: { id: d8fbd7e7-1f65-5d66-8d77-d25ee179f356, referenceType: di_number_input, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{ESP_Please_enter_Di_number}}', label: '{{ESP_Di_number}}', valueMaxItems: 1, patternHint: '{{ESP_Please_enter_Di_number}}', sequenceNumber: 10 }
  es_DefElem_ES_31_1:
    __construct: { id: 01fab63e-c112-5314-89d2-e9c94b5fa6a6, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{what_to_do_next?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@es_DefElemOption_ES_20', '@es_DefElemOption_ES_21', '@es_DefElemOption_ES_22'] }
  es_DefElem_ES_32_1:
    __construct: { id: d1bbc14c-fef0-5c91-a2e8-fac62b492897, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
