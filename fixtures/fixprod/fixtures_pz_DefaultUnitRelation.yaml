App\Domain\Entity\DefaultUnitRelation:
  pz_DefUnitRel_1:
    additionalServiceConfig: '@pz_AddServConf_1'
    unit: '<(App\Domain\Entity\Enum\Unit::H)>'
    sequenceNumber: '10'
    tenant: '<getGermanyTenant()>'
    id: '1ff17430-71a2-11ee-b962-0242ac120002'
  pz_DefUnitRel_2:
    additionalServiceConfig: '@pz_AddServConf_1'
    unit: '<(App\Domain\Entity\Enum\Unit::P)>'
    sequenceNumber: '20'
    tenant: '<getGermanyTenant()>'
    id: '379ee6a7-dc28-47c8-a089-8eedfceed550'
  pz_DefUnitRel_3:
    additionalServiceConfig: '@pz_AddServConf_1'
    unit: '<(App\Domain\Entity\Enum\Unit::M3)>'
    sequenceNumber: '30'
    tenant: '<getGermanyTenant()>'
    id: 'a7cf3190-c17f-460d-8694-22dfa2941e23'
  pz_DefUnitRel_4:
    additionalServiceConfig: '@pz_AddServConf_1'
    unit: '<(App\Domain\Entity\Enum\Unit::M)>'
    sequenceNumber: '40'
    tenant: '<getGermanyTenant()>'
    id: '4f20c3a8-69b9-46d0-851a-226a1f5dd574'
  pz_DefUnitRel_5:
    additionalServiceConfig: '@pz_AddServConf_1'
    unit: '<(App\Domain\Entity\Enum\Unit::L)>'
    sequenceNumber: '50'
    tenant: '<getGermanyTenant()>'
    id: 'dd1a3d80-67b5-439e-9c3d-4508d676a31c'
