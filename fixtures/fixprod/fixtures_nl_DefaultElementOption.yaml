App\Domain\Entity\ValueObject\ElementOption:
  nl_DefElemOption_NL_1:
    __construct: { name: '{{No_remark}}', id: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, sequenceNumber: 10 }
  nl_DefElemOption_NL_2:
    __construct: { name: '{{Container_Waste_unreachable}}', id: d9ca3dec-d27e-4a4b-a8a4-cf5c02148f77, sequenceNumber: 30, sourceSystemId: EXC1 }
  nl_DefElemOption_NL_3:
    __construct: { name: '{{Container_Waste_not_offered}}', id: 7e80886f-22d9-482f-8118-07ee2788effb, sequenceNumber: 20, sourceSystemId: EXC2 }
  nl_DefElemOption_NL_4:
    __construct: { name: '{{Container_locked}}', id: e00ea576-ed92-406b-85a7-67d4eba30931, sequenceNumber: 60, sourceSystemId: EXC3 }
  nl_DefElemOption_NL_5:
    __construct: { name: '{{Container_is_empty}}', id: 920fc825-3d92-4114-83f0-968581bcd0b2, sequenceNumber: 40, sourceSystemId: EXC14 }
  nl_DefElemOption_NL_6:
    __construct: { name: '{{Customers_demand}}', id: 20d68ea1-b922-42b2-9f42-9cd364b39d1e, sequenceNumber: 90, sourceSystemId: EXC4 }
  nl_DefElemOption_NL_8:
    __construct: { name: '{{Customer_is_not_present}}', id: 7d5f9970-7d0b-4670-8225-cb9879c4e091, sequenceNumber: 50, sourceSystemId: EXC6 }
  nl_DefElemOption_NL_10:
    __construct: { name: '{{Wrong_waste_offered}}', id: 1d91be12-c3df-442b-bdf8-9f0674155c02, sequenceNumber: 70, sourceSystemId: EXC8 }
  nl_DefElemOption_NL_11:
    __construct: { name: '{{Container_broken}}', id: 2426c5bc-4685-40b0-8e76-68bf33a90cc4, sequenceNumber: 80, sourceSystemId: EXC9 }
  nl_DefElemOption_NL_12:
    __construct: { name: '{{Wrong_loadingsystem}}', id: 0fa7e26c-b6e0-4da1-89eb-1749a4076b99, sequenceNumber: 100, sourceSystemId: EXC10 }
  nl_DefElemOption_NL_16:
    __construct: { name: '{{Conatiner_to_heavyincorrect_loaded}}', id: 0430f5af-4c54-4806-b963-d477d6ec72fd, sequenceNumber: 110, sourceSystemId: EXC15 }
  nl_DefElemOption_NL_23:
    __construct: { name: '{{Yes}}', id: e7f11529-1588-4545-ac7f-babb3fd87161, sequenceNumber: 1 }
  nl_DefElemOption_NL_24:
    __construct: { name: '{{No}}', id: 0db420e9-a8ba-4de9-9964-93a96cb577b6, sequenceNumber: 2 }
  nl_DefElemOption_NL_25:
    __construct: { name: '{{Diesel}}', id: b7243c57-47a0-4d64-8f64-6bc3b18d5b9e, sequenceNumber: 10 }
  nl_DefElemOption_NL_26:
    __construct: { name: '{{AdBlue}}', id: f8680b85-d82c-4782-a3b0-29373af43733, sequenceNumber: 20 }
  nl_DefElemOption_NL_27:
    __construct: { name: '{{HVO_Diesel}}', id: 7fa56628-5f54-47fa-91ed-ee0615d4082d, sequenceNumber: 30 }
  nl_DefElemOption_NL_28:
    __construct: { name: '{{CNG_LNG}}', id: 3db252f6-b7b1-4927-97d3-bdd73c35d9b9, sequenceNumber: 40 }
  nl_DefElemOption_NL_29:
    __construct: { name: '{{H2O}}', id: b6fcd9cd-004a-4a35-8ecc-7c33883e9a52, sequenceNumber: 50 }
  nl_DefElemOption_NL_30:
    __construct: { name: '{{Electricity}}', id: 8b634e1f-a627-47cc-8b44-5a8d44026dd0, sequenceNumber: 60 }
  nl_DefElemOption_NL_31:
    __construct: { name: '{{Liter}}', id: ad31925a-a75f-4a43-8d9c-96e988430a91, sequenceNumber: 10 }
  nl_DefElemOption_NL_32:
    __construct: { name: '{{kWh}}', id: 6665c13b-cbf8-4f49-8e8c-041626465932, sequenceNumber: 20 }
  nl_DefElemOption_NL_33:
    __construct: { name: '{{No_auditer_present}}', id: 67fde878-9104-4f92-9581-23f1be8fa348, sequenceNumber: 10, sourceSystemId: EMPFAENGER_KEIN }
  nl_DefElemOption_NL_34:
    __construct: { name: '{{Other_reason}}', id: 53625a59-bad1-418b-9cdf-39a71e212189, sequenceNumber: 20 }
