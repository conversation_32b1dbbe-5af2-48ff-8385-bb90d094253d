App\Domain\Entity\DefaultTaskGroup:
  pz_sub_DefTG_SUB_DE_1:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_1'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'aa8768f8-d4a0-4110-b35e-92d285ac765a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_1']
  pz_sub_DefTG_SUB_DE_2:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_2'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '7f97db74-9d62-4f2d-b87f-1dc3b5c56f8c'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_2']
  pz_sub_DefTG_SUB_DE_3:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_2'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2231c73b-44a7-4c27-a93a-d80957a22d92'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_1']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_3']
  pz_sub_DefTG_SUB_DE_4:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_3'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '539927bc-5a38-4ffa-8df3-2979453fc54e'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_4']
  pz_sub_DefTG_SUB_DE_5:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_3'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '26c3b9f9-a34c-47bf-a86b-02c853671b21'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_2']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_5']
  pz_sub_DefTG_SUB_DE_6:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_3'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'e5bc6762-493b-438b-a79e-46766120b092'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_3']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_6']
  pz_sub_DefTG_SUB_DE_7:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_4'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'aa1cd105-7fd2-4d4b-a76d-6c1158d79ef8'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_7']
  pz_sub_DefTG_SUB_DE_8:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_4'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '6f3f69b2-1453-483f-b0a8-64057a71ed94'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_4']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_8']
  pz_sub_DefTG_SUB_DE_9:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_4'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'fe04fd3d-128c-47c3-bb47-99dbcf6f2c99'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_5']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_9']
  pz_sub_DefTG_SUB_DE_10:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_5'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '3aef8c0b-e0d6-4f18-9a49-3480bf8879c9'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_10']
  pz_sub_DefTG_SUB_DE_11:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_5'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'd53e51b4-e9e1-4359-aeac-673b40ce736c'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_6']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_11']
  pz_sub_DefTG_SUB_DE_12:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_5'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '10c2b026-bc66-4897-a090-85bc466222d2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_7']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_12']
  pz_sub_DefTG_SUB_DE_13:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_6'
    title: '{{Loading_location}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '984dbe58-7abe-4725-aff1-e6c81d3c76ec'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_sub_DefTG_SUB_DE_14:
    orderTypeConfig: '@pz_sub_OrderConf_SUB_DE_6'
    title: '{{Customer}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '339ea216-af64-48b5-8b48-d50d671bf93a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_sub_DefTGR_SUB_DE_8']
    additionalInformationItems: ['@pz_sub_DefAddInfo_SUB_DE_13']
