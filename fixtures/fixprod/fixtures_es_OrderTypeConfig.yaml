App\Domain\Entity\OrderTypeConfig:
  es_OrderTypeConfig_ES_1:
    name: '{{exchange}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CHNG)>'
    id: 'bd6cbecb-40da-45b3-b9c2-a307d013e2b0'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_2:
    name: '{{transport}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::TRA)>'
    id: 'b353328d-6428-486b-8bdf-1f7ae1603a05'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_3:
    name: '{{delivery}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::PLC)>'
    id: '482f4e52-3cc5-458f-a2a4-f2886e648b01'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_4:
    name: '{{replacement}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::REP)>'
    id: 'affaae05-5215-4624-a6d9-d0549695141c'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_5:
    name: '{{empty_and_return}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::RET)>'
    id: '119f811e-a89b-48e6-82b9-92170610f407'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_6:
    name: '{{internal_movement}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::MOV)>'
    id: '1430b1ea-161d-45bc-91c7-1a8e6a2b9f6a'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_7:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::REM)>'
    id: '6eb5e87a-155b-405c-bbdd-86ef725c5c09'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_8:
    name: '{{exchange}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CHNGIT)>'
    id: 'c41f9a4d-e213-45da-8265-9d7ed2fc5358'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_9:
    name: '{{delivery}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::PLCIT)>'
    id: '12159f2a-6e82-4e5d-986c-fbcdec420baf'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_10:
    name: '{{replacement}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::REPIT)>'
    id: '529e14ed-c6a7-4f7f-8fd3-8e4921c50e9a'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_11:
    name: '{{empty}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::RETIT)>'
    id: 'b416aba2-de2d-4bfe-a602-d67a9bb41e54'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_12:
    name: '{{cleaning}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::LIMIT)>'
    id: 'd00cee13-168f-4ff3-8b17-0a8aee1d1c0c'
    tenant: '<getSpainTenant()>'
  es_OrderTypeConfig_ES_13:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::REMIT)>'
    id: '6bacbfa9-084a-4813-a1c2-a772e7e050a3'
    tenant: '<getSpainTenant()>'
