App\Domain\Entity\DefaultInterruptionRelation:
  es_DefIntRel_ES_1:
    sequenceNumber: '10'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_1'
    tenant: '<getSpainTenant()>'
  es_DefIntRel_ES_2:
    sequenceNumber: '20'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_2'
    tenant: '<getSpainTenant()>'
  es_DefIntRel_ES_3:
    sequenceNumber: '30'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_3'
    tenant: '<getSpainTenant()>'
  es_DefIntRel_ES_4:
    sequenceNumber: '40'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_4'
    tenant: '<getSpainTenant()>'
  es_DefIntRel_ES_5:
    sequenceNumber: '50'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_5'
    tenant: '<getSpainTenant()>'
  es_DefIntRel_ES_6:
    sequenceNumber: '60'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_6'
    tenant: '<getSpainTenant()>'
  es_DefIntRel_ES_7:
    sequenceNumber: '70'
    tourDataConfig: '@es_TourConf_ES_1'
    defaultInterruption: '@es_DefInt_ES_7'
    tenant: '<getSpainTenant()>'
