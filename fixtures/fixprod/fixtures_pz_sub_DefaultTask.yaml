App\Domain\Entity\DefaultTask:
  pz_sub_DefTask_SUB_DE_1:
    name: '{{Arrival}}'
    id: 'fd8326a8-7311-4bf0-9cd2-19970720cbee'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_1']
  pz_sub_DefTask_SUB_DE_2:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'f78b1c43-37c0-4342-a890-908916400d78'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_2_1']
  pz_sub_DefTask_SUB_DE_3:
    name: '{{Number_of_containers}}'
    id: '45c438ab-6cb9-4f03-985a-2cda27eb973e'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_3_1']
  pz_sub_DefTask_SUB_DE_4:
    name: '{{Ready}}'
    id: 'be55ead1-057d-476b-89df-fe41dc89fc91'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_1']
  pz_sub_DefTask_SUB_DE_5:
    name: '{{Arrival}}'
    id: '5b741e36-f676-4063-adde-2e5ac9b7d989'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_2']
  pz_sub_DefTask_SUB_DE_6:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '92580208-d66f-41b2-89bc-efdc4c67f9cc'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_1', '@pz_sub_DefElem_SUB_DE_6_1', '@pz_sub_DefElem_SUB_DE_7_1', '@pz_sub_DefElem_SUB_DE_8_1']
  pz_sub_DefTask_SUB_DE_7:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '676fbc05-8775-4df4-b057-ca6188a3dbd1'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_2_2']
  pz_sub_DefTask_SUB_DE_8:
    name: '{{Number_of_containers}}'
    id: '7c08df41-ee18-418c-8de8-1f279c2ed610'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_3_2']
  pz_sub_DefTask_SUB_DE_9:
    name: '{{Ready}}'
    id: '613c14c1-fa2d-4b5a-9556-4e0318e74ac1'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_2']
  pz_sub_DefTask_SUB_DE_10:
    name: '{{Arrival}}'
    id: '103479d3-2c64-47c6-b68d-44fa8eab4b10'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_3']
  pz_sub_DefTask_SUB_DE_11:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '32ed9286-64b1-4137-b0dc-133497c123f1'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_2', '@pz_sub_DefElem_SUB_DE_6_2', '@pz_sub_DefElem_SUB_DE_7_2', '@pz_sub_DefElem_SUB_DE_8_2']
  pz_sub_DefTask_SUB_DE_12:
    name: '{{Ready}}'
    id: '12b2d4e4-10e4-49a4-bead-f6d15c2fb54e'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_3']
  pz_sub_DefTask_SUB_DE_13:
    name: '{{Arrival}}'
    id: '4d207e6b-beef-4ca7-b1d7-35d5e8e0fceb'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_4']
  pz_sub_DefTask_SUB_DE_14:
    name: '{{Ready}}'
    id: 'ebdff60a-b9f1-42b3-9ef0-4b5800530c34'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_4']
  pz_sub_DefTask_SUB_DE_15:
    name: '{{Arrival}}'
    id: '98c9b70b-32fb-4cee-bc2d-5dfa79913f2d'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_5']
  pz_sub_DefTask_SUB_DE_16:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'b1d25d97-89bf-40b2-9824-c233c2b629d7'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_3', '@pz_sub_DefElem_SUB_DE_6_3', '@pz_sub_DefElem_SUB_DE_7_3', '@pz_sub_DefElem_SUB_DE_8_3']
  pz_sub_DefTask_SUB_DE_17:
    name: '{{Ready}}'
    id: '76ef133f-1b97-4e7e-8d1a-768cc44d0b99'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_5']
  pz_sub_DefTask_SUB_DE_18:
    name: '{{Arrival}}'
    id: '03de688d-29bc-48d2-9810-b5c8fe289460'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_6']
  pz_sub_DefTask_SUB_DE_19:
    name: '{{Ready}}'
    id: 'c42577fe-f1b9-4a7f-9c16-85e213dbb562'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_6']
  pz_sub_DefTask_SUB_DE_20:
    name: '{{Number_of_containers}}'
    id: '7b090042-ce62-41f2-bb39-b1c3668389e8'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_3_3']
  pz_sub_DefTask_SUB_DE_21:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '89222e48-c364-4d33-81af-4dfe4db1eed8'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_2_3']
  pz_sub_DefTask_SUB_DE_22:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '19787b9d-5a71-47ab-a126-76d5410f3eb7'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_4', '@pz_sub_DefElem_SUB_DE_6_4', '@pz_sub_DefElem_SUB_DE_7_4', '@pz_sub_DefElem_SUB_DE_8_4']
  pz_sub_DefTask_SUB_DE_23:
    name: '{{Arrival}}'
    id: '55ca8e90-18c6-421a-84b9-d035cbf8df41'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_7']
  pz_sub_DefTask_SUB_DE_24:
    name: '{{Ready}}'
    id: 'e57694b2-e71b-43b2-989c-8e8b294b07d7'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_7']
  pz_sub_DefTask_SUB_DE_25:
    name: '{{Arrival}}'
    id: 'acf395d1-4f10-410a-b7ef-83976d362cc9'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_8']
  pz_sub_DefTask_SUB_DE_26:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'e703b8ce-8835-4506-ba44-5f5e83a4e006'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_5', '@pz_sub_DefElem_SUB_DE_6_5', '@pz_sub_DefElem_SUB_DE_7_5', '@pz_sub_DefElem_SUB_DE_8_5']
  pz_sub_DefTask_SUB_DE_27:
    name: '{{Ready}}'
    id: '88fce3c5-c253-499b-a20a-7691eea1e398'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_8']
  pz_sub_DefTask_SUB_DE_28:
    name: '{{Arrival}}'
    id: '0720d51b-d1f7-4f8c-bcc8-cd97f103dd04'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_9']
  pz_sub_DefTask_SUB_DE_29:
    name: '{{Ready}}'
    id: 'e3c34ee8-947e-475a-9d1b-ff97e3c24f8f'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_9']
  pz_sub_DefTask_SUB_DE_30:
    name: '{{Number_of_containers}}'
    id: 'fe5dae31-8b40-401b-817d-b33674dec1db'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_3_4']
  pz_sub_DefTask_SUB_DE_31:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'c1c81bf4-a617-4d88-90e2-054d796c1197'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_2_4']
  pz_sub_DefTask_SUB_DE_32:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '76e45b7f-0d73-4768-8260-a4ec30445246'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_6', '@pz_sub_DefElem_SUB_DE_6_6', '@pz_sub_DefElem_SUB_DE_7_6', '@pz_sub_DefElem_SUB_DE_8_6']
  pz_sub_DefTask_SUB_DE_33:
    name: '{{Arrival}}'
    id: '65726d77-b2b6-408f-9a27-b6502d5b0316'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_10']
  pz_sub_DefTask_SUB_DE_34:
    name: '{{Ready}}'
    id: '64cdbe16-c9f9-416b-9bdd-db0cfd9b832b'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_10']
  pz_sub_DefTask_SUB_DE_35:
    name: '{{Arrival}}'
    id: 'b93d1369-c238-4112-b6da-2f9b0e2dc0f0'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_11']
  pz_sub_DefTask_SUB_DE_36:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'cdda5dd0-46dc-4ea9-a214-01f48edad98d'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_7', '@pz_sub_DefElem_SUB_DE_6_7', '@pz_sub_DefElem_SUB_DE_7_7', '@pz_sub_DefElem_SUB_DE_8_7']
  pz_sub_DefTask_SUB_DE_37:
    name: '{{Ready}}'
    id: '84acd93b-f7fd-474e-bf8b-66746eec4ccd'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_11']
  pz_sub_DefTask_SUB_DE_38:
    name: '{{Arrival}}'
    id: '98e1516c-7e9a-4523-bb30-ad48b5d6cae9'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_12']
  pz_sub_DefTask_SUB_DE_39:
    name: '{{Ready}}'
    id: '2e300d60-ab0e-4541-86b1-fe5a55675afd'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_12']
  pz_sub_DefTask_SUB_DE_40:
    name: '{{Number_of_containers}}'
    id: '46e72c3c-fdce-4dce-b18d-6cbb7c4ae26d'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_3_5']
  pz_sub_DefTask_SUB_DE_41:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '9d2e55f6-6d7a-4343-8e89-55078d75ce9e'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_2_5']
  pz_sub_DefTask_SUB_DE_42:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'e6cdc061-2de2-48e4-8655-892036060e24'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_8', '@pz_sub_DefElem_SUB_DE_6_8', '@pz_sub_DefElem_SUB_DE_7_8', '@pz_sub_DefElem_SUB_DE_8_8']
  pz_sub_DefTask_SUB_DE_43:
    name: '{{Arrival}}'
    id: 'a8c1de92-fb71-4860-8ee9-6d775fe46893'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_13']
  pz_sub_DefTask_SUB_DE_44:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '804a3634-733c-4bca-906b-91b8859d990e'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_9', '@pz_sub_DefElem_SUB_DE_6_9', '@pz_sub_DefElem_SUB_DE_7_9', '@pz_sub_DefElem_SUB_DE_8_9']
  pz_sub_DefTask_SUB_DE_45:
    name: '{{Ready}}'
    id: '65e235aa-588a-4c90-9b8e-82245fa9929c'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_13']
  pz_sub_DefTask_SUB_DE_46:
    name: '{{Arrival}}'
    id: '24cb8840-3b42-4651-a556-4a08488d6266'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_1_14']
  pz_sub_DefTask_SUB_DE_47:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '500f1acc-a576-4484-bd1e-bf5f5cca14f4'
    type: 'customer_weight'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_5_10', '@pz_sub_DefElem_SUB_DE_6_10', '@pz_sub_DefElem_SUB_DE_7_10', '@pz_sub_DefElem_SUB_DE_8_10']
  pz_sub_DefTask_SUB_DE_48:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'c0c98eb5-1c63-4086-8fd0-db3690cde12f'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_2_6']
  pz_sub_DefTask_SUB_DE_49:
    name: '{{Number_of_containers}}'
    id: 'ced2803a-4572-4d77-b8f7-543f9fc7cc62'
    type: 'container_count'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@pz_sub_DefElem_SUB_DE_3_6']
  pz_sub_DefTask_SUB_DE_50:
    name: '{{Ready}}'
    id: 'a7fd112e-0076-4a40-9a2d-13dcc56dd73f'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@pz_sub_DefElem_SUB_DE_4_14']
