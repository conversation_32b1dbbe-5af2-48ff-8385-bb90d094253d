App\Domain\Entity\DefaultInterruption:
  es_DefInt_ES_1:
    description: '{{Refuel}}'
    externalId: '10'
    id: '93378897-da77-4fa3-84fa-fbc452450cd7'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  es_DefInt_ES_2:
    description: '{{Break}}'
    externalId: '20'
    id: 'cdc130ab-96e9-43d5-aa12-21577cc5815a'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  es_DefInt_ES_3:
    description: '{{Workshop}}'
    externalId: '30'
    id: '79493e69-db07-472b-87a8-8e4429fdd846'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  es_DefInt_ES_4:
    description: '{{Vehicle_washing}}'
    externalId: '40'
    id: '201c5483-532a-4a82-8a8c-afd940a0e9ca'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  es_DefInt_ES_5:
    description: '{{Waiting_disposalsite}}'
    externalId: '50'
    id: '6dcaed9a-ac4d-42f5-81a8-c3fbd0d45d7e'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  es_DefInt_ES_6:
    description: '{{Waiting_for_customer}}'
    externalId: '60'
    id: 'c234254e-c293-487c-9ef5-9ddefac8d8e1'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  es_DefInt_ES_7:
    description: '{{Traffic_jam}}'
    externalId: '70'
    id: '83dbde0d-6075-471f-8efa-b747a380bfe1'
    tenant: '<getSpainTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
