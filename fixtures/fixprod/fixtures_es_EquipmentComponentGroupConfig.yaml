App\Domain\VehicleInspection\Config\EquipmentComponentGroupConfig:
  es_VIR_Group_ES_1:
    __construct: { title: '{{VIR_GENERAL_STATIC_CHECKS}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_1', '@es_EquipmentComponentConf_2', '@es_EquipmentComponentConf_3', '@es_EquipmentComponentConf_4', '@es_EquipmentComponentConf_5', '@es_EquipmentComponentConf_6', '@es_EquipmentComponentConf_7', '@es_EquipmentComponentConf_8', '@es_EquipmentComponentConf_9', '@es_EquipmentComponentConf_10', '@es_EquipmentComponentConf_11', '@es_EquipmentComponentConf_12', '@es_EquipmentComponentConf_13'] }
  es_VIR_Group_ES_2:
    __construct: { title: '{{VIR_STATIC_CHECKS_AZXUILIARY}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_14', '@es_EquipmentComponentConf_15', '@es_EquipmentComponentConf_16'] }
  es_VIR_Group_ES_3:
    __construct: { title: '{{VIR_CHECK_WITH_ENGINGE_RUNNING}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_17', '@es_EquipmentComponentConf_18', '@es_EquipmentComponentConf_19', '@es_EquipmentComponentConf_20', '@es_EquipmentComponentConf_21', '@es_EquipmentComponentConf_22', '@es_EquipmentComponentConf_23', '@es_EquipmentComponentConf_24', '@es_EquipmentComponentConf_25', '@es_EquipmentComponentConf_26'] }
  es_VIR_Group_ES_4:
    __construct: { title: '{{VIR_CHECK_AUXILIARY_WITH_ENGINE_RUNNING}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_27', '@es_EquipmentComponentConf_28', '@es_EquipmentComponentConf_29', '@es_EquipmentComponentConf_30', '@es_EquipmentComponentConf_31', '@es_EquipmentComponentConf_32', '@es_EquipmentComponentConf_33'] }
  es_VIR_Group_ES_5:
    __construct: { title: '{{VIR_GENERAL_STATIC_CHECKS}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_34', '@es_EquipmentComponentConf_35', '@es_EquipmentComponentConf_36', '@es_EquipmentComponentConf_37', '@es_EquipmentComponentConf_38', '@es_EquipmentComponentConf_39', '@es_EquipmentComponentConf_40', '@es_EquipmentComponentConf_41', '@es_EquipmentComponentConf_42', '@es_EquipmentComponentConf_43', '@es_EquipmentComponentConf_44', '@es_EquipmentComponentConf_45', '@es_EquipmentComponentConf_46'] }
  es_VIR_Group_ES_6:
    __construct: { title: '{{VIR_STATIC_CHECKS_AZXUILIARY}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_47', '@es_EquipmentComponentConf_48', '@es_EquipmentComponentConf_49', '@es_EquipmentComponentConf_50'] }
  es_VIR_Group_ES_7:
    __construct: { title: '{{VIR_CHECK_WITH_ENGINGE_RUNNING}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_51', '@es_EquipmentComponentConf_52', '@es_EquipmentComponentConf_53', '@es_EquipmentComponentConf_54', '@es_EquipmentComponentConf_55', '@es_EquipmentComponentConf_56', '@es_EquipmentComponentConf_57', '@es_EquipmentComponentConf_58', '@es_EquipmentComponentConf_59'] }
  es_VIR_Group_ES_8:
    __construct: { title: '{{VIR_CHECK_AUXILIARY_WITH_ENGINE_RUNNING}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_60', '@es_EquipmentComponentConf_61', '@es_EquipmentComponentConf_62'] }
  es_VIR_Group_ES_9:
    __construct: { title: '{{VIR_GENERAL_STATIC_CHECKS}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_63', '@es_EquipmentComponentConf_64', '@es_EquipmentComponentConf_65', '@es_EquipmentComponentConf_66', '@es_EquipmentComponentConf_67', '@es_EquipmentComponentConf_68', '@es_EquipmentComponentConf_69', '@es_EquipmentComponentConf_70', '@es_EquipmentComponentConf_71', '@es_EquipmentComponentConf_72', '@es_EquipmentComponentConf_73', '@es_EquipmentComponentConf_74', '@es_EquipmentComponentConf_75'] }
  es_VIR_Group_ES_10:
    __construct: { title: '{{VIR_STATIC_CHECKS_AZXUILIARY}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_76', '@es_EquipmentComponentConf_77', '@es_EquipmentComponentConf_78'] }
  es_VIR_Group_ES_11:
    __construct: { title: '{{VIR_CHECK_WITH_ENGINGE_RUNNING}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_79', '@es_EquipmentComponentConf_80', '@es_EquipmentComponentConf_81', '@es_EquipmentComponentConf_82', '@es_EquipmentComponentConf_83', '@es_EquipmentComponentConf_84', '@es_EquipmentComponentConf_85', '@es_EquipmentComponentConf_86', '@es_EquipmentComponentConf_87', '@es_EquipmentComponentConf_88'] }
  es_VIR_Group_ES_12:
    __construct: { title: '{{VIR_CHECK_AUXILIARY_WITH_ENGINE_RUNNING}}', automaticCompletionEnabled: false, components: ['@es_EquipmentComponentConf_89', '@es_EquipmentComponentConf_90', '@es_EquipmentComponentConf_91', '@es_EquipmentComponentConf_92', '@es_EquipmentComponentConf_93'] }
