App\Domain\Entity\DefaultTask:
  lu_int_DefTask_LU_INT_1:
    name: '{{Approach_selection}}'
    id: 'b17aebe1-db63-4a7c-8ccb-e881d846782c'
    type: 'select'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_1_1']
  lu_int_DefTask_LU_INT_2:
    name: '{{Ready}}'
    id: '197a1598-a2f7-4bac-8255-d967c525afff'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_1']
  lu_int_DefTask_LU_INT_3:
    name: '{{Ready}}'
    id: '68bc9aa3-f444-4586-91ef-1a7d56cbf367'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_27_1']
  lu_int_DefTask_LU_INT_4:
    name: '{{Ready}}'
    id: 'b8604ce3-1173-414d-9560-3b2d22ade1f8'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_2']
  lu_int_DefTask_LU_INT_5:
    name: '{{Arrival}}'
    id: 'ca6c0922-ebc5-496a-9b31-fb457eabf220'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_1']
  lu_int_DefTask_LU_INT_6:
    name: '{{Ready}}'
    id: '1af02679-38a5-40f7-868a-23e529f58c30'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_3']
  lu_int_DefTask_LU_INT_7:
    name: '{{Arrival}}'
    id: 'a9d66586-3dbe-4a34-9178-6158ceeb25f7'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_2']
  lu_int_DefTask_LU_INT_8:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '8f7b3d18-d244-413f-8307-f43d8ed7c9e3'
    type: 'signature'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_4_1']
  lu_int_DefTask_LU_INT_9:
    name: '{{Photo}}'
    id: '6f3c2feb-ecab-4657-8c5f-57486170de50'
    type: 'photo'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_14_1']
  lu_int_DefTask_LU_INT_10:
    name: '{{cleaningTime}}'
    repeatable: true
    id: 'c00cd6b4-22bb-4cde-9b57-e0a5f1e74b04'
    type: 'cleaningTime'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_23_1', '@lu_int_DefElem_LU_INT_24_1']
  lu_int_DefTask_LU_INT_11:
    name: 'deliveryOfOperationalRessource1'
    activatedBySapData: true
    id: '91607224-beb9-43fc-b3b0-57e0903d6218'
    type: 'gp_deliveryOfOpRes_1'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_25_1']
  lu_int_DefTask_LU_INT_12:
    name: 'deliveryOfOperationalRessource2'
    activatedBySapData: true
    id: 'a0b5e2a5-574a-4bbc-8758-191614b377c3'
    type: 'gp_deliveryOfOpRes_2'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_25_2']
  lu_int_DefTask_LU_INT_13:
    name: 'deliveryOfOperationalRessource3'
    activatedBySapData: true
    id: '2c16cc35-6a9f-4a4c-8c96-783438ab5273'
    type: 'gp_deliveryOfOpRes_3'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_25_3']
  lu_int_DefTask_LU_INT_14:
    name: 'deliveryOfOperationalRessource4'
    activatedBySapData: true
    id: 'c206591c-4d74-4415-8b8a-682156b0848b'
    type: 'gp_deliveryOfOpRes_4'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_25_4']
  lu_int_DefTask_LU_INT_15:
    name: 'deliveryOfOperationalRessource5'
    activatedBySapData: true
    id: '8094f482-f2e7-43ff-b275-f4ba04466a58'
    type: 'gp_deliveryOfOpRes_5'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_25_5']
  lu_int_DefTask_LU_INT_16:
    name: '{{Ready}}'
    id: 'f375f6fa-8c5d-46d7-b600-5e5d307ef434'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_4']
  lu_int_DefTask_LU_INT_17:
    name: '{{Arrival}}'
    id: 'eb2c0363-50b3-4a75-83ab-49444062cc87'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_3']
  lu_int_DefTask_LU_INT_18:
    name: '{{Ready}}'
    id: '18db7ed8-c90b-4ba3-aa33-27da034ced38'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_5']
  lu_int_DefTask_LU_INT_19:
    name: '{{Approach_selection}}'
    id: '93886a3c-274b-4175-a11b-5fa0b629e0b1'
    type: 'select'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_26_1']
  lu_int_DefTask_LU_INT_20:
    name: '{{Ready}}'
    id: 'c8fb9459-ec13-4cbd-8583-8550bb8225a2'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_6']
  lu_int_DefTask_LU_INT_21:
    name: '{{Ready}}'
    id: 'e42f7113-9aca-47f6-ab7b-e0f3862942a9'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_28_1']
  lu_int_DefTask_LU_INT_22:
    name: '{{Ready}}'
    id: '978f0405-ac0d-4679-8815-646d54f883bc'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_7']
  lu_int_DefTask_LU_INT_23:
    name: '{{Ready}}'
    id: '5f4eff60-e9b4-445a-ad53-7fc6d36136ee'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_8']
  lu_int_DefTask_LU_INT_24:
    name: '{{Ready}}'
    id: '4929f6ab-b244-477a-ba4a-17a3d550b94e'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_9']
  lu_int_DefTask_LU_INT_25:
    name: '{{Free_text}}'
    id: '0322ba45-4100-4fbd-a0e0-7dfd73a2eba5'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_11_1']
  lu_int_DefTask_LU_INT_26:
    name: '{{Ready}}'
    id: '76357ebb-9a71-4782-b722-c44568c0e078'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_10']
  lu_int_DefTask_LU_INT_27:
    name: '{{Ready}}'
    id: 'f78f703c-db8a-4549-8be5-676421ce56a5'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_11']
  lu_int_DefTask_LU_INT_28:
    name: '{{Ready}}'
    id: '79d70175-0007-455d-beb4-ad2cac276e69'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_12']
  lu_int_DefTask_LU_INT_29:
    name: '{{Ready}}'
    id: '178a65b4-2a96-4aef-9e87-2503475bce93'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_13']
  lu_int_DefTask_LU_INT_30:
    name: '{{Diesel}}'
    id: '1c7bfadf-9e2b-4644-a496-ddf7bb2e6b29'
    type: 'diesel'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_12_1']
  lu_int_DefTask_LU_INT_31:
    name: '{{AdBlue}}'
    id: '1d918966-5abe-4434-a6d8-fd0145013a72'
    type: 'adblue'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_13_1']
  lu_int_DefTask_LU_INT_32:
    name: '{{Ready}}'
    id: '27a10e9b-4c04-4b46-b095-551ea8c5949b'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_14']
  lu_int_DefTask_LU_INT_33:
    name: '{{Ready}}'
    id: '509b30db-594e-439c-badb-2bc4c01f577e'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_15']
  lu_int_DefTask_LU_INT_34:
    name: '{{Ready}}'
    id: '64246f95-a46d-41aa-8ec8-7f7711b43865'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_16']
  lu_int_DefTask_LU_INT_35:
    name: '{{Arrival}}'
    id: '59a7c0f3-e83d-4b72-8649-db92e5f0e6ca'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_4']
  lu_int_DefTask_LU_INT_36:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'a7dcf836-7ce3-4333-b28d-f02c7069a31f'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_1', '@lu_int_DefElem_LU_INT_8_1', '@lu_int_DefElem_LU_INT_9_1', '@lu_int_DefElem_LU_INT_10_1']
  lu_int_DefTask_LU_INT_37:
    name: '{{Ready}}'
    id: '6247065d-07f6-4213-8f58-1451870bb558'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_17']
  lu_int_DefTask_LU_INT_38:
    name: '{{Ready}}'
    id: 'c7333579-690a-4876-adf9-c0d5afad9545'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_18']
  lu_int_DefTask_LU_INT_39:
    name: '{{Ready}}'
    id: 'e6b1a4b1-5738-453a-97b7-f63e96ba6251'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_3_19']
  lu_int_DefTask_LU_INT_40:
    name: '{{Photo}}'
    id: 'cf82a42d-06d2-4d59-8443-9da0168ff01b'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_14_2']
  lu_int_DefTask_LU_INT_41:
    name: '{{Free_text}}'
    id: '127082e3-b8dc-4684-8398-86eba5d535af'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_11_2']
  lu_int_DefTask_LU_INT_42:
    name: '{{Photo}}'
    id: 'fae9f2a9-d96b-47ef-9127-4383f69d9a37'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_14_3']
  lu_int_DefTask_LU_INT_43:
    name: '{{Free_text}}'
    id: 'bc5e8f2e-be88-46d1-93a1-88726be7bc55'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_11_3']
  lu_int_DefTask_LU_INT_44:
    name: '{{Photo}}'
    id: '8f12963a-1dc5-4808-bdfc-bfe9a43aaa77'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_14_4']
  lu_int_DefTask_LU_INT_45:
    name: '{{Initial_mileage}}'
    id: '0ba0cf45-c64e-4762-a705-9ec866b5e4c6'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_1']
  lu_int_DefTask_LU_INT_46:
    name: '{{Final_mileage}}'
    id: 'c5d664e4-f88a-4095-93cc-ea1fb16842d3'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_1']
  lu_int_DefTask_LU_INT_47:
    name: '{{Photo}}'
    id: 'ac887f4f-cd06-4d02-99ac-4aa8c6f0cd28'
    type: 'foto'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_14_5']
  lu_int_DefTask_LU_INT_48:
    name: '{{Free_text}}'
    id: '44428eba-b8c4-4744-8dca-3e67240f61e7'
    type: 'text'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_11_4']
  lu_int_DefTask_LU_INT_49:
    name: '{{Initial_mileage}}'
    id: '7e43e53c-9af9-4a37-b0be-448798ff7c97'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_2']
  lu_int_DefTask_LU_INT_50:
    name: '{{Final_mileage}}'
    id: '7e6b5f7f-eba2-42fd-9105-fec763a7b91c'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_2']
  lu_int_DefTask_LU_INT_51:
    name: '{{Initial_mileage}}'
    id: 'a30d25f2-94aa-4454-b374-18a108e0dbe7'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_3']
  lu_int_DefTask_LU_INT_52:
    name: '{{Final_mileage}}'
    id: '7a215da1-37a8-438b-a979-c9ac94f65b37'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_3']
  lu_int_DefTask_LU_INT_53:
    name: '{{Arrival}}'
    id: 'ba23fa4a-c088-4d02-90fb-3b8e0a03e10b'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_5']
  lu_int_DefTask_LU_INT_54:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'c3b14636-63b9-48fe-8eb5-a9239724e75d'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_2', '@lu_int_DefElem_LU_INT_8_2', '@lu_int_DefElem_LU_INT_9_2', '@lu_int_DefElem_LU_INT_10_2']
  lu_int_DefTask_LU_INT_55:
    name: '{{Ready}}'
    id: '561dc92d-9fac-4c1d-a4b7-77fb7bf873d8'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_20']
  lu_int_DefTask_LU_INT_56:
    name: '{{Initial_mileage}}'
    id: '86bbcd22-e912-4f74-9c22-3391cfe3c829'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_4']
  lu_int_DefTask_LU_INT_57:
    name: '{{Final_mileage}}'
    id: '068bd233-cebd-45d6-848d-80ef9b1c2ae0'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_4']
  lu_int_DefTask_LU_INT_58:
    name: '{{Arrival}}'
    id: 'ff4ee6bf-bb87-49a2-81bf-562afac0a7e5'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_6']
  lu_int_DefTask_LU_INT_59:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'ced73e88-a87e-433c-94ef-f54f4cebe6aa'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_3', '@lu_int_DefElem_LU_INT_8_3', '@lu_int_DefElem_LU_INT_9_3', '@lu_int_DefElem_LU_INT_10_3']
  lu_int_DefTask_LU_INT_60:
    name: '{{Ready}}'
    id: '30b36741-df1a-48b1-bac9-37c2c91ba808'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_21']
  lu_int_DefTask_LU_INT_61:
    name: '{{Initial_mileage}}'
    id: '0e67a9e1-7013-4bb9-afb7-ac0283df242c'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_5']
  lu_int_DefTask_LU_INT_62:
    name: '{{Final_mileage}}'
    id: '325994e8-41e0-41b6-b3bb-49b18a2ff695'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_5']
  lu_int_DefTask_LU_INT_63:
    name: '{{Arrival}}'
    id: 'e2b4490e-148d-4352-a1fa-07ebd082665b'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_7']
  lu_int_DefTask_LU_INT_64:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '09aff431-9a02-45ba-9e48-1ddf202d9cc8'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_4', '@lu_int_DefElem_LU_INT_8_4', '@lu_int_DefElem_LU_INT_9_4', '@lu_int_DefElem_LU_INT_10_4']
  lu_int_DefTask_LU_INT_65:
    name: '{{Ready}}'
    id: '8e7323f5-6976-48e3-94ef-85182e750ba6'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_22']
  lu_int_DefTask_LU_INT_66:
    name: '{{Initial_mileage}}'
    id: 'eb30919e-0eef-4887-a45a-4a5fbbb47ee6'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_6']
  lu_int_DefTask_LU_INT_67:
    name: '{{Final_mileage}}'
    id: '2674ebf4-043f-469f-94e2-347b4f6fac61'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_6']
  lu_int_DefTask_LU_INT_68:
    name: '{{Arrival}}'
    id: '3e0678f4-e1c6-47f1-a08a-a0707a83cbeb'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_8']
  lu_int_DefTask_LU_INT_69:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2a813b2e-ec59-4d95-8469-f2eb2bd147d4'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_5', '@lu_int_DefElem_LU_INT_8_5', '@lu_int_DefElem_LU_INT_9_5', '@lu_int_DefElem_LU_INT_10_5']
  lu_int_DefTask_LU_INT_70:
    name: '{{Ready}}'
    id: '0bf344f1-630f-49fd-8aa8-283f93a4017c'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_23']
  lu_int_DefTask_LU_INT_71:
    name: '{{Initial_mileage}}'
    id: '230fc319-86fb-43a1-b8a0-b3b42f2c32cf'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_7']
  lu_int_DefTask_LU_INT_72:
    name: '{{Final_mileage}}'
    id: '798cc9b1-1499-4cd0-a8b7-8943ece14f32'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_7']
  lu_int_DefTask_LU_INT_73:
    name: '{{Arrival}}'
    id: 'bd179692-21c0-4ee6-aa3d-6c45efcc50d0'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_9']
  lu_int_DefTask_LU_INT_74:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'cc390d97-36d5-404b-9f67-04981babc469'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_6', '@lu_int_DefElem_LU_INT_8_6', '@lu_int_DefElem_LU_INT_9_6', '@lu_int_DefElem_LU_INT_10_6']
  lu_int_DefTask_LU_INT_75:
    name: '{{Ready}}'
    id: 'e81e512d-2a9e-4407-a846-c39a4484f83d'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_24']
  lu_int_DefTask_LU_INT_76:
    name: '{{Initial_mileage}}'
    id: '799ebb54-c95b-4148-9b95-1455fc657e09'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_8']
  lu_int_DefTask_LU_INT_77:
    name: '{{Final_mileage}}'
    id: '8f673892-b117-4760-af12-6e342bd14931'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_8']
  lu_int_DefTask_LU_INT_78:
    name: '{{Arrival}}'
    id: 'b99733b6-a418-4a13-8854-038b02d97cd5'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_10']
  lu_int_DefTask_LU_INT_79:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'b55bb5f6-dd80-4ca2-b8e9-65288c2ba236'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_7', '@lu_int_DefElem_LU_INT_8_7', '@lu_int_DefElem_LU_INT_9_7', '@lu_int_DefElem_LU_INT_10_7']
  lu_int_DefTask_LU_INT_80:
    name: '{{Ready}}'
    id: '3e7b2cf5-8854-4a0e-bb1c-e0e19aafd520'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_25']
  lu_int_DefTask_LU_INT_81:
    name: '{{Initial_mileage}}'
    id: '443a1571-1fca-40a2-921d-6fd05af27310'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_9']
  lu_int_DefTask_LU_INT_82:
    name: '{{Final_mileage}}'
    id: '363e5c41-c5c5-4950-bae4-e50c7a79e4d8'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_9']
  lu_int_DefTask_LU_INT_83:
    name: '{{Arrival}}'
    id: '9cd8477d-855e-42dd-b788-ad6afb16af83'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_11']
  lu_int_DefTask_LU_INT_84:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '158bcff6-6266-4944-9ec6-20294a976f23'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_8', '@lu_int_DefElem_LU_INT_8_8', '@lu_int_DefElem_LU_INT_9_8', '@lu_int_DefElem_LU_INT_10_8']
  lu_int_DefTask_LU_INT_85:
    name: '{{Ready}}'
    id: '04247bef-bc8d-4e1f-969f-adf5cdec664e'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_26']
  lu_int_DefTask_LU_INT_86:
    name: '{{Initial_mileage}}'
    id: '888e0c1b-d9ec-4dfb-b316-f5a3053ebd38'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_10']
  lu_int_DefTask_LU_INT_87:
    name: '{{Final_mileage}}'
    id: '28a33351-3bfa-465d-87f8-7d2ef5445eec'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_10']
  lu_int_DefTask_LU_INT_88:
    name: '{{Arrival}}'
    id: 'b03d4111-5785-437e-aff1-6d7028e87df9'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_12']
  lu_int_DefTask_LU_INT_89:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'dffc4807-b891-4ecf-b1d6-0da4c1d506c1'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_9', '@lu_int_DefElem_LU_INT_8_9', '@lu_int_DefElem_LU_INT_9_9', '@lu_int_DefElem_LU_INT_10_9']
  lu_int_DefTask_LU_INT_90:
    name: '{{Ready}}'
    id: 'd2bcd383-b6f7-41c6-82df-cb96e45d2cd4'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_27']
  lu_int_DefTask_LU_INT_91:
    name: '{{Initial_mileage}}'
    id: '368351a0-a979-4a76-b62a-2b29edbc6898'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_11']
  lu_int_DefTask_LU_INT_92:
    name: '{{Final_mileage}}'
    id: '673c8b5a-e11e-4a92-986e-d6fa6f099ec0'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_11']
  lu_int_DefTask_LU_INT_93:
    name: '{{Arrival}}'
    id: '29c23639-13ea-4c56-a044-093a17fd6997'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_13']
  lu_int_DefTask_LU_INT_94:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '0feb848f-0f19-4e2a-8a90-f714d0103592'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_10', '@lu_int_DefElem_LU_INT_8_10', '@lu_int_DefElem_LU_INT_9_10', '@lu_int_DefElem_LU_INT_10_10']
  lu_int_DefTask_LU_INT_95:
    name: '{{Ready}}'
    id: 'a65ea959-4764-414b-9053-d4c3a475911e'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_28']
  lu_int_DefTask_LU_INT_96:
    name: '{{Initial_mileage}}'
    id: '5563b7e8-6326-451e-9a51-1643bfe09e0c'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_12']
  lu_int_DefTask_LU_INT_97:
    name: '{{Final_mileage}}'
    id: '378597d0-0c39-4d05-9dc7-e7c62e89a8a8'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_12']
  lu_int_DefTask_LU_INT_98:
    name: '{{Arrival}}'
    id: '4d3afee2-99c3-4698-a029-aec1586e3dc6'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_14']
  lu_int_DefTask_LU_INT_99:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '7ed80f07-3792-4adf-9e0c-b0c275e1d1e8'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_11', '@lu_int_DefElem_LU_INT_8_11', '@lu_int_DefElem_LU_INT_9_11', '@lu_int_DefElem_LU_INT_10_11']
  lu_int_DefTask_LU_INT_100:
    name: '{{Ready}}'
    id: '3d1e6f8d-c46d-4b0d-8b13-25860ca7be14'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_29']
  lu_int_DefTask_LU_INT_101:
    name: '{{Initial_mileage}}'
    id: 'cc7da564-c0fa-4fa8-89f6-10e2ce56a8b1'
    type: 'mileage_start'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_15_13']
  lu_int_DefTask_LU_INT_102:
    name: '{{Final_mileage}}'
    id: '2738b9ac-6f4f-4849-b178-bce82387bff6'
    type: 'mileage_end'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_16_13']
  lu_int_DefTask_LU_INT_103:
    name: '{{Arrival}}'
    id: '6862bd7b-2e27-4f6d-86dd-de49c35c3ce8'
    type: 'arrival'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_2_15']
  lu_int_DefTask_LU_INT_104:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '512ceeff-3491-4b6e-8ea2-0f49c44bda11'
    type: 'weighingnote'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_7_12', '@lu_int_DefElem_LU_INT_8_12', '@lu_int_DefElem_LU_INT_9_12', '@lu_int_DefElem_LU_INT_10_12']
  lu_int_DefTask_LU_INT_105:
    name: '{{Ready}}'
    id: 'd9f01d1d-4d3e-45ae-8673-372ccc35cc27'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_30']
  lu_int_DefTask_LU_INT_106:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '9ee227e4-a4af-4657-b84c-8c4a13d9769f'
    type: 'deliveryNote_sig'
    tenant: '<getGermanyTenant()>'
    elementItems: ['@lu_int_DefElem_LU_INT_4_2']
  lu_int_DefTask_LU_INT_107:
    name: '{{Ready}}'
    id: '6f89ac4d-a159-4b94-81b6-c27768e1c16a'
    type: 'departure'
    tenant: '<getGermanyTenant()>'
    sapAction: true
    elementItems: ['@lu_int_DefElem_LU_INT_3_31']
