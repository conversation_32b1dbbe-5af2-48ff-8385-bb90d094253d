App\Domain\Entity\DefaultTaskGroup:
  es_DefTG_ES_1:
    orderTypeConfig: '@es_OrderTypeConfig_ES_1'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '38bf7cb9-b82a-4b90-8c5f-d92959c0685c'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_2:
    orderTypeConfig: '@es_OrderTypeConfig_ES_1'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '7f0ef671-4ea3-4562-bb2d-1b7ea2fcc1e5'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_1']
  es_DefTG_ES_3:
    orderTypeConfig: '@es_OrderTypeConfig_ES_1'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '099fff8a-73fb-4683-8e1f-d3a62cda2e71'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_2']
  es_DefTG_ES_4:
    orderTypeConfig: '@es_OrderTypeConfig_ES_2'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '94f2e0a8-25df-4950-830a-adfc6bef5950'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_5:
    orderTypeConfig: '@es_OrderTypeConfig_ES_2'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '233f3060-40f7-4462-8df3-cadef88a0018'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_3']
  es_DefTG_ES_6:
    orderTypeConfig: '@es_OrderTypeConfig_ES_2'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '6e0a506b-2230-4989-b41d-392489bdc922'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_4']
  es_DefTG_ES_7:
    orderTypeConfig: '@es_OrderTypeConfig_ES_3'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'e7e900f6-16ea-4f4a-bf05-baa6d13dfe04'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_8:
    orderTypeConfig: '@es_OrderTypeConfig_ES_3'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: 'a4c74772-1a24-402b-8248-ff8f5b7926bd'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_5']
  es_DefTG_ES_9:
    orderTypeConfig: '@es_OrderTypeConfig_ES_4'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'fd882be1-1747-4127-8562-9d5e1056e074'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_10:
    orderTypeConfig: '@es_OrderTypeConfig_ES_4'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'e8255805-6546-415b-9dcd-6a5ebfe64737'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_6']
  es_DefTG_ES_11:
    orderTypeConfig: '@es_OrderTypeConfig_ES_4'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '0ae7bcb3-2d78-4323-b112-275ca505984d'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_7', '@es_DefTGR_ES_8']
  es_DefTG_ES_12:
    orderTypeConfig: '@es_OrderTypeConfig_ES_5'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2587f048-2670-49ad-8b6f-729819cea920'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_13:
    orderTypeConfig: '@es_OrderTypeConfig_ES_5'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '20ab1baa-aaf8-451a-904b-6753bbc45719'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_9']
  es_DefTG_ES_14:
    orderTypeConfig: '@es_OrderTypeConfig_ES_5'
    title: '{{Customer}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a985020e-54ab-4f99-b3c6-103258a01104'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_10']
  es_DefTG_ES_15:
    orderTypeConfig: '@es_OrderTypeConfig_ES_5'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '9b99b146-5359-4470-9a35-b57dba9595c2'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_11']
  es_DefTG_ES_16:
    orderTypeConfig: '@es_OrderTypeConfig_ES_6'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'b0264487-94ef-4ab5-acff-786984b19632'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_17:
    orderTypeConfig: '@es_OrderTypeConfig_ES_6'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '6c44fa88-6c0b-4802-bb17-76df112304e1'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_12']
  es_DefTG_ES_18:
    orderTypeConfig: '@es_OrderTypeConfig_ES_7'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '9b55a652-e0b2-4ecc-8409-2a8c084a211a'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_19:
    orderTypeConfig: '@es_OrderTypeConfig_ES_7'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '51e99025-d202-4426-8e1f-86dc3299a194'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_13', '@es_DefTGR_ES_14']
  es_DefTG_ES_20:
    orderTypeConfig: '@es_OrderTypeConfig_ES_7'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '5f51fb7f-2c03-49f5-b0cd-7826f1eb8d18'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@es_DefTGR_ES_15']
  es_DefTG_ES_21:
    defaultNote: '@es_DefNote_ES_1'
    title: '{{Other}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '994d9738-3c4a-4676-95bd-6c680fd6ea07'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_22:
    defaultInterruption: '@es_DefInt_ES_1'
    title: '{{Refuelling}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '681e95bd-b84d-486b-825d-1c13a468d1a4'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_23:
    defaultInterruption: '@es_DefInt_ES_2'
    title: '{{driver_rest_time}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '0acca014-8827-41ae-a537-f01389e002ee'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_24:
    defaultInterruption: '@es_DefInt_ES_3'
    title: '{{Workshop}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2479eb62-7aff-402d-b5d0-2945af12ba58'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_25:
    defaultInterruption: '@es_DefInt_ES_4'
    title: '{{Vehicle_washing}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'cd7a6970-adc0-43af-927d-37872ffd4ebc'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_26:
    defaultInterruption: '@es_DefInt_ES_5'
    title: '{{Waiting_at_waste_Treatment_plant}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'f50e4307-4b71-4a32-9356-a4edb2b045a5'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_27:
    defaultInterruption: '@es_DefInt_ES_6'
    title: '{{Waiting at the customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '41a9b032-53e9-4ab5-bd69-c0c637c70b71'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_28:
    defaultInterruption: '@es_DefInt_ES_7'
    title: '{{Traffic or road blocked}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '46e516bc-12ae-4981-ac5f-a7f45e482025'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_29:
    defaultTermination: '@es_DefTerm_ES_1'
    title: '{{Customer_not_on_site}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '22336d8b-bd7d-4c08-a1cc-518e0ad036ae'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_30:
    defaultTermination: '@es_DefTerm_ES_2'
    title: '{{Incorrect_waste}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'ba19bef5-9aa8-4e93-ace8-909d73044712'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_31:
    defaultTermination: '@es_DefTerm_ES_3'
    title: '{{Container_not_accessible_for_collection}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'd5d743e4-15a4-454b-a936-799ceb0c86b8'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_32:
    defaultTermination: '@es_DefTerm_ES_4'
    title: '{{Other_-_text}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '26462655-12a0-4888-a687-29e7444f8bfd'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_33:
    equipmentConfig: '@es_EquipmentConf_ES_2'
    title: '{{Vehicle_inspection_hook_general}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: '79af32ff-da0f-4607-909b-498289bd0079'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  es_DefTG_ES_34:
    equipmentConfig: '@es_EquipmentConf_ES_2'
    title: '{{Vehicle_inspection_hook_safety_relevant_things}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: '6e992696-4e8e-4733-8c73-a77e7ab7c4af'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
    rules: ['@es_DefTGR_ES_16']
  es_DefTG_ES_35:
    equipmentConfig: '@es_EquipmentConf_ES_2'
    title: '{{Vehicle_inspection_hoock_crane_check}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: 'b76613e2-158e-4c3d-bd6b-8cdb41260eca'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
    rules: ['@es_DefTGR_ES_17']
  es_DefTG_ES_40:
    equipmentConfig: '@es_EquipmentConf_ES_2'
    title: '{{Vehicle_inspection_hook_engine_running}} '
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: '54041527-e41e-4a76-a56c-2619e8d211a7'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
    rules: ['@es_DefTGR_ES_21']
  es_DefTG_ES_36:
    equipmentConfig: '@es_EquipmentConf_ES_3'
    title: '{{Vehicle_inspection_skip_general}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: '32bcb09e-0b9a-424f-bab1-7431ee6db9ff'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  es_DefTG_ES_37:
    equipmentConfig: '@es_EquipmentConf_ES_3'
    title: '{{Vehicle_inspection_skip_safety_relevant_things}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: 'c7b0cd86-d097-41c7-85d4-7bea47a3417c'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
    rules: ['@es_DefTGR_ES_18']
  es_DefTG_ES_38:
    equipmentConfig: '@es_EquipmentConf_ES_3'
    title: '{{Vehicle_inspection_skip_check_tools}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: 'c77334e5-f647-409b-bbbe-7149af7a74eb'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
    rules: ['@es_DefTGR_ES_19']
  es_DefTG_ES_39:
    equipmentConfig: '@es_EquipmentConf_ES_3'
    title: '{{Vehicle_inspection_skip_engine_running}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
    id: '6b880bbe-28c7-4807-97ab-3b1ab76fa6bd'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
    rules: ['@es_DefTGR_ES_20']
  es_DefTG_ES_41:
    defaultTermination: '@es_DefTerm_ES_5'
    title: '{{Route_not_completed}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '3c099284-dd71-48e1-99a7-12ea37603198'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_42:
    orderTypeConfig: '@es_OrderTypeConfig_ES_8'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '956cb29b-be4d-4859-817d-527da59e3032'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_43:
    orderTypeConfig: '@es_OrderTypeConfig_ES_8'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'f9d9ec22-f3d0-4db7-a68c-2980b04b73c5'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_44:
    orderTypeConfig: '@es_OrderTypeConfig_ES_9'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '402d90f7-8ba5-46dc-97ee-a71d5bab5ad5'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_45:
    orderTypeConfig: '@es_OrderTypeConfig_ES_10'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '1039acc8-06ac-491b-8cc2-bc65239be727'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_46:
    orderTypeConfig: '@es_OrderTypeConfig_ES_10'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '3453ec70-60ef-4e6f-8303-7ea11fb27b2a'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_47:
    orderTypeConfig: '@es_OrderTypeConfig_ES_11'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a616878e-17b3-4c74-8614-7adb29cf745f'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_48:
    orderTypeConfig: '@es_OrderTypeConfig_ES_11'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '87363e89-dfc5-4f72-be88-2373e9edb2ff'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_49:
    orderTypeConfig: '@es_OrderTypeConfig_ES_12'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2f085ae0-f9de-41e9-9481-8661b2aec305'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_50:
    orderTypeConfig: '@es_OrderTypeConfig_ES_13'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a14632be-17dc-4856-9fd3-346eb095cd9a'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_51:
    orderTypeConfig: '@es_OrderTypeConfig_ES_13'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '49cb057e-95de-46e7-93b7-943a24cbaebb'
    tenant: '<getSpainTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  es_DefTG_ES_52:
    tourDataConfig: '@es_TourConf_ES_1'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'a4df12f4-1b8b-48f7-b01b-11ad5b965aa6'
    tenant: '<getSpainTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
