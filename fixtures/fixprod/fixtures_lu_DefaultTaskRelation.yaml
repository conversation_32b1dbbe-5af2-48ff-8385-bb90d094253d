App\Domain\Entity\DefaultTaskRelation:
  lu_DefTaskRel_LU_1:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_1'
    defaultTaskGroup: '@lu_DefTG_LU_30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0c29184a-df9b-4419-9c19-4d43249f287c'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_2:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_2'
    defaultTaskGroup: '@lu_DefTG_LU_31'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '29eaa66d-a739-4814-87e9-feda85ce0bf1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_123:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_113'
    defaultTaskGroup: '@lu_DefTG_LU_31'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f7236b9e-0e4a-49c2-b203-bfd52206e604'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_6']
  lu_DefTaskRel_LU_3:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_3'
    defaultTaskGroup: '@lu_DefTG_LU_31'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1acd3192-b5c6-4f21-b256-e54ddc406dcd'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_7', '@lu_DefTaskRule_LU_8']
  lu_DefTaskRel_LU_124:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_114'
    defaultTaskGroup: '@lu_DefTG_LU_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '589805e0-1ab3-4ef9-b0b1-c4f025ba10ae'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_125:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_115'
    defaultTaskGroup: '@lu_DefTG_LU_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2710de1f-ce7e-4ec2-a8ac-8a286718eace'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_1']
  lu_DefTaskRel_LU_126:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_116'
    defaultTaskGroup: '@lu_DefTG_LU_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1cb9288f-20f3-4449-b051-f4e62f1d48f1'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_2']
  lu_DefTaskRel_LU_127:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_117'
    defaultTaskGroup: '@lu_DefTG_LU_61'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '86a20d78-2997-49c5-9158-76bc81ec1d83'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_3', '@lu_DefTaskRule_LU_4', '@lu_DefTaskRule_LU_5']
  lu_DefTaskRel_LU_4:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_4'
    defaultTaskGroup: '@lu_DefTG_LU_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c6876a6c-43d6-4bbf-8803-25efbd6947c7'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_173:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_156'
    defaultTaskGroup: '@lu_DefTG_LU_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5b1aedf0-d288-421c-9558-4823605d76ca'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_9']
  lu_DefTaskRel_LU_6:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_6'
    defaultTaskGroup: '@lu_DefTG_LU_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '000c5668-26c1-4d50-8326-6408d6497bad'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_10']
  lu_DefTaskRel_LU_176:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_158'
    defaultTaskGroup: '@lu_DefTG_LU_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '656b0881-3882-4ee7-a566-968033a87685'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_11']
  lu_DefTaskRel_LU_5:
    sequenceNumber: '45'
    defaultTask: '@lu_DefTask_LU_5'
    defaultTaskGroup: '@lu_DefTG_LU_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '55f61858-bfaf-42fd-93a1-0762f45e0f1c'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_12']
  lu_DefTaskRel_LU_8:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_8'
    defaultTaskGroup: '@lu_DefTG_LU_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '08cd4533-221c-44ed-9fe8-b6078d9700c0'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_13', '@lu_DefTaskRule_LU_14', '@lu_DefTaskRule_LU_15', '@lu_DefTaskRule_LU_16', '@lu_DefTaskRule_LU_17', '@lu_DefTaskRule_LU_18', '@lu_DefTaskRule_LU_19', '@lu_DefTaskRule_LU_20']
  lu_DefTaskRel_LU_157:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_141'
    defaultTaskGroup: '@lu_DefTG_LU_72'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ae4a05b5-ce3b-4783-8dd2-63358d50fa23'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_16:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_16'
    defaultTaskGroup: '@lu_DefTG_LU_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '06434c41-8945-4b67-ad04-6e737532fdb2'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_17:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_17'
    defaultTaskGroup: '@lu_DefTG_LU_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e9f9f842-5925-46eb-89f6-5d44cadbcabb'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_21']
  lu_DefTaskRel_LU_18:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_18'
    defaultTaskGroup: '@lu_DefTG_LU_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c8924391-a877-488b-a6e8-9f7c4410620e'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_22', '@lu_DefTaskRule_LU_23']
  lu_DefTaskRel_LU_9:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_9'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4ca431f1-d5b0-4f41-81c6-66ec0b836cc2'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_171:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_154'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5f03e7ec-f80f-4148-b363-5a895462ac16'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_24']
  lu_DefTaskRel_LU_172:
    sequenceNumber: '25'
    defaultTask: '@lu_DefTask_LU_155'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7b3fafd8-88d8-4360-9916-e4616a87f7f7'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_25']
  lu_DefTaskRel_LU_163:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_149'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1f4248ac-0805-40c5-a0cd-d6019d6dc4c4'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_26']
  lu_DefTaskRel_LU_164:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_150'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0ce92ecb-179d-4191-a10b-4f9b7064a041'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_27']
  lu_DefTaskRel_LU_10:
    sequenceNumber: '55'
    defaultTask: '@lu_DefTask_LU_10'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f2fe7a62-2db7-4ed4-abb3-448f48f1e0ba'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_28']
  lu_DefTaskRel_LU_12:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_12'
    defaultTaskGroup: '@lu_DefTG_LU_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '01be130c-d3ee-4cca-94b7-e82ab7950d33'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_29', '@lu_DefTaskRule_LU_30', '@lu_DefTaskRule_LU_31', '@lu_DefTaskRule_LU_32', '@lu_DefTaskRule_LU_33', '@lu_DefTaskRule_LU_34', '@lu_DefTaskRule_LU_35', '@lu_DefTaskRule_LU_36', '@lu_DefTaskRule_LU_37']
  lu_DefTaskRel_LU_13:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_13'
    defaultTaskGroup: '@lu_DefTG_LU_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '35598a99-8465-4e5a-b050-94878ce48dc5'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_158:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_142'
    defaultTaskGroup: '@lu_DefTG_LU_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'db1f6a92-9603-449c-ad1c-baa9e47518dd'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_38']
  lu_DefTaskRel_LU_14:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_14'
    defaultTaskGroup: '@lu_DefTG_LU_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cdea196b-0291-41be-aead-c0a5b08a7746'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_39']
  lu_DefTaskRel_LU_160:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_144'
    defaultTaskGroup: '@lu_DefTG_LU_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'f40477f9-ccbc-4e11-9ee2-932e381abbd4'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_40']
  lu_DefTaskRel_LU_161:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_145'
    defaultTaskGroup: '@lu_DefTG_LU_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '65099545-a3a8-409b-8af4-1069320bfd82'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_41']
  lu_DefTaskRel_LU_15:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_15'
    defaultTaskGroup: '@lu_DefTG_LU_73'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '37bf2ef1-0f2d-4a38-997e-760694635b3d'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_42', '@lu_DefTaskRule_LU_43', '@lu_DefTaskRule_LU_44', '@lu_DefTaskRule_LU_45', '@lu_DefTaskRule_LU_46', '@lu_DefTaskRule_LU_47', '@lu_DefTaskRule_LU_48', '@lu_DefTaskRule_LU_49', '@lu_DefTaskRule_LU_50']
  lu_DefTaskRel_LU_128:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_118'
    defaultTaskGroup: '@lu_DefTG_LU_62'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '56741d4b-b243-4df5-887d-e439091d809f'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_129:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_119'
    defaultTaskGroup: '@lu_DefTG_LU_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '174a0d02-05d4-47a4-afff-4acf25480c94'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_130:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_120'
    defaultTaskGroup: '@lu_DefTG_LU_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6b70519e-334a-4f9d-add7-70c4d6efb940'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_51']
  lu_DefTaskRel_LU_131:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_121'
    defaultTaskGroup: '@lu_DefTG_LU_65'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '08a9a3ac-5be3-482c-b44c-3586dbec7a40'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_52', '@lu_DefTaskRule_LU_53']
  lu_DefTaskRel_LU_19:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_19'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'bbf7df28-3521-4e4f-89f5-fc1e39718a2e'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_197:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_181'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '09388041-540a-4025-94c3-fdcbebd2e0d3'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_21:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_159'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '29e2e03f-5ed3-46f8-bd89-b4b7782e4c70'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_54']
  lu_DefTaskRel_LU_165:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_152'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'aa02cc22-f20a-43a1-b726-4e461c5524b1'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_55']
  lu_DefTaskRel_LU_166:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_153'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '44023d9d-ce11-42ee-bcc6-23ccf15c49bb'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_56']
  lu_DefTaskRel_LU_132:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_130'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5fdc0b41-71a2-4c40-94a9-6afd87c85f16'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_57']
  lu_DefTaskRel_LU_22:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_20'
    defaultTaskGroup: '@lu_DefTG_LU_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '03d86368-ae58-4644-ae54-f53ad2034e0c'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_58', '@lu_DefTaskRule_LU_59', '@lu_DefTaskRule_LU_60', '@lu_DefTaskRule_LU_61', '@lu_DefTaskRule_LU_62']
  lu_DefTaskRel_LU_23:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_21'
    defaultTaskGroup: '@lu_DefTG_LU_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7a7ad52e-adb2-4dfb-bdd3-92428820c9e1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_133:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_122'
    defaultTaskGroup: '@lu_DefTG_LU_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3ddf90dc-b16b-4bbf-94ec-82a046f8c49a'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_66']
  lu_DefTaskRel_LU_24:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_22'
    defaultTaskGroup: '@lu_DefTG_LU_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '48825e33-65e4-44b8-864d-54790a7e107d'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_67']
  lu_DefTaskRel_LU_136:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_132'
    defaultTaskGroup: '@lu_DefTG_LU_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '377a8aab-0ab3-4b2e-bd6b-fa68612214cf'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_68']
  lu_DefTaskRel_LU_137:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_131'
    defaultTaskGroup: '@lu_DefTG_LU_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '222ac5bb-e5fb-443f-b27a-e667f34590c3'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_69']
  lu_DefTaskRel_LU_138:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_134'
    defaultTaskGroup: '@lu_DefTG_LU_63'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4bee1035-810f-474c-8258-8282a7b9d922'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_70', '@lu_DefTaskRule_LU_71', '@lu_DefTaskRule_LU_72', '@lu_DefTaskRule_LU_73', '@lu_DefTaskRule_LU_74', '@lu_DefTaskRule_LU_75', '@lu_DefTaskRule_LU_76', '@lu_DefTaskRule_LU_77', '@lu_DefTaskRule_LU_78']
  lu_DefTaskRel_LU_26:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_24'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'aa7c7b72-2925-4036-bad8-04c8d88e037f'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_167:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_152'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '01f05b7c-5b11-4d98-b817-518232cd4570'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_79']
  lu_DefTaskRel_LU_168:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_153'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6e8cb52d-47e2-4762-b7a6-658de228f8d4'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_80']
  lu_DefTaskRel_LU_29:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_27'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bccab1c7-ff59-41d1-8140-4900e5c17019'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_81']
  lu_DefTaskRel_LU_134:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_239'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ac562eef-4d8b-4af7-b186-1a929a7522f0'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_82']
  lu_DefTaskRel_LU_28:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_26'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '470ee9e7-6e0d-4ef6-adba-264ef35de96f'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_83']
  lu_DefTaskRel_LU_30:
    sequenceNumber: '70'
    defaultTask: '@lu_DefTask_LU_28'
    defaultTaskGroup: '@lu_DefTG_LU_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '477607f0-c009-4d61-9ba7-b2193a18b65f'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_84', '@lu_DefTaskRule_LU_85', '@lu_DefTaskRule_LU_86', '@lu_DefTaskRule_LU_87', '@lu_DefTaskRule_LU_88', '@lu_DefTaskRule_LU_89', '@lu_DefTaskRule_LU_90', '@lu_DefTaskRule_LU_91', '@lu_DefTaskRule_LU_92']
  lu_DefTaskRel_LU_139:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_123'
    defaultTaskGroup: '@lu_DefTG_LU_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '650ebf1a-bb29-4a74-bd79-1015ac6e1d44'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_140:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_124'
    defaultTaskGroup: '@lu_DefTG_LU_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '80200ce6-230f-4dd0-928c-9fb1024fef70'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_93']
  lu_DefTaskRel_LU_141:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_125'
    defaultTaskGroup: '@lu_DefTG_LU_66'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '49962996-2ce5-47d4-bef1-f8f47d8f55df'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_94', '@lu_DefTaskRule_LU_95']
  lu_DefTaskRel_LU_142:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_126'
    defaultTaskGroup: '@lu_DefTG_LU_67'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '83707090-b6a5-4064-b3ef-2a4ff11ed2f1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_143:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_127'
    defaultTaskGroup: '@lu_DefTG_LU_67'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '79923ca0-ea36-4496-b17f-74e2c8ef155d'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_96']
  lu_DefTaskRel_LU_144:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_128'
    defaultTaskGroup: '@lu_DefTG_LU_67'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ca81790e-4e48-46d5-bae9-0c12ccf047da'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_97']
  lu_DefTaskRel_LU_145:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_129'
    defaultTaskGroup: '@lu_DefTG_LU_67'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ca08f381-3f27-45c2-b281-f1f6d1916a13'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_98', '@lu_DefTaskRule_LU_99', '@lu_DefTaskRule_LU_100']
  lu_DefTaskRel_LU_146:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_135'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '6a27225f-e9c0-47d1-a4db-8e35246621ce'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_169:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_152'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '699248b6-202d-425a-99c4-a13c20babb42'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_101']
  lu_DefTaskRel_LU_170:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_153'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '10737324-98fd-444e-b79c-0e928770372f'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_102']
  lu_DefTaskRel_LU_149:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_27'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4fe13454-b239-4e6c-af4f-1057a861a2b4'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_103']
  lu_DefTaskRel_LU_150:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_130'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4408b09d-c6b9-44f0-8f7e-022e271dbd6b'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_104']
  lu_DefTaskRel_LU_151:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_151'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2f73cf4a-ffd1-4b3c-873c-e32a37505b71'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_105']
  lu_DefTaskRel_LU_148:
    sequenceNumber: '70'
    defaultTask: '@lu_DefTask_LU_26'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2ac5f8ac-c006-41f1-a274-66c1b49847d4'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_106']
  lu_DefTaskRel_LU_152:
    sequenceNumber: '80'
    defaultTask: '@lu_DefTask_LU_136'
    defaultTaskGroup: '@lu_DefTG_LU_68'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2acd3c33-ba12-455c-a4aa-78532456601b'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_107', '@lu_DefTaskRule_LU_108', '@lu_DefTaskRule_LU_109', '@lu_DefTaskRule_LU_110', '@lu_DefTaskRule_LU_111', '@lu_DefTaskRule_LU_112', '@lu_DefTaskRule_LU_113', '@lu_DefTaskRule_LU_114', '@lu_DefTaskRule_LU_115', '@lu_DefTaskRule_LU_116']
  lu_DefTaskRel_LU_154:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_138'
    defaultTaskGroup: '@lu_DefTG_LU_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c27abaf3-1656-421c-afd9-2e91dda4c9d6'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_153:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_137'
    defaultTaskGroup: '@lu_DefTG_LU_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '984d5dcb-a362-4e57-9c3a-84eb157b5e02'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_117']
  lu_DefTaskRel_LU_155:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_139'
    defaultTaskGroup: '@lu_DefTG_LU_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '09f53457-13fb-46d9-a7c0-ff696f1e0276'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_118']
  lu_DefTaskRel_LU_256:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_132'
    defaultTaskGroup: '@lu_DefTG_LU_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'b02828f8-f775-496d-bcb3-8a0d50ede502'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_119']
  lu_DefTaskRel_LU_257:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_131'
    defaultTaskGroup: '@lu_DefTG_LU_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '7b1c0274-7814-4ce0-b659-1d989849984f'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_120']
  lu_DefTaskRel_LU_156:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_140'
    defaultTaskGroup: '@lu_DefTG_LU_69'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c905aace-e853-4ecb-9aaf-b404bc77f196'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_121', '@lu_DefTaskRule_LU_122', '@lu_DefTaskRule_LU_123', '@lu_DefTaskRule_LU_124', '@lu_DefTaskRule_LU_125', '@lu_DefTaskRule_LU_126', '@lu_DefTaskRule_LU_127', '@lu_DefTaskRule_LU_128', '@lu_DefTaskRule_LU_129']
  lu_DefTaskRel_LU_46:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_44'
    defaultTaskGroup: '@lu_DefTG_LU_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '23797fca-d1eb-44e2-847d-d580fbab01cd'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_47:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_52'
    defaultTaskGroup: '@lu_DefTG_LU_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '07de32e8-a07f-42dc-8caf-65e33f39d63f'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_48:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_53'
    defaultTaskGroup: '@lu_DefTG_LU_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9f98c499-78f4-41b7-9e50-0489719d3884'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_130']
  lu_DefTaskRel_LU_49:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_54'
    defaultTaskGroup: '@lu_DefTG_LU_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '86cf206b-fff4-4a99-bb18-21fd89644e00'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_131', '@lu_DefTaskRule_LU_132']
  lu_DefTaskRel_LU_51:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_45'
    defaultTaskGroup: '@lu_DefTG_LU_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b746ed4b-5e1e-4e1c-aa91-e0e06e6e8f7a'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_198:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_182'
    defaultTaskGroup: '@lu_DefTG_LU_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'a32fd3a8-9d47-455c-ac4c-d5672ba9b0ce'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_174:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_157'
    defaultTaskGroup: '@lu_DefTG_LU_46'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd012042e-aaa5-4286-bac7-80da2f250ef1'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_133']
  lu_DefTaskRel_LU_53:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_147'
    defaultTaskGroup: '@lu_DefTG_LU_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a3f96017-0443-4b7b-b5bf-edabef36e557'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_134']
  lu_DefTaskRel_LU_54:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_46'
    defaultTaskGroup: '@lu_DefTG_LU_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '497021e8-9081-448c-b0c0-b19480215176'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_135', '@lu_DefTaskRule_LU_140', '@lu_DefTaskRule_LU_141']
  lu_DefTaskRel_LU_55:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_60'
    defaultTaskGroup: '@lu_DefTG_LU_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ab5c770c-2acf-4364-9589-343923c14625'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_199:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_177'
    defaultTaskGroup: '@lu_DefTG_LU_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '34eeda61-8209-4baa-940b-9dd0e1bf1d48'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_142']
  lu_DefTaskRel_LU_56:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_63'
    defaultTaskGroup: '@lu_DefTG_LU_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '000a6308-5ddd-4905-baa8-f3ea25358f0a'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_143']
  lu_DefTaskRel_LU_175:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_157'
    defaultTaskGroup: '@lu_DefTG_LU_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8f8ed4d4-2151-4bb9-83e9-c5a3c348e402'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_144']
  lu_DefTaskRel_LU_57:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_148'
    defaultTaskGroup: '@lu_DefTG_LU_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e0f46fc6-057c-4b60-bfc3-f638b61e28ca'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_145']
  lu_DefTaskRel_LU_58:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_61'
    defaultTaskGroup: '@lu_DefTG_LU_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '51fd1d4b-729f-43ab-a372-e914959aed69'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_146', '@lu_DefTaskRule_LU_147', '@lu_DefTaskRule_LU_148', '@lu_DefTaskRule_LU_149', '@lu_DefTaskRule_LU_150', '@lu_DefTaskRule_LU_151', '@lu_DefTaskRule_LU_152', '@lu_DefTaskRule_LU_153']
  lu_DefTaskRel_LU_96:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_82'
    defaultTaskGroup: '@lu_DefTG_LU_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5e1a0f5d-5687-48cb-acef-8d6877bf79a3'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_97:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_83'
    defaultTaskGroup: '@lu_DefTG_LU_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b84aa516-da71-4b80-b3d6-83cc1b927c72'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_98:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_84'
    defaultTaskGroup: '@lu_DefTG_LU_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7411205e-0d7b-450c-948c-c88841aa1a5b'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_99:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_85'
    defaultTaskGroup: '@lu_DefTG_LU_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5af98b3d-ff70-42ac-94f6-53335719b211'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_100:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_86'
    defaultTaskGroup: '@lu_DefTG_LU_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a179831b-ab89-4645-9688-3edd1cf98fd1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_101:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_87'
    defaultTaskGroup: '@lu_DefTG_LU_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e22d77b1-eb5c-4f22-87c0-a548ce1d0181'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_102:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_88'
    defaultTaskGroup: '@lu_DefTG_LU_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ec333196-e801-4d91-a03c-42bf3c6374fa'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_103:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_89'
    defaultTaskGroup: '@lu_DefTG_LU_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '10234fa3-0b89-4f86-a420-64fff24fd1cf'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_104:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_90'
    defaultTaskGroup: '@lu_DefTG_LU_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b0b000d6-e34a-45e3-b078-9dcf73c0d5d4'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_105:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_91'
    defaultTaskGroup: '@lu_DefTG_LU_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5b19af23-1236-444d-9456-9a50b9a75ece'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_106:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_92'
    defaultTaskGroup: '@lu_DefTG_LU_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '801ce9d5-45cb-40bf-9db2-234889f113a3'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_107:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_93'
    defaultTaskGroup: '@lu_DefTG_LU_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2e729555-cd17-4d94-8b8a-4e002b5b0d58'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_108:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_94'
    defaultTaskGroup: '@lu_DefTG_LU_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8cb98861-da8c-42ba-979e-cc0244cb1f86'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_109:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_95'
    defaultTaskGroup: '@lu_DefTG_LU_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd4b25249-7867-46ed-b29b-28faa0cdd6c9'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_110:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_104'
    defaultTaskGroup: '@lu_DefTG_LU_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c7506485-6e51-48eb-be83-59d093feff3f'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_111:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_97'
    defaultTaskGroup: '@lu_DefTG_LU_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '21da0f5a-e113-4ad7-8fd3-2f0dcb58b125'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_112:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_98'
    defaultTaskGroup: '@lu_DefTG_LU_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2f320560-3906-4681-a669-701992713a00'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_113:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_99'
    defaultTaskGroup: '@lu_DefTG_LU_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5bd0d78e-c58d-41c3-bc5e-e0303cc587c7'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_114:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_100'
    defaultTaskGroup: '@lu_DefTG_LU_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '25989a3e-2f81-4ff9-b675-852dd28007dc'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_115:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_101'
    defaultTaskGroup: '@lu_DefTG_LU_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '692d8b3a-eb25-421b-bdee-42005692f19c'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_116:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_102'
    defaultTaskGroup: '@lu_DefTG_LU_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ee45267f-b5e8-4f38-a709-e04042c8ebbb'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_120:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_106'
    defaultTaskGroup: '@lu_DefTG_LU_59'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e44c2a63-451d-4d83-9c5d-f53f7276ec4d'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_121:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_107'
    defaultTaskGroup: '@lu_DefTG_LU_59'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5242f039-9ea4-4ad8-8ccf-f9a634b263ff'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_122:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_108'
    defaultTaskGroup: '@lu_DefTG_LU_60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cfeaaa95-d62f-4559-a4d3-ae269ed692a6'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_177:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_160'
    defaultTaskGroup: '@lu_DefTG_LU_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '140cd08d-a658-4015-8c66-5974c016f823'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_178:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_161'
    defaultTaskGroup: '@lu_DefTG_LU_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8eb1b048-51e8-4baf-9198-57219781e2e3'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_179:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_162'
    defaultTaskGroup: '@lu_DefTG_LU_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1134fbc4-aae8-48f6-92c5-b18ceb62ba59'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_180:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_163'
    defaultTaskGroup: '@lu_DefTG_LU_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f4833e13-ddc9-4f5b-b65f-96a156a992ad'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_181:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_164'
    defaultTaskGroup: '@lu_DefTG_LU_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4007d66d-5e59-4d72-9bff-3c82a90735b5'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_182:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_165'
    defaultTaskGroup: '@lu_DefTG_LU_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '598e0408-3c79-41b7-a4a4-3578047afaac'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_183:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_166'
    defaultTaskGroup: '@lu_DefTG_LU_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '35f9edd5-13ab-4e6a-876b-15b45f544754'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_184:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_167'
    defaultTaskGroup: '@lu_DefTG_LU_21'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '48ecb92a-0354-4851-b289-560f49822fcb'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_185:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_168'
    defaultTaskGroup: '@lu_DefTG_LU_22'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '6f9b8b45-818d-4843-b03b-040e11b2869f'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_186:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_169'
    defaultTaskGroup: '@lu_DefTG_LU_23'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '60f110ec-5b38-4cce-a2a1-ce9f437396cc'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_187:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_170'
    defaultTaskGroup: '@lu_DefTG_LU_24'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8beceeb2-4a30-42d0-9b6d-baadc16f1948'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_188:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_171'
    defaultTaskGroup: '@lu_DefTG_LU_25'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cc58e62b-f5e5-4f64-b5fc-482084212060'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_189:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_172'
    defaultTaskGroup: '@lu_DefTG_LU_26'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a22b01c6-99ff-4f57-a70a-1c93f3ae3dda'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_190:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_173'
    defaultTaskGroup: '@lu_DefTG_LU_27'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e95ff29c-e0b4-4a65-af92-924adf57d2b5'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_191:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_174'
    defaultTaskGroup: '@lu_DefTG_LU_28'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cfd4ce2b-dc51-4d26-a91d-9a559b5b1a6b'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_192:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_175'
    defaultTaskGroup: '@lu_DefTG_LU_29'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '6c4e2abe-7455-420f-a520-214e3478c21d'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_196:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_176'
    defaultTaskGroup: '@lu_DefTG_LU_75'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e5863107-5704-43e5-b446-80fc351499bb'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_200:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_183'
    defaultTaskGroup: '@lu_DefTG_LU_76'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd9f0143f-c901-4acf-ba9b-ca0515546d01'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_201:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_184'
    defaultTaskGroup: '@lu_DefTG_LU_77'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5004e18a-34c2-4a7d-912b-179d2e2e814c'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_202:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_185'
    defaultTaskGroup: '@lu_DefTG_LU_77'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7c5cd159-634b-47c2-b827-a53e1f0013b0'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_154']
  lu_DefTaskRel_LU_204:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_187'
    defaultTaskGroup: '@lu_DefTG_LU_77'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9b5d8e60-79c7-4168-a58c-f9001eb60e93'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_155', '@lu_DefTaskRule_LU_156']
  lu_DefTaskRel_LU_205:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_188'
    defaultTaskGroup: '@lu_DefTG_LU_78'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'db72d371-dd69-4ca3-a568-5193efe5f116'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_206:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_189'
    defaultTaskGroup: '@lu_DefTG_LU_78'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '63e0db85-c1e8-4361-ba7b-ab36a61f53d4'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_157']
  lu_DefTaskRel_LU_207:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_190'
    defaultTaskGroup: '@lu_DefTG_LU_78'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '65871357-a9cd-42e6-8486-58ced7e71f97'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_158']
  lu_DefTaskRel_LU_209:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_192'
    defaultTaskGroup: '@lu_DefTG_LU_78'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5030d82f-529e-4672-9392-a81bd1ac0b7c'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_159', '@lu_DefTaskRule_LU_160', '@lu_DefTaskRule_LU_161']
  lu_DefTaskRel_LU_210:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_193'
    defaultTaskGroup: '@lu_DefTG_LU_79'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '985fd45c-0c47-4a08-b2fe-2ebcfd9cdf40'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_211:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_194'
    defaultTaskGroup: '@lu_DefTG_LU_79'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0f09b107-527d-4d82-b3ce-82cf829d6412'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_162']
  lu_DefTaskRel_LU_212:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_195'
    defaultTaskGroup: '@lu_DefTG_LU_79'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8de795bc-3f6a-4a48-8f78-263fce7e7ed9'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_163']
  lu_DefTaskRel_LU_213:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_196'
    defaultTaskGroup: '@lu_DefTG_LU_79'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6d1c1f3e-6ee4-4a77-9c16-2271d38df92d'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_164']
  lu_DefTaskRel_LU_214:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_197'
    defaultTaskGroup: '@lu_DefTG_LU_79'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '07dc410e-6863-4caf-9f54-f8ebf8086b53'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_165', '@lu_DefTaskRule_LU_166', '@lu_DefTaskRule_LU_167', '@lu_DefTaskRule_LU_168', '@lu_DefTaskRule_LU_169', '@lu_DefTaskRule_LU_170', '@lu_DefTaskRule_LU_171']
  lu_DefTaskRel_LU_215:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_198'
    defaultTaskGroup: '@lu_DefTG_LU_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e6abfb33-9a66-499f-b11f-d42ea6b081d1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_216:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_199'
    defaultTaskGroup: '@lu_DefTG_LU_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6c125093-abd2-4a50-95df-8d3f277968c9'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_172']
  lu_DefTaskRel_LU_217:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_200'
    defaultTaskGroup: '@lu_DefTG_LU_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c7c33b9d-5403-4eab-b0f5-402033a409aa'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_173']
  lu_DefTaskRel_LU_220:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_203'
    defaultTaskGroup: '@lu_DefTG_LU_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: '9da9dc31-ba5e-45d4-9a23-ed074215c201'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_174']
  lu_DefTaskRel_LU_221:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_204'
    defaultTaskGroup: '@lu_DefTG_LU_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'e1e65d47-372d-4614-b49d-3fefd8074587'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_175']
  lu_DefTaskRel_LU_223:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_206'
    defaultTaskGroup: '@lu_DefTG_LU_80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '017b255a-0426-4203-8cf4-e593dd414dfb'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_176', '@lu_DefTaskRule_LU_177', '@lu_DefTaskRule_LU_178', '@lu_DefTaskRule_LU_179', '@lu_DefTaskRule_LU_180', '@lu_DefTaskRule_LU_181', '@lu_DefTaskRule_LU_182', '@lu_DefTaskRule_LU_183']
  lu_DefTaskRel_LU_224:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_207'
    defaultTaskGroup: '@lu_DefTG_LU_83'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3199b052-4e7c-428f-b1c3-c13781d1d8b2'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_225:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_208'
    defaultTaskGroup: '@lu_DefTG_LU_84'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd5bbf81d-ba3b-4891-bb76-4071d11d5285'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_226:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_209'
    defaultTaskGroup: '@lu_DefTG_LU_84'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3394f110-cf00-4eb6-ab79-e26a0938976d'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_184']
  lu_DefTaskRel_LU_227:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_210'
    defaultTaskGroup: '@lu_DefTG_LU_84'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd942efe9-dd1c-49ce-92c3-afff0b8f2314'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_185', '@lu_DefTaskRule_LU_186']
  lu_DefTaskRel_LU_228:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_211'
    defaultTaskGroup: '@lu_DefTG_LU_85'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '72bb5cfa-46f5-408e-92da-a6a5daa80aca'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_229:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_212'
    defaultTaskGroup: '@lu_DefTG_LU_85'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3ff758cf-6cbe-42f1-8b37-5bc647de3f39'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_187']
  lu_DefTaskRel_LU_230:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_213'
    defaultTaskGroup: '@lu_DefTG_LU_85'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0088eaf2-ae42-485e-b059-6893f99a30a9'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_188']
  lu_DefTaskRel_LU_231:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_214'
    defaultTaskGroup: '@lu_DefTG_LU_85'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e4789fdf-82cb-47e3-8616-f6949b833041'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_189']
  lu_DefTaskRel_LU_232:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_215'
    defaultTaskGroup: '@lu_DefTG_LU_85'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ad86e6fe-254d-47b3-a41e-8e469f36dc14'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_190']
  lu_DefTaskRel_LU_233:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_216'
    defaultTaskGroup: '@lu_DefTG_LU_85'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '1024d121-fca2-4221-9973-95e271ba344e'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_191', '@lu_DefTaskRule_LU_192', '@lu_DefTaskRule_LU_193', '@lu_DefTaskRule_LU_194', '@lu_DefTaskRule_LU_195', '@lu_DefTaskRule_LU_196', '@lu_DefTaskRule_LU_197', '@lu_DefTaskRule_LU_198']
  lu_DefTaskRel_LU_234:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_217'
    defaultTaskGroup: '@lu_DefTG_LU_86'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f8e7e4ab-b8e2-465f-8f2b-38378cee2a79'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_235:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_218'
    defaultTaskGroup: '@lu_DefTG_LU_86'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '45733631-ccb5-4526-acd2-2b0b1e346d83'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_199']
  lu_DefTaskRel_LU_236:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_219'
    defaultTaskGroup: '@lu_DefTG_LU_86'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '75daa1af-2f90-4f5c-bd8c-e66be3ab8d3a'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_200']
  lu_DefTaskRel_LU_237:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_220'
    defaultTaskGroup: '@lu_DefTG_LU_86'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4a8b9987-b902-4d71-98ed-ccdc514826d2'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_201']
  lu_DefTaskRel_LU_238:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_221'
    defaultTaskGroup: '@lu_DefTG_LU_86'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c1bd4903-056f-4e7c-adce-d7eb065ecb99'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_202']
  lu_DefTaskRel_LU_239:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_222'
    defaultTaskGroup: '@lu_DefTG_LU_86'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '10faf16f-ca46-4058-a5dc-278eb0f013ce'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_203', '@lu_DefTaskRule_LU_204', '@lu_DefTaskRule_LU_205', '@lu_DefTaskRule_LU_206', '@lu_DefTaskRule_LU_207', '@lu_DefTaskRule_LU_208', '@lu_DefTaskRule_LU_209', '@lu_DefTaskRule_LU_210']
  lu_DefTaskRel_LU_240:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_223'
    defaultTaskGroup: '@lu_DefTG_LU_87'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '30a0866a-e2ea-4998-afeb-d9a5f7f3aa2a'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_241:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_224'
    defaultTaskGroup: '@lu_DefTG_LU_87'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '39a9a774-51be-402a-b15f-9e14673b4d81'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_211']
  lu_DefTaskRel_LU_242:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_225'
    defaultTaskGroup: '@lu_DefTG_LU_87'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '60b4fbc0-2292-4aa0-a824-f42d54b48e92'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_212', '@lu_DefTaskRule_LU_213']
  lu_DefTaskRel_LU_243:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_226'
    defaultTaskGroup: '@lu_DefTG_LU_88'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2c1a117b-17fd-485a-b393-fa92acde1802'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_244:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_227'
    defaultTaskGroup: '@lu_DefTG_LU_88'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '52288041-a147-421c-b3a0-5f1aad8861d8'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_214']
  lu_DefTaskRel_LU_245:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_228'
    defaultTaskGroup: '@lu_DefTG_LU_88'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b09126b2-5b8a-4f38-8df6-02b661e50aec'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_215']
  lu_DefTaskRel_LU_246:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_229'
    defaultTaskGroup: '@lu_DefTG_LU_88'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '892ec3a3-f922-4b88-bbc3-b62fd6783713'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_216']
  lu_DefTaskRel_LU_247:
    sequenceNumber: '50'
    defaultTask: '@lu_DefTask_LU_230'
    defaultTaskGroup: '@lu_DefTG_LU_88'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '26efa11a-cd68-4e6e-b16a-3574949a375e'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_217']
  lu_DefTaskRel_LU_248:
    sequenceNumber: '60'
    defaultTask: '@lu_DefTask_LU_231'
    defaultTaskGroup: '@lu_DefTG_LU_88'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6712b8a1-dd74-46d8-83c6-15e758dcef4c'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_218', '@lu_DefTaskRule_LU_219', '@lu_DefTaskRule_LU_220', '@lu_DefTaskRule_LU_221', '@lu_DefTaskRule_LU_222', '@lu_DefTaskRule_LU_223', '@lu_DefTaskRule_LU_224', '@lu_DefTaskRule_LU_225']
  lu_DefTaskRel_LU_249:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_232'
    defaultTaskGroup: '@lu_DefTG_LU_89'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a11b0d69-2063-4b78-b977-7c04dab40f32'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_250:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_233'
    defaultTaskGroup: '@lu_DefTG_LU_89'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ee98baa2-7ba2-4c4d-be40-eb90222a3487'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_226']
  lu_DefTaskRel_LU_251:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_234'
    defaultTaskGroup: '@lu_DefTG_LU_89'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f6a631d0-3e8c-4b7c-89be-ea56d12c9a1c'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_227']
  lu_DefTaskRel_LU_252:
    sequenceNumber: '40'
    defaultTask: '@lu_DefTask_LU_235'
    defaultTaskGroup: '@lu_DefTG_LU_89'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e28e6bb6-34a7-4f74-9ab6-e4a6e3bcbc9f'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_228', '@lu_DefTaskRule_LU_229', '@lu_DefTaskRule_LU_230', '@lu_DefTaskRule_LU_231', '@lu_DefTaskRule_LU_232', '@lu_DefTaskRule_LU_233', '@lu_DefTaskRule_LU_234']
  lu_DefTaskRel_LU_253:
    sequenceNumber: '10'
    defaultTask: '@lu_DefTask_LU_236'
    defaultTaskGroup: '@lu_DefTG_LU_90'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '14543cc8-a092-4757-90b1-154c38f51f97'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTaskRel_LU_254:
    sequenceNumber: '20'
    defaultTask: '@lu_DefTask_LU_237'
    defaultTaskGroup: '@lu_DefTG_LU_90'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ebbfe411-ffda-40d9-988f-3b7347ad694a'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_235']
  lu_DefTaskRel_LU_255:
    sequenceNumber: '30'
    defaultTask: '@lu_DefTask_LU_238'
    defaultTaskGroup: '@lu_DefTG_LU_90'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7bf4356f-bcfb-40a1-9c1c-ed763d2b8808'
    tenant: '<getLuxembourgTenant()>'
    rules: ['@lu_DefTaskRule_LU_236', '@lu_DefTaskRule_LU_237']
