App\Domain\Entity\DefaultTermination:
  pz_DefTerm_1:
    text: '{{Tour_too_long_no_emptying}}'
    externalId: '215'
    id: '2bbae082-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_2:
    text: '{{Vehicle_defective}}'
    externalId: '220'
    id: '2bbae17c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_3:
    text: '{{Driver_sick}}'
    externalId: '225'
    id: '2bbae262-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_4:
    text: '{{Transport_interruption}}'
    externalId: '290'
    id: '2bbae398-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_5:
    text: '{{Order_returned_to_dispatch}}'
    externalId: '350'
    id: '2bbae492-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_6:
    text: '{{Customer_not_on_site}}'
    externalId: '505'
    id: 'c114a606-443e-442a-bf82-7307009a27d1'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_7:
    text: '{{Container_locked}}'
    externalId: '510'
    id: 'a92f77d2-7307-4eae-bfb6-773f35f2047f'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_8:
    text: '{{Container_too_heavy/overfilled}}'
    externalId: '250'
    id: '2bbaea46-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_9:
    text: '{{Incorrect_filling_-_No_emptying}}'
    externalId: '255'
    id: '2bbaeb36-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_10:
    text: '{{Container_defective_-_No_emptying}}'
    externalId: '265'
    id: '2bbaec30-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_11:
    text: '{{Container_fallen_into_vehicle}}'
    externalId: '305'
    id: '2bbaed20-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_12:
    text: '{{container_empty}}'
    externalId: '245'
    id: '2bbaee24-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_13:
    text: '{{Transport_interruption_without_scan}}'
    externalId: '290'
    id: '7842fc79-9d8a-4221-b5f6-3aa4915adccf'
    tenant: '<getGermanyTenant()>'
  pz_DefTerm_14:
    text: '{{Incorrectly_planned_too_many_containers}}'
    externalId: '260'
    id: '32afcee5-feda-4257-9f55-d60870ad837c'
    tenant: '<getGermanyTenant()>'
