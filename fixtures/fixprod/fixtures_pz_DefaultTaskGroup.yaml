App\Domain\Entity\DefaultTaskGroup:
  pz_DefTG_1:
    defaultInterruption: '@pz_DefInt_1'
    title: '{{Break}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb1af2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_2:
    defaultInterruption: '@pz_DefInt_2'
    title: '{{Driver_meeting}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb1c00-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_3:
    defaultInterruption: '@pz_DefInt_3'
    title: '{{Other}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb1f20-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_4:
    defaultInterruption: '@pz_DefInt_4'
    title: '{{Waiting_time}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb2038-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_5:
    defaultInterruption: '@pz_DefInt_5'
    title: '{{Vehicle_inspection}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb2128-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_6:
    defaultInterruption: '@pz_DefInt_6'
    title: '{{Workshop}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb2218-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_7:
    defaultInterruption: '@pz_DefInt_7'
    title: '{{Refuel}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb2308-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_8:
    defaultInterruption: '@pz_DefInt_8'
    title: '{{Traffic_jam}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb242a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_9:
    defaultInterruption: '@pz_DefInt_9'
    title: '{{Vehicle_care}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb254c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_10:
    tourDataConfig: '@pz_TourConf_2'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '2bbb2664-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_11:
    defaultInterruption: '@pz_DefInt_11'
    title: '{{Way_home}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb292a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_12:
    defaultInterruption: '@pz_DefInt_12'
    title: '{{Pre-loading}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '2bbb2a4c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_13:
    defaultNote: '@pz_DefNote_1'
    title: '{{Consultation_dispo}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb2b64-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_14:
    defaultNote: '@pz_DefNote_2'
    title: '{{Consultation_distribution}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb2c7c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_15:
    defaultNote: '@pz_DefNote_3'
    title: '{{Faulty_filling_Emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb2d8a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_16:
    defaultNote: '@pz_DefNote_4'
    title: '{{Container_UVV_required}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb2eca-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_17:
    defaultNote: '@pz_DefNote_5'
    title: '{{Container_defective_-_emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb328a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_18:
    defaultNote: '@pz_DefNote_6'
    title: '{{Technical_fault_-_no_weight}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb33ac-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_19:
    defaultNote: '@pz_DefNote_7'
    title: '{{Dif_DisposalSite}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb34ba-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_20:
    defaultNote: '@pz_DefNote_8'
    title: '{{Container_picked_up_empty}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb35be-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_21:
    defaultTermination: '@pz_DefTerm_1'
    title: '{{Tour_too_long_no_emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb36c2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_22:
    defaultTermination: '@pz_DefTerm_2'
    title: '{{Vehicle_defective}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb37d0-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_23:
    defaultTermination: '@pz_DefTerm_3'
    title: '{{Driver_sick}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb38d4-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_24:
    defaultTermination: '@pz_DefTerm_4'
    title: '{{Transport_interruption}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb3c9e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_25:
    defaultTermination: '@pz_DefTerm_5'
    title: '{{Order_returned_to_dispatch}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb3dc0-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_26:
    defaultTermination: '@pz_DefTerm_6'
    title: '{{Customer_not_on_site}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb3ece-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_27:
    defaultTermination: '@pz_DefTerm_7'
    title: '{{Container_locked}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb3fd2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_28:
    defaultTermination: '@pz_DefTerm_8'
    title: '{{Container_too_heavy/overfilled}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb4126-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_29:
    defaultTermination: '@pz_DefTerm_9'
    title: '{{Incorrect_filling_-_No_emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb4284-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_30:
    defaultTermination: '@pz_DefTerm_10'
    title: '{{Container_defective_-_No_emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb4392-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_31:
    defaultNote: '@pz_DefNote_19'
    title: '{{Container_fallen_into_vehicle}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2bbb4856-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_32:
    defaultTermination: '@pz_DefTerm_12'
    title: '{{container_empty}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '2bbb498c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_33:
    orderTypeConfig: '@pz_OrderConf_1'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '2bbb4a90-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_34:
    orderTypeConfig: '@pz_OrderConf_1'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb4b9e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_1']
    additionalInformationItems: ['@pz_DefAddInfo_1']
  pz_DefTG_35:
    orderTypeConfig: '@pz_OrderConf_1'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb4cac-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_2', '@pz_DefTGR_3']
    additionalInformationItems: ['@pz_DefAddInfo_2']
  pz_DefTG_36:
    orderTypeConfig: '@pz_OrderConf_2'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb4dc4-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_DefAddInfo_3']
  pz_DefTG_37:
    orderTypeConfig: '@pz_OrderConf_2'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb4f22-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_4']
    additionalInformationItems: ['@pz_DefAddInfo_4']
  pz_DefTG_38:
    orderTypeConfig: '@pz_OrderConf_2'
    title: '{{Container_storage}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb504e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_5']
    additionalInformationItems: ['@pz_DefAddInfo_5']
  pz_DefTG_39:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '2bbb53a0-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_40:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Customer}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb54c2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_6']
    additionalInformationItems: ['@pz_DefAddInfo_6']
  pz_DefTG_41:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb55c6-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_7']
    additionalInformationItems: ['@pz_DefAddInfo_7']
  pz_DefTG_42:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb56fc-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_8']
    additionalInformationItems: ['@pz_DefAddInfo_8']
  pz_DefTG_43:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Container_storage}}'
    sequenceNumber: '50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb5864-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_9']
    additionalInformationItems: ['@pz_DefAddInfo_9']
  pz_DefTG_44:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Customer}}'
    sequenceNumber: '60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb59a4-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_10', '@pz_DefTGR_11']
    additionalInformationItems: ['@pz_DefAddInfo_10']
  pz_DefTG_45:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '70'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb5b48-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_12']
    additionalInformationItems: ['@pz_DefAddInfo_11']
  pz_DefTG_46:
    orderTypeConfig: '@pz_OrderConf_3'
    title: '{{Container_storage}}'
    sequenceNumber: '80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb5ec2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_13']
    additionalInformationItems: ['@pz_DefAddInfo_12']
  pz_DefTG_47:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '2bbb5fee-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_48:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Customer}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb60fc-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_14']
    additionalInformationItems: ['@pz_DefAddInfo_13']
  pz_DefTG_49:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb65fc-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_15']
    additionalInformationItems: ['@pz_DefAddInfo_14']
  pz_DefTG_50:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb6732-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_16']
    additionalInformationItems: ['@pz_DefAddInfo_15']
  pz_DefTG_51:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Container_storage}}'
    sequenceNumber: '50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb684a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_17']
    additionalInformationItems: ['@pz_DefAddInfo_16']
  pz_DefTG_52:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Customer}}'
    sequenceNumber: '60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb6958-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_18', '@pz_DefTGR_19']
    additionalInformationItems: ['@pz_DefAddInfo_17']
  pz_DefTG_53:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Container_storage}}'
    sequenceNumber: '70'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb6a5c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_20']
    additionalInformationItems: ['@pz_DefAddInfo_18']
  pz_DefTG_54:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Customer}}'
    sequenceNumber: '80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb6dc2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_21', '@pz_DefTGR_22']
    additionalInformationItems: ['@pz_DefAddInfo_19']
  pz_DefTG_55:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '90'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb6f3e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_23', '@pz_DefTGR_24']
    additionalInformationItems: ['@pz_DefAddInfo_20']
  pz_DefTG_56:
    orderTypeConfig: '@pz_OrderConf_4'
    title: '{{Container_storage}}'
    sequenceNumber: '100'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb706a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_25']
    additionalInformationItems: ['@pz_DefAddInfo_21']
  pz_DefTG_57:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '2bbb7196-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_58:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Customer}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb72a4-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_26']
    additionalInformationItems: ['@pz_DefAddInfo_22']
  pz_DefTG_59:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb73da-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_27']
    additionalInformationItems: ['@pz_DefAddInfo_23']
  pz_DefTG_60:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb7cb8-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_28']
    additionalInformationItems: ['@pz_DefAddInfo_24']
  pz_DefTG_61:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Container_storage}}'
    sequenceNumber: '50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb7e20-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_29']
    additionalInformationItems: ['@pz_DefAddInfo_25']
  pz_DefTG_62:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Customer}}'
    sequenceNumber: '60'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb7fb0-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_30', '@pz_DefTGR_31']
    additionalInformationItems: ['@pz_DefAddInfo_26']
  pz_DefTG_63:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Container_storage}}'
    sequenceNumber: '70'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb82f8-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_32']
    additionalInformationItems: ['@pz_DefAddInfo_27']
  pz_DefTG_64:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Customer}}'
    sequenceNumber: '80'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '2bbb847e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_33', '@pz_DefTGR_34']
    additionalInformationItems: ['@pz_DefAddInfo_28']
  pz_DefTG_65:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '90'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '2bbb8780-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_35', '@pz_DefTGR_36']
    additionalInformationItems: ['@pz_DefAddInfo_29']
  pz_DefTG_66:
    orderTypeConfig: '@pz_OrderConf_5'
    title: '{{Container_storage}}'
    sequenceNumber: '100'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: '2bbb88ac-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_37']
    additionalInformationItems: ['@pz_DefAddInfo_30']
  pz_DefTG_67:
    tourDataConfig: '@pz_TourConf_1'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '2bbb89ba-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_68:
    equipmentConfig: '@pz_EquipmentConf_1'
    title: '{{Vehicle}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::EQUIPMENT)>'
    id: '2bbb8ad2-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_69:
    tourDataConfig: '@pz_TourConf_1'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '2bbb8bea-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_70:
    orderTypeConfig: '@pz_OrderConf_6'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '0afa02ef-6bbe-483f-a94e-5417ac6e8cc4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_71:
    orderTypeConfig: '@pz_OrderConf_6'
    title: '{{Container_storage}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: 'a7e02ea6-f63e-4571-a9d1-a8e9bafea8b1'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_38']
  pz_DefTG_72:
    orderTypeConfig: '@pz_OrderConf_6'
    title: '{{Loading_location}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'ec9a0ccd-0861-44c0-b007-ea8f5fa27528'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_39', '@pz_DefTGR_40']
  pz_DefTG_73:
    orderTypeConfig: '@pz_OrderConf_6'
    title: '{{Customer}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '18acb1bf-445e-4ec8-afeb-79587e9eae45'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_41']
  pz_DefTG_74:
    orderTypeConfig: '@pz_OrderConf_6'
    title: '{{Container_storage}}'
    sequenceNumber: '50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
    id: 'a46fe413-c4b4-45c4-9025-84a7010e99f8'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_42']
  pz_DefTG_75:
    orderTypeConfig: '@pz_OrderConf_7'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a743cc2b-9d7c-4e7b-a9ff-509f231fd30a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_76:
    orderTypeConfig: '@pz_OrderConf_7'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'd372e046-5a09-409e-918d-1420bfaa5ed1'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_43']
  pz_DefTG_77:
    orderTypeConfig: '@pz_OrderConf_8'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '21f69130-02fa-44aa-b4ac-df21c057c2f1'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_78:
    orderTypeConfig: '@pz_OrderConf_9'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '9cb3c70d-58b2-4cc8-bd8e-8af4d87d58b3'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_79:
    orderTypeConfig: '@pz_OrderConf_10'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '99417f63-c480-437f-89e7-4fba8d9af4e2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_80:
    orderTypeConfig: '@pz_OrderConf_11'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a8eaf7db-8fb5-4936-8441-b61b2cd3847c'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_81:
    orderTypeConfig: '@pz_OrderConf_11'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '42545bd7-05fd-4aba-bb4c-729fa5d3a6f1'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_44']
  pz_DefTG_82:
    orderTypeConfig: '@pz_OrderConf_12'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'e15def8b-b39e-4314-b4dd-858b4835cb1a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_83:
    orderTypeConfig: '@pz_OrderConf_12'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '423b92de-1d39-4a11-ab92-9d925a261e41'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_45']
  pz_DefTG_84:
    orderTypeConfig: '@pz_OrderConf_13'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'd88ab692-c250-444a-ab7f-562f7abe48b9'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_85:
    orderTypeConfig: '@pz_OrderConf_13'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '853697d4-08a8-412c-afc9-3afa69bb2f87'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_46']
  pz_DefTG_86:
    orderTypeConfig: '@pz_OrderConf_14'
    title: '{{Loading_location}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '8ac1d8cf-ff8a-4782-a63d-fb84901bf30a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_87:
    orderTypeConfig: '@pz_OrderConf_14'
    title: '{{Customer}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '4745fa6b-48c1-4328-9dbc-9ef2c6a5aa42'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_47']
  pz_DefTG_88:
    orderTypeConfig: '@pz_OrderConf_15'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '69689bdc-ce65-4f72-a31f-57b6a0fb00bc'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_DefAddInfo_31']
  pz_DefTG_89:
    orderTypeConfig: '@pz_OrderConf_16'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '6d956265-adfb-4c4f-958e-a71b42207714'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_90:
    orderTypeConfig: '@pz_OrderConf_16'
    title: '{{Municipal_Approach}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_APPROACH)>'
    id: '86b3e8ba-9b15-4cb3-81d2-9c9b7fd4b7cd'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_48']
    additionalInformationItems: ['@pz_DefAddInfo_32']
  pz_DefTG_91:
    orderTypeConfig: '@pz_OrderConf_16'
    title: '{{Municipal_Service}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_SERVICE)>'
    id: '3fc07296-aaa4-4682-b09e-073d71d60338'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_49', '@pz_DefTGR_50']
    additionalInformationItems: ['@pz_DefAddInfo_33']
  pz_DefTG_92:
    orderTypeConfig: '@pz_OrderConf_16'
    title: '{{Municipal_Return}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_RETURN)>'
    id: 'c9ed63f8-6324-4e3a-a725-9cd27ec14310'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_51']
    additionalInformationItems: ['@pz_DefAddInfo_34']
  pz_DefTG_95:
    orderTypeConfig: '@pz_OrderConf_17'
    title: '{{Municipal_Service}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'd812a6d4-fe98-410c-9396-bc0a943e3201'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    additionalInformationItems: ['@pz_DefAddInfo_36']
  pz_DefTG_97:
    orderTypeConfig: '@pz_OrderConf_18'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '80202e44-9315-4cad-895b-cce19f1ad9ee'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_98:
    orderTypeConfig: '@pz_OrderConf_18'
    title: '{{approach_sweeping_area}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_APPROACH)>'
    id: '0380ad20-a0f6-4a9f-b5ed-0dc449a68e20'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_56']
    additionalInformationItems: ['@pz_DefAddInfo_38']
  pz_DefTG_99:
    orderTypeConfig: '@pz_OrderConf_18'
    title: '{{sweeping}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_SERVICE)>'
    id: '081845cf-21f1-48e0-b941-bd3f3592dd3b'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_57', '@pz_DefTGR_58']
    additionalInformationItems: ['@pz_DefAddInfo_39']
  pz_DefTG_100:
    orderTypeConfig: '@pz_OrderConf_18'
    title: '{{return_from_sweeping_area}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_RETURN)>'
    id: 'f143d2d1-6b45-4126-92ec-6d68e3a08fdc'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_59']
    additionalInformationItems: ['@pz_DefAddInfo_40']
  pz_DefTG_101:
    orderTypeConfig: '@pz_OrderConf_19'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: 'ad1416bb-b8b2-46e6-8fa6-4213a0893f43'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_102:
    orderTypeConfig: '@pz_OrderConf_19'
    title: '{{approach_snow_clearance_area}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_APPROACH)>'
    id: 'df4ac80b-f414-4675-9307-e72536dd5ffb'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_60']
    additionalInformationItems: ['@pz_DefAddInfo_41']
  pz_DefTG_103:
    orderTypeConfig: '@pz_OrderConf_19'
    title: '{{winter_service}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_SERVICE)>'
    id: 'f13e70b0-3a45-4786-b8fc-a60c93387492'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_61', '@pz_DefTGR_62']
    additionalInformationItems: ['@pz_DefAddInfo_42']
  pz_DefTG_104:
    orderTypeConfig: '@pz_OrderConf_19'
    title: '{{return_from_snow_clearance_area}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_RETURN)>'
    id: '013a6d52-36db-47e2-b438-e37ca6ad0780'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_63']
    additionalInformationItems: ['@pz_DefAddInfo_43']
  pz_DefTG_105:
    orderTypeConfig: '@pz_OrderConf_20'
    title: '{{bin_service}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '802e964b-2e64-44d1-8385-302e6bbaab08'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_106:
    orderTypeConfig: '@pz_OrderConf_21'
    title: '{{loose_waste}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '36372b34-935c-45ca-9f39-62db541b5f3c'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_107:
    orderTypeConfig: '@pz_OrderConf_22'
    title: '{{court_work}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '15e162c4-23cb-4ad3-8659-61470cedecc4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_108:
    orderTypeConfig: '@pz_OrderConf_23'
    title: '{{special_service}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a5174e1a-6d6b-4984-93c5-779f8c0377b7'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_109:
    orderTypeConfig: '@pz_OrderConf_24'
    title: '{{add_clean}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'f8462a6d-0ba0-4640-a218-4c26da138180'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_110:
    orderTypeConfig: '@pz_OrderConf_25'
    title: '{{place_sweep}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '9be3b613-4d3d-4b24-b8db-d1a72616aca2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_111:
    orderTypeConfig: '@pz_OrderConf_26'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '93a01b19-3ad5-482d-8590-37c3b58d12a7'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_112:
    orderTypeConfig: '@pz_OrderConf_26'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'e05072f2-9eeb-4b89-a5f0-dd66c64fb3e6'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_64']
  pz_DefTG_113:
    orderTypeConfig: '@pz_OrderConf_27'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'fd45edc8-cae9-49b0-9620-87c9bb2e65a6'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_114:
    orderTypeConfig: '@pz_OrderConf_27'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '352b3776-1dfb-466a-b895-06d8cfcb8ad7'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_65']
  pz_DefTG_115:
    orderTypeConfig: '@pz_OrderConf_28'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '1df52841-be13-4d7a-9fb4-31192182b54a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_116:
    orderTypeConfig: '@pz_OrderConf_29'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '7bc3fa15-718e-423c-9951-5b560c315f0b'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_117:
    defaultNote: '@pz_DefNote_9'
    title: '{{Container_defective_-_No_emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '9e115e53-c267-41cc-b0a3-1bba432d71e9'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_118:
    defaultTermination: '@pz_DefTerm_13'
    title: '{{Transport_interruption_without_scan}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'aaa243d9-bb97-461c-b445-77cb54cdaaf2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_119:
    defaultNote: '@pz_DefNote_10'
    title: '{{Change_in_working_hours}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '9b661eec-6e5f-46ef-a750-93f07cf7e042'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_120:
    defaultNote: '@pz_DefNote_11'
    title: '{{Not_provided}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '090c8d0d-187a-4c7f-ad30-39d8a424886c'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_121:
    defaultNote: '@pz_DefNote_12'
    title: '{{Container_overfilled}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'dc10d445-ded6-42ae-9754-bc90ae310aa3'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_122:
    defaultNote: '@pz_DefNote_13'
    title: '{{Container_filled_incorrectly}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '92df8b0f-195d-4dcd-b35c-afa3021d65fc'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_123:
    defaultNote: '@pz_DefNote_14'
    title: '{{Container_upside_down}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '7024c8bc-5418-426b-8646-7f55022188a4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_124:
    defaultNote: '@pz_DefNote_15'
    title: '{{Wrong_material}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'fa6538b0-4faf-45a8-91f9-62e1f9e378bb'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_125:
    defaultNote: '@pz_DefNote_16'
    title: '{{Fee_stamp_missing}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '38fcd896-e0a7-43a9-892c-979baf7a7bff'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_126:
    defaultNote: '@pz_DefNote_17'
    title: '{{Road_not_passable}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'c7f4da62-c1e5-4fdc-bb29-33baf6bce151'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_127:
    defaultNote: '@pz_DefNote_18'
    title: '{{Excess_quantity}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '89c67791-dec6-48f6-ab22-ce505b9eaaa5'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_128:
    tourDataConfig: '@pz_TourConf_2'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '5e5ff8fa-94a1-4bcd-8661-7470f7bb19ba'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_129:
    tourDataConfig: '@pz_TourConf_2'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '1af5665b-0f17-4bb6-bb65-68054ac8df0b'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_130:
    defaultNote: '@pz_DefNote_20'
    title: '{{Cleaning_the_press_shaft}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'fa881768-b926-453f-a3a8-b6c336245688'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_131:
    defaultInterruption: '@pz_DefInt_13'
    title: '{{Container_back_to_customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '35e82709-08f6-463a-b2a6-5ac3ee91d4cf'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_132:
    tourDataConfig: '@pz_TourConf_3'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '05d10b75-d9e1-43fd-8add-d3297f2adb9e'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_133:
    tourDataConfig: '@pz_TourConf_3'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '1cf4cbe4-d3a4-40c8-9b3c-2685e580373d'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_134:
    tourDataConfig: '@pz_TourConf_3'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '890b6a35-e02b-4cbb-b6d2-8773146fe70b'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_135:
    tourDataConfig: '@pz_TourConf_4'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'f15b6503-67a6-4c09-b723-cfd201d2e918'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_136:
    tourDataConfig: '@pz_TourConf_4'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '773cbe5c-dbdb-4afb-b364-4e070128efab'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_137:
    tourDataConfig: '@pz_TourConf_4'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'ef8f5e89-b85c-423f-9935-74d1750857ee'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_138:
    tourDataConfig: '@pz_TourConf_5'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '0b12f597-2098-4247-9eba-afa5c99dd3b1'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_139:
    tourDataConfig: '@pz_TourConf_6'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '9695d126-697e-4797-b0bb-1cfe5fd0c569'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_140:
    tourDataConfig: '@pz_TourConf_5'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '113ff79b-c57d-4648-a9d5-99d481021283'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_141:
    tourDataConfig: '@pz_TourConf_5'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '2170cd54-93eb-489b-8d5e-36427b66adbe'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_142:
    tourDataConfig: '@pz_TourConf_6'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '3687b280-4421-4867-bbed-f0924c46a603'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_143:
    tourDataConfig: '@pz_TourConf_6'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'f180d052-26e9-494f-a892-b279be344e54'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_144:
    tourDataConfig: '@pz_TourConf_7'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '83d0fd92-83c8-4bc3-a2d6-8bcef92983a8'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_145:
    tourDataConfig: '@pz_TourConf_7'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '3c0ec582-14a7-41b3-8683-a259b0544cc5'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_146:
    tourDataConfig: '@pz_TourConf_7'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'c452b8be-b129-4f3e-becf-b87644f1f77b'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_147:
    tourDataConfig: '@pz_TourConf_8'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'bf2f9a78-caa5-4b5a-aaa0-828ace9df966'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_148:
    tourDataConfig: '@pz_TourConf_8'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '5ef25c28-4e7a-4903-8d08-966972d084a4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  pz_DefTG_149:
    tourDataConfig: '@pz_TourConf_8'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '6c646a14-4cce-4cfd-9dcd-9970531cbffd'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  pz_DefTG_150:
    orderTypeConfig: '@pz_OrderConf_30'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '8b049f17-8b29-46e7-8991-c21f5a02d8b4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_151:
    orderTypeConfig: '@pz_OrderConf_30'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '55d5f254-e6c2-4a61-b407-a11203ca92ff'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_66']
  pz_DefTG_152:
    orderTypeConfig: '@pz_OrderConf_31'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '76546d95-cf1b-4a4e-9359-5e6249dc45cf'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_153:
    orderTypeConfig: '@pz_OrderConf_31'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '5c511740-100a-4ba0-9744-cd8903d94eb7'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_67']
  pz_DefTG_154:
    orderTypeConfig: '@pz_OrderConf_32'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '7420b25a-8527-49c2-8ec5-e8fd802b4965'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_155:
    orderTypeConfig: '@pz_OrderConf_32'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '0c2db028-6ec3-445e-98a7-b3fc4b648b55'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_68']
  pz_DefTG_156:
    orderTypeConfig: '@pz_OrderConf_33'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '0688b123-1030-4f3a-b871-03719ad7070d'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  pz_DefTG_157:
    orderTypeConfig: '@pz_OrderConf_33'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '7731d1cc-a513-4a12-a337-c06ba54039c2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@pz_DefTGR_69']
  pz_DefTG_158:
    defaultTermination: '@pz_DefTerm_14'
    title: '{{Incorrectly_planned_too_many_containers}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'e2ec5a5f-b1e4-490f-a095-7e1530456cb6'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
