App\Domain\Entity\ValueObject\Element:
  pz_sub_DefElem_SUB_DE_1_1:
    __construct: { id: ba5bdc91-9650-5fe5-9838-b86b57d4310b, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_2:
    __construct: { id: 9d87df65-0884-5536-bbf8-175d92c99ce9, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_3:
    __construct: { id: b4984d7b-f8e5-56fd-800c-28092f867152, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_4:
    __construct: { id: c3d5681f-d5ad-5cce-960f-e6cfe163b47f, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_5:
    __construct: { id: 6a76b78e-6a01-5630-a684-c127e4b7e7ef, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_6:
    __construct: { id: 5cf5eb98-7cc6-5345-8bed-4cb812d92189, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_7:
    __construct: { id: aeeeb7ee-264c-5c05-b5de-fe5b3b540c07, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_8:
    __construct: { id: 44d06e04-aada-5f56-8ea7-581921c3a9eb, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_9:
    __construct: { id: bb78c870-b02c-5d6b-b40d-7f27afe7bbc1, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_10:
    __construct: { id: 55637b4d-cef2-585c-8747-16750269cbcc, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_11:
    __construct: { id: a97a7655-3f15-5cb5-a516-795b6a0c056c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_12:
    __construct: { id: a13c6c41-874c-5906-b0d0-77b536c32c49, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_13:
    __construct: { id: 98bca483-bba1-5efb-9393-29a2b5d5a125, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_1_14:
    __construct: { id: 322e8c27-5253-5020-b41f-7376b5433fc8, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_2_1:
    __construct: { id: 291828eb-6aa2-5b48-b7c1-5cee324ff2b0, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_2_2:
    __construct: { id: 43ebab0d-bf7c-5310-93ae-01a16b0882ef, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_2_3:
    __construct: { id: 10686a1e-19b8-5043-8afa-9251665496b8, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_2_4:
    __construct: { id: ec132434-1fad-51ea-85bf-040bdd84bfc6, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_2_5:
    __construct: { id: 38d43c16-a3b0-513a-a994-acf6c90ec779, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_2_6:
    __construct: { id: bc3e6507-990e-51c7-b012-12d1b874f659, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_3_1:
    __construct: { id: 301c32d4-0fe4-541f-a0ca-bb7d404cb24a, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: 'Nur Behälteranzahlen von 1 bis 3 sind möglich', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_3_2:
    __construct: { id: 95160d71-0d22-5e82-bfca-32742aab9702, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: 'Nur Behälteranzahlen von 1 bis 3 sind möglich', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_3_3:
    __construct: { id: 883f559c-51fd-5564-8029-7202ae6a460d, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: 'Nur Behälteranzahlen von 1 bis 3 sind möglich', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_3_4:
    __construct: { id: 8f233671-272a-501c-9773-7b8a630da99f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: 'Nur Behälteranzahlen von 1 bis 3 sind möglich', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_3_5:
    __construct: { id: 377847d1-3cda-584a-9268-ad14a5af6b7f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: 'Nur Behälteranzahlen von 1 bis 3 sind möglich', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_3_6:
    __construct: { id: b9cf0b4f-f5d6-5910-9788-8ab70e0048ae, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: 'Nur Behälteranzahlen von 1 bis 3 sind möglich', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_1:
    __construct: { id: bd34dc21-4050-5f90-a1af-5578d3a429f7, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_2:
    __construct: { id: bc927982-8ffb-51e6-b42f-81b2d3763bed, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_3:
    __construct: { id: 7909faab-f20d-5db9-b953-7b9117af2946, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_4:
    __construct: { id: a38826d1-ebde-5667-b16e-15d543670086, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_5:
    __construct: { id: 3e29ef65-2906-5e92-b306-1f070c70dc38, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_6:
    __construct: { id: e6f394d1-cd45-55d1-b065-e350087a52f8, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_7:
    __construct: { id: aabdf790-7874-56b3-8500-ed2f02942314, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_8:
    __construct: { id: 9a7472aa-025e-5f59-9e8d-ef40df4e0056, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_9:
    __construct: { id: ddd363ad-7e22-54f0-a98c-1841de785c02, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_10:
    __construct: { id: ba8874d2-d6a1-5482-a8ff-bc6010618650, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_11:
    __construct: { id: 2a691489-71fc-5978-a166-665fc1ab1f82, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_12:
    __construct: { id: 7ee0272d-c019-5827-80ae-ba51ac146afc, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_13:
    __construct: { id: 1ef340de-6de4-5f26-b6d3-ada4eb7ad39e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_4_14:
    __construct: { id: 1ca9b229-199a-5361-826d-1a67231415fc, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_1:
    __construct: { id: c34ef1e8-fc3e-5ece-b3cb-af81e6d4d470, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_2:
    __construct: { id: 323dc3ed-5605-5a49-b544-2b4e57a40a04, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_3:
    __construct: { id: 6c81ec5e-30a1-53a5-9ce2-aee11dd56c04, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_4:
    __construct: { id: 7d0c0e49-399b-5039-bb8a-16a710e84b23, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_5:
    __construct: { id: d54ba47e-da60-54af-a570-2f11276e67d8, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_6:
    __construct: { id: c34da16c-ad73-5865-8e6e-2af04160a9f2, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_7:
    __construct: { id: 4dc2de99-262f-5e25-81fb-a2a2aad0450b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_8:
    __construct: { id: 61f2db12-8fb6-5110-bf97-b312eaa3a8c4, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_9:
    __construct: { id: 01650843-09fc-53a1-89e5-4195cb9a718f, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_5_10:
    __construct: { id: ba8d23c5-2868-515e-a3fb-2fa16759a70e, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_sub_DefElem_SUB_DE_6_1:
    __construct: { id: f87eea64-c0e0-548e-be5b-c52c749d7f74, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_2:
    __construct: { id: b06249b2-1aac-585e-9b31-1c03a9890ec8, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_3:
    __construct: { id: 912e0634-9bdd-5497-9198-9aba4f7cf19d, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_4:
    __construct: { id: a7ad583b-eaaf-5a34-85ae-4f64e2d5ccbf, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_5:
    __construct: { id: 67ce0ea9-42d0-5455-a761-571293774773, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_6:
    __construct: { id: 976d71ca-972b-57e8-a7c1-fa4e824a4c81, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_7:
    __construct: { id: 15febb66-9509-5876-81b0-fcfc0b422594, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_8:
    __construct: { id: 26de3cfe-7709-523a-9066-8d460753b637, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_9:
    __construct: { id: 9bcf4d32-44cb-5b44-8e1d-1e41c9b43909, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_6_10:
    __construct: { id: fe40082f-584f-5dcd-ad8c-3c60e21f701e, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: eafb95fb-48ea-422c-9727-115d21d2849a, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_sub_DefElemOption_SUB_DE_1', '@pz_sub_DefElemOption_SUB_DE_2'] }
  pz_sub_DefElem_SUB_DE_7_1:
    __construct: { id: 7fe62c6f-ac40-5d84-b79d-48ac4045cfe1, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_2:
    __construct: { id: 425c5285-c297-5fb6-a341-f0a12fed96d3, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_3:
    __construct: { id: 7ceae030-73be-5ea4-8af4-41e434c2e9fc, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_4:
    __construct: { id: a1d700f9-36a1-55cf-838b-5e34594ca465, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_5:
    __construct: { id: df4e0b08-024c-54f2-84ca-c0bbbd16de58, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_6:
    __construct: { id: 97436a71-32c5-58e4-ae29-9cf86a35a910, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_7:
    __construct: { id: be45bf30-305d-5135-bf2d-0de1724bd66a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_8:
    __construct: { id: 6e57f537-638f-57ab-89ad-174b7585dee7, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_9:
    __construct: { id: 39309340-3408-5003-9c7c-cf420a4a13da, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_7_10:
    __construct: { id: 4334cb06-b91e-5904-8b32-fa73c132ab3f, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: 'Bitte ein Gewicht zwischen 0.001 und 9999.999 eintragen ', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_sub_DefElem_SUB_DE_8_1:
    __construct: { id: 94bd8a40-668e-5b60-912f-7a19aae9efaf, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_2:
    __construct: { id: 3c045064-0e9f-59bf-8a02-37cdb32edac7, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_3:
    __construct: { id: 7f012791-88c8-5ee2-ab71-02510b1c5005, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_4:
    __construct: { id: 138806c3-42e9-517c-8a88-eff7eae62dd8, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_5:
    __construct: { id: 93892472-50c2-5215-a7a8-4b474c14454e, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_6:
    __construct: { id: 2883ef93-a7ef-579e-af6d-8f16fa3c8a7d, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_7:
    __construct: { id: 4752aeb3-51ba-5e7a-8034-6d4eb32e9ca6, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_8:
    __construct: { id: 6b6ee38c-62ef-5c9f-a83b-b2482844710b, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_9:
    __construct: { id: 1ef575e7-6d53-5dd8-960f-b4d166d22095, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_sub_DefElem_SUB_DE_8_10:
    __construct: { id: 8a219f4b-3bd6-5b94-8222-21c1e2551c28, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
