App\Domain\Entity\OrderTypeConfig:
  lu_int_OrderConf_LU_INT_1:
    name: '{{Street sweeping}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_06)>'
    branch: '<getBranchObject("4487f068-d1c0-4444-b3a3-92aeb1962b32", <getGermanyTenant()>)>'
    id: '69a793f3-41b8-44d0-9a6a-cbd9a102705d'
    tenant: '<getGermanyTenant()>'
  lu_int_OrderConf_LU_INT_2:
    name: '{{Square sweeping}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_36)>'
    branch: '<getBranchObject("4487f068-d1c0-4444-b3a3-92aeb1962b32", <getGermanyTenant()>)>'
    id: '6325b380-ed1d-4894-a707-32e7c1a80a77'
    tenant: '<getGermanyTenant()>'
  lu_int_OrderConf_LU_INT_3:
    name: '{{Cleaning}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_21)>'
    branch: '<getBranchObject("4487f068-d1c0-4444-b3a3-92aeb1962b32", <getGermanyTenant()>)>'
    id: '16f2c224-3190-4d56-9dd4-84260ddc7e45'
    tenant: '<getGermanyTenant()>'
  lu_int_OrderConf_LU_INT_4:
    name: '{{Cleaning assistance}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_38)>'
    branch: '<getBranchObject("4487f068-d1c0-4444-b3a3-92aeb1962b32", <getGermanyTenant()>)>'
    id: 'c2fdf4b1-10f3-45d1-8655-767a2e92c207'
    tenant: '<getGermanyTenant()>'
  lu_int_OrderConf_LU_INT_5:
    name: '{{Winter service}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_07)>'
    branch: '<getBranchObject("4487f068-d1c0-4444-b3a3-92aeb1962b32", <getGermanyTenant()>)>'
    id: '4fedb529-a6af-4e01-9200-cd56593bf70f'
    tenant: '<getGermanyTenant()>'
