App\Domain\Entity\OrderTypeConfig:
  pz_sub_OrderConf_SUB_DE_1:
    name: '{{delivery}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_10_SUB)>'
    id: '3c2f6854-d0a3-40c2-8fcf-1ee16035469f'
    tenant: '<getGermanyTenant()>'
  pz_sub_OrderConf_SUB_DE_2:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11_SUB)>'
    id: '888061b6-53e9-4670-be48-77d3f9e352b8'
    tenant: '<getGermanyTenant()>'
  pz_sub_OrderConf_SUB_DE_3:
    name: '{{empty/exchange}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_12_SUB)>'
    id: 'ee807e4b-fb6c-432b-8666-0921fdd24e21'
    tenant: '<getGermanyTenant()>'
  pz_sub_OrderConf_SUB_DE_4:
    name: '{{14}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_14_SUB)>'
    id: '154a2073-c497-4120-8858-3274c9cbe36c'
    tenant: '<getGermanyTenant()>'
  pz_sub_OrderConf_SUB_DE_5:
    name: '{{15}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_15_SUB)>'
    id: 'fd42c134-3989-430d-a0fc-bfd57190d0d7'
    tenant: '<getGermanyTenant()>'
  pz_sub_OrderConf_SUB_DE_6:
    name: '{{30}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_30_SUB)>'
    id: '2852c635-f334-4b2e-b3db-2fbc12dfe9f5'
    tenant: '<getGermanyTenant()>'
