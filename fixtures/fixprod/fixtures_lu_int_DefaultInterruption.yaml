App\Domain\Entity\DefaultInterruption:
  lu_int_DefInt_LU_INT_1:
    description: '{{Break}}'
    externalId: '120'
    id: 'f15a4faf-cc0a-4891-903b-a1ab818c6e5d'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  lu_int_DefInt_LU_INT_2:
    description: '{{Driver_meeting}}'
    externalId: '154'
    id: '8ae5e690-7da1-42ad-a671-d38a90eb0b50'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  lu_int_DefInt_LU_INT_3:
    description: '{{Other}}'
    externalId: '156'
    id: 'f612eafa-1d0a-4191-8dbd-932e5ce661bd'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::OTHER)>'
  lu_int_DefInt_LU_INT_4:
    description: '{{Waiting_time}}'
    externalId: '122'
    id: '21bfb796-160d-4b80-9e18-6c4b09ba2ca0'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::WAITING)>'
  lu_int_DefInt_LU_INT_5:
    description: '{{Vehicle_inspection}}'
    externalId: '158'
    id: '8a353b5b-5271-40da-941d-bdd7dafc5b07'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  lu_int_DefInt_LU_INT_6:
    description: '{{Workshop}}'
    externalId: '142'
    id: '5940cfc8-bfeb-4e67-a3b9-786d532b4d65'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::GARAGE)>'
  lu_int_DefInt_LU_INT_7:
    description: '{{Refuel}}'
    externalId: '148'
    id: '99640e4f-478d-4d60-8f2f-ffa081eeeb7e'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::REFUEL)>'
  lu_int_DefInt_LU_INT_8:
    description: '{{Traffic_jam}}'
    externalId: '140'
    id: '04962001-174b-4406-aea1-c07d37ddcebb'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::TRAFFICJAM)>'
  lu_int_DefInt_LU_INT_9:
    description: '{{Vehicle_care}}'
    externalId: '144'
    id: 'b6ea4919-ef35-4bf8-baf2-646bcd188a3e'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  lu_int_DefInt_LU_INT_10:
    description: '{{Way_home}}'
    externalId: '162'
    id: 'b93adc9d-55bc-4c3b-8bb7-c6f5dc8829f5'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  lu_int_DefInt_LU_INT_11:
    description: '{{Pre-loading}}'
    externalId: '152'
    id: '0dc608d7-5e33-4a14-87b8-dde8c4a0e717'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  lu_int_DefInt_LU_INT_12:
    description: '{{Non_working_time}}'
    externalId: '166'
    id: '77660b55-2b6b-461b-8a60-7e74a829fe62'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
