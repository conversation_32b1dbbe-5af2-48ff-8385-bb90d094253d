App\Domain\Entity\LocationFormatConfig:
  location_format_config_1:
    tenant: '<getGermanyTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    highlight: true
    sequence: 0
    icon: 'person'
    alsoFrontView: true
    formatString: '%name%'
  location_format_config_2:
    tenant: '<getGermanyTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    highlight: true
    sequence: 1
    icon: 'pin_drop'
    alsoFrontView: true
    formatString: '%street% %houseNumber%, %postalCode%, %city% (%district%)'

  location_format_config_3:
    tenant: '<getGermanyTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    highlight: true
    sequence: 0
    icon: 'person'
    alsoFrontView: true
    formatString: '%name%'
  location_format_config_4:
    tenant: '<getGermanyTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    highlight: true
    sequence: 1
    icon: 'pin_drop'
    alsoFrontView: true
    formatString: '%street% %houseNumber%, %postalCode%, %city%'

  location_format_config_5:
    tenant: '<getGermanyTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    highlight: true
    sequence: 0
    icon: 'person'
    alsoFrontView: true
    formatString: '%name%'
  location_format_config_6:
    tenant: '<getGermanyTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    highlight: true
    sequence: 1
    icon: 'pin_drop'
    alsoFrontView: true
    formatString: '%street% %houseNumber%, %postalCode%, %city%'

  location_format_config_7:
    tenant: '<getNetherlandsTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    highlight: true
    sequence: 0
    icon: 'person'
    alsoFrontView: true
    formatString: '%name%'
  location_format_config_8:
    tenant: '<getNetherlandsTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    highlight: true
    sequence: 1
    icon: 'pin_drop'
    alsoFrontView: true
    formatString: '%street% %houseNumber%, %postalCode%, %city%'

  location_format_config_9:
    tenant: '<getLuxembourgTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    highlight: true
    sequence: 0
    icon: 'person'
    alsoFrontView: true
    formatString: '%name%'
  location_format_config_10:
    tenant: '<getLuxembourgTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    highlight: true
    sequence: 1
    icon: 'pin_drop'
    alsoFrontView: true
    formatString: '%street% %houseNumber%, %postalCode%, %city%'

  location_format_config_11:
    tenant: '<getSpainTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    highlight: true
    sequence: 0
    icon: 'person'
    alsoFrontView: true
    formatString: '%name%'
  location_format_config_12:
    tenant: '<getSpainTenant()>'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    highlight: true
    sequence: 1
    icon: 'pin_drop'
    alsoFrontView: true
    formatString: '%street% %houseNumber%, %postalCode%, %city%'
