App\Domain\Entity\ValueObject\Element:
  nl_DefElem_NL_1_1:
    __construct: { id: 3495cf68-f96a-5a3a-adde-133644dac500, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_2:
    __construct: { id: 0251d20c-272a-537a-8fdc-b6334ab866a9, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_3:
    __construct: { id: 43df59a9-5e14-54b8-a930-4b84d9e30bd6, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_4:
    __construct: { id: df746224-5101-517b-8084-2d81db6351b4, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_5:
    __construct: { id: 51db712e-887c-5508-89b2-7eab604cab71, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_6:
    __construct: { id: 6aba32b8-2440-5ebd-a89a-a543b6b70459, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_7:
    __construct: { id: 77e7f822-8892-5887-8efb-6e9fb0266e21, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_8:
    __construct: { id: 8de2f8ca-4459-5ac0-9077-b056960084b4, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_9:
    __construct: { id: 2a9122fc-914f-5cf7-9a2a-2bdca6d89892, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_10:
    __construct: { id: d017be42-ea39-5ac0-b3bb-89fc3b4d1bf0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_11:
    __construct: { id: 48d57cc1-57d9-5acf-8edf-235d722c4274, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_12:
    __construct: { id: 7d1175b1-9070-5fde-8f37-aaa5de748d4c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_13:
    __construct: { id: 69231af1-b47e-56d5-b443-23a2b58c1afa, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_1_14:
    __construct: { id: 1cdcfa28-c9a2-51bf-8721-8c3585f1c31c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_1:
    __construct: { id: aa51fb28-3b2b-5db8-b296-3d375f4adaa7, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_2:
    __construct: { id: 57e24bd8-6c55-5429-a9f4-fcbe86107a41, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_3:
    __construct: { id: ad4881d8-7ad9-5f43-801b-3bc7956ae285, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_4:
    __construct: { id: 4af2ec7f-5592-5a33-b673-2b7a1d584c0b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_5:
    __construct: { id: 5183f060-61e8-58bb-8c06-a852ad7659cb, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_6:
    __construct: { id: 2b79774b-e4df-5774-ac92-65b465cf7647, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_7:
    __construct: { id: aa00e05f-0117-5bc4-8145-ad5ad568779a, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_8:
    __construct: { id: 0d5c253a-d5dc-597f-835b-5fe442bc8053, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_9:
    __construct: { id: b0b9c010-96d9-5813-a6b0-877919ea8cfd, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 20 }
  nl_DefElem_NL_2_10:
    __construct: { id: ca5fb6cb-0ff9-57bb-913d-0add3de05edc, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 20 }
  nl_DefElem_NL_2_11:
    __construct: { id: e72572d2-008e-5dff-a1e8-ba660d43d08e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 20 }
  nl_DefElem_NL_2_12:
    __construct: { id: 090f7603-11cc-56d9-8c67-da8eb3582829, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_13:
    __construct: { id: c63d97bb-78cc-5abc-8d7d-d96026f0835c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_14:
    __construct: { id: 61ebe9a3-3bc4-5203-af92-5e336f5681c5, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_15:
    __construct: { id: 2ea55bf7-572c-52cc-a24a-1056c4890175, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_16:
    __construct: { id: f924bdc4-4fec-5d02-bbad-7f3e0f467d34, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_17:
    __construct: { id: 875ae338-2a5e-5f73-9fc8-bb5a9d6cb1fa, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_18:
    __construct: { id: 23d7d13c-3f5c-54af-a959-362abb2e61ad, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_19:
    __construct: { id: 1cc28543-f3e9-5f93-b686-34f6303b1f6b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_20:
    __construct: { id: 1f252c07-050b-5d2f-9424-a50a51e4b46c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_21:
    __construct: { id: 850cf28e-4c85-5f99-895b-a5dd67ac9ba6, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_22:
    __construct: { id: 2a16438e-a7de-5c6a-8e3e-00ac67649258, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_23:
    __construct: { id: 1c649f04-4e91-5991-b71b-600e89fcb995, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_24:
    __construct: { id: f84069ce-2b32-5be9-9a39-9c1b2cd072ed, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_25:
    __construct: { id: ad38774a-768e-5d7d-803c-17ed00f59549, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_2_26:
    __construct: { id: c28e3eb3-05be-596b-90da-403acf3801b1, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  nl_DefElem_NL_3_1:
    __construct: { id: a509e0da-ff34-5ba9-99a8-4ed4bc7b8635, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  nl_DefElem_NL_3_2:
    __construct: { id: 94ac31b2-097c-5aa4-84e1-200ce7f6c3c1, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  nl_DefElem_NL_3_3:
    __construct: { id: 327ce9d0-1cbc-5b9d-b005-0d8f29bb6e6a, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  nl_DefElem_NL_3_4:
    __construct: { id: 7be51265-afa7-5908-859b-1109ad1ac599, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  nl_DefElem_NL_3_5:
    __construct: { id: bf119192-6ca1-5bbd-b488-12278f00e590, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_1:
    __construct: { id: 7b89a5d3-64ce-5b1d-aabb-4f91882afb37, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_2:
    __construct: { id: 1c5ae1b5-564b-560a-839f-79cec000ae04, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_3:
    __construct: { id: a7909e10-beed-5d26-8cba-e17c28fd9e61, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_4:
    __construct: { id: 0473fc6a-b066-5743-9087-36184fe0b015, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_5:
    __construct: { id: 2c90e39c-367d-563c-ba4d-2af42ec1baa1, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_6:
    __construct: { id: df16ab4b-b594-599a-8491-8fa016911b91, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_7:
    __construct: { id: 19d9d759-7288-5b8e-9aaa-8f7c77509558, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_8:
    __construct: { id: 466e271d-79d6-58ad-aab9-7d4c61e1bfbf, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_9:
    __construct: { id: d8341a4a-10cd-5603-b731-cf9240b23e9b, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_10:
    __construct: { id: cd961323-387d-58ed-9f37-2481ea8cf968, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_11:
    __construct: { id: e7c2b8eb-2ec7-5cbc-b7e3-3c2ff514c98b, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_12:
    __construct: { id: 1110337c-6088-5c54-9a21-64ce00820db3, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_13:
    __construct: { id: e8929125-b41c-582e-bd43-9921ed64fb1a, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_14:
    __construct: { id: 9237da82-e1a2-52a9-8fed-87b9fb8a84b8, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_15:
    __construct: { id: 2235fe08-6307-5aaa-a6b8-a50c9f97a032, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_16:
    __construct: { id: 7a46968b-26a6-5521-914e-ebb9f7079724, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_17:
    __construct: { id: 83cf969f-10dc-5718-9615-b7313c451f5b, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_18:
    __construct: { id: 991b431e-acbb-5e4f-9e02-02b56d766140, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_19:
    __construct: { id: 333a9d96-975c-5618-96ab-ffa784307cb5, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_20:
    __construct: { id: 20e0447c-922a-5eb0-869f-a65c60a4c9bf, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_21:
    __construct: { id: 005d81c5-7de8-52a7-bc00-370bd2db947e, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_22:
    __construct: { id: 00b5ed51-d2c3-579e-aedb-b768a82fde55, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_23:
    __construct: { id: 04031d50-0473-5bc7-9014-8bfcb4a679f5, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_24:
    __construct: { id: 7da7957f-fe26-5e73-85c9-a9a794383820, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_25:
    __construct: { id: e1ec29bb-305f-5015-8537-0ba535474f46, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_26:
    __construct: { id: 339834b0-ccf8-5641-8e5f-2221f7711185, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_27:
    __construct: { id: 94de66d0-4196-515d-a67b-9a7c68a62c3f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_28:
    __construct: { id: 56c300be-0fee-549d-84b7-9f067721d0e5, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_29:
    __construct: { id: bf0a75c6-d2b9-50ba-8d20-0d2dea056347, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_30:
    __construct: { id: 72322c5b-1044-599c-a26a-49779dd3bfaa, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_31:
    __construct: { id: 6edab1f3-c061-5cf1-87e6-fb02d36ea1af, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_32:
    __construct: { id: b0b45215-818f-566a-8bf4-395df90cea9d, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_33:
    __construct: { id: 2d7fff52-784f-5d9e-8395-dc40258a29ec, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_34:
    __construct: { id: fd4b667e-5669-55a4-bbf9-91b53a42d740, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_35:
    __construct: { id: 5fab317e-d3b8-5f2a-87f8-e178dc7ddead, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_36:
    __construct: { id: 68949314-ce84-599e-9d78-5c13a6aaa95d, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_37:
    __construct: { id: 148db97c-46ae-5076-89cd-d603b5e5cca1, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_38:
    __construct: { id: 356e8073-cb04-50fb-afe3-b33c8a2f53d4, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_39:
    __construct: { id: d8c25e31-5972-5fd5-b481-7347301bee1b, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_4_40:
    __construct: { id: 28e0783b-f98f-5f57-9457-67e025e532bd, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_quantity}}', label: '{{Please_enter_number_quantity}}', pattern: '^(?!0$)\d{1,3}$', patternError: '{{Number_not_authorized}}', patternHint: '{{Please_enter_number_quantity}}', sequenceNumber: 10 }
  nl_DefElem_NL_5_1:
    __construct: { id: 1d4409ba-1469-50fd-9431-2b9e4368d9b4, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  nl_DefElem_NL_5_2:
    __construct: { id: b506d22e-bcba-5b97-abbb-957badf8e47b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_3:
    __construct: { id: bd0d7b23-dd91-5319-8827-46fd78d1bd4a, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  nl_DefElem_NL_5_4:
    __construct: { id: cd9e4c55-1b40-5a15-aca0-e60a69cf26de, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  nl_DefElem_NL_5_5:
    __construct: { id: 59ab1e58-a94b-5de7-8fce-42d312449e03, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_6:
    __construct: { id: 2899e841-d931-5d26-bd30-8cedeff28622, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_7:
    __construct: { id: 7504df8d-62df-5124-8261-23082ce51f4a, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_8:
    __construct: { id: 18f71c43-8a7b-5f11-8fb2-459e1189106f, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_9:
    __construct: { id: 321d1ecd-88e3-571f-a1ff-7804ac945868, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_10:
    __construct: { id: 20606676-54b5-5413-8886-a74fef1ac129, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_11:
    __construct: { id: 368358c8-e922-5dd2-b39c-7210689ffc53, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_5_12:
    __construct: { id: d9e6179d-d8f9-55e6-b4f7-9256d4dfbea3, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 20 }
  nl_DefElem_NL_6_1:
    __construct: { id: 8c06404e-66f7-5995-989d-13836711144e, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 20 }
  nl_DefElem_NL_6_2:
    __construct: { id: 368831c1-d46e-566a-ac07-aa3386e3fbd4, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_3:
    __construct: { id: 25a0e1ab-4b92-5e01-8dd4-0aabe8e282d0, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 20 }
  nl_DefElem_NL_6_4:
    __construct: { id: eac96da1-6d83-5167-aa48-b1c17c9e32d7, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 20 }
  nl_DefElem_NL_6_5:
    __construct: { id: d79d993b-5a41-5277-bd17-10a0e38790fd, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_6:
    __construct: { id: 2eb69572-f03e-553f-8a37-f95abf2407c8, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_7:
    __construct: { id: b2534343-3313-5fff-9fea-2faa36d0c6f8, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_8:
    __construct: { id: bc5d5abe-c06d-5648-b8be-af879969667f, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_9:
    __construct: { id: b37d30dd-049d-5499-b5ba-d3a327541ab1, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_10:
    __construct: { id: 579abb11-055c-52e7-820b-02046ad1046f, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_11:
    __construct: { id: 172e97a7-583d-5fa4-8c15-c9b110d24586, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_6_12:
    __construct: { id: 4a249cc1-9f99-51bc-9efc-f9449ff7f04c, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight_in_kg}}', label: '{{Please_enter_weight_in_kg}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Please_enter_weight_in_kg}}', sequenceNumber: 30 }
  nl_DefElem_NL_7_1:
    __construct: { id: 75423168-10e9-542b-8b65-edc9398457de, referenceType: fuel, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_the_fuel_you_have_refueled}}', label: '{{Please_select_the_fuel_you_have_refueled}}', valueMaxItems: 1, patternHint: '{{Fuel}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_25', '@nl_DefElemOption_NL_26', '@nl_DefElemOption_NL_27', '@nl_DefElemOption_NL_28', '@nl_DefElemOption_NL_29', '@nl_DefElemOption_NL_30'] }
  nl_DefElem_NL_8_1:
    __construct: { id: 6c651833-c565-5ef7-8362-f9e075bd3c52, referenceType: Unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_the_quantity}}', label: '{{Please_select_the_quantity}}', valueMaxItems: 1, patternHint: '{{Quantity}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_31', '@nl_DefElemOption_NL_32'] }
  nl_DefElem_NL_9_1:
    __construct: { id: 657671b6-636e-5244-9dea-a9a1c54c3211, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_2:
    __construct: { id: 7bdd0d2d-dc53-53e0-9896-828d301c9bf0, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_3:
    __construct: { id: e94d3e11-43c3-5b3f-a7eb-82ad4bde5f06, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_4:
    __construct: { id: 0954913e-260d-5cd0-8f8c-dfe8ae279ca1, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_5:
    __construct: { id: 894a80a5-063f-579f-9819-bb3d8c62d223, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_6:
    __construct: { id: 6a1b6b60-db02-5964-96cd-bb75b51a965e, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_7:
    __construct: { id: df709030-004f-5446-a9e5-34230329c986, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_8:
    __construct: { id: 4b8f5f0d-9205-58c4-bbe6-6041f8821983, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_9:
    __construct: { id: a6f34b9f-01ec-5971-95d9-a9195d41746a, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_10:
    __construct: { id: 873d178f-5b82-5e22-965e-8715ac6342af, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_11:
    __construct: { id: 34099389-1d9b-56a5-8b5d-dc3de7c7c06a, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_12:
    __construct: { id: 822801f8-229b-50f8-83fa-838b09135294, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_13:
    __construct: { id: 3fa50b2c-ef9f-51d4-ab6a-dda7cd40c526, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_14:
    __construct: { id: e2cda02f-55f0-5b31-a153-6f00d8b732e0, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_15:
    __construct: { id: 1f77aee9-0c98-5be4-9de1-7ea26d438398, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_16:
    __construct: { id: 5cc41732-a4ad-5065-84c9-d19c6c547ec9, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_17:
    __construct: { id: 8051b8cb-620d-5473-9ae9-542568dce5c4, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_18:
    __construct: { id: 1507ec18-09e4-56de-896e-69989a0235a0, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_19:
    __construct: { id: be5ea222-de41-5a38-b1f2-b44d781b3fbf, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_20:
    __construct: { id: 79109357-032c-5be2-b51d-e4df394bad90, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_21:
    __construct: { id: f8956fc3-3a17-5676-a891-394930ab5bd6, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_22:
    __construct: { id: 62132ed5-d1f6-5b43-baad-3610b399dffe, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_23:
    __construct: { id: eb052e81-2cdf-536a-8543-3fd7b1bd2b08, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_24:
    __construct: { id: 0eabe338-a347-59ef-9f46-0283ac7cd518, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_25:
    __construct: { id: 427f3fa5-f964-5053-bff7-3c1be9fd1e47, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_26:
    __construct: { id: 800a629b-644e-58d6-be9d-5a4b1b10ca71, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_27:
    __construct: { id: fbce1706-ea5a-5beb-908a-bffe291b9ff2, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_28:
    __construct: { id: e0ec8b51-2c0c-5ac1-89a2-227310c25ec4, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_29:
    __construct: { id: ae76232f-07b3-5fdf-9ebe-80a43a27edaa, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_30:
    __construct: { id: 7a1ada78-d98e-5d07-a74f-274fa5a88711, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_9_31:
    __construct: { id: 00386828-51cd-517e-b8b4-9c57f21448fc, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  nl_DefElem_NL_10_1:
    __construct: { id: 710b7639-b1c2-5237-a120-d15be66304d0, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_2:
    __construct: { id: fe889b82-f27d-5476-bbd0-55b99a8136b1, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_3:
    __construct: { id: 3918d5fc-e860-57b4-8ee0-8bb6ce4e0354, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_4:
    __construct: { id: 34da2d83-f9a4-57f0-a8a1-ebb6ebe3a041, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_5:
    __construct: { id: f640c520-df42-5ccf-8c6d-e3c2561068da, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_6:
    __construct: { id: 5eb0994f-d5f0-57c9-9ace-ed5d25810797, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_7:
    __construct: { id: 61a0394c-94ae-5075-93ed-c7667c224f71, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_10_8:
    __construct: { id: 20bb3930-2194-5e96-963b-16e4edc6cfde, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_1:
    __construct: { id: 03addbe9-49ef-5a4a-8ee9-69e379a20fd5, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_2:
    __construct: { id: 0a67252f-981b-5dba-8be7-62d696eeaf96, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_3:
    __construct: { id: 329de566-2c8a-5050-a23b-ec56e119ecac, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_4:
    __construct: { id: 6ce689db-1c3d-5b7c-89cb-1e7ee86f6db9, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_5:
    __construct: { id: c42283bb-2bb5-5c76-b8c0-c86fe56aad02, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_6:
    __construct: { id: e958ef27-97fb-5e3e-b527-368bd5bbeab7, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_7:
    __construct: { id: 7c385eac-f44d-508b-85c4-d51d06698cfb, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_8:
    __construct: { id: a5a6b047-914b-5bc4-9c52-2cd7a7492185, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_9:
    __construct: { id: c0f86021-f459-536f-b2c2-2838f4d8c1fe, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_10:
    __construct: { id: 4091f96c-a6c6-5f60-b32b-e514f5d65ab7, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_11:
    __construct: { id: 3cf098ec-5c81-5188-b3a9-2b4177fdd86b, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_11_12:
    __construct: { id: fb224712-ace3-5f0f-9c31-b83b3b48b9f5, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  nl_DefElem_NL_12_1:
    __construct: { id: e7bde39d-8f36-513a-a7fd-d798b20e53c4, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', patternHint: '{{Please_enter_containerweight}}', autoAssignment: '<(App\Domain\Entity\Enum\ScaleElementAutoAssignment::FORCED)>', sequenceNumber: 10 }
  nl_DefElem_NL_13_1:
    __construct: { id: 56cd7dda-535a-59ec-ade1-1037a4df6305, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_2:
    __construct: { id: 06ee7c02-99fd-5cb4-839c-02e2ab17bd08, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_3:
    __construct: { id: 65e5a1fb-98d4-57fa-93ef-36fa9a3793c7, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_4:
    __construct: { id: 1039c3fd-63c6-5c23-a22e-4f6e04279c72, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_5:
    __construct: { id: 59332e58-a547-5c10-88f4-c67c5c7ba957, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_6:
    __construct: { id: a50bcee8-bddd-52d4-9ed7-7816914840ae, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_7:
    __construct: { id: d492b75f-e802-5e61-80f9-89b8aa9962cd, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_8:
    __construct: { id: 3bc6f8a9-161e-545a-8d51-2d6fb49a7c07, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_9:
    __construct: { id: e50d8537-6fe2-5d04-8958-024380b52dd3, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_10:
    __construct: { id: d05e21a8-cde2-5650-8cf2-24d05ca30bb1, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_11:
    __construct: { id: 76de02e6-b959-5c83-ba6d-e6f4799b84b0, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_12:
    __construct: { id: 96c52cab-d263-5820-b4ab-155e94ec89d5, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_13:
    __construct: { id: 0f3a3c3e-159e-5c7e-a3b4-d37b3e47eba9, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_14:
    __construct: { id: 8a0d938d-7f60-581e-8580-8ff6e97ca3cd, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_15:
    __construct: { id: c6fcc5df-9d70-5b1f-b663-c407acd1e6ee, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_16:
    __construct: { id: 9c73bc72-926b-58ef-bdc7-bb86fb6478cf, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_17:
    __construct: { id: d8b8ac13-902d-54d4-b5f1-ba4f76267538, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_18:
    __construct: { id: fa9497a8-ae27-52f5-8703-200943a95d8c, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_19:
    __construct: { id: ba24cb0e-81ec-53fc-9025-84b430adff11, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_20:
    __construct: { id: 9a8e7de1-98cd-5b1c-befd-3f835a72745d, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_21:
    __construct: { id: 8d1e69ea-31c2-5d9a-b210-d9a8b95feaa4, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_22:
    __construct: { id: 2ace5c96-0a0d-5176-9a5a-f00d77e680c1, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_23:
    __construct: { id: a6898b7a-8436-58ef-95e9-6fcebf8a1246, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_24:
    __construct: { id: 6e70bcf6-96d1-5127-9b04-3c6cc9bb373f, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_25:
    __construct: { id: 426bb0b2-69d4-59b0-a00d-606144bc6817, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_26:
    __construct: { id: feff2cbc-cba9-5378-9c99-cfcf531f6ac3, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_27:
    __construct: { id: 993bdbcd-8837-5fd7-a703-5ef03087e870, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_28:
    __construct: { id: e355d46f-8d2a-5622-b312-cc54ddc9b70f, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_29:
    __construct: { id: c6373e58-f86c-5fd5-81f7-1cd2d24d6f98, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_30:
    __construct: { id: a64c27b8-6021-5952-998f-2a0b4596a32a, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_31:
    __construct: { id: b7513ade-209a-5bd6-adc5-11053976a808, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_32:
    __construct: { id: c3b15cfb-00ca-5fc2-bc5c-467eaff02027, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_33:
    __construct: { id: 3828fd60-87ea-5c41-b08c-c04c65bd7547, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_34:
    __construct: { id: a997cf0a-6e3a-5607-af88-e518c6648e3b, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_35:
    __construct: { id: 6e874526-4a1b-51a7-9110-77124a5e5b94, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_36:
    __construct: { id: 61e9e6ce-e302-5f2f-9bc3-981bfdb6f770, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_37:
    __construct: { id: 8ca885d4-aaa1-5ae9-8ce1-64f299cd0692, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_38:
    __construct: { id: 2ee4c79b-7d33-5068-b171-dcd1666aaf91, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_39:
    __construct: { id: cf221cb9-12b2-50ec-9b4c-e25f35ac0047, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_13_40:
    __construct: { id: 8b861bca-6881-5f57-9fa6-07ac05e454dd, referenceType: remark, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 0d883b80-11f9-40ae-9ad2-a3be6925a7ba, placeholder: '{{Do_you_have_remarks?}}', label: '{{Do_you_have_remarks?}}', patternHint: '{{Do_you_have_remarks?}}', sequenceNumber: 20, options: ['@nl_DefElemOption_NL_1', '@nl_DefElemOption_NL_2', '@nl_DefElemOption_NL_3', '@nl_DefElemOption_NL_4', '@nl_DefElemOption_NL_5', '@nl_DefElemOption_NL_6', '@nl_DefElemOption_NL_8', '@nl_DefElemOption_NL_10', '@nl_DefElemOption_NL_11', '@nl_DefElemOption_NL_12', '@nl_DefElemOption_NL_16'] }
  nl_DefElem_NL_14_1:
    __construct: { id: 0a0f04dd-c506-5bc2-a578-34808e29c185, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_2:
    __construct: { id: 17ef38bb-23a8-5501-a8bc-58266ce96430, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_3:
    __construct: { id: 30c05f68-05e7-52d3-a161-46ea8963ce4c, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_4:
    __construct: { id: 49ab29d3-bec5-54cf-b2ef-82d5a7605ce8, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_5:
    __construct: { id: b7e8bf2d-a94f-524a-ab64-14eb597b9f0d, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_6:
    __construct: { id: 9e5dc4c3-0575-5747-93c9-ed9a82835c89, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_7:
    __construct: { id: 929a6549-d91b-545e-bd07-78a930213dcb, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_8:
    __construct: { id: 6ecf1ae3-517f-5d56-b7a3-0794caa2b4e1, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_14_9:
    __construct: { id: 47bdc839-73b3-52ae-bc30-350242aa2433, referenceType: remark_wrong_waste, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: e7f11529-1588-4545-ac7f-babb3fd87161, placeholder: '{{Correct_wasteype?}}', label: '{{Correct_wasteype?}}', patternHint: '{{Correct_wasteype?}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_23', '@nl_DefElemOption_NL_24'] }
  nl_DefElem_NL_15_1:
    __construct: { id: bb09e518-5dfe-525b-9920-0a8c185a712f, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_2:
    __construct: { id: e6bca10c-1ad4-5708-8ead-5f64be7c977f, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_3:
    __construct: { id: 67b3e497-ed79-5ca6-900b-6e6b1d527b1b, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_4:
    __construct: { id: 5819b8ca-8bb0-54b5-aea5-70fea9eeff28, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_5:
    __construct: { id: e4dd27cf-2a15-50ed-971a-b7126a4e482e, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_6:
    __construct: { id: 231ff444-1cab-567b-a3c7-59f83081115a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_7:
    __construct: { id: 42cdcec8-7719-5ba4-bff7-c83a34022114, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_8:
    __construct: { id: 6b973d96-3180-596e-a5fc-70c787381ca1, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_9:
    __construct: { id: 0eafcf0d-5117-5330-b174-ee0c09d64ba6, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_10:
    __construct: { id: 3e4b5ef4-7d12-51cf-845f-c8da03f3665e, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_11:
    __construct: { id: 38c6c7e1-c12f-5bca-9212-89d2b2684945, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_12:
    __construct: { id: d77771c9-5ac1-511a-9797-7d5ab00e28ad, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_13:
    __construct: { id: 791de378-7ecd-5294-807b-6a5c9a72fa8a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_14:
    __construct: { id: 67308c92-5f0b-55e4-94d5-2b21ea17a3da, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_15:
    __construct: { id: 501f759e-5b94-5b51-b305-ac3c889df5bb, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_16:
    __construct: { id: 5e264748-edb3-59cd-b7e3-8c2d37a2fa38, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_17:
    __construct: { id: 3ccde450-c9fa-564f-ae3f-ef51862cc077, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_18:
    __construct: { id: a1d78299-fbda-5a0b-8a72-6eb4f586715c, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_19:
    __construct: { id: e208c080-856b-59ab-a687-bd1ef6fcdb00, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_20:
    __construct: { id: fb4cb471-b62f-5402-9e54-75b947836afb, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_21:
    __construct: { id: d05ecfc2-1a79-5dbf-9bea-61d17fa8e063, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_22:
    __construct: { id: 8248474f-1018-5fe7-98d3-312548b6ccc3, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_23:
    __construct: { id: 40796592-577c-5da8-8310-2000495da5b6, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_24:
    __construct: { id: 0ee51b6e-8dc9-5221-8421-4421f74ce2af, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_25:
    __construct: { id: 56a29950-37e9-5ec6-a4af-44dc91483eae, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_26:
    __construct: { id: 1bfcf7e4-5035-58f3-a88f-dccb50dd7afb, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_27:
    __construct: { id: 53dd9731-d17d-5df5-a629-8ef7344c889d, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_28:
    __construct: { id: 6f136dc8-8ce4-56f1-b203-17188d766101, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_29:
    __construct: { id: 9798bcad-6aaa-51be-a7c7-84009e020870, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  nl_DefElem_NL_15_30:
    __construct: { id: a7e7cd4f-a87e-5207-85ff-4284a35da7c4, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_31:
    __construct: { id: 0d9a8300-34d2-52fa-a664-4d3e83b87621, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_32:
    __construct: { id: 01816981-2719-5ee7-a9d1-9243b6a70a0f, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_33:
    __construct: { id: e2c2b615-ab34-517f-9213-09d8797a80c3, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_34:
    __construct: { id: 7a81aa9b-0592-59e0-a4d7-fd4b49212f4a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_35:
    __construct: { id: f0590dbf-55f6-59f6-b45f-99e748785f29, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_36:
    __construct: { id: 1f30df01-e31a-5b9e-a0ee-bb1ca1db354a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_37:
    __construct: { id: e97548d1-dd88-560e-9f66-12f1a6242cd5, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_38:
    __construct: { id: 2602ecf0-b142-55eb-a0b3-54575ead0e15, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_39:
    __construct: { id: dbceb034-30d3-5b8e-a0b4-b4b04bea9caf, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_40:
    __construct: { id: fd32e5cf-8da8-5d95-aa85-97ff76faa055, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_41:
    __construct: { id: 797a993c-4e00-5015-a039-10f10c764a20, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_42:
    __construct: { id: 0472e55b-e71c-51c4-87c7-2e25a1670dfa, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_43:
    __construct: { id: 224315b4-c954-5bc1-b325-ee517be6c12a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_15_44:
    __construct: { id: 6e36e75e-a171-50e9-b7e6-bc95652e6bbf, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  nl_DefElem_NL_16_1:
    __construct: { id: d992b3b0-bb98-50a5-b6d8-54a95d5cd1e3, referenceType: fuel_amount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_the_amount_refueled}}', label: '{{Please_enter_the_amount_refueled}}', patternHint: '{{Please_enter_the_amount_refueled}}', sequenceNumber: 30 }
  nl_DefElem_NL_17_1:
    __construct: { id: aa454032-3d03-5959-8926-153691b5d895, referenceType: minutes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_waitingtime_in_minutes}}', label: '{{Please_enter_waitingtime_in_minutes}}', patternHint: '{{Waitingtime_in_minutes}}', sequenceNumber: 10 }
  nl_DefElem_NL_17_2:
    __construct: { id: e61598e4-7b96-598c-a5dd-37d0832d58cd, referenceType: minutes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_waitingtime_in_minutes}}', label: '{{Please_enter_waitingtime_in_minutes}}', patternHint: '{{Waitingtime_in_minutes}}', sequenceNumber: 10 }
  nl_DefElem_NL_18_1:
    __construct: { id: 54f6e9a2-58d8-509b-8a45-73abab1cd4ff, referenceType: selected_reason_waiting_at_disposalsite, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_the_reason_for_waitingtime}}', label: '{{Please_select_the_reason_for_waitingtime}}', patternHint: '{{Please_select_the_reason_for_waitingtime}}', sequenceNumber: 10, options: ['@nl_DefElemOption_NL_33', '@nl_DefElemOption_NL_34'] }
  nl_DefElem_NL_19_1:
    __construct: { id: 1f0b36d3-a920-5ee9-8a22-df8502babd70, referenceType: departure_check, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Departure_check_completed?}}', label: '{{Departure_check_completed?}}', patternHint: '{{Departure_check_completed?}}', sequenceNumber: 10 }
  nl_DefElem_NL_19_2:
    __construct: { id: 97ce01a8-0c09-5e15-b223-8423d809b457, referenceType: departure_check, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Departure_check_completed?}}', label: '{{Departure_check_completed?}}', patternHint: '{{Departure_check_completed?}}', sequenceNumber: 10 }
  nl_DefElem_NL_20_1:
    __construct: { id: 9f68ffe4-6542-5826-91a8-643a5b8c3ff1, referenceType: extraWaste, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Extra_waste_in_cubes}}', label: '{{Extra_waste_in_cubes}}', pattern: '^((?!0$)(?!0\.0$)(\d(\.\d)?))$', patternError: '{{Only_between_0.1_and_9.9}}', valueNumberPrecision: 1, patternHint: '{{Extra_waste_in_cubes}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_1:
    __construct: { id: 4eda4eec-e389-57e2-ab1d-ca95c51a7800, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_2:
    __construct: { id: dec7c564-4a11-55df-ad91-d1ee8b8f1b46, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_3:
    __construct: { id: 2cd35eaa-6e41-522e-a8eb-6300fba04121, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_4:
    __construct: { id: 2a40328d-8de2-50f0-9349-10a0cdbf7b26, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_5:
    __construct: { id: caa171aa-20f9-55b4-9f00-9f875c80851b, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_6:
    __construct: { id: caf96532-f490-5c02-87ee-023fa6550727, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  nl_DefElem_NL_21_7:
    __construct: { id: e1358cde-40a1-5d3a-9825-86d924872045, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
