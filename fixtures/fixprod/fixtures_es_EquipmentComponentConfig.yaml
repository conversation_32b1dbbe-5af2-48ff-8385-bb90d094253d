App\Domain\VehicleInspection\Config\EquipmentComponentConfig:
  es_EquipmentComponentConf_1:
    __construct: { title: '{{VIR_Fire_Extinguishers}}', label: '{{VIR_Fire_Extinguishers_Label}}', externalId: GANCHO-1, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_2:
    __construct: { title: '{{VIR_First-Aid_Kit}}', label: '{{VIR_First-Aid_Kit_Label}}', externalId: GANCHO-2, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_3:
    __construct: { title: '{{VIR_Warning_Signals}}', label: '{{VIR_Warning_Signals_Label}}', externalId: GANCHO-3, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_4:
    __construct: { title: '{{VIR_Cabin_Condition}}', label: '{{VIR_Cabin_Condition_Label}}', externalId: GANCHO-4, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_5:
    __construct: { title: '{{VIR_Windshield_Condition}}', label: '{{VIR_Windshield_Condition_Label}}', externalId: GANCHO-5, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_6:
    __construct: { title: '{{VIR_Mirror_Condition}}', label: '{{VIR_Mirror_Condition_Label}}', externalId: GANCHO-6, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_7:
    __construct: { title: '{{VIR_Seatbelt_Condition}}', label: '{{VIR_Seatbelt_Condition_Label}}', externalId: GANCHO-7, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_8:
    __construct: { title: '{{VIR_Cabin_Steps}}', label: '{{VIR_Cabin_Steps_Label}}', externalId: GANCHO-8, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_9:
    __construct: { title: '{{VIR_Hot_Zone_Accumulation}}', label: '{{VIR_Hot_Zone_Accumulation_Label}}', externalId: GANCHO-9, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_10:
    __construct: { title: '{{VIR_Engine_Fluid_Levels}}', label: '{{VIR_Engine_Fluid_Levels_Label}}', externalId: GANCHO-10, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_11:
    __construct: { title: '{{VIR_Tire_Condition}}', label: '{{VIR_Tire_Condition_Label}}', externalId: GANCHO-11, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_12:
    __construct: { title: '{{VIR_Chassis_Condition}}', label: '{{VIR_Chassis_Condition_Label}}', externalId: GANCHO-12, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_13:
    __construct: { title: '{{VIR_License_Plate_Condition}}', label: '{{VIR_License_Plate_Condition_Label}}', externalId: GANCHO-13, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_14:
    __construct: { title: '{{VIR_Hook_Equipment_Condition}}', label: '{{VIR_Hook_Equipment_Condition_Label}}', externalId: GANCHO-14, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_15:
    __construct: { title: '{{VIR_Ampliroll_Hook_Condition}}', label: '{{VIR_Ampliroll_Hook_Condition_Label}}', externalId: GANCHO-15, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_16:
    __construct: { title: '{{VIR_Rokinger_Hitch_Condition}}', label: '{{VIR_Rokinger_Hitch_Condition_Label}}', externalId: GANCHO-16, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_17:
    __construct: { title: '{{VIR_Fuel/Energy_Level}}', label: '{{VIR_Fuel/Energy_Level_Label}}', externalId: GANCHO-17, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_18:
    __construct: { title: '{{VIR_AdBlue_Level}}', label: '{{VIR_AdBlue_Level_Label}}', externalId: GANCHO-18, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_19:
    __construct: { title: '{{VIR_Dashboard_Lights}}', label: '{{VIR_Dashboard_Lights_Label}}', externalId: GANCHO-19, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_20:
    __construct: { title: '{{VIR_Minimum_Lighting}}', label: '{{VIR_Minimum_Lighting_Label}}', externalId: GANCHO-20, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_21:
    __construct: { title: '{{VIR_Rotary_Lights_Function}}', label: '{{VIR_Rotary_Lights_Function_Label}}', externalId: GANCHO-21, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_22:
    __construct: { title: '{{VIR_Reverse_Beep}}', label: '{{VIR_Reverse_Beep_Label}}', externalId: GANCHO-22, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_23:
    __construct: { title: '{{VIR_Reverse_Camera_Function}}', label: '{{VIR_Reverse_Camera_Function_Label}}', externalId: GANCHO-23, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_24:
    __construct: { title: '{{VIR_Major_Air_Leaks}}', label: '{{VIR_Major_Air_Leaks_Label}}', externalId: GANCHO-24, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_25:
    __construct: { title: '{{VIR_Abnormal_Noises}}', label: '{{VIR_Abnormal_Noises_Label}}', externalId: GANCHO-25, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_26:
    __construct: { title: '{{VIR_Fluid_Loss}}', label: '{{VIR_Fluid_Loss_Label}}', externalId: GANCHO-26, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_27:
    __construct: { title: '{{VIR_Emergency_Stop_Function}}', label: '{{VIR_Emergency_Stop_Function_Label}}', externalId: GANCHO-27, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_28:
    __construct: { title: '{{VIR_Locking_Claws_Function}}', label: '{{VIR_Locking_Claws_Function_Label}}', externalId: GANCHO-28, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_29:
    __construct: { title: '{{VIR_Suspension_Roller}}', label: '{{VIR_Suspension_Roller_Label}}', externalId: GANCHO-29, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_30:
    __construct: { title: '{{VIR_Crane_Condition}}', label: '{{VIR_Crane_Condition_Label}}', externalId: GANCHO-30, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_31:
    __construct: { title: '{{VIR_Grapple/Pincer_Condition}}', label: '{{VIR_Grapple/Pincer_Condition_Label}}', externalId: GANCHO-31, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_32:
    __construct: { title: '{{VIR_Crane_Controls_Condition}}', label: '{{VIR_Crane_Controls_Condition_Label}}', externalId: GANCHO-32, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_33:
    __construct: { title: '{{VIR_Crane_Stabilizers_Condition}}', label: '{{VIR_Crane_Stabilizers_Condition_Label}}', externalId: GANCHO-33, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_34:
    __construct: { title: '{{VIR_Fire_Extinguishers}}', label: '{{VIR_Fire_Extinguishers_Label}}', externalId: CADENAS-1, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_35:
    __construct: { title: '{{VIR_First-Aid_Kit}}', label: '{{VIR_First-Aid_Kit_Label}}', externalId: CADENAS-2, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_36:
    __construct: { title: '{{VIR_Warning_Signals}}', label: '{{VIR_Warning_Signals_Label}}', externalId: CADENAS-3, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_37:
    __construct: { title: '{{VIR_Cabin_Condition}}', label: '{{VIR_Cabin_Condition_Label}}', externalId: CADENAS-4, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_38:
    __construct: { title: '{{VIR_Windshield_Condition}}', label: '{{VIR_Windshield_Condition_Label}}', externalId: CADENAS-5, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_39:
    __construct: { title: '{{VIR_Mirror_Condition}}', label: '{{VIR_Mirror_Condition_Label}}', externalId: CADENAS-6, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_40:
    __construct: { title: '{{VIR_Seatbelt_Condition}}', label: '{{VIR_Seatbelt_Condition_Label}}', externalId: CADENAS-7, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_41:
    __construct: { title: '{{VIR_Cabin_Steps}}', label: '{{VIR_Cabin_Steps_Label}}', externalId: CADENAS-8, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_42:
    __construct: { title: '{{VIR_Hot_Zone_Accumulation}}', label: '{{VIR_Hot_Zone_Accumulation_Label}}', externalId: CADENAS-9, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_43:
    __construct: { title: '{{VIR_Engine_Fluid_Levels}}', label: '{{VIR_Engine_Fluid_Levels_Label}}', externalId: CADENAS-10, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_44:
    __construct: { title: '{{VIR_Tire_Condition}}', label: '{{VIR_Tire_Condition_Label}}', externalId: CADENAS-11, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_45:
    __construct: { title: '{{VIR_Vehicle_Chassis_Condition}}', label: '{{VIR_Vehicle_Chassis_Condition_Label}}', externalId: CADENAS-12, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_46:
    __construct: { title: '{{VIR_License_Plate_Condition}}', label: '{{VIR_License_Plate_Condition_Label}}', externalId: CADENAS-13, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_47:
    __construct: { title: '{{VIR_Chains_&_Hooks}}', label: '{{VIR_Chains_&_Hooks_Label}}', externalId: CADENAS-14, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_48:
    __construct: { title: '{{VIR_Equipment_General_Condition}}', label: '{{VIR_Equipment_General_Condition_Label}}', externalId: CADENAS-15, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_49:
    __construct: { title: '{{VIR_Platform_Floor}}', label: '{{VIR_Platform_Floor_Label}}', externalId: CADENAS-16, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_50:
    __construct: { title: '{{VIR_Tipping_Hooks}}', label: '{{VIR_Tipping_Hooks_Label}}', externalId: CADENAS-17, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_51:
    __construct: { title: '{{VIR_Fuel/Energy_Level}}', label: '{{VIR_Fuel/Energy_Level_Label}}', externalId: CADENAS-18, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_52:
    __construct: { title: '{{VIR_AdBlue_Level}}', label: '{{VIR_AdBlue_Level_Label}}', externalId: CADENAS-19, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_53:
    __construct: { title: '{{VIR_Dashboard_Lights}}', label: '{{VIR_Dashboard_Lights_Label}}', externalId: CADENAS-20, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_54:
    __construct: { title: '{{VIR_Minimum_Lighting}}', label: '{{VIR_Minimum_Lighting_Label}}', externalId: CADENAS-21, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_55:
    __construct: { title: '{{VIR_Rotary_Lights_Function}}', label: '{{VIR_Rotary_Lights_Function_Label}}', externalId: CADENAS-22, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_56:
    __construct: { title: '{{VIR_Reverse_Beep}}', label: '{{VIR_Reverse_Beep_Label}}', externalId: CADENAS-23, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_57:
    __construct: { title: '{{VIR_Major_Air_Leaks}}', label: '{{VIR_Major_Air_Leaks_Label}}', externalId: CADENAS-24, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_58:
    __construct: { title: '{{VIR_Abnormal_Noises}}', label: '{{VIR_Abnormal_Noises_Label}}', externalId: CADENAS-25, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_59:
    __construct: { title: '{{VIR_Fluid_Loss}}', label: '{{VIR_Fluid_Loss_Label}}', externalId: CADENAS-26, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_60:
    __construct: { title: '{{VIR_Stabilizer_Condition}}', label: '{{VIR_Stabilizer_Condition_Label}}', externalId: CADENAS-27, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_61:
    __construct: { title: '{{VIR_Telescopic_Arms}}', label: '{{VIR_Telescopic_Arms_Label}}', externalId: CADENAS-28, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_62:
    __construct: { title: '{{VIR_Emergency_Stop_Function}}', label: '{{VIR_Emergency_Stop_Function_Label}}', externalId: CADENAS-29, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_63:
    __construct: { title: '{{VIR_Fire_Extinguishers}}', label: '{{VIR_Fire_Extinguishers_Label}}', externalId: REC-TRASERA-1, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_64:
    __construct: { title: '{{VIR_First-Aid_Kit}}', label: '{{VIR_First-Aid_Kit_Label}}', externalId: REC-TRASERA-2, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_65:
    __construct: { title: '{{VIR_Warning_Signals}}', label: '{{VIR_Warning_Signals_Label}}', externalId: REC-TRASERA-3, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_66:
    __construct: { title: '{{VIR_Cabin_Condition}}', label: '{{VIR_Cabin_Condition_Label}}', externalId: REC-TRASERA-4, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_67:
    __construct: { title: '{{VIR_Windshield_Condition}}', label: '{{VIR_Windshield_Condition_Label}}', externalId: REC-TRASERA-5, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_68:
    __construct: { title: '{{VIR_Mirror_Condition}}', label: '{{VIR_Mirror_Condition_Label}}', externalId: REC-TRASERA-6, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_69:
    __construct: { title: '{{VIR_Seatbelt_Condition}}', label: '{{VIR_Seatbelt_Condition_Label}}', externalId: REC-TRASERA-7, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_70:
    __construct: { title: '{{VIR_Cabin_Steps}}', label: '{{VIR_Cabin_Steps_Label}}', externalId: REC-TRASERA-8, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_71:
    __construct: { title: '{{VIR_Hot_Zone_Accumulation}}', label: '{{VIR_Hot_Zone_Accumulation_Label}}', externalId: REC-TRASERA-9, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_72:
    __construct: { title: '{{VIR_Engine_Fluid_Levels}}', label: '{{VIR_Engine_Fluid_Levels_Label}}', externalId: REC-TRASERA-10, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_73:
    __construct: { title: '{{VIR_Tire_Condition}}', label: '{{VIR_Tire_Condition_Label}}', externalId: REC-TRASERA-11, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_74:
    __construct: { title: '{{VIR_Chassis_Condition}}', label: '{{VIR_Chassis_Condition_Label}}', externalId: REC-TRASERA-12, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_75:
    __construct: { title: '{{VIR_License_Plate_Condition}}', label: '{{VIR_License_Plate_Condition_Label}}', externalId: REC-TRASERA-13, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_76:
    __construct: { title: '{{VIR_Rear_Step_Condition}}', label: '{{VIR_Rear_Step_Condition_Label}}', externalId: REC-TRASERA-14, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_77:
    __construct: { title: '{{VIR_Body_General_Condition}}', label: '{{VIR_Body_General_Condition_Label}}', externalId: REC-TRASERA-15, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_78:
    __construct: { title: '{{VIR_Leachate/Liquid_Loss}}', label: '{{VIR_Leachate/Liquid_Loss_Label}}', externalId: REC-TRASERA-16, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_79:
    __construct: { title: '{{VIR_Fuel/Energy_Level}}', label: '{{VIR_Fuel/Energy_Level_Label}}', externalId: REC-TRASERA-17, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_80:
    __construct: { title: '{{VIR_AdBlue_Level}}', label: '{{VIR_AdBlue_Level_Label}}', externalId: REC-TRASERA-18, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_81:
    __construct: { title: '{{VIR_Dashboard_Lights}}', label: '{{VIR_Dashboard_Lights_Label}}', externalId: REC-TRASERA-19, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_82:
    __construct: { title: '{{VIR_Minimum_Lighting_Check}}', label: '{{VIR_Minimum_Lighting_Check_Label}}', externalId: REC-TRASERA-20, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_83:
    __construct: { title: '{{VIR_Reverse_Beep}}', label: '{{VIR_Reverse_Beep_Label}}', externalId: REC-TRASERA-21, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_84:
    __construct: { title: '{{VIR_Reverse_Camera_Function}}', label: '{{VIR_Reverse_Camera_Function_Label}}', externalId: REC-TRASERA-22, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_85:
    __construct: { title: '{{VIR_Major_Air_Leaks}}', label: '{{VIR_Major_Air_Leaks_Label}}', externalId: REC-TRASERA-23, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_86:
    __construct: { title: '{{VIR_Abnormal_Noises}}', label: '{{VIR_Abnormal_Noises_Label}}', externalId: REC-TRASERA-24, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_87:
    __construct: { title: '{{VIR_Fluid_Loss}}', label: '{{VIR_Fluid_Loss_Label}}', externalId: REC-TRASERA-25, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_88:
    __construct: { title: '{{VIR_Rotary_Lights_Function}}', label: '{{VIR_Rotary_Lights_Function_Label}}', externalId: REC-TRASERA-26, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_89:
    __construct: { title: '{{VIR_Device_Functionality}}', label: '{{VIR_Device_Functionality_Label}}', externalId: REC-TRASERA-27, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_90:
    __construct: { title: '{{VIR_Lifter_General_Condition}}', label: '{{VIR_Lifter_General_Condition_Label}}', externalId: REC-TRASERA-28, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: false }
  es_EquipmentComponentConf_91:
    __construct: { title: '{{VIR_Emergency_Stop_Function}}', label: '{{VIR_Emergency_Stop_Function_Label}}', externalId: REC-TRASERA-29, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_92:
    __construct: { title: '{{VIR_Crane_Condition}}', label: '{{VIR_Crane_Condition_Label}}', externalId: REC-TRASERA-30, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
  es_EquipmentComponentConf_93:
    __construct: { title: '{{VIR_Crane_Stabilizers_Condition}}', label: '{{VIR_Crane_Stabilizers_Condition_Label}}', externalId: REC-TRASERA-31, valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>', critical: true }
