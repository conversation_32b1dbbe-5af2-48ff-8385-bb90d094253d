App\Domain\Entity\EquipmentConfig:
  es_EquipmentConf_ES_1:
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    id: '1ab74673-497e-4961-ae1b-888df5205246'
    tenant: '<getSpainTenant()>'
  es_EquipmentConf_ES_2:
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ100)>'
    id: '52aa3db6-9466-4aa1-a158-69f1a4d8ff5e'
    tenant: '<getSpainTenant()>'
  es_EquipmentConf_ES_3:
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ101)>'
    id: '3bf431fe-bfd1-4f3f-bf0c-6bb20b94ac6d'
    tenant: '<getSpainTenant()>'
