App\Domain\Entity\TourDataConfig:
  lu_int_TourConf_LU_INT_1:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    id: 'd0007f2c-f590-43cd-b5ac-80ef5cd85ba9'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_2:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
    id: 'a36b58c4-41c4-4f03-b407-f641a83b5eb0'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_3:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::FL)>'
    id: '72eee05c-45fb-44f4-9cda-12f043ad330d'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_4:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::GAK)>'
    id: '7cc5ac3c-add8-425f-b7dd-7a8a92f24719'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_5:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::HL)>'
    id: '783cd6b3-7f13-4505-abe8-e19108283026'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_6:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::KMA)>'
    id: '9c99870a-ad6f-47aa-9203-2a1b2bcfa1d0'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_7:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::LEI)>'
    id: 'f62d4508-c18a-44dd-9db2-aeb43ced8bd0'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_8:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::PRI)>'
    id: 'c3ee880a-339d-407a-92ef-f7176a405e8c'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_9:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SFA)>'
    id: '1df0f303-b5c5-4f3c-8f50-e3cb6a16291d'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_10:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SL)>'
    id: '08a0ba39-542c-4b6d-9de7-16c63d6b964f'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_11:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SON)>'
    id: '51bbc043-fa4a-4c24-8f16-c8ed1ced26ee'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_12:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::STL)>'
    id: 'be3c029b-00fe-45fc-a6ba-73fc11325a75'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_13:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SUB)>'
    id: '18375bc0-e4de-4a65-8776-a34d90dc9ca3'
    tenant: '<getGermanyTenant()>'
  lu_int_TourConf_LU_INT_14:
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SWA)>'
    id: 'c7d086a1-bf93-46ff-a28c-f22a34d8b49d'
    tenant: '<getGermanyTenant()>'
