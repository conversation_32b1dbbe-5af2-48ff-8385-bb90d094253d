App\Domain\Entity\DefaultTaskGroup:
  nl_DefTG_NL_1:
    orderTypeConfig: '@nl_OrderConf_NL_1'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '974717a1-3b1b-4da4-b7ed-f858b83d4e4e'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_2:
    orderTypeConfig: '@nl_OrderConf_NL_2'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'a8fa789a-1cfa-4815-991a-6f7181100c0b'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_3:
    orderTypeConfig: '@nl_OrderConf_NL_2'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: 'f6619896-9081-4e1d-9d85-c1d6ebf2b3e4'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@nl_DefTGR_NL_1']
  nl_DefTG_NL_4:
    orderTypeConfig: '@nl_OrderConf_NL_2'
    title: '{{Customer}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '4e5c4318-4e7b-4d64-af89-4730bd0c477a'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@nl_DefTGR_NL_2']
  nl_DefTG_NL_5:
    orderTypeConfig: '@nl_OrderConf_NL_3'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: 'b067eb21-dd01-470f-869b-957936883657'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_6:
    orderTypeConfig: '@nl_OrderConf_NL_4'
    title: '{{Customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '85a4d2f7-0334-4f7b-a08f-4e0886770cb2'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_7:
    orderTypeConfig: '@nl_OrderConf_NL_4'
    title: '{{Waste_disposal_plant}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
    id: '0d98da04-e705-49e0-ab36-c6ba08596a81'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
    rules: ['@nl_DefTGR_NL_3']
  nl_DefTG_NL_8:
    defaultInterruption: '@nl_DefInt_NL_1'
    title: '{{Break}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '9c33855a-4272-4f65-93ec-b8971b99aa99'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_9:
    defaultInterruption: '@nl_DefInt_NL_2'
    title: '{{Waiting_time}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '391112e2-a1b1-438e-a995-7330d911c94f'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_10:
    defaultInterruption: '@nl_DefInt_NL_3'
    title: '{{Other}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '0adc7917-5ee3-42a7-9800-0c1210e5e450'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_11:
    defaultInterruption: '@nl_DefInt_NL_4'
    title: '{{Vehicle_inspection}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'ae749e13-e510-4964-beba-94d31ab7f4e9'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_12:
    defaultInterruption: '@nl_DefInt_NL_5'
    title: '{{Breakdown}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '22b6aac6-1f7b-4e97-be6a-0eb53b8ab05b'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_13:
    defaultInterruption: '@nl_DefInt_NL_6'
    title: '{{workshop_maintenance}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'be12b783-5670-4ce3-a40b-28069ac3b596'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_14:
    defaultInterruption: '@nl_DefInt_NL_7'
    title: '{{Traffic_jam}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '47018c80-1732-4638-aa17-33a437ba63cb'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_15:
    defaultInterruption: '@nl_DefInt_NL_8'
    title: '{{Trailer_handling}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '4eb1354d-43c7-490b-b1fb-8d777fd30271'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_16:
    defaultInterruption: '@nl_DefInt_NL_9'
    title: '{{Fueling}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '7c2ae051-f71c-4a2c-bf81-165f56f2f552'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_17:
    defaultInterruption: '@nl_DefInt_NL_10'
    title: '{{Washing}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'b7c4d98c-369a-4478-a7fd-b626da20659e'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_18:
    defaultInterruption: '@nl_DefInt_NL_11'
    title: '{{Load_Unload}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '840d22cb-de30-488a-85e9-741acb82bbf8'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_19:
    defaultNote: '@nl_DefNote_NL_1'
    title: '{{New_sequence_in_route}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '70a21afb-2e84-4950-9154-20516ffe982a'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_20:
    defaultNote: '@nl_DefNote_NL_2'
    title: '{{Container_damaged}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'b54e3bd6-77c8-4a3b-9c04-4e8202707a6b'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_21:
    defaultNote: '@nl_DefNote_NL_3'
    title: '{{Container_to_heavy}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '2a81ff90-3afc-4eca-98a5-30a1d6754f0e'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_22:
    defaultNote: '@nl_DefNote_NL_4'
    title: '{{Much_extra_waste}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '45586c2a-2b2d-46d9-be85-b3a96b2c6690'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_23:
    defaultNote: '@nl_DefNote_NL_5'
    title: '{{Waiting_for_customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '8adc82b8-3994-46c2-b0c9-df22992d7921'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_24:
    defaultNote: '@nl_DefNote_NL_6'
    title: '{{Waiting_at_dispoal_site}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '539e35f7-07f1-43ab-89c5-06d1cb4bf964'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_25:
    defaultTermination: '@nl_DefTerm_NL_29'
    title: '{{Full_down}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'ef6ea4da-1370-4c36-88c8-701441b07177'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_26:
    defaultNote: '@nl_DefNote_NL_8'
    title: '{{Other_remarks}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'c6610b34-9850-45bc-8a56-346b1ee228f5'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_27:
    defaultTermination: '@nl_DefTerm_NL_1'
    title: '{{Not_for_this_routetruck}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '50175bda-9b2a-4dc0-a954-31ba97b1978a'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_28:
    defaultTermination: '@nl_DefTerm_NL_2'
    title: '{{Wrong_day_in_order}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '459ab117-14be-4d75-bcc7-798599b5ceb8'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_29:
    defaultTermination: '@nl_DefTerm_NL_3'
    title: '{{Wrong_waste_in_order}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '11ed6f9c-30f2-4c79-b565-079c3ef6f028'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_30:
    defaultTermination: '@nl_DefTerm_NL_4'
    title: '{{Holiday_customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '6bb2f53f-c137-4daf-99eb-bb31333d07e9'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_31:
    defaultTermination: '@nl_DefTerm_NL_5'
    title: '{{Bankrupt_customer}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'f52d90e4-270c-41e4-a638-17db274d03db'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_32:
    defaultTermination: '@nl_DefTerm_NL_6'
    title: '{{Request_dispatcher}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'f38b58d1-9167-41b0-a14f-1453a26ce8a1'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_33:
    defaultTermination: '@nl_DefTerm_NL_7'
    title: '{{Replanning_necessary_other_reason}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '39d18e4d-9ace-4517-8e60-5122c599b982'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_34:
    defaultTermination: '@nl_DefTerm_NL_8'
    title: '{{Container_Waste_unreachable}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '514649e5-1357-4959-afcb-d16a633dc408'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_35:
    defaultTermination: '@nl_DefTerm_NL_9'
    title: '{{Container_Waste_not_offered}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'e7074cd2-27ab-418d-9bf5-b6af3ed5b9a3'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_36:
    defaultTermination: '@nl_DefTerm_NL_10'
    title: '{{Container_locked}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '0fe3aa0b-1d84-4379-8708-8ccac5e872cf'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_37:
    defaultTermination: '@nl_DefTerm_NL_11'
    title: '{{Container_is_empty}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '9e8bdb20-bc49-47aa-bdc1-48ddcebaebb8'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_38:
    defaultTermination: '@nl_DefTerm_NL_12'
    title: '{{Customers_demand}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '4b5c137a-5261-4b54-8788-30d724ed5aef'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_39:
    defaultTermination: '@nl_DefTerm_NL_13'
    title: '{{Dispatchers_demand}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'd6548da5-2ba8-41a2-82cb-144ec0e8d5e9'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_40:
    defaultTermination: '@nl_DefTerm_NL_14'
    title: '{{Customer_is_not_present}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'b97a39f8-632c-4764-bb31-2ec13fb7a953'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_41:
    defaultTermination: '@nl_DefTerm_NL_15'
    title: '{{Customer_Stop_Service}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'bf78c942-acb2-4b2c-814a-2c2985dde11c'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_42:
    defaultTermination: '@nl_DefTerm_NL_16'
    title: '{{Wrong_waste_offered}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '126404c0-40ec-49fd-8896-047d8d05dedf'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_43:
    defaultTermination: '@nl_DefTerm_NL_17'
    title: '{{Container_broken}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'faee2a91-3034-4e9d-9dc1-ff370de3edd8'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_44:
    defaultTermination: '@nl_DefTerm_NL_18'
    title: '{{Wrong_loadingsystem}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '36a20c12-ec38-47fa-9503-ddc79636211f'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_45:
    defaultTermination: '@nl_DefTerm_NL_19'
    title: '{{Customer_holiday}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '103bbc66-81d0-4827-b36e-8fdc3197fca4'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_46:
    defaultTermination: '@nl_DefTerm_NL_20'
    title: '{{Received_wrong_order}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'e4cfc7da-2914-4f70-ae3f-43cc157773c4'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_47:
    defaultTermination: '@nl_DefTerm_NL_21'
    title: '{{Customer_bankrupt}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '5665ced1-c884-4d50-89a3-579c2b921723'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_48:
    defaultTermination: '@nl_DefTerm_NL_22'
    title: '{{Heavy_container_or_Wrong_loaded}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '449acfcc-efa5-40df-8076-ef5b1de17827'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_49:
    defaultTermination: '@nl_DefTerm_NL_23'
    title: '{{Other_remarks}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '528735eb-2040-4680-88b6-165bd66359f5'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_50:
    defaultTermination: '@nl_DefTerm_NL_24'
    title: '{{End_of_day}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'aeca19de-dff9-43d4-9d10-8398aa43a7d4'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_51:
    defaultTermination: '@nl_DefTerm_NL_25'
    title: '{{Dispatchers_request}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '3aa8d7c6-b0ab-4d45-85e4-f90f4d315af8'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_52:
    defaultTermination: '@nl_DefTerm_NL_26'
    title: '{{Truck_breakdown}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'f594dfb7-8c51-481b-a94e-8d93af3255c8'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_53:
    defaultTermination: '@nl_DefTerm_NL_27'
    title: '{{Other_reason}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '9ee227e4-a4af-4657-b84c-8c4a13d9769f'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_54:
    tourDataConfig: '@nl_TourConf_NL_1'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'f3c4e301-84fa-4d95-9b9d-e457d9115e32'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_55:
    tourDataConfig: '@nl_TourConf_NL_1'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '77ff6c9f-9bff-4424-bc70-49f6b22fb8d9'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_57:
    defaultNote: '@nl_DefNote_NL_9'
    title: '{{Wrong_waste_offered}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'f95e403e-1ac0-4aef-9075-acac8fb3de00'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_58:
    defaultInterruption: '@nl_DefInt_NL_12'
    title: '{{Driver_meeting}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '47d4775c-2f9f-4c4c-8608-92db1860e18d'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_59:
    defaultTermination: '@nl_DefTerm_NL_28'
    title: '{{Container_is_damaged}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '9ab8ed28-0b2a-42fe-8e29-6a6fd976cecf'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_60:
    tourDataConfig: '@nl_TourConf_NL_2'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '602aabc9-86f4-4ef7-b875-82549c084962'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_61:
    tourDataConfig: '@nl_TourConf_NL_3'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '61e5231c-762c-45e9-a817-6f5a42b68bbd'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_62:
    tourDataConfig: '@nl_TourConf_NL_4'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '13d0d594-815b-4142-8911-41ea2b90829a'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_63:
    tourDataConfig: '@nl_TourConf_NL_5'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'a025f812-ba4b-4aa7-88fb-3a59a3b74c30'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_64:
    tourDataConfig: '@nl_TourConf_NL_6'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '94f3eddb-39b6-4337-b164-15b4d43c0b91'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_65:
    tourDataConfig: '@nl_TourConf_NL_7'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '894d75da-91cb-4a4d-81d1-bf1c7fd5cdbd'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_66:
    tourDataConfig: '@nl_TourConf_NL_8'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '9786fdd4-f75d-4345-b9a6-ad0f4250241e'
    tenant: '<getNetherlandsTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    trackable: true
  nl_DefTG_NL_67:
    tourDataConfig: '@nl_TourConf_NL_2'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'c8e3da8a-c711-4bb5-9efd-a412835b237b'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_68:
    tourDataConfig: '@nl_TourConf_NL_2'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'd7ed83e2-839a-40ae-871e-dabb8899d119'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_69:
    tourDataConfig: '@nl_TourConf_NL_3'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '84f0f2e7-8059-473a-82e8-c5ccb2e21369'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_70:
    tourDataConfig: '@nl_TourConf_NL_3'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '4562d0c6-def7-434a-83e9-064bca62f95f'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_71:
    tourDataConfig: '@nl_TourConf_NL_4'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '7bfecb30-9160-4534-9397-e92011361319'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_72:
    tourDataConfig: '@nl_TourConf_NL_4'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '3f072373-044b-48ca-b077-5f1ff4d348d9'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_73:
    tourDataConfig: '@nl_TourConf_NL_5'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '6bf9db02-bb18-4296-8ef2-8313f34faf53'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_74:
    tourDataConfig: '@nl_TourConf_NL_5'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'e70b19b0-ae53-4c13-b7cb-4ce96ee8fe68'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_75:
    tourDataConfig: '@nl_TourConf_NL_6'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'b869d6e7-0983-42aa-9400-d49a37b287ca'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_76:
    tourDataConfig: '@nl_TourConf_NL_6'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'a490b239-398c-4c79-8198-f52cbbeaca6d'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_77:
    tourDataConfig: '@nl_TourConf_NL_7'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'fd7675cd-7fe3-4dcb-8d20-07b5adfb7400'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_78:
    tourDataConfig: '@nl_TourConf_NL_7'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '659ca063-e236-4fbd-a283-081af610886b'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_79:
    tourDataConfig: '@nl_TourConf_NL_8'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'd93c20b9-b368-4866-87ec-10140fc77fa5'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
  nl_DefTG_NL_80:
    tourDataConfig: '@nl_TourConf_NL_8'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '1b384780-f796-4666-bea7-33603027fbb0'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
    trackable: true
  nl_DefTG_NL_81:
    equipmentConfig: '@nl_EquipmentConf_NL_1'
    title: '{{Vehicle}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::EQUIPMENT)>'
    id: 'f41b91b7-4628-4f43-84ba-3e1f843db045'
    tenant: '<getNetherlandsTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
    trackable: true
