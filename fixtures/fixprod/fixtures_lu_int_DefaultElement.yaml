App\Domain\Entity\ValueObject\Element:
  lu_int_DefElem_LU_INT_1_1:
    __construct: { id: 261eb097-f09f-5d4b-a1c5-0ac99587d7ba, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_int_DefElemOption_LU_INT_1', '@lu_int_DefElemOption_LU_INT_2'] }
  lu_int_DefElem_LU_INT_2_1:
    __construct: { id: ba8edbce-29ca-5ef2-b397-06e3a2f1d005, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_2:
    __construct: { id: 09170cd7-a125-531c-95ca-92c0fe475c6e, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_3:
    __construct: { id: 20b70dce-d437-5fec-943a-faa3802a4d20, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_4:
    __construct: { id: 368471e4-39a2-5d54-9451-9b6fde570c5c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_5:
    __construct: { id: b242a0be-6586-5d75-8b47-1073219a3e4c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_6:
    __construct: { id: ad5ae36e-e35d-5044-830f-91453d976bdf, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_7:
    __construct: { id: 7a5c53ac-66c1-5d30-a20e-f3a67fde13fc, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_8:
    __construct: { id: 0430d863-a781-5434-ab06-3ec42fd91f4f, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_9:
    __construct: { id: b7a135b2-ddc5-5b23-ab94-0f3bd696baae, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_10:
    __construct: { id: 3eca1091-862c-540b-9652-1d4b3bcaa7d5, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_11:
    __construct: { id: b10eb262-596c-5991-97cf-62de0e519bf9, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_12:
    __construct: { id: db577ebf-6227-54a0-8cce-03382d7e0508, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_13:
    __construct: { id: 5f1e1eed-9215-5284-bafa-771ef8b0e84d, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_14:
    __construct: { id: 4a4797ed-24d2-5683-a17e-54db8f5477d0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_2_15:
    __construct: { id: a1bbe471-0712-55b5-9caa-a857284b6334, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_1:
    __construct: { id: 9be8dd4f-1712-5bde-953d-5ed8543bfb85, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_2:
    __construct: { id: 04d00569-3b17-5145-8968-2c915f9aed8e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_3:
    __construct: { id: 0db36cd0-2b50-527e-9f6e-d2bdd9a0ef69, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_4:
    __construct: { id: 11df1e85-8828-58e1-bd21-bc4557baddaf, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_5:
    __construct: { id: 40bdff1c-d920-513a-a93e-b88be0e68fcf, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_6:
    __construct: { id: 26c1bac9-2d92-56b6-ba31-18a859958070, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_7:
    __construct: { id: 729114a4-867b-50bc-b0b7-4a94a471dc5b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_8:
    __construct: { id: 9875ef05-49f8-5f7b-880c-b58317fe596d, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_9:
    __construct: { id: 8c7e1a4d-1e91-5e86-88b7-075fff1819a4, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_10:
    __construct: { id: 44045b05-ba64-5b5f-914c-6834df336b1c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_11:
    __construct: { id: 78675d26-f748-574e-99bb-01e88a1792c2, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_12:
    __construct: { id: 22dfcd0f-084e-5d95-8bb9-863ab0551375, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_13:
    __construct: { id: e883a32c-58f9-55d0-8c4d-68fa288e3916, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_14:
    __construct: { id: e379dbeb-b496-5259-a2d9-d8905f8c521a, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_15:
    __construct: { id: 2917c875-c9c1-5fd7-a9d2-97cf6138b11b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_16:
    __construct: { id: adafa2bc-8b2a-52c5-899d-f55cf7e4f82c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_17:
    __construct: { id: 03cd84e9-c389-58b4-8ad7-382e56433923, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_18:
    __construct: { id: 9e35bbbb-efe4-50ed-b590-54e2acddbc55, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_19:
    __construct: { id: d8d24b74-a9d0-5748-8f7e-d542bc98c8a5, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_20:
    __construct: { id: 5d11f3c1-6899-5bc0-95e4-9a606b6c6f38, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_21:
    __construct: { id: 662a7acf-71c6-5871-a53e-2e371c978650, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_22:
    __construct: { id: 5c0778fa-9e9c-5c18-927d-151a4443041e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_23:
    __construct: { id: 314ca118-91fc-599d-974b-6c548552272c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_24:
    __construct: { id: 7e012f20-81f3-5e85-b00f-af941e2b6746, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_25:
    __construct: { id: dbe4cec4-9ac1-55bd-a845-1363fe886c0c, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_26:
    __construct: { id: 5fe604fe-2849-52c5-aafd-cced64eb4cb4, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_27:
    __construct: { id: 06a2da51-d704-5712-ab62-6528a48193e4, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_28:
    __construct: { id: f555a8fe-a7a5-5dfb-a430-43e784e87071, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_29:
    __construct: { id: ed5e1868-2061-5dac-bbc8-d14adcec9414, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_30:
    __construct: { id: a25d63cc-e20e-581a-bd25-8423693d20ab, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_3_31:
    __construct: { id: 3a26faa8-104c-50c1-b08e-0acb6f8a546f, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_4_1:
    __construct: { id: a2961b8f-838e-5d0c-b673-02fc662f2dd0, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_4_2:
    __construct: { id: ee0ec69f-174d-5cc1-8900-e7d599974e0e, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_1:
    __construct: { id: 0c8637a2-2017-5dd6-ba73-70fa7751298b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_2:
    __construct: { id: 9df48d49-b0af-5ce1-990c-d4f1693fd30b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_3:
    __construct: { id: 5ed92e5a-18e6-54ca-934b-f17650ed357c, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_4:
    __construct: { id: d1bbdd64-10ba-597a-a08e-22f67fa12d17, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_5:
    __construct: { id: 54f44659-1f71-5aa4-a8e4-6d9939d16209, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_6:
    __construct: { id: 3e231f13-868e-5e15-96cf-37388eac14ac, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_7:
    __construct: { id: 7b052354-7ea8-5868-b810-176453c7f728, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_8:
    __construct: { id: d335cb65-efa7-577e-988d-50600e659fbf, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_9:
    __construct: { id: 04d6c78a-73e1-5e7e-b4f4-4c7fde68c371, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_10:
    __construct: { id: 634decdd-ef85-53b1-a1e4-ea66e3e2c65a, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_11:
    __construct: { id: ce3856bf-3cfb-5a8b-8d95-24fc508dbe1d, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_7_12:
    __construct: { id: 7174e425-02a8-593b-9e1a-6fb5308bea26, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_8_1:
    __construct: { id: 97258060-0f70-54b0-a8cb-a8ca621b9fef, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_2:
    __construct: { id: 462ddfa0-0762-5cd6-878e-2ce712264b14, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_3:
    __construct: { id: 624d367a-fcd8-5e49-a9ef-51d63395311f, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_4:
    __construct: { id: 0ae6a033-6c4e-55c0-978e-175c5659344b, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_5:
    __construct: { id: 56791ffc-fb06-51f7-9a53-bbd93bd977da, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_6:
    __construct: { id: e43ee5d6-9a6c-5f2e-87ba-81dd1d255dc0, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_7:
    __construct: { id: a572d986-2bcc-57c9-81ec-45e7d5152d02, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_8:
    __construct: { id: 7a1cfd75-9b46-55a0-b349-98a87574a7b3, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_9:
    __construct: { id: 86694f19-3df8-51da-98aa-b89d80ef9dc8, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_10:
    __construct: { id: b1dc8a34-8914-5547-946b-77ae289d5657, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_11:
    __construct: { id: 02c017dc-5d3e-5c06-a39c-7a32b7a200dc, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_8_12:
    __construct: { id: 8f00559f-df90-5601-a45e-cf4e88325fef, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@lu_int_DefElemOption_LU_INT_9', '@lu_int_DefElemOption_LU_INT_10'] }
  lu_int_DefElem_LU_INT_9_1:
    __construct: { id: b661ac31-4d07-5f2e-bc7f-54b465c9f29d, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_2:
    __construct: { id: 1bfe5e0b-7d33-5ec0-b051-60379d9394db, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_3:
    __construct: { id: 305bb071-c0cd-5b8e-96cc-23d2e55c6813, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_4:
    __construct: { id: 6ead2a69-b095-5866-956c-3d385810b566, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_5:
    __construct: { id: 97bae7aa-f690-561c-95de-49288f436142, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_6:
    __construct: { id: c46281eb-e421-5e17-bd0b-28345dc71d9a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_7:
    __construct: { id: 283ecc23-990d-5210-88ad-da168ee62628, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_8:
    __construct: { id: e930fcda-43ad-539d-b6b5-e60566a1242c, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_9:
    __construct: { id: 47347a2e-8365-5ba9-9bb2-a4c5e512dc12, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_10:
    __construct: { id: 0458fd0c-9ff0-5e76-a31e-9b44cb4a91bb, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_11:
    __construct: { id: 0e6069ee-8861-5ce4-adb9-8212eb95f541, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_9_12:
    __construct: { id: c8ee4830-4ff9-5530-ab17-3b304a5e2a8e, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^(\d{1,5})([.]\d{1,3})?$', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  lu_int_DefElem_LU_INT_10_1:
    __construct: { id: 2744b64e-ba79-552a-be7c-ea73beb66184, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_2:
    __construct: { id: beab2656-bbe4-52dd-9518-5f0bc1656332, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_3:
    __construct: { id: da4509a5-1071-5dfd-87ee-80db49f31dcf, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_4:
    __construct: { id: 4a74614f-ab78-54cd-b249-1b625456cdf3, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_5:
    __construct: { id: 3d346e56-9ec0-5cf3-a041-3c36d9fff5d9, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_6:
    __construct: { id: ef97d703-573f-596a-8868-63b51a32e841, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_7:
    __construct: { id: 566f85b5-417b-5427-adbc-5df0c4121039, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_8:
    __construct: { id: 5b47caca-b9ce-5f72-be0f-f11fc7204403, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_9:
    __construct: { id: 2b96922a-4f52-5945-8248-a68f42b6c89e, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_10:
    __construct: { id: a228f6f5-b3e4-51b1-8ee6-6f57f5ffc672, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_11:
    __construct: { id: 6a653f97-62c3-56f1-a93f-3420ea00353d, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_10_12:
    __construct: { id: cf87a8bf-d383-5b2e-b50d-fe2b1f17a515, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', sequenceNumber: 40 }
  lu_int_DefElem_LU_INT_11_1:
    __construct: { id: 54847425-42c4-5a1d-89f3-1d60c88e542d, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_11_2:
    __construct: { id: 44e61939-ebd5-5c88-a6b9-ad5427c08925, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  lu_int_DefElem_LU_INT_11_3:
    __construct: { id: 58da13a0-a2c0-561a-bbc1-17eff26cb5ed, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  lu_int_DefElem_LU_INT_11_4:
    __construct: { id: de46478a-9c37-57e0-9b29-dd18a7f09e59, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 20 }
  lu_int_DefElem_LU_INT_12_1:
    __construct: { id: 7354a509-e72e-5d76-87f0-b3e36b857a2f, referenceType: dieselAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_diesel_quantity_in_liters}}', label: '{{Please_enter_diesel_quantity_in_liters}}', valueNumberPrecision: 1, patternHint: '{{Quantity}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_13_1:
    __construct: { id: c875d616-685b-58fd-890d-044a5f54b710, referenceType: adBlueAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_AdBlue_quantity}}', label: '{{Please_enter_AdBlue_quantity}}', patternHint: '{{Quantity}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_14_1:
    __construct: { id: 5f128cde-5bfd-5318-9d98-46fefe9e8a8d, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_14_2:
    __construct: { id: 9d323eb1-885d-58cb-bdce-5a0e92bb70d7, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_14_3:
    __construct: { id: a5a352d4-f7ce-5126-882e-745c0a1ab710, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_14_4:
    __construct: { id: 990a86a3-d34d-510d-93ff-16aecc726329, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_14_5:
    __construct: { id: 73b98b68-2a85-592d-ba86-5d799ca24ae3, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_15_1:
    __construct: { id: e34ebcdb-6372-5915-b4ab-5a7f00caeef5, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_2:
    __construct: { id: 33273d10-760f-5a0c-a056-2aecbf47b665, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_3:
    __construct: { id: d3a19439-623d-5cce-be95-1aa62c82ef87, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_4:
    __construct: { id: 1aab0bd7-cbbc-5c14-baf5-4c4f3dab0003, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_5:
    __construct: { id: 64ebdf43-a2f4-56a6-aa4b-6f145adb1155, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_6:
    __construct: { id: f899c5f1-dfbb-57fb-ac24-38c1060ace00, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_7:
    __construct: { id: 1181f0a6-aa04-5b1d-a7bb-075ee52f5d6c, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_8:
    __construct: { id: 5faea4a9-196b-55dd-9283-0656a303b62e, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_9:
    __construct: { id: 6a924dbd-f018-5620-95f3-1a415a39318f, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_10:
    __construct: { id: 553f4f3d-a27e-5efa-a801-cd5fdcad2cd6, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_11:
    __construct: { id: 564c2915-5114-5253-8342-bc65f330abae, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_12:
    __construct: { id: 964982d0-5e22-5c44-8a86-89e6cce43e79, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_15_13:
    __construct: { id: c1dd27fb-c19d-58a8-b838-7da23a5d2b95, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_1:
    __construct: { id: e668c6d1-edea-5a0f-8b33-bb359b002d86, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_2:
    __construct: { id: 31d89799-71f6-5973-83d6-12ed314dd503, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_3:
    __construct: { id: 20452691-292d-5217-8789-dbd0f4c592a4, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_4:
    __construct: { id: 9618d7f7-f9db-5096-8ae8-70f6d1dee6df, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_5:
    __construct: { id: a311d29f-21f8-5055-af39-6fd7293d6d4c, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_6:
    __construct: { id: 1ef164a6-526b-555a-96fe-f89d679cb8b2, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_7:
    __construct: { id: c5ff31ae-a673-5a50-a3d8-872b5f868037, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_8:
    __construct: { id: fe4dbb33-467e-5c33-8eb9-0a9d1b4eaa5f, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_9:
    __construct: { id: 7e6c42d9-52a2-5712-8ce0-226f60a257a3, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_10:
    __construct: { id: 2deacfbe-1520-5846-b276-f169cb3eab32, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_11:
    __construct: { id: 2ed3a3a2-53c6-566a-b530-43dab6d314ed, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_12:
    __construct: { id: 4962afad-47ab-5a8d-8d7c-f2cc6077cc58, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_16_13:
    __construct: { id: b67237a3-2c25-5a4c-b70d-b623be58b3a8, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  lu_int_DefElem_LU_INT_23_1:
    __construct: { id: a12e368c-bd7d-5617-a472-8a5f07bef160, referenceType: personnelNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_your_personnel_Number}}', label: '{{Please_enter_your_personnel_Number}}', patternHint: '{{Please_enter_your_personnel_Number}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_24_1:
    __construct: { id: d8f78cef-c42c-577d-872e-270be77b9dce, referenceType: cleaningTime, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_the_needed_time_for_cleaning}}', label: '{{Please_enter_the_needed_time_for_cleaning}}', valueNumberPrecision: 2, patternHint: '{{Please_enter_the_needed_time_for_cleaning}}', sequenceNumber: 20 }
  lu_int_DefElem_LU_INT_25_1:
    __construct: { id: df0e7f96-6d7a-5421-b359-06fb71ccd2bb, referenceType: deliveryOfOpRes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_delivered_amount}}', label: '{{Please_enter_delivered_amount}}', patternHint: '{{Please_enter_delivered_amount}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_25_2:
    __construct: { id: 619b530b-5723-5b84-b4ad-8fda00fb28d9, referenceType: deliveryOfOpRes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_delivered_amount}}', label: '{{Please_enter_delivered_amount}}', patternHint: '{{Please_enter_delivered_amount}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_25_3:
    __construct: { id: c2750e18-2008-5dd7-b2be-baec3b3b00e0, referenceType: deliveryOfOpRes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_delivered_amount}}', label: '{{Please_enter_delivered_amount}}', patternHint: '{{Please_enter_delivered_amount}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_25_4:
    __construct: { id: 5d7b2eb6-1af2-5629-954a-75afdb6e25bf, referenceType: deliveryOfOpRes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_delivered_amount}}', label: '{{Please_enter_delivered_amount}}', patternHint: '{{Please_enter_delivered_amount}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_25_5:
    __construct: { id: f3868a38-a27b-5272-8174-4e557f1dd148, referenceType: deliveryOfOpRes, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_delivered_amount}}', label: '{{Please_enter_delivered_amount}}', patternHint: '{{Please_enter_delivered_amount}}', sequenceNumber: 10 }
  lu_int_DefElem_LU_INT_26_1:
    __construct: { id: 1292a878-02bb-5142-9ac7-a25deb7c3047, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@lu_int_DefElemOption_LU_INT_5', '@lu_int_DefElemOption_LU_INT_6'] }
  lu_int_DefElem_LU_INT_27_1:
    __construct: { id: 2a038f09-f618-5475-adab-5c5657c3f4f6, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@lu_int_DefElemOption_LU_INT_3', '@lu_int_DefElemOption_LU_INT_4'] }
  lu_int_DefElem_LU_INT_28_1:
    __construct: { id: 487dd663-e4fa-516d-8a89-3251a82dbdf9, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@lu_int_DefElemOption_LU_INT_7', '@lu_int_DefElemOption_LU_INT_8'] }
