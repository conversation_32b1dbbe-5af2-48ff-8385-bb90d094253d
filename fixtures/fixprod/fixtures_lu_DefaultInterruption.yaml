App\Domain\Entity\DefaultInterruption:
  lu_DefInt_LU_1:
    description: '{{Breakdown}}'
    id: '985f23a2-246c-4e97-a8ae-3d5f30a24bb9'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '1'
  lu_DefInt_LU_2:
    description: '{{Accident}}'
    id: 'd2e1570f-2b98-4ded-bdc7-bb8708a90fae'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '2'
  lu_DefInt_LU_3:
    description: '{{Break}}'
    id: '3354dae1-83fa-42d2-864e-f5dec3eccb2e'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '3'
  lu_DefInt_LU_4:
    description: '{{Other}}'
    id: '99597809-c257-4abe-87d4-3fbee56e3624'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::OTHER)>'
    externalId: '4'
  lu_DefInt_LU_5:
    description: '{{Refuel}}'
    id: 'b9193958-1eea-4bb5-a760-c2c2a3fc5253'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '5'
  lu_DefInt_LU_6:
    description: '{{Vehicle_washing}}'
    id: '365921c2-6ee4-49e9-bcd7-d55af9e34a87'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '6'
  lu_DefInt_LU_7:
    description: '{{Internal_handling}}'
    id: '438424d3-d00f-46a2-8fa5-5066f73e7210'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '7'
  lu_DefInt_LU_8:
    description: '{{Vehicle_maintenance}}'
    id: '7b455c39-fa08-4345-8b1e-32fbcf8542da'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '8'
  lu_DefInt_LU_9:
    description: '{{External_handling}}'
    id: 'f9ab43ef-b984-4181-b137-f700ff990a95'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '9'
  lu_DefInt_LU_10:
    description: '{{Pre-loading}}'
    id: '3ae1cd8c-50ab-4850-a1e3-2d37e9d52b3e'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '10'
  lu_DefInt_LU_11:
    description: '{{Way_home}}'
    id: 'd00bda24-a768-479a-bff8-44582d6078f2'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '11'
  lu_DefInt_LU_12:
    description: '{{Departure_check}}'
    id: 'a88626b6-d6d6-417d-bcf7-dbb830012dac'
    tenant: '<getLuxembourgTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
    externalId: '12'
