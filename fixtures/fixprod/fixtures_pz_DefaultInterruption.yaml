App\Domain\Entity\DefaultInterruption:
  pz_DefInt_1:
    description: '{{Break}}'
    externalId: '120'
    id: '2bbabbde-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  pz_DefInt_2:
    description: '{{Driver_meeting}}'
    externalId: '154'
    id: '2bbac1d8-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  pz_DefInt_3:
    description: '{{Other}}'
    externalId: '156'
    id: '2bbac2e6-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::OTHER)>'
  pz_DefInt_4:
    description: '{{Waiting_time}}'
    externalId: '122'
    id: '2bbac41c-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::WAITING)>'
  pz_DefInt_5:
    description: '{{Vehicle_inspection}}'
    externalId: '158'
    id: '2bbac520-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  pz_DefInt_6:
    description: '{{Workshop}}'
    externalId: '142'
    id: '2bbac61a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::GARAGE)>'
  pz_DefInt_7:
    description: '{{Refuel}}'
    externalId: '148'
    id: '2bbac71e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::REFUEL)>'
  pz_DefInt_8:
    description: '{{Traffic_jam}}'
    externalId: '140'
    id: '2bbac80e-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::TRAFFICJAM)>'
  pz_DefInt_9:
    description: '{{Vehicle_care}}'
    externalId: '144'
    id: '2bbac908-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  pz_DefInt_11:
    description: '{{Way_home}}'
    externalId: '162'
    id: '2bbace76-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  pz_DefInt_12:
    description: '{{Pre-loading}}'
    externalId: '152'
    id: '2bbacf7a-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
  pz_DefInt_13:
    description: '{{Container_back_to_customer}}'
    externalId: '164'
    id: '6e9c3ceb-18e5-46f2-94a0-90201e2c0ecd'
    tenant: '<getGermanyTenant()>'
    type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
