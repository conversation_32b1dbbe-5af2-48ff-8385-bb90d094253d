App\Domain\Entity\DefaultTaskGroup:
  lu_int_DefTG_LU_INT_1:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_1'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: 'ea052f4d-94c8-4100-bd6f-7ccc3e366d2a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_2:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_1'
    title: '{{approach_sweeping_area}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_APPROACH)>'
    id: 'd3589013-c774-41a7-820a-e2717d0f6f54'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_int_DefTGR_LU_INT_1']
  lu_int_DefTG_LU_INT_3:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_1'
    title: '{{sweeping}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_SERVICE)>'
    id: 'b17bb0b3-7f8e-43f1-8e5c-0cf25e345c0b'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_int_DefTGR_LU_INT_2', '@lu_int_DefTGR_LU_INT_3']
  lu_int_DefTG_LU_INT_4:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_1'
    title: '{{return_from_sweeping_area}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_RETURN)>'
    id: '89c86fa4-96f4-41c2-b4b4-15588de674db'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_int_DefTGR_LU_INT_4']
  lu_int_DefTG_LU_INT_5:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_2'
    title: '{{sweeping}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '347d5879-150c-4a91-9665-e84e370acf59'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_6:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_3'
    title: '{{Cleaning}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '1e69e68d-e8a0-42fe-8feb-b0adb7ab93e2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_7:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_4'
    title: '{{Cleaning assistance}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
    id: '8377506b-407f-4760-b230-b512fc691fc4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_8:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_5'
    title: '{{module/orders/option_start_button}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    id: '2323e572-1753-4e7f-a867-e83fc952b6ba'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_9:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_5'
    title: '{{approach_winter_service}}'
    sequenceNumber: '20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_APPROACH)>'
    id: 'b425365c-8f89-466d-813e-9f096d5613f3'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_int_DefTGR_LU_INT_5']
  lu_int_DefTG_LU_INT_10:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_5'
    title: '{{winter_service}}'
    sequenceNumber: '30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_SERVICE)>'
    id: '1f5974e6-2403-49f7-8cb6-ea9489b43b42'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_int_DefTGR_LU_INT_6', '@lu_int_DefTGR_LU_INT_7']
  lu_int_DefTG_LU_INT_11:
    orderTypeConfig: '@lu_int_OrderConf_LU_INT_5'
    title: '{{return_from_winter_service}}'
    sequenceNumber: '40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::MUNICIPAL_RETURN)>'
    id: '2af00f2a-c0e0-4c1a-b0b1-838ced9dc853'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
    rules: ['@lu_int_DefTGR_LU_INT_8']
  lu_int_DefTG_LU_INT_12:
    defaultInterruption: '@lu_int_DefInt_LU_INT_1'
    title: '{{Break}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '7c5d0bb9-0076-4f2d-8b48-22f93edb3751'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_13:
    defaultInterruption: '@lu_int_DefInt_LU_INT_2'
    title: '{{Driver_meeting}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '091933f5-74f8-4b82-a5a2-10cc8ce5953e'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_14:
    defaultInterruption: '@lu_int_DefInt_LU_INT_3'
    title: '{{Other}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '7012c0d5-0a22-4ec6-b140-a9a98cb1416a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_15:
    defaultInterruption: '@lu_int_DefInt_LU_INT_4'
    title: '{{Waiting_time}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'da72c950-3e11-4c97-a81c-bba632b96a0a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_16:
    defaultInterruption: '@lu_int_DefInt_LU_INT_5'
    title: '{{Vehicle_inspection}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '8c199a30-1ae3-43d3-ba0e-8283fadafd63'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_17:
    defaultInterruption: '@lu_int_DefInt_LU_INT_6'
    title: '{{Workshop}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: 'aa94e30b-8446-4829-b3c9-ab5d1ded96fe'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_18:
    defaultInterruption: '@lu_int_DefInt_LU_INT_7'
    title: '{{Refuel}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '9bbc9f89-47b9-40c5-9d27-b4e32c3e98ce'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_19:
    defaultInterruption: '@lu_int_DefInt_LU_INT_8'
    title: '{{Traffic_jam}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '3e8e667e-6937-48d8-83d8-38f1ad69ed5a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_20:
    defaultInterruption: '@lu_int_DefInt_LU_INT_9'
    title: '{{Vehicle_care}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '0b6cbba6-e567-4c31-a34e-911557d2279f'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_21:
    tourDataConfig: '@lu_int_TourConf_LU_INT_3'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '7a3aad20-aad8-4de3-bc59-9bf77ea9ea01'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_22:
    defaultInterruption: '@lu_int_DefInt_LU_INT_10'
    title: '{{Way_home}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '3fdbe046-a29e-4d35-b2b7-75100e29091f'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_23:
    defaultInterruption: '@lu_int_DefInt_LU_INT_11'
    title: '{{Pre-loading}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '99312369-9291-496b-861a-eea5e3e8a919'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_24:
    defaultNote: '@lu_int_DefNote_LU_INT_1'
    title: '{{Consultation_dispo}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: 'c9df8cf4-778e-4375-8723-71fd8c999137'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_25:
    defaultNote: '@lu_int_DefNote_LU_INT_2'
    title: '{{Consultation_distribution}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '7852c5ba-d9c4-43d9-9490-e9e678125057'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_26:
    defaultNote: '@lu_int_DefNote_LU_INT_7'
    title: '{{Dif_DisposalSite}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '8015a801-e821-4e34-a5e7-4966698d4cec'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_27:
    defaultTermination: '@lu_int_DefTerm_LU_INT_1'
    title: '{{Tour_too_long_no_emptying}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '27578458-160f-46c6-aa15-2b16772b5155'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_28:
    defaultTermination: '@lu_int_DefTerm_LU_INT_2'
    title: '{{Vehicle_defective}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'a0653c25-6ba0-46f9-bc10-b2d473ab367c'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_29:
    defaultTermination: '@lu_int_DefTerm_LU_INT_3'
    title: '{{Driver_sick}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '029d68d1-64ae-41e4-9c12-c89783a0d62e'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_30:
    defaultTermination: '@lu_int_DefTerm_LU_INT_5'
    title: '{{Order_returned_to_dispatch}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: '9a463078-2d1c-4e3c-b6db-e94c3617cdc8'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_31:
    defaultTermination: '@lu_int_DefTerm_LU_INT_6'
    title: '{{Customer_not_on_site}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TERMINATION)>'
    id: 'b512ff8a-05a7-4fe8-a485-30baa791ccd2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_32:
    tourDataConfig: '@lu_int_TourConf_LU_INT_2'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'd7b2093d-8564-4949-9bc8-df75e709a314'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_33:
    tourDataConfig: '@lu_int_TourConf_LU_INT_2'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'd0a9ac9e-fa48-4f59-bacc-b1e25e563139'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_34:
    defaultNote: '@lu_int_DefNote_LU_INT_9'
    title: '{{Heavy_contamination}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::NOTE)>'
    id: '354d9576-9c06-44bb-b942-18b26fd67abd'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_35:
    tourDataConfig: '@lu_int_TourConf_LU_INT_3'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '8fae6289-dee7-421d-8967-d5776c2cff22'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_36:
    tourDataConfig: '@lu_int_TourConf_LU_INT_3'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '8085e53e-0b80-4b90-9538-0f2bfc133043'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_37:
    tourDataConfig: '@lu_int_TourConf_LU_INT_4'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '0d8675a2-9988-47ff-825a-64ab5a289638'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_38:
    tourDataConfig: '@lu_int_TourConf_LU_INT_4'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'bd16a86c-b31e-4af9-9cd9-8fd91418b684'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_39:
    tourDataConfig: '@lu_int_TourConf_LU_INT_4'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'bda6948c-7cd3-479e-be18-dd231629ca42'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_40:
    tourDataConfig: '@lu_int_TourConf_LU_INT_5'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '88b700a8-40a0-4cbf-b4ac-ea76078f8db2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_41:
    tourDataConfig: '@lu_int_TourConf_LU_INT_5'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '6829d1d4-e13f-45ab-a398-e7c54aeaa664'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_42:
    tourDataConfig: '@lu_int_TourConf_LU_INT_5'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'd4da0444-8df9-4287-becb-fca19414a469'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_43:
    tourDataConfig: '@lu_int_TourConf_LU_INT_6'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '8bca1a75-0233-474c-a77f-ac0e87564f4d'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_44:
    tourDataConfig: '@lu_int_TourConf_LU_INT_6'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '97521be0-eab4-4603-b936-42d330fde321'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_45:
    tourDataConfig: '@lu_int_TourConf_LU_INT_6'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: 'e45086c7-5048-4122-b97e-e893291ff143'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_46:
    tourDataConfig: '@lu_int_TourConf_LU_INT_7'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '4515caef-dc65-4de3-8462-067e83dfc149'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_47:
    tourDataConfig: '@lu_int_TourConf_LU_INT_7'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '68bd3caf-56dd-4571-8669-b4df7ac40ab0'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_48:
    tourDataConfig: '@lu_int_TourConf_LU_INT_7'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '7318644e-9b74-498f-a8ac-753ff0a0f64e'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_49:
    tourDataConfig: '@lu_int_TourConf_LU_INT_8'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '72194084-1c73-4d54-8ebc-9ffcdfa5ac20'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_50:
    tourDataConfig: '@lu_int_TourConf_LU_INT_8'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'b46571c7-07ab-4ab3-8c84-c0b05cbb8b25'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_51:
    tourDataConfig: '@lu_int_TourConf_LU_INT_8'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '4592cd6c-872f-4bad-a7d0-222a6b479ffa'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_52:
    tourDataConfig: '@lu_int_TourConf_LU_INT_9'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'd12acc4e-82bb-4b91-a84a-8f96fa80a450'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_53:
    tourDataConfig: '@lu_int_TourConf_LU_INT_9'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '67991ec4-31b1-40d8-99c6-6ebab2a32086'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_54:
    tourDataConfig: '@lu_int_TourConf_LU_INT_9'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '332e02b3-70e2-4e32-920c-da4a5bced221'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_55:
    tourDataConfig: '@lu_int_TourConf_LU_INT_10'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '7816adfb-29e7-49f2-8df3-4c8f8bf2d9e4'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_56:
    tourDataConfig: '@lu_int_TourConf_LU_INT_10'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'b972163d-65fe-46ce-b27e-2a76f143976e'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_57:
    tourDataConfig: '@lu_int_TourConf_LU_INT_10'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '3cc805da-6bf1-4fa8-a634-a591838a05df'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_58:
    tourDataConfig: '@lu_int_TourConf_LU_INT_11'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '480e2239-c927-4338-9ab5-d27b2d59576f'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_59:
    tourDataConfig: '@lu_int_TourConf_LU_INT_11'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '4881bde5-e25f-4e04-bf59-ec32f27304f9'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_60:
    tourDataConfig: '@lu_int_TourConf_LU_INT_11'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '5b407717-a53b-42dc-a9c5-3b3f0b7f6646'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_61:
    tourDataConfig: '@lu_int_TourConf_LU_INT_12'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: 'd7259a54-4ca0-4f42-9950-56d9e26243ea'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_62:
    tourDataConfig: '@lu_int_TourConf_LU_INT_12'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '926b33fd-0d8c-45dc-bdab-9ebf2f6e7915'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_63:
    tourDataConfig: '@lu_int_TourConf_LU_INT_12'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '53702152-feb7-4dac-98fa-f64b9b2b4491'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_64:
    tourDataConfig: '@lu_int_TourConf_LU_INT_13'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '823a524f-bcf4-47e2-932a-02b09d1e9cce'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_65:
    tourDataConfig: '@lu_int_TourConf_LU_INT_13'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '9ea411a6-7d75-480d-8512-b1f51f8657ae'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_66:
    tourDataConfig: '@lu_int_TourConf_LU_INT_13'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '57618241-4b09-48f6-865f-a697dc2c4236'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_67:
    tourDataConfig: '@lu_int_TourConf_LU_INT_14'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '9884bdb4-67c6-4e3f-af6d-599905f86ec2'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
  lu_int_DefTG_LU_INT_68:
    tourDataConfig: '@lu_int_TourConf_LU_INT_14'
    title: '{{Tour}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::TOUR)>'
    id: '48a06e2d-a27b-4b60-9718-972ff68bb855'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::POST)>'
  lu_int_DefTG_LU_INT_69:
    tourDataConfig: '@lu_int_TourConf_LU_INT_14'
    title: '{{Disposal_plant_drive}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
    id: '01db5f0e-3c0a-4c6b-b63f-724563f67d7a'
    tenant: '<getGermanyTenant()>'
    onlyForManualCreation: true
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
  lu_int_DefTG_LU_INT_70:
    defaultInterruption: '@lu_int_DefInt_LU_INT_12'
    title: '{{Non_working_time}}'
    sequenceNumber: '10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
    id: '640fa702-9ff3-4b90-b3ad-df55a587d66a'
    tenant: '<getGermanyTenant()>'
    toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::IN)>'
