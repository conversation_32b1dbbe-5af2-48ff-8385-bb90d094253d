App\Domain\Entity\ValueObject\AdditionalInformation:
  pz_DefAddInfo_1:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_2:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Place_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_3:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Remove_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_4:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_5:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Put_down_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_6:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_7:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_8:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_9:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_10:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_11:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_12:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Put_down_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_13:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_14:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_15:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_16:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_17:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Fill_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_18:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_19:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_20:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_21:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Put_down_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_22:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_23:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_24:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_25:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_26:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Fill_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_27:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{NL_51}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_28:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Swap_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_29:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_30:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Put_down_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_31:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Empty_container}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_32:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Drive_to_collection_area}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_33:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Pollutant_collection}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_34:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_journey_to_branch}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_35:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Drive_to_collection_area}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_36:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Municipal_Service}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_37:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_journey_to_branch}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_38:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Drive_to_sweeping_area}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_39:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Sweeping}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_40:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_journey_to_branch}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_41:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Drive_to_snow_clearance_area}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_42:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{winter_service}}', alsoFrontView: false, icon: handyman, highlight: false }
  pz_DefAddInfo_43:
    __construct: { sequence: 5, source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>', text: '{{Return_journey_to_branch}}', alsoFrontView: false, icon: handyman, highlight: false }
