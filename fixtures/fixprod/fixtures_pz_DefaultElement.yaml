App\Domain\Entity\ValueObject\Element:
  pz_DefElem_1_1:
    __construct: { id: 766cbb73-5a44-5c3b-b370-9cdf8c0a5644, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_3', '@pz_DefElemOption_4'] }
  pz_DefElem_2_1:
    __construct: { id: 01927e81-33cd-5975-a91f-490e33c9aa56, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_2:
    __construct: { id: 1c6bb526-1d63-5da3-a2ef-43728e2d4f08, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_3:
    __construct: { id: 1a8cdee6-6f1c-545a-bb20-60f314542f31, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_4:
    __construct: { id: 8d5a405d-929b-571c-a0a3-3d302b8049b0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_5:
    __construct: { id: 61359c73-3760-53c3-b54f-88632d5f49af, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_6:
    __construct: { id: d35811a9-204b-5ee9-9b90-c04aa7ca06eb, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_7:
    __construct: { id: c9e62385-6de6-5342-8a48-37649e8b8849, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_8:
    __construct: { id: 82e2116e-426c-5cad-bb09-20f13eb53285, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_9:
    __construct: { id: 3b7d69b9-3785-5e52-912a-2e4b7350439f, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_10:
    __construct: { id: b53c45e7-d891-5ecb-bd41-152c85582528, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_11:
    __construct: { id: be49e4d8-562b-5a54-8dcc-c243db3174ab, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_12:
    __construct: { id: 8e4335be-e207-59c1-a66e-f665a9a4987c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_13:
    __construct: { id: 203db54d-7f9a-54bc-b272-97aad7cba795, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_14:
    __construct: { id: 87e9a4d8-47e7-52d5-a604-e46e18537c21, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_15:
    __construct: { id: 19e0fc01-03ee-5b75-93c7-e2cd1736aed2, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_16:
    __construct: { id: 2c3870f1-2d7b-58fb-ae0c-a2a6316e176a, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_17:
    __construct: { id: f8899958-d7ee-5e16-ae5a-4d5956d0c328, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_18:
    __construct: { id: feaeea75-3114-5ac8-853d-f4d98eb99322, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_19:
    __construct: { id: ff166f1e-2953-541e-8b28-a8967c0b1191, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_20:
    __construct: { id: e50f9968-0d7a-58d2-8f48-40bf65af444e, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_21:
    __construct: { id: 589f1b88-51f2-55a0-bd2c-1b9e088b1605, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_22:
    __construct: { id: 0b8763e8-de98-5abf-9d6b-47446fdc05b2, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_23:
    __construct: { id: 4bcd4165-64f7-5817-bf80-59532eda2b71, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_24:
    __construct: { id: 78fffc9b-125c-5e68-9a50-33ab7606a6e0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_25:
    __construct: { id: 17473b95-4e45-5032-bd5a-40844cf648b7, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_26:
    __construct: { id: 8f33ad2f-f4a8-5767-896c-a28d95dba41d, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_27:
    __construct: { id: f55e3648-1b29-5a21-abfe-be3beab03146, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_28:
    __construct: { id: 9738cc89-da4c-5ada-af66-d9c41cebcec4, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_29:
    __construct: { id: a9ced4c6-59fb-5449-865a-52c7e07bb534, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_30:
    __construct: { id: e48a7113-cef7-56e6-8efa-9cb88fdbd9fb, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_31:
    __construct: { id: d09d556f-1717-5d8e-9e28-cb06086c7ccd, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_32:
    __construct: { id: 8e2bfde9-9d3a-58e9-89c8-40131b877074, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_33:
    __construct: { id: 58504352-f608-57c8-ba80-5abeaabc2e8c, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_34:
    __construct: { id: 4d929fac-053a-5ba4-b5af-d56ba5567c00, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_35:
    __construct: { id: bc162e62-0eac-5437-a223-f1928d052d18, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_36:
    __construct: { id: 8873d8bc-a4a4-5511-8491-4dfcb5687f63, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_37:
    __construct: { id: 333db92b-237c-585e-b9bf-ee02035e9511, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_38:
    __construct: { id: e9064d25-fa2d-554f-ae60-e1e9ef5a45fa, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_39:
    __construct: { id: 46ed0cd6-a85b-520c-84f0-d8bd3ce0a3a0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_40:
    __construct: { id: d3568290-51d1-5786-b740-46c721dedcf8, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_41:
    __construct: { id: a5afa29f-c5a6-5142-9c98-da3ea07246c6, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_42:
    __construct: { id: e702b37d-b76f-551b-94c4-a9d8542e1ccb, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_43:
    __construct: { id: 436c5ef5-e03a-5f12-87a5-19aa19c16e07, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_44:
    __construct: { id: 1e4a31d2-2eae-5199-a44b-fdbad0105ceb, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_45:
    __construct: { id: 0a537d0c-c0bc-57a5-8de9-a09d668dc2e9, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_46:
    __construct: { id: 3f27d2b9-40f1-549e-9db1-ad41ebce45f6, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_47:
    __construct: { id: 7951dfc9-474a-5243-b8c2-5e5ed1a756bf, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_48:
    __construct: { id: 6e1d486e-1cad-5b7d-a734-6dfc28ea7f20, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_49:
    __construct: { id: d5768cc1-e4e3-5c04-95b1-17de72077fa2, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_50:
    __construct: { id: 9bebd3a1-571e-5631-bae8-32a29a5f19e0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_51:
    __construct: { id: 78988bb2-5837-5350-a4cc-ff7560faa524, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_52:
    __construct: { id: 7981cac6-180e-518e-9f5d-2f40bb34fb55, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_53:
    __construct: { id: 2d92f652-a2dc-5052-b467-9697bad596d0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_54:
    __construct: { id: dab257fd-f2ab-5feb-a176-0546962074e2, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_55:
    __construct: { id: 3357d687-f68e-5c10-81d2-b57ee443b870, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_56:
    __construct: { id: 9d380a53-dc2b-5ed2-bcc2-dd5199d6d553, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_57:
    __construct: { id: 27118ea7-303d-5220-a00b-78ad87dcee76, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_58:
    __construct: { id: cfa155d6-2772-59ba-8d4c-53adaa23e0b6, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_59:
    __construct: { id: e7cb5a45-065c-5e02-90cd-cc021d3e3511, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_60:
    __construct: { id: caddfdf4-97ed-5d2d-ba67-06279c14f892, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_61:
    __construct: { id: 1cd3d565-024d-527e-aea8-dc2e77414363, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_62:
    __construct: { id: 8517640b-0815-5401-9480-4fc409cd6370, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_63:
    __construct: { id: cf59ef84-6912-510d-961f-3c60fa0eec3e, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_64:
    __construct: { id: 0fa31406-1a90-5054-a977-0461350d11cd, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_65:
    __construct: { id: 60539f68-3334-5000-b88b-f579bad2e2ab, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_66:
    __construct: { id: 0dd7ca51-507d-5a4f-bd5b-a689f75f89f3, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_67:
    __construct: { id: ca51c485-3cab-5380-9a47-29946cd77ce5, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_68:
    __construct: { id: 048a1406-a5a4-55ae-a135-a2c1e8ad750f, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_69:
    __construct: { id: 56687d99-a884-5010-9259-69a84c2d74a0, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_70:
    __construct: { id: 52b65b1e-7e3a-5c00-85bb-2f0727065fbf, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_71:
    __construct: { id: b688294d-f31a-5b0a-8984-67f9d01fd00b, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_72:
    __construct: { id: 45e711cf-6bfb-5268-8d3d-06845d569d62, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_2_73:
    __construct: { id: 8d8a3a3c-1744-5992-9d52-e8f70f1e3340, referenceType: arrival, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Arrived?}}', label: '{{Arrival}}', patternHint: '{{Arrival}}', sequenceNumber: 10 }
  pz_DefElem_3_1:
    __construct: { id: 680cf527-4ead-55b9-be89-d950ea13ccb2, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_2:
    __construct: { id: bb2d8a2d-4894-505f-8e0e-030dbef81a11, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_3:
    __construct: { id: aa490774-b1bc-5360-9536-f06d08fd07df, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_4:
    __construct: { id: c07b20bd-a757-5399-bed1-ba061803760b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_5:
    __construct: { id: 11ebb906-d4a0-5451-ad07-4311609ad9b0, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_6:
    __construct: { id: cb895ed3-e4bd-5e56-8074-1c32903b92b2, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_7:
    __construct: { id: 6b43fcc4-420f-5eca-b24b-58350e6c785e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_8:
    __construct: { id: b7bd7db9-9128-5c77-9f89-0ad2bc48bbbc, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_9:
    __construct: { id: 5693b70e-7848-55e0-b505-e33f40460b67, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_10:
    __construct: { id: 532a5cf8-6214-5123-85fe-ad9d88e9499d, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_11:
    __construct: { id: 025cb030-a2de-5e43-aeb7-6c02d4897f0d, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_12:
    __construct: { id: ef55c2c2-f4b0-5ac2-839e-4a47693649a0, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_13:
    __construct: { id: 161fe5f1-b8d7-5791-81d3-5dd9cfff3656, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_14:
    __construct: { id: 67eff3d4-9e88-5719-b5af-049f80a7211f, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_15:
    __construct: { id: 5d9dc2a1-a778-583d-a32e-09193a02c404, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_16:
    __construct: { id: 79961dfc-8684-5e02-811d-ba2ac10a52ce, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_17:
    __construct: { id: 2e45d5dd-86da-55c4-b14e-395a6f416f5b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_18:
    __construct: { id: 07d6cc42-9867-5b42-a581-4829232dbab1, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_19:
    __construct: { id: d88cea8b-f2f8-5cfb-8efe-4e445fb62b62, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_20:
    __construct: { id: 8408c282-7b67-5abd-8afd-c872ae3e7958, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_21:
    __construct: { id: 8b89706a-2e15-51db-aaf2-d425cdad6942, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_22:
    __construct: { id: dd6dd613-5c05-5ad2-88a7-d928721cffb6, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_23:
    __construct: { id: c9d17374-2b03-5947-bbb8-6f36cace6c02, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_24:
    __construct: { id: 4d5a264d-be54-58c0-a9b7-02fbbce28067, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_25:
    __construct: { id: 1ae6ace4-fa50-56d9-91e3-49642667e638, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_26:
    __construct: { id: a32f948e-aaa7-5c58-89bd-d95b50401ad6, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_27:
    __construct: { id: 33afe7c4-c23c-5342-940d-f12b2ff6e8ec, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_28:
    __construct: { id: de1f3672-5d1f-5d4c-b63e-5e65e7d6ec85, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_29:
    __construct: { id: 08ea7219-6364-54cb-96f2-eb7a90de2caf, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_30:
    __construct: { id: d75af992-34fa-5a7b-9402-5032bcfe6175, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_31:
    __construct: { id: 66d31f62-1c4e-5296-b1aa-bb3168c92146, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_32:
    __construct: { id: bfaca090-9900-51e0-830f-e50e84d4f102, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_33:
    __construct: { id: 2964007f-182e-5788-9f6f-a260c322d47d, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_34:
    __construct: { id: 5043fa6e-de65-5c16-872c-c8bba303a5d5, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_35:
    __construct: { id: 066c0ebd-30bb-5048-be05-4993be797dd8, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_36:
    __construct: { id: d1e227c4-72f5-59aa-a308-948d9718e25f, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_37:
    __construct: { id: 3e4ababd-ff06-55a2-93b2-0acdc9052f6a, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_38:
    __construct: { id: 41d691fc-cae5-5fa9-a82c-3dd13af0a279, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_39:
    __construct: { id: 0ef2be09-5a3b-53d5-bf08-2a07be8b0108, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_40:
    __construct: { id: aac138ed-4af7-531b-98bd-30dc7a14d72e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_41:
    __construct: { id: 688b0d20-45fc-5bf2-aae2-6a3baad96f0b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_42:
    __construct: { id: cef0489c-9c05-59f9-9f60-5fd0fe437bd4, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_43:
    __construct: { id: 74a1a5ed-1200-5157-bec1-44756063ea6b, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_44:
    __construct: { id: a791c2b9-e9bc-5917-b652-b1a1f45c4975, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_45:
    __construct: { id: 79c784bd-aec9-50c1-81aa-fc7dacfbd6e9, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_46:
    __construct: { id: 43f285a7-59e2-5923-87d0-7674f869a1da, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_47:
    __construct: { id: 196f4527-042d-5381-8c2a-c353efb99545, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_48:
    __construct: { id: c5a0799a-4b89-5f20-8ce8-e10c89027f53, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_49:
    __construct: { id: f275a8df-cd32-5a7f-b186-54b0c188c9c1, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_50:
    __construct: { id: bb7734a3-1054-54a9-98eb-e94a09c7c4bc, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_51:
    __construct: { id: 59c5af8e-a507-5850-b47d-71cc370e46c9, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_52:
    __construct: { id: 03ee0bcd-dae4-526c-ae28-de842bcecfba, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_53:
    __construct: { id: 4c86c620-0486-5116-931b-3069ecc763b1, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_54:
    __construct: { id: 0661f8a1-8464-5587-b205-a2b33c9b1a18, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_55:
    __construct: { id: e84485c7-f84f-5afc-a54a-5fb4a21ad3cb, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_56:
    __construct: { id: 695e819f-001d-5de6-a0de-13112dbeb4b8, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_57:
    __construct: { id: 1c3fe0d4-9569-5344-b7b6-6c520cb2f899, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_58:
    __construct: { id: 0872b2bd-5035-5945-ad5a-517d3f9e4a87, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_59:
    __construct: { id: 892d908f-ae12-535a-af12-87a66e800714, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_60:
    __construct: { id: d87df8d0-92e0-5da1-bb13-071cd02d2661, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_61:
    __construct: { id: af684373-0035-5bcd-9e02-c790cacf92a0, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_62:
    __construct: { id: 2d98ec5c-68d7-56ac-a5f3-bf8d952237e7, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_63:
    __construct: { id: 6e687f17-53b6-50cd-82c5-4f7fbe8021a0, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_64:
    __construct: { id: 7a1af4be-0dff-5f58-93a0-a3e87a158956, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_65:
    __construct: { id: 5033bb1b-2df5-51ba-95bd-c8b36c5ec0a5, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_66:
    __construct: { id: f6d49560-8059-548c-a498-b8a913a1f721, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_67:
    __construct: { id: 610da67a-56f4-5f5e-a62b-53ea45facc95, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_68:
    __construct: { id: f7130d78-432d-554f-9b5e-fa297bb58118, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_69:
    __construct: { id: a2c512ea-6ea7-5c46-bb89-31f5c9dc12fe, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_70:
    __construct: { id: 2ba31203-4488-5064-88c1-f73d711a8d03, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_71:
    __construct: { id: 73f35a32-c8c2-5531-9661-2810a0330795, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 200 }
  pz_DefElem_3_72:
    __construct: { id: e506e932-4e3f-5ddd-bc30-419f7ad9f995, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_73:
    __construct: { id: 9f257e13-5ec9-56e4-a84c-bbebcd914a64, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_74:
    __construct: { id: 61c66f95-2711-570c-9e59-14c00543d118, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_75:
    __construct: { id: ac6bdb29-3856-516f-bfae-0a3c8eacc99e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_76:
    __construct: { id: 6a65edb7-8496-50ae-840e-d41cc560a13e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_77:
    __construct: { id: b96a3f53-77c6-571d-a770-7a40d6ee1456, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_78:
    __construct: { id: d353a667-b274-5264-ad88-8262946e3ebb, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_79:
    __construct: { id: a9eb2779-071e-5a7b-a889-17059a040ac7, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_80:
    __construct: { id: 8ac896e6-89a1-590c-816c-2c4b8f7009a8, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_81:
    __construct: { id: 9d3908c2-30ab-533b-8989-98b05d29ecaf, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_3_82:
    __construct: { id: 7787aff3-c4bf-54e7-af37-f68c555c0c4e, referenceType: departure, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Finished?}}', label: '{{Ready}}', patternHint: '{{Ready}}', sequenceNumber: 10 }
  pz_DefElem_4_1:
    __construct: { id: 026a5771-35a9-5811-906b-719b03d6fcb1, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_2:
    __construct: { id: 5f6d658a-3470-5b1a-a7e7-478643c0aa7f, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_3:
    __construct: { id: 64cf81d9-150a-5c5f-803e-ea6535f6e334, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_4:
    __construct: { id: ed7953d2-1983-5df1-8cf6-73066dd05dcf, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_5:
    __construct: { id: f1a97152-84ff-555f-83b3-c961ea78f51f, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_6:
    __construct: { id: 70c291b2-bb05-59e4-8427-aeeaaac12130, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_7:
    __construct: { id: 43f73e12-e695-5163-be70-0a2ad8cad9af, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_8:
    __construct: { id: e5b9799f-8902-5366-84c9-ddf3a1b45b60, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_9:
    __construct: { id: 23895911-b1c9-5b24-b644-9d9d81a7e3ec, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_10:
    __construct: { id: 73135715-9a77-5f15-815c-d1ce97723bd1, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_11:
    __construct: { id: 664f7bea-6999-5afb-9bb5-1a46d837e10a, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_12:
    __construct: { id: 9e43502e-4719-5f77-9f7d-5f831d118523, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_13:
    __construct: { id: bac9bdd1-ad50-5ea1-afe3-77fd5e224559, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_14:
    __construct: { id: 897cf434-9487-5941-a136-c5a20acf86da, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_15:
    __construct: { id: c82ae466-8a17-590b-a779-203228c8fa80, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_16:
    __construct: { id: 0a8ea0d6-95ff-540f-a180-0c3d4292269f, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_17:
    __construct: { id: ed50d5fb-a986-59b4-ade6-f2419c648014, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_18:
    __construct: { id: d5d945ae-e682-5a6c-9e8d-551134af4615, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_19:
    __construct: { id: 590cfe0a-7619-51b8-89d8-b17538149d9b, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_20:
    __construct: { id: 6435f83c-f5ab-5ffb-bd29-c41d2b336ee4, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_21:
    __construct: { id: 321cf0d5-fa60-59c0-936b-dad19ed10888, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_22:
    __construct: { id: bafe93e6-1c36-54ab-9c4e-e7c58a94b6b2, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_23:
    __construct: { id: 9bc13a4e-1483-5312-9f67-abe0a83beccc, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_24:
    __construct: { id: c33c1493-219a-52f7-842b-09f023455d4e, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_25:
    __construct: { id: e8eeaaff-1eb8-5fd0-a0e4-491258dd0312, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_26:
    __construct: { id: 06e5dadf-0d64-5289-a5d7-cb9145cdd01d, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_27:
    __construct: { id: 6f91b279-942b-5e91-aa76-6abac7b9df06, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_4_28:
    __construct: { id: 4bf84782-4475-5d0d-a47c-7b20cc3e58bb, referenceType: signature, type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>', placeholder: '{{Please_obtain_signature}}', label: '{{Please_obtain_signature}}', patternHint: '{{widget/bottom_sheet/signature/name_title}}', sequenceNumber: 10 }
  pz_DefElem_5_1:
    __construct: { id: 9645211e-b56e-5f03-ab34-0f4ee188f3f7, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_2:
    __construct: { id: e42723c4-9236-5bb8-af62-fd5c229a9d24, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_3:
    __construct: { id: 539ab29d-ea20-53e3-bb7a-5839bd685d23, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_4:
    __construct: { id: 93988631-ccea-5d60-922e-2f5635cbcfbb, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_5:
    __construct: { id: 5b98427e-d33a-52e0-80b2-dba5717c0d5d, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_6:
    __construct: { id: a4a801db-cc2f-550f-844c-c8a504b41f00, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_7:
    __construct: { id: 54b7804e-2ffc-5ced-82e7-0d8c8d1e808e, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_8:
    __construct: { id: f1b93435-749f-5a00-ab70-7842d0f026c3, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_9:
    __construct: { id: 263b33af-5fdd-5c33-83e2-977d23f8dcc5, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_10:
    __construct: { id: 77a1e13c-3711-5772-8f0a-4b338a121620, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_11:
    __construct: { id: c4e38f0d-0dfc-5e6c-bccc-e66d4d349619, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_12:
    __construct: { id: 63248ef5-11ff-5f41-839f-0f50172dd933, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_13:
    __construct: { id: ea6dcbea-2695-55ba-97a3-af4314a9331e, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_14:
    __construct: { id: 545da5e3-4279-5c72-a49e-26eb0678b3d0, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_15:
    __construct: { id: f64ed213-85f5-5525-9f3e-7d7287d1f196, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_16:
    __construct: { id: 24d2d55f-f099-5679-8e9d-4672fc49b509, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_17:
    __construct: { id: 4b3ee198-9d94-5991-a134-15411fa68ec9, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_18:
    __construct: { id: 46e7890b-7149-5060-87a7-c406bb312e5f, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_5_19:
    __construct: { id: 33414d2c-d85a-54fb-9cca-e6852868e41d, referenceType: containerScan, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_scan_container_QR_code}}', label: '{{Please_scan_container_QR_code}}', pattern: '^(((TB)?\d{10})|(SUEZ\d{8})){1}$', patternError: '{{pattern/error/wrong_format_barcode_de}}', valueMinItems: 1, valueMaxItems: 3, patternHint: '{{QR_code}}', containerAction: place, sequenceNumber: 10 }
  pz_DefElem_6_1:
    __construct: { id: 96b0f8d7-9e00-5a58-8f7f-59f458754c4f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_2:
    __construct: { id: d53ec4c6-7b88-59e0-993a-cb3907dded03, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_3:
    __construct: { id: 117a6314-4d9f-5696-b085-14dd703ed025, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_4:
    __construct: { id: 5d3ab4ff-3014-5dd9-83b7-cee109113ad9, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_5:
    __construct: { id: 5a6784a1-5870-5e9b-9149-55bc5f18cbe3, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_6:
    __construct: { id: bebe7583-9587-5fe6-aa8b-abc5400ba007, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_7:
    __construct: { id: e398731f-496a-54e8-89da-1561ad493682, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_8:
    __construct: { id: 8b0a5734-7c20-55e6-a854-1d4353779283, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_9:
    __construct: { id: 4781a71e-71a6-526c-878a-aa952f7c0954, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_10:
    __construct: { id: 9cd62e57-3849-553b-8132-ba07aa48a6f5, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_11:
    __construct: { id: 1499cb08-1a47-5023-b9db-f90a4578d927, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_12:
    __construct: { id: a2a2c2ed-3482-5855-bc3d-58da9a27e17f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_13:
    __construct: { id: e354fe7a-1d64-55fc-8338-692fc41b1b4b, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_14:
    __construct: { id: 15ba4acb-1ebc-5060-85db-7d7508290103, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_6_15:
    __construct: { id: e3529323-229d-5f09-aa87-de084614f11f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^\[1-3]$', patternError: '{{pattern/error/invalid_number_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_7_1:
    __construct: { id: 6a06db41-3696-5fc8-9563-d456639d9c91, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_2:
    __construct: { id: 02e864b6-ecab-5db5-8f73-1dcdb470783b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_3:
    __construct: { id: db02e208-657d-5651-bbd4-e46a45851f37, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_4:
    __construct: { id: 5245eae8-0c83-5ee3-9ded-e1db980e7572, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_5:
    __construct: { id: 6fd290f5-6bc3-56da-887e-d9cf21bd27f6, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_6:
    __construct: { id: 9dee1117-e23d-54a8-9019-b08e3f450709, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_7:
    __construct: { id: 3bd2932f-90a2-594e-81d6-1588e73fec68, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_8:
    __construct: { id: edb87726-0bcc-541f-908c-59e5ec676018, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_9:
    __construct: { id: 16fcd6d3-5900-59af-9a29-ec4de5f4fd12, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_10:
    __construct: { id: 417d3178-a2bb-5c99-9a3a-eb91dc9d4efa, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_11:
    __construct: { id: 8de4b2de-a6fe-5c0a-a6da-c38368a6a000, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_12:
    __construct: { id: 64b7369f-18ce-53ae-ab13-e489083b8489, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_13:
    __construct: { id: f3c05862-a79f-53c8-a7fc-196d9c4a280b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_14:
    __construct: { id: ba4e808b-c93c-557b-989a-6b027d2ffaed, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_15:
    __construct: { id: d5fbec16-a91c-5a33-a8f2-ee9dbbc2c8b4, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_16:
    __construct: { id: 55bff737-ea17-5d96-870f-35b779139632, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_17:
    __construct: { id: 810c5594-376f-5a68-a67a-bd29c8673842, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_18:
    __construct: { id: 6e3c4915-cb34-5cf3-9256-eb8826ae4299, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_19:
    __construct: { id: 06c5a9d9-668a-5c9e-a17c-14e57f319825, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_20:
    __construct: { id: c79f1305-7465-5583-8470-18d39350343b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_21:
    __construct: { id: 1c27b72e-3090-5422-8a70-58dddad234e1, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_22:
    __construct: { id: 1c0a8b50-2d2f-5f3b-8222-f619f5dc9ccb, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_23:
    __construct: { id: b66c84fb-ac35-5329-95d1-84018f091ca0, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_24:
    __construct: { id: 71b648d4-671b-59fb-a3b3-b3850e763621, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_25:
    __construct: { id: 891da40f-93d2-5db5-a984-bfb047053d34, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_26:
    __construct: { id: aeae4931-9556-56c5-8570-9bfec630fe8b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_27:
    __construct: { id: b6806db2-d278-503e-b4ae-bc2f83a27323, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_28:
    __construct: { id: bba40caa-3346-5bfa-9acc-ea42cca89add, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_29:
    __construct: { id: 1c25ad6b-8f8b-5f9a-95bb-5f7acaaf603b, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_30:
    __construct: { id: b212fe6b-3b91-52e9-b7b6-704c7f52478e, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_31:
    __construct: { id: 2c362a70-3f9f-5cb3-b10f-cb4547d24193, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_32:
    __construct: { id: 4da21198-d51e-588d-a980-3772ce6c349d, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_33:
    __construct: { id: 9878b32a-5bd2-56dd-9432-6e8eeb0141db, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_34:
    __construct: { id: 3c66f24c-fa5b-5eba-beb0-a61c3448c392, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_35:
    __construct: { id: 4067ca6c-1026-562d-a2b8-3339ed5d83a2, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_36:
    __construct: { id: 6702ddd9-d13e-5d44-8ef8-86bf388acb9c, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_37:
    __construct: { id: 78b15f15-8915-5e96-93b9-d0f124a47736, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_38:
    __construct: { id: efa66918-2c7a-5255-89f5-8d74a5838b18, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_39:
    __construct: { id: d87a1768-ef84-5250-ad1e-42b30268eb45, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_40:
    __construct: { id: aa6630af-084b-5eb1-8313-00fea363b015, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_41:
    __construct: { id: a37f8e4c-0547-57eb-b49c-577d712924eb, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_7_42:
    __construct: { id: 57c5bfb7-157c-5dfb-964f-929c50501bd0, referenceType: weighingNoteNumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Please_enter_the_weighing_slip_number}}', label: '{{Please_enter_the_weighing_slip_number}}', patternHint: '{{Weighing_ticket_number}}', sequenceNumber: 10 }
  pz_DefElem_8_1:
    __construct: { id: aebd0ecc-2d35-53ef-9b5f-55496e6aac7c, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_2:
    __construct: { id: a6fe273c-a3e4-5bbe-a91f-e20943389581, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_3:
    __construct: { id: 401cad6b-d07d-5326-8986-5b1081d4b576, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_4:
    __construct: { id: 05434e61-3921-5f37-9d87-9057b815d443, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_5:
    __construct: { id: 31687ccf-8366-55db-9725-cf610bdcd5b4, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_6:
    __construct: { id: a5b6235b-63cb-5a1f-ab1e-4c2d2692aead, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_7:
    __construct: { id: 89700e7f-18a6-5df3-b99f-b2ab0f979b81, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_8:
    __construct: { id: 20b7d981-6e68-529f-9cc8-338bc793b424, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_9:
    __construct: { id: e25d41e4-03c3-5b82-9320-f29a2c93a0c9, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_10:
    __construct: { id: 4477d493-7e02-56ff-969c-b47a4007eb4f, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_11:
    __construct: { id: 26a4bd51-8cf7-5d0d-a971-9d3844807663, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_12:
    __construct: { id: 28406164-1de5-5e96-9742-bfce1c395982, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_13:
    __construct: { id: f922243e-1397-55a1-8396-2fa22f697f7a, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_14:
    __construct: { id: 25314fb0-8474-5d64-a5c2-54217d1cd0e7, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_15:
    __construct: { id: b46705f3-5bf5-523c-8ca4-bc224d4b9492, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_16:
    __construct: { id: 28555df5-e868-5b72-9032-5d64c8702705, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_17:
    __construct: { id: d3c54b17-e09f-5725-8d31-27ebb10b8bd5, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_18:
    __construct: { id: 1864130f-21f4-54f9-aff2-5a8f537e4fa5, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_19:
    __construct: { id: b54ededd-5c31-53a6-8674-ee7dd62cc3ba, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_20:
    __construct: { id: a298324f-50c7-5ef3-947f-df396f1a8685, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_21:
    __construct: { id: d72e2659-f38d-5dae-9b76-8846ecec596b, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_22:
    __construct: { id: 324c4a64-d6df-56ad-b490-2b8f7d2c3761, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_23:
    __construct: { id: 1ffa1d16-2704-5fd9-9389-2a7238078607, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_24:
    __construct: { id: ed29acd3-9784-5893-95e5-0584cba0296d, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_25:
    __construct: { id: 7dfe8dda-3129-562b-befe-eb237ff44965, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_26:
    __construct: { id: e41e4514-c631-5860-91ff-50133d733d5e, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_27:
    __construct: { id: a593c8b5-6d12-53f8-a92a-65f99855c427, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_28:
    __construct: { id: 54986aff-37db-5176-b82f-cf27adaa5243, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_29:
    __construct: { id: 8a7b2e04-7a02-5345-ae46-9b095d2d846e, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_30:
    __construct: { id: 3b41002f-8665-5be4-aafd-513ebebf16ba, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_31:
    __construct: { id: 230de268-c2dc-5c7f-9570-6ed7011a9a1e, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_32:
    __construct: { id: 08a463e4-d4db-5372-a7e3-f1329dfdbcb3, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_33:
    __construct: { id: 821ee998-3742-5715-a2ba-c2e6b12ea346, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_34:
    __construct: { id: d681fcb1-ac15-5f3a-b971-a210f617dff0, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_35:
    __construct: { id: b4fb27f8-9fb2-53aa-98cb-7d5ae78d6fb2, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_36:
    __construct: { id: bec62d33-a0a0-5f40-9ae0-da7e6b49e951, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_37:
    __construct: { id: 5092f1f2-54b4-5704-bb30-d2f5a3e2e450, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_38:
    __construct: { id: a9a9b31f-2210-537d-9f8b-7402b9c8ab12, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_39:
    __construct: { id: c7ca01d0-9059-5243-b0a3-9dea08c9597a, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_40:
    __construct: { id: d78c506b-f596-5e3e-91b7-eb62a417357f, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_41:
    __construct: { id: 9d9cba0a-4c6c-5d4f-8782-d5d280929b80, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_8_42:
    __construct: { id: 59d8ce97-4ad9-54c9-9eab-cd3f8ab74702, referenceType: unit, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', expectedValue: 1ff13c9a-71a2-11ee-b962-0242ac120002, placeholder: '{{In_which_unit_should_the_weight_be_recorded?}}', label: '{{In_which_unit_should_the_weight_be_recorded?}}', patternHint: '{{Unit}}', sequenceNumber: 20, options: ['@pz_DefElemOption_1', '@pz_DefElemOption_2'] }
  pz_DefElem_9_1:
    __construct: { id: 72bab909-db3d-57cb-a5da-2c7b3999e58c, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_2:
    __construct: { id: e8d0460c-dcb3-5cf8-ace7-1a44fe593a8a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_3:
    __construct: { id: 99e18684-8c7e-5bec-89a8-1943244eded7, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_4:
    __construct: { id: 196b8a8f-d179-5852-a062-c729391d5b15, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_5:
    __construct: { id: 15f17a42-f83e-5be0-95c2-6d47a34da1b3, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_6:
    __construct: { id: 94b812f6-e319-5840-adc9-bd7729a2a3ae, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_7:
    __construct: { id: b0537096-9a91-56f4-bc51-682cf9f74a7a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_8:
    __construct: { id: 487e0bfe-7316-591e-88d7-b519a4967720, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_9:
    __construct: { id: fe054a7c-b244-576f-872b-b5ea147e36e4, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_10:
    __construct: { id: 9e2b03a5-1b6f-553f-b593-5c490c2a32fa, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_11:
    __construct: { id: 277b1281-48df-5c9f-b32e-ba0b2e79748a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_12:
    __construct: { id: f51aa94a-3d31-536e-9e03-6b088899e5d8, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_13:
    __construct: { id: 1373418f-e6b1-5f71-9859-b92ad17cf8ce, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_14:
    __construct: { id: 75840b1f-2462-5f20-a007-034c5fc6b940, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_15:
    __construct: { id: 5b2cffb8-93f4-5665-93f7-09d2c4e72890, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_16:
    __construct: { id: 24796dbf-97b3-594f-834d-484b5bb43c80, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_17:
    __construct: { id: 7cff4e5b-c38d-50cc-bb7b-80c6e393f504, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_18:
    __construct: { id: 004aead3-ef17-52bb-9d0f-2fcf1687b6b7, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_19:
    __construct: { id: 7bdf5bea-abeb-5c73-a9d1-fabf47b6759f, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_20:
    __construct: { id: c6c48948-cd34-5d22-91ce-c1383a28dc11, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_21:
    __construct: { id: bc986f61-2d44-52c8-a2e3-a840adda2a5e, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_22:
    __construct: { id: e471560e-5116-514f-b368-8a4abd18c93e, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_23:
    __construct: { id: 281209a5-b3d1-5601-9f8b-88d5007712d7, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_24:
    __construct: { id: bd2cd44e-76a0-5e3d-aa78-7a766f2affc3, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_25:
    __construct: { id: d3954f84-2e72-5a29-8652-bfcafc8ebcec, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_26:
    __construct: { id: e3f51879-fbf2-5413-af6d-3d697d3f32c2, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_27:
    __construct: { id: 8157aa87-9ce1-5c3b-93a2-6ce96a199dbe, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_28:
    __construct: { id: 089f5638-375c-5bc6-aa17-4a8b4e8dedf1, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_29:
    __construct: { id: 3f062e4f-ae25-5fad-990e-855914e1a02f, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_30:
    __construct: { id: 7e10e789-08e2-519f-8994-58c2b3a87919, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_31:
    __construct: { id: 3db8d686-3c8d-5082-bc7c-7b633b618d01, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_32:
    __construct: { id: c87ed9cc-18ca-5cdb-8829-c93c4f811b91, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_33:
    __construct: { id: d382622b-0066-54dc-ae26-55786d4714a4, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_34:
    __construct: { id: 73ef99e7-4605-58f7-a0c6-e161a111c461, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_35:
    __construct: { id: 12cbf9d3-0f00-5c75-a8ce-ce241b111aa2, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_36:
    __construct: { id: 2c54fffe-1789-5c57-b31c-4906999c5198, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_37:
    __construct: { id: 5f7ac071-6c6e-5f6f-9edd-fc8e156cdc1c, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_38:
    __construct: { id: 52bdc43c-1789-5bbc-96c2-d9ff61e5d6ec, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_39:
    __construct: { id: 738881ba-1c87-594d-9f9a-3af5e5b71900, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_40:
    __construct: { id: d8fd3722-5ae5-5f0d-8898-21d27d5c285a, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_41:
    __construct: { id: 64ac269b-41ec-5218-bb82-1205515b7aeb, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_9_42:
    __construct: { id: 00bd3252-f1c0-5db4-8509-cc61a7c1bef4, referenceType: value, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_weight}}', label: '{{Please_enter_weight}}', pattern: '^((?!0(\.0+)?$)([0-9]{1,5}|0)(\.[0-9]{1,3})?)$', patternError: '{{pattern/error/invalid_weight_de}}', valueNumberPrecision: 3, patternHint: '{{Quantity}}', sequenceNumber: 30 }
  pz_DefElem_10_1:
    __construct: { id: ba8a5121-09de-53c3-9b53-12b50a1c793a, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_2:
    __construct: { id: f489dc46-acdf-5190-8999-3145d524e65a, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_3:
    __construct: { id: 17253572-41bb-50e2-9c8f-524385c4202b, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_4:
    __construct: { id: e4a1ee73-45f4-579c-ae48-4350889ed2a6, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_5:
    __construct: { id: eba98f8e-0da8-5e1c-8738-9db98bcb48db, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_6:
    __construct: { id: cb76337a-98ba-57b6-b015-2e5eb0dbedf1, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_7:
    __construct: { id: db1ecce0-cda9-54a9-85fd-ed3c323d4003, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_8:
    __construct: { id: a93ec527-e988-5d15-9928-1989c4a6efc1, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_9:
    __construct: { id: 90c4062a-ab73-5544-b826-3f0e58c316f8, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_10:
    __construct: { id: 6f5a8b42-9f9c-5897-ab90-b2d0040418f2, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_11:
    __construct: { id: c2c9744a-019f-5ffa-b7d8-64e0d88d9a44, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_12:
    __construct: { id: b55df2fc-d31f-5e37-94cd-054829fc327a, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_13:
    __construct: { id: 022ef587-8133-5823-ab46-7414710f8dbe, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_14:
    __construct: { id: 157a1d51-e94f-5894-8831-39c05d289a12, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_15:
    __construct: { id: 25e2643c-bf77-5e66-ab95-66187d6d7305, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_16:
    __construct: { id: fbea8c28-a184-598f-8e7d-e0d09a233fd6, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_17:
    __construct: { id: bb7eb4fe-ba84-5534-9182-e652fca698fc, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_18:
    __construct: { id: 64decd0d-5ecb-5f4d-ad63-72ac21f5fbb2, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_19:
    __construct: { id: c9a945d2-0964-5b7f-8a9f-fc6106a1d130, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_20:
    __construct: { id: 29fd91f7-73a3-5ce3-8920-d013e1c7eaac, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_21:
    __construct: { id: 8f528d6b-67c7-526a-a007-31691e031e92, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_22:
    __construct: { id: 14de48e0-71ac-582a-b20d-e0126ef5d7c0, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_23:
    __construct: { id: b4930308-6960-586e-9e33-373fbe94747f, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_24:
    __construct: { id: f2c5dec1-19ff-59ab-8a57-f511489b9e29, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_25:
    __construct: { id: 176eaefb-0dff-585c-8169-773c5036e4c5, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_26:
    __construct: { id: 110d6d24-f6fc-5e6c-932e-f301f0721dfd, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_27:
    __construct: { id: 6924c8b0-7556-5923-964e-904d1507ddec, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_28:
    __construct: { id: 36ce840e-3057-5120-9a23-223c817289e9, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_29:
    __construct: { id: 4987bed0-297b-5635-8db7-3e9d5a274a2f, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_30:
    __construct: { id: e5610470-e922-5305-928f-20fc0fdc6f29, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_31:
    __construct: { id: 0432000f-6f39-5ee4-bb90-99f77d0e32f4, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_32:
    __construct: { id: f33cca7a-bddc-508c-a824-9fb794814901, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_33:
    __construct: { id: 0f4cb9fa-2621-585b-a16e-4aea7b9477a7, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_34:
    __construct: { id: 4f2ac2ea-9fa4-5693-8ecc-054863711e1a, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_35:
    __construct: { id: 65496107-a3bc-5be1-a40e-7d3e5bd380d4, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_36:
    __construct: { id: 8c8e1ccc-9d5b-5dda-9311-7b0c68877aed, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_37:
    __construct: { id: 1f350feb-c6f0-5211-9bd8-dc4aaf7b1fcb, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_38:
    __construct: { id: 008c4bb6-7fc0-546c-85ee-e475dc60a8a9, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_39:
    __construct: { id: 26c7e6ad-ea2d-5979-a813-5bbe66bc871a, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_40:
    __construct: { id: 76b2d8a9-e354-5927-8ddb-ebad34e058e6, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_41:
    __construct: { id: 46c7018c-be2a-56bd-bd69-0467d4afd824, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_10_42:
    __construct: { id: 846cfc1d-52e5-5d5a-b4a2-17b76e852d3a, referenceType: weighingNoteQR, type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>', placeholder: '{{Please_enter_the_weighing_ticket_QR_code}}', label: '{{Please_enter_the_weighing_ticket_QR_code}}', patternHint: '{{QR_code}}', checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>', sequenceNumber: 40 }
  pz_DefElem_11_1:
    __construct: { id: 87f431be-3dc5-593d-b4bf-89151c77a2fb, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_5', '@pz_DefElemOption_6', '@pz_DefElemOption_7'] }
  pz_DefElem_12_1:
    __construct: { id: 87cce0b6-32dd-524b-a098-8edfb6f3f704, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_8', '@pz_DefElemOption_9', '@pz_DefElemOption_10', '@pz_DefElemOption_11', '@pz_DefElemOption_12'] }
  pz_DefElem_13_1:
    __construct: { id: 165ed653-6425-50b1-a91a-c5abdaf00963, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_13', '@pz_DefElemOption_14', '@pz_DefElemOption_15', '@pz_DefElemOption_16', '@pz_DefElemOption_17'] }
  pz_DefElem_14_1:
    __construct: { id: 40b695b3-1c72-56ea-a23d-c83eba055be2, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_2:
    __construct: { id: 32fb4457-d950-53b0-b5b5-1a516e791818, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_3:
    __construct: { id: 8d4f57aa-13b9-5ae3-9db6-46a76424e676, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_4:
    __construct: { id: c8e243f1-8e90-5df4-ae91-25bb5a34a5d1, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_5:
    __construct: { id: af6ed38e-6b1e-5c91-9127-a0722d01cf47, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_6:
    __construct: { id: f246b89e-3c86-5118-90cf-d3733e3025e7, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_7:
    __construct: { id: f86a4f54-feb2-5768-ba6b-18f9da1ab86b, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_8:
    __construct: { id: 21ecdc5f-02fc-55a5-92bb-e688fa9ee23c, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_9:
    __construct: { id: f6a01141-9f85-514a-ae01-326ff9bf42f0, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_10:
    __construct: { id: bcade184-5ccc-5dc2-9bb0-8ecd6dd1cfd3, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_11:
    __construct: { id: 81866dd9-b41d-5448-931e-12c387cbafd4, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_12:
    __construct: { id: 5c17febd-0842-50be-9cf9-753ced0e3301, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_13:
    __construct: { id: fe6fb10e-e23a-5fab-ba27-9ceb1a04f05a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_14:
    __construct: { id: 4ae0af55-f86c-5f20-b29a-e03fbdfe4261, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_15:
    __construct: { id: afa74a05-c6bd-5d78-a3b7-7ae573fe6f4b, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_14_16:
    __construct: { id: 4794a092-a20d-5466-b602-1eedf145473a, referenceType: freetext, type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>', placeholder: '{{Free_text}}', label: '{{Please_specify_reason}}', patternHint: '{{Free_text}}', sequenceNumber: 10 }
  pz_DefElem_15_1:
    __construct: { id: dd1cbe01-2bc4-51b2-a33f-2d2071e2268d, referenceType: dieselAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_diesel_quantity_in_liters}}', label: '{{Please_enter_diesel_quantity_in_liters}}', valueNumberPrecision: 1, patternHint: '{{Quantity}}', sequenceNumber: 10 }
  pz_DefElem_16_1:
    __construct: { id: 4ee24daf-38dd-5117-8673-865d8a8da654, referenceType: adBlueAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_AdBlue_quantity}}', label: '{{Please_enter_AdBlue_quantity}}', patternHint: '{{Quantity}}', sequenceNumber: 10 }
  pz_DefElem_17_1:
    __construct: { id: 8f18c848-68b7-542b-9543-bc03044ac3f9, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_2:
    __construct: { id: e374c781-87f1-5530-b019-71788ea5f714, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_3:
    __construct: { id: d6451c1f-1a46-5cb6-88bf-fda26b2d31f5, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_4:
    __construct: { id: 31111942-33a0-5353-aae7-c3c4e6fd2c64, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_5:
    __construct: { id: 8fd85beb-d952-5670-a459-c3a38cd1d652, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_6:
    __construct: { id: 51190581-0527-5e96-8dc1-0e8ff56061a9, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_7:
    __construct: { id: 2d9e1680-ab89-5b3f-9e4b-5cf408ca1b68, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_8:
    __construct: { id: 3651215b-5b06-599d-b472-97207e047d5b, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_9:
    __construct: { id: c8dfaa8b-54da-5275-bb6d-3b6c4e5b3208, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_10:
    __construct: { id: 2d5f30dd-1107-5882-8161-cc94d14da53f, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_11:
    __construct: { id: b1b90cfe-6f20-59d9-8c07-23e2a811541c, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_12:
    __construct: { id: 65201257-465f-51fa-b35a-1ff61ee90553, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_13:
    __construct: { id: 3c812167-e814-5e31-b4b6-0447995a5ec1, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_14:
    __construct: { id: adbf668c-fa27-5d5c-910a-e54c764eef03, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_15:
    __construct: { id: a5fe5b3d-b579-5b9d-a3e3-d22a55056e12, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_16:
    __construct: { id: a39b5417-4568-55a9-bfd5-3d6f7765cbf0, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_17:
    __construct: { id: 10129658-c3df-54a2-b8d7-60b0be4af7a6, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_18:
    __construct: { id: cbae9742-19dd-5367-84fd-7f53b0f28b5c, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_19:
    __construct: { id: 7a2c8485-f53e-5476-9583-f6fa5583bd60, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_20:
    __construct: { id: 334c74d5-9e6b-5cc8-bb5e-612888e48857, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_17_21:
    __construct: { id: 138c58ff-f229-516f-af93-01a6b03d6e56, referenceType: photo, type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>', placeholder: '{{Please_take_a_photo}}', label: '{{Please_take_a_photo}}', patternHint: '{{Photo}}', sequenceNumber: 10 }
  pz_DefElem_18_1:
    __construct: { id: 82592a7c-f5f4-5d5e-a5c0-e24536c6c5d2, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_2:
    __construct: { id: 759c158c-013b-575d-9356-30a8921f39d6, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_3:
    __construct: { id: 5cff577d-0e2b-5eaf-a1ce-9e729bf08251, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_4:
    __construct: { id: 19e40e92-1ec6-5cdc-8620-ac96c2f2cb31, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_5:
    __construct: { id: 4e715265-1cf1-5749-8c1f-11cd3af91a79, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_6:
    __construct: { id: 8c5f47e9-646b-5972-808c-9282881e75f6, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_7:
    __construct: { id: 183ca0ab-dd88-54d9-a94c-88ba3fc5ac12, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_18_8:
    __construct: { id: aae0856f-f13c-5c96-a9ed-68033478f95b, referenceType: startMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Initial_mileage}}', label: '{{Please_record_initial_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Initial_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_1:
    __construct: { id: 974cc85f-2fe0-54ee-929a-dd1e7ffa883b, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_2:
    __construct: { id: aeb06212-ad99-591e-a3bc-e900a79aaeee, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_3:
    __construct: { id: bc86cc83-6a91-59c3-9033-8d60e620e768, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_4:
    __construct: { id: cd959acc-a74b-53c1-afd7-f4f67dff59cd, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_5:
    __construct: { id: c65b294c-a3e3-5135-a214-24b8d7811372, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_6:
    __construct: { id: 8739da89-f1cf-5cce-b1a9-48ae6e6aaffe, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_7:
    __construct: { id: 4eba82a5-b7b8-5507-a773-c07a07f7175f, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_8:
    __construct: { id: 64d251e1-a4a6-562d-ae34-2b813ebbadd6, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_9:
    __construct: { id: 779b8cf0-e80e-5515-ab34-a2e9a405ec99, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_10:
    __construct: { id: 0a0a6612-df58-5d0b-b6f9-cfb55b7f2a9a, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_19_11:
    __construct: { id: 401e71d4-d97a-5f61-946b-cf88cc551953, referenceType: endMileage, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Final_mileage}}', label: '{{Please_enter_final_mileage}}', pattern: '^\d{1,6}$', patternHint: '{{Final_mileage}}', sequenceNumber: 10, externalSourceSystem: '<(App\Domain\Entity\Enum\ExternalSourceSystem::VEHICLE_ODOMETER)>' }
  pz_DefElem_20_1:
    __construct: { id: 69842549-edc6-5d77-981e-925f80f2042f, referenceType: vehicleCheck, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Departure_check_completed?}}', label: '{{Departure_check_completed?}}', patternHint: '{{Departure_check_completed?}}', sequenceNumber: 10 }
  pz_DefElem_21_1:
    __construct: { id: af6ddedc-9017-58b1-8e97-1c8b040504a4, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_18', '@pz_DefElemOption_19'] }
  pz_DefElem_22_1:
    __construct: { id: e1553c52-59d8-527a-9cac-f43ac3765bb0, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_20', '@pz_DefElemOption_21'] }
  pz_DefElem_23_1:
    __construct: { id: c8c81842-d8fc-51f7-a7b4-e83902e6ee12, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_22', '@pz_DefElemOption_23'] }
  pz_DefElem_24_1:
    __construct: { id: dc110210-59a1-5dd3-9f31-8523cc631d22, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_24', '@pz_DefElemOption_25'] }
  pz_DefElem_25_1:
    __construct: { id: 927abb96-53b5-5e07-9926-cd4ccb311cf5, referenceType: additionalServiceMaterial, dataSource: '<(App\Domain\Entity\Enum\ElementDataSource::MATERIAL)>', type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{What_is_the_additional_service?}}', label: '{{Material}}', patternHint: '{{What_is_the_additional_service?}}', sequenceNumber: 10 }
  pz_DefElem_26_1:
    __construct: { id: f356f916-1691-564a-8372-5adaf4d5887b, referenceType: additionalServiceUnit, dataSource: '<(App\Domain\Entity\Enum\ElementDataSource::UNIT)>', type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{In_which_unit_should_the_additional_service_be_recorded?}}', label: '{{Unit}}', patternHint: '{{In_which_unit_should_the_additional_service_be_recorded?}}', sequenceNumber: 20 }
  pz_DefElem_27_1:
    __construct: { id: 0a2581bf-40b5-53ed-be19-7c63103e867a, referenceType: additionalServiceValue, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{What_is_the_quantity?}}', label: '{{Quantity}}', valueNumberPrecision: 2, patternHint: '{{What_is_the_quantity?}}', sequenceNumber: 30 }
  pz_DefElem_28_1:
    __construct: { id: 7f47526a-c69b-5946-891b-5e82d269c1ea, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_26', '@pz_DefElemOption_27'] }
  pz_DefElem_29_1:
    __construct: { id: 19c0e275-5726-5754-b7d1-630da8245e64, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_28', '@pz_DefElemOption_29'] }
  pz_DefElem_30_1:
    __construct: { id: bc386a39-5a86-5b06-88f4-dd09a7f91a4b, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_30', '@pz_DefElemOption_31'] }
  pz_DefElem_31_1:
    __construct: { id: cf6e8dcb-1346-5e57-804b-95ca47223eda, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_31_2:
    __construct: { id: 23b8fe51-f98a-599d-bd33-9372a63b7b5c, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_31_3:
    __construct: { id: a0ba0172-2847-5f8d-a0dc-510500e786e1, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_4:
    __construct: { id: 828e427a-8882-5311-82e6-c745be5bd07f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_5:
    __construct: { id: 3ac7f2fe-0af5-5c59-9b13-94699f3814e9, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_6:
    __construct: { id: 49289186-9abd-5b72-9b1d-8426ff13b9f8, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_7:
    __construct: { id: 4d815c3f-0ddd-52ca-bf55-ba20c3cafeab, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_8:
    __construct: { id: b0e481d0-44eb-5d4f-841a-c8d5e9c19f42, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_9:
    __construct: { id: 0f622e22-33c6-5156-8c6e-f4978d32350e, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_10:
    __construct: { id: f09e0fc3-d38f-583c-90da-4ca12500207f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_11:
    __construct: { id: d569bc6a-2ef9-5b7d-8d80-dd32572e2ff0, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_12:
    __construct: { id: c8c8c3d7-e2f0-57c4-94b8-fb67136c6f69, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_31_13:
    __construct: { id: 575c83a2-5384-546f-a914-cf580a45a5b0, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_14:
    __construct: { id: 0fef6b96-a8c5-59c1-989e-bf0f119d7f49, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_15:
    __construct: { id: 3c6c567b-d9d1-5415-b67c-ea85e1cf017b, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_16:
    __construct: { id: 09b5a3be-02a7-5287-987d-a0d83be1e2a5, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_17:
    __construct: { id: de680a7c-9af1-5507-b010-518914d11623, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_18:
    __construct: { id: 05af167e-7741-5b17-bb47-e0d3453b608c, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_19:
    __construct: { id: b5c99a5d-50ab-58e1-a963-12ba08a6a1bc, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_20:
    __construct: { id: c330ff37-c3c5-5b85-a4d1-e40994744948, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_21:
    __construct: { id: d31a9db8-f2bc-5feb-8f68-2893dfd359a6, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 20 }
  pz_DefElem_31_22:
    __construct: { id: f20ed80f-3669-5cc6-bc90-36a2ac18ce64, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9]\[0-9]?)$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_32_1:
    __construct: { id: 7542b6be-32c6-54db-aa36-777753b33d7e, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_2:
    __construct: { id: 569c41f0-35c9-5472-bbd0-555b0aa48ff3, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_3:
    __construct: { id: 0b08cbd5-6eb5-5930-80af-4e0edb6062f1, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_4:
    __construct: { id: 786ef285-f323-5eec-adf4-aed323dd6067, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_5:
    __construct: { id: 0adb590f-d9e6-582d-9786-5ea3d7dc1678, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_6:
    __construct: { id: 70faff67-65c7-5cf2-8419-5064cdf0fadc, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_7:
    __construct: { id: e770272f-db83-5af4-9ac3-88a2886bf776, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_8:
    __construct: { id: 699936c6-ebd2-5883-8bad-1c741ed8f283, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_9:
    __construct: { id: 41217571-515f-5c67-a187-35660e820a7b, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_10:
    __construct: { id: 665b40ae-7208-5a87-ae76-6f6882436177, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_11:
    __construct: { id: 8739ed99-4986-5cc4-a9a3-7f580ae3e2ac, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_12:
    __construct: { id: 288eeedc-688a-528c-bcc4-b7b2a8e5ce8e, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_13:
    __construct: { id: 8b981fa2-c048-5def-b22a-447c35e7f9a6, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_14:
    __construct: { id: 65b06c3f-6506-564e-a5d8-4cc4917296a4, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_15:
    __construct: { id: d3b3d576-e1b9-56cb-bd8a-4f714d5e1799, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_16:
    __construct: { id: 13ecd6f0-af89-5560-b0b5-bcf482a1ad8a, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_17:
    __construct: { id: 87c4ddbe-f94d-5947-a699-1f64f761d547, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_32_18:
    __construct: { id: 5c09b2b6-f8a4-5392-a30e-e31c5b2c7815, referenceType: scale_weighing_data, type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>', placeholder: '{{Please_enter_containerweight}}', label: '{{Please_enter_containerweight}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_weight_container_de}}', patternHint: '{{Please_enter_containerweight}}', sequenceNumber: 10 }
  pz_DefElem_33_1:
    __construct: { id: 0454a86f-31d4-520e-9458-8634794aa614, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_32', '@pz_DefElemOption_33'] }
  pz_DefElem_34_1:
    __construct: { id: 41e5a575-edc6-5adb-90c1-2446666f72b6, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_34', '@pz_DefElemOption_35'] }
  pz_DefElem_35_1:
    __construct: { id: 6870cb1a-70b3-5b58-8a03-df57366df2e3, referenceType: municipal_collection_end, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Collection_finished?}}', label: '{{End_Collection}}', patternHint: '{{End_Collection}}', sequenceNumber: 10 }
  pz_DefElem_35_2:
    __construct: { id: 4e5d5a77-3ddb-5a2e-8de6-a412a8df6ff8, referenceType: municipal_collection_end, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Collection_finished?}}', label: '{{End_Collection}}', patternHint: '{{End_Collection}}', sequenceNumber: 10 }
  pz_DefElem_38_1:
    __construct: { id: b1d77b54-62f4-5507-919b-c0409a106805, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_40', '@pz_DefElemOption_41'] }
  pz_DefElem_39_1:
    __construct: { id: 24f2dc07-0114-5ffd-9dbc-7f44874803ce, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_42', '@pz_DefElemOption_43'] }
  pz_DefElem_40_1:
    __construct: { id: 8e872521-b9ae-5c0e-a63c-2b536eb3db46, referenceType: municipal_collection_end, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Sweeping_finished?}}', label: '{{Finish_sweeping}}', patternHint: '{{Finish_sweeping}}', sequenceNumber: 10 }
  pz_DefElem_41_1:
    __construct: { id: 180f113d-4413-55b9-9527-5c2091fa5627, referenceType: taskgroupPicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_started?}}', label: '{{How_should_the_order_be_started?}}', patternHint: '{{Selection_of_the_start}}', sequenceNumber: 10, options: ['@pz_DefElemOption_44', '@pz_DefElemOption_45'] }
  pz_DefElem_42_1:
    __construct: { id: 51f717c3-21d0-5e59-9978-356cd9d3bad7, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_46', '@pz_DefElemOption_47'] }
  pz_DefElem_43_1:
    __construct: { id: 94a2a441-9d99-5dc1-b558-688c1172f019, referenceType: municipal_collection_end, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{winterservice_finished?}}', label: '{{Finish_winterservice}}', patternHint: '{{Finish_winterservice}}', sequenceNumber: 10 }
  pz_DefElem_44_1:
    __construct: { id: 432137ce-1652-5499-b4af-a63cdfbfa576, referenceType: cubicmeter, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_cubicmeter}}', label: '{{Please_enter_cubicmeter}}', pattern: '^((?!0$)(?!00$)(?!0\.000$)(?!0\.00$)(?!0\.0$)(?!00\.000$)(?!00\.00$)(?!00\.0$)(\d{1,2}(\.\d{1,3})?))$', patternError: '{{pattern/error/invalid_cubicmeter_de}}', valueNumberPrecision: 3, patternHint: '{{Please_enter_cubicmeter}}', sequenceNumber: 10 }
  pz_DefElem_44_2:
    __construct: { id: 0db605b7-fd4e-52ac-91d1-e047527dc133, referenceType: cubicmeter, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_cubicmeter}}', label: '{{Please_enter_cubicmeter}}', pattern: '^((?!0$)(?!00$)(?!0\.000$)(?!0\.00$)(?!0\.0$)(?!00\.000$)(?!00\.00$)(?!00\.0$)(\d{1,2}(\.\d{1,3})?))$', patternError: '{{pattern/error/invalid_cubicmeter_de}}', valueNumberPrecision: 3, patternHint: '{{Please_enter_cubicmeter}}', sequenceNumber: 10 }
  pz_DefElem_45_1:
    __construct: { id: 48e0d33a-d609-5351-8372-3e6dc2c62cc7, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_48', '@pz_DefElemOption_49'] }
  pz_DefElem_46_1:
    __construct: { id: 44b5111f-b41c-5a2e-a3d6-785cff565451, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_50', '@pz_DefElemOption_51'] }
  pz_DefElem_47_1:
    __construct: { id: 56b94bda-88ca-54f3-b95c-fead98c6e763, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 20 }
  pz_DefElem_47_2:
    __construct: { id: 79a78046-5e5f-5149-8a8e-aba84c966b4f, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 30 }
  pz_DefElem_47_3:
    __construct: { id: f17e31e8-598d-520e-b0df-25b634663978, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 40 }
  pz_DefElem_47_4:
    __construct: { id: 27c2449f-bed5-57ad-9781-9d87d72ac8b5, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 50 }
  pz_DefElem_47_5:
    __construct: { id: a791c8f7-de52-5724-ac20-094223c9b763, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 60 }
  pz_DefElem_47_6:
    __construct: { id: 8b97382b-80ad-579b-be3d-b413640ca346, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 70 }
  pz_DefElem_47_7:
    __construct: { id: ade68a4e-e1e6-5e96-b8fc-e27313767e91, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 80 }
  pz_DefElem_47_8:
    __construct: { id: 1e982ef5-e9f3-5583-b88f-5f58455d4f60, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 90 }
  pz_DefElem_47_9:
    __construct: { id: db8debd8-3bbd-5cca-8b68-d729638ca00e, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 100 }
  pz_DefElem_47_10:
    __construct: { id: 13e764f2-3acc-5cd7-9ba2-dfcecd43931e, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 110 }
  pz_DefElem_47_11:
    __construct: { id: 58795bfd-67df-53d6-9d70-10546cee8732, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 120 }
  pz_DefElem_47_12:
    __construct: { id: 545e9184-1797-5fa1-a51f-7509f91ef78e, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 130 }
  pz_DefElem_47_13:
    __construct: { id: ae260a05-72bf-5966-a4c6-9b6b8184c2b8, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 140 }
  pz_DefElem_47_14:
    __construct: { id: 2039698a-fb67-5563-a2e7-dc948c612a30, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 150 }
  pz_DefElem_47_15:
    __construct: { id: 43e62a68-6e81-5010-9835-db40e4f2d5c5, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 160 }
  pz_DefElem_47_16:
    __construct: { id: e0d5b42b-59bf-59fb-abee-2c0ae3ff606b, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 170 }
  pz_DefElem_47_17:
    __construct: { id: 38fc9744-f0da-53d8-848b-6a639ac3feb2, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 180 }
  pz_DefElem_47_18:
    __construct: { id: 490cc4aa-f618-56c0-9a60-87d39d19a411, referenceType: iglu_accept, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{Container_emptied}}', label: '{{Container_emptied}}', patternHint: '{{Container_emptied}}', sequenceNumber: 190 }
  pz_DefElem_48_1:
    __construct: { id: 96c4a60b-4b08-5c61-95f6-e24cda60794f, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_48_2:
    __construct: { id: 82a8cc40-cfeb-5509-a5ac-b9234781a239, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(\[1-9]\[0-9]{0,3})$', patternError: '{{pattern/error/invalid_count_containers_de}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_49_1:
    __construct: { id: 96104db2-0ae6-506b-a208-68f7c08caa6b, referenceType: timestamp, type: '<(App\Domain\Entity\Enum\Types\ElementType::TIME)>', placeholder: '{{timestamp}}', label: '{{Please_enter_your_new_begin}}', patternError: '{{pattern/error/invalid_timestamp_de}}', patternHint: '{{Please_enter_your_new_begin}}', sequenceNumber: 30 }
  pz_DefElem_50_1:
    __construct: { id: d724e5b8-6686-55b2-96ab-9deb379b4200, referenceType: personnelnumber, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Your_personnel_number}}', label: '{{Please_enter_your_personnel_Number}}', patternHint: '{{Please_enter_your_personnel_Number}}', sequenceNumber: 10 }
  pz_DefElem_51_1:
    __construct: { id: 64456adf-56e6-5caa-abd9-efe0828c0d56, referenceType: changeWorkinghour, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Select_reason_for_workingtime_change}}', label: '{{Please_select_reason_for_workingtime_change}}', patternHint: '{{Please_select_reason_for_workingtime_change}}', sequenceNumber: 20, options: ['@pz_DefElemOption_52', '@pz_DefElemOption_53', '@pz_DefElemOption_54', '@pz_DefElemOption_55'] }
  pz_DefElem_52_1:
    __construct: { id: de39c0cc-be02-5d39-8fbb-31861f2e6a20, referenceType: workerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_employeeAmount}}', label: '{{employeeAmount}}', pattern: '^(0?\[1-9]|\[1-9]\[0-9])$', patternError: '{{pattern/error/invalid_amount_employee_de}}', patternHint: '{{Please_enter_employeeAmount}}', sequenceNumber: 10 }
  pz_DefElem_53_1:
    __construct: { id: 09791525-766c-59af-831f-270bc27ec856, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_53_2:
    __construct: { id: c6fc04fb-678c-5a48-9404-503a6d0280f1, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_53_3:
    __construct: { id: 27c390df-856d-5ecc-b778-e8529a8eec5e, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_53_4:
    __construct: { id: d3fb8d7c-d284-5871-a782-2723c1a9552e, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_53_5:
    __construct: { id: 4c9c0169-a539-5a5a-b4dc-7204b4629063, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_53_6:
    __construct: { id: fa7a45a1-0746-5e8c-a33e-6d3e904dc261, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_53_7:
    __construct: { id: 8e239b80-a8eb-5b07-b86d-0f2cd0a0c8c7, referenceType: wastePicker, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{Please_select_a_waste}}', label: '{{waste_selection}}', valueMinItems: 1, patternHint: '{{Please_select_a_waste}}', sequenceNumber: 10 }
  pz_DefElem_54_1:
    __construct: { id: 520bb94f-1599-583c-9f35-c1ec30a71fa0, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_56', '@pz_DefElemOption_57'] }
  pz_DefElem_55_1:
    __construct: { id: 94b04cb9-99cd-5e2d-acd8-3d3eeb24bfa0, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_58', '@pz_DefElemOption_59'] }
  pz_DefElem_56_1:
    __construct: { id: 3886a5b5-04fb-5fc5-8eac-bd3287db7d1c, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_60', '@pz_DefElemOption_61'] }
  pz_DefElem_57_1:
    __construct: { id: a8851387-7720-5c7c-8203-9c611df18890, referenceType: continue, type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>', placeholder: '{{How_should_the_order_be_continued?}}', label: '{{How_should_the_order_be_continued?}}', patternHint: '{{Continue}}', sequenceNumber: 10, options: ['@pz_DefElemOption_62', '@pz_DefElemOption_63'] }
  pz_DefElem_58_1:
    __construct: { id: ac440960-c111-5cbe-bf54-0309f357e547, referenceType: containerAmount, type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>', placeholder: '{{Please_enter_number_of_containers}}', label: '{{Please_enter_number_of_containers}}', pattern: '^(?:\[1-9]\d|[1-9]\d{2,3})$', patternError: '{{pattern/error/invalid_count_containers_de_greater_ten}}', patternHint: '{{Number_of_containers}}', sequenceNumber: 10 }
  pz_DefElem_59_1:
    __construct: { id: 32ec268e-74cc-5119-8ea7-7a2eef26bb91, referenceType: finish_return_journey, type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>', placeholder: '{{End_Return_Journey}}', label: '{{End_Return_Journey}}', patternHint: '{{End_Return_Journey}}', sequenceNumber: 10 }
