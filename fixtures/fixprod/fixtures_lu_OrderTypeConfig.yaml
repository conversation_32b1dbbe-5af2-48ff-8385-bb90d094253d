App\Domain\Entity\OrderTypeConfig:
  lu_OrderConf_LU1:
    name: '{{delivery}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CONTAINER_DELIVERY)>'
    id: '8b1078b4-0470-437d-b0d1-3b38f7d43c62'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU2:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CONTAINER_PICKUP)>'
    id: '5fbcfb3a-3b42-4177-998e-2768a7790ed2'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU3:
    name: '{{empty}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CONTAINER_EMPTY)>'
    id: '61c60187-5ebc-4cef-bb44-f6ca6e11b320'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU5:
    name: '{{internal_movement}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::TRANSPORTATION)>'
    id: 'd9deb0e9-c8a4-451f-967c-43930fd28128'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU6:
    name: '{{loading_on_place}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::LOADING_ON_PLACE)>'
    id: 'f83db287-5811-4d2d-8173-12b9ce027c27'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU7:
    name: '{{deliver_toilet}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::TOI)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CONTAINER_DELIVERY)>'
    id: 'eaf52959-0f2e-4b70-947d-0b64c30a8e89'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU8:
    name: '{{remove_toilet}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::TOI)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CONTAINER_PICKUP)>'
    id: '0d3784da-aec6-42b1-be05-47dbd809319a'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU9:
    name: '{{empty_toilet}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::TOI)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::CONTAINER_EMPTY)>'
    id: '9fad6f87-ba5a-4bca-afe3-08d2327820d2'
    tenant: '<getLuxembourgTenant()>'
  lu_OrderConf_LU10:
    name: '{{toilet_disposal}}'
    country: '<(App\Domain\Entity\Enum\Country::LU)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::TOI)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::TOILET_DISPOSAL)>'
    id: '6db116c4-e6f5-4093-8ff2-bf3be774dced'
    tenant: '<getLuxembourgTenant()>'
