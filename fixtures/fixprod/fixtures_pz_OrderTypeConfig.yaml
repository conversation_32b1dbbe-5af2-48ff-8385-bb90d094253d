App\Domain\Entity\OrderTypeConfig:
  pz_OrderConf_1:
    name: '{{delivery}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_10)>'
    id: '2bb9d412-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_2:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11)>'
    id: '2bb9d552-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_3:
    name: '{{empty/exchange}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_12)>'
    id: '2bb9d674-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_4:
    name: '{{14}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_14)>'
    id: '2bb9d7a0-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_5:
    name: '{{15}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_15)>'
    id: '2bb9d8b8-71a0-11ee-b962-0242ac120002'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_6:
    name: '{{30}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_30)>'
    id: 'e6ea289b-3487-45f4-8596-b442c3dfd25c'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_7:
    name: '{{35}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_35)>'
    id: '11f10fe1-f625-4407-baa5-151e0b498d5f'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_8:
    name: '{{19}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_19)>'
    id: '3d5fc863-3724-4d8c-9271-4d41a845d1d4'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_9:
    name: '{{09}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_09)>'
    id: '58a09ba8-10e8-4c56-9090-2cb48f29123c'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_10:
    name: '{{21}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_21)>'
    id: '157d6164-8729-4177-b868-d511b8184e6c'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_11:
    name: '{{empty/exchange}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::STL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_12)>'
    id: '8080fa71-cb45-4457-b054-25d3003c8aeb'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_12:
    name: '{{14}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::STL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_14)>'
    id: '8030b13c-d110-409b-8d1f-0946ea49a461'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_13:
    name: '{{15}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::STL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_15)>'
    id: '061187f5-b98c-4505-ac1b-e79544199c24'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_14:
    name: '{{30}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::STL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_30)>'
    id: '690d8a38-04fe-40ca-9fb2-392ab6ab5f9d'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_15:
    name: '{{13}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_13)>'
    id: 'd9755b1f-593d-43ae-849e-295afbe23cbb'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_16:
    name: '{{contamination_mobil}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_04)>'
    id: 'd8e2d10d-378a-4b56-83c9-3c7cf8704ddf'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_17:
    name: '{{municipal_collection}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_05)>'
    id: 'c72e68a4-b787-472a-81fb-fdc71c83e6a4'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_18:
    name: '{{street_sweep}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_06)>'
    id: '6087d137-70c0-4e06-81fa-201dce620f15'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_19:
    name: '{{winter_service}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_07)>'
    id: '02a8da91-d3f8-40ee-88ed-b581856e728f'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_20:
    name: '{{bin_service}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_03)>'
    id: 'f315b44d-5e11-4cf2-8b72-3727f8cee495'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_21:
    name: '{{loose_waste}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_08)>'
    id: '04702ea4-375d-4538-99d2-ac174e1e9255'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_22:
    name: '{{court_work}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_18)>'
    id: 'ed42761d-b80f-475e-af91-189170b2c87a'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_23:
    name: '{{special_service}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_20)>'
    id: '728397ac-babc-456c-a236-c9c835028ac7'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_24:
    name: '{{add_clean}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_29)>'
    id: 'fb9c65d2-c1c0-4197-936c-d1681e9efd21'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_25:
    name: '{{place_sweep}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_36)>'
    id: 'c5dd5dda-3ce9-492b-a643-d022e1ca8ac8'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_26:
    name: '{{suction_truck}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_31)>'
    id: 'aead117f-9a55-4eb8-b74c-23ba0d6c332a'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_27:
    name: '{{handload_container}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_32)>'
    id: 'ff627b66-248d-4029-b60d-d178f1735126'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_28:
    name: '{{handload_systemless}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_33)>'
    id: 'e6a58135-1f38-4e7b-ac35-ac6ecc6513b0'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_29:
    name: '{{iglu_depot_emptying}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_34)>'
    id: '4e2ef615-004d-4612-b091-4a5b1e7bd499'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_30:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::PRI)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11)>'
    id: '9684215c-24d4-492d-a0ad-54638bf1ef22'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_31:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::STL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11)>'
    id: '19c4296c-4b0b-469d-a60a-9a9ff511a5f4'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_32:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SFA)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11)>'
    id: '78418a2a-2474-43cf-9411-128085cdf399'
    tenant: '<getGermanyTenant()>'
  pz_OrderConf_33:
    name: '{{removal}}'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::SON)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11)>'
    id: '67fb144c-dcdd-4870-b15b-6f9ee27d89dc'
    tenant: '<getGermanyTenant()>'
