App\Domain\Entity\DefaultTaskRelation:
  es_DefTaskRel_ES_1:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_1'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'be58e318-cb62-4f87-b438-bef37e087b7d'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_2:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_2'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a103b86f-264e-44c4-87b7-35fc85f0ab4d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_1']
  es_DefTaskRel_ES_3:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_3'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cadbf9b3-1e03-4944-9de6-cb1a43520c4f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_2']
  es_DefTaskRel_ES_4:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_4'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9a12ef60-6dab-4852-8caf-d56bc2151e4f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_3']
  es_DefTaskRel_ES_5:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_5'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3ec622bf-2b42-4887-bbe1-02c2c84fd469'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_4']
  es_DefTaskRel_ES_6:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_6'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6604e3d1-9ac4-412d-be3c-4a8b1d4c7d92'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_5']
  es_DefTaskRel_ES_206:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_193'
    defaultTaskGroup: '@es_DefTG_ES_1'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '63a39f09-fa67-4caf-934e-c947b4fef7b3'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_232']
  es_DefTaskRel_ES_7:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_7'
    defaultTaskGroup: '@es_DefTG_ES_1'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e5ec7ce3-d706-4050-b771-31db00a6e9b1'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_6']
  es_DefTaskRel_ES_8:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_8'
    defaultTaskGroup: '@es_DefTG_ES_1'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9b4988d2-5bc1-4fe1-86e9-531cc71f94d1'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_7', '@es_DefTaskRule_ES_8', '@es_DefTaskRule_ES_9', '@es_DefTaskRule_ES_10', '@es_DefTaskRule_ES_11', '@es_DefTaskRule_ES_12', '@es_DefTaskRule_ES_233', '@es_DefTaskRule_ES_13']
  es_DefTaskRel_ES_9:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_9'
    defaultTaskGroup: '@es_DefTG_ES_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'a1aa0ba4-a6d0-4b41-bd40-774aafd4d40c'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_10:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_10'
    defaultTaskGroup: '@es_DefTG_ES_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '157acfab-d4a7-44d2-955f-8d911130e2e2'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_14']
  es_DefTaskRel_ES_11:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_11'
    defaultTaskGroup: '@es_DefTG_ES_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b6c11851-1f9e-4fd8-95a1-68f6b4ddd23c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_15']
  es_DefTaskRel_ES_12:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_12'
    defaultTaskGroup: '@es_DefTG_ES_2'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ebe5c4a3-d6bd-4656-b279-b77cc3c7a858'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_16']
  es_DefTaskRel_ES_13:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_13'
    defaultTaskGroup: '@es_DefTG_ES_2'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2fd04a3e-a80f-4bad-ad94-7523e2f22806'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_17', '@es_DefTaskRule_ES_18', '@es_DefTaskRule_ES_19', '@es_DefTaskRule_ES_20', '@es_DefTaskRule_ES_22', '@es_DefTaskRule_ES_23']
  es_DefTaskRel_ES_14:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_14'
    defaultTaskGroup: '@es_DefTG_ES_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '631b0a20-c2e4-4fee-b284-58d335d959f9'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_15:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_15'
    defaultTaskGroup: '@es_DefTG_ES_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ba2f199b-79b2-4ad6-b427-cda206cce1d7'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_24']
  es_DefTaskRel_ES_16:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_16'
    defaultTaskGroup: '@es_DefTG_ES_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'eb4ab949-de1e-4d85-9c06-c22cb599541c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_25']
  es_DefTaskRel_ES_17:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_17'
    defaultTaskGroup: '@es_DefTG_ES_3'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '52fcc5d3-83ac-436f-8ea1-0d32a0080adf'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_26', '@es_DefTaskRule_ES_27', '@es_DefTaskRule_ES_28']
  es_DefTaskRel_ES_18:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_18'
    defaultTaskGroup: '@es_DefTG_ES_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ab685255-9ff0-486a-9d95-b8cbcf7841f9'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_19:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_19'
    defaultTaskGroup: '@es_DefTG_ES_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '587e2437-68ab-431a-8a0a-8be134e0c211'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_29']
  es_DefTaskRel_ES_207:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_194'
    defaultTaskGroup: '@es_DefTG_ES_4'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8a083b0f-eda9-4424-acef-41673230d00c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_234']
  es_DefTaskRel_ES_20:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_20'
    defaultTaskGroup: '@es_DefTG_ES_4'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '45a22f84-4f90-486f-a0d7-7b9e4f8f09e9'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_30']
  es_DefTaskRel_ES_21:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_21'
    defaultTaskGroup: '@es_DefTG_ES_4'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'de6889f8-e50e-49fe-827a-9db0ca729aa3'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_31', '@es_DefTaskRule_ES_32', '@es_DefTaskRule_ES_33', '@es_DefTaskRule_ES_235']
  es_DefTaskRel_ES_22:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_22'
    defaultTaskGroup: '@es_DefTG_ES_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '91cd6189-b2ea-479b-825a-75b5a8da2dd9'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_23:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_23'
    defaultTaskGroup: '@es_DefTG_ES_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ee87ff7c-1085-490d-9872-af361fee023f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_34']
  es_DefTaskRel_ES_24:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_24'
    defaultTaskGroup: '@es_DefTG_ES_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '35af8b78-917e-4eed-9f79-7c17711515e3'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_35']
  es_DefTaskRel_ES_25:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_25'
    defaultTaskGroup: '@es_DefTG_ES_5'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cdce7d6e-9e93-473f-995d-64920847b145'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_36']
  es_DefTaskRel_ES_26:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_26'
    defaultTaskGroup: '@es_DefTG_ES_5'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fc7c8988-6d40-4091-b490-91162565d6a2'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_37', '@es_DefTaskRule_ES_38', '@es_DefTaskRule_ES_39', '@es_DefTaskRule_ES_40', '@es_DefTaskRule_ES_42', '@es_DefTaskRule_ES_43']
  es_DefTaskRel_ES_27:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_27'
    defaultTaskGroup: '@es_DefTG_ES_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e1c94da0-f0e1-4387-a56d-e44ac73ce125'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_28:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_28'
    defaultTaskGroup: '@es_DefTG_ES_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7d18942f-8ece-4d27-b987-c5d1e42991cb'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_44']
  es_DefTaskRel_ES_29:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_29'
    defaultTaskGroup: '@es_DefTG_ES_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6efdc697-7d98-4015-a9d0-2f83dd0619a4'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_45']
  es_DefTaskRel_ES_30:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_30'
    defaultTaskGroup: '@es_DefTG_ES_6'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '89387c2a-c57e-4d5b-b7ce-b61c833ac2e6'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_46', '@es_DefTaskRule_ES_47', '@es_DefTaskRule_ES_48']
  es_DefTaskRel_ES_31:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_31'
    defaultTaskGroup: '@es_DefTG_ES_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f1b98544-d83e-4e85-9aea-a8c12ee226f1'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_32:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_32'
    defaultTaskGroup: '@es_DefTG_ES_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '935e7667-4a0d-46f4-82fb-f9ccc7dafb5c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_49']
  es_DefTaskRel_ES_33:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_33'
    defaultTaskGroup: '@es_DefTG_ES_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '06503af2-6e05-4ff1-8c78-4f8be715ef09'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_50']
  es_DefTaskRel_ES_34:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_34'
    defaultTaskGroup: '@es_DefTG_ES_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '094121d4-6013-428f-996e-c8d6e39a2235'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_51']
  es_DefTaskRel_ES_208:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_195'
    defaultTaskGroup: '@es_DefTG_ES_7'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '059b2492-74d5-4808-a990-f9e3c74df1be'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_236']
  es_DefTaskRel_ES_35:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_35'
    defaultTaskGroup: '@es_DefTG_ES_7'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd7b06936-6708-45b1-a8ca-5b27f200e459'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_52']
  es_DefTaskRel_ES_36:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_36'
    defaultTaskGroup: '@es_DefTG_ES_7'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '47d28013-7cfd-47d2-aed2-d58eeb1371ac'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_53', '@es_DefTaskRule_ES_54', '@es_DefTaskRule_ES_55', '@es_DefTaskRule_ES_56', '@es_DefTaskRule_ES_57', '@es_DefTaskRule_ES_237']
  es_DefTaskRel_ES_37:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_37'
    defaultTaskGroup: '@es_DefTG_ES_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5ad3be12-afae-4631-85ac-f5147a1fee49'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_38:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_38'
    defaultTaskGroup: '@es_DefTG_ES_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b7bda8e5-7271-4213-9080-ebd038aa2fb3'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_58']
  es_DefTaskRel_ES_39:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_39'
    defaultTaskGroup: '@es_DefTG_ES_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd0052ec4-b0cc-4157-8741-359987398284'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_59']
  es_DefTaskRel_ES_40:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_40'
    defaultTaskGroup: '@es_DefTG_ES_8'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c748c60c-4e64-42e5-b04f-bd57fd00f238'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_60', '@es_DefTaskRule_ES_61', '@es_DefTaskRule_ES_62']
  es_DefTaskRel_ES_41:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_41'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ea5caf65-3a9e-412d-b357-0addd50dd372'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_42:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_42'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'dd94aef5-9463-49a1-a970-02ad098f057b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_63']
  es_DefTaskRel_ES_43:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_43'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '62061048-ae1c-4c9d-9b00-0616653faefc'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_64']
  es_DefTaskRel_ES_44:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_44'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ea150d84-f88e-467d-9abe-eb7780af7348'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_65']
  es_DefTaskRel_ES_45:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_45'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0e84d33e-ba74-4897-8a7d-6562caae2d0b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_66']
  es_DefTaskRel_ES_46:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_46'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bc6d875f-2a86-432e-abb7-1e6446ff1854'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_67']
  es_DefTaskRel_ES_209:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_196'
    defaultTaskGroup: '@es_DefTG_ES_9'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bedc5eba-6775-4af3-8355-f3f66e239ccd'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_238']
  es_DefTaskRel_ES_47:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_47'
    defaultTaskGroup: '@es_DefTG_ES_9'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '97aa217c-5429-41f4-9276-50ddebded412'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_68']
  es_DefTaskRel_ES_48:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_48'
    defaultTaskGroup: '@es_DefTG_ES_9'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cb834057-b577-4296-987c-1af01d020596'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_69', '@es_DefTaskRule_ES_70', '@es_DefTaskRule_ES_71', '@es_DefTaskRule_ES_72', '@es_DefTaskRule_ES_73', '@es_DefTaskRule_ES_74', '@es_DefTaskRule_ES_75', '@es_DefTaskRule_ES_239']
  es_DefTaskRel_ES_49:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_49'
    defaultTaskGroup: '@es_DefTG_ES_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c893481d-1118-4582-bc3b-3c1b7617a852'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_50:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_50'
    defaultTaskGroup: '@es_DefTG_ES_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c4d82264-9efe-4bf3-be9b-73db26b9f1dd'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_76']
  es_DefTaskRel_ES_51:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_51'
    defaultTaskGroup: '@es_DefTG_ES_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7ebf8af1-c5de-4ef2-ae3b-29c225699441'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_77']
  es_DefTaskRel_ES_52:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_52'
    defaultTaskGroup: '@es_DefTG_ES_10'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '784e3880-d1e6-48ea-80d1-df7c31c7b819'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_78']
  es_DefTaskRel_ES_53:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_53'
    defaultTaskGroup: '@es_DefTG_ES_10'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6f54814f-22ad-44a9-a005-9ca981d23f89'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_79', '@es_DefTaskRule_ES_80', '@es_DefTaskRule_ES_81', '@es_DefTaskRule_ES_82', '@es_DefTaskRule_ES_83', '@es_DefTaskRule_ES_84']
  es_DefTaskRel_ES_54:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_54'
    defaultTaskGroup: '@es_DefTG_ES_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7391a584-afe4-493d-8dc4-f5c018f83743'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_55:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_55'
    defaultTaskGroup: '@es_DefTG_ES_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b99a38bc-0562-4675-b707-00401cb84abd'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_85']
  es_DefTaskRel_ES_56:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_56'
    defaultTaskGroup: '@es_DefTG_ES_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5be54e7f-f55a-48a8-8a59-4cefaf12ce2a'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_86']
  es_DefTaskRel_ES_57:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_57'
    defaultTaskGroup: '@es_DefTG_ES_11'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '305b34ea-313a-4c60-bc88-f6a6bec27507'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_87', '@es_DefTaskRule_ES_88', '@es_DefTaskRule_ES_89']
  es_DefTaskRel_ES_58:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_58'
    defaultTaskGroup: '@es_DefTG_ES_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '39a940c9-a65c-4cdc-bfb9-274864cb4c14'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_59:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_59'
    defaultTaskGroup: '@es_DefTG_ES_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd774af34-778b-4f14-b3ac-221190cf4630'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_90']
  es_DefTaskRel_ES_60:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_60'
    defaultTaskGroup: '@es_DefTG_ES_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '789186aa-e791-48fb-8e6b-339752ceb28a'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_91']
  es_DefTaskRel_ES_61:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_61'
    defaultTaskGroup: '@es_DefTG_ES_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bb31e6d5-7da2-4976-a089-3249f7321352'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_92']
  es_DefTaskRel_ES_210:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_197'
    defaultTaskGroup: '@es_DefTG_ES_12'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '62d4d337-4106-48d7-9fd0-6cf199314495'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_240']
  es_DefTaskRel_ES_62:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_62'
    defaultTaskGroup: '@es_DefTG_ES_12'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b8dbd67e-7033-4ff1-b7f0-4f6eafe28e4e'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_93']
  es_DefTaskRel_ES_63:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_63'
    defaultTaskGroup: '@es_DefTG_ES_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '8ea6e3f1-ff5c-4124-9ea5-aeabdb7d47e9'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_94', '@es_DefTaskRule_ES_95', '@es_DefTaskRule_ES_96', '@es_DefTaskRule_ES_97', '@es_DefTaskRule_ES_98', '@es_DefTaskRule_ES_241']
  es_DefTaskRel_ES_164:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_71'
    defaultTaskGroup: '@es_DefTG_ES_12'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'c8108acb-09f7-4f91-b2bd-1518d8895815'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_64:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_64'
    defaultTaskGroup: '@es_DefTG_ES_13'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '40dcaa1d-3123-43a1-88f3-b51980157d8b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_65:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_65'
    defaultTaskGroup: '@es_DefTG_ES_13'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4c823a47-c367-445e-8738-97c68d70da16'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_99']
  es_DefTaskRel_ES_66:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_66'
    defaultTaskGroup: '@es_DefTG_ES_13'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ea26e5c9-f9f4-4a31-9c66-f9aaf8378754'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_100']
  es_DefTaskRel_ES_67:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_67'
    defaultTaskGroup: '@es_DefTG_ES_13'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e7e2fcfc-dccd-40a2-9ba8-9dde1a499b06'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_101']
  es_DefTaskRel_ES_68:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_68'
    defaultTaskGroup: '@es_DefTG_ES_13'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd94cc59b-3f75-4547-9a12-f944ed10493c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_102', '@es_DefTaskRule_ES_103', '@es_DefTaskRule_ES_104', '@es_DefTaskRule_ES_105', '@es_DefTaskRule_ES_106', '@es_DefTaskRule_ES_107']
  es_DefTaskRel_ES_69:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_69'
    defaultTaskGroup: '@es_DefTG_ES_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '15608bed-94b8-4322-94fb-aaf5e05f8372'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_70:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_70'
    defaultTaskGroup: '@es_DefTG_ES_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '4a827a06-0d4a-40dd-af86-f48365ba61eb'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_108']
  es_DefTaskRel_ES_71:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_71'
    defaultTaskGroup: '@es_DefTG_ES_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '052b5649-7c78-475e-8d83-49739f87bdc0'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_109']
  es_DefTaskRel_ES_72:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_163'
    defaultTaskGroup: '@es_DefTG_ES_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0e6f37b1-76f6-4f9a-9eb9-38d0ea3d5003'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_110']
  es_DefTaskRel_ES_73:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_72'
    defaultTaskGroup: '@es_DefTG_ES_14'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '681e5d69-4400-403b-984d-27f6bfb7f9aa'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_111']
  es_DefTaskRel_ES_74:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_73'
    defaultTaskGroup: '@es_DefTG_ES_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '69323f90-8f75-4728-b6c8-5f2ad89843d7'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_112', '@es_DefTaskRule_ES_113', '@es_DefTaskRule_ES_114', '@es_DefTaskRule_ES_246', '@es_DefTaskRule_ES_115']
  es_DefTaskRel_ES_165:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_60'
    defaultTaskGroup: '@es_DefTG_ES_14'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
    id: 'd550a708-f475-4d8a-8c73-ca1c0e9217bb'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_75:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_74'
    defaultTaskGroup: '@es_DefTG_ES_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2ad77caf-fc53-4f7c-bb7c-1bfb948023e4'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_76:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_75'
    defaultTaskGroup: '@es_DefTG_ES_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'bc2b11bf-3a9b-4018-9985-d64f7ccfc74c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_116']
  es_DefTaskRel_ES_77:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_76'
    defaultTaskGroup: '@es_DefTG_ES_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c0c73cf4-75a8-44e5-b681-359345d474ea'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_117']
  es_DefTaskRel_ES_78:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_77'
    defaultTaskGroup: '@es_DefTG_ES_15'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6f3b5fb3-7b7e-4eee-8548-25f24c89af25'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_118', '@es_DefTaskRule_ES_119', '@es_DefTaskRule_ES_120']
  es_DefTaskRel_ES_79:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_78'
    defaultTaskGroup: '@es_DefTG_ES_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4bd2b586-9c07-43b7-a3ee-360cab77eec6'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_80:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_79'
    defaultTaskGroup: '@es_DefTG_ES_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '167f49a5-5d44-4455-8ce1-4e260ede2a0b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_121']
  es_DefTaskRel_ES_81:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_80'
    defaultTaskGroup: '@es_DefTG_ES_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd70bf968-4dee-498a-aafb-7f12572da7f5'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_122']
  es_DefTaskRel_ES_82:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_81'
    defaultTaskGroup: '@es_DefTG_ES_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cb8ff1d4-7d22-4ebf-8f5a-f34554fcb0e6'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_123']
  es_DefTaskRel_ES_211:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_198'
    defaultTaskGroup: '@es_DefTG_ES_16'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '526f49be-61c9-46ce-a9d9-9140dea50471'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_242']
  es_DefTaskRel_ES_83:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_82'
    defaultTaskGroup: '@es_DefTG_ES_16'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b9fa892e-eef4-4ce4-8462-2293242f4a8a'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_124']
  es_DefTaskRel_ES_84:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_83'
    defaultTaskGroup: '@es_DefTG_ES_16'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '36874a83-33cd-4333-bcdb-63c2cb6622cb'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_125', '@es_DefTaskRule_ES_126', '@es_DefTaskRule_ES_127', '@es_DefTaskRule_ES_128', '@es_DefTaskRule_ES_129', '@es_DefTaskRule_ES_243']
  es_DefTaskRel_ES_85:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_84'
    defaultTaskGroup: '@es_DefTG_ES_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e9b0afa2-77e1-4c7c-aeac-ca4b7bb00f8b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_86:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_85'
    defaultTaskGroup: '@es_DefTG_ES_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a56af851-0714-4af2-9b36-c3007086153c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_130']
  es_DefTaskRel_ES_87:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_86'
    defaultTaskGroup: '@es_DefTG_ES_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '50d9a2ae-63ef-4192-a7a1-cd66a621279a'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_131']
  es_DefTaskRel_ES_88:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_87'
    defaultTaskGroup: '@es_DefTG_ES_17'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9b943c6d-5407-4b3d-a495-462855fd7774'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_132', '@es_DefTaskRule_ES_133', '@es_DefTaskRule_ES_134']
  es_DefTaskRel_ES_89:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_88'
    defaultTaskGroup: '@es_DefTG_ES_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ac926597-973e-43c5-b6bd-ab51cdc5c8b7'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_90:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_89'
    defaultTaskGroup: '@es_DefTG_ES_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f1fb2995-b703-46e9-9113-12e14f80072b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_135']
  es_DefTaskRel_ES_91:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_90'
    defaultTaskGroup: '@es_DefTG_ES_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f0c2b9cf-dee2-446b-ba64-0a497a0d3a08'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_136']
  es_DefTaskRel_ES_92:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_91'
    defaultTaskGroup: '@es_DefTG_ES_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f172d6c0-9790-45b2-918f-f4fd1158e7ea'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_137']
  es_DefTaskRel_ES_212:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_199'
    defaultTaskGroup: '@es_DefTG_ES_18'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9e80db52-1104-4d44-9875-5af57d7d10b4'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_244']
  es_DefTaskRel_ES_93:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_92'
    defaultTaskGroup: '@es_DefTG_ES_18'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '071f975f-8467-4003-a156-5589fd7a3183'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_138']
  es_DefTaskRel_ES_94:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_93'
    defaultTaskGroup: '@es_DefTG_ES_18'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9bdd72a7-1736-4b51-8ff4-6e63b1b7a686'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_139', '@es_DefTaskRule_ES_140', '@es_DefTaskRule_ES_141', '@es_DefTaskRule_ES_142', '@es_DefTaskRule_ES_143', '@es_DefTaskRule_ES_245']
  es_DefTaskRel_ES_95:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_94'
    defaultTaskGroup: '@es_DefTG_ES_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2e8ec7bb-7da1-4df5-a832-191a6cb49bc8'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_96:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_95'
    defaultTaskGroup: '@es_DefTG_ES_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cfff3184-8175-4206-8f2f-4fde3da3eb6c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_144']
  es_DefTaskRel_ES_97:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_96'
    defaultTaskGroup: '@es_DefTG_ES_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'edbf70b1-1d73-41fa-91dd-3eb9d7284073'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_145']
  es_DefTaskRel_ES_98:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_97'
    defaultTaskGroup: '@es_DefTG_ES_19'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e5fb27cc-dcbe-44a7-968d-f4f8e3d6291f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_146']
  es_DefTaskRel_ES_99:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_98'
    defaultTaskGroup: '@es_DefTG_ES_19'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '23b07032-dabf-416c-8b0d-dbe323d9acab'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_147', '@es_DefTaskRule_ES_148', '@es_DefTaskRule_ES_149', '@es_DefTaskRule_ES_150', '@es_DefTaskRule_ES_151', '@es_DefTaskRule_ES_152']
  es_DefTaskRel_ES_100:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_99'
    defaultTaskGroup: '@es_DefTG_ES_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '264572c7-4c84-4b08-a503-009b33c6854f'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_101:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_100'
    defaultTaskGroup: '@es_DefTG_ES_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '04858366-1541-4f0c-806e-4d7492eff234'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_153']
  es_DefTaskRel_ES_102:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_101'
    defaultTaskGroup: '@es_DefTG_ES_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6425e11d-b21b-4ba0-9611-33eda66852a9'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_154']
  es_DefTaskRel_ES_103:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_102'
    defaultTaskGroup: '@es_DefTG_ES_20'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e1e611f0-ba75-4166-a970-853dd82100d1'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_155', '@es_DefTaskRule_ES_156', '@es_DefTaskRule_ES_157']
  es_DefTaskRel_ES_104:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_103'
    defaultTaskGroup: '@es_DefTG_ES_21'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1efed017-75e2-496b-a413-846df00ad84b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_105:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_104'
    defaultTaskGroup: '@es_DefTG_ES_29'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'bd270c71-e652-4ce0-bd7f-8422b3ee96cb'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_106:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_105'
    defaultTaskGroup: '@es_DefTG_ES_29'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e1200b11-ac3c-4b18-b632-8a6d99ad8f95'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_107:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_106'
    defaultTaskGroup: '@es_DefTG_ES_30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c11a3348-95a7-4147-bd32-41e75fd6e6fa'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_108:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_107'
    defaultTaskGroup: '@es_DefTG_ES_30'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cd1a3683-8d56-4747-bb11-08a5fb7fa6c0'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_109:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_108'
    defaultTaskGroup: '@es_DefTG_ES_31'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f5f4500b-9c38-42ef-aed4-88b979fa9483'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_110:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_109'
    defaultTaskGroup: '@es_DefTG_ES_31'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1902ab3f-cf40-45c4-8ad0-53735302b849'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_111:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_110'
    defaultTaskGroup: '@es_DefTG_ES_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '93a4d0bb-b113-4540-ae8d-b8aac1f674b5'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_112:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_111'
    defaultTaskGroup: '@es_DefTG_ES_32'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'fb97c6fe-9b62-48bb-9102-4e039ef16e63'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_113:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_112'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'fdcb448b-a67d-4f80-a10c-c56c63f03761'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_114:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_113'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'cb044d34-1977-4fd5-ad39-72447f8034c4'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_115:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_114'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b5b65ecf-b693-4e36-8e65-d3f1923a9237'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_116:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_115'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4bdcb7a3-adc6-44c1-a949-02df1d4eeb61'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_117:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_116'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8f694f03-bfb1-4360-ae12-349f27e3b083'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_118:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_117'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'af82dd76-bbf3-444c-9c6c-dad4d5bc39ee'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_180:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_178'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e7a14119-e901-4987-a673-52f761150a0d'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_181:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_179'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8f9baabe-070f-472c-832e-2eb83e176645'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_194:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_187'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4a7fd6be-9752-4e1c-ac3f-bbbd07b327f2'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_166:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_164'
    defaultTaskGroup: '@es_DefTG_ES_33'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7ab50f28-b191-494f-b227-29a203935fdc'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_172:
    sequenceNumber: '110'
    defaultTask: '@es_DefTask_ES_170'
    defaultTaskGroup: '@es_DefTG_ES_33'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ea052f4d-94c8-4100-bd6f-7ccc3e366d2a'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_159', '@es_DefTaskRule_ES_160', '@es_DefTaskRule_ES_161', '@es_DefTaskRule_ES_162', '@es_DefTaskRule_ES_163', '@es_DefTaskRule_ES_164', '@es_DefTaskRule_ES_165', '@es_DefTaskRule_ES_166', '@es_DefTaskRule_ES_167', '@es_DefTaskRule_ES_168']
  es_DefTaskRel_ES_119:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_118'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f4ba1693-5bb3-491d-bf84-bc4bb939b3e4'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_120:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_119'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '44c1c4cd-ffcb-444e-aade-1aff85ab410c'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_121:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_120'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '2da883b1-a2ed-41f9-b740-e9873210b803'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_122:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_121'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e28dd014-543c-41b0-b191-fbc8d129ea13'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_123:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_122'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '67604cd4-fd2f-4794-bfa2-3ebe9cedcf51'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_124:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_123'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4bf1bb31-bf8f-4ed5-a0fb-fd2253360e20'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_125:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_124'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '90b52a3f-2e3e-472b-a886-a301e4ea31b9'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_126:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_125'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3fa65fd8-d724-438b-bc01-09b6dd8c434b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_193:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_186'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '52722bfe-917a-4103-bceb-abb8b347eed7'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_167:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_165'
    defaultTaskGroup: '@es_DefTG_ES_34'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'eded816f-62fc-406e-85f8-09677bb11190'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_173:
    sequenceNumber: '110'
    defaultTask: '@es_DefTask_ES_171'
    defaultTaskGroup: '@es_DefTG_ES_34'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd03c20b5-b130-4bc4-8f5c-695c09b0357b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_170', '@es_DefTaskRule_ES_171', '@es_DefTaskRule_ES_172', '@es_DefTaskRule_ES_173', '@es_DefTaskRule_ES_174', '@es_DefTaskRule_ES_175', '@es_DefTaskRule_ES_176', '@es_DefTaskRule_ES_177', '@es_DefTaskRule_ES_178', '@es_DefTaskRule_ES_179']
  es_DefTaskRel_ES_127:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_126'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '34b662bb-91d5-4c6e-91d6-65190159b5ea'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_128:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_127'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '66737b5a-a8db-4373-a845-bd037ba19844'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_129:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_128'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'f565827c-f6a9-49f4-944c-d13780605fbb'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_130:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_129'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '7c367de5-48c6-45eb-aa8f-22283d6ffef4'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_131:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_130'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9bea8a9c-bd76-4939-8b19-4c6a739773ae'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_132:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_131'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9b0489f2-3282-4090-86cf-54702fb9af87'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_168:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_166'
    defaultTaskGroup: '@es_DefTG_ES_35'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd567375f-1f14-4e60-830c-66b28612aa71'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_174:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_172'
    defaultTaskGroup: '@es_DefTG_ES_35'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '849577d5-1343-4e57-a797-77ed02644a10'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_180', '@es_DefTaskRule_ES_181', '@es_DefTaskRule_ES_182', '@es_DefTaskRule_ES_183', '@es_DefTaskRule_ES_184', '@es_DefTaskRule_ES_185', '@es_DefTaskRule_ES_186']
  es_DefTaskRel_ES_139:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_138'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '45bb89a5-40a2-4d09-947d-6a6012012e72'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_140:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_139'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '6ce978fa-ed5c-4d9e-a2be-d0c806fd43f9'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_141:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_140'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ff6cc0a1-5f34-4305-bc44-e7f412107626'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_142:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_141'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '620171d1-d114-4e1d-846a-1747dfb59250'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_143:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_142'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '23dc21bf-56ab-4b08-bc9a-8de0c7500dd0'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_144:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_143'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd3664cae-21ae-4abf-acd1-e202496cdb1d'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_178:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_176'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4dd25d09-6570-49db-b561-975dbba9206d'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_179:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_177'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ccb2b1c6-7075-4b46-9c04-83352c87c6c8'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_169:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_167'
    defaultTaskGroup: '@es_DefTG_ES_36'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4a202586-cb06-4b68-baa1-868d8c26f70a'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_175:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_173'
    defaultTaskGroup: '@es_DefTG_ES_36'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9639f62b-578d-4269-b4be-7f3a8bad6717'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_187', '@es_DefTaskRule_ES_188', '@es_DefTaskRule_ES_189', '@es_DefTaskRule_ES_190', '@es_DefTaskRule_ES_191', '@es_DefTaskRule_ES_192', '@es_DefTaskRule_ES_193', '@es_DefTaskRule_ES_194', '@es_DefTaskRule_ES_195']
  es_DefTaskRel_ES_145:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_144'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ef1962fe-9fda-48cb-960f-a205e0f5d095'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_146:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_145'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5d9fefb4-575f-44f9-be80-ddf4fb3e3925'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_147:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_146'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'fa644236-f968-4f51-8c27-50a0d1a4f005'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_148:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_147'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b9f87c37-cb89-49db-a1a0-560abfa64e54'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_149:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_148'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '93138b21-e24e-4504-86a4-c078d6533865'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_150:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_149'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '339b98ab-f725-4587-b9ad-f36914c81f9f'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_151:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_150'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c5becdd1-a699-4e09-85dd-0801ffe030b8'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_182:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_180'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c7820a4c-2287-454a-9cd3-6194ee3387bd'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_170:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_168'
    defaultTaskGroup: '@es_DefTG_ES_37'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '5b0cffa9-eab7-40d7-a9bc-2e85e1cb3fb5'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_176:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_174'
    defaultTaskGroup: '@es_DefTG_ES_37'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '77a68f0a-0b50-4a75-a8dd-c53cb119009c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_196', '@es_DefTaskRule_ES_197', '@es_DefTaskRule_ES_198', '@es_DefTaskRule_ES_199', '@es_DefTaskRule_ES_200', '@es_DefTaskRule_ES_201', '@es_DefTaskRule_ES_202', '@es_DefTaskRule_ES_203', '@es_DefTaskRule_ES_204']
  es_DefTaskRel_ES_152:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_151'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b76be890-4344-4f06-9fce-146d930e1f54'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_153:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_152'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'e2f13c0f-1260-4b76-ac45-53f918030527'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_154:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_153'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '4777f2b7-9280-47d8-9e58-63862dfe447a'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_155:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_154'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'db9ea8a6-cf39-4b68-802c-499391fef8c9'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_156:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_155'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'bb94f2c0-2e48-464d-a845-e60c28788b36'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_157:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_156'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '04da0333-6004-4858-9eff-ed2447e3ea89'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_158:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_157'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '950f98a1-c87a-4588-bc1d-13d9119a501b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_171:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_169'
    defaultTaskGroup: '@es_DefTG_ES_38'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '8745f43a-8ffe-4a1a-8c7a-3ad73d0c856d'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_177:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_175'
    defaultTaskGroup: '@es_DefTG_ES_38'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '89301e10-5f8d-4a92-b884-4f44ff28ea9b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_205', '@es_DefTaskRule_ES_206', '@es_DefTaskRule_ES_207', '@es_DefTaskRule_ES_208', '@es_DefTaskRule_ES_209', '@es_DefTaskRule_ES_210', '@es_DefTaskRule_ES_211', '@es_DefTaskRule_ES_212']
  es_DefTaskRel_ES_183:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_158'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '97d54c5d-3d50-4fb2-b00d-a5b7840a70bf'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_184:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_159'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '98f78a03-0e63-4c61-8a3b-9f4f8416739f'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_185:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_160'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'b112bf68-a07a-4ce7-83c1-a933165b92f5'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_186:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_161'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '0219afbd-dffa-4ae1-a9e1-68d37cd18aaf'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_187:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_162'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '56baa693-670c-4e47-a17e-449e5a81de28'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_188:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_181'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '539a3c40-0d9d-47ac-b294-7358cedeea01'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_189:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_182'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '3849499e-ca65-4d21-8d22-cc22c9317e65'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_190:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_183'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9654d8f2-4068-4ff2-a69f-ea2e2274ce03'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_191:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_184'
    defaultTaskGroup: '@es_DefTG_ES_39'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd907c5e0-4879-4578-b9c6-0d154ed76c4c'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_192:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_185'
    defaultTaskGroup: '@es_DefTG_ES_39'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '235308b8-2a2d-4386-81a1-2c57e6588290'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_213', '@es_DefTaskRule_ES_214', '@es_DefTaskRule_ES_215', '@es_DefTaskRule_ES_216', '@es_DefTaskRule_ES_217', '@es_DefTaskRule_ES_218', '@es_DefTaskRule_ES_219', '@es_DefTaskRule_ES_220', '@es_DefTaskRule_ES_221']
  es_DefTaskRel_ES_195:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_132'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '1fa569aa-f021-468e-be84-c4efe71ed3b0'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_196:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_133'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '67212d65-9fe3-44d4-a877-aca9ece2873d'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_197:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_134'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c90281eb-64f3-4936-82dc-f2c8109e77f1'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_198:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_135'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '20a90d1f-5a01-4c62-b025-17230f3325bd'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_199:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_136'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'fffa2765-0919-4cc2-b73c-34e7063a5ed6'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_200:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_137'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ecc528d6-7f54-4d77-81aa-975361650e49'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_201:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_188'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'ab70665b-b861-41a1-b335-8e1e005f0ca6'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_202:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_189'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '443636d5-5673-49f1-998f-d44daef35e4b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_203:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_190'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9629c06d-c71f-4e8b-a9ae-bea65d638999'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_204:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_191'
    defaultTaskGroup: '@es_DefTG_ES_40'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'd57f043f-1769-4f59-b03c-3c3673360e18'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_205:
    sequenceNumber: '110'
    defaultTask: '@es_DefTask_ES_192'
    defaultTaskGroup: '@es_DefTG_ES_40'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd412cf58-f9ed-4a0d-bf00-b2f6e6d7e4d8'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_222', '@es_DefTaskRule_ES_223', '@es_DefTaskRule_ES_224', '@es_DefTaskRule_ES_225', '@es_DefTaskRule_ES_226', '@es_DefTaskRule_ES_227', '@es_DefTaskRule_ES_228', '@es_DefTaskRule_ES_229', '@es_DefTaskRule_ES_230', '@es_DefTaskRule_ES_231']
  es_DefTaskRel_ES_214:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_200'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: 'c8a1002f-fe8f-411d-8629-7648452c4e5b'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_215:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_201'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a06b095f-f9d2-4082-bf1a-47c3b70598ae'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_247']
  es_DefTaskRel_ES_216:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_202'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '06b9bac2-dd32-431b-bfde-285d9ba112c6'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_248']
  es_DefTaskRel_ES_217:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_203'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0e3717b9-004b-43e7-a1f0-1c5eac5b73b9'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_249']
  es_DefTaskRel_ES_218:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_204'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5a0e7ba9-1540-4e17-be05-3cea483ef1f0'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_250']
  es_DefTaskRel_ES_219:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_205'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0046d90a-5140-418e-bd3e-2ea4b709a40c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_251']
  es_DefTaskRel_ES_220:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_206'
    defaultTaskGroup: '@es_DefTG_ES_42'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7327a3ba-e672-4de0-a156-ace44105c969'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_252']
  es_DefTaskRel_ES_221:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_207'
    defaultTaskGroup: '@es_DefTG_ES_42'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2ce53218-3c8a-44a7-9c3a-353e4eeab2f1'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_253']
  es_DefTaskRel_ES_222:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_208'
    defaultTaskGroup: '@es_DefTG_ES_42'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ff428b08-bcd1-460d-a8dc-2bc37b52a4bb'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_254']
  es_DefTaskRel_ES_223:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_209'
    defaultTaskGroup: '@es_DefTG_ES_42'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c29ddbd4-ec1b-4741-a382-7dc98306b840'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_255', '@es_DefTaskRule_ES_256', '@es_DefTaskRule_ES_257', '@es_DefTaskRule_ES_258', '@es_DefTaskRule_ES_259', '@es_DefTaskRule_ES_260', '@es_DefTaskRule_ES_261', '@es_DefTaskRule_ES_262', '@es_DefTaskRule_ES_263', '@es_DefTaskRule_ES_264', '@es_DefTaskRule_ES_265']
  es_DefTaskRel_ES_224:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_210'
    defaultTaskGroup: '@es_DefTG_ES_43'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '27b2a17f-a58c-449c-bd9c-55fa6a90d401'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_225:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_211'
    defaultTaskGroup: '@es_DefTG_ES_43'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7f90d298-9ad8-4030-880f-4fb97c47afd2'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_266']
  es_DefTaskRel_ES_226:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_212'
    defaultTaskGroup: '@es_DefTG_ES_43'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '377c41ff-1cc0-4d37-851a-906925cbcc8b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_267']
  es_DefTaskRel_ES_227:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_213'
    defaultTaskGroup: '@es_DefTG_ES_43'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ecdbbbd8-6faa-444c-b7c7-616f07c2b9b2'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_268']
  es_DefTaskRel_ES_228:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_214'
    defaultTaskGroup: '@es_DefTG_ES_43'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '71ec5375-f35e-45ea-ba79-c1d4d0482b76'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_269', '@es_DefTaskRule_ES_270', '@es_DefTaskRule_ES_271', '@es_DefTaskRule_ES_272', '@es_DefTaskRule_ES_273', '@es_DefTaskRule_ES_274']
  es_DefTaskRel_ES_229:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_215'
    defaultTaskGroup: '@es_DefTG_ES_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '9b4639a6-33e1-4d73-9003-f71f8c7282ca'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_230:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_216'
    defaultTaskGroup: '@es_DefTG_ES_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'ec188fd3-6e0e-4cf1-a097-47e3d66905a9'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_275']
  es_DefTaskRel_ES_231:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_217'
    defaultTaskGroup: '@es_DefTG_ES_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0111187c-2f7f-45a2-bf3e-b82caa73eb7d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_276']
  es_DefTaskRel_ES_232:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_218'
    defaultTaskGroup: '@es_DefTG_ES_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'b58eff7b-9857-4421-bdf0-9f4704db4df5'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_277']
  es_DefTaskRel_ES_233:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_219'
    defaultTaskGroup: '@es_DefTG_ES_44'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '27278125-2faf-417c-864c-4fe53a80ce08'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_278']
  es_DefTaskRel_ES_234:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_220'
    defaultTaskGroup: '@es_DefTG_ES_44'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9194e5be-02e4-40d0-98d8-eaef0687938d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_279', '@es_DefTaskRule_ES_280', '@es_DefTaskRule_ES_281', '@es_DefTaskRule_ES_282', '@es_DefTaskRule_ES_283']
  es_DefTaskRel_ES_235:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_221'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '83874541-94da-42cc-8668-620cb3f76966'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_236:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_222'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '56422c9c-92cb-4f0b-a586-50a502ab5603'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_284']
  es_DefTaskRel_ES_237:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_223'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'edcb7aa5-7b42-45f4-986b-009fa1c6ed82'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_285']
  es_DefTaskRel_ES_238:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_224'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fb3de61a-258f-47d4-87a9-19ec02d822a4'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_286']
  es_DefTaskRel_ES_239:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_225'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5e5bc760-20de-40ca-a93c-2e2d9f7866fc'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_287']
  es_DefTaskRel_ES_240:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_226'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3869251f-c2ac-4ac5-989b-62fc3a98ccb5'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_288']
  es_DefTaskRel_ES_241:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_227'
    defaultTaskGroup: '@es_DefTG_ES_45'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2ddf2bbf-b44a-45a7-97e5-b0bf93c1381b'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_289']
  es_DefTaskRel_ES_242:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_228'
    defaultTaskGroup: '@es_DefTG_ES_45'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '27e8546c-1d74-4b3d-a4c0-e17eec51a3fe'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_290']
  es_DefTaskRel_ES_243:
    sequenceNumber: '90'
    defaultTask: '@es_DefTask_ES_229'
    defaultTaskGroup: '@es_DefTG_ES_45'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '06b32733-f71c-443d-a205-8722a747c5ee'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_291']
  es_DefTaskRel_ES_244:
    sequenceNumber: '100'
    defaultTask: '@es_DefTask_ES_230'
    defaultTaskGroup: '@es_DefTG_ES_45'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fdce7e3d-d6d8-4c18-97a3-86dbffd6fa75'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_292', '@es_DefTaskRule_ES_293', '@es_DefTaskRule_ES_294', '@es_DefTaskRule_ES_295', '@es_DefTaskRule_ES_296', '@es_DefTaskRule_ES_297', '@es_DefTaskRule_ES_298', '@es_DefTaskRule_ES_299', '@es_DefTaskRule_ES_300', '@es_DefTaskRule_ES_301', '@es_DefTaskRule_ES_302']
  es_DefTaskRel_ES_245:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_231'
    defaultTaskGroup: '@es_DefTG_ES_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '18a4cf98-bb8a-4f9f-a980-c653b46f0534'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_246:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_232'
    defaultTaskGroup: '@es_DefTG_ES_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3a669207-523a-4deb-8a0e-be9bb7a46b00'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_303']
  es_DefTaskRel_ES_247:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_233'
    defaultTaskGroup: '@es_DefTG_ES_46'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '55b5e569-41fa-4e3e-8b0f-0be12cd5d6d0'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_304']
  es_DefTaskRel_ES_248:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_234'
    defaultTaskGroup: '@es_DefTG_ES_46'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e4a02729-32e9-4add-b5e1-276f313ebea8'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_305']
  es_DefTaskRel_ES_249:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_235'
    defaultTaskGroup: '@es_DefTG_ES_46'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '78cf4669-6538-40ca-84e7-81c72c1a4643'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_306', '@es_DefTaskRule_ES_307', '@es_DefTaskRule_ES_308', '@es_DefTaskRule_ES_309', '@es_DefTaskRule_ES_310', '@es_DefTaskRule_ES_311']
  es_DefTaskRel_ES_250:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_236'
    defaultTaskGroup: '@es_DefTG_ES_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '42aa744b-4775-4e4a-95c2-72929d8235e3'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_251:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_237'
    defaultTaskGroup: '@es_DefTG_ES_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a3a5aecf-3541-4d78-9c12-b841279f6b3f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_312']
  es_DefTaskRel_ES_252:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_238'
    defaultTaskGroup: '@es_DefTG_ES_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a326420d-8c4c-4a21-affd-3ab585a8a58e'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_313']
  es_DefTaskRel_ES_253:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_239'
    defaultTaskGroup: '@es_DefTG_ES_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '32f44911-d42c-4667-85d0-8f55428980d9'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_314']
  es_DefTaskRel_ES_254:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_240'
    defaultTaskGroup: '@es_DefTG_ES_47'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c3a42d75-81d2-41c9-a059-f4a7384bae47'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_315']
  es_DefTaskRel_ES_255:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_241'
    defaultTaskGroup: '@es_DefTG_ES_47'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '94ed11d6-84f2-4265-b613-1e9c7e9a648f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_316']
  es_DefTaskRel_ES_256:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_242'
    defaultTaskGroup: '@es_DefTG_ES_47'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '17b53ab9-4b9b-4261-bdbc-68ab5cbb2619'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_317']
  es_DefTaskRel_ES_257:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_243'
    defaultTaskGroup: '@es_DefTG_ES_47'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cd6b7c38-b4ae-45f3-a719-88d041270922'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_318', '@es_DefTaskRule_ES_319', '@es_DefTaskRule_ES_320', '@es_DefTaskRule_ES_321', '@es_DefTaskRule_ES_322', '@es_DefTaskRule_ES_323', '@es_DefTaskRule_ES_324', '@es_DefTaskRule_ES_325', '@es_DefTaskRule_ES_326']
  es_DefTaskRel_ES_258:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_244'
    defaultTaskGroup: '@es_DefTG_ES_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '82490c52-a350-4697-abed-81c1ca54aa2c'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_259:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_245'
    defaultTaskGroup: '@es_DefTG_ES_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'f4933f2f-c158-418a-bd00-ffe724e2e669'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_327']
  es_DefTaskRel_ES_260:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_246'
    defaultTaskGroup: '@es_DefTG_ES_48'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '7aa0e155-1fca-48cb-90e9-aed6b85dde1c'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_328']
  es_DefTaskRel_ES_261:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_247'
    defaultTaskGroup: '@es_DefTG_ES_48'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '5ccc041b-e8f7-4246-af0f-5f5c91fef669'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_329']
  es_DefTaskRel_ES_262:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_248'
    defaultTaskGroup: '@es_DefTG_ES_48'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '82338362-2053-4cdc-abb4-6f8b9a7e6dc8'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_330', '@es_DefTaskRule_ES_331', '@es_DefTaskRule_ES_332', '@es_DefTaskRule_ES_333', '@es_DefTaskRule_ES_334', '@es_DefTaskRule_ES_335']
  es_DefTaskRel_ES_263:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_249'
    defaultTaskGroup: '@es_DefTG_ES_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '273e2c09-3563-4dfb-8c8a-d07665827137'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_264:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_250'
    defaultTaskGroup: '@es_DefTG_ES_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6668283b-6d84-4ab2-ac1f-6a10903184ef'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_336']
  es_DefTaskRel_ES_265:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_251'
    defaultTaskGroup: '@es_DefTG_ES_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0165c7b0-b19b-4361-824f-7c39a280b011'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_337']
  es_DefTaskRel_ES_266:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_252'
    defaultTaskGroup: '@es_DefTG_ES_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '6bf79afd-f089-4292-9bfa-b1f556f1ca7d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_338']
  es_DefTaskRel_ES_267:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_253'
    defaultTaskGroup: '@es_DefTG_ES_49'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'fc9251dd-71d2-4fbc-9832-f66232d6ff1d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_339']
  es_DefTaskRel_ES_268:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_254'
    defaultTaskGroup: '@es_DefTG_ES_49'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '07e25cb9-f1a8-4c26-9738-6fe25a1f820d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_340', '@es_DefTaskRule_ES_341', '@es_DefTaskRule_ES_342', '@es_DefTaskRule_ES_343', '@es_DefTaskRule_ES_344']
  es_DefTaskRel_ES_269:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_255'
    defaultTaskGroup: '@es_DefTG_ES_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '92565fd7-075d-4bba-a2bb-1046652845f1'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_270:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_256'
    defaultTaskGroup: '@es_DefTG_ES_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'c40a9857-7908-4cb0-953b-a8b5a66fe384'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_345']
  es_DefTaskRel_ES_271:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_257'
    defaultTaskGroup: '@es_DefTG_ES_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'cb9bc081-d347-4b4a-a6cf-aad807f7c42f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_346']
  es_DefTaskRel_ES_272:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_258'
    defaultTaskGroup: '@es_DefTG_ES_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '73cf99f3-d1db-40b0-bd2e-7d3d379fd2ee'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_347']
  es_DefTaskRel_ES_273:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_259'
    defaultTaskGroup: '@es_DefTG_ES_50'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '88809322-d246-424c-849f-77a40fc56692'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_348']
  es_DefTaskRel_ES_274:
    sequenceNumber: '60'
    defaultTask: '@es_DefTask_ES_260'
    defaultTaskGroup: '@es_DefTG_ES_50'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'd310b16a-65d4-4bf1-bffd-a8a0afa06116'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_349']
  es_DefTaskRel_ES_275:
    sequenceNumber: '70'
    defaultTask: '@es_DefTask_ES_261'
    defaultTaskGroup: '@es_DefTG_ES_50'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'a4767dcf-bb28-4383-9d39-c493b649ba6d'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_350']
  es_DefTaskRel_ES_276:
    sequenceNumber: '80'
    defaultTask: '@es_DefTask_ES_262'
    defaultTaskGroup: '@es_DefTG_ES_50'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '24558924-7e8c-4dda-876b-4b2c5135d900'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_351', '@es_DefTaskRule_ES_352', '@es_DefTaskRule_ES_353', '@es_DefTaskRule_ES_354', '@es_DefTaskRule_ES_355', '@es_DefTaskRule_ES_356', '@es_DefTaskRule_ES_357', '@es_DefTaskRule_ES_358', '@es_DefTaskRule_ES_359']
  es_DefTaskRel_ES_277:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_263'
    defaultTaskGroup: '@es_DefTG_ES_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '81c2a579-59c7-464a-be0a-65d83936905a'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_278:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_264'
    defaultTaskGroup: '@es_DefTG_ES_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '2a8723c3-5af0-4755-8a85-1d14f6729ba4'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_360']
  es_DefTaskRel_ES_279:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_265'
    defaultTaskGroup: '@es_DefTG_ES_51'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '0420bb78-4793-4c71-86aa-318961c13d78'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_361']
  es_DefTaskRel_ES_281:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_267'
    defaultTaskGroup: '@es_DefTG_ES_51'
    optional: true
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '3a8615e9-6267-41f0-bfc1-97c0d96f03ad'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_363']
  es_DefTaskRel_ES_280:
    sequenceNumber: '50'
    defaultTask: '@es_DefTask_ES_266'
    defaultTaskGroup: '@es_DefTG_ES_51'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: 'e0184b8e-01fa-46a2-956c-455abe1caaac'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_362', '@es_DefTaskRule_ES_364', '@es_DefTaskRule_ES_365', '@es_DefTaskRule_ES_366', '@es_DefTaskRule_ES_367', '@es_DefTaskRule_ES_368']
  es_DefTaskRel_ES_282:
    sequenceNumber: '10'
    defaultTask: '@es_DefTask_ES_268'
    defaultTaskGroup: '@es_DefTG_ES_52'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    id: '673cb74a-e68b-49c1-9c79-cf97cd41cf07'
    tenant: '<getSpainTenant()>'
  es_DefTaskRel_ES_283:
    sequenceNumber: '20'
    defaultTask: '@es_DefTask_ES_269'
    defaultTaskGroup: '@es_DefTG_ES_52'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9b827fb5-e9c4-4fbb-9ec9-7999fbe006bf'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_369']
  es_DefTaskRel_ES_284:
    sequenceNumber: '30'
    defaultTask: '@es_DefTask_ES_270'
    defaultTaskGroup: '@es_DefTG_ES_52'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '9eee73b2-1e5b-4812-9cc1-d1f2dd22785f'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_370']
  es_DefTaskRel_ES_285:
    sequenceNumber: '40'
    defaultTask: '@es_DefTask_ES_271'
    defaultTaskGroup: '@es_DefTG_ES_52'
    ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
    ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::ENABLED)>'
    ruleDefault: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
    id: '56e66fff-276b-47ad-a0eb-1c1d0b47e196'
    tenant: '<getSpainTenant()>'
    rules: ['@es_DefTaskRule_ES_371', '@es_DefTaskRule_ES_372', '@es_DefTaskRule_ES_373', '@es_DefTaskRule_ES_374', '@es_DefTaskRule_ES_375']
