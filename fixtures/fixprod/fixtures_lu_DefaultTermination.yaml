App\Domain\Entity\DefaultTermination:
  lu_DefTerm_LU_1:
    text: '{{Administrative_error}}'
    externalId: '2'
    id: '657d6e7d-c943-441a-b123-8cdde8b20720'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_2:
    text: '{{Extra_sheet}}'
    externalId: '3'
    id: '1b94361a-1f5d-4f73-95e0-bf65c95604c1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_3:
    text: '{{No_equipment_available}}'
    externalId: '4'
    id: '2add42b3-9a37-41b3-9c27-25471f7b2317'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_4:
    text: '{{Road_blocked_works}}'
    externalId: '5'
    id: '34b48eec-1c58-413b-b918-0d51547ceaa4'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_5:
    text: '{{Transport_cancelled}}'
    externalId: '6'
    id: '28af22dc-158b-438d-98fb-64b7426f67e8'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_6:
    text: '{{Miscellaneous_not_for_billing}}'
    externalId: '7'
    id: '8111df96-cc71-48a7-9eef-d9f3755072d1'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_7:
    text: '{{Customer_stop_service}}'
    externalId: '8'
    id: 'f82ffb65-cdd3-4501-b707-67d45400af53'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_8:
    text: '{{Customer_modifications}}'
    externalId: '9'
    id: '11d7c5cd-da64-4816-b493-366e4c88e702'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_9:
    text: '{{Customer_not_on_site}}'
    externalId: '10'
    id: 'd8b5220a-bb07-4c8c-a062-2548078de098'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_10:
    text: '{{Crane_not_available}}'
    externalId: '11'
    id: 'd2d06d66-dbb1-443c-a9d7-fba687c4be72'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_11:
    text: '{{Impossible_placement}}'
    externalId: '12'
    id: 'f1656ae1-80d8-4d75-a142-422618cf3005'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_12:
    text: '{{Cannot_be_emptied}}'
    externalId: '13'
    id: '54465814-d46c-40a9-8d4a-91a67d59f925'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_13:
    text: '{{Container_waste_not_offered}}'
    externalId: '14'
    id: '9c405a1f-cb46-4583-b804-ac8300bea158'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_14:
    text: '{{Container_not_taken_out}}'
    externalId: '15'
    id: '14d51be1-5561-4793-a512-6973ea191062'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_15:
    text: '{{Container_too_heavy/overfilled}}'
    externalId: '16'
    id: '34bc034e-2d97-4e4a-97de-a6bf464f952b'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_16:
    text: '{{container_empty}}'
    externalId: '17'
    id: '9601e5ac-7074-44cb-b03c-de164e34814d'
    tenant: '<getLuxembourgTenant()>'
  lu_DefTerm_LU_17:
    text: '{{Miscellaneous_to_be_billed}}'
    externalId: '18'
    id: 'f7158820-674b-4051-987c-2e1bae617a78'
    tenant: '<getLuxembourgTenant()>'
