App\Domain\Entity\DefaultTask:
  nl_DefTask_NL_1:
    name: '{{Arrival}}'
    id: 'a250bd5f-7fb0-4165-90ac-ddb56e2115ce'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_1']
  nl_DefTask_NL_2:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'cb356268-b207-4722-b873-8384ceb1190f'
    type: 'signature'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_3_1']
  nl_DefTask_NL_3:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '6f898e1b-5388-4f7b-9e18-7a2ae30c94ee'
    type: 'amount_per_container_type_1'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_1', '@nl_DefElem_NL_13_1']
  nl_DefTask_NL_4:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '5f9931c2-2479-431e-9275-2c9a47515139'
    type: 'amount_per_container_type_2'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_2', '@nl_DefElem_NL_13_2']
  nl_DefTask_NL_5:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '04a24765-5c31-476c-9f71-75bfc41d757f'
    type: 'amount_per_container_type_3'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_3', '@nl_DefElem_NL_13_3']
  nl_DefTask_NL_6:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '5391a118-658e-4407-8756-820e4dafbf05'
    type: 'amount_per_container_type_4'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_4', '@nl_DefElem_NL_13_4']
  nl_DefTask_NL_7:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '9e1d46bd-dbcf-43ab-ae58-658cf4e22ff3'
    type: 'amount_per_container_type_5'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_5', '@nl_DefElem_NL_13_5']
  nl_DefTask_NL_8:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'fc8fff16-8d9c-43d8-9a41-09ea55b34cbd'
    type: 'amount_per_container_type_6'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_6', '@nl_DefElem_NL_13_6']
  nl_DefTask_NL_9:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '654be680-5fbb-4404-9a76-5f71ce8ed061'
    type: 'amount_per_container_type_7'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_7', '@nl_DefElem_NL_13_7']
  nl_DefTask_NL_10:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '99c0e64b-4608-4c35-92b1-b134a97d8d98'
    type: 'amount_per_container_type_8'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_8', '@nl_DefElem_NL_13_8']
  nl_DefTask_NL_11:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '5b915776-cd39-4018-b1c0-df7dff8d6b55'
    type: 'amount_per_container_type_9'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_9', '@nl_DefElem_NL_13_9']
  nl_DefTask_NL_12:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '6880b9e5-72da-472a-8bbf-127e53602a6f'
    type: 'amount_per_container_type_10'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_10', '@nl_DefElem_NL_13_10']
  nl_DefTask_NL_13:
    name: '{{Ready}}'
    id: '33e56fec-6859-4a91-8fad-96d44f294910'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_1']
  nl_DefTask_NL_14:
    name: '{{Arrival}}'
    id: '313e8daa-c261-4ea8-a6f0-c3db02f5852b'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_2']
  nl_DefTask_NL_15:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'a890cca4-bf11-4551-ba0c-7a0b7b24fde5'
    type: 'signature'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_3_2']
  nl_DefTask_NL_16:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'eedf9238-0e75-4171-9fb7-a979b4e2feb4'
    type: 'customer_weight'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_1', '@nl_DefElem_NL_6_1']
  nl_DefTask_NL_17:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'beed01e4-155d-4328-80b4-d1b0a6a2a89d'
    type: 'amount_per_container_type_1'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_11', '@nl_DefElem_NL_13_11']
  nl_DefTask_NL_18:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '747dea91-8eb4-4de1-a391-6b2bca18a3cf'
    type: 'amount_per_container_type_2'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_12', '@nl_DefElem_NL_13_12']
  nl_DefTask_NL_19:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '4e66d4b4-8ec9-41d5-8436-a3b9e6f0796c'
    type: 'amount_per_container_type_3'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_13', '@nl_DefElem_NL_13_13']
  nl_DefTask_NL_20:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'b1cb5b89-59d2-4dc3-a934-70fd57cdf6e7'
    type: 'amount_per_container_type_4'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_14', '@nl_DefElem_NL_13_14']
  nl_DefTask_NL_21:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '4809bfff-7740-4bc5-9344-348978a2a6b6'
    type: 'amount_per_container_type_5'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_15', '@nl_DefElem_NL_13_15']
  nl_DefTask_NL_22:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '28623612-f75a-40d7-97fa-1b17e3513939'
    type: 'amount_per_container_type_6'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_16', '@nl_DefElem_NL_13_16']
  nl_DefTask_NL_23:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '38e63368-0de0-47a1-8f53-f7d874baf4af'
    type: 'amount_per_container_type_7'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_17', '@nl_DefElem_NL_13_17']
  nl_DefTask_NL_24:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'ccc2f1a5-9264-42fc-bc79-9057fd909228'
    type: 'amount_per_container_type_8'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_18', '@nl_DefElem_NL_13_18']
  nl_DefTask_NL_25:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '1df48f7c-0f9c-49ea-aaf5-001e279f7eed'
    type: 'amount_per_container_type_9'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_19', '@nl_DefElem_NL_13_19']
  nl_DefTask_NL_26:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'e0ca361c-b7ff-490f-b1ac-62e34de70f53'
    type: 'amount_per_container_type_10'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_20', '@nl_DefElem_NL_13_20']
  nl_DefTask_NL_27:
    name: '{{Ready}}'
    id: '2b1c7bb1-6c97-4e16-af7a-90990d56d5dd'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_2']
  nl_DefTask_NL_28:
    name: '{{Arrival}}'
    id: '2f629b18-c11b-4456-be19-1c413b878cbf'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_3']
  nl_DefTask_NL_29:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'd23c9695-c1ff-4a1b-848e-c12c0e1d8aba'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_2', '@nl_DefElem_NL_6_2', '@nl_DefElem_NL_14_1']
  nl_DefTask_NL_30:
    name: '{{Weighing_data_remark}}'
    activatedBySapData: true
    id: '6948721c-def2-4b1c-84c4-8f3c188da647'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_1']
  nl_DefTask_NL_31:
    name: '{{Ready}}'
    id: '3431819b-f5d4-4155-a5bf-f732f708b5fa'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_3']
  nl_DefTask_NL_32:
    name: '{{Arrival}}'
    id: 'cf2862f1-bbd9-4869-8021-f0fc532e4156'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_4']
  nl_DefTask_NL_33:
    name: '{{Ready}}'
    id: '5aa6a7ae-49bc-457b-a348-1541702a1aa1'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_4']
  nl_DefTask_NL_34:
    name: '{{Arrival}}'
    id: '669417d4-81f1-4f23-8270-f70c9d52c66e'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_5']
  nl_DefTask_NL_35:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: '211b07b9-0bda-4b29-9c29-6e111b465db3'
    type: 'signature'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_3_3']
  nl_DefTask_NL_36:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: 'd071ce9e-7d5a-4482-82bf-31d6a9d3722d'
    type: 'customer_weight'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_3', '@nl_DefElem_NL_6_3']
  nl_DefTask_NL_37:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '50c0dd3e-83ad-4592-8510-b5de85422e5f'
    type: 'amount_per_container_type_1'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_21', '@nl_DefElem_NL_13_21']
  nl_DefTask_NL_38:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'ad212b5a-a9fa-4fe8-a526-4719edc74b05'
    type: 'amount_per_container_type_2'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_22', '@nl_DefElem_NL_13_22']
  nl_DefTask_NL_39:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '344fe0db-60c3-4fe9-bf97-970f76703f5b'
    type: 'amount_per_container_type_3'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_23', '@nl_DefElem_NL_13_23']
  nl_DefTask_NL_40:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '615610c7-8513-415d-aacf-901c6953c57a'
    type: 'amount_per_container_type_4'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_24', '@nl_DefElem_NL_13_24']
  nl_DefTask_NL_41:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '71c43bd7-2f96-4ad4-bb08-fd2053429733'
    type: 'amount_per_container_type_5'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_25', '@nl_DefElem_NL_13_25']
  nl_DefTask_NL_42:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '7415d897-b769-40ac-8571-0853e1ce2c60'
    type: 'amount_per_container_type_6'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_26', '@nl_DefElem_NL_13_26']
  nl_DefTask_NL_43:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'f332ba3d-73df-4914-b127-bf2000519a48'
    type: 'amount_per_container_type_7'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_27', '@nl_DefElem_NL_13_27']
  nl_DefTask_NL_44:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'c5341b0b-6339-4720-a4a5-784076a693e9'
    type: 'amount_per_container_type_8'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_28', '@nl_DefElem_NL_13_28']
  nl_DefTask_NL_45:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '657e1337-8ff5-4ead-9294-eb772e04c06a'
    type: 'amount_per_container_type_9'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_29', '@nl_DefElem_NL_13_29']
  nl_DefTask_NL_46:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '569be179-4ae3-4e23-8ff4-3a52a422accb'
    type: 'amount_per_container_type_10'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_30', '@nl_DefElem_NL_13_30']
  nl_DefTask_NL_47:
    name: '{{Container_weighing_data}}'
    activatedBySapData: true
    id: '62c11859-6e03-44da-a490-9f5f0f358550'
    type: 'scale_weighing_data'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_12_1']
    taskActions: ['@nl_DefTaskAction_NL_1', '@nl_DefTaskAction_NL_2']
  nl_DefTask_NL_48:
    name: '{{Ready}}'
    id: 'e3730c41-5c28-4328-89be-c2f95951c245'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_5']
  nl_DefTask_NL_49:
    name: '{{Arrival}}'
    id: '5061199a-382c-44ee-bd31-98a8e21e6df1'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_6']
  nl_DefTask_NL_50:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    activatedBySapData: true
    id: 'e0504cb8-243e-485e-93c5-9eef1af4ceeb'
    type: 'signature'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_3_4']
  nl_DefTask_NL_51:
    name: '{{Customer_weighing}}'
    activatedBySapData: true
    id: '105986f9-e4e2-4502-83b5-52e571bd38f7'
    type: 'customer_weight'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_4', '@nl_DefElem_NL_6_4']
  nl_DefTask_NL_52:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'a4367f03-04d5-4f1f-b111-4fb57a645659'
    type: 'amount_per_container_type_1'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_31', '@nl_DefElem_NL_13_31']
  nl_DefTask_NL_53:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '75cb5073-72f5-4c79-9b3f-291f67f489ca'
    type: 'amount_per_container_type_2'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_32', '@nl_DefElem_NL_13_32']
  nl_DefTask_NL_54:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '228149bb-eede-4d8e-b9da-a1713c810dca'
    type: 'amount_per_container_type_3'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_33', '@nl_DefElem_NL_13_33']
  nl_DefTask_NL_55:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '5b9a6993-6ab6-4de1-8d0b-3349daf090ed'
    type: 'amount_per_container_type_4'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_34', '@nl_DefElem_NL_13_34']
  nl_DefTask_NL_56:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '47c0ba3c-f3b7-4230-86eb-be6767a56874'
    type: 'amount_per_container_type_5'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_35', '@nl_DefElem_NL_13_35']
  nl_DefTask_NL_57:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '2641e1f1-c02c-48b3-a210-49d0f9d6b24c'
    type: 'amount_per_container_type_6'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_36', '@nl_DefElem_NL_13_36']
  nl_DefTask_NL_58:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'c9b80d25-e62d-46fa-934e-dc66d1db43c8'
    type: 'amount_per_container_type_7'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_37', '@nl_DefElem_NL_13_37']
  nl_DefTask_NL_59:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'f8cdba72-4b77-4df9-a740-1d643f4dbf7e'
    type: 'amount_per_container_type_8'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_38', '@nl_DefElem_NL_13_38']
  nl_DefTask_NL_60:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: 'ea405210-f3ab-4696-b7c5-1eba2a28ae31'
    type: 'amount_per_container_type_9'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_39', '@nl_DefElem_NL_13_39']
  nl_DefTask_NL_61:
    name: 'AmountPerContainerType'
    activatedBySapData: true
    id: '71905b9e-b0fa-4f11-a69b-7232694b2951'
    type: 'amount_per_container_type_10'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_4_40', '@nl_DefElem_NL_13_40']
  nl_DefTask_NL_62:
    name: '{{Ready}}'
    id: 'acf3e499-0a37-47a4-b57a-c74487acb35c'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_6']
  nl_DefTask_NL_63:
    name: '{{Arrival}}'
    id: '4228d8f1-cf54-40a8-bf49-c59e05dce3a0'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_1_7']
  nl_DefTask_NL_64:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '959d23b4-bab1-44b4-99e6-9c23924713cc'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_5', '@nl_DefElem_NL_6_5', '@nl_DefElem_NL_14_2']
  nl_DefTask_NL_65:
    name: '{{Weighing_data_remark}}'
    activatedBySapData: true
    id: '54697096-ef8a-4b6d-804e-ab8e7d24e1fa'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_2']
  nl_DefTask_NL_66:
    name: '{{Ready}}'
    id: 'c7684487-600b-494e-bb40-869e4da00fdf'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_7']
  nl_DefTask_NL_67:
    name: '{{Ready}}'
    id: '61ae9378-7ffe-4f05-8385-400adc83651a'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_8']
  nl_DefTask_NL_68:
    name: '{{Free_text}}'
    id: '8d923322-4590-4530-a86f-61aeb979969f'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_3']
  nl_DefTask_NL_69:
    name: '{{Ready}}'
    id: 'd9fe6560-c8d8-40a0-916d-5c7b7575023e'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_9']
  nl_DefTask_NL_70:
    name: '{{Free_text}}'
    id: '1593acd9-2ff7-42cc-90af-e4692c243835'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_4']
  nl_DefTask_NL_71:
    name: '{{Ready}}'
    id: '3c8d6ad5-d036-4947-9199-cd803447c1cc'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_10']
  nl_DefTask_NL_72:
    name: '{{Photo}}'
    id: '5b68c0cb-b00a-4e49-a048-4dfebab5fb5e'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_1']
  nl_DefTask_NL_73:
    name: '{{Ready}}'
    id: '3727c22e-eb5e-4727-800a-cbea3bfc3c2f'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_11']
  nl_DefTask_NL_74:
    name: '{{Ready}}'
    id: '7db1fd3a-4d3a-4944-9e3d-31e15b5b44a0'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_12']
  nl_DefTask_NL_75:
    name: '{{Ready}}'
    id: '67bab862-99f2-489d-9d71-098d294f7b4c'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_13']
  nl_DefTask_NL_76:
    name: '{{Ready}}'
    id: 'ac8efe39-4c73-4cfe-a297-00d450aea511'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_14']
  nl_DefTask_NL_77:
    name: '{{Ready}}'
    id: 'd37e4a62-02d5-4f55-b277-f6d45a94e979'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_15']
  nl_DefTask_NL_78:
    name: '{{Fueling}}'
    repeatable: true
    id: '57fb9734-6471-4e88-a572-324196d0f8c0'
    type: 'fueling'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_7_1', '@nl_DefElem_NL_8_1', '@nl_DefElem_NL_16_1']
  nl_DefTask_NL_79:
    name: '{{Ready}}'
    id: '4b888524-fdb1-4c89-9025-cde00c19758d'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_16']
  nl_DefTask_NL_80:
    name: '{{Ready}}'
    id: '471c503f-de9a-4960-8878-f8f89d738402'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_17']
  nl_DefTask_NL_81:
    name: '{{Ready}}'
    id: '63e47baa-8ab4-4040-8906-86ee10d80af4'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_18']
  nl_DefTask_NL_82:
    name: '{{Free_text}}'
    id: 'c0bb00dc-cb8d-4495-b35a-e17210b67c25'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_5']
  nl_DefTask_NL_83:
    name: '{{Photo}}'
    id: '7b2c9082-9635-4ff9-b704-51d8523f342d'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_2']
  nl_DefTask_NL_84:
    name: '{{Free_text}}'
    id: '6da0d974-e826-4d85-bc71-a8c8581ba321'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_6']
  nl_DefTask_NL_85:
    name: '{{Free_text}}'
    id: 'a386ae13-31d3-43ba-8da8-be5af6c2620c'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_7']
  nl_DefTask_NL_86:
    name: '{{Photo}}'
    id: 'f45c0887-6314-44e5-ba53-9fa8d4702088'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_3']
  nl_DefTask_NL_87:
    name: '{{Free_text}}'
    id: '42c53a0f-8e99-4189-8afe-1b5ca0b666a7'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_8']
  nl_DefTask_NL_88:
    name: '{{Photo}}'
    id: '875db8d0-0187-47f8-96fe-44f80491f332'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_4']
  nl_DefTask_NL_89:
    name: '{{Free_text}}'
    id: '02886ee9-b418-4802-a1ab-81ca4716868c'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_9']
  nl_DefTask_NL_90:
    name: '{{Waitingtime}}'
    id: 'cc554282-9481-4572-9375-01bc24091b64'
    type: 'waitingtime'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_17_1']
  nl_DefTask_NL_91:
    name: '{{Select_reason}}'
    id: '08166125-20f1-4bab-af34-9fa69d5ca10d'
    type: 'select'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_18_1']
  nl_DefTask_NL_92:
    name: '{{Free_text}}'
    id: 'bdf35fda-a653-4de3-9034-6b6b8d91b28b'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_10']
  nl_DefTask_NL_93:
    name: '{{Free_text}}'
    id: 'efc9f548-6d69-4ca3-800b-f949ea0cfd3d'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_11']
  nl_DefTask_NL_94:
    name: '{{Photo}}'
    id: '6df40055-60db-4c5f-a4f3-ecf6a8a57cbf'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_5']
  nl_DefTask_NL_95:
    name: '{{Free_text}}'
    id: 'f581fc97-7c1b-4a6b-bf29-96a8db74c35d'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_12']
  nl_DefTask_NL_96:
    name: '{{Free_text}}'
    id: '2313d482-e3c5-406b-ab7f-821a412ad131'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_13']
  nl_DefTask_NL_97:
    name: '{{Photo}}'
    id: '2a2c8438-eb88-48ff-921b-eb099b03bb4a'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_6']
  nl_DefTask_NL_98:
    name: '{{Free_text}}'
    id: '84c651e3-2f46-497e-a475-6f30e5c1d4b7'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_14']
  nl_DefTask_NL_99:
    name: '{{Photo}}'
    id: 'd13170ae-bff3-4ce3-b663-9782e92e7fe0'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_7']
  nl_DefTask_NL_100:
    name: '{{Free_text}}'
    id: '59f8fb80-21ff-4da4-9ba7-41e4a553788e'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_15']
  nl_DefTask_NL_101:
    name: '{{Photo}}'
    id: 'fbdc2140-ec6c-400e-a164-c6fb91e8685f'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_8']
  nl_DefTask_NL_102:
    name: '{{Free_text}}'
    id: '58d9b007-526d-4891-97c3-a4d169230fe8'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_16']
  nl_DefTask_NL_103:
    name: '{{Photo}}'
    id: 'd6f877a5-d190-4970-9f79-2805feef37c6'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_9']
  nl_DefTask_NL_104:
    name: '{{Free_text}}'
    id: '12f88d87-3517-4c18-8a66-d66de83df6da'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_17']
  nl_DefTask_NL_105:
    name: '{{Photo}}'
    id: '5c283aea-807d-42cc-9bae-e1f6680501af'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_10']
  nl_DefTask_NL_106:
    name: '{{Free_text}}'
    id: 'b7b68df2-1b75-40ac-b446-901e750cd5d2'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_18']
  nl_DefTask_NL_107:
    name: '{{Photo}}'
    id: '3468d876-1777-4928-b151-4c096341dc31'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_11']
  nl_DefTask_NL_108:
    name: '{{Free_text}}'
    id: '07d2a8ea-859e-4f0e-a73d-7439cbf387da'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_19']
  nl_DefTask_NL_109:
    name: '{{Photo}}'
    id: '85431de8-328b-4b71-ab79-fbd2a7044efa'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_12']
  nl_DefTask_NL_110:
    name: '{{Free_text}}'
    id: 'c21603bd-8c7e-4a54-a226-54e8dba1eec7'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_20']
  nl_DefTask_NL_111:
    name: '{{Photo}}'
    id: '3641460c-6519-4635-8c3f-e838d5d2964c'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_13']
  nl_DefTask_NL_112:
    name: '{{Free_text}}'
    id: '6188d135-bc5d-4abe-9dee-d0152d86a2fb'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_21']
  nl_DefTask_NL_113:
    name: '{{Photo}}'
    id: '504929cd-dace-4631-9923-f8e310413f9c'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_14']
  nl_DefTask_NL_114:
    name: '{{Free_text}}'
    id: '8d227a5e-11de-41d8-a6f1-dac604567358'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_22']
  nl_DefTask_NL_115:
    name: '{{Photo}}'
    id: '70557f4b-e053-47a6-a83f-101c2cfa343c'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_15']
  nl_DefTask_NL_116:
    name: '{{Free_text}}'
    id: '09109f3a-c886-4b97-b432-b700fe645935'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_23']
  nl_DefTask_NL_117:
    name: '{{Photo}}'
    id: '153acda2-265c-4efe-93c8-b015c6c2765f'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_16']
  nl_DefTask_NL_118:
    name: '{{Free_text}}'
    id: '6cf5a59f-8efc-41e4-af01-20e721688346'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_24']
  nl_DefTask_NL_119:
    name: '{{Photo}}'
    id: '5ffd3e7f-ac31-4f59-b833-c1cd9ae3d219'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_17']
  nl_DefTask_NL_120:
    name: '{{Free_text}}'
    id: 'a1a7043d-377e-4ee7-9e8e-86e8892a0a0f'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_25']
  nl_DefTask_NL_121:
    name: '{{Photo}}'
    id: '6cb83858-0cd6-42d1-ab66-d16a1fedaedf'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_18']
  nl_DefTask_NL_122:
    name: '{{Free_text}}'
    id: 'ad00432f-f7f9-4f60-89a1-0f6450d6455c'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_26']
  nl_DefTask_NL_123:
    name: '{{Photo}}'
    id: '46d6b18e-1c92-400b-af09-f5b5b1a718bf'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_19']
  nl_DefTask_NL_124:
    name: '{{Free_text}}'
    id: '6b57dd25-275e-46f7-bd84-e4961adbea5f'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_27']
  nl_DefTask_NL_125:
    name: '{{Photo}}'
    id: 'f5898ae8-739c-4a6b-85f5-181856048090'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_20']
  nl_DefTask_NL_126:
    name: '{{Free_text}}'
    id: '892b5432-661c-4faa-89fb-560be5a73daf'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_28']
  nl_DefTask_NL_127:
    name: '{{Photo}}'
    id: 'a7bf28fe-b12a-4b87-8029-14605ecee1d6'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_21']
  nl_DefTask_NL_128:
    name: '{{Free_text}}'
    id: '2b5f86b9-91ee-4391-8171-1a4890844579'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_29']
  nl_DefTask_NL_129:
    name: '{{Final_mileage}}'
    id: 'a2f82a93-4872-4fc5-9e3e-b9bfe71512f0'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_1']
  nl_DefTask_NL_130:
    name: '{{Final_mileage}}'
    id: 'f37514b1-d4f5-4f11-b572-8f1aab479aeb'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_2']
  nl_DefTask_NL_131:
    name: '{{Final_mileage}}'
    id: 'e7904341-2c9c-4a39-9246-79e32fe3b341'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_3']
  nl_DefTask_NL_132:
    name: '{{Final_mileage}}'
    id: '3c87d196-2529-4a73-8f33-aff667f8583e'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_4']
  nl_DefTask_NL_133:
    name: '{{Initial_mileage}}'
    id: '32603378-d6d0-4e15-ae49-133fa637a883'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_1']
  nl_DefTask_NL_134:
    name: '{{Final_mileage}}'
    id: '2db86740-85d1-4f60-831b-745f8ee309fe'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_5']
  nl_DefTask_NL_136:
    name: '{{Photo}}'
    id: '201f49c0-f2b6-4cdb-ae22-a82df54bef6c'
    type: 'additional_service_photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_22']
  nl_DefTask_NL_137:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '39d1fef0-a7ac-4384-8217-91d3ea53c33f'
    type: 'additional_service_signature'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_3_5']
  nl_DefTask_NL_138:
    name: '{{Photo}}'
    id: '221cccc8-ae86-4105-bd66-1a591fba01bb'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_23']
  nl_DefTask_NL_139:
    name: '{{Photo}}'
    id: 'fea9f82c-2c32-4a6b-81c7-467835f5109b'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_24']
  nl_DefTask_NL_140:
    name: '{{Free_text}}'
    id: 'ccfed848-12d1-4885-a1b4-168efde87af6'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_30']
  nl_DefTask_NL_141:
    name: '{{Photo}}'
    id: 'e455f909-7c48-42ef-9da4-2e30e728416e'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_25']
  nl_DefTask_NL_142:
    name: '{{Photo}}'
    id: 'a8e478f1-326e-43f4-b405-badeeffed966'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_26']
  nl_DefTask_NL_143:
    name: '{{Free_text}}'
    id: '4e563b9e-85db-49d3-80ed-5cebcba05bec'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_31']
  nl_DefTask_NL_144:
    name: '{{Photo}}'
    id: '75732a70-fedc-4c6f-907b-c2c6f6019d2c'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_27']
  nl_DefTask_NL_145:
    name: '{{Free_text}}'
    id: '185688c1-17fb-4f3c-b41b-36ddf6c843a2'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_32']
  nl_DefTask_NL_146:
    name: '{{Free_text}}'
    id: '9b1884f0-9b3a-4619-856b-976e7913fafc'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_33']
  nl_DefTask_NL_147:
    name: '{{Photo}}'
    id: '4f0fd67d-de0f-4119-901e-d95f2b63f2d9'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_28']
  nl_DefTask_NL_148:
    name: '{{Free_text}}'
    id: '3bfad9df-4011-404d-91cc-63eda9d437d0'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_34']
  nl_DefTask_NL_149:
    name: '{{Photo}}'
    id: 'a3fe6e00-a960-457c-917f-0a8608508e24'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_29']
  nl_DefTask_NL_150:
    name: '{{Free_text}}'
    id: '2949995f-57b8-4f1c-ac37-eb0e78eb46b4'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_35']
  nl_DefTask_NL_151:
    name: '{{Free_text}}'
    id: '9fda0c82-1f43-470e-916c-6cf56e11a26c'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_36']
  nl_DefTask_NL_152:
    name: '{{Ready}}'
    id: 'a1b7c923-7fce-4836-8ad0-8098b5931d4c'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_2_19']
  nl_DefTask_NL_153:
    name: '{{Photo}}'
    id: 'e3de9ed9-a61f-4b2e-82ee-8d6a95b3ced2'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_30']
  nl_DefTask_NL_154:
    name: '{{Photo}}'
    id: '16abbf3c-fe00-428b-bc74-9904bd0b2cc3'
    type: 'photo'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_9_31']
  nl_DefTask_NL_155:
    name: '{{Free_text}}'
    id: '7b2bcfdc-12dc-468c-a52a-52c83eaa87c7'
    type: 'text'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_37']
  nl_DefTask_NL_156:
    name: '{{Extra_Waste}}'
    id: '90164a95-58e7-4d16-877f-85ac4d1a1daa'
    type: 'extra_waste'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_20_1']
  nl_DefTask_NL_157:
    name: '{{Arrival}}'
    id: '9d565f30-4850-5b9c-8a3a-ffe08bf31fad'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_8']
  nl_DefTask_NL_158:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'e983d1f3-d6c4-512e-8432-50d23e9b498d'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_6', '@nl_DefElem_NL_6_6', '@nl_DefElem_NL_14_3']
  nl_DefTask_NL_159:
    name: '{{Ready}}'
    id: '212049c0-03c7-5cdb-bf32-a82df54bef6c'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_20']
  nl_DefTask_NL_160:
    name: '{{Waitingtime_in_minutes}}'
    id: 'ee7100d6-f3da-4415-85bf-486dc141ae5f'
    type: 'waitingtime'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_17_2']
  nl_DefTask_NL_161:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '6da8425e-848c-4f4d-a9a0-1e81782a5096'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_1']
  nl_DefTask_NL_162:
    name: '{{Weighing_data_remark}}'
    id: '0b340435-c152-4177-93a3-22e5d3a48f2c'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_38']
  nl_DefTask_NL_163:
    name: '{{Arrival}}'
    id: 'e6f8407f-b360-42c3-91b0-54b9624973ef'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_9']
  nl_DefTask_NL_164:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '804322df-4459-44ef-9ab9-27b39fb3d195'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_7', '@nl_DefElem_NL_6_7', '@nl_DefElem_NL_14_4']
  nl_DefTask_NL_165:
    name: '{{Weighing_data_remark}}'
    id: '56bf4eed-bedf-452d-b702-cf70701d3c9a'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_39']
  nl_DefTask_NL_166:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '5a0bc845-3c1f-481e-92a7-6c7b8a35d6a1'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_2']
  nl_DefTask_NL_167:
    name: '{{Ready}}'
    id: '32accfee-e52f-441d-a6a1-58983f80cb54'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_21']
  nl_DefTask_NL_168:
    name: '{{Arrival}}'
    id: 'ff66135f-ff8c-4547-b825-39d68a8af3ec'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_10']
  nl_DefTask_NL_169:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'a20fba02-e463-4e5c-bfc6-c46b3e5ae979'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_8', '@nl_DefElem_NL_6_8', '@nl_DefElem_NL_14_5']
  nl_DefTask_NL_170:
    name: '{{Weighing_data_remark}}'
    id: '52b5f6e0-9f33-4159-97a6-595ca6926a0d'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_40']
  nl_DefTask_NL_171:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: 'edc64e1d-d270-4d05-aa45-9788a3b2802b'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_3']
  nl_DefTask_NL_172:
    name: '{{Ready}}'
    id: '46c1d6ac-216c-4c42-b9ac-5e1e3dfafe46'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_22']
  nl_DefTask_NL_173:
    name: '{{Arrival}}'
    id: '34c85c07-48a2-43f4-a6d7-f290ff1b8a71'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_11']
  nl_DefTask_NL_174:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'aeeb067e-5d0b-451c-a496-6d1e6e61c9c0'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_9', '@nl_DefElem_NL_6_9', '@nl_DefElem_NL_14_6']
  nl_DefTask_NL_175:
    name: '{{Weighing_data_remark}}'
    id: '9a1e83f5-03b6-4a44-8595-05ad41eda00f'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_41']
  nl_DefTask_NL_176:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '4f695838-8479-46c4-8550-103d26f435cf'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_4']
  nl_DefTask_NL_177:
    name: '{{Ready}}'
    id: 'da734206-fc31-4c79-ae99-a69b37e46f15'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_23']
  nl_DefTask_NL_178:
    name: '{{Arrival}}'
    id: '6091ec5c-f66d-4f03-be18-53cb1d92109d'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_12']
  nl_DefTask_NL_179:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '607c3e8e-9414-4b18-90da-91fe97326de6'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_10', '@nl_DefElem_NL_6_10', '@nl_DefElem_NL_14_7']
  nl_DefTask_NL_180:
    name: '{{Weighing_data_remark}}'
    id: 'aa3e1802-acd4-4c06-92ad-d68c33b5a989'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_42']
  nl_DefTask_NL_181:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '960f99ce-8aae-489f-9626-09cb9d32409e'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_5']
  nl_DefTask_NL_182:
    name: '{{Ready}}'
    id: 'dbe1229a-bc02-4d3d-abf2-10174ddafa8e'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_24']
  nl_DefTask_NL_183:
    name: '{{Arrival}}'
    id: 'bbe72068-cfd6-418d-a639-46ddad328890'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_13']
  nl_DefTask_NL_184:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'e60832ef-9619-4c23-a441-d07892fa72cc'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_11', '@nl_DefElem_NL_6_11', '@nl_DefElem_NL_14_8']
  nl_DefTask_NL_185:
    name: '{{Weighing_data_remark}}'
    id: '65a0f98a-9026-4d5e-ac83-6e170a13d729'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_43']
  nl_DefTask_NL_186:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: '09f02f6b-c921-4038-ab91-f7df7b67e14e'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_6']
  nl_DefTask_NL_187:
    name: '{{Ready}}'
    id: 'ebc26002-7e49-487a-ae48-1393688d4ab8'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_25']
  nl_DefTask_NL_188:
    name: '{{Arrival}}'
    id: 'f4bd75f3-7ce0-4b1c-bd03-1a5c8cf3df37'
    type: 'arrival'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_1_14']
  nl_DefTask_NL_189:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '2328bdec-44eb-478c-8ca1-0c50f98b969d'
    type: 'weighingnote'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_5_12', '@nl_DefElem_NL_6_12', '@nl_DefElem_NL_14_9']
  nl_DefTask_NL_190:
    name: '{{Weighing_data_remark}}'
    id: '4688da5b-0a28-45fd-82b6-bd190c8a073b'
    type: 'weighingnote_remark'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_15_44']
  nl_DefTask_NL_191:
    name: '{{waste_picker}}'
    activatedBySapData: true
    id: 'ae53c497-0910-4177-834f-720147d0154e'
    type: 'wastePicker'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_21_7']
  nl_DefTask_NL_192:
    name: '{{Ready}}'
    id: '709015be-20ee-45d6-ae53-1d17993ffe10'
    type: 'departure'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_2_26']
  nl_DefTask_NL_193:
    name: '{{Initial_mileage}}'
    id: '7bfb68a7-ea36-4294-a786-596638e16498'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_2']
  nl_DefTask_NL_194:
    name: '{{Final_mileage}}'
    id: 'cdccaadb-d344-4864-a629-83eeb15878de'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_6']
  nl_DefTask_NL_195:
    name: '{{Initial_mileage}}'
    id: 'fe91e458-d23b-49de-8027-4722095cc93e'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_3']
  nl_DefTask_NL_196:
    name: '{{Final_mileage}}'
    id: 'c7ca34c5-a0e6-49d5-850c-c8e970df5f57'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_7']
  nl_DefTask_NL_197:
    name: '{{Initial_mileage}}'
    id: '985e33e0-ba39-4a11-a49f-21d090f54c32'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_4']
  nl_DefTask_NL_198:
    name: '{{Final_mileage}}'
    id: '14972d85-8a7e-4d96-af02-c427f5fedc03'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_8']
  nl_DefTask_NL_199:
    name: '{{Initial_mileage}}'
    id: 'cd437dee-3160-490e-9a73-c0543fe47064'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_5']
  nl_DefTask_NL_200:
    name: '{{Final_mileage}}'
    id: '4b1c5267-95b8-496b-a65a-ec66408db6e9'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_9']
  nl_DefTask_NL_201:
    name: '{{Initial_mileage}}'
    id: 'a5de3cdb-bf8b-440d-8895-27cecdc8d92c'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_6']
  nl_DefTask_NL_202:
    name: '{{Final_mileage}}'
    id: '2ca3aee9-51fb-47e1-9285-8cc3bb75659c'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_10']
  nl_DefTask_NL_203:
    name: '{{Initial_mileage}}'
    id: '843abf05-b0cd-4d38-bccd-456f0ba396b8'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_7']
  nl_DefTask_NL_204:
    name: '{{Final_mileage}}'
    id: 'eab76699-15dd-4cf8-8e20-af99e6376729'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_11']
  nl_DefTask_NL_205:
    name: '{{Initial_mileage}}'
    id: '561ea1c7-6e19-49d3-a744-e0dad0305f7d'
    type: 'mileage_start'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_10_8']
  nl_DefTask_NL_206:
    name: '{{Final_mileage}}'
    id: 'df652aaf-74db-4b9a-9ad1-531548fcbc41'
    type: 'mileage_end'
    tenant: '<getNetherlandsTenant()>'
    elementItems: ['@nl_DefElem_NL_11_12']
  nl_DefTask_NL_207:
    name: '{{Departure_check}}'
    id: 'a0e5e5ba-ba86-4bd2-a773-d21b0199bceb'
    type: 'departure_check'
    tenant: '<getNetherlandsTenant()>'
    sapAction: true
    elementItems: ['@nl_DefElem_NL_19_2']
