App\Domain\Entity\DefaultTask:
  lu_DefTask_LU_1:
    name: '{{Approach_selection}}'
    id: 'ab6cc1fa-c9fc-42b4-9d49-11075a1081bc'
    type: 'start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_1_1']
  lu_DefTask_LU_2:
    name: '{{Arrival}}'
    id: '499b10df-ef36-4ac2-b5c2-f5603686c308'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_1']
  lu_DefTask_LU_113:
    name: '{{Please_enter_container_number_load}}'
    id: '22d6b9ce-b562-46de-a9cb-12e337e07f30'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_6']
  lu_DefTask_LU_3:
    name: '{{Ready}}'
    id: '14f04d92-f041-4674-9718-68b387841337'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_1']
  lu_DefTask_LU_114:
    name: '{{Arrival}}'
    id: '1e53070e-2e9a-46bb-8878-3d71f8412e54'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_13']
  lu_DefTask_LU_115:
    name: '{{Please_enter_container_number_unload}}'
    id: 'fd1715b8-2ea2-4c13-b5d5-c93b1875432c'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_7']
  lu_DefTask_LU_116:
    name: '{{Please_enter_container_number_load}}'
    id: '44fed371-cd26-47a0-804b-de0aa7e2bbf6'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_8']
  lu_DefTask_LU_117:
    name: '{{Ready}}'
    id: 'f9ad0d22-7cef-4750-9f27-2b82cb1cff3e'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_24']
  lu_DefTask_LU_4:
    name: '{{Arrival}}'
    id: '63f242e5-599b-4676-8a30-1af81da0b207'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_2']
  lu_DefTask_LU_5:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '92ee1231-133d-4c93-b499-1b0ffd7456d4'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_1', '@lu_DefElem_LU_43_1', '@lu_DefElem_LU_44_1', '@lu_DefElem_LU_45_1']
  lu_DefTask_LU_6:
    name: '{{Please_enter_container_number_unload}}'
    id: '39ab79c9-e1dd-438c-ad01-f810d5ec3255'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_1']
  lu_DefTask_LU_8:
    name: '{{Ready}}'
    id: 'bc688576-5148-40c7-8d90-37528cbe8020'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_2']
  lu_DefTask_LU_141:
    name: '{{Approach_selection}}'
    id: '6ecb2d2a-69fa-41d9-b70d-73c236911907'
    type: 'start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_33_1']
  lu_DefTask_LU_9:
    name: '{{Arrival}}'
    id: '98672240-1b5e-4cf1-a354-bb2f9a11fc06'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_3']
  lu_DefTask_LU_10:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '1091a33d-f44e-4783-a329-41e9d0de49f9'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_2', '@lu_DefElem_LU_43_2', '@lu_DefElem_LU_44_2', '@lu_DefElem_LU_45_2']
  lu_DefTask_LU_149:
    name: '{{Photo}}'
    id: 'd48db59c-596e-4d2c-bdfe-408c80d8670a'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_10']
  lu_DefTask_LU_150:
    name: '{{Please_enter_container_number_load}}'
    id: '51704007-98de-4d79-89a5-220f39cdd502'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_16']
  lu_DefTask_LU_12:
    name: '{{Ready}}'
    id: 'd8705cb4-0d24-4538-903e-37cebaf42c0d'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_36_1']
  lu_DefTask_LU_142:
    name: '{{Emptying_possible}}'
    id: 'eee8d9e8-f8af-4c91-b7de-eb21d90a2a53'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_37_1']
  lu_DefTask_LU_13:
    name: '{{Arrival}}'
    id: '79812606-7426-45fc-bb7b-fa09f78eaae0'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_4']
  lu_DefTask_LU_14:
    name: '{{Weighing_data}}'
    id: 'a1d99437-bdb6-4ffd-aa80-04da373b0388'
    type: 'weighingnote'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_7_1', '@lu_DefElem_LU_8_1', '@lu_DefElem_LU_9_1', '@lu_DefElem_LU_10_1', '@lu_DefElem_LU_17_1']
  lu_DefTask_LU_15:
    name: '{{Ready}}'
    id: '968e9254-f471-454e-b709-fce48e4bfe44'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_3']
  lu_DefTask_LU_16:
    name: '{{Arrival}}'
    id: 'fc98c67e-cc6c-42ed-aa90-1ac0e49728c1'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_5']
  lu_DefTask_LU_17:
    name: '{{Please_enter_container_number_unload}}'
    id: '7ef89a90-82e6-4fe0-b864-d63214bfa241'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_2']
  lu_DefTask_LU_18:
    name: '{{Ready}}'
    id: '363472af-6f69-49cc-9296-49732594e140'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_4']
  lu_DefTask_LU_143:
    name: '{{Arrival}}'
    id: '46288c08-d7a5-44de-a916-037f2eda420a'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_14']
  lu_DefTask_LU_144:
    name: '{{Photo}}'
    id: 'b72fcb6c-cf6c-4511-aca3-d80354aab802'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_5']
  lu_DefTask_LU_145:
    name: '{{Please_enter_container_number_unload}}'
    id: 'bbf7627b-cb89-4974-aac4-62a430bab159'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_9']
  lu_DefTask_LU_146:
    name: '{{Ready}}'
    id: '75474b6f-a0ec-4b5c-be50-1f3e7199d5a6'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_25']
  lu_DefTask_LU_118:
    name: '{{Approach_selection}}'
    id: 'aa5fe090-c717-44fb-8bd6-47a1f50d2bfc'
    type: 'start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_34_1']
  lu_DefTask_LU_119:
    name: '{{Arrival}}'
    id: '7e9d5de5-126d-4ea7-ae11-aefdf986d017'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_15']
  lu_DefTask_LU_120:
    name: '{{Please_enter_container_number_unload}}'
    id: 'd545f941-28bd-4629-a2c1-3a145ebc3b81'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_10']
  lu_DefTask_LU_121:
    name: '{{Ready}}'
    id: '2ec41878-4f7f-4276-bc0a-fecacd407984'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_26']
  lu_DefTask_LU_19:
    name: '{{Arrival}}'
    id: 'dc8f1a5e-4d38-44c9-886d-fff6834da53e'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_6']
  lu_DefTask_LU_20:
    name: '{{Ready}}'
    id: '6bff48ad-f6c4-4feb-b365-09c39c20d051'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_5']
  lu_DefTask_LU_122:
    name: '{{Emptying_possible}}'
    id: 'e2b7545a-8475-4b8e-a93a-5d9757285a1f'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_39_1']
  lu_DefTask_LU_21:
    name: '{{Arrival}}'
    id: 'e7b4a7a4-ee1e-44f1-b993-dffbd9eef292'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_7']
  lu_DefTask_LU_22:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    repeatable: true
    id: '36cedc47-1dae-483d-9c8a-63f25d7feda1'
    type: 'weighingnote'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_7_2', '@lu_DefElem_LU_8_2', '@lu_DefElem_LU_9_2', '@lu_DefElem_LU_10_2', '@lu_DefElem_LU_17_2']
  lu_DefTask_LU_23:
    name: '{{Ready}}'
    id: '42815a92-18a5-418c-bdc3-6c921bd43723'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_6']
  lu_DefTask_LU_24:
    name: '{{Arrival}}'
    id: '5cfec86b-3048-490b-b616-3d4221f9f03f'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_8']
  lu_DefTask_LU_28:
    name: '{{Ready}}'
    id: '46bdbb90-ec48-4f73-9946-21569785945e'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_7']
  lu_DefTask_LU_123:
    name: '{{Arrival}}'
    id: 'd34fc80e-9116-4fba-bf7f-40ddfb0fde99'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_16']
  lu_DefTask_LU_124:
    name: '{{Please_enter_container_number_load}}'
    id: '98e8f802-27e7-4192-ad94-967e4a0a3003'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_11']
  lu_DefTask_LU_125:
    name: '{{Ready}}'
    id: 'abeb0802-b344-4e86-b64f-407c1d8b4931'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_27']
  lu_DefTask_LU_126:
    name: '{{Arrival}}'
    id: '80183709-bc86-4fdf-92f7-936d8912d12f'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_17']
  lu_DefTask_LU_127:
    name: '{{Please_enter_container_number_load}}'
    id: 'd8fec3a3-b5de-4e9d-9260-33abd5c4e52b'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_12']
  lu_DefTask_LU_128:
    name: '{{Please_enter_container_number_unload}}'
    id: 'cb9ceecf-6e4a-41b7-9858-5ac8f31ddf49'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_13']
  lu_DefTask_LU_129:
    name: '{{Ready}}'
    id: 'e0b44b9e-9622-40e5-8f6f-df2d48224bdd'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_28']
  lu_DefTask_LU_135:
    name: '{{Arrival}}'
    id: '92cf86f8-ff43-4221-b7c4-e807613efb45'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_18']
  lu_DefTask_LU_136:
    name: '{{Ready}}'
    id: '834d051f-9b6e-4162-b4d5-2b7261d23145'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_29']
  lu_DefTask_LU_26:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '980cd074-98d3-42d9-8705-d35a884f918b'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_3', '@lu_DefElem_LU_43_3', '@lu_DefElem_LU_44_3', '@lu_DefElem_LU_45_3']
  lu_DefTask_LU_27:
    name: '{{Please_enter_container_number_unload}}'
    id: '3a3ddd27-fc12-4b61-8a1e-44049f6201db'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_3']
  lu_DefTask_LU_130:
    name: '{{Please_enter_container_number_load}}'
    id: 'a4ba8bdb-16ba-432a-b1a9-2398b7097d2e'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_14']
  lu_DefTask_LU_239:
    name: '{{Photo}}'
    id: '373e9650-e4a4-4e98-8827-40d91b0c0a7d'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_37']
  lu_DefTask_LU_131:
    name: '{{Photo}}'
    id: '0552a3d6-f25f-4268-9ed3-9bda9a27a657'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_6']
  lu_DefTask_LU_133:
    name: '{{Arrival}}'
    id: '499e7e44-2ffc-4c37-a57c-495b8e82dcd1'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_19']
  lu_DefTask_LU_132:
    name: '{{Please_enter_container_number_unload}}'
    id: '08087b25-26a3-4caf-a38b-a26b1e504ac0'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_15']
  lu_DefTask_LU_134:
    name: '{{Ready}}'
    id: '2c9a19d4-3e0c-4aca-b419-05f95178d4d3'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_30']
  lu_DefTask_LU_137:
    name: '{{Emptying_possible}}'
    id: 'a2191654-ea29-4dd6-90f7-f6f35b1664af'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_38_1']
  lu_DefTask_LU_138:
    name: '{{Arrival}}'
    id: '7a60fb1f-47a2-4a8b-9a24-4dd617606f61'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_20']
  lu_DefTask_LU_139:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    repeatable: true
    id: '04857636-49ed-449c-8435-5fbb33c7d421'
    type: 'weighingnote'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_7_3', '@lu_DefElem_LU_8_3', '@lu_DefElem_LU_9_3', '@lu_DefElem_LU_10_3', '@lu_DefElem_LU_17_7']
  lu_DefTask_LU_140:
    name: '{{Ready}}'
    id: '97bf209b-1a02-4c48-8ed7-487042bfed3c'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_31']
  lu_DefTask_LU_44:
    name: '{{Approach_selection}}'
    id: 'f4ac6da3-5ae2-41f4-a34d-6ae1711f92a7'
    type: 'start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_13_1']
  lu_DefTask_LU_45:
    name: '{{Arrival}}'
    id: 'c7e25319-2601-4f9d-9789-9eb27ff0688c'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_9']
  lu_DefTask_LU_46:
    name: '{{Ready}}'
    id: 'f720979e-9dda-408e-b1ad-5981541a5f79'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_8']
  lu_DefTask_LU_52:
    name: '{{Arrival}}'
    id: '91664f07-9a08-456f-aeec-73f780e44dd7'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_10']
  lu_DefTask_LU_53:
    name: '{{Please_enter_container_number_unload}}'
    id: '062bafac-2ba4-4074-a9a4-001d5b9aac6d'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_4']
  lu_DefTask_LU_54:
    name: '{{Ready}}'
    id: '63134845-bd31-4330-b686-e824853c54ba'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_9']
  lu_DefTask_LU_60:
    name: '{{Arrival}}'
    id: 'c49f18a7-4112-4f9d-b1fe-88b34c317322'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_11']
  lu_DefTask_LU_61:
    name: '{{Ready}}'
    id: '8fd403ba-800f-46a2-9f79-7d77c2f88256'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_10']
  lu_DefTask_LU_63:
    name: '{{Please_enter_container_number_unload}}'
    id: '3e72aba7-a9ae-410c-9593-c427910e636e'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_5']
  lu_DefTask_LU_147:
    name: '{{Photo}}'
    id: '754d0c70-fc12-4af5-a0fa-4904ff0184f0'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_8']
  lu_DefTask_LU_148:
    name: '{{Photo}}'
    id: 'e6fb5e11-4f78-4bb1-ac8c-68dabce1cf9e'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_9']
  lu_DefTask_LU_82:
    name: '{{Photo}}'
    id: 'fa49180a-8090-4af8-ad92-53420033a7b3'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_3']
  lu_DefTask_LU_83:
    name: '{{Free_text}}'
    id: '085a2bd5-feb8-4ac2-bece-2887981cd348'
    type: 'text'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_14_1']
  lu_DefTask_LU_84:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '6b2a0cc4-9278-4040-9c38-b7723d2cf9cc'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_4', '@lu_DefElem_LU_43_4', '@lu_DefElem_LU_44_4', '@lu_DefElem_LU_45_4']
  lu_DefTask_LU_85:
    name: '{{Ready}}'
    id: 'f252c69d-7d5e-418d-823f-7ccd52b97310'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_11']
  lu_DefTask_LU_86:
    name: '{{Ready}}'
    id: 'ebba1f9e-53c2-4bca-a62b-059fd3ce635e'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_12']
  lu_DefTask_LU_87:
    name: '{{Current_mileage}}'
    id: '4d35ddb1-fe21-4b0b-aa6b-4f02e26643a8'
    type: 'mileage_actual'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_31_1']
  lu_DefTask_LU_88:
    name: '{{Ready}}'
    id: '33e1b4f9-8732-4474-bcbb-e8dc4adc3338'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_13']
  lu_DefTask_LU_89:
    name: '{{Ready}}'
    id: '1db93ad8-2f37-4140-87ac-3c4a30824d92'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_14']
  lu_DefTask_LU_90:
    name: '{{Diesel}}'
    id: '138fb5f5-6581-4700-a0da-6d6b47137b85'
    type: 'diesel'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_15_1']
  lu_DefTask_LU_91:
    name: '{{AdBlue}}'
    id: 'fcb002bd-d9db-44ba-9e2b-2b90f7e7f2ed'
    type: 'adblue'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_16_1']
  lu_DefTask_LU_92:
    name: '{{Current_mileage}}'
    id: 'dcafbc2b-eb17-4d6a-b9ed-e3c4f05170f0'
    type: 'mileage_actual'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_31_2']
  lu_DefTask_LU_93:
    name: '{{Ready}}'
    id: '7933a0b9-926c-43c8-96d8-1829b8fcf27d'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_15']
  lu_DefTask_LU_94:
    name: '{{Ready}}'
    id: '8f3bd1a4-c9e2-483c-890d-2b4ba4d05f85'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_16']
  lu_DefTask_LU_95:
    name: '{{Ready}}'
    id: '13095cff-794a-4183-ba16-be6a11ccd418'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_17']
  lu_DefTask_LU_96:
    name: '{{Signature}}'
    id: 'a48f7fe7-8f14-4973-a50e-ed54799231b6'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_4']
  lu_DefTask_LU_97:
    name: '{{Free_text}}'
    id: '7503faa6-5768-41b5-9b83-bf747a2d48da'
    type: 'text'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_14_2']
  lu_DefTask_LU_98:
    name: '{{Ready}}'
    id: 'e213c018-3462-40d8-a928-c0c35e7cf33e'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_18']
  lu_DefTask_LU_99:
    name: '{{Ready}}'
    id: '52d21cae-8d10-4115-aa93-0343a9d03de3'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_19']
  lu_DefTask_LU_100:
    name: '{{Ready}}'
    id: 'ba3be9e4-d7c0-41f9-b9e9-e67caef0af17'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_20']
  lu_DefTask_LU_101:
    name: '{{Ready}}'
    id: 'c58e5e4e-3bcb-44e7-ad34-6bd887058610'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_21']
  lu_DefTask_LU_102:
    name: '{{Ready}}'
    id: 'd68cf718-80a4-4c47-a1ff-23554b441e4c'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_22']
  lu_DefTask_LU_103:
    name: '{{Arrival}}'
    id: '061abf63-9992-4f1f-8b5f-0bc0529aab35'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_12']
  lu_DefTask_LU_104:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '1aed01b5-9904-4965-bb0a-c50d2ba2c18c'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_5', '@lu_DefElem_LU_43_5', '@lu_DefElem_LU_44_5']
  lu_DefTask_LU_105:
    name: '{{Ready}}'
    id: 'ea53591f-29e3-4429-80a6-d6a6ee843301'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_23']
  lu_DefTask_LU_106:
    name: '{{Initial_mileage}}'
    id: '1303d4a6-3b6a-420b-a7f6-f7a1c4a5c65d'
    type: 'mileage_start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_18_1']
  lu_DefTask_LU_107:
    name: '{{Final_mileage}}'
    id: '1f20017a-4e80-467b-acd5-dfc3712a1ebb'
    type: 'mileage_end'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_19_1']
  lu_DefTask_LU_108:
    name: '{{Departure_check}}'
    id: '94ff61eb-e331-42a0-82f7-be57eaf1eeee'
    type: 'departure_check'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_20_1']
  lu_DefTask_LU_109:
    name: '{{Additional_service}}'
    id: '7eade917-482e-433a-a2b5-ed322e40f44f'
    type: 'additional_service'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_25_1', '@lu_DefElem_LU_26_1', '@lu_DefElem_LU_27_1']
  lu_DefTask_LU_110:
    name: '{{Approach_selection}}'
    id: '754b2b11-7b9b-4b9c-bde6-32343c48ded3'
    type: 'disposalSite_choice'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_35_1']
  lu_DefTask_LU_112:
    name: '{{Free_text}}'
    id: 'b0392293-2b06-43b8-9c40-b0b06ca2a730'
    type: 'text'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_14_3']
  lu_DefTask_LU_151:
    name: '{{Photo}}'
    id: '3199c4a9-1189-4cc2-9b49-0c7001329423'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_11']
  lu_DefTask_LU_152:
    name: 'ATTENTE PAS VIDANGE'
    activatedBySapData: true
    id: '92169186-2d0e-4c0a-b360-bbc84047245b'
    type: 'ATTENTE PAS VIDANGE'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_40_1']
  lu_DefTask_LU_153:
    name: "TEMPS D'ATTENTE"
    activatedBySapData: true
    id: '30d2d060-9c37-456b-954c-c7f67cc4b480'
    type: "TEMPS D'ATTENTE"
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_41_1']
  lu_DefTask_LU_154:
    name: 'ATTENTE PAS VIDANGE'
    activatedBySapData: true
    id: '8ca0da40-62f9-4a93-a264-d766d4b3567c'
    type: 'ATTENTE PAS VIDANGE'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_40_2']
  lu_DefTask_LU_155:
    name: "TEMPS D'ATTENTE"
    activatedBySapData: true
    id: '9a7c90c9-2435-47b3-9d24-49bd805cfb98'
    type: "TEMPS D'ATTENTE"
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_41_2']
  lu_DefTask_LU_156:
    name: "TEMPS D'ATTENTE"
    activatedBySapData: true
    id: 'c6f0c2bf-cb8d-41b5-82d0-47c0eba1d42a'
    type: "TEMPS D'ATTENTE"
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_41_3']
  lu_DefTask_LU_157:
    name: 'TEMPS DE CHARGEMENT'
    activatedBySapData: true
    id: '56342f01-8702-446d-9585-20283a027c71'
    type: 'TEMPS DE CHARGEMENT'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_42_1']
  lu_DefTask_LU_158:
    name: '{{Photo}}'
    id: '319ee575-baaf-4a6e-90d4-393f43e129a3'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_12']
  lu_DefTask_LU_159:
    name: '{{Photo}}'
    id: '6b2bbf7a-a565-45ae-97ba-e24ecd9983e7'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_13']
  lu_DefTask_LU_160:
    name: '{{Photo}}'
    id: '71b224d6-8500-4bc8-9c57-c0deb332d0cd'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_14']
  lu_DefTask_LU_161:
    name: '{{Photo}}'
    id: 'eb5cd05f-15b8-4ae0-a72c-55c1e7d70ef7'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_15']
  lu_DefTask_LU_162:
    name: '{{Photo}}'
    id: 'e9bce493-c98e-4798-a6cb-ce6f418cc472'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_16']
  lu_DefTask_LU_163:
    name: '{{Photo}}'
    id: '53cae53d-c91f-497e-91fa-37c1d358a16b'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_17']
  lu_DefTask_LU_164:
    name: '{{Photo}}'
    id: 'c8266e7c-9892-48fe-9191-edea9b976fed'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_18']
  lu_DefTask_LU_165:
    name: '{{Photo}}'
    id: 'a0f817c9-360d-4f9f-9417-ab6a1bdf2f0c'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_19']
  lu_DefTask_LU_166:
    name: '{{Photo}}'
    id: '7ef2461b-67da-405d-9c2c-f7645070a780'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_20']
  lu_DefTask_LU_167:
    name: '{{Photo}}'
    id: 'bd8df35c-40d7-4b2c-a5bd-b8d71a219dfa'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_21']
  lu_DefTask_LU_168:
    name: '{{Photo}}'
    id: 'ae02c9f5-f309-4e70-9446-42e6cc430f1f'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_22']
  lu_DefTask_LU_169:
    name: '{{Photo}}'
    id: '00cd3517-e3a1-4843-98eb-b1b3d44377dd'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_23']
  lu_DefTask_LU_170:
    name: '{{Photo}}'
    id: 'e737dab9-73ae-4c7e-a688-0d5069c4b763'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_24']
  lu_DefTask_LU_171:
    name: '{{Photo}}'
    id: 'a052b1ef-d0a5-4cf7-b0a8-3776c8ae5e9e'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_25']
  lu_DefTask_LU_172:
    name: '{{Photo}}'
    id: '2d0382b6-8cfb-4dea-9566-368efc589e7e'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_26']
  lu_DefTask_LU_173:
    name: '{{Photo}}'
    id: '619b12aa-9172-4c25-92f0-799171be0b06'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_27']
  lu_DefTask_LU_174:
    name: '{{Photo}}'
    id: '99bd5f5a-7952-4e6b-a151-823185571f51'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_28']
  lu_DefTask_LU_175:
    name: '{{Photo}}'
    id: 'e7d3b323-5757-45d9-add7-46b7cb1f91cb'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_29']
  lu_DefTask_LU_176:
    name: '{{Photo}}'
    id: '325f499c-6036-4fef-8ee9-86baab8c5c83'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_30']
  lu_DefTask_LU_177:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '660551b6-8b63-41cd-8c23-bc9a6deaed86'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_6', '@lu_DefElem_LU_43_6', '@lu_DefElem_LU_44_6', '@lu_DefElem_LU_45_5']
  lu_DefTask_LU_181:
    name: '{{widget/bottom_sheet/signature/name_title}} Dummy'
    id: '9aac1fbc-56eb-463a-b00d-307f8576fcad'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_7', '@lu_DefElem_LU_43_7', '@lu_DefElem_LU_44_7', '@lu_DefElem_LU_45_6']
  lu_DefTask_LU_182:
    name: '{{widget/bottom_sheet/signature/name_title}} Dummy'
    id: '2eb965b5-4ade-4e68-a70d-003e94cd38fb'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_8', '@lu_DefElem_LU_43_8', '@lu_DefElem_LU_44_8', '@lu_DefElem_LU_45_7']
  lu_DefTask_LU_183:
    name: '{{Approach_selection}}'
    id: '2d6afdd2-1ceb-4aa9-97d0-89f5ef442ba6'
    type: 'start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_46_1']
  lu_DefTask_LU_184:
    name: '{{Arrival}}'
    id: '56c36be8-9971-42e9-ad46-904ddd1de98c'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_21']
  lu_DefTask_LU_185:
    name: '{{Please_enter_container_number_load}}'
    id: 'e1947bb6-481e-4c56-90dc-94c6605b55fb'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_17']
  lu_DefTask_LU_187:
    name: '{{Ready}}'
    id: '729294b0-3dc8-42f8-a5ad-5aed95e44228'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_32']
  lu_DefTask_LU_188:
    name: '{{Arrival}}'
    id: 'f755b91d-feeb-4ae0-934e-055550653b04'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_22']
  lu_DefTask_LU_189:
    name: '{{Please_enter_container_number_unload}}'
    id: 'b32da75c-2d84-4973-b938-e9983c18f409'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_18']
  lu_DefTask_LU_190:
    name: '{{Please_enter_container_number_load}}'
    id: '4f30d4b5-18d9-430d-b984-e4f227b1de7c'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_19']
  lu_DefTask_LU_192:
    name: '{{Ready}}'
    id: '17765d93-5061-445a-9092-c47151225f50'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_33']
  lu_DefTask_LU_193:
    name: '{{Arrival}}'
    id: '12f7e804-70f7-4a35-915b-82d35255192a'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_23']
  lu_DefTask_LU_194:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '1eec87bf-ee31-46f1-b5a3-8885bee342cf'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_9', '@lu_DefElem_LU_43_9', '@lu_DefElem_LU_44_9', '@lu_DefElem_LU_45_8']
  lu_DefTask_LU_195:
    name: '{{Photo}}'
    id: '2c648b86-c438-4634-9ff7-74e38368fb4c'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_31']
  lu_DefTask_LU_196:
    name: 'TEMPS DE CHARGEMENT'
    activatedBySapData: true
    id: '82f770d6-b01d-4c63-99ff-87919abb8ee0'
    type: 'TEMPS DE CHARGEMENT'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_42_2']
  lu_DefTask_LU_197:
    name: '{{Ready}}'
    id: '46f80846-da9d-403d-86ea-5dc6997128ce'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_34']
  lu_DefTask_LU_198:
    name: '{{Arrival}}'
    id: 'fcdda566-560f-4c4f-8eb9-4662591a23c3'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_24']
  lu_DefTask_LU_199:
    name: '{{Emptying_possible}}'
    id: 'f74a9169-6b96-48b5-bc23-2a76ac2426e4'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_47_1']
  lu_DefTask_LU_200:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: '707bed69-8659-4c88-9d13-7608c9cd0802'
    type: 'weighingnote'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_7_4', '@lu_DefElem_LU_8_4', '@lu_DefElem_LU_9_4', '@lu_DefElem_LU_10_4']
  lu_DefTask_LU_201:
    name: '{{Ready}}'
    id: '418b8f12-a592-4b94-819e-de574089d6dd'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_35']
  lu_DefTask_LU_202:
    name: '{{Arrival}}'
    id: '42eaf322-724d-4cd3-b866-4053d0641dff'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_25']
  lu_DefTask_LU_203:
    name: '{{Photo}}'
    id: '7c527e5e-bff1-4614-8348-673855402f22'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_32']
  lu_DefTask_LU_204:
    name: '{{Please_enter_container_number_unload}}'
    id: 'a388403b-a673-4270-a223-ee6eec7acb38'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_28_20']
  lu_DefTask_LU_206:
    name: '{{Ready}}'
    id: 'a4c26810-55a5-48ef-9a6d-199b6ccb2978'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_36']
  lu_DefTask_LU_207:
    name: '{{Approach_selection}}'
    id: '73006ff5-307a-4923-9f85-451138f372e2'
    type: 'start'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_48_1']
  lu_DefTask_LU_208:
    name: '{{Arrival}}'
    id: '4f477dee-e41a-4b98-bd32-4807eadd3a85'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_26']
  lu_DefTask_LU_209:
    name: '{{Please_enter_container_number_load}}'
    repeatable: true
    id: '069dcfd7-30eb-45ef-8d90-cfba401f1a5d'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_55_1']
  lu_DefTask_LU_210:
    name: '{{Ready}}'
    id: 'b1009584-80de-49ca-9005-65fcb3125e58'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_37']
  lu_DefTask_LU_211:
    name: '{{Arrival}}'
    id: '1a7e6c1d-a72e-4cf5-a532-f5e1af80a252'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_27']
  lu_DefTask_LU_212:
    name: '{{Please_enter_toilet_number_unload}}'
    repeatable: true
    id: 'a4047a27-3179-4599-81d0-f7f7f3de465a'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_55_2']
  lu_DefTask_LU_213:
    name: '{{Photo}}'
    id: 'ff20e7a2-576b-4edb-bde4-7e40c95b7d04'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_33']
  lu_DefTask_LU_214:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: 'abd970d2-fdd6-4416-b754-31a8b7ec4496'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_10', '@lu_DefElem_LU_43_10', '@lu_DefElem_LU_44_10', '@lu_DefElem_LU_45_9']
  lu_DefTask_LU_215:
    name: '{{Condition_of_toilet}}'
    id: '24b745af-3f13-4b02-8ada-33cb34baa111'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_49_1']
  lu_DefTask_LU_216:
    name: '{{Ready}}'
    id: '862c4af2-35b0-435e-924e-12f747f3b0dc'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_38']
  lu_DefTask_LU_217:
    name: '{{Arrival}}'
    id: '49cebec8-1c1b-4e32-8306-86856c2a4be7'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_28']
  lu_DefTask_LU_218:
    name: '{{Please_enter_toilet_number_load}}'
    repeatable: true
    id: '7f037fb6-17ee-4dd6-81be-eecb824c96a4'
    type: 'container_number_take'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_55_3']
  lu_DefTask_LU_219:
    name: '{{Photo}}'
    id: '820792bb-c3e9-4ca7-a164-ff5741b07df2'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_34']
  lu_DefTask_LU_220:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: 'fe19aab3-9791-4d09-b6c7-e93629112f95'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_11', '@lu_DefElem_LU_43_11', '@lu_DefElem_LU_44_11', '@lu_DefElem_LU_45_10']
  lu_DefTask_LU_221:
    name: '{{Condition_of_toilet}}'
    id: '71940900-edfe-4921-8c5c-1aa216910da2'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_50_1']
  lu_DefTask_LU_222:
    name: '{{Ready}}'
    id: '5a13dd40-0bce-4bac-a75a-3b6ac02a7e1a'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_39']
  lu_DefTask_LU_223:
    name: '{{Arrival}}'
    id: '8b9af800-7a68-44cd-b83c-2d94da387742'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_29']
  lu_DefTask_LU_224:
    name: '{{Please_enter_toilet_number_unload}}'
    repeatable: true
    id: 'c9e4e42b-b725-4f15-8f00-b9bf7c99e41e'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_55_4']
  lu_DefTask_LU_225:
    name: '{{Ready}}'
    id: '65677986-5493-4b85-b8ba-ca1e7c1029dd'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_40']
  lu_DefTask_LU_226:
    name: '{{Arrival}}'
    id: '1a4d96e4-4b63-49cc-ab12-dbe0ff648c3a'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_30']
  lu_DefTask_LU_227:
    name: '{{Please_enter_toilet_number_empty}}'
    repeatable: true
    id: '1bf0354d-7d06-4517-8827-d413dd7892b4'
    type: 'container_number_empty'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_55_5']
  lu_DefTask_LU_228:
    name: '{{Photo}}'
    id: '7997b0c0-1f0a-4bc9-be80-ca5f6f769865'
    type: 'photo'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_17_35']
  lu_DefTask_LU_229:
    name: '{{widget/bottom_sheet/signature/name_title}}'
    id: '0519ec70-351a-4530-9659-a958115981a7'
    type: 'signature'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_4_12', '@lu_DefElem_LU_43_12', '@lu_DefElem_LU_44_12', '@lu_DefElem_LU_45_11']
  lu_DefTask_LU_230:
    name: '{{Condition_of_toilet}}'
    id: '5d88c508-7f42-46e6-b041-2e33001ba6ac'
    type: 'select'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_52_1']
  lu_DefTask_LU_231:
    name: '{{Ready}}'
    id: 'df146cfb-3ff6-4f6d-af7b-28453e3d8fa5'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_41']
  lu_DefTask_LU_232:
    name: '{{Arrival}}'
    id: '7fef682b-eb93-4377-bb0d-a8451c902d32'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_31']
  lu_DefTask_LU_233:
    name: '{{Weighing_data}}'
    activatedBySapData: true
    id: 'cbef493a-6c03-4e70-b2f1-07bbe5cb0821'
    type: 'weighingnote'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_7_5', '@lu_DefElem_LU_8_5', '@lu_DefElem_LU_9_5', '@lu_DefElem_LU_10_5', '@lu_DefElem_LU_17_36']
  lu_DefTask_LU_234:
    name: '{{Cubicmeter?}}'
    id: '464b4cc8-8e94-4877-9179-5f28bfb12c3b'
    type: 'cubicmeter'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_53_1']
  lu_DefTask_LU_235:
    name: '{{Ready}}'
    id: '3432bf63-a5fa-4e8a-a841-40b9f205b639'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_54_1']
  lu_DefTask_LU_236:
    name: '{{Arrival}}'
    id: '495c0fd3-2b8a-498f-8aef-00d27db9c3dd'
    type: 'arrival'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_2_32']
  lu_DefTask_LU_237:
    name: '{{Please_enter_toilet_number_unload}}'
    repeatable: true
    id: 'eb309408-dda2-4ab0-8bbf-1d002b9b98dc'
    type: 'container_number_place'
    tenant: '<getLuxembourgTenant()>'
    elementItems: ['@lu_DefElem_LU_55_6']
  lu_DefTask_LU_238:
    name: '{{Ready}}'
    id: '953ba33e-c4f4-4c73-829e-d687d0d3f4e1'
    type: 'departure'
    tenant: '<getLuxembourgTenant()>'
    sapAction: true
    elementItems: ['@lu_DefElem_LU_3_42']
