App\Domain\VehicleInspection\Config\VehicleInspectionConfig:
  es_VIR_ES_1:
    __construct: { country: '<(App\Domain\Entity\Enum\Country::ES)>', equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ100)>', automaticCompletionEnabled: false, equipmentComponentGroups: ['@es_VIR_Group_ES_1', '@es_VIR_Group_ES_2', '@es_VIR_Group_ES_3', '@es_VIR_Group_ES_4'] }
    tenant: '<getSpainTenant()>'
  es_VIR_ES_2:
    __construct: { country: '<(App\Domain\Entity\Enum\Country::ES)>', equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ101)>', automaticCompletionEnabled: false, equipmentComponentGroups: ['@es_VIR_Group_ES_5', '@es_VIR_Group_ES_6', '@es_VIR_Group_ES_7', '@es_VIR_Group_ES_8'] }
    tenant: '<getSpainTenant()>'
  es_VIR_ES_3:
    __construct: { country: '<(App\Domain\Entity\Enum\Country::ES)>', equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::EQ102)>', automaticCompletionEnabled: false, equipmentComponentGroups: ['@es_VIR_Group_ES_9', '@es_VIR_Group_ES_10', '@es_VIR_Group_ES_11', '@es_VIR_Group_ES_12'] }
    tenant: '<getSpainTenant()>'
