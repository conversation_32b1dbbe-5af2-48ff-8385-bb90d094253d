App\Domain\Entity\DefaultTermination:
  lu_int_DefTerm_LU_INT_1:
    text: '{{Tour_too_long_no_emptying}}'
    externalId: '215'
    id: 'a639389f-2840-4a94-991f-0e9a9c2c3abe'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_2:
    text: '{{Vehicle_defective}}'
    externalId: '220'
    id: 'd25cfa04-d368-436a-b014-ad2b17465825'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_3:
    text: '{{Driver_sick}}'
    externalId: '225'
    id: 'ded9ae22-ae34-476a-8119-39916c243045'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_4:
    text: '{{Transport_interruption}}'
    externalId: '290'
    id: 'b2675953-d67f-4603-8296-77a11cc37c73'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_5:
    text: '{{Order_returned_to_dispatch}}'
    externalId: '350'
    id: 'b4808360-ca3c-4d88-8f1c-8dbf14cd5ac1'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_6:
    text: '{{Customer_not_on_site}}'
    externalId: '240'
    id: '7fd820f0-262a-4764-9514-5f2f7011bf5e'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_7:
    text: '{{Container_locked}}'
    externalId: '240'
    id: '72eade9e-c869-4523-a08e-81deb1bd8813'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_8:
    text: '{{Container_too_heavy/overfilled}}'
    externalId: '250'
    id: '852f4a7a-a8b9-48b7-bb9a-aab126dba54d'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_9:
    text: '{{Incorrect_filling_-_No_emptying}}'
    externalId: '255'
    id: 'ea5b6ce9-e74c-4535-8df6-0c6695566254'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_10:
    text: '{{Container_defective_-_No_emptying}}'
    externalId: '265'
    id: '93edffad-c78f-4129-9e07-b7bbfa28ce0d'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_11:
    text: '{{Container_fallen_into_vehicle}}'
    externalId: '305'
    id: 'ba6faa54-24ef-4e9f-86a0-b3ad4fb1ac32'
    tenant: '<getGermanyTenant()>'
  lu_int_DefTerm_LU_INT_12:
    text: '{{container_empty}}'
    externalId: '245'
    id: '4e00414c-34b1-4dac-b8f5-40dfea2884bd'
    tenant: '<getGermanyTenant()>'
