App\Domain\Entity\User:
    nl_user_api_1:
        __construct:
            tenant: '<getNetherlandsTenant()>'
            username: nl-user-1
            firstname: NlApi
            lastname: User1
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@nl_staff_user_api_1'
    nl_user_api_2:
        __construct:
            tenant: '<getNetherlandsTenant()>'
            username: nl-user-2
            firstname: NlApi
            lastname: User2
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@nl_staff_user_api_2'
    user_portal_admin_nl:
        __construct:
            tenant: '<getNetherlandsTenant()>'
            username: portal-admin-nl
            firstname: 'portal-admin'
            lastname: 'nl'
        id: 'f71fd7b7-3864-4769-a83c-57587401eeff'
        email: '<EMAIL>'
        country: nl
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::NL)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_COUNTRY_ADMINS", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS", "ROLE_PORTAL_MANAGE_FAQ_ADMINS" ]
    user_portal_nl:
        __construct:
            tenant: '<getNetherlandsTenant()>'
            username: portal-user-nl
            firstname: 'portal-user'
            lastname: 'nl'
        id: 'b90f82eb-16ce-40e8-8b22-8afddd110c4b'
        email: '<EMAIL>'
        country: 'nl'
        branchAccess: [ 'ac3ba014-1c19-453f-aca6-6962f3d18497' ]
        roles: [ "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_VIEW_BRANCH_DATA", "ROLE_PORTAL_ACCESS" ]
