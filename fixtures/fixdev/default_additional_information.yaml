App\Domain\Entity\ValueObject\AdditionalInformation:
    tour_default_additional_information_1:
        __construct:
            sequence: 5
            text: 'default tour additional information 1 - be28ba3b-6311-46d2-a6ba-054911124f43'
            alsoFrontView: false
            icon: 'handyman'
            highlight: false
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'

    order_default_additional_information_1:
        __construct:
            sequence: 10
            text: 'default order additional information 1 - df3ff398-5d81-4e2a-b7bc-3966503cfc1d'
            alsoFrontView: false
            icon: 'handyman'
            highlight: false
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'

    default_interruption_default_additional_information_1:
        __construct:
            sequence: 10
            text: 'default interruption default additional information 1 - 7751bd17-98b0-4d87-acd2-dbd0cca132b6'
            alsoFrontView: false
            icon: 'handyman'
            highlight: false
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'
