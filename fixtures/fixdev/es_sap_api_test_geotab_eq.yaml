App\Domain\Entity\Equipment:
  es_equipment_1:
    id: '4fe5d82a-543a-47d8-955e-df5f2c0c8207'
    tenant: '<getSpainTenant()>'
    externalId: 'ES_EQUIPMENT_1'
    status: '<(App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE)>'
    height: 3550
    length: 6815
    width: 2550
    weight: 12130
    minimumLoad: 0
    overload: 0
    loadCapacity: 0
    totalPermissibleWeight: 0
    maxAxleLoad: 0
    licensePlate: 'ES-AK 1'
    type: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
    containerMounting: single
    branch: '@branch_9'
    search: 'ES-AK 1 ES_EQUIPMENT_1 ask'

App\Domain\Entity\ConnectedDevice:
  es_equipment_device_1:
    tenant: '<getSpainTenant()>'
    equipment: '@es_equipment_1'
    pin: 'b101D'
    macAddress: ''
    connectionType: '<(App\Domain\Entity\Enum\Types\ConnectionType::CLOUD_API)>'
    hardwareType: '<(App\Domain\Entity\Enum\Types\HardwareType::GEOTAB)>'

App\Domain\Entity\EquipmentVersion:
  es_equipment_1_version_1:
    tenant: '<getSpainTenant()>'
    equipment: '@es_equipment_1'
    height: 3550
    length: 6815
    width: 2550
    weight: 12130
    minimumLoad: 0
    overload: 0
    loadCapacity: 0
    totalPermissibleWeight: 0
    maxAxleLoad: 0
    licensePlate: 'ES-AK 1'
    type: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
    containerMounting: single
    branch: '@branch_9'
