App\Domain\Entity\DefaultTaskGroup:
    sap_api_test_test_disposalsite_default_tg:
        tourDataConfig: '@sap_api_test_tour_config_1'
        title: 'disposal-site-drive (api-test)'
        sequenceNumber: '1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSAL_SITE_TEMPLATE)>'
        tenant: '<getGermanyTenant()>'
        onlyForManualCreation: true

App\Domain\Entity\DefaultTask:
    sap_api_test_test_disposalsite_default_t_1:
        tenant: '<getGermanyTenant()>'
        name: dispose
        type: 'dispose'
        activatedBySapData: false
        externalId: DIS111
        elementItems:
            - '@sap_api_test_test_disposalsite_default_el_1_1'
            - '@sap_api_test_test_disposalsite_default_el_1_2'
    sap_api_test_test_disposalsite_default_t_2:
        tenant: '<getGermanyTenant()>'
        name: weight
        type: 'weigh'
        activatedBySapData: true
        externalId: DIS112
    sap_api_test_test_disposalsite_default_t_3:
        tenant: '<getGermanyTenant()>'
        name: wastePicker
        type: 'wastePicker'
        activatedBySapData: true
        externalId: DIS113
        elementItems:
            - '@sap_api_test_test_disposalsite_default_el_3_1'

App\Domain\Entity\DefaultTaskRelation:
    sap_api_test_test_disposalsite_default_tr_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@sap_api_test_test_disposalsite_default_tg'
        defaultTask: '@sap_api_test_test_disposalsite_default_t_1'
    sap_api_test_test_disposalsite_default_tr_2:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 10
        optional: false
        defaultTaskGroup: '@sap_api_test_test_disposalsite_default_tg'
        defaultTask: '@sap_api_test_test_disposalsite_default_t_2'
    sap_api_test_test_disposalsite_default_tr_3:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 20
        optional: false
        defaultTaskGroup: '@sap_api_test_test_disposalsite_default_tg'
        defaultTask: '@sap_api_test_test_disposalsite_default_t_3'

App\Domain\Entity\ValueObject\Element:
    sap_api_test_test_disposalsite_default_el_1_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'disposed? (api-test)'
            label: 'disposed (api-test)'
            patternHint: 'test'
            referenceType: 'asd1'
    sap_api_test_test_disposalsite_default_el_1_2:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'weigh (api-test)'
            label: 'weigh (api-test)'
            patternHint: ''
            referenceType: 'asd1'
    sap_api_test_test_disposalsite_default_el_3_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            label: 'waste-picker-material'
            referenceType: 'wastePicker'
