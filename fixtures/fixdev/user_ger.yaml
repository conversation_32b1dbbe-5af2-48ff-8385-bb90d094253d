App\Domain\Entity\User:
    user_4:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: max<PERSON><PERSON><PERSON>-prezero
            firstname: Max
            lastname: <PERSON><PERSON><PERSON>
        email: 'max<PERSON><PERSON><PERSON>@prezero.com'
        status: 1
        id: 'b5e7d574-8355-4f21-a525-2a77574de446'
        createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:11"))>'
        modifiedAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
        staff: '@staff_user_4'
    user_5:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: mariamusterfrau-prezero
            firstname: Maria
            lastname: Musterfrau
        email: '<EMAIL>'
        status: 1
        id: 'a17f6837-c124-4234-a4bb-18c5a31920c5'
        createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
        modifiedAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
        staff: '@staff_user_5'
    user_api_1:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: api-user-1
            firstname: Api
            lastname: User
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@staff_user_api_1'
    user_api_2:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: api-user-2
            firstname: Api
            lastname: User
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@staff_user_api_2'
    user_api_3:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: api-user-3
            firstname: Api
            lastname: User
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@staff_user_api_3'
    user_api_4:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: api-user-4
            firstname: Api
            lastname: User
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@staff_user_api_4'
    user_portal_de:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-user-de
            firstname: 'portal-user'
            lastname: 'de'
        id: 'ddba0342-cb56-4f81-b2d5-56d979118df5'
        email: '<EMAIL>'
        country: 'de'
        branchAccess: [ 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8' ]
        roles: [ "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_VIEW_BRANCH_DATA", "ROLE_PORTAL_ACCESS" ]
    user_portal_de_lu:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-user-de-lu
            firstname: 'portal-user'
            lastname: 'de-lu'
        id: 'e5afd1f0-197c-4e06-92d8-09b1e1bd4de8'
        email: '<EMAIL>'
        country: 'lu'
        branchAccess: [ 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8' ]
        roles: [ "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_VIEW_BRANCH_DATA", "ROLE_PORTAL_ACCESS" ]
    user_portal_country_admin_de:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-country-admin-de
            firstname: 'portal-c-admin'
            lastname: 'de'
        id: 'd191400e-0e16-4768-a1d2-9abfafe75dbb'
        email: '<EMAIL>'
        country: 'de'
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::DE)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS" ]
    user_portal_admin_de:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-admin-de
            firstname: 'portal-admin'
            lastname: 'de'
        id: '8f93452f-c58b-4720-85a5-1a97db5f88bb'
        email: '<EMAIL>'
        country: de
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::DE)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_COUNTRY_ADMINS", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS", "ROLE_PORTAL_MANAGE_FAQ_ADMINS" ]
    user_portal_admin_de_lu:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-admin-de-lu
            firstname: 'portal-admin'
            lastname: 'de-lu'
        id: '25841052-0e2c-427e-9e60-af6098f7f508'
        email: '<EMAIL>'
        country: lu
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::LU)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_COUNTRY_ADMINS", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS", "ROLE_PORTAL_MANAGE_FAQ_ADMINS" ]
    user_portal_admin_de_both:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-admin-de-both
            firstname: 'portal-admin'
            lastname: 'de-both'
        id: '18bfbf91-4fb3-4e20-99ae-c975cec3ce18'
        email: '<EMAIL>'
        country: de
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::DE)>', '<(App\Domain\Entity\Enum\COUNTRY::LU)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_COUNTRY_ADMINS", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS", "ROLE_PORTAL_MANAGE_FAQ_ADMINS" ]
    user_portal_admin2_de:
        __construct:
            tenant: '<getGermanyTenant()>'
            username: portal-admin2-de
            firstname: 'portal-admin-2'
            lastname: 'de'
        id: 'a971a45c-a5f3-4369-bb07-63a289d4d3ab'
        email: '<EMAIL>'
        country: de
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::DE)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_COUNTRY_ADMINS", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS", "ROLE_PORTAL_MANAGE_FAQ_ADMINS" ]
