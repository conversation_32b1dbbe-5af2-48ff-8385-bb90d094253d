App\Domain\Entity\StaffConfig:
  8zn2f_StaffConf_1:
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultInterruption:
    staff_interruption_1:
        tenant: '<getGermanyTenant()>'
        id: '08f252e4-2ce5-4b7d-9ccf-8394a27ddf48'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
        description: 'staff break interruption'
        externalId: '<regexify("[a-z][a-z-]{1,30}[a-z]")>'
    staff_interruption_2:
        tenant: '<getGermanyTenant()>'
        id: '6be629be-7d37-4377-a6ce-844d9c3be546'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::OTHER)>'
        description: 'staff other interruption'
        externalId: '<regexify("[a-z][a-z-]{1,30}[a-z]")>'
App\Domain\Entity\DefaultInterruptionRelation:
    staff_interruption_relation_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 15
        staffConfig: '@8zn2f_StaffConf_1'
        defaultInterruption: '@staff_interruption_1'
    staff_interruption_relation_2:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 5
        staffConfig: '@8zn2f_StaffConf_1'
        defaultInterruption: '@staff_interruption_2'

App\Domain\Entity\DefaultTaskGroup:
    staff_dtg_1:
        tenant: '<getGermanyTenant()>'
        title: staff-taskgroup1
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        sequenceNumber: 0
        staffConfig: '@8zn2f_StaffConf_1'
    staff_dtg_2:
        tenant: '<getGermanyTenant()>'
        title: staff-taskgroup2
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        sequenceNumber: 1
        staffConfig: '@8zn2f_StaffConf_1'
    staff_dtg_3:
        tenant: '<getGermanyTenant()>'
        title: staff-taskgroup3
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        sequenceNumber: 2
        staffConfig: '@8zn2f_StaffConf_1'
        rules: ['@stafftaskgroup_rule_3_1', '@stafftaskgroup_rule_3_2', '@stafftaskgroup_rule_3_3']
    staff_interruption_tg_1:
        tenant: '<getGermanyTenant()>'
        id: 'e43861d8-656d-416e-a32e-031aaf2b6d9a'
        title: 'staff-interruption taskgroup1'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        sequenceNumber: 0
        defaultInterruption: '@staff_interruption_1'

App\Domain\Entity\DefaultTask:
    staff_dtg_t_1:
        tenant: '<getGermanyTenant()>'
        name: staff.tgshared.task
        type: '<word()>'
        activatedBySapData: false
        externalId: StaffTgTask1
        elementItems:
            - '@default_element_oil_dropdown_2'
    staff_dtg_t_2:
        tenant: '<getGermanyTenant()>'
        name: staff.tg1.task1.2
        type: '<word()>'
        activatedBySapData: false
        externalId: StaffTgTask1.2
        elementItems:
            - '@default_element_number_litres3'
            - '@default_element_string'
    staff_dtg_t_3:
        tenant: '<getGermanyTenant()>'
        name: staff.tg1.task2.2
        type: '<word()>'
        activatedBySapData: false
        externalId: StaffTgTask2.2
        elementItems:
            - '@stafftask_element_number'
            - '@stafftask_element_string'
            - '@stafftask_element_text'
            - '@stafftask_element_accept'
            - '@stafftask_element_date'
            - '@stafftask_element_photo'
            - '@stafftask_element_qr'
            - '@stafftask_element_time'
            - '@stafftask_element_boolean'
            - '@stafftask_element_signature'
    staff_dtg_t_4:
        tenant: '<getGermanyTenant()>'
        name: staff.tg1.task2.3
        type: '<word()>'
        activatedBySapData: false
        externalId: StaffTgTask2.3
        elementItems: []

    staff_interruption_t_1:
        tenant: '<getGermanyTenant()>'
        id: 'af5d34df-8b07-48bb-b4dc-3e10e9e261af'
        name: staffconfig.interruption.task1
        type: '<word()>'
        activatedBySapData: false
        externalId: StaffIntTask1

App\Domain\Entity\DefaultTaskRelation:
    staff_dtg_tr_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@staff_dtg_1'
        defaultTask: '@staff_dtg_t_1'
    staff_dtg_tr_2:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@staff_dtg_2'
        defaultTask: '@staff_dtg_t_1'
    staff_dtg_tr_3:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        optional: false
        defaultTaskGroup: '@staff_dtg_1'
        defaultTask: '@staff_dtg_t_2'
    staff_dtg_tr_4:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        optional: false
        defaultTaskGroup: '@staff_dtg_2'
        defaultTask: '@staff_dtg_t_3'
    staff_dtg_tr_5:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 2
        optional: false
        defaultTaskGroup: '@staff_dtg_2'
        defaultTask: '@staff_dtg_t_4'
        rules: ['@stafftask_rule_2_1', '@stafftask_rule_2_2', '@stafftask_rule_2_3']
    staff_interruption_tr_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@staff_interruption_tg_1'
        defaultTask: '@staff_interruption_t_1'

App\Domain\Entity\ValueObject\Element:
    stafftask_element_boolean:
        __construct:
            id: '0384d253-7e06-4c2b-9cc7-8acec8496cf4'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>'
            placeholder: input.boolean.ph
            label: input.boolean.label
            description: 'Test boolean element'
            referenceType: 'asd1'
    stafftask_element_text:
        __construct:
            id: '6343d6c1-4a32-48ce-b087-e073219c88d3'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::TEXT)>'
            placeholder: input.text.ph
            label: input.text.label
            description: 'Test text element'
            referenceType: 'asd1'
    stafftask_element_qr:
        __construct:
            id: '4190fc9e-7d21-4852-b9fd-44eeeeeac3a7'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: input.qr.ph
            label: input.qr.label
            description: 'Test qr element'
            referenceType: 'asd1'
    stafftask_element_signature:
        __construct:
            id: 'cb654c63-2570-4033-ad48-5f8375b9c767'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>'
            placeholder: input.signature.ph
            label: input.signature.label
            description: 'Test signature element'
            referenceType: 'asd1'
    stafftask_element_photo:
        __construct:
            id: '13d5ff0c-2f85-41cc-adad-0f4db6e25d3b'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>'
            placeholder: input.photo.ph
            label: input.photo.label
            description: 'Test photo element'
            referenceType: 'asd1'
    stafftask_element_time:
        __construct:
            id: 'fdd3d1ae-4904-4c2c-a5aa-dc0668c0dd4d'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::TIME)>'
            placeholder: input.time.ph
            label: input.time.label
            description: 'Test time element'
            referenceType: 'asd1'
    stafftask_element_date:
        __construct:
            id: '82a4fcee-5887-4f62-95ea-18d58ebecc53'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::DATE)>'
            placeholder: input.date.ph
            label: input.date.label
            description: 'Test date element'
            referenceType: 'asd1'
    stafftask_element_accept:
        __construct:
            id: '0b4099ae-d947-44b9-8d7f-4344dfe5ccd3'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: input.accept.ph
            label: input.accept.label
            description: 'Test accept element'
            referenceType: 'asd1'
    stafftask_element_string:
        __construct:
            id: '5e88d1ea-25d9-486f-bcda-02147b6573f4'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            valueStringMinLength: 3
            valueStringMaxLength: 50
            placeholder: input.text.ph
            label: input.text.label
            description: 'test config text input'
            referenceType: 'asd1'
    stafftask_element_number:
        __construct:
            id: 'c7a962a2-c7b6-4411-badb-41b0151268ca'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.ph
            label: number
            pattern: '\[0-9]+'
            description: 'input of number'
            referenceType: 'asd1'
            values: [ '42' ]

App\Domain\Entity\ValueObject\Rule:
    stafftask_rule_2_1:
        __construct:
            elementId: 'c7a962a2-c7b6-4411-badb-41b0151268ca'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '42'
    stafftask_rule_2_2:
        __construct:
            elementId: '0384d253-7e06-4c2b-9cc7-8acec8496cf4'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    stafftask_rule_2_3:
        __construct:
            elementId: '5e88d1ea-25d9-486f-bcda-02147b6573f4'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'my test'
    stafftaskgroup_rule_3_1:
        __construct:
            elementId: 'c7a962a2-c7b6-4411-badb-41b0151268ca'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '42'
    stafftaskgroup_rule_3_2:
        __construct:
            elementId: '0384d253-7e06-4c2b-9cc7-8acec8496cf4'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    stafftaskgroup_rule_3_3:
        __construct:
            elementId: '5e88d1ea-25d9-486f-bcda-02147b6573f4'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'my test'
