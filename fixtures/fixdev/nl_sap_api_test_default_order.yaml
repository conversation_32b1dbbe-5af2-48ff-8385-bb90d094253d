App\Domain\Entity\DefaultTaskGroup:
    nl_sap_api_test_tg_1:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Start (nl-api-test)'
        sequenceNumber: '10'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tg_2:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Kunde (nl-api-test tg2)'
        sequenceNumber: '20'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_1_1' ]
    nl_sap_api_test_tg_3:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Entsorgungsanlage (nl-api-test)'
        sequenceNumber: '30'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_2_1' ]
    nl_sap_api_test_tg_4:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Kunde (nl-api-test)'
        sequenceNumber: '40'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_3_1' ]
    nl_sap_api_test_tg_5:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Containerlager (nl-api-test)'
        sequenceNumber: '50'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_1_2' ]
    nl_sap_api_test_tg_6:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Kunde (nl-api-test)'
        sequenceNumber: '60'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_1_3', '@nl_sap_api_test_tgrule_5_1' ]
    nl_sap_api_test_tg_7:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Entsorgungsanlage (nl-api-test)'
        sequenceNumber: '70'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_6_1' ]
    nl_sap_api_test_tg_8:
        orderTypeConfig: '@nl_sap_api_test_order_config_1'
        title: 'Containerlager (nl-api-test)'
        sequenceNumber: '80'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_tgrule_7_1' ]

App\Domain\Entity\DefaultTaskRelation:
    nl_sap_api_test_tr_1_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_1'
        defaultTaskGroup: '@nl_sap_api_test_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_2_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_2'
        defaultTaskGroup: '@nl_sap_api_test_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_2_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_3'
        defaultTaskGroup: '@nl_sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_trule_2_1' ]
    nl_sap_api_test_tr_2_3:
        sequenceNumber: '30'
        defaultTask: '@nl_sap_api_test_t_4'
        defaultTaskGroup: '@nl_sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_trule_2_2' ]
    nl_sap_api_test_tr_2_4:
        sequenceNumber: '40'
        defaultTask: '@nl_sap_api_test_t_5'
        defaultTaskGroup: '@nl_sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_trule_2_3' ]
    nl_sap_api_test_tr_2_5:
        sequenceNumber: '50'
        defaultTask: '@nl_sap_api_test_t_6'
        defaultTaskGroup: '@nl_sap_api_test_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
        rules: [ '@nl_sap_api_test_trule_2_4' ]
    nl_sap_api_test_tr_3_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_14'
        defaultTaskGroup: '@nl_sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_3_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_15'
        defaultTaskGroup: '@nl_sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_3_3:
        sequenceNumber: '30'
        defaultTask: '@nl_sap_api_test_t_20'
        defaultTaskGroup: '@nl_sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_4_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_21'
        defaultTaskGroup: '@nl_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_4_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_3'
        defaultTaskGroup: '@nl_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_4_3:
        sequenceNumber: '30'
        defaultTask: '@nl_sap_api_test_t_4'
        defaultTaskGroup: '@nl_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_4_4:
        sequenceNumber: '40'
        defaultTask: '@nl_sap_api_test_t_5'
        defaultTaskGroup: '@nl_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_4_5:
        sequenceNumber: '50'
        defaultTask: '@nl_sap_api_test_t_7'
        defaultTaskGroup: '@nl_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_4_6:
        sequenceNumber: '60'
        defaultTask: '@nl_sap_api_test_t_8'
        defaultTaskGroup: '@nl_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_5_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_9'
        defaultTaskGroup: '@nl_sap_api_test_tg_5'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_5_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_10'
        defaultTaskGroup: '@nl_sap_api_test_tg_5'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_6_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_11'
        defaultTaskGroup: '@nl_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_6_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_3'
        defaultTaskGroup: '@nl_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_6_3:
        sequenceNumber: '30'
        defaultTask: '@nl_sap_api_test_t_4'
        defaultTaskGroup: '@nl_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_6_4:
        sequenceNumber: '40'
        defaultTask: '@nl_sap_api_test_t_5'
        defaultTaskGroup: '@nl_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_6_5:
        sequenceNumber: '50'
        defaultTask: '@nl_sap_api_test_t_12'
        defaultTaskGroup: '@nl_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_6_6:
        sequenceNumber: '60'
        defaultTask: '@nl_sap_api_test_t_13'
        defaultTaskGroup: '@nl_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_7_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_14'
        defaultTaskGroup: '@nl_sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_7_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_15'
        defaultTaskGroup: '@nl_sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_7_3:
        sequenceNumber: '30'
        defaultTask: '@nl_sap_api_test_t_16'
        defaultTaskGroup: '@nl_sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_8_1:
        sequenceNumber: '10'
        defaultTask: '@nl_sap_api_test_t_17'
        defaultTaskGroup: '@nl_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_8_2:
        sequenceNumber: '20'
        defaultTask: '@nl_sap_api_test_t_18'
        defaultTaskGroup: '@nl_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_8_3:
        sequenceNumber: '30'
        defaultTask: '@nl_sap_api_test_t_19'
        defaultTaskGroup: '@nl_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'
    nl_sap_api_test_tr_8_4:
        sequenceNumber: '40'
        defaultTask: '@nl_sap_api_test_t_22'
        defaultTaskGroup: '@nl_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getNetherlandsTenant()>'

App\Domain\Entity\DefaultTask:
    nl_sap_api_test_t_1:
        name: 'Anfahrtsauswahl (nl-api-test)'
        type: 'start'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_1'
    nl_sap_api_test_t_2:
        name: 'Ankunft (nl-api-test t2)'
        type: 'arrival'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_2'
    nl_sap_api_test_t_3:
        name: 'Containeranzahl (nl-api-test t3)'
        type: 'container_count'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_3'
    nl_sap_api_test_t_4:
        name: 'Kundenwiegung (nl-api-test t4)'
        activatedBySapData: true
        type: 'customer_weight'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_4_1'
            - '@nl_sap_api_test_e_5_1'
            - '@nl_sap_api_test_e_6_1'
            - '@nl_sap_api_test_e_7_1'
    nl_sap_api_test_t_5:
        name: 'Unterschrift (nl-api-test t5)'
        activatedBySapData: true
        type: 'deliveryNote_sig'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_8'
    nl_sap_api_test_t_6:
        name: 'Fertig (nl-api-test t6)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_9'
    nl_sap_api_test_t_7:
        name: 'Containeranzahl (nl-api-test)'
        type: 'container_count'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_10_1'
    nl_sap_api_test_t_8:
        name: 'Fertig (nl-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_9_4'
    nl_sap_api_test_t_9:
        name: 'Ankunft (nl-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_2_1'
    nl_sap_api_test_t_10:
        name: 'Fertig (nl-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_9_1'
    nl_sap_api_test_t_11:
        name: 'Ankunft (nl-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_2_2'
    nl_sap_api_test_t_12:
        name: 'Containeranzahl (nl-api-test)'
        type: 'container_count'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_10_2'
    nl_sap_api_test_t_13:
        name: 'Fertig (nl-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_9_2'
    nl_sap_api_test_t_14:
        name: 'Ankunft (nl-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_2_3'
    nl_sap_api_test_t_15:
        name: 'Wiegedaten (nl-api-test)'
        activatedBySapData: true
        type: 'weighingnote'
        tenant: '<getNetherlandsTenant()>'
        externalId: 'pdfT1'
        elementItems:
            - '@nl_sap_api_test_e_4_2'
            - '@nl_sap_api_test_e_5_2'
            - '@nl_sap_api_test_e_6_2'
            - '@nl_sap_api_test_e_7_2'
    nl_sap_api_test_t_16:
        name: 'Fertig (nl-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_11'
    nl_sap_api_test_t_17:
        name: 'Ankunft (nl-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_2_4'
    nl_sap_api_test_t_18:
        name: 'Containerscan (nl-api-test)'
        type: 'container_scan'
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_10_3'
    nl_sap_api_test_t_19:
        name: 'Fertig (nl-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_9_5'
    nl_sap_api_test_t_20:
        name: 'Fertig (nl-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_9_3'
    nl_sap_api_test_t_21:
        name: 'Ankunft (nl-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_2_5'
    nl_sap_api_test_t_22:
        name: 'Taskgroup (nl-iot-test)'
        type: 'iot_task'
        sapAction: true
        tenant: '<getNetherlandsTenant()>'
        elementItems:
            - '@nl_sap_api_test_e_12'

App\Domain\Entity\ValueObject\Element:
    nl_sap_api_test_e_1:
        __construct:
            id: '6ada7d67-67bb-4068-899a-b973c923020e'
            referenceType: 'taskgroupPicker'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'Wie soll der Auftrag gestartet werden? (nl-api-test)'
            label: 'Wie soll der Auftrag gestartet werden? (nl-api-test)'
            patternHint: 'Auswahl des Starts'
            options:
                - '@nl_sap_api_test_eo_1_1'
                - '@nl_sap_api_test_eo_1_2'
                - '@nl_sap_api_test_eo_1_3'
    nl_sap_api_test_e_2:
        __construct:
            id: 'b3ce7cb0-8e99-44f4-87e2-789d4e07446b'
            referenceType: 'arrival'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Angekommen? (nl-api-test)'
            label: 'Ankunft (nl-api-test)'
            patternHint: 'Ankunft'
    nl_sap_api_test_e_2_{1..5}:
        __construct:
            referenceType: 'arrival'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Angekommen? (nl-api-test)'
            label: 'Ankunft (nl-api-test)'
            patternHint: 'Ankunft'
    nl_sap_api_test_e_3:
        __construct:
            referenceType: 'containerAmount'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Containeranzahl erfassen (nl-api-test)'
            label: 'Bitte Containeranzahl erfassen (nl-api-test)'
            patternHint: 'Containeranzahl'
    nl_sap_api_test_e_4_{1..2}:
        __construct:
            referenceType: 'weighingNoteNumber'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            placeholder: 'Bitte Wiegescheinnummer erfassen (nl-api-test)'
            label: 'Bitte Wiegescheinnummer erfassen (nl-api-test)'
            patternHint: 'Wiegescheinnummer'
    nl_sap_api_test_e_5_{1..2}:
        __construct:
            referenceType: 'unit'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'In welcher Einheit soll das Gewicht erfasst werden? (nl-api-test)'
            label: 'In welcher Einheit soll das Gewicht erfasst werden? (nl-api-test)'
            patternHint: 'Einheit'
            options:
                - '@nl_sap_api_test_eo_5_1'
                - '@nl_sap_api_test_eo_5_2'
    nl_sap_api_test_e_6_{1..2}:
        __construct:
            referenceType: 'value'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Gewicht erfassen (nl-api-test)'
            label: 'Bitte Gewicht erfassen (nl-api-test)'
            patternHint: 'Menge'
    nl_sap_api_test_e_7_{1..2}:
        __construct:
            referenceType: 'weighingNoteQR'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: 'Bitte Wiegeschein-QR-Code erfassen (nl-api-test)'
            label: 'Bitte Wiegeschein-QR-Code erfassen (nl-api-test)'
            patternHint: 'QR-Code'
    nl_sap_api_test_e_8:
        __construct:
            referenceType: 'signature'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>'
            placeholder: 'Bitte Unterschrift einholen (nl-api-test)'
            label: 'Bitte Unterschrift einholen (nl-api-test)'
            patternHint: 'Unterschrift'
    nl_sap_api_test_e_9:
        __construct:
            id: 'edede91b-1a07-4e8b-a9ab-86425f8ae20e'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (nl-api-test)'
            label: 'Fertig (nl-api-test)'
            patternHint: 'Fertig'
    nl_sap_api_test_e_9_1:
        __construct:
            id: '2922b36e-e612-4c51-9101-c713b6e241f2'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (nl-api-test)'
            label: 'Fertig (nl-api-test)'
            patternHint: 'Fertig'
    nl_sap_api_test_e_9_2:
        __construct:
            id: '073afbec-8875-4d67-bb31-7d42b8f42399'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (nl-api-test)'
            label: 'Fertig (nl-api-test)'
            patternHint: 'Fertig'
    nl_sap_api_test_e_9_3:
        __construct:
            id: '83d435ba-f27f-4ba4-8e29-226c686ef244'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (nl-api-test)'
            label: 'Fertig (nl-api-test)'
            patternHint: 'Fertig'
    nl_sap_api_test_e_9_{4..5}:
        __construct:
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (nl-api-test)'
            label: 'Fertig (nl-api-test)'
            patternHint: 'Fertig'
    nl_sap_api_test_e_10_{1..3}:
        __construct:
            referenceType: 'containerScan'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: 'Bitte Container-QR-Code scannen (nl-api-test)'
            label: 'Bitte Container-QR-Code scannen (nl-api-test)'
            patternHint: 'QR-Code'
    nl_sap_api_test_e_11:
        __construct:
            id: 'e2fb2074-b396-49b7-a8f5-abd1f11d65f4'
            referenceType: 'continue'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'Wie soll der Auftrag fortgeführt werden? (nl-api-test)'
            label: 'Wie soll der Auftrag fortgeführt werden? (nl-api-test)'
            patternHint: 'Fortsetzen'
            options:
                - '@nl_sap_api_test_eo_11_1'
                - '@nl_sap_api_test_eo_11_2'
    nl_sap_api_test_e_12:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            label: 'nl-iot'
            referenceType: 'vc-qr'
            containerAction: 'place'

App\Domain\Entity\ValueObject\ElementOption:
    nl_sap_api_test_eo_1_1:
        __construct:
            name: 'Direkt zum Kunden (leeren) (nl-api-test)'
            sequenceNumber: 10
            id: '22aff3ce-9d97-4605-973a-9cbbcfc4f831'
    nl_sap_api_test_eo_1_2:
        __construct:
            name: 'Container aus Lager holen (tauschen) (nl-api-test)'
            sequenceNumber: 20
            id: 'd9946c83-e66c-4b52-8466-fd62b6254a94'
    nl_sap_api_test_eo_1_3:
        __construct:
            name: 'Direkt zum Kunden (tauschen) (nl-api-test)'
            sequenceNumber: 30
            id: 'bbc7263f-3372-4846-b01d-10a998fe8f8f'
    nl_sap_api_test_eo_5_1:
        __construct:
            name: 'KG (nl-api-test)'
            sequenceNumber: 40
    nl_sap_api_test_eo_5_2:
        __construct:
            name: 'TO (nl-api-test)'
            sequenceNumber: 50
    nl_sap_api_test_eo_11_1:
        __construct:
            name: 'Container im Lager abstellen (nl-api-test)'
            sequenceNumber: 60
            id: 'd67c8e92-60e0-4b2a-ae76-0b93df6795f6'
    nl_sap_api_test_eo_11_2:
        __construct:
            name: 'Auftrag beenden (nl-api-test)'
            sequenceNumber: 70

App\Domain\Entity\ValueObject\Rule:
    nl_sap_api_test_tgrule_1_1:
        __construct:
            elementId: '6ada7d67-67bb-4068-899a-b973c923020e'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '22aff3ce-9d97-4605-973a-9cbbcfc4f831'
    nl_sap_api_test_tgrule_1_2:
        __construct:
            elementId: '6ada7d67-67bb-4068-899a-b973c923020e'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'd9946c83-e66c-4b52-8466-fd62b6254a94'
    nl_sap_api_test_tgrule_1_3:
        __construct:
            elementId: '6ada7d67-67bb-4068-899a-b973c923020e'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'bbc7263f-3372-4846-b01d-10a998fe8f8f'
    nl_sap_api_test_tgrule_2_1:
        __construct:
            elementId: 'edede91b-1a07-4e8b-a9ab-86425f8ae20e'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_tgrule_3_1:
        __construct:
            elementId: '83d435ba-f27f-4ba4-8e29-226c686ef244'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_tgrule_5_1:
        __construct:
            elementId: '2922b36e-e612-4c51-9101-c713b6e241f2'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_tgrule_6_1:
        __construct:
            elementId: '073afbec-8875-4d67-bb31-7d42b8f42399'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_tgrule_7_1:
        __construct:
            elementId: 'e2fb2074-b396-49b7-a8f5-abd1f11d65f4'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'd67c8e92-60e0-4b2a-ae76-0b93df6795f6'

    nl_sap_api_test_trule_2_1:
        __construct:
            elementId: 'b3ce7cb0-8e99-44f4-87e2-789d4e07446b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_trule_2_2:
        __construct:
            elementId: 'b3ce7cb0-8e99-44f4-87e2-789d4e07446b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_trule_2_3:
        __construct:
            elementId: 'b3ce7cb0-8e99-44f4-87e2-789d4e07446b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    nl_sap_api_test_trule_2_4:
        __construct:
            elementId: 'b3ce7cb0-8e99-44f4-87e2-789d4e07446b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
