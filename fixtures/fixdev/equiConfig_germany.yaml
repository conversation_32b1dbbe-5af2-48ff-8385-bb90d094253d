App\Domain\Entity\EquipmentConfig:
    8zn2f_EquipmentConf_1:
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultTaskGroup:
    equipment_dtg_1:
        tenant: '<getGermanyTenant()>'
        id: '9ebb9abe-5e25-4c70-9f1e-91933610cf35'
        title: equipment-taskgroup1
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        sequenceNumber: 10
        toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
        equipmentConfig: '@8zn2f_EquipmentConf_1'
    equipment_dtg_2:
        tenant: '<getGermanyTenant()>'
        id: 'c6c075ad-0372-4e4c-9418-a5fa8bbd99d8'
        title: equipment-taskgroup2
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        sequenceNumber: 20
        toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
        equipmentConfig: '@8zn2f_EquipmentConf_1'
    equipment_dtg_3:
        tenant: '<getGermanyTenant()>'
        id: '36c7bb9f-d39e-419d-b020-2b27dfc76a52'
        title: equipment-control-1
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
        sequenceNumber: 30
        toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
        equipmentConfig: '@8zn2f_EquipmentConf_1'
        additionalInformationItems: [ '@equipment_dtg_3_add_info_1', '@equipment_dtg_3_add_info_2' ]
    equipment_dtg_4:
        tenant: '<getGermanyTenant()>'
        id: '8ba948c2-68c9-4df2-88a0-fbef9a44b281'
        title: equipment-control-2
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
        sequenceNumber: 40
        toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
        equipmentConfig: '@8zn2f_EquipmentConf_1'
        rules: [ '@vc_test_tgrule_1' ]
        additionalInformationItems: [ '@equipment_dtg_4_add_info_1', '@equipment_dtg_4_add_info_2' ]

App\Domain\Entity\ValueObject\Rule:
    vc_test_tgrule_1:
        __construct:
            elementId: '33f26bd1-733f-4c3c-8945-9241aa5b3945'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'

App\Domain\Entity\DefaultTask:
    equipment_dtg_t_1:
        tenant: '<getGermanyTenant()>'
        id: '3ab44f5c-f459-4583-ac44-22ed856c1fbf'
        name: equipment.tgshared.task
        type: '<word()>'
        activatedBySapData: false
        externalId: EquTgTask1
        elementItems:
            - '@default_element_oil_dropdown_1'
    equipment_dtg_t_2:
        tenant: '<getGermanyTenant()>'
        id: 'f3a5c5b5-4939-496d-8aa1-a7c819fdf144'
        name: equipment.tg1.task1.2
        type: '<word()>'
        activatedBySapData: false
        externalId: EquTgTask1.2
        elementItems:
            - '@default_element_string2'
            - '@default_element_number_litres'
    equipment_dtg_t_3:
        tenant: '<getGermanyTenant()>'
        id: 'e4e4fc28-3d46-4db3-8ce0-6dcccfcaea9b'
        name: equipment.tg1.task2.2
        type: '<word()>'
        activatedBySapData: false
        externalId: EquTgTask2.2
        elementItems:
            - '@default_element_string3'
            - '@default_element_number_common_2'
    equipment_dtg_t_4:
        tenant: '<getGermanyTenant()>'
        id: '5950ba4e-0d63-4d0a-bec9-0c7cdeaf9538'
        name: equipment.check1.task1
        type: 'eq-check'
        activatedBySapData: false
        externalId: vc1.1
        elementItems:
            - '@default_element_vc1'
            - '@default_element_vc6_1'
    equipment_dtg_t_5:
        tenant: '<getGermanyTenant()>'
        id: '62392e10-d736-46b1-8ee4-5b8f2be4c8cc'
        name: equipment.check1.task2
        type: 'eq-check'
        activatedBySapData: false
        externalId: vc1.2
        elementItems:
            - '@default_element_vc2'
            - '@default_element_vc6_2'
    equipment_dtg_t_6:
        tenant: '<getGermanyTenant()>'
        id: 'f5c3a5a5-bfac-4cd7-859a-70e222c50653'
        name: equipment.check2.task1
        type: 'eq-check'
        activatedBySapData: false
        externalId: vc2.1
        elementItems:
            - '@default_element_vc3'
            - '@default_element_vc6_3'
    equipment_dtg_t_7:
        tenant: '<getGermanyTenant()>'
        id: '9cd92be7-1108-4788-b628-254d600d8ba4'
        name: equipment.check2.task2
        type: 'eq-check'
        activatedBySapData: false
        externalId: vc2.2
        elementItems:
            - '@default_element_vc4'
            - '@default_element_vc5'
            - '@default_element_vc6_4'
            - '@default_element_vc7'
            - '@default_element_vc8'
            - '@default_element_vc9'
            - '@default_element_vc10'
            - '@default_element_vc11'
            - '@default_element_vc12'
            - '@default_element_vc13'
            - '@default_element_vc14'
            - '@default_element_vc15'

App\Domain\Entity\DefaultTaskRelation:
    equipment_dtg_tr_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@equipment_dtg_1'
        defaultTask: '@equipment_dtg_t_1'
    equipment_dtg_tr_2:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@equipment_dtg_2'
        defaultTask: '@equipment_dtg_t_1'
    equipment_dtg_tr_3:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        optional: false
        defaultTaskGroup: '@equipment_dtg_1'
        defaultTask: '@equipment_dtg_t_2'
    equipment_dtg_tr_4:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        optional: false
        defaultTaskGroup: '@equipment_dtg_2'
        defaultTask: '@equipment_dtg_t_3'
    equipment_dtg_tr_5:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@equipment_dtg_3'
        defaultTask: '@equipment_dtg_t_4'
    equipment_dtg_tr_6:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 10
        optional: false
        defaultTaskGroup: '@equipment_dtg_3'
        defaultTask: '@equipment_dtg_t_5'
    equipment_dtg_tr_7:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@equipment_dtg_4'
        defaultTask: '@equipment_dtg_t_6'
    equipment_dtg_tr_8:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 10
        optional: false
        defaultTaskGroup: '@equipment_dtg_4'
        defaultTask: '@equipment_dtg_t_7'

App\Domain\Entity\ValueObject\Element:
    default_element_vc1:
        __construct:
            id: '33f26bd1-733f-4c3c-8945-9241aa5b3945'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>'
            label: 'oil level ok'
            referenceType: 'vc-oil'
    default_element_vc2:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>'
            label: 'water level ok'
            referenceType: 'vc-water'
    default_element_vc3:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>'
            label: 'tires ok'
            referenceType: 'vc-tire'
    default_element_vc4:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>'
            label: 'lights ok'
            referenceType: 'vc-light'
    default_element_vc5:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::BOOLEAN)>'
            label: 'battery ok'
            referenceType: 'vc-battery'
    default_element_vc6_{1..4}:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            label: 'control done'
            referenceType: 'vc-done'
    default_element_vc7:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            label: 'control description'
            referenceType: 'vc-dsc'
    default_element_vc8:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            label: 'control number'
            referenceType: 'vc-num'
    default_element_vc9:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            label: 'control qr'
            referenceType: 'vc-qr'
    default_element_vc10:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::DATE)>'
            label: 'control date'
            referenceType: 'vc-date'
    default_element_vc11:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::TIME)>'
            label: 'control time'
            referenceType: 'vc-time'
    default_element_vc12:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            label: 'control select'
            referenceType: 'vc-select'
            options:
                - '@ctl_default_element_option_1'
                - '@ctl_default_element_option_2'
    default_element_vc13:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>'
            label: 'control photo'
            referenceType: 'vc-photo'
    default_element_vc14:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>'
            label: 'control signature'
            referenceType: 'vc-signature'
    default_element_vc15:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>'
            label: 'control scale'
            referenceType: 'vc-scale'

App\Domain\Entity\ValueObject\ElementOption:
    ctl_default_element_option_1:
        __construct:
            name: control option 1
            sequenceNumber: 10
            id: '82f91666-ca5f-4b27-b3d6-d5d48be877e2'
    ctl_default_element_option_2:
        __construct:
            name: control option 2
            sequenceNumber: 20
            id: 'b09bd1bb-55f8-4edf-acb4-25a134e1062d'

App\Domain\Entity\ValueObject\AdditionalInformation:
    equipment_dtg_3_add_info_1:
        __construct:
            sequence: 0
            text: 'this is a piece of information 1.1'
            alsoFrontView: true
            icon: handyman
            highlight: true
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'
    equipment_dtg_3_add_info_2:
        __construct:
            sequence: 1
            text: 'this is a piece of information 1.2'
            alsoFrontView: true
            icon: handyman
            highlight: true
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'
    equipment_dtg_4_add_info_1:
        __construct:
            sequence: 0
            text: 'this is a piece of information 2.1'
            alsoFrontView: true
            icon: handyman
            highlight: true
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'
    equipment_dtg_4_add_info_2:
        __construct:
            sequence: 1
            text: 'this is a piece of information 2.2'
            alsoFrontView: true
            icon: handyman
            highlight: true
            source: '<(App\Domain\Entity\Enum\AdditionalInformationSource::CONFIG)>'
