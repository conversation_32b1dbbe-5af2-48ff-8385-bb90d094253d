App\Domain\Entity\Staff:
  es_staff_user_api_1:
    tenant: '<getSpainTenant()>'
    externalId: '1111ES'
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::IDLE)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: EsApi
    lastname: User1
    branch: '@branch_7'
    search: 'EsApi User1 1111ES'
  es_staff_user_api_2:
    tenant: '<getSpainTenant()>'
    externalId: EXTSTAFFAPIUSERES2
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::IDLE)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: ESApi
    lastname: User2
    branch: '@branch_7'
    search: 'ESApi User2 EXTSTAFFAPIUSERES2'

App\Domain\Entity\StaffVersion:
  es_staff_1_version_1:
    tenant: '<getSpainTenant()>'
    staff: '@es_staff_user_api_1'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: EsApi
    lastname: User1
    branch: '@branch_7'
  es_staff_2_version_1:
    tenant: '<getSpainTenant()>'
    staff: '@es_staff_user_api_2'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: ESApi
    lastname: User2
    branch: '@branch_7'
