App\Domain\Entity\OrderTypeConfig:
    dummy_order_config_1:
        id: '3b55b0b7-b2a3-448c-ac0c-7042ae759d03'
        tenant: '<getGermanyTenant()>'
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
        name: 'DE_10 german branch1-orders for ASK'
        tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_10)>'
    dummy_order_config_2:
        id: '73473536-191b-48b6-af43-c80e5bff4c01'
        tenant: '<getGermanyTenant()>'
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
        name: 'DE_11 german branch1-orders for ASK'
        tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_11)>'

App\Domain\Entity\DefaultTaskGroup:
    dummy_order_ask_de10_tg_1:
        tenant: '<getGermanyTenant()>'
        title: 'first tg in DE_10 ASK order'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        orderTypeConfig: '@dummy_order_config_1'
        sequenceNumber: 0
    dummy_order_ask_de10_tg_2:
        tenant: '<getGermanyTenant()>'
        title: 'second tg in DE_10 ASK order'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        orderTypeConfig: '@dummy_order_config_1'
        sequenceNumber: 0
    dummy_order_ask_de11_tg_1:
        tenant: '<getGermanyTenant()>'
        title: 'first tg in DE_11 ASK order'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        orderTypeConfig: '@dummy_order_config_2'
        sequenceNumber: 0
    dummy_order_ask_de11_tg_2:
        tenant: '<getGermanyTenant()>'
        title: 'second tg in DE_11 ASK order'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        orderTypeConfig: '@dummy_order_config_2'
        sequenceNumber: 0

App\Domain\Entity\DefaultTask:
    dummy_order_ask_de10_tg_1_t1:
        tenant: '<getGermanyTenant()>'
        externalId: 'ask-de10-tg1-t1'
        name: 'order ask de10 task 1'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@default_element_number_common_3'
    dummy_order_ask_de10_tg_2_t1:
        tenant: '<getGermanyTenant()>'
        externalId: 'ask-de10-tg2-t1'
        name: 'order ask de10 task 2'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@default_element_number_common_4'
    dummy_order_ask_de11_tg_1_t1:
        tenant: '<getGermanyTenant()>'
        externalId: 'ask-de11-tg1-t1'
        name: 'order ask de11 task 1'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@default_element_number_common_5'
    dummy_order_ask_de11_tg_2_t1:
        tenant: '<getGermanyTenant()>'
        externalId: 'ask-de11-tg2-t1'
        name: 'order ask de11 task 2'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@default_element_number_common_6'

App\Domain\Entity\DefaultTaskRelation:
    dummy_order_ask_de10_tr_1_1:
        tenant: '<getGermanyTenant()>'
        defaultTaskGroup: '@dummy_order_ask_de10_tg_1'
        defaultTask: '@dummy_order_ask_de10_tg_1_t1'
        sequenceNumber: 0
        optional: false
    dummy_order_ask_de10_tr_1_2:
        tenant: '<getGermanyTenant()>'
        defaultTaskGroup: '@dummy_order_ask_de10_tg_2'
        defaultTask: '@dummy_order_ask_de10_tg_2_t1'
        sequenceNumber: 0
        optional: false
    dummy_order_ask_de11_tr_1_1:
        tenant: '<getGermanyTenant()>'
        defaultTaskGroup: '@dummy_order_ask_de11_tg_1'
        defaultTask: '@dummy_order_ask_de11_tg_1_t1'
        sequenceNumber: 0
        optional: false
    dummy_order_ask_de11_tr_1_2:
        tenant: '<getGermanyTenant()>'
        defaultTaskGroup: '@dummy_order_ask_de11_tg_2'
        defaultTask: '@dummy_order_ask_de11_tg_2_t1'
        sequenceNumber: 0
        optional: false
