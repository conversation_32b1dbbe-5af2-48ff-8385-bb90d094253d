App\Domain\Entity\Branch:
  branch_1:
    tenant: '<getGermanyTenant()>'
    externalId: Oelbronn
    id: 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_1'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
  branch_2:
    tenant: '<getGermanyTenant()>'
    externalId: Nirgends
    id: '4f892b7f-3870-4e84-8ba2-e663f3b0f60b'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_1'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
  branch_7:
    tenant: '<getNetherlandsTenant()>'
    id: 'ac3ba014-1c19-453f-aca6-6962f3d18497'
    externalId: 'brnl71'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_71'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
  branch_9:
    tenant: '<getSpainTenant()>'
    id: '58ded041-e2d5-44f4-adcd-b7c165de1cb9'
    externalId: 'bres71'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_91'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
