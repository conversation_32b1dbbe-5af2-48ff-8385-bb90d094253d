App\Domain\Entity\AdditionalServiceConfig:
    sap_api_test_additional_service_config_1:
        tenant: '<getGermanyTenant()>'
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        defaultTask: '@sap_api_test_additional_service_task_1'
    sap_api_test_additional_service_config_2:
        tenant: '<getGermanyTenant()>'
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        defaultTask: '@sap_api_test_additional_service_task_2'

    sap_api_test_additional_service_config_3:
        tenant: '<getGermanyTenant()>'
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        branch: '@branch_1'
        defaultTask: '@sap_api_test_additional_service_task_3'
    sap_api_test_additional_service_config_4:
        tenant: '<getGermanyTenant()>'
        country: '<(App\Domain\Entity\Enum\Country::DE)>'
        branch: '@branch_1'
        defaultTask: '@sap_api_test_additional_service_task_4'

App\Domain\Entity\DefaultTask:
    sap_api_test_additional_service_task_1:
        tenant: '<getGermanyTenant()>'
        externalId: sap.api.test1
        name: 'sap-api-test-as1'
        type: 'additional_service_1'
        activatedBySapData: false
        elementItems:
            - '@default_element_additional_material_1'
            - '@default_element_additional_unit_1'
            - '@default_element_add_service_number_1'
    sap_api_test_additional_service_task_2:
        tenant: '<getGermanyTenant()>'
        externalId: sap.api.test2
        name: 'sap-api-test-as2'
        type: 'additional_service_2'
        activatedBySapData: false
        elementItems:
            - '@default_element_additional_material_2'
            - '@default_element_additional_unit_2'
            - '@default_element_add_service_string_1'

    sap_api_test_additional_service_task_3:
        tenant: '<getGermanyTenant()>'
        externalId: sap.api.test3
        name: 'sap-api-test-as3'
        type: 'additional_service_3'
        activatedBySapData: false
        elementItems:
            - '@default_element_additional_material_3'
            - '@default_element_additional_unit_3'
            - '@default_element_add_service_number_1'
    sap_api_test_additional_service_task_4:
        tenant: '<getGermanyTenant()>'
        externalId: sap.api.test4
        name: 'sap-api-test-as4'
        type: 'additional_service_4'
        activatedBySapData: false
        elementItems:
            - '@default_element_additional_material_4'
            - '@default_element_additional_unit_4'
            - '@default_element_add_service_string_1'

App\Domain\Entity\DefaultMaterialRelation:
    sap_api_test_additional_service_mat_rel_1:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_1'
        defaultMaterial: '@default_material_1'
        sequenceNumber: 0
    sap_api_test_additional_service_mat_rel_2:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_1'
        defaultMaterial: '@default_material_2'
        sequenceNumber: 1

    sap_api_test_additional_service_mat_rel_3:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_2'
        defaultMaterial: '@default_material_2'
        sequenceNumber: 0
    sap_api_test_additional_service_mat_rel_4:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_2'
        defaultMaterial: '@default_material_3'
        sequenceNumber: 1

    sap_api_test_additional_service_mat_rel_5:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_3'
        defaultMaterial: '@default_material_2'
        sequenceNumber: 0
    sap_api_test_additional_service_mat_rel_6:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_3'
        defaultMaterial: '@default_material_3'
        sequenceNumber: 1

    sap_api_test_additional_service_mat_rel_7:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_4'
        defaultMaterial: '@default_material_2'
        sequenceNumber: 0
    sap_api_test_additional_service_mat_rel_8:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_4'
        defaultMaterial: '@default_material_3'
        sequenceNumber: 1

App\Domain\Entity\DefaultUnitRelation:
    sap_api_test_additional_service_unit_rel_1:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_1'
        unit: '<(App\Domain\Entity\Enum\Unit::KG)>'
        sequenceNumber: 0
    sap_api_test_additional_service_unit_rel_2:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_1'
        unit: '<(App\Domain\Entity\Enum\Unit::M3)>'
        sequenceNumber: 1

    sap_api_test_additional_service_unit_rel_3:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_2'
        unit: '<(App\Domain\Entity\Enum\Unit::KG)>'
        sequenceNumber: 0
    sap_api_test_additional_service_unit_rel_4:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_2'
        unit: '<(App\Domain\Entity\Enum\Unit::STD)>'
        sequenceNumber: 1

    sap_api_test_additional_service_unit_rel_5:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_3'
        unit: '<(App\Domain\Entity\Enum\Unit::KG)>'
        sequenceNumber: 0
    sap_api_test_additional_service_unit_rel_6:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_3'
        unit: '<(App\Domain\Entity\Enum\Unit::STD)>'
        sequenceNumber: 1

    sap_api_test_additional_service_unit_rel_7:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_4'
        unit: '<(App\Domain\Entity\Enum\Unit::KG)>'
        sequenceNumber: 0
    sap_api_test_additional_service_unit_rel_8:
        tenant: '<getGermanyTenant()>'
        additionalServiceConfig: '@sap_api_test_additional_service_config_4'
        unit: '<(App\Domain\Entity\Enum\Unit::STD)>'
        sequenceNumber: 1

App\Domain\Entity\DefaultMaterial:
    default_material_1:
        tenant: '<getGermanyTenant()>'
        name: 'P&P'
        legalNumber: PP345
        materialType: '<(App\Domain\Entity\Enum\Types\MaterialType::WASTE)>'
        materialId: MP1
        id: '3a948b37-c51c-443e-a3dc-f1deb748e583'
        createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:10"))>'
    default_material_2:
        tenant: '<getGermanyTenant()>'
        name: PET
        legalNumber: PET75345
        materialType: '<(App\Domain\Entity\Enum\Types\MaterialType::CONTAINER)>'
        materialId: PE331
        id: '762364c7-e247-48d7-a082-823f3ef78f98'
        createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:10"))>'
    default_material_3:
        tenant: '<getGermanyTenant()>'
        name: GLAS
        legalNumber: GLAS75345
        materialType: '<(App\Domain\Entity\Enum\Types\MaterialType::CONTAINER)>'
        materialId: GL123
        id: 'e229012f-be24-4438-ab54-36b0ddb7e3ab'
        createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:10"))>'

App\Domain\Entity\ValueObject\Element:
    default_element_add_service_number_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.service
            label: number
            pattern: '\[0-9]+'
            description: 'input of number'
            referenceType: 'asd1'
            values: [ '42' ]
    default_element_add_service_string_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            placeholder: input.string.service
            label: string
            pattern: '\[0-9]+'
            description: 'input of strin'
            referenceType: 'asd2'
            values: [ 'test' ]
    default_element_additional_material_{1..4}:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: addservice.dd.mat
            label: input.addservice.mat
            description: 'test config dd input for addservice-mat'
            referenceType: 'asd1'
            dataSource: '<(App\Domain\Entity\Enum\ElementDataSource::MATERIAL)>'
    default_element_additional_unit_{1..4}:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: addservice.dd.unit
            label: input.addservice.unit
            description: 'test config dd input for addservice-unit'
            referenceType: 'asd1'
            dataSource: '<(App\Domain\Entity\Enum\ElementDataSource::UNIT)>'
