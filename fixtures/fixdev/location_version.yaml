App\Domain\Entity\LocationVersion:
  location_version_1:
    id: '9d2e187f-c09f-438d-b9c2-deabff8fa140'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    tenant: '<getGermanyTenant()>'
    location: '@location_3'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::BRANCH)>'
    street: Cust-Strasse
    houseNumber: '12'
    postalCode: '12345'
    city: Oelbronn
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.1
    longitude: 2.1
  location_version_2:
    tenant: '<getGermanyTenant()>'
    id: '0a2fba48-8a14-41ab-a685-88e529458e8d'
    location: '@location_1'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::BRANCH)>'
    street: Loc-Strasse
    houseNumber: '1'
    postalCode: '12345'
    city: Oelbronn
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.2
    longitude: 2.2
  location_version_3:
    tenant: '<getGermanyTenant()>'
    location: '@location_2'
    id: 'f3061e7d-2302-4097-b042-0e5b8a162564'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: Teststrasse
    houseNumber: '111'
    postalCode: '54321'
    city: 'Containerort Kunde'
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.3
    longitude: 2.3
  location_version_4:
    tenant: '<getGermanyTenant()>'
    id: '2f8ee428-429d-4041-9c6f-581931b1196d'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_4'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: 'An der Pforte'
    houseNumber: '2'
    postalCode: '32457'
    city: 'Porta Westfalica'
    district: NRW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 52.247821383703
    longitude: 8.9223155423295
  location_version_5:
    tenant: '<getGermanyTenant()>'
    id: '0c441fb3-970a-4f71-9baf-0b2d0257f848'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_5'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: 'Pohlsche Heide'
    houseNumber: '1'
    postalCode: '32479'
    city: Hille
    district: NRW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.4
    longitude: 2.4
  location_version_6:
    tenant: '<getGermanyTenant()>'
    id: 'c5e6d5af-6015-485e-abd9-d3429c00ba95'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_3'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: CLoc-Strasse
    houseNumber: '1'
    postalCode: '12345'
    city: Oelbronn
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.5
    longitude: 2.5
  location_version_7:
    tenant: '<getGermanyTenant()>'
    id: '7d520f56-084b-49db-85ba-dd0ade9fb82a'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_7'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: TG-Loc-Strasse
    houseNumber: '1'
    postalCode: '54321'
    city: TaskGroupingen
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.6
    longitude: 2.6
  location_version_8:
    tenant: '<getGermanyTenant()>'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_6'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: TG-Loc-Strasse
    houseNumber: '1'
    postalCode: '54321'
    city: TaskGroupingen
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.7
    longitude: 2.7
  location_version_71:
    tenant: '<getNetherlandsTenant()>'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_71'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: Teststreet
    houseNumber: '1'
    postalCode: '54321'
    city: TaskGroupingen
    district: T1
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.8
    longitude: 2.8
  location_version_91:
    tenant: '<getSpainTenant()>'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    location: '@location_91'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: Teststreet
    houseNumber: '3'
    postalCode: '87621'
    city: TaskGroupingen
    district: T1
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    latitude: 1.9
    longitude: 2.9
