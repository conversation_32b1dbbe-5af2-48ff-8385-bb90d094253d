App\Domain\Entity\DefaultTaskGroup:
    sap_api_test_tg_1: #8zn2f_DefTG_39:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Start (api-test)'
        sequenceNumber: '10'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getGermanyTenant()>'
        trackable: true
    sap_api_test_tg_2: #8zn2f_DefTG_40:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Kunde (api-test tg2)'
        sequenceNumber: '20'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_1_1' ]
        trackable: true
    sap_api_test_tg_3: #8zn2f_DefTG_41:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Entsorgungsanlage (api-test)'
        sequenceNumber: '30'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_2_1' ]
        trackable: true
    sap_api_test_tg_4: #8zn2f_DefTG_42:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Kunde (api-test)'
        sequenceNumber: '40'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_3_1' ]
        trackable: true
    sap_api_test_tg_5: #8zn2f_DefTG_43:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Containerlager (api-test)'
        sequenceNumber: '50'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_1_2' ]
        trackable: true
    sap_api_test_tg_6: #8zn2f_DefTG_44:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Kunde (api-test)'
        sequenceNumber: '60'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_1_3', '@sap_api_test_tgrule_5_1' ]
        trackable: true
    sap_api_test_tg_7: #8zn2f_DefTG_45:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Entsorgungsanlage (api-test)'
        sequenceNumber: '70'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_6_1' ]
        trackable: false
    sap_api_test_tg_8: #8zn2f_DefTG_46:
        orderTypeConfig: '@sap_api_test_order_config_1'
        title: 'Containerlager (api-test)'
        sequenceNumber: '80'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tgrule_7_1' ]

App\Domain\Entity\DefaultTaskRelation:
    sap_api_test_tr_1_1: #8zn2f_DefTaskRel_20:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_1'
        defaultTaskGroup: '@sap_api_test_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_2_1: #8zn2f_DefTaskRel_21:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_2'
        defaultTaskGroup: '@sap_api_test_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_2_2: #8zn2f_DefTaskRel_22:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_3'
        defaultTaskGroup: '@sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_trule_2_1' ]
    sap_api_test_tr_2_3: # 8zn2f_DefTaskRel_23:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_t_4'
        defaultTaskGroup: '@sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_trule_2_2' ]
    sap_api_test_tr_2_4: #8zn2f_DefTaskRel_24:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_t_5'
        defaultTaskGroup: '@sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_trule_2_3' ]
    sap_api_test_tr_2_5: #8zn2f_DefTaskRel_25:
        sequenceNumber: '50'
        defaultTask: '@sap_api_test_t_6'
        defaultTaskGroup: '@sap_api_test_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_trule_2_4' ]
    sap_api_test_tr_3_1: #8zn2f_DefTaskRel_26:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_14'
        defaultTaskGroup: '@sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_3_2: #8zn2f_DefTaskRel_27:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_15'
        defaultTaskGroup: '@sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_3_3: #8zn2f_DefTaskRel_28:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_t_20'
        defaultTaskGroup: '@sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_7_4:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_t_15_1'
        defaultTaskGroup: '@sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_7_5:
        sequenceNumber: '50'
        defaultTask: '@sap_api_test_t_15_2'
        defaultTaskGroup: '@sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_7_6:
        sequenceNumber: '60'
        defaultTask: '@sap_api_test_t_15_3'
        defaultTaskGroup: '@sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_4_1: #8zn2f_DefTaskRel_29:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_21'
        defaultTaskGroup: '@sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_4_2: #8zn2f_DefTaskRel_30:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_3'
        defaultTaskGroup: '@sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_4_3: #8zn2f_DefTaskRel_31:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_t_4'
        defaultTaskGroup: '@sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_4_4: #8zn2f_DefTaskRel_32:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_t_5'
        defaultTaskGroup: '@sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_4_5: #8zn2f_DefTaskRel_33:
        sequenceNumber: '50'
        defaultTask: '@sap_api_test_t_7'
        defaultTaskGroup: '@sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_4_6: #8zn2f_DefTaskRel_34:
        sequenceNumber: '60'
        defaultTask: '@sap_api_test_t_8'
        defaultTaskGroup: '@sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_5_1: #8zn2f_DefTaskRel_35:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_9'
        defaultTaskGroup: '@sap_api_test_tg_5'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_5_2: #8zn2f_DefTaskRel_36:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_10'
        defaultTaskGroup: '@sap_api_test_tg_5'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_6_1: #8zn2f_DefTaskRel_37:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_11'
        defaultTaskGroup: '@sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_6_2: #8zn2f_DefTaskRel_38:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_3'
        defaultTaskGroup: '@sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_6_3: #8zn2f_DefTaskRel_39:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_t_4'
        defaultTaskGroup: '@sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_6_4: #8zn2f_DefTaskRel_40:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_t_5'
        defaultTaskGroup: '@sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_6_5: #8zn2f_DefTaskRel_41:
        sequenceNumber: '50'
        defaultTask: '@sap_api_test_t_12'
        defaultTaskGroup: '@sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_6_6: #8zn2f_DefTaskRel_42:
        sequenceNumber: '60'
        defaultTask: '@sap_api_test_t_13'
        defaultTaskGroup: '@sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_7_1: #8zn2f_DefTaskRel_43:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_14'
        defaultTaskGroup: '@sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_7_2: #8zn2f_DefTaskRel_44:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_15'
        defaultTaskGroup: '@sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_7_3: #8zn2f_DefTaskRel_45:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_t_16'
        defaultTaskGroup: '@sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_8_1: #8zn2f_DefTaskRel_46:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_t_17'
        defaultTaskGroup: '@sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_8_2: #8zn2f_DefTaskRel_47:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_t_18'
        defaultTaskGroup: '@sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_8_3: #8zn2f_DefTaskRel_48:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_t_19'
        defaultTaskGroup: '@sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tr_8_4:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_t_22'
        defaultTaskGroup: '@sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultTask:
    sap_api_test_t_1: #8zn2f_DefTask_20:
        name: 'Anfahrtsauswahl (api-test)'
        type: 'start'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_1'
    sap_api_test_t_2: #8zn2f_DefTask_21
        name: 'Ankunft (api-test t2)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_2'
    sap_api_test_t_3: #8zn2f_DefTask_36:
        name: 'Containeranzahl (api-test t3)'
        type: 'container_count'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_3'
    sap_api_test_t_4: #8zn2f_DefTask_37:
        name: 'Kundenwiegung (api-test t4)'
        activatedBySapData: true
        type: 'customer_weight'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_4_1'
            - '@sap_api_test_e_5_1'
            - '@sap_api_test_e_6_1'
            - '@sap_api_test_e_7_1'
    sap_api_test_t_5: #8zn2f_DefTask_38:
        name: 'Unterschrift (api-test t5)'
        activatedBySapData: false
        type: 'deliveryNote_sig'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_8_1'
    sap_api_test_t_6: #8zn2f_DefTask_22:
        name: 'Fertig (api-test t6)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_9'
    sap_api_test_t_7: #8zn2f_DefTask_25:
        name: 'Containeranzahl (api-test)'
        type: 'container_count'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_10_1'
    sap_api_test_t_8: #8zn2f_DefTask_26:
        name: 'Fertig (api-test t8)'
        type: 'departure'
        repeatable: true
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_9_1'
    sap_api_test_t_9: #8zn2f_DefTask_27:
        name: 'Ankunft (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_2_1'
    sap_api_test_t_10: #8zn2f_DefTask_28:
        name: 'Fertig (api-test t10)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_13'
    sap_api_test_t_11: #8zn2f_DefTask_29:
        name: 'Ankunft (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_2_2'
    sap_api_test_t_12: #8zn2f_DefTask_30:
        name: 'Containeranzahl (api-test)'
        type: 'container_count'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_10_2'
    sap_api_test_t_13: #8zn2f_DefTask_31:
        name: 'Fertig (api-test t13)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_14'
    sap_api_test_t_14: #8zn2f_DefTask_39:
        name: 'Ankunft (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_2_3'
    sap_api_test_t_15: #8zn2f_DefTask_40:
        name: 'weighingnoteSentBySAP'
        activatedBySapData: true
        type: 'weighingnote'
        tenant: '<getGermanyTenant()>'
        externalId: 'pdfT1'
        elementItems:
            - '@sap_api_test_e_4_2'
            - '@sap_api_test_e_5_2'
            - '@sap_api_test_e_6_2'
            - '@sap_api_test_e_7_2'
            - '@sap_api_test_e_8_2'
            - '@sap_api_test_e_12_1'
    sap_api_test_t_15_1:
        name: 'scale1WeighingSentBySAP'
        activatedBySapData: false
        type: 'weighingnote2'
        tenant: '<getGermanyTenant()>'
        externalId: 'pdfT2'
        elementItems:
            - '@sap_api_test_e_12_2'
    sap_api_test_t_15_2:
        name: 'scale2WeighingSentBySAP'
        activatedBySapData: false
        type: 'weighingnote2'
        tenant: '<getGermanyTenant()>'
        externalId: 'pdfT3'
        elementItems:
            - '@sap_api_test_e_12_3'
        taskActions:
            - '@sap_api_test_DefTaskAction_1'
    sap_api_test_t_15_3:
        name: 'skipme'
        activatedBySapData: false
        type: 'skiptask'
        tenant: '<getGermanyTenant()>'
        externalId: 'skip'
        elementItems:
            - '@sap_api_test_e_2_3'
    sap_api_test_t_16: #8zn2f_DefTask_32:
        name: 'Fertig (api-test t16)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_11'
    sap_api_test_t_17: #8zn2f_DefTask_33:
        name: 'Ankunft (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_2_4'
    sap_api_test_t_18: #8zn2f_DefTask_34:
        name: 'Containerscan (api-test)'
        type: 'container_scan'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_10_3'
    sap_api_test_t_19: #8zn2f_DefTask_35:
        name: 'Fertig (api-test t19)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_9_2'
    sap_api_test_t_20: #8zn2f_DefTask_23:
        name: 'Fertig (api-test t20)'
        type: 'departure'
        sapAction: true
        repeatable: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_15'
    sap_api_test_t_21: #8zn2f_DefTask_24:
        name: 'Ankunft (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_2_5'
    sap_api_test_t_22:
        name: 'Taskgroup (de-iot-test)'
        type: 'iot_task'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_e_16'
            - '@sap_api_test_e_17'

App\Domain\Entity\ValueObject\TaskAction:
    sap_api_test_DefTaskAction_1:
        __construct: { sequence: 10, trigger: '<(App\Domain\Entity\Enum\TaskActionTrigger::ON_TASK_BOTTOM_SHEET_OPEN)>', callback: '<(App\Domain\Entity\Enum\TaskActionCallback::WEIGHING_START)>', id: 68d798d1-583b-41f2-a9da-ddd7a8a3c15b }


App\Domain\Entity\ValueObject\Element:
    sap_api_test_e_1: #8zn2f_DefElem_11:
        __construct:
            id: '52c4e9b6-a8c5-4b32-a669-bfa426ba9a71'
            referenceType: 'taskgroupPicker'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'Wie soll der Auftrag gestartet werden? (api-test)'
            label: 'Wie soll der Auftrag gestartet werden? (api-test)'
            patternHint: 'Auswahl des Starts'
            descriptionFile: '0be52c99-1c24-4f1a-bf61-4432702664b4'
            descriptionFileType: '<(App\Domain\Entity\Enum\Types\DescriptionFileType::PDF)>'
            descriptionFileName: 'my test pdf'
            options:
                - '@sap_api_test_eo_1_1'
                - '@sap_api_test_eo_1_2'
                - '@sap_api_test_eo_1_3'
    sap_api_test_e_2: #8zn2f_DefElem_2:
        __construct:
            id: 'ea8ea766-7c72-41b6-8c4f-4d8da1c2bf0b'
            referenceType: 'arrival'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Angekommen? (api-test)'
            label: 'Ankunft (api-test)'
            patternHint: 'Ankunft'
    sap_api_test_e_2_{1..5}:
        __construct:
            referenceType: 'arrival'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Angekommen? (api-test)'
            label: 'Ankunft (api-test)'
            patternHint: 'Ankunft'
    sap_api_test_e_3: #8zn2f_DefElem_6:
        __construct:
            referenceType: 'containerAmount'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Containeranzahl erfassen (api-test)'
            label: 'Bitte Containeranzahl erfassen (api-test)'
            patternHint: 'Containeranzahl'
    sap_api_test_e_4_{1..2}: #8zn2f_DefElem_7:
        __construct:
            referenceType: 'weighingNoteNumber'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            placeholder: 'Bitte Wiegescheinnummer erfassen (api-test)'
            label: 'Bitte Wiegescheinnummer erfassen (api-test)'
            patternHint: 'Wiegescheinnummer'
            hasAutocomplete: true
            checksum: '42'
            checksumFormula: '<(App\Domain\Entity\Enum\ChecksumFormula::CODE_43)>'
            options:
                - '@sap_api_test_eo_11_3'
    sap_api_test_e_5_{1..2}:
        __construct:
            referenceType: 'unit'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'In welcher Einheit soll das Gewicht erfasst werden? (api-test)'
            label: 'In welcher Einheit soll das Gewicht erfasst werden? (api-test)'
            patternHint: 'Einheit'
            options:
                - '@sap_api_test_eo_5_1'
                - '@sap_api_test_eo_5_2'
    sap_api_test_e_6_{1..2}: #8zn2f_DefElem_9:
        __construct:
            referenceType: 'containerAmount'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Gewicht erfassen (api-test)'
            label: 'Bitte Anzahl erfassen (api-test)'
            patternHint: 'Menge'
    sap_api_test_e_7_{1..2}: #8zn2f_DefElem_10:
        __construct:
            referenceType: 'weighingNoteQR'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: 'Bitte Wiegeschein-QR-Code erfassen (api-test)'
            label: 'Bitte Wiegeschein-QR-Code erfassen (api-test)'
            patternHint: 'QR-Code'
    sap_api_test_e_8_{1..2}: #8zn2f_DefElem_4:
        __construct:
            referenceType: 'signature'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>'
            placeholder: 'Bitte Unterschrift einholen (api-test)'
            label: 'Bitte Unterschrift einholen (api-test)'
            patternHint: 'Unterschrift'
    sap_api_test_e_9: #8zn2f_DefElem_3:
        __construct:
            id: '42bbc532-19d1-41fc-9a6b-31d1cdc14320'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (api-test)'
            label: 'Fertig (api-test)'
            patternHint: 'Fertig'
    sap_api_test_e_9_{1..2}:
        __construct:
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (api-test)'
            label: 'Fertig (api-test)'
            patternHint: 'Fertig'
    sap_api_test_e_10_{1..3}: #8zn2f_DefElem_5:
        __construct:
            referenceType: 'containerScan'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: 'Bitte Container-QR-Code scannen (api-test)'
            label: 'Bitte Container-QR-Code scannen (api-test)'
            patternHint: 'QR-Code'
    sap_api_test_e_11: #8zn2f_DefElem_22:
        __construct:
            id: '66200403-4ef2-4a38-bb49-8b1e47d8d90c'
            referenceType: 'continue'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'Wie soll der Auftrag fortgeführt werden? (api-test)'
            label: 'Wie soll der Auftrag fortgeführt werden? (api-test)'
            patternHint: 'Fortsetzen'
            options:
                - '@sap_api_test_eo_11_1'
                - '@sap_api_test_eo_11_2'
    sap_api_test_e_12_{1..3}:
        __construct:
            referenceType: 'scale_weighing_data'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>'
            placeholder: 'wiegedaten (api-test)'
            label: 'Bitte Container wiegen (api-test)'
    sap_api_test_e_13:
        __construct:
            id: 'aa4b3bc7-bf77-4714-9d4f-54b462602f02'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (api-test)'
            label: 'Fertig (api-test)'
            patternHint: 'Fertig'
    sap_api_test_e_14:
        __construct:
            id: 'ee808191-939d-41b4-b79d-72a736cf4e4e'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (api-test)'
            label: 'Fertig (api-test)'
            patternHint: 'Fertig'
    sap_api_test_e_15:
        __construct:
            id: 'da31d7f9-72cf-41d2-923d-f3fc7288666e'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (api-test)'
            label: 'Fertig (api-test)'
            patternHint: 'Fertig'
    sap_api_test_e_16:
        __construct:
            referenceType: 'number'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Unterschrift einholen (de-iot-test)'
            label: 'testnumber'
            patternHint: 'Unterschrift'
    sap_api_test_e_17:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            label: 'de-iot'
            referenceType: 'vc-qr'
            containerAction: 'place'

App\Domain\Entity\ValueObject\ElementOption:
    sap_api_test_eo_1_1:
        __construct:
            name: 'Direkt zum Kunden (leeren) (api-test)'
            sequenceNumber: 10
            id: '02aad4c9-3052-4202-aaf5-fc2ee15b5ec3'
    sap_api_test_eo_1_2:
        __construct:
            name: 'Container aus Lager holen (tauschen) (api-test)'
            sequenceNumber: 20
            id: '164f98cd-1cb8-46bb-b983-bd69b04bf2e8'
    sap_api_test_eo_1_3:
        __construct:
            name: 'Direkt zum Kunden (tauschen) (api-test)'
            sequenceNumber: 30
            id: 'a93eb46d-1c7a-47de-a0cc-87c613faa5e4'
    sap_api_test_eo_5_1:
        __construct:
            name: 'KG (api-test)'
            sequenceNumber: 40
    sap_api_test_eo_5_2:
        __construct:
            name: 'TO (api-test)'
            sequenceNumber: 50
    sap_api_test_eo_11_1:
        __construct:
            name: 'Container im Lager abstellen (api-test)'
            sequenceNumber: 60
            id: '4e62400f-5a17-4051-9ec9-22cbcd3cb785'
    sap_api_test_eo_11_2:
        __construct:
            name: 'Auftrag beenden (api-test)'
            sequenceNumber: 70
    sap_api_test_eo_11_3:
        __construct:
            name: 'Auto 1'
            sequenceNumber: 80

App\Domain\Entity\ValueObject\Rule:
    sap_api_test_tgrule_1_1:
        __construct:
            elementId: '52c4e9b6-a8c5-4b32-a669-bfa426ba9a71'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '02aad4c9-3052-4202-aaf5-fc2ee15b5ec3'
    sap_api_test_tgrule_1_2:
        __construct:
            elementId: '52c4e9b6-a8c5-4b32-a669-bfa426ba9a71'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '164f98cd-1cb8-46bb-b983-bd69b04bf2e8'
    sap_api_test_tgrule_1_3:
        __construct:
            elementId: '52c4e9b6-a8c5-4b32-a669-bfa426ba9a71'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'a93eb46d-1c7a-47de-a0cc-87c613faa5e4'
    sap_api_test_tgrule_2_1:
        __construct:
            elementId: '42bbc532-19d1-41fc-9a6b-31d1cdc14320'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_tgrule_3_1:
        __construct:
            elementId: 'da31d7f9-72cf-41d2-923d-f3fc7288666e'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_tgrule_5_1:
        __construct:
            elementId: 'aa4b3bc7-bf77-4714-9d4f-54b462602f02'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_tgrule_6_1:
        __construct:
            elementId: 'ee808191-939d-41b4-b79d-72a736cf4e4e'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_tgrule_7_1:
        __construct:
            elementId: '66200403-4ef2-4a38-bb49-8b1e47d8d90c'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '4e62400f-5a17-4051-9ec9-22cbcd3cb785'

    sap_api_test_trule_2_1:
        __construct:
            elementId: 'ea8ea766-7c72-41b6-8c4f-4d8da1c2bf0b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_trule_2_2:
        __construct:
            elementId: 'ea8ea766-7c72-41b6-8c4f-4d8da1c2bf0b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_trule_2_3:
        __construct:
            elementId: 'ea8ea766-7c72-41b6-8c4f-4d8da1c2bf0b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    sap_api_test_trule_2_4:
        __construct:
            elementId: 'ea8ea766-7c72-41b6-8c4f-4d8da1c2bf0b'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
