App\Domain\Entity\DefaultTermination:
    sap_api_test_default_order_termination_1:
        tenant: '<getGermanyTenant()>'
        text: 'first order-termination from config'
        externalId: 'def-order-ter-1'

App\Domain\Entity\DefaultTerminationRelation:
    sap_api_test__default_order_termination_rl_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        defaultTermination: '@sap_api_test_default_order_termination_1'
        orderTypeConfig: '@sap_api_test_order_config_1'

App\Domain\Entity\DefaultTaskGroup:
    sap_api_test_default_order_termination_tg_1:
        tenant: '<getGermanyTenant()>'
        title: 'first tg in first order-termination'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        defaultTermination: '@sap_api_test_default_order_termination_1'
        sequenceNumber: 0
    sap_api_test_default_order_termination_tg_2:
        tenant: '<getGermanyTenant()>'
        title: 'second tg in first order-termination'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        defaultTermination: '@sap_api_test_default_order_termination_1'
        sequenceNumber: 1

App\Domain\Entity\DefaultTask:
    sap_api_test_default_order_termination_t_12_1:
        tenant: '<getGermanyTenant()>'
        externalId: 'o-ter1-tg12-t1'
        name: 'termination task 1 (shared)'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@sap_api_test_default_order_termination_element_number_common'
            - '@sap_api_test_default_order_termination_element_photo_common'
    sap_api_test_default_order_termination_t_1_2:
        tenant: '<getGermanyTenant()>'
        externalId: 'o-ter1-tg1-t2'
        name: 'termination task 2'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@sap_api_test_default_order_termination_element_accept_1'
    sap_api_test_default_order_termination_t_2_2:
        tenant: '<getGermanyTenant()>'
        externalId: 'o-ter1-tg2-t2'
        name: 'termination task 3'
        type: '<word()>'
        activatedBySapData: false
        repeatable: false
        elementItems:
            - '@sap_api_test_default_order_termination_element_accept_2'

App\Domain\Entity\DefaultTaskRelation:
    sap_api_test_default_order_termination_trl_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@sap_api_test_default_order_termination_tg_1'
        defaultTask: '@sap_api_test_default_order_termination_t_12_1'
    sap_api_test_default_order_termination_trl_2:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        optional: false
        defaultTaskGroup: '@sap_api_test_default_order_termination_tg_1'
        defaultTask: '@sap_api_test_default_order_termination_t_1_2'
    sap_api_test_default_order_termination_trl_3:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@sap_api_test_default_order_termination_tg_2'
        defaultTask: '@sap_api_test_default_order_termination_t_12_1'
    sap_api_test_default_order_termination_trl_4:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        optional: false
        defaultTaskGroup: '@sap_api_test_default_order_termination_tg_2'
        defaultTask: '@sap_api_test_default_order_termination_t_2_2'

App\Domain\Entity\ValueObject\Element:
    sap_api_test_default_order_termination_element_number_common:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.common
            label: number
            pattern: '\[0-9]+'
            description: 'input of order-termination number'
            referenceType: 'o-t-1'
    sap_api_test_default_order_termination_element_photo_common:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>'
            placeholder: input.photo.common
            label: photo
            pattern: '\[0-9]+'
            description: 'input of order-termination photo'
            referenceType: 'o-p-1'
    sap_api_test_default_order_termination_element_accept_{1..2}:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: input.accept.ph
            label: input.accept.label
            description: 'Test accept order-termination element'
            referenceType: 'o-t-2'
