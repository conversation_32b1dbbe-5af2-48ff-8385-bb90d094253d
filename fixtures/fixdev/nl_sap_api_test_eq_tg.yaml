App\Domain\Entity\EquipmentConfig:
    nl_EquipmentConf_1:
        country: '<(App\Domain\Entity\Enum\Country::NL)>'
        tenant: '<getNetherlandsTenant()>'

App\Domain\Entity\DefaultTaskGroup:
    equipment_nl_eq_tg_1:
        tenant: '<getNetherlandsTenant()>'
        id: '1483f170-7f9b-4593-ac63-8548ffb1e285'
        title: nl-eq-tg_1
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::VEHICLECHECK)>'
        sequenceNumber: 10
        toBeDone: '<(App\Domain\Entity\Enum\TaskGroupToBeDone::PRE)>'
        equipmentConfig: '@nl_EquipmentConf_1'

App\Domain\Entity\DefaultTask:
    equipment_nl_eq_t_1:
        tenant: '<getNetherlandsTenant()>'
        id: '154d21aa-721c-43e2-a584-95e1be36626f'
        name: equipment.nl.task
        type: '<word()>'
        activatedBySapData: false
        externalId: NlEquTgTask1
        elementItems:
            - '@default_element_nl_equ_tg_1'

App\Domain\Entity\DefaultTaskRelation:
    default_nl_equ_dtg_tr_1:
        tenant: '<getNetherlandsTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@equipment_nl_eq_tg_1'
        defaultTask: '@equipment_nl_eq_t_1'

App\Domain\Entity\ValueObject\Element:
    default_element_nl_equ_tg_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            label: 'eq_tg_description'
            referenceType: 'eq-tg'
