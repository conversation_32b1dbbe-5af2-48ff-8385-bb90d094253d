App\Domain\Entity\TourDataConfig:
  sap_api_test_tour_config_1:
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tenant: '<getGermanyTenant()>'
    additionalInformationItems: ['@tour_default_additional_information_1']
  sap_api_test_tour_config_2:
      country: '<(App\Domain\Entity\Enum\Country::LU)>'
      tenant: '<getGermanyTenant()>'
      additionalInformationItems: []

App\Domain\Entity\OrderTypeConfig:
  sap_api_test_order_config_1: # from 8zn2f_OrderConf_3
    name: 'Container tauschen/leeren (api-test)'
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_12)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
    tenant: '<getGermanyTenant()>'
    additionalInformationItems: ['@order_default_additional_information_1']
  sap_api_test_order_config_2: # from 8zn2f_OrderConf_3
      name: 'Container tauschen/leeren (api-test-lu)'
      country: '<(App\Domain\Entity\Enum\Country::LU)>'
      tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::DE_12)>'
      tenant: '<getGermanyTenant()>'
      additionalInformationItems: []

App\Domain\Entity\DefaultInterruption:
    tour_interruption_1:
        tenant: '<getGermanyTenant()>'
        id: '6c012a87-6e60-4efe-9607-b8addb370140'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
        description: 'tour break interruption'
        externalId: '<regexify("[a-z][a-z-]{1,30}[a-z]")>'

App\Domain\Entity\DefaultInterruptionRelation:
    tour_interruption_relation_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 8
        tourDataConfig: '@sap_api_test_tour_config_1'
        defaultInterruption: '@tour_interruption_1'
