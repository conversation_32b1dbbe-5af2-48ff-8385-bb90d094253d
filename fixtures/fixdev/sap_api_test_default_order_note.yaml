App\Domain\Entity\DefaultNote:
    sap_api_test_order_default_note_1:
        externalId: 'tg-note-1'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultNoteRelation:
    sap_api_test_order_default_note_rel_1:
        sequenceNumber: '100'
        orderTypeConfig: '@sap_api_test_order_config_1'
        defaultNote: '@sap_api_test_order_default_note_1'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultTaskGroup:
    sap_api_test_order_default_note_tg_1:
        defaultNote: '@sap_api_test_order_default_note_1'
        title: 'N-TG-Ruletest-1 (api-test)'
        sequenceNumber: '10'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_order_default_note_tg_2:
        defaultNote: '@sap_api_test_order_default_note_1'
        title: 'N-TG-Ruletest-2 (api-test)'
        sequenceNumber: '20'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_order_default_note_tgrule_1' ]

App\Domain\Entity\DefaultTask:
    sap_api_test_order_default_note_t_1_1:
        name: 'Init task for n-rules (api-test)'
        type: 'start'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_order_default_note_el_1_1'
    sap_api_test_order_default_note_t_1_2:
        name: 'Choose for n-taskgrouprule (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_order_default_note_el_1_2'
    sap_api_test_order_default_note_t_1_3:
        name: 'Choose for n-taskrule (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_order_default_note_el_1_3'
    sap_api_test_order_default_note_t_1_4:
        name: 'Optional only by n-taskrule (api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_order_default_note_el_1_4'
    sap_api_test_order_default_note_t_2_1:
        name: 'home of n-tg-rule (api-test)'
        type: 'start'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_order_default_note_el_2_1'

App\Domain\Entity\DefaultTaskRelation:
    sap_api_test_order_default_note_trel_1_1:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_order_default_note_t_1_1'
        defaultTaskGroup: '@sap_api_test_order_default_note_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_order_default_note_trel_1_2:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_order_default_note_t_1_2'
        defaultTaskGroup: '@sap_api_test_order_default_note_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_order_default_note_trel_1_3:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_order_default_note_t_1_3'
        defaultTaskGroup: '@sap_api_test_order_default_note_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_order_default_note_trel_1_4:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_order_default_note_t_1_4'
        defaultTaskGroup: '@sap_api_test_order_default_note_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_order_default_note_trule_1' ]
    sap_api_test_order_default_note_trel_2_1:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_order_default_note_t_2_1'
        defaultTaskGroup: '@sap_api_test_order_default_note_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\ValueObject\Element:
    sap_api_test_order_default_note_el_1_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'start n-rule-test now? (api-test)'
            label: 'Start n-test (api-test)'
            patternHint: 'test'
            referenceType: 'asd1'
    sap_api_test_order_default_note_el_1_2:
        __construct:
            id: '1b193c08-ebf7-4c10-8af8-8e7c514550ee'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'should the next n-taskgroup be shown? (api-test)'
            label: 'should the next taskgroup be shown? (api-test)'
            patternHint: 'select visibility'
            referenceType: 'asd1'
            options:
                - '@sap_api_test_order_default_note_elopt_1_1'
                - '@sap_api_test_order_default_note_elopt_1_2'
    sap_api_test_order_default_note_el_1_3:
        __construct:
            id: 'bda93786-f83d-4e3e-9f0c-eab0e873df68'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'should the next n-task be shown? (api-test)'
            label: 'should the task be shown? (api-test)'
            patternHint: 'select visibility'
            referenceType: 'asd1'
            options:
                - '@sap_api_test_order_default_note_elopt_2_1'
                - '@sap_api_test_order_default_note_elopt_2_2'
    sap_api_test_order_default_note_el_1_4:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'i am shown by n-taskrule (api-test)'
            label: 'enter a number (api-test)'
            patternHint: 'number'
            referenceType: 'asd1'
            hasAutocomplete: true
            options:
                - '@sap_api_test_order_default_note_elopt_3_autocomplete_1'
                - '@sap_api_test_order_default_note_elopt_3_autocomplete_2'
    sap_api_test_order_default_note_el_2_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'i am shown by n-taskgrouprule (api-test)'
            label: 'enter a number (api-test)'
            patternHint: 'number'
            referenceType: 'asd1'

App\Domain\Entity\ValueObject\ElementOption:
    sap_api_test_order_default_note_elopt_1_1:
        __construct:
            name: 'yes, show the n-taskgroup (api-test)'
            sequenceNumber: 10
            id: 'b088c6ba-8aa6-4919-aa90-01e3a781837f'
    sap_api_test_order_default_note_elopt_1_2:
        __construct:
            name: 'no, n-taskgroup should stay hidden (api-test)'
            sequenceNumber: 20
            id: '7a76f2aa-f55f-4291-a828-1b6dbfbcaab9'
    sap_api_test_order_default_note_elopt_2_1:
        __construct:
            name: 'yes, show the n-task (api-test)'
            sequenceNumber: 30
            id: '1a41926e-8e7f-4e8c-81c2-6b304fb1660e'
    sap_api_test_order_default_note_elopt_2_2:
        __construct:
            name: 'no, n-task should stay hidden (api-test)'
            sequenceNumber: 40
            id: '083b7ed8-1b65-4e39-9dc3-cf3bfcac170f'
    sap_api_test_order_default_note_elopt_3_autocomplete_1:
        __construct:
            name: '123'
            sequenceNumber: 20
            id: 'a85125bb-8333-4921-b800-5104bb97602b'
    sap_api_test_order_default_note_elopt_3_autocomplete_2:
        __construct:
            name: '987'
            sequenceNumber: 10
            id: 'ec3eb551-3858-4c29-b9d0-47a7b6464e84'

App\Domain\Entity\ValueObject\Rule:
    sap_api_test_order_default_note_tgrule_1:
        __construct:
            elementId: '1b193c08-ebf7-4c10-8af8-8e7c514550ee'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'b088c6ba-8aa6-4919-aa90-01e3a781837f'

    sap_api_test_order_default_note_trule_1:
        __construct:
            elementId: 'bda93786-f83d-4e3e-9f0c-eab0e873df68'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1a41926e-8e7f-4e8c-81c2-6b304fb1660e'
