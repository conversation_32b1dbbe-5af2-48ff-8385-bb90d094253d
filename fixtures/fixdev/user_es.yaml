App\Domain\Entity\User:
    es_user_api_1:
        __construct:
            tenant: '<getSpainTenant()>'
            username: es-user-1
            firstname: ESApi
            lastname: User1
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@es_staff_user_api_1'
    es_user_api_2:
        __construct:
            tenant: '<getSpainTenant()>'
            username: es-user-2
            firstname: ESApi
            lastname: User2
        email: <EMAIL>
        status: 1
        createdAt: '<(new \DateTimeImmutable())>'
        modifiedAt: '<(new \DateTimeImmutable())>'
        staff: '@es_staff_user_api_2'
    user_portal_admin_es:
        __construct:
            tenant: '<getSpainTenant()>'
            username: portal-admin-es
            firstname: 'portal-admin'
            lastname: 'es'
        id: '78982a04-2c92-48f4-bb89-549d65c17076'
        email: '<EMAIL>'
        country: es
        countryAccess: [ '<(App\Domain\Entity\Enum\COUNTRY::ES)>' ]
        roles: [ "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_MANAGE_COUNTRY_ADMINS", "ROLE_PORTAL_MANAGE_SUPPORT_USERS", "ROLE_PORTAL_MANAGE_PORTAL_USERS", "ROLE_PORTAL_MANAGE_DEVICE_ACCESS", "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_VIEW_COUNTRY_DATA", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_MANAGE_FAQ", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_ACCESS", "ROLE_PORTAL_MANAGE_FAQ_ADMINS" ]
    user_portal_es:
        __construct:
            tenant: '<getSpainTenant()>'
            username: portal-user-es
            firstname: 'portal-user'
            lastname: 'es'
        id: 'e5744398-383c-41b1-9a57-c3e2834b3739'
        email: '<EMAIL>'
        country: 'es'
        branchAccess: [ '58ded041-e2d5-44f4-adcd-b7c165de1cb9' ]
        roles: [ "ROLE_PORTAL_VIEW_FILES", "ROLE_PORTAL_DEVICE_MESSAGE_ACCESS", "ROLE_PORTAL_CHANGE_PROFILE", "ROLE_PORTAL_VIEW_DASHBOARD", "ROLE_PORTAL_VIEW_STAFF", "ROLE_PORTAL_VIEW_EQUIPMENT", "ROLE_PORTAL_VIEW_BRANCH_DATA", "ROLE_PORTAL_ACCESS" ]
