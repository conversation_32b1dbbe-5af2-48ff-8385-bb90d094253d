App\Domain\Entity\TourDataConfig:
  nl_sap_api_test_tour_config_1:
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
    tenant: '<getNetherlandsTenant()>'

App\Domain\Entity\OrderTypeConfig:
  nl_sap_api_test_order_config_1:
    name: 'container change (nl-api-test)'
    country: '<(App\Domain\Entity\Enum\Country::NL)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::NL_50)>'
    tenant: '<getNetherlandsTenant()>'

App\Domain\Entity\DefaultInterruption:
    nl_tour_interruption_1:
        tenant: '<getNetherlandsTenant()>'
        id: 'e6041035-6d0f-4789-8eb2-134d9aaa6b80'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
        description: 'tour-interruption-1-break'
        externalId: 'interruption-fixed-external-id-1'

App\Domain\Entity\DefaultInterruptionRelation:
    nl_tour_interruption_relation_1:
        tenant: '<getNetherlandsTenant()>'
        sequenceNumber: 8
        tourDataConfig: '@nl_sap_api_test_tour_config_1'
        defaultInterruption: '@nl_tour_interruption_1'

App\Domain\Entity\DefaultTaskGroup:
    nl_tour_interruption_tg_1:
        tenant: '<getNetherlandsTenant()>'
        title: tour-interruption taskgroup1
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::INTERRUPTION)>'
        sequenceNumber: 0
        defaultInterruption: '@nl_tour_interruption_1'

App\Domain\Entity\DefaultTaskRelation:
    nl_tour_interruption_tr_1:
        tenant: '<getNetherlandsTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@nl_tour_interruption_tg_1'
        defaultTask: '@nl_tour_interruption_tg1_t1'

App\Domain\Entity\DefaultTask:
    nl_tour_interruption_tg1_t1:
        tenant: '<getNetherlandsTenant()>'
        name: tour.interruption.tg1.task1
        type: 'interruption-type-1'
        activatedBySapData: false
        externalId: TourInterruptionTg1Task1
        elementItems:
            - '@nl_tour_interruption_element_photo'

App\Domain\Entity\ValueObject\Element:
    nl_tour_interruption_element_photo:
        __construct:
            id: 'ea439c5f-5b9f-44c2-b5c9-76e4522c2cb1'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::PHOTO)>'
            placeholder: input.photo.ph
            label: input.photo.label
            description: 'Test photo element'
            referenceType: 'asd1'
