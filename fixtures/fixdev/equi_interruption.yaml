App\Domain\Entity\DefaultInterruption:
    equipment_interruption_1:
        tenant: '<getGermanyTenant()>'
        id: '98e07c3c-241f-42ff-aba8-639af94f1c1e'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::REFUEL)>'
        description: 'equi refuel interruption'
        externalId: '<regexify("[a-z][a-z-]{1,30}[a-z]")>'
        additionalInformationItems: [ '@default_interruption_default_additional_information_1' ]
    equipment_interruption_2:
        tenant: '<getGermanyTenant()>'
        id: '2b823f0b-4783-42da-a719-10e10f4e4dd3'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::TRAFFICJAM)>'
        description: 'equi jam interruption'
        externalId: '<regexify("[a-z][a-z-]{1,30}[a-z]")>'
        additionalInformationItems: [ ]
    equipment_interruption_3:
        tenant: '<getGermanyTenant()>'
        id: '801a1a6f-097d-44a1-9967-92f066408c98'
        type: '<(App\Domain\Entity\Enum\Types\InterruptionType::BREAK)>'
        description: 'equi jam break'
        externalId: '<regexify("[a-z][a-z-]{1,30}[a-z]")>'
        additionalInformationItems: [ ]
App\Domain\Entity\DefaultInterruptionRelation:
    equipment_interruption_relation_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 10
        equipmentConfig: '@8zn2f_EquipmentConf_1'
        defaultInterruption: '@equipment_interruption_1'
    equipment_interruption_relation_2:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 1
        equipmentConfig: '@8zn2f_EquipmentConf_1'
        defaultInterruption: '@equipment_interruption_2'
    equipment_interruption_relation_3:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 20
        equipmentConfig: '@8zn2f_EquipmentConf_1'
        defaultInterruption: '@equipment_interruption_3'
App\Domain\Entity\DefaultTaskGroup:
    equipment_interruption_tg_1:
        tenant: '<getGermanyTenant()>'
        id: 'a775aadc-795f-4e66-986c-528d41a1c661'
        title: 'equipment-interruption taskgroup1'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        sequenceNumber: 0
        defaultInterruption: '@equipment_interruption_1'
App\Domain\Entity\DefaultTask:
    equipment_interruption_t_1:
        tenant: '<getGermanyTenant()>'
        id: 'abdc024d-3331-4e80-a87a-2cf9b345228b'
        name: equipmentConfig.interruption.task1
        type: '<word()>'
        activatedBySapData: false
        externalId: EquIntTask1
        elementItems:
            - '@default_element_number_litres2'
App\Domain\Entity\DefaultTaskRelation:
    equipment_interruption_tr_1:
        tenant: '<getGermanyTenant()>'
        sequenceNumber: 0
        optional: false
        defaultTaskGroup: '@equipment_interruption_tg_1'
        defaultTask: '@equipment_interruption_t_1'
