App\Domain\Entity\Faq:
    faq_1:
        iso: 'de_DE'
        tenant: '<getGermanyTenant()>'
        question: 'What is the meaning of life?'
        answer: '42'
        sequence: 0
    faq_2:
        iso: 'de_DE'
        tenant: '<getGermanyTenant()>'
        question: 'How do I use the equipment?'
        answer: 'With your hands'
        sequence: 1
    faq_3:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a forklift?'
        answer: 'A forklift is a vehicle used to move heavy objects'
        sequence: 0
    faq_4:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a car?'
        answer: 'A car is a vehicle used to move people'
        sequence: 1
    faq_5:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a truck?'
        answer: 'A truck is a vehicle used to move heavy objects'
        sequence: 2
    faq_6:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a bus?'
        answer: 'A bus is a vehicle used to move people'
        sequence: 3
    faq_7:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a train?'
        answer: 'A train is a vehicle used to move people and heavy objects'
        sequence: 4
    faq_8:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a plane?'
        answer: 'A plane is a vehicle used to move people'
        sequence: 5
    faq_9:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a boat?'
        answer: 'A boat is a vehicle used to move people and heavy objects'
        sequence: 6
    faq_10:
        iso: 'en_GB'
        tenant: '<getGermanyTenant()>'
        question: 'What is a bike?'
        answer: 'A bike is a vehicle used to move people'
        sequence: 7
