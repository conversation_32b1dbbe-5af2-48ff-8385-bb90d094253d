App\Domain\VehicleInspection\Config\VehicleInspectionConfig:
    vehicle_inspection_config1:
        __construct:
            country: '<(App\Domain\Entity\Enum\Country::DE)>'
            equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
            equipmentComponentGroups: ['@equipment_component_group1']
            automaticCompletionEnabled: true
        tenant: 'pz'
    vehicle_inspection_config2:
        __construct:
            country: '<(App\Domain\Entity\Enum\Country::DE)>'
            equipmentComponentGroups: [ '@equipment_component_group2' ]
            automaticCompletionEnabled: true
        tenant: 'pz'
    vehicle_inspection_config3:
        __construct:
            country: '<(App\Domain\Entity\Enum\Country::ES)>'
            equipmentComponentGroups: [ '@equipment_component_group3' ]
            automaticCompletionEnabled: true
        tenant: 'pze'

App\Domain\VehicleInspection\Config\EquipmentComponentGroupConfig:
    equipment_component_group1:
        __construct:
            title: 'Exterior'
            components: ['@equipment_component1', '@equipment_component2']
            automaticCompletionEnabled: true
    equipment_component_group2:
        __construct:
            title: 'Exterior-common'
            components: [ '@equipment_component3', '@equipment_component4' ]
            automaticCompletionEnabled: true
    equipment_component_group3:
        __construct:
            title: 'Exterior-es'
            components: [ '@equipment_component5', '@equipment_component6' ]
            automaticCompletionEnabled: true

App\Domain\VehicleInspection\Config\EquipmentComponentConfig:
    equipment_component1:
        __construct:
            title: 'Headlight'
            label: 'Is the light working?'
            critical: false
            externalId: '01-headlight'
            valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>'

    equipment_component2:
        __construct:
            title: 'Tyres'
            label: 'Are the tyres not flat?'
            critical: true
            externalId: '02-tyres'
            valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>'

    equipment_component3:
        __construct:
            title: 'common Headlight'
            label: 'Is the light working?'
            critical: false
            externalId: '03-headlight'
            valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>'

    equipment_component4:
        __construct:
            title: 'common Tyres'
            label: 'Are the tyres not flat?'
            critical: true
            externalId: '04-tyres'
            valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>'

    equipment_component5:
        __construct:
            title: 'es Headlight'
            label: 'Is the light working?'
            critical: false
            externalId: '05-headlight'
            valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>'

    equipment_component6:
        __construct:
            title: 'es Tyres'
            label: 'Are the tyres not flat?'
            critical: true
            externalId: '06-tyres'
            valueType: '<(App\Domain\VehicleInspection\ReportValueType::WORKING_STATE)>'
