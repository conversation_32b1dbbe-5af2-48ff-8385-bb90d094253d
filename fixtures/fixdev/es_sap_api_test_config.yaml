App\Domain\Entity\TourDataConfig:
  es_sap_api_test_tour_config_1:
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    equipmentType: '<(App\Domain\Entity\Enum\Types\EquipmentType::ASK)>'
    tenant: '<getSpainTenant()>'

App\Domain\Entity\OrderTypeConfig:
  es_sap_api_test_order_config_1:
    name: 'container change (es-api-test)'
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    tourOrderType: '<(App\Domain\Entity\Enum\Types\TourOrderType::ES_50)>'
    tenant: '<getSpainTenant()>'
