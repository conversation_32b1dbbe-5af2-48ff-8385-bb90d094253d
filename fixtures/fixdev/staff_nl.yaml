App\Domain\Entity\Staff:
  nl_staff_user_api_1:
    tenant: '<getNetherlandsTenant()>'
    externalId: '1111NL'
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::IDLE)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: NlApi
    lastname: User1
    branch: '@branch_7'
    search: 'NlApi User1 1111NL'
  nl_staff_user_api_2:
    tenant: '<getNetherlandsTenant()>'
    externalId: EXTSTAFFAPIUSERNL2
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::IDLE)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: NlApi
    lastname: User2
    branch: '@branch_7'
    search: 'NlApi User2 EXTSTAFFAPIUSERNL2'

App\Domain\Entity\StaffVersion:
  nl_staff_1_version_1:
    tenant: '<getNetherlandsTenant()>'
    staff: '@nl_staff_user_api_1'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: NlApi
    lastname: User1
    branch: '@branch_7'
  nl_staff_2_version_1:
    tenant: '<getNetherlandsTenant()>'
    staff: '@nl_staff_user_api_2'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: NlApi
    lastname: User2
    branch: '@branch_7'
