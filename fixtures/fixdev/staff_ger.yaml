App\Domain\Entity\Staff:
  staff_user_4:
    tenant: '<getGermanyTenant()>'
    externalId: EXTSTAFFUSER4
    id: '84ec9a14-f951-41ef-b8ce-65ae511eb2df'
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::WORKING)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DISPATCHER)>'
    firstname: Max
    lastname: Mustermann
    branch: '@branch_1'
    search: 'Max Mustermann EXTSTAFFUSER4'
  staff_user_5:
    tenant: '<getGermanyTenant()>'
    externalId: EXTSTAFFUSER5
    id: '0ca4a34b-d63d-42b1-93ba-f326a84e8194'
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::WORKING)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: Maria
    lastname: Musterfrau
    branch: '@branch_1'
    search: 'Maria Musterfrau EXTSTAFFUSER5'
  staff_user_api_1:
    id: 'd021d9da-c238-4367-9bfd-63398435a974'
    tenant: '<getGermanyTenant()>'
    externalId: EXTSTAFFAPIUSER1
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: 'Francis'
    lastname: 'Rooks'
    branch: '@branch_1'
    search: 'Francis Rooks EXTSTAFFAPIUSER1'
    user: '@user_api_1'
  staff_user_api_2:
    tenant: '<getGermanyTenant()>'
    externalId: EXTSTAFFAPIUSER2
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::WORKING)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: 'Eric'
    lastname: 'Felan'
    branch: '@branch_1'
    search: 'Eric Felan EXTSTAFFAPIUSER2'
  staff_user_api_3:
    tenant: '<getGermanyTenant()>'
    externalId: EXTSTAFFAPIUSER3
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::IDLE)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: 'Darrel'
    lastname: 'Bullock'
    branch: '@branch_1'
    search: 'Darrel Bullock EXTSTAFFAPIUSER3'
  staff_user_api_4:
    tenant: '<getGermanyTenant()>'
    externalId: EXTSTAFFAPIUSER4
    status: '<(App\Domain\Entity\Enum\Status\StaffStatus::IDLE)>'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: 'Charles'
    lastname: 'Young'
    branch: '@branch_1'
    search: 'Charles Young EXTSTAFFAPIUSER4'

App\Domain\Entity\StaffVersion:
  staff_1_version_1:
    tenant: '<getGermanyTenant()>'
    staff: '@staff_user_4'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DISPATCHER)>'
    firstname: Max
    lastname: Mustermann
    branch: '@branch_1'
  staff_2_version_1:
    tenant: '<getGermanyTenant()>'
    staff: '@staff_user_5'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: Maria
    lastname: Musterfrau
    branch: '@branch_1'
  staff_3_version_1:
    tenant: '<getGermanyTenant()>'
    staff: '@staff_user_api_1'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: '<firstName()>'
    lastname: '<lastName()>'
    branch: '@branch_1'
  staff_4_version_1:
    tenant: '<getGermanyTenant()>'
    staff: '@staff_user_api_2'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: '<firstName()>'
    lastname: '<lastName()>'
    branch: '@branch_1'
  staff_5_version_1:
    tenant: '<getGermanyTenant()>'
    staff: '@staff_user_api_3'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: '<firstName()>'
    lastname: '<lastName()>'
    branch: '@branch_1'
  staff_6_version_1:
    tenant: '<getGermanyTenant()>'
    staff: '@staff_user_api_4'
    personnelType: '<(App\Domain\Entity\Enum\Types\PersonnelType::DRIVER)>'
    firstname: '<firstName()>'
    lastname: '<lastName()>'
    branch: '@branch_1'
