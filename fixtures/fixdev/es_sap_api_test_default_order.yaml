App\Domain\Entity\DefaultTaskGroup:
    es_sap_api_test_tg_1:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Start (es-api-test)'
        sequenceNumber: '10'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tg_2:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Kunde (es-api-test tg2)'
        sequenceNumber: '20'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_1_1' ]
    es_sap_api_test_tg_3:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Entsorgungsanlage (es-api-test)'
        sequenceNumber: '30'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_2_1' ]
    es_sap_api_test_tg_4:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Kunde (es-api-test)'
        sequenceNumber: '40'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_3_1' ]
    es_sap_api_test_tg_5:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Containerlager (es-api-test)'
        sequenceNumber: '50'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_1_2' ]
    es_sap_api_test_tg_6:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Kunde (es-api-test)'
        sequenceNumber: '60'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CUSTOMER)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_1_3', '@es_sap_api_test_tgrule_5_1' ]
    es_sap_api_test_tg_7:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Entsorgungsanlage (es-api-test)'
        sequenceNumber: '70'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DISPOSALSITE)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_6_1' ]
    es_sap_api_test_tg_8:
        orderTypeConfig: '@es_sap_api_test_order_config_1'
        title: 'Containerlager (es-api-test)'
        sequenceNumber: '80'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::DEPOT)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_tgrule_7_1' ]

App\Domain\Entity\DefaultTaskRelation:
    es_sap_api_test_tr_1_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_1'
        defaultTaskGroup: '@es_sap_api_test_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_2_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_2'
        defaultTaskGroup: '@es_sap_api_test_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_2_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_3'
        defaultTaskGroup: '@es_sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_trule_2_1' ]
    es_sap_api_test_tr_2_3:
        sequenceNumber: '30'
        defaultTask: '@es_sap_api_test_t_4'
        defaultTaskGroup: '@es_sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_trule_2_2' ]
    es_sap_api_test_tr_2_4:
        sequenceNumber: '40'
        defaultTask: '@es_sap_api_test_t_5'
        defaultTaskGroup: '@es_sap_api_test_tg_2'
        optional: true
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_trule_2_3' ]
    es_sap_api_test_tr_2_5:
        sequenceNumber: '50'
        defaultTask: '@es_sap_api_test_t_6'
        defaultTaskGroup: '@es_sap_api_test_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
        rules: [ '@es_sap_api_test_trule_2_4' ]
    es_sap_api_test_tr_3_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_14'
        defaultTaskGroup: '@es_sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_3_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_15'
        defaultTaskGroup: '@es_sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_3_3:
        sequenceNumber: '30'
        defaultTask: '@es_sap_api_test_t_20'
        defaultTaskGroup: '@es_sap_api_test_tg_3'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_4_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_21'
        defaultTaskGroup: '@es_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_4_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_3'
        defaultTaskGroup: '@es_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_4_3:
        sequenceNumber: '30'
        defaultTask: '@es_sap_api_test_t_4'
        defaultTaskGroup: '@es_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_4_4:
        sequenceNumber: '40'
        defaultTask: '@es_sap_api_test_t_5'
        defaultTaskGroup: '@es_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_4_5:
        sequenceNumber: '50'
        defaultTask: '@es_sap_api_test_t_7'
        defaultTaskGroup: '@es_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_4_6:
        sequenceNumber: '60'
        defaultTask: '@es_sap_api_test_t_8'
        defaultTaskGroup: '@es_sap_api_test_tg_4'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_5_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_9'
        defaultTaskGroup: '@es_sap_api_test_tg_5'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_5_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_10'
        defaultTaskGroup: '@es_sap_api_test_tg_5'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_6_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_11'
        defaultTaskGroup: '@es_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_6_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_3'
        defaultTaskGroup: '@es_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_6_3:
        sequenceNumber: '30'
        defaultTask: '@es_sap_api_test_t_4'
        defaultTaskGroup: '@es_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_6_4:
        sequenceNumber: '40'
        defaultTask: '@es_sap_api_test_t_5'
        defaultTaskGroup: '@es_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_6_5:
        sequenceNumber: '50'
        defaultTask: '@es_sap_api_test_t_12'
        defaultTaskGroup: '@es_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_6_6:
        sequenceNumber: '60'
        defaultTask: '@es_sap_api_test_t_13'
        defaultTaskGroup: '@es_sap_api_test_tg_6'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_7_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_14'
        defaultTaskGroup: '@es_sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_7_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_15'
        defaultTaskGroup: '@es_sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_7_3:
        sequenceNumber: '30'
        defaultTask: '@es_sap_api_test_t_16'
        defaultTaskGroup: '@es_sap_api_test_tg_7'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_8_1:
        sequenceNumber: '10'
        defaultTask: '@es_sap_api_test_t_17'
        defaultTaskGroup: '@es_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_8_2:
        sequenceNumber: '20'
        defaultTask: '@es_sap_api_test_t_18'
        defaultTaskGroup: '@es_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_8_3:
        sequenceNumber: '30'
        defaultTask: '@es_sap_api_test_t_19'
        defaultTaskGroup: '@es_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'
    es_sap_api_test_tr_8_4:
        sequenceNumber: '40'
        defaultTask: '@es_sap_api_test_t_22'
        defaultTaskGroup: '@es_sap_api_test_tg_8'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::OR)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getSpainTenant()>'

App\Domain\Entity\DefaultTask:
    es_sap_api_test_t_1:
        name: 'Anfahrtsauswahl (es-api-test)'
        type: 'start'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_1'
    es_sap_api_test_t_2:
        name: 'Ankunft (es-api-test t2)'
        type: 'arrival'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_2_1'
    es_sap_api_test_t_3:
        name: 'Containeranzahl (es-api-test t3)'
        type: 'container_count'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_3'
    es_sap_api_test_t_4:
        name: 'Kundenwiegung (es-api-test t4)'
        activatedBySapData: true
        type: 'customer_weight'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_4_1'
            - '@es_sap_api_test_e_5_1'
            - '@es_sap_api_test_e_6_1'
            - '@es_sap_api_test_e_7_1'
    es_sap_api_test_t_5:
        name: 'Unterschrift (es-api-test t5)'
        activatedBySapData: true
        type: 'deliveryNote_sig'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_8'
    es_sap_api_test_t_6:
        name: 'Fertig (es-api-test t6)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_9'
    es_sap_api_test_t_7:
        name: 'Containeranzahl (es-api-test)'
        type: 'container_count'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_10_1'
    es_sap_api_test_t_8:
        name: 'Fertig (es-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_9_4'
    es_sap_api_test_t_9:
        name: 'Ankunft (es-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_2_2'
    es_sap_api_test_t_10:
        name: 'Fertig (es-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_9_1'
    es_sap_api_test_t_11:
        name: 'Ankunft (es-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_2_3'
    es_sap_api_test_t_12:
        name: 'Containeranzahl (es-api-test)'
        type: 'container_count'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_10_2'
    es_sap_api_test_t_13:
        name: 'Fertig (es-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_9_2'
    es_sap_api_test_t_14:
        name: 'Ankunft (es-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_2_4'
    es_sap_api_test_t_15:
        name: 'Wiegedaten (es-api-test)'
        activatedBySapData: true
        type: 'weighingnote'
        tenant: '<getSpainTenant()>'
        externalId: 'pdfT1'
        elementItems:
            - '@es_sap_api_test_e_4_2'
            - '@es_sap_api_test_e_5_2'
            - '@es_sap_api_test_e_6_2'
            - '@es_sap_api_test_e_7_2'
    es_sap_api_test_t_16:
        name: 'Fertig (es-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_11'
    es_sap_api_test_t_17:
        name: 'Ankunft (es-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_2_5'
    es_sap_api_test_t_18:
        name: 'Containerscan (es-api-test)'
        type: 'container_scan'
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_10_3'
    es_sap_api_test_t_19:
        name: 'Fertig (es-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_9_5'
    es_sap_api_test_t_20:
        name: 'Fertig (es-api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_9_3'
    es_sap_api_test_t_21:
        name: 'Ankunft (es-api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_2_6'
    es_sap_api_test_t_22:
        name: 'Iot Task (es-iot-test)'
        type: 'iot_task'
        sapAction: true
        tenant: '<getSpainTenant()>'
        elementItems:
            - '@es_sap_api_test_e_12'

App\Domain\Entity\ValueObject\Element:
    es_sap_api_test_e_1:
        __construct:
            id: 'ceeb6b62-f404-40ca-b268-34d3d34962ce'
            referenceType: 'taskgroupPicker'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'Wie soll der Auftrag gestartet werden? (es-api-test)'
            label: 'Wie soll der Auftrag gestartet werden? (es-api-test)'
            patternHint: 'Auswahl des Starts'
            options:
                - '@es_sap_api_test_eo_1_1'
                - '@es_sap_api_test_eo_1_2'
                - '@es_sap_api_test_eo_1_3'
    es_sap_api_test_e_2_1:
        __construct:
            id: 'fe567c90-b0ab-4ed3-958c-8bd1d6109b05'
            referenceType: 'arrival'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Angekommen? (es-api-test)'
            label: 'Ankunft (es-api-test)'
            patternHint: 'Ankunft'
    es_sap_api_test_e_2_{2..6}:
        __construct:
            referenceType: 'arrival'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Angekommen? (es-api-test)'
            label: 'Ankunft (es-api-test)'
            patternHint: 'Ankunft'
    es_sap_api_test_e_3:
        __construct:
            referenceType: 'containerAmount'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Containeranzahl erfassen (es-api-test)'
            label: 'Bitte Containeranzahl erfassen (es-api-test)'
            patternHint: 'Containeranzahl'
    es_sap_api_test_e_4_{1..2}:
        __construct:
            referenceType: 'weighingNoteNumber'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            placeholder: 'Bitte Wiegescheinnummer erfassen (es-api-test)'
            label: 'Bitte Wiegescheinnummer erfassen (es-api-test)'
            patternHint: 'Wiegescheinnummer'
    es_sap_api_test_e_5_{1..2}:
        __construct:
            referenceType: 'unit'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'In welcher Einheit soll das Gewicht erfasst werden? (es-api-test)'
            label: 'In welcher Einheit soll das Gewicht erfasst werden? (es-api-test)'
            patternHint: 'Einheit'
            options:
                - '@es_sap_api_test_eo_5_1'
                - '@es_sap_api_test_eo_5_2'
    es_sap_api_test_e_6_{1..2}:
        __construct:
            referenceType: 'value'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'Bitte Gewicht erfassen (es-api-test)'
            label: 'Bitte Gewicht erfassen (es-api-test)'
            patternHint: 'Menge'
    es_sap_api_test_e_7_{1..2}:
        __construct:
            referenceType: 'weighingNoteQR'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: 'Bitte Wiegeschein-QR-Code erfassen (es-api-test)'
            label: 'Bitte Wiegeschein-QR-Code erfassen (es-api-test)'
            patternHint: 'QR-Code'
    es_sap_api_test_e_8:
        __construct:
            referenceType: 'signature'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SIGNATURE)>'
            placeholder: 'Bitte Unterschrift einholen (es-api-test)'
            label: 'Bitte Unterschrift einholen (es-api-test)'
            patternHint: 'Unterschrift'
    es_sap_api_test_e_9:
        __construct:
            id: '6283ff0a-471f-4bc4-ae9e-f56fb3b3be49'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (es-api-test)'
            label: 'Fertig (es-api-test)'
            patternHint: 'Fertig'
    es_sap_api_test_e_9_1:
        __construct:
            id: 'b8365856-ec31-4099-96a7-e534201e1a32'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (es-api-test)'
            label: 'Fertig (es-api-test)'
            patternHint: 'Fertig'
    es_sap_api_test_e_9_2:
        __construct:
            id: '04196953-9adc-419f-a6a3-48cabd59542c'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (es-api-test)'
            label: 'Fertig (es-api-test)'
            patternHint: 'Fertig'
    es_sap_api_test_e_9_3:
        __construct:
            id: '7624b3b3-84d0-4671-adf6-7a6ec187a318'
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (es-api-test)'
            label: 'Fertig (es-api-test)'
            patternHint: 'Fertig'
    es_sap_api_test_e_9_{4..5}:
        __construct:
            referenceType: 'departure'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'Fertig? (es-api-test)'
            label: 'Fertig (es-api-test)'
            patternHint: 'Fertig'
    es_sap_api_test_e_10_{1..3}:
        __construct:
            referenceType: 'containerScan'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            placeholder: 'Bitte Container-QR-Code scannen (es-api-test)'
            label: 'Bitte Container-QR-Code scannen (es-api-test)'
            patternHint: 'QR-Code'
    es_sap_api_test_e_11:
        __construct:
            id: '8e092b89-f84f-4f37-ae22-5d57393b3b2f'
            referenceType: 'continue'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'Wie soll der Auftrag fortgeführt werden? (es-api-test)'
            label: 'Wie soll der Auftrag fortgeführt werden? (es-api-test)'
            patternHint: 'Fortsetzen'
            options:
                - '@es_sap_api_test_eo_11_1'
                - '@es_sap_api_test_eo_11_2'
    es_sap_api_test_e_12:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::QR)>'
            label: 'es-iot'
            referenceType: 'vc-qr'
            containerAction: 'place'

App\Domain\Entity\ValueObject\ElementOption:
    es_sap_api_test_eo_1_1:
        __construct:
            name: 'Direkt zum Kunden (leeren) (es-api-test)'
            sequenceNumber: 10
            id: 'ed541d2a-a192-4153-aa63-3ffa5ffe6040'
    es_sap_api_test_eo_1_2:
        __construct:
            name: 'Container aus Lager holen (tauschen) (es-api-test)'
            sequenceNumber: 20
            id: 'e65c851e-e91f-46e9-ba9a-d2afccae3c0f'
    es_sap_api_test_eo_1_3:
        __construct:
            name: 'Direkt zum Kunden (tauschen) (es-api-test)'
            sequenceNumber: 30
            id: '6df77f57-90ea-49e3-ab19-df4404836c38'
    es_sap_api_test_eo_5_1:
        __construct:
            name: 'KG (es-api-test)'
            sequenceNumber: 40
            id: 'd62b6bde-1467-44b9-9d34-13ac713007dd'
    es_sap_api_test_eo_5_2:
        __construct:
            name: 'TO (es-api-test)'
            sequenceNumber: 50
            id: '52b87ffa-9ac5-485f-8eee-a6cfdd74952f'
    es_sap_api_test_eo_11_1:
        __construct:
            name: 'Container im Lager abstellen (es-api-test)'
            sequenceNumber: 60
            id: '67b6744e-55eb-4ab7-91c7-d6d4f5809252'
    es_sap_api_test_eo_11_2:
        __construct:
            name: 'Auftrag beenden (es-api-test)'
            sequenceNumber: 70
            id: 'fb3b9611-defe-4312-b7b6-ff87ad6431cf'

App\Domain\Entity\ValueObject\Rule:
    es_sap_api_test_tgrule_1_1:
        __construct:
            elementId: 'ceeb6b62-f404-40ca-b268-34d3d34962ce'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'ed541d2a-a192-4153-aa63-3ffa5ffe6040'
    es_sap_api_test_tgrule_1_2:
        __construct:
            elementId: 'ceeb6b62-f404-40ca-b268-34d3d34962ce'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'e65c851e-e91f-46e9-ba9a-d2afccae3c0f'
    es_sap_api_test_tgrule_1_3:
        __construct:
            elementId: 'ceeb6b62-f404-40ca-b268-34d3d34962ce'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '6df77f57-90ea-49e3-ab19-df4404836c38'
    es_sap_api_test_tgrule_2_1:
        __construct:
            elementId: '6283ff0a-471f-4bc4-ae9e-f56fb3b3be49'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_tgrule_3_1:
        __construct:
            elementId: '7624b3b3-84d0-4671-adf6-7a6ec187a318'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_tgrule_5_1:
        __construct:
            elementId: 'b8365856-ec31-4099-96a7-e534201e1a32'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_tgrule_6_1:
        __construct:
            elementId: '04196953-9adc-419f-a6a3-48cabd59542c'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_tgrule_7_1:
        __construct:
            elementId: '8e092b89-f84f-4f37-ae22-5d57393b3b2f'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '67b6744e-55eb-4ab7-91c7-d6d4f5809252'

    es_sap_api_test_trule_2_1:
        __construct:
            elementId: 'fe567c90-b0ab-4ed3-958c-8bd1d6109b05'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_trule_2_2:
        __construct:
            elementId: 'fe567c90-b0ab-4ed3-958c-8bd1d6109b05'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_trule_2_3:
        __construct:
            elementId: 'fe567c90-b0ab-4ed3-958c-8bd1d6109b05'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
    es_sap_api_test_trule_2_4:
        __construct:
            elementId: 'fe567c90-b0ab-4ed3-958c-8bd1d6109b05'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '1'
