App\Domain\Entity\DefaultNote:
  dummy_default_order_note_1:
    tenant: '<getGermanyTenant()>'
    externalId: 'def-order-note-1'

App\Domain\Entity\DefaultNoteRelation:
  dummy_default_order_note_rl_1:
    tenant: '<getGermanyTenant()>'
    sequenceNumber: 0
    defaultNote: '@dummy_default_order_note_1'
    orderTypeConfig: '@dummy_order_config_1'

App\Domain\Entity\DefaultTaskGroup:
  dummy_default_order_note_tg_1:
    tenant: '<getGermanyTenant()>'
    title: 'first tg in first order-note'
    type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
    defaultNote: '@dummy_default_order_note_1'
    sequenceNumber: 0

App\Domain\Entity\DefaultTask:
  dummy_default_order_tnote_t_1_1:
    tenant: '<getGermanyTenant()>'
    externalId: 'o-note1-tg1-t1'
    name: 'note task 1'
    type: '<word()>'
    activatedBySapData: false
    repeatable: false
    elementItems:
        - '@default_element_number_common_7'

App\Domain\Entity\DefaultTaskRelation:
  dummy_default_order_note_trl_1:
    tenant: '<getGermanyTenant()>'
    sequenceNumber: 0
    optional: false
    defaultTaskGroup: '@dummy_default_order_note_tg_1'
    defaultTask: '@dummy_default_order_tnote_t_1_1'
