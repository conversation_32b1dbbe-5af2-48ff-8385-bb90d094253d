App\Domain\Entity\ValueObject\Element:
    default_element_string:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            valueStringMinLength: 3
            valueStringMaxLength: 50
            placeholder: input.text.ph
            label: input.text.label
            description: 'test config text input'
            referenceType: 'asd1'
    default_element_string2:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            valueStringMinLength: 3
            valueStringMaxLength: 50
            placeholder: input.text.ph
            label: input.text.label
            description: 'test config text input'
            referenceType: 'asd1'
    default_element_string3:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::STRING)>'
            valueStringMinLength: 3
            valueStringMaxLength: 50
            placeholder: input.text.ph
            label: input.text.label
            description: 'test config text input'
            referenceType: 'asd1'
    default_element_number_litres:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.ph
            label: 'Menge (l)'
            pattern: '\[0-9]+'
            description: 'input of litres'
            referenceType: 'asd1'
    default_element_number_litres2:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.ph
            label: 'Menge (l)'
            pattern: '\[0-9]+'
            description: 'input of litres'
            referenceType: 'asd1'
    default_element_number_litres3:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.ph
            label: 'Menge (l)'
            pattern: '\[0-9]+'
            description: 'input of litres'
            referenceType: 'asd1'
    default_element_number_common_{1..9}:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: input.number.common
            label: number
            pattern: '\[0-9]+'
            description: 'input of number'
            referenceType: 'asd1'
            values: [ '42' ]
    default_element_oil_dropdown_{1..2}:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: input.dd.equipment.oiltype
            label: input.dd.oil
            description: 'test config dd input for equipment-oil'
            referenceType: 'asd1'
            options:
                - '@default_element_option_3'
                - '@default_element_option_4'









