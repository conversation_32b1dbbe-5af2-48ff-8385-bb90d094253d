App\Domain\Entity\DefaultTermination:
    sap_api_test_tour_default_termination_1:
        text: '{{Customer_not_on_site}}'
        externalId: 'tg-term-1'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultTerminationRelation:
    sap_api_test_tour_default_termination_ter_rel_1:
        sequenceNumber: '100'
        tourDataConfig: '@sap_api_test_tour_config_1'
        defaultTermination: '@sap_api_test_tour_default_termination_1'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\DefaultTaskGroup:
    sap_api_test_tour_default_termination_tg_1:
        defaultTermination: '@sap_api_test_tour_default_termination_1'
        title: 'T-TG-Ruletest-1 (api-test)'
        sequenceNumber: '10'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tour_default_termination_tg_2:
        defaulttermination: '@sap_api_test_tour_default_termination_1'
        title: 'T-TG-Ruletest-2 (api-test)'
        sequenceNumber: '20'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::INVISIBLE)>'
        type: '<(App\Domain\Entity\Enum\Types\TaskGroupType::CHOICE)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tour_default_termination_tgrule_1' ]

App\Domain\Entity\DefaultTask:
    sap_api_test_tour_default_termination_t_1_1:
        name: 'Init task for t-rules (api-test)'
        type: 'start'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_tour_default_termination_el_1_1'
    sap_api_test_tour_default_termination_t_1_2:
        name: 'Choose for t-taskgrouprule (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_tour_default_termination_el_1_2'
    sap_api_test_tour_default_termination_t_1_3:
        name: 'Choose for t-taskrule (api-test)'
        type: 'arrival'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_tour_default_termination_el_1_3'
    sap_api_test_tour_default_termination_t_1_4:
        name: 'Optional only by t-taskrule (api-test)'
        type: 'departure'
        sapAction: true
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_tour_default_termination_el_1_4'
    sap_api_test_tour_default_termination_t_2_1:
        name: 'home of t-tg-rule (api-test)'
        type: 'start'
        tenant: '<getGermanyTenant()>'
        elementItems:
            - '@sap_api_test_tour_default_termination_el_2_1'
            - '@sap_api_test_tour_default_termination_el_2_2'

App\Domain\Entity\DefaultTaskRelation:
    sap_api_test_tour_default_termination_trel_1_1:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_tour_default_termination_t_1_1'
        defaultTaskGroup: '@sap_api_test_tour_default_termination_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tour_default_termination_trel_1_2:
        sequenceNumber: '20'
        defaultTask: '@sap_api_test_tour_default_termination_t_1_2'
        defaultTaskGroup: '@sap_api_test_tour_default_termination_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tour_default_termination_trel_1_3:
        sequenceNumber: '30'
        defaultTask: '@sap_api_test_tour_default_termination_t_1_3'
        defaultTaskGroup: '@sap_api_test_tour_default_termination_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
    sap_api_test_tour_default_termination_trel_1_4:
        sequenceNumber: '40'
        defaultTask: '@sap_api_test_tour_default_termination_t_1_4'
        defaultTaskGroup: '@sap_api_test_tour_default_termination_tg_1'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'
        rules: [ '@sap_api_test_tour_default_termination_trule_1' ]
    sap_api_test_tour_default_termination_trel_2_1:
        sequenceNumber: '10'
        defaultTask: '@sap_api_test_tour_default_termination_t_2_1'
        defaultTaskGroup: '@sap_api_test_tour_default_termination_tg_2'
        ruleLogic: '<(App\Domain\Entity\Enum\RuleLogic::AND)>'
        ruleEffect: '<(App\Domain\Entity\Enum\RuleEffect::DISABLED)>'
        tenant: '<getGermanyTenant()>'

App\Domain\Entity\ValueObject\Element:
    sap_api_test_tour_default_termination_el_1_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::ACCEPT)>'
            placeholder: 'start t-rule-test now? (api-test)'
            label: 'Start t-test (api-test)'
            patternHint: 'test'
            referenceType: 'asd1'
    sap_api_test_tour_default_termination_el_1_2:
        __construct:
            id: 'c333b873-31b1-431e-80e8-e2cd4d94ec25'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'should the next t-taskgroup be shown? (api-test)'
            label: 'should the next taskgroup be shown? (api-test)'
            patternHint: 'select visibility'
            referenceType: 'asd1'
            options:
                - '@sap_api_test_tour_default_termination_elopt_1_1'
                - '@sap_api_test_tour_default_termination_elopt_1_2'
    sap_api_test_tour_default_termination_el_1_3:
        __construct:
            id: '0bb69a45-64df-4664-82b9-83aadaef0fbd'
            type: '<(App\Domain\Entity\Enum\Types\ElementType::SELECT)>'
            placeholder: 'should the next t-task be shown? (api-test)'
            label: 'should the task be shown? (api-test)'
            patternHint: 'select visibility'
            referenceType: 'asd1'
            options:
                - '@sap_api_test_tour_default_termination_elopt_2_1'
                - '@sap_api_test_tour_default_termination_elopt_2_2'
    sap_api_test_tour_default_termination_el_1_4:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'i am shown by t-taskrule (api-test)'
            label: 'enter a number (api-test)'
            patternHint: 'number'
            referenceType: 'asd1'
    sap_api_test_tour_default_termination_el_2_1:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::NUMBER)>'
            placeholder: 'i am shown by t-taskgrouprule (api-test)'
            label: 'enter a number (api-test)'
            patternHint: 'number'
            referenceType: 'asd1'
    sap_api_test_tour_default_termination_el_2_2:
        __construct:
            type: '<(App\Domain\Entity\Enum\Types\ElementType::WEIGHING_DATA)>'
            placeholder: 'i am a scale for termination (api-test)'
            label: 'scale the termination'
            referenceType: 'weighing_data'

App\Domain\Entity\ValueObject\ElementOption:
    sap_api_test_tour_default_termination_elopt_1_1:
        __construct:
            name: 'yes, show the t-taskgroup (api-test)'
            sequenceNumber: 10
            id: 'fd0ba2ee-b595-458b-9b8a-d9c9aa0cd422'
    sap_api_test_tour_default_termination_elopt_1_2:
        __construct:
            name: 'no, t-taskgroup should stay hidden (api-test)'
            sequenceNumber: 20
            id: 'd52d8e0d-6cf6-4428-83f2-928dbdf48bc7'
    sap_api_test_tour_default_termination_elopt_2_1:
        __construct:
            name: 'yes, show the t-task (api-test)'
            sequenceNumber: 30
            id: '693d18b8-8ea5-4e63-b85a-2521b045c99f'
    sap_api_test_tour_default_termination_elopt_2_2:
        __construct:
            name: 'no, t-task should stay hidden (api-test)'
            sequenceNumber: 40
            id: '480291a0-99c5-4162-aaf3-6fe9361c82dd'

App\Domain\Entity\ValueObject\Rule:
    sap_api_test_tour_default_termination_tgrule_1:
        __construct:
            elementId: 'c333b873-31b1-431e-80e8-e2cd4d94ec25'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: 'fd0ba2ee-b595-458b-9b8a-d9c9aa0cd422'

    sap_api_test_tour_default_termination_trule_1:
        __construct:
            elementId: '0bb69a45-64df-4664-82b9-83aadaef0fbd'
            operator: '<(App\Domain\Entity\Enum\RuleOperator::NOT_EQUAL)>'
            value: '693d18b8-8ea5-4e63-b85a-2521b045c99f'
