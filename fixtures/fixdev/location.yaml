App\Domain\Entity\Location:
  location_1:
    tenant: '<getGermanyTenant()>'
    externalId: Loc1-1-Oe
    id: '548fa6c1-07bd-46ba-ba56-83d1f0510495'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:10"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::BRANCH)>'
    street: Loc-Strasse
    houseNumber: '1'
    postalCode: '12345'
    city: Oelbronn
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.2
    longitude: 2.2
  location_2:
    tenant: '<getGermanyTenant()>'
    externalId: Loc2-2-Bs
    id: '337a132e-9113-4542-8a56-7c162e2f7a98'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:10"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: Teststrasse
    houseNumber: '111'
    postalCode: '54321'
    city: 'Containerort Kunde'
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.3
    longitude: 2.3
  location_3:
    tenant: '<getGermanyTenant()>'
    externalId: Customer-Loc1-1-Oe
    id: '54a9ed85-d944-41a5-ad60-9dc3f4f8181b'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::BRANCH)>'
    street: Cust-Strasse
    houseNumber: '12'
    postalCode: '12345'
    city: Oelbronn
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.1
    longitude: 2.1
  location_4:
    tenant: '<getGermanyTenant()>'
    externalId: Loc-PZ-S-Westfalsen
    id: 'e0366f93-acec-4e67-a730-14b530a658ad'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: 'An der Pforte'
    houseNumber: '2'
    postalCode: '32457'
    city: 'Porta Westfalica'
    district: NRW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 52.247821383703
    longitude: 8.9223155423295
  location_5:
    tenant: '<getGermanyTenant()>'
    externalId: Loc-P-Heide
    id: '481d440c-49c8-4749-a05a-367f3640010d'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: 'Pohlsche Heide'
    houseNumber: '1'
    postalCode: '32479'
    city: Hille
    district: NRW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.4
    longitude: 2.4
  location_6:
    tenant: '<getGermanyTenant()>'
    externalId: OC1-Loc1
    id: '09eb7514-0dd7-46ac-ba71-b7a20c3e14f7'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: TG-Loc-Strasse
    houseNumber: '1'
    postalCode: '54321'
    city: TaskGroupingen
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.7
    longitude: 2.7
  location_7:
    tenant: '<getGermanyTenant()>'
    externalId: TG1-Loc1
    id: '4af7543a-4365-407d-b834-d03d1f54672c'
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: TG-Loc-Strasse
    houseNumber: '1'
    postalCode: '54321'
    city: TaskGroupingen
    district: BW
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.6
    longitude: 2.6
  location_71:
    tenant: '<getNetherlandsTenant()>'
    externalId: loc-nl-pz
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: Teststreet
    houseNumber: '1'
    postalCode: '54321'
    city: TaskGroupingen
    district: T1
    country: '<(App\Domain\Entity\Enum\Country::DE)>'
    latitude: 1.8
    longitude: 2.8
  location_91:
    tenant: '<getSpainTenant()>'
    externalId: loc-es-pz
    createdAt: '<(new \DateTimeImmutable("2023-06-13 13:47:12"))>'
    type: '<(App\Domain\Entity\Enum\Types\LocationType::DEPOT)>'
    street: Teststreet
    houseNumber: '3'
    postalCode: '87621'
    city: TaskGroupingen
    district: T1
    country: '<(App\Domain\Entity\Enum\Country::ES)>'
    latitude: 1.9
    longitude: 2.9
