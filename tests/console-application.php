<?php

declare(strict_types=1);

/**
 * This file is needed for PHPStan to be able to analyze the Symfony console commands.
 *
 * @see https://github.com/phpstan/phpstan-symfony?tab=readme-ov-file#analysis-of-symfony-console-commands
 */

use App\Kernel;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Dotenv\Dotenv;

require __DIR__.'/../vendor/autoload.php';

(new Dotenv())->bootEnv(__DIR__.'/../.env');

$kernel = new Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);

return new Application($kernel);
