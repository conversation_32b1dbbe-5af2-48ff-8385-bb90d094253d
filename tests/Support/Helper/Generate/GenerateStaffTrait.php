<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Generate;

use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

trait GenerateStaffTrait
{
    protected string $sapTourId = '';
    protected string $staffExtId = '';
    protected string $tourExtId = '';
    protected string $tourName = '';

    /**
     * @throws \Throwable
     */
    public function generateStaffAndAuthenticate($tenantIdentifier): string
    {
        $this->staffExtId = ApiTester::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-1-id-'.uniqid();
        $this->tourName = 'random-tour-1-name-'.uniqid();

        $this->sapTourId = $this->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourStaffCreate(
            staffExtId: $this->staffExtId,
            tourExtId: $this->tourExtId,
            tourName: $this->tourName,
        ));

        $this->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $tenantIdentifier);

        return $this->staffExtId;
    }
}
