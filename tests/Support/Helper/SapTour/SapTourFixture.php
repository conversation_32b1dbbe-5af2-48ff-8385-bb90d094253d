<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\SapTour;

readonly class SapTourFixture
{
    /**
     * @param string[]           $staffExternalIds
     * @param string[]           $addressExternalIds
     * @param string[]           $customerExternalIds
     * @param EquipmentFixture[] $equipments
     * @param OrderFixture[]     $orders
     */
    public function __construct(
        public string $requestData,
        public array $staffExternalIds,
        public array $addressExternalIds,
        public array $customerExternalIds,
        public array $equipments,
        public array $orders,
        public string $tourExternalId,
        public string $tourName,
    ) {
    }
}
