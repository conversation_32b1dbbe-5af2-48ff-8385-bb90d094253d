<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\SapTour;

use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Enum\Types\TourOrderType;
use App\Tests\Support\ApiTester;
use Faker\Factory;
use Faker\Generator;

class GermanSapTourFixtures
{
    private const array TASK_TYPES = [
        'deliveryNote_sig',
        'container_count',
        'customer_weight',
        'weighingnote',
        'not-existing',
    ];

    private Generator $faker;
    private int $numberOfStaff = 1;
    private int $numberOfAddresses = 1;
    private int $numberOfCustomers = 1;
    private int $numberOfEquipments = 1;
    private int $numberOfOrders = 1;
    private int $numberOfAdditionalInformationPerOrder = 1;
    private int $numberOfOrderDocumentsPerOrder = 1;
    private int $numberOfOrderDocumentDeliveryServices = 1;
    private int $numberOfOrderLocationsPerOrder = 1;
    private int $numberOfDisposalSites = 1;
    private array $mastertourExternalIds = [];

    private array $fixedEquipments = [];
    private array $fixedOrderTypes = [];

    // Result data (should be cleared on each generation)
    /** @var string[] */
    private array $staffExternalIds = [];
    /** @var string[] */
    private array $addressExternalIds = [];
    /** @var string[] */
    private array $customerExternalIds = [];
    /** @var EquipmentFixture[] */
    private array $equipments = [];
    /** @var OrderFixture[] */
    private array $orders = [];

    public function __construct(
    ) {
        $this->faker = Factory::create();
    }

    public function setNumberOfStaff(int $numberOfStaff): self
    {
        $this->numberOfStaff = $numberOfStaff;

        return $this;
    }

    public function setNumberOfAddresses(int $numberOfAddresses): self
    {
        $this->numberOfAddresses = $numberOfAddresses;

        return $this;
    }

    public function setNumberOfCustomers(int $numberOfCustomers): self
    {
        $this->numberOfCustomers = $numberOfCustomers;

        return $this;
    }

    public function setNumberOfEquipments(int $numberOfEquipments): self
    {
        $this->numberOfEquipments = $numberOfEquipments;

        return $this;
    }

    public function setNumberOfOrders(int $numberOfOrders): self
    {
        $this->numberOfOrders = $numberOfOrders;

        return $this;
    }

    public function setNumberOfAdditionalInformationPerOrder(int $numberOfAdditionalInformationPerOrder): self
    {
        $this->numberOfAdditionalInformationPerOrder = $numberOfAdditionalInformationPerOrder;

        return $this;
    }

    public function setNumberOfOrderDocumentsPerOrder(int $numberOfOrderDocumentsPerOrder): self
    {
        $this->numberOfOrderDocumentsPerOrder = $numberOfOrderDocumentsPerOrder;

        return $this;
    }

    public function setNumberOfOrderDocumentDeliveryServices(int $numberOfOrderDocumentDeliveryServices): self
    {
        $this->numberOfOrderDocumentDeliveryServices = $numberOfOrderDocumentDeliveryServices;

        return $this;
    }

    public function setNumberOfOrderLocationsPerOrder(int $numberOfOrderLocationsPerOrder): self
    {
        $this->numberOfOrderLocationsPerOrder = $numberOfOrderLocationsPerOrder;

        return $this;
    }

    public function setNumberOfDisposalSites(int $numberOfDisposalSites): self
    {
        $this->numberOfDisposalSites = $numberOfDisposalSites;

        return $this;
    }

    public function setMastertourExternalIds(array $mastertourExternalIds): self
    {
        $this->mastertourExternalIds = $mastertourExternalIds;

        return $this;
    }

    /**
     * Tour config 1 matches by fixed equipment with external id: 10004635 and country 'de'.
     * Fixtures reference: 'sap_api_test_tour_config_1'.
     */
    public function matchTourConfig1(): self
    {
        $this->fixedEquipments[] = [
            'equipmentExtId' => '10004635',
            'equipmentType' => 'ASK',
            'height' => 3550,
            'length' => 6815,
            'width' => 2550,
            'weight' => 12130,
            'minimumLoad' => 0,
            'overload' => 0,
            'totalPermissibleWeight' => 0,
            'maxAxleLoad' => 0,
            'licencePlate' => 'MI-AK 1092',
            'containerMounting' => '',
        ];

        return $this;
    }

    /**
     * Order config 1 matches by fixed order type '12' and using the same equipment as in tour config 1.
     * Fixtures reference: 'sap_api_test_order_config_1'.
     */
    public function matchOrderConfig1(): self
    {
        $this->fixedOrderTypes[] = '12';

        return $this;
    }

    public function generate(): SapTourFixture
    {
        $this->staffExternalIds = [];
        $this->addressExternalIds = [];
        $this->customerExternalIds = [];
        $this->equipments = [];
        $this->orders = [];

        $staff = json_encode($this->generateStaff());
        $addresses = json_encode($this->generateAddresses());
        $customers = json_encode($this->generateCustomers());
        $equipments = json_encode($this->generateEquipments());
        $orders = json_encode($this->generateOrders());
        $disposalSites = json_encode($this->generateDisposalSites());

        $tourExternalId = 'tour-external-id-'.uniqid();
        $tourName = 'tour-name-'.uniqid();
        $startDate = (new \DateTime())->setTime(2, 0)->format(DATE_ATOM);
        $endDate = (new \DateTime())->setTime(23, 0)->format(DATE_ATOM);
        $interruptionExternalId = 'interruption-external-id-'.uniqid();
        $mastertourAdditionToTour = '';

        if (count($this->mastertourExternalIds) > 0) {
            $mastertourAdditionToTour = ',"mastertours":'.json_encode($this->mastertourExternalIds);
        }

        $startAddressExternalId = $this->faker->randomElement($this->addressExternalIds);
        $endAddressExternalId = $this->faker->randomElement($this->addressExternalIds);

        $requestData = <<<JSON
        {
            "staff": {$staff},
            "addresses": {$addresses},
            "customers": {$customers},
            "equipments": {$equipments},
            "orders": {$orders},
            "tourExtId": "{$tourExternalId}",
            "name": "{$tourName}",
            "branchExternalId": "Oelbronn",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "{$startAddressExternalId}",
            "endAddressExtId": "{$endAddressExternalId}",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": {$disposalSites},
            "interruptionExternalId": "{$interruptionExternalId}"
            {$mastertourAdditionToTour}
        }
        JSON;

        return new SapTourFixture(
            requestData: $requestData,
            staffExternalIds: $this->staffExternalIds,
            addressExternalIds: $this->addressExternalIds,
            customerExternalIds: $this->customerExternalIds,
            equipments: $this->equipments,
            orders: $this->orders,
            tourExternalId: $tourExternalId,
            tourName: $tourName,
        );
    }

    private function generateStaff(): array
    {
        $staffList = [];

        for ($i = 0; $i < $this->numberOfStaff; ++$i) {
            $staffExternalId = ApiTester::TEST_STAFF_EXT_ID_PREFIX.uniqid();
            $this->staffExternalIds[] = $staffExternalId;

            $staffList[] = [
                'staffExtId' => $staffExternalId,
                'personnelType' => 'DRIVER',
                'firstName' => $this->faker->firstName(),
                'lastName' => $this->faker->lastName(),
            ];
        }

        return $staffList;
    }

    private function generateAddresses(): array
    {
        $addresses = [];

        for ($i = 0; $i < $this->numberOfAddresses; ++$i) {
            $addressExternalId = 'address-external-id-'.uniqid();
            $this->addressExternalIds[] = $addressExternalId;

            $addresses[] = [
                'country' => $this->faker->randomElement(['DE', 'LU', 'ES', 'NL']),
                'postalCode' => $this->faker->postcode(),
                'city' => $this->faker->city(),
                'district' => $this->faker->word(),
                'street' => $this->faker->streetName(),
                'houseNumber' => $this->faker->buildingNumber(),
                'phoneNumber' => $this->faker->phoneNumber(),
                'state' => $this->faker->word(),
                'addressExtId' => $addressExternalId,
            ];
        }

        return $addresses;
    }

    private function generateCustomers(): array
    {
        $customers = [];

        for ($i = 0; $i < $this->numberOfCustomers; ++$i) {
            $customerExternalId = 'customer-external-id-'.uniqid();
            $this->customerExternalIds[] = $customerExternalId;

            $customers[] = [
                'customerName' => $this->faker->company(),
                'customerExtId' => $customerExternalId,
            ];
        }

        return $customers;
    }

    private function generateEquipments(): array
    {
        $equipments = [];

        for ($i = 0; $i < $this->numberOfEquipments; ++$i) {
            if (count($this->fixedEquipments) > 0) {
                $fixedEquipment = array_shift($this->fixedEquipments);
                $this->equipments[] = new EquipmentFixture(
                    externalId: $fixedEquipment['equipmentExtId'],
                    licensePlate: $fixedEquipment['licencePlate']
                );
                $equipments[] = $fixedEquipment;
                continue;
            }

            $equipmentExternalId = 'equipment-external-id-'.uniqid();
            $equipmentLicensePlate = $this->faker->regexify('[A-Z]{2}-[A-Z]{2} [0-9]{4}');

            $this->equipments[] = new EquipmentFixture(
                externalId: $equipmentExternalId,
                licensePlate: $equipmentLicensePlate
            );

            $equipments[] = [
                'equipmentExtId' => $equipmentExternalId,
                'equipmentType' => $this->faker->randomElement(EquipmentType::class),
                'height' => $this->faker->numberBetween(1000, 5000),
                'length' => $this->faker->numberBetween(1000, 5000),
                'width' => $this->faker->numberBetween(1000, 5000),
                'weight' => $this->faker->numberBetween(1000, 50000),
                'minimumLoad' => $this->faker->numberBetween(0, 10000),
                'overload' => $this->faker->numberBetween(0, 10000),
                'totalPermissibleWeight' => $this->faker->numberBetween(0, 10000),
                'maxAxleLoad' => $this->faker->numberBetween(0, 10000),
                'licencePlate' => $equipmentLicensePlate,
                'containerMounting' => '',
            ];
        }

        return $equipments;
    }

    private function generateOrders(): array
    {
        $orders = [];

        for ($i = 0; $i < $this->numberOfOrders; ++$i) {
            if (count($this->fixedOrderTypes) > 0) {
                $orderType = array_shift($this->fixedOrderTypes);
            } else {
                $orderType = $this->faker->randomElement(TourOrderType::class);
            }

            $orderExternalId = 'order-external-id-'.uniqid();

            $this->orders[] = new OrderFixture(
                externalId: $orderExternalId,
            );

            $orders[] = [
                'locationExtId' => $this->faker->randomElement($this->addressExternalIds),
                'customerExtId' => $this->faker->randomElement($this->customerExternalIds),
                'additionalInformation' => $this->generateAdditionalInformation(),
                'orderLocations' => $this->generateOrderLocations(),
                'orderType' => $orderType,
                'orderPosNr' => $i + 1,
                'orderExtId' => $orderExternalId,
                'contractNumber' => $this->faker->bothify('cn-#########'),
                'transportNumber' => $this->faker->bothify('tn-#########'),
                'purchaseOrder' => $this->faker->bothify('po-#########'),
                'orderDocuments' => $this->generateOrderDocuments(),
            ];
        }

        return $orders;
    }

    private function generateAdditionalInformation(): array
    {
        $icons = [
            'handyman',
            'pz_restabfaelle',
            'pz_container',
            'info_outline',
        ];

        $additionalInformation = [];

        for ($i = 0; $i < $this->numberOfAdditionalInformationPerOrder; ++$i) {
            $additionalInformation[] = [
                'sequence' => ($i + 1) * 10,
                'text' => $this->faker->sentence(),
                'alsoFrontView' => $this->faker->boolean(),
                'bold' => $this->faker->boolean(),
                'icon' => $this->faker->randomElement($icons),
            ];
        }

        return $additionalInformation;
    }

    private function generateOrderLocations(): array
    {
        $orderLocations = [];

        for ($i = 0; $i < $this->numberOfOrderLocationsPerOrder; ++$i) {
            $tasks = [];

            for ($j = 0, $count = rand(1, 5); $j < $count; ++$j) {
                $tasks[] = [
                    'taskType' => $this->faker->randomElement(self::TASK_TYPES),
                    'taskName' => $this->faker->sentence(),
                    'elements' => [],
                    'taskExtId' => 'task-external-id-'.uniqid(),
                ];
            }

            $orderLocations[] = [
                'orderLocationType' => $this->faker->randomElement(['CUSTOMER', 'DEPOT', 'DISPOSALSITE']),
                'locationExtId' => $this->faker->randomElement($this->addressExternalIds),
                'additionalInformation' => $this->generateAdditionalInformation(),
                'tasks' => $tasks,
            ];
        }

        return $orderLocations;
    }

    private function generateOrderDocuments(): array
    {
        $textBlocks = [
            [
                'blockId' => 'header-1',
                'text' => 'PreZero Service Westfalen GmbH & Co. KG\\nAn der Pforte 2, 32457 Porta <br>Westfalica\\npls regard',
            ],
            [
                'blockId' => 'footer-1',
                'text' => 'Placeat rerum ut et enim ex eveniet facere sunt quia delectus aut nam et eum architecto fugit repellendus illo veritatis qui ex esse.',
            ],
            [
                'blockId' => 'footer-2',
                'text' => 'Voluptate vel possimus omnis aut incidunt sunt cumque asperiores incidunt iure sequi cum.',
            ],
            [
                'blockId' => 'footer-3',
                'text' => 'Rem aut rerum exercitationem est rem dicta voluptas fuga totam reiciendis qui architecto fugiat nemo omnis consequatur recusandae qui cupiditate eos quod.',
            ],
            [
                'blockId' => 'footer-4',
                'text' => 'Vel\\n optio provident non incidunt magnam molestias et quibusdam et ab quo voluptatum quia.',
            ],
            [
                'blockId' => 'tos',
                'text' => 'Voluptatibus est accusantium eveniet aut atque possimus aut dolores quis totam incidunt ducimus aperiam nesciunt est quia assumenda minima sunt qui similique.',
            ],
        ];

        $orderDocuments = [];

        for ($i = 0; $i < $this->numberOfOrderDocumentsPerOrder; ++$i) {
            $deliveryServices = [];

            for ($j = 0; $j < $this->numberOfOrderDocumentDeliveryServices; ++$j) {
                $deliveryServices[] = match (rand(1, 2)) {
                    1 => [
                        'materialNumber' => $this->faker->bothify('######'),
                        'text' => $this->faker->sentence(),
                        'taskExternalId' => 'pdfT1',
                        'templateField' => 'material-list',
                        'unit' => $this->faker->randomElement(['kg', 'test', 'really? anything?', 'give me a break :D']),
                    ],
                    2 => [
                        'materialNumber' => $this->faker->bothify('######'),
                        'text' => $this->faker->sentence(),
                        'taskExternalId' => 'pdfT1',
                        'templateField' => 'material-list',
                    ],
                };
            }

            $orderDocuments[] = [
                'documentType' => 'deliverynote_lu_int',
                'deliveryServices' => $deliveryServices,
                'textBlocks' => $textBlocks,
            ];
        }

        return $orderDocuments;
    }

    private function generateDisposalSites(): array
    {
        $disposalSites = [];

        for ($i = 0; $i < $this->numberOfDisposalSites; ++$i) {
            $disposalSites[] = [
                'addressExtId' => $this->faker->randomElement($this->addressExternalIds),
                'weigh' => $this->faker->boolean(),
                'disposalSiteExtId' => 'disposal-site-external-id-'.uniqid(),
                'name' => $this->faker->sentence(),
            ];
        }

        return $disposalSites;
    }
}
