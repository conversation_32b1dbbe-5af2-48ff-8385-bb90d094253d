<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\SessionData;

use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTour;
use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTourEquipment;
use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTourStaff;

class TestSession
{
    public string $deviceId;

    /**
     * @var array<string, TestTourEquipment>
     */
    public array $equipments = [];

    /**
     * @var array<string, TestTourStaff>
     */
    public array $staff = [];

    /**
     * @var array<string, TestTour>
     */
    public array $tours = [];

    public function __construct(
        public string $tenant,
    ) {
        $this->deviceId = 'test-device-'.uniqid();
    }
}
