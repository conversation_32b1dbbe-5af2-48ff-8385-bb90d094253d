<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Actor;

use App\Infrastructure\PortalApi\Resource\Dto\DeviceMessage\RecipientKind;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use App\Tests\Support\Helper\Scenario\IngestData\Phonebook\TestPhonebook;
use App\Tests\Support\Helper\Scenario\IngestData\Phonebook\TestPhonebookContact;
use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestBranch;

class PortalActor extends Actor
{
    private string $password;

    public function authenticate(string $password): self
    {
        $this->password = $password;

        return $this->useAccount();
    }

    private function useAccount(): self
    {
        $this->apiTester->amAuthenticatedAsPortalUser($this->alias, $this->alias, $this->password);

        return $this;
    }

    public function writeDeviceMessageToEquipment(string $equipmentAlias): self
    {
        $this->useAccount();
        $data = [
            'recipient' => [
                'kind' => RecipientKind::EQUIPMENT->value,
                'equipment_id' => $this->equipmentContext->getEquipment($equipmentAlias)->uuid,
            ],
            'message' => DataFaker::instance()->sentence(10),
        ];
        $this->apiTester->callApiPortalDeviceMessageCreate(json_encode($data) ?: '');

        return $this;
    }

    public function findThreadInSearch(string $query): self
    {
        $this->useAccount();

        $this->apiTester->waitUntil(
            function () use ($query) {
                $this->apiTester->callApiPortalDeviceMessageThreadSearch(search: $query);
                $threads = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
                $this->apiTester->assertGreaterOrEquals(1, count($threads));
            }
        );

        return $this;
    }

    public function createDeviceAccess(
        string $staffExternalId,
        string $accessType,
        int $validMinutes = 5,
        int $expectedStatusCode = 201,
    ): self {
        $this->useAccount();
        $this->apiTester->callApiPortalStaffList(query: $staffExternalId);
        $staffId = $this->apiTester->grabDataFromResponseByJsonPath('$.items[0].staff_id')[0];
        $this->apiTester->assertNotEmpty($staffId);
        $validUntil = new \DateTimeImmutable()->modify("+ $validMinutes minutes")->format(\DateTimeInterface::RFC3339_EXTENDED);

        $data = json_encode([
            'device_access_type' => $accessType,
            'valid_until' => $validUntil,
        ]) ?: '';
        $this->apiTester->callApiPortalStaffDeviceAccessCreate(staffId: $staffId, data: $data, expectedStatusCode: $expectedStatusCode);

        return $this;
    }

    public function getTrackingList(
        ?\DateTimeImmutable $minDate = null,
        ?\DateTimeImmutable $maxDate = null,
        ?string $search = null,
        ?int $expectedListCount = null,
        ?int $maxWaypointCount = null,
    ): self {
        $this->useAccount();
        $this->apiTester->callApiPortalTrackingList(
            minDate: $minDate?->format('Y-m-d'),
            maxDate: $maxDate?->format('Y-m-d'),
            search: $search,
        );

        $list = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        if (null !== $expectedListCount) {
            $this->apiTester->assertCount(
                $expectedListCount,
                $list,
            );
        }

        if (null !== $maxWaypointCount) {
            foreach ($list as $listEntry) {
                $this->apiTester->callApiPortalTrackingDetails($listEntry['tracking_id']);
                $trackingData = $this->apiTester->grabDataFromResponseByJsonPath('$.tracking_data')[0];
                $this->apiTester->assertLessThan($maxWaypointCount, count($trackingData));
            }
        }

        return $this;
    }

    public function searchTourInTourFileDd(string $searchedTourAlias, bool $tourFoundInResponse, ?string $hasFiles = null): self
    {
        $tourId = $this->tourContext->getTour($searchedTourAlias)->uuid;
        if (null === $tourId) {
            $this->apiTester->fail('no tour-id set');

            return $this;
        }
        $this->useAccount();

        $this->apiTester->waitUntil(function () use ($tourFoundInResponse, $hasFiles, $tourId) {
            $this->apiTester->callApiPortalTourLabelsByDate(date('Y-m-d'), $hasFiles);
            $tourIds = array_column($this->apiTester->grabDataFromResponseByJsonPath('$.items')[0], 'tour_id');

            if ($tourFoundInResponse) {
                $this->apiTester->assertContains($tourId, $tourIds);
            } else {
                $this->apiTester->assertNotContains($tourId, $tourIds);
            }

            $this->apiTester->callApiPortalOrdersByTour($tourId, $hasFiles);
            $orders = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
            $this->apiTester->assertIsArray($orders);

            if ($tourFoundInResponse) {
                $this->apiTester->assertNotEmpty($orders);
            } else {
                $this->apiTester->assertEmpty($orders);
            }
        });

        return $this;
    }

    public function getLanguageInformation(): self
    {
        $this->useAccount();
        $this->apiTester->callApiPortalTranslationIsoCodeList();

        $isos = $this->apiTester->grabDataFromResponseByJsonPath('$.items[*].iso');
        $this->apiTester->assertCount(17, $isos);
        $this->apiTester->assertContains('de_DE', $isos);

        return $this;
    }

    public function getLanguageData(string $iso): self
    {
        $this->useAccount();
        $this->apiTester->callApiPortalTranslationByCode($iso);

        $translations = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        $this->apiTester->assertNotEmpty($translations);

        $emptyTranslationFound = false;

        foreach ($translations as $translation) {
            if ('module/dashboard/welcome_message/content' === $translation['key']) {
                $this->apiTester->assertEquals('', $translation['value']);
                $emptyTranslationFound = true;
                break;
            }
        }
        $this->apiTester->assertTrue($emptyTranslationFound);

        return $this;
    }

    public function getWaypointLanguageData(string $iso): self
    {
        $this->useAccount();
        $this->apiTester->callApiPortalWaypointTranslationByCode($iso);

        $items = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        $this->apiTester->assertNotEmpty($items);

        return $this;
    }

    public function getAccessibleBranches(): self
    {
        $this->useAccount();

        $this->apiTester->callApiPortalBranchListAccessible();
        $accessibleBranches = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($accessibleBranches as $accessibleBranch) {
            $this->branchContext->setBranchIdByExternalId(id: $accessibleBranch['branch_id'], externalId: $accessibleBranch['external_id']);
        }

        return $this;
    }

    /**
     * @param string[] $branchAliases
     */
    public function createPhonebook(string $phonebookAlias, array $branchAliases): self
    {
        $this->useAccount();

        $testPhonebook = new TestPhonebook(
            array_map(
                fn (string $branchAlias): TestBranch => $this->branchContext->getBranch($branchAlias),
                $branchAliases,
            )
        );
        $this->phonebookContext->addPhonebook($phonebookAlias, $testPhonebook);
        $this->apiTester->callApiPortalPhonebookCreate(json_encode($testPhonebook, JSON_THROW_ON_ERROR));
        $phonebookId = $this->apiTester->grabDataFromResponseByJsonPath('$.id')[0];
        $this->apiTester->assertNotEmpty($phonebookId);
        $testPhonebook->uuid = $phonebookId;

        return $this;
    }

    /**
     * @param string[] $branchAliases
     */
    public function patchPhonebook(string $phonebookAlias, array $branchAliases): self
    {
        $this->useAccount();

        $testPhonebook = $this->phonebookContext->getPhonebook($phonebookAlias);
        $testPhonebook->branches = array_map(
            fn (string $branchAlias): TestBranch => $this->branchContext->getBranch($branchAlias),
            $branchAliases,
        );
        $testPhonebook->name = DataFaker::instance()->word();
        $testPhonebook->description = DataFaker::instance()->sentence(3);

        $this->apiTester->callApiPortalPhonebookPatch(
            $testPhonebook->uuid ?? throw new \Exception('phonebook uuid not set before patching'),
            json_encode($testPhonebook, JSON_THROW_ON_ERROR),
        );

        return $this;
    }

    public function createPhonebookContact(string $phonebookAlias, string $contactAlias): self
    {
        $this->useAccount();
        $testPhonebook = $this->phonebookContext->getPhonebook($phonebookAlias);
        $testContact = new TestPhonebookContact();
        $testPhonebook->addContact($contactAlias, $testContact);

        $this->apiTester->callApiPortalPhonebookContactCreate(
            phonebookId: $testPhonebook->uuid ?? throw new \Exception('uuid of phonebook not set'),
            data: json_encode($testContact, JSON_THROW_ON_ERROR),
        );

        return $this;
    }

    public function patchPhonebookContact(string $phonebookAlias, string $contactAlias): self
    {
        $this->useAccount();

        $testContact = $this->phonebookContext->getPhonebook($phonebookAlias)->getContact($contactAlias);
        $testContact->lastName = DataFaker::instance()->citySuffix();

        $this->apiTester->callApiPortalPhonebookContactPatch(
            $this->phonebookContext->getPhonebook($phonebookAlias)->uuid ?? throw new \Exception('phonebook uuid not set'),
            json_encode($testContact, JSON_THROW_ON_ERROR),
        );

        return $this;
    }

    public function deletePhonebookContact(string $phonebookAlias, string $contactAlias): self
    {
        $this->useAccount();
        $this->apiTester->callApiPortalPhonebookContactDelete(
            $this->phonebookContext->getPhonebook($phonebookAlias)->uuid ?? throw new \Exception('phonebook uuid not set'),
            $this->phonebookContext->getPhonebook($phonebookAlias)->getContact($contactAlias)->uuid ?? throw new \Exception('contact uuid not set'),
        );
        $this->phonebookContext->getPhonebook($phonebookAlias)->removeContact($contactAlias);

        return $this;
    }

    public function findPhonebook(string $phonebookAlias): self
    {
        $this->useAccount();

        $this->apiTester->callApiPortalPhonebookCollection();
        $phonebooks = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        $testPhonebook = $this->phonebookContext->getPhonebook($phonebookAlias);
        $found = false;
        foreach ($phonebooks as $phonebook) {
            if ($phonebook['id'] === $testPhonebook->uuid) {
                $found = true;
                $this->apiTester->assertEquals($phonebook['name'], $testPhonebook->name);
                $this->apiTester->assertEquals($phonebook['description'], $testPhonebook->description);

                break;
            }
        }
        $this->apiTester->assertTrue($found);

        return $this;
    }

    public function getPhonebook(
        string $phonebookAlias,
        int $expectedStatusCode = 200,
    ): self {
        $this->useAccount();

        $testPhoneBook = $this->phonebookContext->getPhonebook($phonebookAlias);
        $this->apiTester->callApiPortalPhonebookDetails(
            $testPhoneBook->uuid ?? throw new \Exception('phonebook uuid not set'),
            $expectedStatusCode,
        );

        if ($expectedStatusCode >= 300) {
            return $this;
        }

        $phonebookData = $this->apiTester->grabDataFromResponseByJsonPath('$')[0];
        $this->apiTester->assertEquals($testPhoneBook->name, $phonebookData['name']);
        $this->apiTester->assertEquals($testPhoneBook->description, $phonebookData['description']);
        $this->apiTester->assertEquals(count($testPhoneBook->branches), count($phonebookData['branches']));
        $this->apiTester->assertEquals(count($testPhoneBook->branches), count($phonebookData['branch_external_ids']));

        $testPhoneBookBranchIds = array_map(
            fn (TestBranch $testBranch): string => $testBranch->uuid ?? throw new \Exception('uuid not set in branch'),
            $testPhoneBook->branches
        );
        sort($testPhoneBookBranchIds);
        sort($phonebookData['branches']);
        $testPhoneBookBranchExternalIds = array_map(
            fn (TestBranch $testBranch): string => $testBranch->externalId,
            $testPhoneBook->branches
        );
        sort($testPhoneBookBranchExternalIds);
        sort($phonebookData['branch_external_ids']);

        $this->apiTester->assertEquals($testPhoneBookBranchIds, $phonebookData['branches']);
        $this->apiTester->assertEquals($testPhoneBookBranchExternalIds, $phonebookData['branch_external_ids']);

        $contactFoundIds = [];
        foreach ($testPhoneBook->contacts as $contactAlias => $testContact) {
            foreach ($phonebookData['contacts'] as $contactData) {
                if ($contactData['first_name'] === $testContact->firstName
                    && $contactData['last_name'] === $testContact->lastName
                    && $contactData['country_prefix'] === $testContact->countryPrefix
                    && $contactData['phone_number'] === $testContact->phoneNumber
                    && $contactData['emergency_contact'] === $testContact->emergencyContact
                    && $contactData['description'] === $testContact->description
                    && !in_array($contactData['id'], $contactFoundIds)
                ) {
                    $testContact->uuid = $contactData['id'];
                    $contactFoundIds[] = $testContact->uuid;
                    break;
                }
            }
        }
        $this->apiTester->assertEquals(count($contactFoundIds), count($testPhoneBook->contacts));

        return $this;
    }
}
