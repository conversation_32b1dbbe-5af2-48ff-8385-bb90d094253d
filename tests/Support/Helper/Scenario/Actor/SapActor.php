<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Actor;

use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTour;

class SapActor extends Actor
{
    /**
     * @param array<int, array{alias: string}> $equipments
     * @param array<int, array{alias: string}> $staff
     */
    public function importTour(
        string $tourAlias,
        array $equipments,
        array $staff,
        string $branchAlias,
        int $orderCount = 1,
    ): self {
        if ('pz' === $this->tenant) {
            return $this->importGermanTestConfigTour(
                tourAlias: $tourAlias,
                branchAlias: $branchAlias,
                equipments: $equipments,
                staff: $staff,
                orderCount: $orderCount,
            );
        }

        throw new \Exception('tenant no immplemented');
    }

    public function reimportTour(
        string $tourAlias,
    ): self {
        if ('pz' === $this->tenant) {
            return $this->reimportGermanTestConfigTour($tourAlias);
        }

        throw new \Exception('tenant no immplemented');
    }

    /**
     * @param array<int, array{alias: string}> $equipments
     * @param array<int, array{alias: string}> $staff
     */
    private function importGermanTestConfigTour(
        string $tourAlias,
        string $branchAlias,
        array $equipments,
        array $staff,
        int $orderCount,
    ): self {
        $tourEquipments = array_map(
            fn (array $equipment): array => [
                'alias' => $equipment['alias'],
                'existingObject' => false === $this->equipmentContext->hasEquipment($equipment['alias']) ? null : $this->equipmentContext->getEquipment($equipment['alias']),
            ],
            $equipments,
        );
        $tourStaff = array_map(
            fn (array $staffMember): array => [
                'alias' => $staffMember['alias'],
                'existingObject' => false === $this->staffContext->hasStaff($staffMember['alias']) ? null : $this->staffContext->getStaff($staffMember['alias']),
            ],
            $staff,
        );

        $testTour = new TestTour(
            equipments: $tourEquipments,
            staff: $tourStaff,
            branch: [
                'alias' => $branchAlias,
                'existingObject' => false === $this->branchContext->hasBranch($branchAlias) ? null : $this->branchContext->getBranch($branchAlias),
            ],
            orderCount: $orderCount,
        );
        $this->tourContext->addTour($tourAlias, $testTour);

        $sapTourId = $this->apiTester->callSapGermanyTourUpsert(
            $testTour
                ->hasOrderDocumentForGermany()
                ->matchesGermanyOrderConfig()
                ->matchesGermanyTourConfig()
                ->getTourJson(),
        );

        foreach ($testTour->equipments as $alias => $testTourEquipment) {
            $this->equipmentContext->addEquipment($alias, $testTourEquipment);
        }
        foreach ($testTour->staff as $alias => $testTourStaff) {
            $this->staffContext->addStaff($alias, $testTourStaff);
        }
        $this->branchContext->addBranch(alias: $branchAlias, branch: $testTour->branch);

        $this->tourContext->getTour($tourAlias)->uuid = $sapTourId;

        return $this;
    }

    private function reimportGermanTestConfigTour(string $tourAlias): self
    {
        $tour = $this->tourContext->getTour($tourAlias);
        $sapTourId = $this->apiTester->callSapGermanyTourUpsert(
            $tour
                ->getTourJson(),
        );
        if ($sapTourId !== $tour->uuid) {
            throw new \Exception('tour reimport did not match the original tour!');
        }

        return $this;
    }
}
