<?php

namespace App\Tests\Support\Helper\Scenario\Actor;

use App\Domain\Services\ObjectStorage\ObjectPrefix;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use App\Tests\Support\Helper\Scenario\IngestData\Fixtures\AppLogData;
use App\Tests\Support\Helper\Scenario\IngestData\Fixtures\ReducableTrackingData;
use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTourEquipment;
use Symfony\Component\Mime\MimeTypes;
use Symfony\Component\Uid\Uuid;

class AppActor extends Actor
{
    private ?string $currentDeviceId = null;

    private const string APP_VERSION_OUTDATED = '4.9.0';
    private const string APP_VERSION_BELOW_RECOMMENDED = '4.10.0';
    private const string APP_VERSION_NEW = '4.11.0';

    private string $appVersion = self::APP_VERSION_NEW;

    public function authenticateWithSessionDevice(string $sessionAlias): self
    {
        if (!$this->sessionContext->sessionExists($sessionAlias)) {
            $this->sessionContext->addNewSession(alias: $sessionAlias, tenant: $this->tenant);
        }

        $testSession = $this->sessionContext->getSession($sessionAlias);
        $this->currentDeviceId = $testSession->deviceId;
        $this->useDevice();

        return $this;
    }

    public function startHermesAppSession(string $sessionAlias): self
    {
        $testSession = $this->sessionContext->getSession($sessionAlias);
        $this->useDevice();

        $this->apiTester->callApiUserStartSessionV2();

        $testSession->staff[$this->alias] = $this->staffContext->getStaff($this->alias);

        return $this;
    }

    public function logoutUserFromSession(): self
    {
        $this->useDevice();
        $testSession = $this->sessionContext->getSessionForStaff($this->alias);
        $staff = $testSession->staff[$this->alias];
        $this->apiTester->amAuthenticatedAsGeneratedUserAndVerify($staff->staffExtId, $testSession->tenant);
        $this->apiTester->amUsingDeviceWithId($testSession->deviceId);
        $this->apiTester->callApiUserEndSessionV2();
        unset($testSession->staff[$this->alias]);
        $this->currentDeviceId = null;

        return $this;
    }

    public function getProfile(int $expectedStatusCode = 200): self
    {
        $this->useDevice();
        $this->apiTester->callApiUserProfileV2(expectedStatusCode: $expectedStatusCode);

        return $this;
    }

    public function hasAppVersionCentrifugoMessage(bool $expectedResult = true): self
    {
        $hasVersionMessage = $this->hasCentrifugoMessageInHistory(
            channel: 'device:channel#'.bin2hex($this->currentDeviceId ?? ''),
            type: 'app_version',
            attributes: [
                ['attribute' => 'recommended_version', 'value' => '4.11.0'],
                ['attribute' => 'title', 'value' => '{{app_version_not_recommended/title}}'],
                ['attribute' => 'message', 'value' => '{{app_version_not_recommended/message}}'],
            ],
        );
        $this->apiTester->assertEquals($expectedResult, $hasVersionMessage, 'Centrifugo did not receive the expected app-version-message');

        return $this;
    }

    /**
     * @param array<int, array{attribute: string, value: string}> $attributes
     */
    private function hasCentrifugoMessageInHistory(string $channel, string $type, array $attributes): bool
    {
        $history = $this->apiTester->grabCentrifugoHistory(channel: $channel);

        foreach ($history as $message) {
            $data = $message['data'];

            if ($type !== $data['type']) {
                continue;
            }

            $equals = true;
            foreach ($attributes as $attribute) {
                if (!isset($data[$attribute['attribute']]) || $data[$attribute['attribute']] !== $attribute['value']) {
                    $equals = false;
                    break;
                }
            }

            if ($equals) {
                return true;
            }
        }

        return false;
    }

    public function patchProfile(
        string $colormode = 'light',
        string $language = 'de_DE',
    ): self {
        $this->useDevice();
        $this->apiTester->callApiPatchUserProfileV2(
            json_encode([
                'colormode' => $colormode,
                'language' => $language,
            ]) ?: ''
        );
        $this->apiTester->waitUntil(function () use ($language, $colormode): void {
            $this->apiTester->callApiUserProfileV2();
            $languageSet = $this->apiTester->grabDataFromResponseByJsonPath('$.language')[0];
            $colormodeSet = $this->apiTester->grabDataFromResponseByJsonPath('$.colormode')[0];
            $this->apiTester->assertEquals($language, $languageSet);
            $this->apiTester->assertEquals($colormode, $colormodeSet);
        });

        return $this;
    }

    public function getDataProtectionNotice(string $locale = 'de_DE'): self
    {
        $this->useDevice();
        $this->apiTester->callApiDataProtectionNoticeV2($locale);
        $this->apiTester->assertNotEmpty($this->apiTester->grabResponse());

        return $this;
    }

    public function getPointsOfInterest(): self
    {
        $this->useDevice();
        $this->apiTester->callApiPoiGetListV2();
        $data = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0][0];
        $this->apiTester->assertGreaterThanOrEqual(1, count($data));

        return $this;
    }

    public function getUserCollection(): self
    {
        $this->useDevice();
        $this->apiTester->callApiUserGetCollectionV2();
        $users = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        $this->apiTester->assertGreaterThanOrEqual(1, count($users));

        return $this;
    }

    public function sendAppLog(): self
    {
        $this->useDevice();
        $this->apiTester->callApiLogAppV2(AppLogData::getLogData());

        return $this;
    }

    public function sendAppNavigationLog(): self
    {
        $this->useDevice();
        $this->apiTester->callApiLogNavigationAppV2(AppLogData::getLogData());

        return $this;
    }

    public function getLanguageInformation(): self
    {
        $this->useDevice();
        $this->apiTester->callApiLanguageGetV2();
        $isos = $this->apiTester->grabDataFromResponseByJsonPath('$.items[*].iso');
        $this->apiTester->assertCount(17, $isos);
        $this->apiTester->assertContains('de_DE', $isos);

        return $this;
    }

    public function getLanguageData(string $iso): self
    {
        $this->useDevice();
        $this->apiTester->callApiLanguageGetV2($iso);

        $translations = $this->apiTester->grabDataFromResponseByJsonPath('$.translations')[0];
        $this->apiTester->assertNotEmpty($translations);
        $emptyTranslationFound = false;

        foreach ($translations as $translation) {
            if ('module/dashboard/welcome_message/content' === $translation['key']) {
                $this->apiTester->assertEquals('', $translation['value']);
                $emptyTranslationFound = true;
                break;
            }
        }
        $this->apiTester->assertTrue($emptyTranslationFound);

        return $this;
    }

    public function findEquipmentOfTour(string $tourAlias): self
    {
        $this->useDevice();
        $tour = $this->tourContext->getTour($tourAlias);
        $this->apiTester->callApiEquipmentGetCollectionV2();
        $foundEquipments = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($tour->equipments as $equipment) {
            $found = false;
            foreach ($foundEquipments as $foundEquipment) {
                if ($equipment->licencePlate === $foundEquipment['license_plate']) {
                    $equipment->uuid = $foundEquipment['uuid'];
                    $found = true;
                    break;
                }
            }
            $this->apiTester->assertTrue($found);
        }

        return $this;
    }

    public function findInEquipmentList(string $equipmentAlias, bool $allAvaileable = false): self
    {
        $this->useDevice();
        $this->apiTester->callApiEquipmentGetCollectionV2(availability: $allAvaileable ? 'all' : null);

        $licensePlates = $this->apiTester->grabDataFromResponseByJsonPath('$.items[*].license_plate');
        $this->apiTester->assertContains(
            $this->equipmentContext->getEquipment($equipmentAlias)->licencePlate,
            $licensePlates,
        );

        return $this;
    }

    public function canNotFindInEquipmentList(string $equipmentAlias, bool $allAvaileable = false): self
    {
        $this->useDevice();
        $this->apiTester->callApiEquipmentGetCollectionV2(availability: $allAvaileable ? 'all' : null);

        $licensePlates = $this->apiTester->grabDataFromResponseByJsonPath('$.items[*].license_plate');
        $this->apiTester->assertNotContains(
            $this->equipmentContext->getEquipment($equipmentAlias)->licencePlate,
            $licensePlates,
        );

        return $this;
    }

    public function findInTourList(
        string $tourAlias,
        ?string $status = null,
    ): self {
        $this->useDevice();
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }

        $this->apiTester->callApiTourGetCollectionV2();
        $tourResults = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];

        $tourFound = false;
        foreach ($tourResults as $tourResult) {
            if ($tourResult['uuid'] === $tour->uuid) {
                $tourFound = true;
                $this->apiTester->assertEquals($tourResult['name'], $tour->name);
                $this->apiTester->assertEquals($tourResult['external_id'], $tour->tourExtId);
                if (null !== $status) {
                    $this->apiTester->assertEquals($tourResult['status'], $status);
                }
                break;
            }
        }
        $this->apiTester->assertEquals(true, $tourFound);

        return $this;
    }

    public function canNotFindInTourList(
        string $tourAlias,
    ): self {
        $this->useDevice();
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }
        $this->apiTester->callApiTourGetCollectionV2();
        $tourResults = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];

        $tourFound = false;
        foreach ($tourResults as $tourResult) {
            if ($tourResult['uuid'] === $tour->uuid) {
                $tourFound = true;
                break;
            }
        }
        $this->apiTester->assertEquals(false, $tourFound);

        return $this;
    }

    public function viewTourDetails(
        string $tourAlias,
        ?string $status = null,
    ): self {
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }
        $this->useDevice();
        $this->apiTester->callApiTourGetV2($tour->uuid);
        $details = $this->apiTester->grabDataFromResponseByJsonPath('$')[0];
        $this->apiTester->assertEquals($details['name'], $tour->name);
        $this->apiTester->assertEquals($details['external_id'], $tour->tourExtId);
        if (null !== $status) {
            $this->apiTester->assertEquals($details['status'], $status);
        }

        $orderUuids = $this->apiTester->grabDataFromResponseByJsonPath('$.orders[*].uuid');
        for ($i = 0; $i < count($tour->orders); ++$i) {
            $tour->orders[$i + 1]->uuid = $orderUuids[$i]; // orderPosNumber is 1-based
        }

        return $this;
    }

    public function viewSingleOrderDetails(string $tourAlias): self
    {
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }
        $this->useDevice();

        $this->apiTester->callApiTourGetV2($tour->uuid);
        $orders = $this->apiTester->grabDataFromResponseByJsonPath('$.orders')[0];

        foreach ($orders as $orderFromTour) {
            $this->apiTester->callApiOrderGetV2(orderId: $orderFromTour['uuid']);
            $this->apiTester->assertEquals(
                $orderFromTour,
                $this->apiTester->grabDataFromResponseByJsonPath('$')[0],
            );
        }

        return $this;
    }

    public function canNotViewTourDetails(
        string $tourAlias,
        int $statusCode = 403,
    ): self {
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }
        $this->useDevice();
        $this->apiTester->callApiTourGetV2($tour->uuid, expectedStatusCode: $statusCode);

        return $this;
    }

    public function bookTourStart(
        string $tourAlias,
    ): self {
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }
        $this->useDevice();
        $this->apiTester->callApiTourBookingV2(
            tourId: $tour->uuid,
            data: $this->bookingStartRequest(false),
        );
        $this->apiTester->assertEquals(
            'processed',
            $this->apiTester->grabDataFromResponseByJsonPath('$.booking')[0],
        );
        $this->sessionContext->getSessionForStaff($this->alias)->tours[$tourAlias] = $tour;

        return $this;
    }

    public function bookTourEnd(
        string $tourAlias,
    ): self {
        $tour = $this->tourContext->getTour($tourAlias);
        if (null === $tour->uuid) {
            throw new \Exception('id is not set');
        }
        $this->useDevice();

        $this->apiTester->callApiTourBookingV2(
            tourId: $tour->uuid,
            data: $this->bookingEndRequest(),
        );
        $this->apiTester->assertEquals(
            'processed',
            $this->apiTester->grabDataFromResponseByJsonPath('$.booking')[0],
        );
        unset($this->sessionContext->getSessionForStaff($this->alias)->tours[$tourAlias]);

        return $this;
    }

    public function bookOrderStart(string $tourAlias, int $orderPosNumber): self
    {
        $tour = $this->tourContext->getTour($tourAlias);
        $orderUuid = $tour->orders[$orderPosNumber]->uuid
            ?? throw new \Exception('order-uuid not set - call tour-details first!');

        $this->useDevice();
        $this->apiTester->callApiOrderBookingV2($orderUuid, $this->bookingStartRequest());

        $this->apiTester->waitUntil(
            function () use ($orderUuid): void {
                $this->apiTester->callApiOrderGetV2($orderUuid);
                $orderStatus = $this->apiTester->grabDataFromResponseByJsonPath('$.status')[0];
                $this->apiTester->assertEquals('started', $orderStatus);
            }
        );

        return $this;
    }

    public function focusOrder(string $tourAlias, int $orderPosNumber): self
    {
        $tour = $this->tourContext->getTour($tourAlias);
        $orderUuid = $tour->orders[$orderPosNumber]->uuid
            ?? throw new \Exception('order-uuid not set - call tour-details first!');

        $this->useDevice();
        $this->apiTester->callApiOrderFocusV2($orderUuid);

        return $this;
    }

    public function bookOrderEnd(string $tourAlias, int $orderPosNumber): self
    {
        $tour = $this->tourContext->getTour($tourAlias);
        $orderUuid = $tour->orders[$orderPosNumber]->uuid
            ?? throw new \Exception('order-uuid not set - call tour-details first!');

        $this->useDevice();
        $this->apiTester->callApiOrderBookingV2($orderUuid, $this->bookingEndRequest());

        return $this;
    }

    public function bookInEquipmentOfTour(string $tourAlias): self
    {
        foreach ($this->tourContext->getTour($tourAlias)->equipments as $equipmentAlias => $equipment) {
            $this->loginEquipmentToSession($equipmentAlias, $equipment);
        }

        return $this;
    }

    public function bookOutEquipmentOfTour(string $tourAlias): self
    {
        foreach ($this->tourContext->getTour($tourAlias)->equipments as $equipmentAlias => $equipment) {
            $this->logoutEquipmentFromSession($equipmentAlias);
        }

        return $this;
    }

    public function loginEquipmentToSession(string $equipmentAlias, TestTourEquipment $equipment): self
    {
        if (null === $equipment->uuid) {
            $equipment->uuid = $this->findEquipmentByExternalId(equipment: $equipment);
        }

        $this->useDevice();
        $this->apiTester->callApiEquipmentBookingV2(
            equipmentId: $equipment->uuid,
            data: $this->bookingStartRequest(false),
        );
        $this->apiTester->assertEquals(
            'processed',
            $this->apiTester->grabDataFromResponseByJsonPath('$.booking')[0],
        );
        $this->sessionContext->getSessionForStaff($this->alias)->equipments[$equipmentAlias] = $equipment;

        return $this;
    }

    public function logoutEquipmentFromSession(string $equipmentAlias): self
    {
        $this->useDevice();
        $session = $this->sessionContext->getSessionForStaff($this->alias);
        $equipment = $session->equipments[$equipmentAlias];
        if (null === $equipment->uuid) {
            throw new \Exception('equipment has no uuid - it can not be logged in!');
        }

        $this->apiTester->callApiEquipmentBookingV2(
            equipmentId: $equipment->uuid,
            data: $this->bookingEndRequest(),
        );
        $this->apiTester->assertEquals(
            'processed',
            $this->apiTester->grabDataFromResponseByJsonPath('$.booking')[0],
        );
        unset($session->equipments[$equipmentAlias]);

        return $this;
    }

    public function sendDeviceMessage(
        bool $withFile = false,
    ): self {
        $this->useDevice();
        $fileUuid = Uuid::v4()->toRfc4122();

        $data = ['message' => DataFaker::instance()->sentence(10), 'attachments' => []];

        if (true === $withFile) {
            $fileContent = file_get_contents(codecept_data_dir('small-img.png')) ?: '';
            $this->apiTester->callApiFileUploadInDirectoryV2(
                directory: 'device-message',
                uuid: $fileUuid,
                fileContent: $fileContent,
            );
            $data['attachments'] = ['device-message/'.$fileUuid];
        }

        $this->apiTester->callApiDeviceMessageCreateV2(
            data: json_encode($data) ?: '',
        );

        return $this;
    }

    public function getFaqForLanguage(string $language = 'de_DE'): self
    {
        $this->useDevice();
        $this->apiTester->callApiFaqGetLanguageV2($language);
        $data = $this->apiTester->grabDataFromResponseByJsonPath('$')[0];
        $this->apiTester->assertNotEmpty($data['questions']);

        return $this;
    }

    public function getFaqInfo(): self
    {
        $this->useDevice();
        $this->apiTester->callApiFaqGetInformationV2();
        $items = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($items as $item) {
            $this->apiTester->assertNotEmpty($item['iso']);
            $this->apiTester->assertNotEmpty($item['last_update']);
        }

        return $this;
    }

    public function sendFeedback(string $logFile, string $screenshotFile, ?string $comment = null): self
    {
        $this->useDevice();
        $this->uploadAppFile(str_replace('/', '', ObjectPrefix::APP_USER_FILES->value), $logFile, 'example.log');
        $this->uploadAppFile(str_replace('/', '', ObjectPrefix::APP_USER_FILES->value), $screenshotFile, 'small-img.png');

        $this->apiTester->callApiFeedbackCreateV2(
            data: json_encode([
                'log_file' => ObjectPrefix::APP_USER_FILES->value.$logFile,
                'screenshot_file' => ObjectPrefix::APP_USER_FILES->value.$screenshotFile,
                'comment' => $comment,
            ]) ?: '',
        );

        $this->apiTester->waitUntil(function () use ($comment): void {
            $this->apiTester->callApiFeedbackGetV2();
            $feedbacks = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
            assert(is_array($feedbacks));
            $comments = array_column($feedbacks, 'comment');
            $this->apiTester->assertContains($comment ?? '', $comments);
        });

        return $this;
    }

    /**
     * @param string[] $deviceAccessExpected
     */
    public function hasDeviceAccess(array $deviceAccessExpected): self
    {
        $this->useDevice();
        $this->apiTester->callApiGetDeviceAccessV2();

        $accessData = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($accessData as $access) {
            $this->apiTester->assertGreaterThan(0, $access['access_expires']);
        }
        $deviceAccessReceived = array_column($accessData, 'access_type');
        $this->apiTester->assertEmpty(
            array_merge(
                array_diff($deviceAccessExpected, $deviceAccessReceived),
                array_diff($deviceAccessReceived, $deviceAccessExpected),
            ),
            'device-access differs from expectation',
        );

        return $this;
    }

    public function getNotificationAccess(): self
    {
        $this->useDevice();
        $this->apiTester->callApiNotificationAccessV2();

        return $this;
    }

    public function sendTrackData(
        int $waypointCount,
        ?string $tourExternalId = null,
        ?string $equipmentExternalId = null,
    ): self {
        $this->useDevice();
        $this->apiTester->callApiTrackingV2(
            data: ReducableTrackingData::getData($waypointCount),
            equipmentExternalId: $equipmentExternalId,
            tourExternalId: $tourExternalId,
        );

        return $this;
    }

    public function useOutDatedAppVersion(): self
    {
        $this->appVersion = self::APP_VERSION_OUTDATED;

        return $this;
    }

    public function useOlderAppVersion(): self
    {
        $this->appVersion = self::APP_VERSION_BELOW_RECOMMENDED;

        return $this;
    }

    public function useNewAppVersion(): self
    {
        $this->appVersion = self::APP_VERSION_NEW;

        return $this;
    }

    public function getInteruptionTemplates(): self
    {
        $this->useDevice();
        $this->apiTester->callApiInterruptionTemplateGetV2();

        return $this;
    }

    public function getInteruptions(): self
    {
        $this->useDevice();
        $this->apiTester->callApiInterruptionGetV2();

        return $this;
    }

    public function findPhonebookContact(string $phonebookAlias, string $contactAlias): self
    {
        $this->useDevice();
        $this->apiTester->callApiPhonebookContactList();
        $contacts = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        $testContact = $this->phonebookContext->getPhonebook($phonebookAlias)->getContact($contactAlias);
        $found = false;
        foreach ($contacts as $contact) {
            if (
                $contact['first_name'] === $testContact->firstName
                && $contact['last_name'] === $testContact->lastName
                && $contact['country_prefix'] === $testContact->countryPrefix
                && $contact['phone_number'] === (string) $testContact->phoneNumber
                && $contact['emergency_contact'] === $testContact->emergencyContact
                && $contact['description'] === $testContact->description
            ) {
                $found = true;
                break;
            }
        }
        $this->apiTester->assertTrue($found);

        return $this;
    }

    private function uploadAppFile(string $dir, string $fileName, string $testFile): void
    {
        $this->useDevice();
        $fileContents = file_get_contents(codecept_data_dir($testFile)) ?: throw new \Exception('testfile not found');
        $mimeType = new MimeTypes()->guessMimeType(codecept_data_dir($testFile)) ?: 'application/octet-stream';
        $this->apiTester->callApiFileUploadInDirectoryV2($dir, $fileName, $fileContents);
        $this->apiTester->callApiFileRetrieveV2(ObjectPrefix::APP_USER_FILES->value.$fileName);
        $contentTypeHeader = $this->apiTester->grabHttpHeader('Content-Type');
        if (!is_string($contentTypeHeader)) {
            $this->apiTester->fail('no unambiguous content-type-header received');

            return;
        }
        $this->apiTester->assertStringContainsString($mimeType, $contentTypeHeader);
    }

    private function findEquipmentByExternalId(TestTourEquipment $equipment): string
    {
        $this->useDevice();
        $this->apiTester->callApiEquipmentListExtended(query: $equipment->equipmentExtId);
        $findings = $this->apiTester->grabDataFromResponseByJsonPath('$.items')[0];
        $this->apiTester->assertCount(1, $findings);

        return $findings[0]['uuid'];
    }

    private function useDevice(): void
    {
        if (null === $this->currentDeviceId) {
            throw new \Exception('app-acttor is not connected to a device');
        }
        $this->apiTester->amAuthenticatedAsGeneratedUserAndVerify(
            staffExtId: $this->staffContext->getStaff($this->alias)->staffExtId,
            tenant: $this->tenant,
        );
        $this->apiTester->amUsingDeviceWithId($this->currentDeviceId);
        $this->apiTester->haveHttpHeader('app-version', $this->appVersion);
    }

    private function bookingStartRequest(bool $force = true): string
    {
        $force = $force ? 'true' : 'false';

        return <<<JSON
        {
            "booking": "start",
            "force": {$force}
        }
        JSON;
    }

    private function bookingEndRequest(): string
    {
        return <<<JSON
        {
            "booking": "end",
            "force": true
        }
        JSON;
    }
}
