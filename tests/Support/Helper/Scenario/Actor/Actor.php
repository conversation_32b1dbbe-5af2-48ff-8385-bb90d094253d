<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Actor;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\Scenario\Context\BranchContext;
use App\Tests\Support\Helper\Scenario\Context\EquipmentContext;
use App\Tests\Support\Helper\Scenario\Context\PhonebookContext;
use App\Tests\Support\Helper\Scenario\Context\SessionContext;
use App\Tests\Support\Helper\Scenario\Context\StaffContext;
use App\Tests\Support\Helper\Scenario\Context\TourContext;
use App\Tests\Support\Helper\Scenario\TestScenario\TestScenario;

abstract class Actor
{
    protected ApiTester $apiTester;
    protected SessionContext $sessionContext;
    protected TourContext $tourContext;
    protected StaffContext $staffContext;
    protected EquipmentContext $equipmentContext;
    protected BranchContext $branchContext;
    protected PhonebookContext $phonebookContext;

    public function __construct(
        protected string $tenant,
        public string $alias,
        TestScenario $testScenario,
    ) {
        $this->apiTester = $testScenario->apiTester;
        $this->sessionContext = $testScenario->sessionContext;
        $this->tourContext = $testScenario->tourContext;
        $this->staffContext = $testScenario->staffContext;
        $this->equipmentContext = $testScenario->equipmentContext;
        $this->branchContext = $testScenario->branchContext;
        $this->phonebookContext = $testScenario->phonebookContext;
    }
}
