<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\TestScenario;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\Scenario\Context\BranchContext;
use App\Tests\Support\Helper\Scenario\Context\EquipmentContext;
use App\Tests\Support\Helper\Scenario\Context\PhonebookContext;
use App\Tests\Support\Helper\Scenario\Context\SessionContext;
use App\Tests\Support\Helper\Scenario\Context\StaffContext;
use App\Tests\Support\Helper\Scenario\Context\TourContext;

abstract class TestScenario
{
    public ApiTester $apiTester;
    public TourContext $tourContext;
    public SessionContext $sessionContext;
    public StaffContext $staffContext;
    public EquipmentContext $equipmentContext;
    public BranchContext $branchContext;

    public PhonebookContext $phonebookContext;

    public function _inject(
        TourContext $tourContext,
        SessionContext $sessionContext,
        StaffContext $staffContext,
        EquipmentContext $equipmentContext,
        BranchContext $branchContext,
        PhonebookContext $phonebookContext,
    ): void {
        $this->tourContext = $tourContext;
        $this->sessionContext = $sessionContext;
        $this->staffContext = $staffContext;
        $this->equipmentContext = $equipmentContext;
        $this->branchContext = $branchContext;
        $this->phonebookContext = $phonebookContext;
    }

    public function _before(ApiTester $apiTester): void
    {
        $this->apiTester = $apiTester;
    }
}
