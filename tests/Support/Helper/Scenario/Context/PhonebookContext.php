<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Context;

use App\Tests\Support\Helper\Scenario\IngestData\Phonebook\TestPhonebook;

class PhonebookContext
{
    /**
     * @var array<string, TestPhonebook>
     */
    public array $phonebooks;

    public function reset(): void
    {
        $this->phonebooks = [];
    }

    public function addPhonebook(string $alias, TestPhonebook $testPhonebook): void
    {
        if (isset($this->phonebooks[$alias])) {
            throw new \Exception(sprintf('phonebook with alias %s already exists', $alias));
        }

        $this->phonebooks[$alias] = $testPhonebook;
    }

    public function getPhonebook(string $alias): TestPhonebook
    {
        return $this->phonebooks[$alias] ?? throw new \Exception(sprintf('phonebook with alias %s does not exist', $alias));
    }

    public function removePhonebook(string $alias): void
    {
        if (!isset($this->phonebooks[$alias])) {
            throw new \Exception(sprintf('phonebook with alias %s does not exist', $alias));
        }

        unset($this->phonebooks[$alias]);
    }
}
