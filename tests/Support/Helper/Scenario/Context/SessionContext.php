<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Context;

use App\Tests\Support\Helper\Scenario\SessionData\TestSession;

class SessionContext
{
    /**
     * @var array<string, TestSession>
     */
    private array $testSessions = [];

    public function reset(): void
    {
        $this->testSessions = [];
    }

    public function addNewSession(string $alias, string $tenant): self
    {
        if (isset($this->testSessions[$alias])) {
            throw new \Exception(sprintf('session with alias %s already exists', $alias));
        }
        $this->testSessions[$alias] = new TestSession(tenant: $tenant);

        return $this;
    }

    public function getSession(string $alias): TestSession
    {
        return $this->testSessions[$alias] ?? throw new \Exception(sprintf('session with alias %s does not exist', $alias));
    }

    public function sessionExists(string $alias): bool
    {
        return isset($this->testSessions[$alias]);
    }

    public function getSessionForStaff(string $staffAlias): TestSession
    {
        foreach ($this->testSessions as $testSession) {
            foreach ($testSession->staff as $alias => $staff) {
                if ($staffAlias === $alias) {
                    return $testSession;
                }
            }
        }

        throw new \Exception('session for staff does not exists');
    }
}
