<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Context;

use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestBranch;

class BranchContext
{
    /**
     * @var array<string, TestBranch>
     */
    private array $branches = [];

    public function addBranch(string $alias, TestBranch $branch): void
    {
        $this->branches[$alias] = $branch;
    }

    public function getBranch(string $alias): TestBranch
    {
        return $this->branches[$alias] ?? throw new \Exception('branch is not set');
    }

    public function hasBranch(string $alias): bool
    {
        return isset($this->branches[$alias]);
    }

    public function setBranchIdByExternalId(string $id, string $externalId): bool
    {
        foreach ($this->branches as $alias => $testBranch) {
            if ($externalId === $testBranch->externalId) {
                $testBranch->uuid = $id;

                return true;
            }
        }

        return false;
    }
}
