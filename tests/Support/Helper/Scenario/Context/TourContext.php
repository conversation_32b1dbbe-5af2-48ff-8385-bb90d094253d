<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Context;

use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTour;

class TourContext
{
    /**
     * @var array<string, TestTour>
     */
    public array $tours = [];

    public function reset(): void
    {
        $this->tours = [];
    }

    public function addTour(string $alias, TestTour $testTour): void
    {
        if (isset($this->tours[$alias])) {
            throw new \Exception(sprintf('tour with alias %s already exists', $alias));
        }
        $this->tours[$alias] = $testTour;
    }

    public function getTour(string $alias): TestTour
    {
        return $this->tours[$alias] ?? throw new \Exception('tour '.$alias.' not set in context');
    }
}
