<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Context;

use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTourEquipment;

class EquipmentContext
{
    /**
     * @var array<string, TestTourEquipment>
     */
    public array $equipments = [];

    public function reset(): void
    {
        $this->equipments = [];
    }

    public function addEquipment(string $alias, TestTourEquipment $equipment): void
    {
        $this->equipments[$alias] = $equipment;
    }

    public function getEquipment(string $alias): TestTourEquipment
    {
        return $this->equipments[$alias] ?? throw new \Exception('equipment '.$alias.' not set in context');
    }

    public function hasEquipment(string $alias): bool
    {
        return isset($this->equipments[$alias]);
    }
}
