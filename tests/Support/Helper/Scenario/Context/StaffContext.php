<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\Context;

use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestTourStaff;

class StaffContext
{
    /**
     * @var array<string, TestTourStaff>
     */
    public array $staff = [];

    public function reset(): void
    {
        $this->staff = [];
    }

    public function addStaff(string $alias, TestTourStaff $staff): void
    {
        $this->staff[$alias] = $staff;
    }

    public function getStaff(string $alias): TestTourStaff
    {
        return $this->staff[$alias] ?? throw new \Exception('staff '.$alias.' not set in context');
    }

    public function hasStaff(string $alias): bool
    {
        return isset($this->staff[$alias]);
    }
}
