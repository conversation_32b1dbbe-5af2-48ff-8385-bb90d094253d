<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Phonebook;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use App\Tests\Support\Helper\Scenario\IngestData\Tour\TestBranch;

class TestPhonebook implements \JsonSerializable
{
    public ?string $uuid = null;

    /**
     * @var array<string, TestPhonebookContact>
     */
    public array $contacts = [];

    public function __construct(
        /**
         * @var TestBranch[]
         */
        public array $branches,
        public ?string $name = null,
        public ?string $description = null,
    ) {
        $this->name = $this->name ?? DataFaker::instance()->city();
        $this->description = $this->description ?? DataFaker::instance()->sentence();
    }

    public function addContact(string $contactAlias, TestPhonebookContact $contact): self
    {
        if (isset($this->contacts[$contactAlias])) {
            throw new \Exception(sprintf('contact with alias %s already set', $contactAlias));
        }
        $this->contacts[$contactAlias] = $contact;

        return $this;
    }

    public function getContact(string $contactAlias): TestPhonebookContact
    {
        return $this->contacts[$contactAlias] ?? throw new \Exception(sprintf('contact %s not set', $contactAlias));
    }

    public function removeContact(string $contactAlias): self
    {
        if (!isset($this->contacts[$contactAlias])) {
            throw new \Exception(sprintf('contact % is not set', $contactAlias));
        }
        unset($this->contacts[$contactAlias]);

        return $this;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'branches' => array_map(
                fn (TestBranch $testBranch): string => $testBranch->uuid ?? throw new \Exception('Branch of phonebook has no uuid yet'),
                $this->branches,
            ),
        ];
    }
}
