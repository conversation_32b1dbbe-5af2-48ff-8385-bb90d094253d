<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Phonebook;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestPhonebookContact implements \JsonSerializable
{
    public ?string $uuid = null;

    public function __construct(
        public ?string $firstName = null,
        public ?string $lastName = null,
        public ?string $countryPrefix = null,
        public ?int $phoneNumber = null,
        public ?bool $emergencyContact = null,
        public ?string $description = null,
    ) {
        $this->firstName = $this->firstName ?? DataFaker::instance()->firstName();
        $this->lastName = $this->lastName ?? DataFaker::instance()->lastName();
        $this->countryPrefix = $this->countryPrefix ?? DataFaker::instance()->numerify('+##');
        $this->phoneNumber = $this->phoneNumber ?? DataFaker::instance()->numberBetween(1000, 9999);
        $this->emergencyContact = $this->emergencyContact ?? DataFaker::instance()->boolean();
        $this->description = $this->description ?? DataFaker::instance()->sentence();
    }

    public function jsonSerialize(): mixed
    {
        $data = [
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'country_prefix' => $this->countryPrefix,
            'phone_number' => $this->phoneNumber,
            'emergency_contact' => $this->emergencyContact,
            'description' => $this->description,
        ];
        if (null !== $this->uuid) {
            $data['id'] = $this->uuid;
        }

        return $data;
    }
}
