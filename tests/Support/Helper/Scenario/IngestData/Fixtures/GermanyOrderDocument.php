<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Fixtures;

class GermanyOrderDocument
{
    public static function getDocumentContentData(): string
    {
        return <<<JSON
          {
            "deliveryServices": [
                {
                    "materialNumber": "969595",
                    "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam hendrerit libero tincidunt, accumsan lacus sit amet, placerat odio. Curabitur arcu dolor, luctus nec consectetur viverra, rhoncus sit amet odio.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list",
                    "unit": "kg"
                },
                {
                    "materialNumber": "456784",
                    "text": "Integer pulvinar eros non arcu faucibus euismod ut sed turpis. <PERSON><PERSON>a sollicitudin at magna id pellentesque. Duis facilisis ut nisi a molestie.",
                    "taskExternalId": "pdfT2",
                    "elementReferenceType": "scale_weighing_data",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "456841",
                    "text": "Natus consequatur reiciendis sit et.",
                    "taskExternalId": "pdfT3",
                    "elementReferenceType": "scale_weighing_data",
                    "templateField": "material-list",
                    "unit": "test"
                },
                {
                    "materialNumber": "123123",
                    "text": "Duis feugiat justo ac porttitor elementum. Duis et ultricies elit. Nam mattis nisi sed tellus iaculis, id hendrerit dolor tincidunt.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "9632025",
                    "text": "Mauris quis convallis tortor.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "7854521",
                    "text": "Integer at arcu velit.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "12341",
                    "text": "Nullam aliquam massa eu urna mattis, nec scelerisque urna rutrum.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "8452134",
                    "text": "Nullam accumsan, libero sed congue sodales, neque felis aliquet mi, vitae ultricies metus sem quis nibh.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list",
                    "unit": "really? anything?"
                },
                {
                    "materialNumber": "123456789",
                    "text": "Etiam lacus nisi, tincidunt sollicitudin neque eu, congue volutpat mauris. Nulla pellentesque iaculis neque sit amet volutpat. Nam lacinia metus tellus, vel bibendum felis tempor at.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "969595",
                    "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam hendrerit libero tincidunt, accumsan lacus sit amet, placerat odio. Curabitur arcu dolor, luctus nec consectetur viverra, rhoncus sit amet odio.",
                    "taskExternalId": "pdfT1",
                    "templateField": "material-list",
                    "elementReferenceType": "containerAmount",
                    "unit": "give me a break :D"
                },
                {
                    "materialNumber": "456784",
                    "text": "Integer pulvinar eros non arcu faucibus euismod ut sed turpis. Nulla sollicitudin at magna id pellentesque. Duis facilisis ut nisi a molestie.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "456841",
                    "text": "Natus consequatur reiciendis sit et.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "123123",
                    "text": "Duis feugiat justo ac porttitor elementum. Duis et ultricies elit. Nam mattis nisi sed tellus iaculis, id hendrerit dolor tincidunt.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "9632025",
                    "text": "Mauris quis convallis tortor.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "7854521",
                    "text": "Integer at arcu velit.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "12341",
                    "text": "Nullam aliquam massa eu urna mattis, nec scelerisque urna rutrum.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "969595",
                    "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam hendrerit libero tincidunt, accumsan lacus sit amet, placerat odio. Curabitur arcu dolor, luctus nec consectetur viverra, rhoncus sit amet odio.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "456784",
                    "text": "Integer pulvinar eros non arcu faucibus euismod ut sed turpis. Nulla sollicitudin at magna id pellentesque. Duis facilisis ut nisi a molestie.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "456841",
                    "text": "Natus consequatur reiciendis sit et.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "123123",
                    "text": "Duis feugiat justo ac porttitor elementum. Duis et ultricies elit. Nam mattis nisi sed tellus iaculis, id hendrerit dolor tincidunt.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "9632025",
                    "text": "Mauris quis convallis tortor.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "7854521",
                    "text": "Integer at arcu velit.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "12341",
                    "text": "Nullam aliquam massa eu urna mattis, nec scelerisque urna rutrum.",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "containerAmount",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "000110022",
                    "text": "i have an empty ref-type :(",
                    "taskExternalId": "pdfT1",
                    "elementReferenceType": "",
                    "templateField": "material-list"
                },
                {
                    "materialNumber": "000110023",
                    "text": "i have no ref-type at all :(",
                    "taskExternalId": "pdfT1",
                    "templateField": "material-list"
                }
            ],
            "textBlocks": [
                {
                    "blockId": "header-1",
                    "text": "PreZero Service Westfalen GmbH & Co. KG\\nAn der Pforte 2, 32457 Porta <br>Westfalica\\npls regard"
                },
                {
                    "blockId": "footer-1",
                    "text": "Placeat rerum ut et enim ex eveniet facere sunt quia delectus aut nam et eum architecto fugit repellendus illo veritatis qui ex esse."
                },
                {
                    "blockId": "footer-2",
                    "text": "Voluptate vel possimus omnis aut incidunt sunt cumque asperiores incidunt iure sequi cum."
                },
                {
                    "blockId": "footer-3",
                    "text": "Rem aut rerum exercitationem est rem dicta voluptas fuga totam reiciendis qui architecto fugiat nemo omnis consequatur recusandae qui cupiditate eos quod."
                },
                {
                    "blockId": "footer-4",
                    "text": "Vel\\n optio provident non incidunt magnam molestias et quibusdam et ab quo voluptatum quia."
                },
                {
                    "blockId": "tos",
                    "text": "Es gelten die Allgemeinen Geschäftsbedingungen des Auftragnehmers, diese\\nkönnen angefordert oder im Internet unter www.prezero.com eingesehen\\nwerden."
                },
                {
                    "blockId": "deliveryNoteInfo",
                    "text": "some new \\ntext inside \\nshould do the job Voluptatibus est accusantium eveniet aut atque possimus aut dolores quis totam incidunt ducimus aperiam nesciunt est quia assumenda minima sunt qui similique.Voluptatibus est accusantium eveniet aut atque."
                }
            ]
          }
        JSON;
    }
}
