<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Fixtures;

class AppLogData
{
    public static function getLogData(): string
    {
        return json_encode([
            'logs' => [
                [
                    'message' => 'Info message',
                    'logLevel' => 'info',
                    'timestamp' => '2021-01-01T00:00:00.000+00:00',
                ],
                [
                    'message' => 'Error message',
                    'logLevel' => 'error',
                    'timestamp' => '2021-01-05T00:00:00.000+00:00',
                ],
            ],
        ]) ?: throw new \Exception('invalid json data');
    }
}
