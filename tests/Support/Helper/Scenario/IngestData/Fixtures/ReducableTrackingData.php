<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Fixtures;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class ReducableTrackingData
{
    public static function getData(int $waypointCount): string
    {
        $faker = DataFaker::instance();
        $locations = [];
        $startDateTime = \DateTimeImmutable::createFromInterface($faker->dateTimeThisMonth());

        if ($waypointCount < 4) {
            throw new \Exception('can not include reducable data in less than 4 waypoints');
        }

        // 1. Reducible Pair (<20m, <45deg bearing diff)
        $locations[] = [
            'latitude' => 52.51630,
            'longitude' => 13.37770,
            'bearing' => rand(0, 360),
            'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
            'protected' => false,
        ];

        // ~7m away, bearing diff 5 deg
        $startDateTime = $startDateTime->add(new \DateInterval('PT1M'));
        $locations[] = [
            'latitude' => 52.51635,
            'longitude' => 13.37775,
            'bearing' => 15,
            'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
            'protected' => false,
        ];
        // 2. Non-Reducible Pair (>=20m OR >=45deg bearing diff)
        // Using distance >= 20m for first non-reducible pair
        $startDateTime = $startDateTime->add(new \DateInterval('PT5M'));
        $locations[] = [
            'latitude' => 52.51800, // Further away from point 2
            'longitude' => 13.38000,
            'bearing' => 30,
            'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
            'protected' => false,
        ];
        // Using bearing >= 45 deg for second non-reducible pair (close distance)
        $startDateTime = $startDateTime->add(new \DateInterval('PT5M'));
        $locations[] = [
            'latitude' => 52.51805, // Close to point 3
            'longitude' => 13.38005,
            'bearing' => 90, // 30 -> 90 = 60 deg diff >= 45
            'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
            'protected' => false,
        ];

        for ($i = 4; $i < $waypointCount; ++$i) {
            $startDateTime = $startDateTime->add(new \DateInterval('PT'.rand(1, 10).'M'));

            $locations[] = [
                'latitude' => $faker->latitude(),
                'longitude' => $faker->longitude(),
                'bearing' => rand(0, 360),
                'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
                'protected' => $faker->boolean(),
            ];
        }

        return json_encode(['locations' => $locations]) ?: throw new \Exception('invalid json');
    }
}
