<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use App\Tests\Support\Helper\Scenario\IngestData\Fixtures\GermanyOrderDocument;

class TestDocument
{
    public const string DOCUMENT_TYPE_GERMANY_ORDER_DOCUMENT = 'delivery_note';
    /**
     * @var TestDocumentDeliveryService[]
     */
    public array $deliveryServices = [];

    /**
     * @var TestDocumentTextBlock[]
     */
    public array $textBlocks = [];

    public function __construct(
        public string $documentType,
        public ?string $documentName = null,
    ) {
        $this->documentName = $this->documentName ?? DataFaker::instance()->domainWord();
    }

    public function setContentGermanDeliveryNote(): void
    {
        $data = json_decode(
            GermanyOrderDocument::getDocumentContentData(),
            true,
        );
        foreach ($data['deliveryServices'] as $deliveryService) {
            $this->deliveryServices[] = new TestDocumentDeliveryService(
                materialNumber: $deliveryService['materialNumber'],
                text: $deliveryService['text'],
                taskExternalId: $deliveryService['taskExternalId'],
                elementReferenceType: $deliveryService['elementReferenceType'] ?? null,
                unit: $deliveryService['unit'] ?? null,
                templateField: $deliveryService['templateField'],
            );
        }
        foreach ($data['textBlocks'] as $textBlock) {
            $this->textBlocks[] = new TestDocumentTextBlock(
                blockId: $textBlock['blockId'],
                text: $textBlock['text'],
            );
        }
    }
}
