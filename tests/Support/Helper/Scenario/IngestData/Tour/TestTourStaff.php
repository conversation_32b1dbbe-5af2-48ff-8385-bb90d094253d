<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestTourStaff
{
    public function __construct(
        public string $staffExtId,
        public ?string $firstName = null,
        public ?string $lastName = null,
        public string $personnelType = 'DRIVER',
    ) {
        $this->firstName = $this->firstName ?? DataFaker::instance()->firstName();
        $this->lastName = $this->lastName ?? DataFaker::instance()->lastName();
    }
}
