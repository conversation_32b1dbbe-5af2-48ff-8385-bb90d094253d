<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

class TestOrderLocation
{
    /**
     * @var TestAdditionalInformation[]
     */
    public array $additionalInformation = [];

    /**
     * @var TestOrderLocationTask[]
     */
    public array $tasks = [];

    public function __construct(
        public string $orderLocationType,
        public ?string $locationExtId = null,
        int $additionalInformationCount = 2,
        int $taskCount = 0,
    ) {
        $this->locationExtId = $this->locationExtId ?? 'location-'.uniqid();
        for ($i = 0; $i < $additionalInformationCount; ++$i) {
            $this->additionalInformation[] = new TestAdditionalInformation(sequence: ($i + 1) * 10);
        }
        for ($i = 0; $i < $taskCount; ++$i) {
            $this->tasks[] = new TestOrderLocationTask();
        }
    }
}
