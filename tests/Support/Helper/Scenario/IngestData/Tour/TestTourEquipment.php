<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestTourEquipment implements \JsonSerializable
{
    public function __construct(
        public string $equipmentExtId,
        public string $licencePlate,
        public ?string $equipmentType = null,
        public ?int $height = null,
        public ?int $length = null,
        public ?int $width = null,
        public ?int $weight = null,
        public ?int $minimumLoad = null,
        public ?int $overload = null,
        public ?int $totalPermissibleWeight = null,
        public ?int $maxAxleLoad = null,
        public string $containerMounting = '',
        public ?string $uuid = null,
    ) {
        $this->height = $this->height ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->length = $this->length ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->width = $this->width ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->weight = $this->weight ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->minimumLoad = $this->minimumLoad ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->overload = $this->overload ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->totalPermissibleWeight = $this->totalPermissibleWeight ?? DataFaker::instance()->numberBetween(1000, 5000);
        $this->maxAxleLoad = $this->maxAxleLoad ?? DataFaker::instance()->numberBetween(1000, 5000);
        $randomType = DataFaker::instance()->randomElement(EquipmentType::cases());
        assert($randomType instanceof EquipmentType);
        $this->equipmentType = $randomType->value;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'equipmentExtId' => $this->equipmentExtId,
            'licencePlate' => $this->licencePlate,
            'height' => $this->height,
            'length' => $this->length,
            'width' => $this->width,
            'weight' => $this->weight,
            'minimumLoad' => $this->minimumLoad,
            'overload' => $this->overload,
            'totalPermissibleWeight' => $this->totalPermissibleWeight,
            'maxAxleLoad' => $this->maxAxleLoad,
            'equipmentType' => $this->equipmentType,
            'containerMounting' => $this->containerMounting,
        ];
    }
}
