<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestTourAddress
{
    public function __construct(
        public string $addressExtId,
        public string $country,
        public ?string $postalCode = null,
        public ?string $city = null,
        public ?string $district = null,
        public ?string $street = null,
        public ?string $houseNumber = null,
        public ?string $phoneNumber = null,
        public ?string $state = null,
    ) {
        $this->postalCode = $this->postalCode ?? DataFaker::instance()->postcode();
        $this->city = $this->city ?? DataFaker::instance()->city();
        $this->district = $this->district ?? DataFaker::instance()->citySuffix();
        $this->street = $this->street ?? DataFaker::instance()->streetName();
        $this->houseNumber = $this->houseNumber ?? (string) DataFaker::instance()->numberBetween(1, 100);
        $this->phoneNumber = $this->phoneNumber ?? DataFaker::instance()->phoneNumber();
        $this->state = $this->state ?? DataFaker::instance()->country();
    }
}
