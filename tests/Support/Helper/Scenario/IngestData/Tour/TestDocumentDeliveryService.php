<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

class TestDocumentDeliveryService implements \JsonSerializable
{
    public function __construct(
        public string $materialNumber,
        public string $text,
        public string $taskExternalId,
        public ?string $elementReferenceType,
        public ?string $unit,
        public string $templateField,
    ) {
    }

    public function jsonSerialize(): mixed
    {
        $ret = [
            'materialNumber' => $this->materialNumber,
            'text' => $this->text,
            'taskExternalId' => $this->taskExternalId,
            'templateField' => $this->templateField,
        ];
        if (null !== $this->unit) {
            $ret['unit'] = $this->unit;
        }
        if (null !== $this->elementReferenceType) {
            $ret['elementReferenceType'] = $this->elementReferenceType;
        }

        return $ret;
    }
}
