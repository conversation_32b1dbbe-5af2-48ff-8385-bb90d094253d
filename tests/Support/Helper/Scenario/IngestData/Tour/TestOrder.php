<?php

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Domain\Entity\Enum\Types\TourOrderType;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestOrder implements \JsonSerializable
{
    /**
     * @var TestAdditionalInformation[]
     */
    public array $additionalInformation = [];

    /**
     * @var array<string, TestOrderLocation>
     */
    public array $orderLocations = [];

    /**
     * @var TestDocument[]
     */
    public array $orderDocuments = [];

    public ?string $uuid = null;

    /**
     * @param string[] $orderLocationTypes
     */
    public function __construct(
        public int $orderPosNr,
        public ?string $orderExtId = null,
        public ?string $orderType = null,
        public ?string $contractNumber = null,
        public ?string $transportNumber = null,
        public ?string $materialExternalId = null,
        public ?string $purchaseOrder = null,
        public ?string $locationExtId = null,
        public ?string $customerExtId = null,
        int $additionalInformationCount = 2,
        array $orderLocationTypes = ['CUSTOMER', 'DEPOT', 'DISPOSALSITE'],
    ) {
        $this->orderExtId = $this->orderExtId ?? 'or-'.uniqid();
        $randomCase = DataFaker::instance()->randomElement(TourOrderType::cases());
        assert($randomCase instanceof TourOrderType);
        $this->orderType = $this->orderType ?? $randomCase->value;
        $this->contractNumber = $this->contractNumber ?? 'cn-'.uniqid();
        $this->transportNumber = $this->transportNumber ?? 'tn-'.uniqid();
        $this->materialExternalId = $this->materialExternalId ?? 'material-'.uniqid();
        $this->purchaseOrder = $this->purchaseOrder ?? 'po-'.uniqid();
        $this->locationExtId = $this->locationExtId ?? 'location-'.uniqid();
        $this->customerExtId = $this->customerExtId ?? 'customer-'.uniqid();

        for ($i = 0; $i < $additionalInformationCount; ++$i) {
            $this->additionalInformation[] = new TestAdditionalInformation(sequence: ($i + 1) * 10);
        }

        $orderLocationTypes = array_values(array_unique($orderLocationTypes));
        for ($i = 0; $i < count($orderLocationTypes); ++$i) {
            $this->orderLocations[$orderLocationTypes[$i]] = new TestOrderLocation(
                orderLocationType: $orderLocationTypes[$i],
                additionalInformationCount: DataFaker::instance()->numberBetween(0, 3),
            );
        }
    }

    public function jsonSerialize(): array
    {
        return [
            'orderExtId' => $this->orderExtId,
            'orderPosNr' => $this->orderPosNr,
            'orderType' => $this->orderType,
            'contractNumber' => $this->contractNumber,
            'transportNumber' => $this->transportNumber,
            'materialExternalId' => $this->materialExternalId,
            'purchaseOrder' => $this->purchaseOrder,
            'locationExtId' => $this->locationExtId,
            'customerExtId' => $this->customerExtId,
            'additionalInformation' => $this->additionalInformation,
            'orderLocations' => array_values($this->orderLocations),
            'orderDocuments' => $this->orderDocuments,
        ];
    }
}
