<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestAdditionalInformation
{
    public function __construct(
        public int $sequence,
        public ?string $text = null,
        public ?bool $alsoFrontView = null,
        public ?bool $bold = null,
        public ?string $icon = null,
    ) {
        $text = DataFaker::instance()->words(3, true);
        assert(is_string($text));
        $this->text = $this->text ?? $text;
        $this->alsoFrontView = $this->alsoFrontView ?? DataFaker::instance()->boolean();
        $this->bold = $this->bold ?? DataFaker::instance()->boolean();
        $this->icon = $this->icon ?? DataFaker::instance()->word();
    }
}
