<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Domain\Entity\Enum\Country;
use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestTour implements \JsonSerializable
{
    /**
     * @var array<string, TestTourEquipment>
     */
    public array $equipments = [];

    /**
     * @var array<string, TestTourStaff>
     */
    public array $staff = [];

    public ?string $uuid = null;

    /**
     * @var array<int, TestOrder>
     */
    public array $orders = [];

    public array $disposalSites = [];

    /**
     * @var array<string, TestTourAddress>
     */
    public array $addresses = [];

    /**
     * @var TestAdditionalInformation[]
     */
    public array $additionalInformation = [];

    /**
     * @var array<string, TestTourCustomer>
     */
    public array $customers = [];

    public TestBranch $branch;

    /**
     * @param array<int, array{alias: string, existingObject: ?TestTourEquipment}> $equipments
     * @param array<int, array{alias: string, existingObject: ?TestTourStaff}>     $staff
     * @param array{alias: string, existingObject: ?TestBranch}                    $branch
     */
    public function __construct(
        array $equipments,
        array $staff,
        array $branch,
        public int $orderCount = 1,
        public ?string $tourExtId = null,
        public ?string $name = null,
        public ?string $country = null,
        public ?string $startAddressExtId = null,
        public ?string $endAddressExtId = null,
        public ?\DateTimeImmutable $startTimestamp = null,
        public ?\DateTimeImmutable $endTimestamp = null,
        public ?bool $forcedOrder = null,
        public ?string $interruptionExternalId = null,
    ) {
        $this->tourExtId = $this->tourExtId ?? 'tour-ext-id-'.uniqid();
        $this->name = $this->name ?? 'tour '.DataFaker::instance()->word().' '.uniqid();
        $this->startAddressExtId = $this->startAddressExtId ?? 'location-'.uniqid();
        $this->endAddressExtId = $this->endAddressExtId ?? 'location-'.uniqid();
        $this->startTimestamp = $this->startTimestamp ?? new \DateTimeImmutable()->setTime(0, 0, 0);
        $this->endTimestamp = $this->endTimestamp ?? new \DateTimeImmutable()->setTime(23, 59, 59);
        $this->forcedOrder = $this->forcedOrder ?? DataFaker::instance()->boolean();
        $this->interruptionExternalId = $this->interruptionExternalId ?? 'int-'.uniqid();
        $randomCountry = DataFaker::instance()->randomElement(Country::cases());
        assert($randomCountry instanceof Country);
        $this->country = $this->country ?? $randomCountry->value;

        if ($branch['existingObject'] instanceof TestBranch) {
            $this->branch = $branch['existingObject'];
        } else {
            $this->branch = new TestBranch(
                externalId: 'branch-ext-id-'.uniqid(),
            );
        }

        foreach ($equipments as $equipment) {
            if ($equipment['existingObject'] instanceof TestTourEquipment) {
                $this->equipments[$equipment['alias']] = $equipment['existingObject'];
            } else {
                $this->equipments[$equipment['alias']] = new TestTourEquipment(
                    equipmentExtId: 'eq-ext-id-'.uniqid(),
                    licencePlate: DataFaker::instance()->countryCode().'-'.DataFaker::instance()->numberBetween(1, 1000),
                );
            }
        }
        foreach ($staff as $staffMember) {
            if ($staffMember['existingObject'] instanceof TestTourStaff) {
                $this->staff[$staffMember['alias']] = $staffMember['existingObject'];
            } else {
                $this->staff[$staffMember['alias']] = new TestTourStaff(
                    staffExtId: 'staff-ext-id-'.uniqid(),
                );
            }
        }
        for ($i = 0; $i < $this->orderCount; ++$i) {
            $this->orders[$i + 1] = new TestOrder($i + 1);
        }

        $addInfoCount = DataFaker::instance()->numberBetween(1, 3);
        for ($i = 0; $i < $addInfoCount; ++$i) {
            $this->additionalInformation[] = new TestAdditionalInformation($i * 10);
        }

        $this->updateAddresses();
        $this->updateCustomers();
    }

    private function updateCustomers(): void
    {
        $customerExternalIds = [];
        foreach ($this->orders as $order) {
            $customerExternalIds[] = $order->customerExtId;
        }
        /**
         * @var string[] $customerExternalIds
         */
        $customerExternalIds = array_values(array_unique($customerExternalIds));

        foreach ($this->customers as $existingCustomerId => $existingCustomerData) {
            if (!in_array($existingCustomerId, $customerExternalIds)) {
                unset($this->customers[$existingCustomerId]);
            }
        }

        foreach ($customerExternalIds as $customerExternalId) {
            if (!isset($this->customers[$customerExternalId])) {
                $this->customers[$customerExternalId] = new TestTourCustomer($customerExternalId);
            }
        }
    }

    private function updateAddresses(): void
    {
        $addressExternalIds = [$this->startAddressExtId, $this->endAddressExtId];
        foreach ($this->orders as $order) {
            $addressExternalIds[] = $order->locationExtId;
            foreach ($order->orderLocations as $orderLocation) {
                $addressExternalIds[] = $orderLocation->locationExtId;
            }
        }
        /**
         * @var string[] $addressExternalIds
         */
        $addressExternalIds = array_values(array_unique($addressExternalIds));
        foreach ($this->addresses as $existingAddressId => $existingAddress) {
            if (!in_array($existingAddressId, $addressExternalIds)) {
                unset($this->addresses[$existingAddressId]);
            }
        }
        foreach ($addressExternalIds as $addressExternalId) {
            if (!isset($this->addresses[$addressExternalId])) {
                $randomCountry = DataFaker::instance()->randomElement(Country::cases());
                assert($randomCountry instanceof Country);
                $this->addresses[$addressExternalId] = new TestTourAddress($addressExternalId, $randomCountry->value);
            }
        }
    }

    /**
     * @throws \Exception
     */
    public function getTourJson(): string
    {
        return json_encode($this) ?: throw new \Exception('invalid json');
    }

    public function jsonSerialize(): mixed
    {
        return [
            'tourExtId' => $this->tourExtId,
            'name' => $this->name,
            'startTimestamp' => $this->startTimestamp?->format(DATE_ATOM),
            'endTimestamp' => $this->endTimestamp?->format(DATE_ATOM),
            'startAddressExtId' => $this->startAddressExtId,
            'endAddressExtId' => $this->endAddressExtId,
            'branchExternalId' => $this->branch->externalId,
            'country' => $this->country,
            'equipments' => array_values($this->equipments),
            'staff' => array_values($this->staff),
            'addresses' => array_values($this->addresses),
            'customers' => array_values($this->customers),
            'orders' => array_values($this->orders),
            'additionalInformation' => $this->additionalInformation,
            'forcedOrder' => $this->forcedOrder,
            'interruptionExternalId' => $this->interruptionExternalId,
            'disposalSites' => $this->disposalSites,
        ];
    }

    /**
     * @throws \Exception
     */
    public function hasOrderDocumentForGermany(int $orderPosNumber = 1, int $documentCount = 1): self
    {
        if (!isset($this->orders[$orderPosNumber])) {
            throw new \Exception('order for document not set!');
        }
        for ($i = 0; $i < $documentCount; ++$i) {
            $orderDocument = new TestDocument(TestDocument::DOCUMENT_TYPE_GERMANY_ORDER_DOCUMENT);
            $orderDocument->setContentGermanDeliveryNote();
            $this->orders[$orderPosNumber]->orderDocuments[] = $orderDocument;
        }

        return $this;
    }

    public function matchesGermanyTourConfig(): self
    {
        $this->country = Country::DE->value;
        $this->addresses[$this->startAddressExtId]->country = Country::DE->value;

        return $this;
    }

    public function matchesGermanyOrderConfig(): self
    {
        foreach ($this->orders as $orderPosNumber => $order) {
            $this->orders[$orderPosNumber]->orderType = '12';
        }

        $this->country = Country::DE->value;
        $this->addresses[$this->startAddressExtId]->country = Country::DE->value;
        array_values($this->equipments)[0]->equipmentType = EquipmentType::ASK->value;

        return $this;
    }
}
