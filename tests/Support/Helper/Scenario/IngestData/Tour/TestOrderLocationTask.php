<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Scenario\IngestData\Tour;

use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;

class TestOrderLocationTask
{
    public function __construct(
        public ?string $taskType = null,
        public ?string $taskName = null,
        public ?string $taskExternalId = null,
        public array $elements = [],
    ) {
        $this->taskType = $this->taskType ?? strtolower(DataFaker::instance()->word);
        $this->taskName = $this->taskName ?? DataFaker::instance()->word;
        $this->taskExternalId = $this->taskExternalId ?? strtolower(DataFaker::instance()->word);
    }
}
