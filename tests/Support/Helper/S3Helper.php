<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

use Aws\Credentials\Credentials;
use Aws\S3\S3Client;

class S3Helper
{
    private static S3Client $s3Client;

    public static function getS3Client(): S3Client
    {
        if (!isset(self::$s3Client)) {
            self::$s3Client = new S3Client([
                'version' => 'latest',
                'region' => 'us-east-1',
                'credentials' => new Credentials(
                    key: getenv('S3_ACCESS_KEY'),
                    secret: getenv('S3_SECRET_KEY'),
                ),
                'bucket_endpoint' => false,
                'use_path_style_endpoint' => true,
                'endpoint' => getenv('S3_ENDPOINT_URL'),
            ]);
        }

        return self::$s3Client;
    }
}
