<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

trait CentrifugoTrait
{
    public function assertCentrifugoReceivedChannelMessage(
        string $channel,
        array $messageParts,
    ): void {
        $history = $this->grabCentrifugoHistory(channel: $channel, limit: 10);
        $this->assertNotEmpty($history);
        $this->assertGreaterOrEquals(1, count($history));
        $hasMatch = false;

        foreach ($history as $historyItem) {
            $receivedMessage = $historyItem['data'];

            foreach ($messageParts as $key => $value) {
                if (!array_key_exists($key, $receivedMessage)) {
                    continue 2; // Skip to the next history item if key is not found
                }

                if ($receivedMessage[$key] !== $value) {
                    continue 2; // Skip to the next history item if value does not match
                }
            }

            $hasMatch = true;
            break;
        }

        $this->assertTrue(
            $hasMatch,
            sprintf(
                'Expected message with parts %s to be found in channel %s history, but it was not found.',
                json_encode($messageParts, JSON_PRETTY_PRINT),
                $channel
            )
        );
    }

    public function assertCentrifugoDidNotReceiveChannelMessage(
        string $channel,
        array $messageParts,
    ): void {
        $history = $this->grabCentrifugoHistory(channel: $channel, limit: 10);

        if (empty($history)) {
            // No messages at all, so the assertion passes
            return;
        }

        $hasMatch = false;

        foreach ($history as $historyItem) {
            $receivedMessage = $historyItem['data'];

            foreach ($messageParts as $key => $value) {
                if (!array_key_exists($key, $receivedMessage)) {
                    continue 2; // Skip to the next history item if key is not found
                }

                if ($receivedMessage[$key] !== $value) {
                    continue 2; // Skip to the next history item if value does not match
                }
            }

            $hasMatch = true;
            break;
        }

        $this->assertFalse(
            $hasMatch,
            sprintf(
                'Expected message with parts %s NOT to be found in channel %s history, but it was found.',
                json_encode($messageParts, JSON_PRETTY_PRINT),
                $channel
            )
        );
    }

    public function clearCentrifugoMessages(): void
    {
        // This method would ideally clear the Centrifugo message history
        // For now, we'll just add a small delay to separate test messages
        sleep(1);
    }
}
