<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I

use App\Tests\Support\InMemoryCache;
use Codeception\Module\REST;
use GuzzleHttp\Psr7\Response;
use League\OpenAPIValidation\PSR7\Exception\ValidationFailed;
use League\OpenAPIValidation\PSR7\OperationAddress;
use League\OpenAPIValidation\PSR7\ValidatorBuilder;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface as HttpClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\HttpExceptionInterface;

class Api extends \Codeception\Module
{
    protected ValidatorBuilder $validatorBuilder;
    protected ValidatorBuilder $portalValidatorBuilder;

    /**
     * @var string
     */
    private const OAUTH2_CLIENT_ID = 'hermes';

    /**
     * @var string
     */
    private const OAUTH2_CLIENT_SECRET = '$78Nine.';

    /**
     * @var string
     */
    private const USER1_USERNAME = 'api-user-1';

    /**
     * @var string
     */
    public const USER1_PASSWORD = 'qwerty';

    /**
     * @var string
     */
    private const USER2_USERNAME = 'api-user-2';

    /**
     * @var string
     */
    private const USER2_PASSWORD = 'qwerty';

    /**
     * @var string
     */
    private const USER3_USERNAME = 'api-user-3';

    /**
     * @var string
     */
    private const USER3_PASSWORD = 'qwerty';

    private const USER4_USERNAME = 'api-user-4';
    private const USER4_PASSWORD = 'qwerty';

    private const NETHERLANDS_USER1_USERNAME = 'nl-user-1';
    private const NETHERLANDS_USER1_PASSWORD = 'qwerty';

    private const SPAIN_USER1_USERNAME = 'es-user-1';
    private const SPAIN_USER1_PASSWORD = 'qwerty';

    public function amAuthenticatedAsGeneratedUser(string $staffId, string $tenant): void
    {
        $this->amAuthenticatedAsDriverUser($staffId, $tenant.$staffId, $staffId);
    }

    public function amAuthenticatedAsApiUser1(): void
    {
        $this->amAuthenticatedAsDriverUser('api-user-1-token', self::USER1_USERNAME, self::USER1_PASSWORD);
    }

    public function amAuthenticatedAsApiUser2(): void
    {
        $this->amAuthenticatedAsDriverUser('api-user-2-token', self::USER2_USERNAME, self::USER2_PASSWORD);
    }

    public function amAuthenticatedAsApiUser3(): void
    {
        $this->amAuthenticatedAsDriverUser('api-user-3-token', self::USER3_USERNAME, self::USER3_PASSWORD);
    }

    public function amAuthenticatedAsApiUser4(): void
    {
        $this->amAuthenticatedAsDriverUser('api-user-4-token', self::USER4_USERNAME, self::USER4_PASSWORD);
    }

    public function amAuthenticatedAsNetherlandsApiUser1(): void
    {
        $this->amAuthenticatedAsDriverUser(
            cacheIdentifier: 'netherlands-user-1-token',
            username: self::NETHERLANDS_USER1_USERNAME,
            password: self::NETHERLANDS_USER1_PASSWORD,
        );
    }

    public function amAuthenticatedAsSpainApiUser1(): void
    {
        $this->amAuthenticatedAsDriverUser(
            cacheIdentifier: 'spain-user-1-token',
            username: self::SPAIN_USER1_USERNAME,
            password: self::SPAIN_USER1_PASSWORD,
        );
    }

    public function validateResponseAgainstOpenApiSchema(): void
    {
        try {
            $this->getHermesAppApiValidationBuilder()->getResponseValidator()->validate(
                $this->getCurrentOperationAddress(),
                $this->getPsrResponse(),
            );
        } catch (ValidationFailed $validationFailed) {
            $this->fail('Response does not match OpenAPI schema: '.$validationFailed->getMessage());
        }
    }

    public function validatePortalResponseAgainstOpenApiSchema(): void
    {
        try {
            $this->getPortalApiValidationBuilder()->getResponseValidator()->validate(
                $this->getCurrentOperationAddress(),
                $this->getPsrResponse(),
            );
        } catch (ValidationFailed $validationFailed) {
            $this->fail('Portal Response does not match OpenAPI schema: '.$validationFailed->getMessage());
        }
    }

    private function getHermesAppApiValidationBuilder(): ValidatorBuilder
    {
        if (!isset($this->validatorBuilder)) {
            $this->validatorBuilder = new ValidatorBuilder()->fromJsonFile(codecept_data_dir('openapi.json'));
        }

        return $this->validatorBuilder;
    }

    private function getPortalApiValidationBuilder(): ValidatorBuilder
    {
        if (!isset($this->portalValidatorBuilder)) {
            $this->portalValidatorBuilder = new ValidatorBuilder()->fromJsonFile(codecept_data_dir('portal-openapi.json'));
        }

        return $this->portalValidatorBuilder;
    }

    private function getCurrentOperationAddress(): OperationAddress
    {
        /** @var REST $restModule */
        $restModule = $this->getModule('REST');

        $request = $restModule->client->getInternalRequest();

        return new OperationAddress(
            path: $request->getUri(),
            method: strtolower($request->getMethod()),
        );
    }

    private function getPsrResponse(): ResponseInterface
    {
        /** @var REST $restModule */
        $restModule = $this->getModule('REST');

        $response = $restModule->client->getInternalResponse();

        return new Response(
            status: $response->getStatusCode(),
            headers: $response->getHeaders(),
            body: $response->getContent(),
        );
    }

    public function amAuthenticatedAsDriverUser(string $cacheIdentifier, string $username, string $password): void
    {
        /** @var REST $restModule */
        $restModule = $this->getModule('REST');

        if (null === InMemoryCache::get($cacheIdentifier)) {
            $accessToken = $this->getAccessTokenFromKeycloak(
                $username,
                $password,
                'hermes-driver',
                $_ENV['KEYCLOAK_CLIENT_ID'],
            );
            assert(null !== $accessToken, 'Access token should not be null');
            InMemoryCache::set($cacheIdentifier, $accessToken);
        }

        $restModule->amBearerAuthenticated(InMemoryCache::get($cacheIdentifier));
    }

    public function amAuthenticatedAsPortalUser(string $cacheIdentifier, string $username, string $password): void
    {
        /** @var REST $restModule */
        $restModule = $this->getModule('REST');

        if (null === InMemoryCache::get($cacheIdentifier)) {
            $accessToken = $this->getAccessTokenFromKeycloak(
                $username,
                $password,
                'hermes-portal',
                $_ENV['KEYCLOAK_PORTAL_CLIENT_ID'],
            );
            assert(null !== $accessToken, 'Access token should not be null');
            InMemoryCache::set($cacheIdentifier, $accessToken);
        }

        $restModule->unsetHttpHeader('Authorization');
        $restModule->amBearerAuthenticated(InMemoryCache::get($cacheIdentifier));
    }

    public function setBearerToken(string $token): void
    {
        $this->debugSection('Setting bearer token', $token);
        /** @var REST $restModule */
        $restModule = $this->getModule('REST');
        $restModule->amBearerAuthenticated($token);
    }

    private function getAccessTokenFromKeycloak(
        string $username,
        string $password,
        string $scope,
        string $clientId,
        ?string $clientSecret = null,
    ): ?string {
        $client = HttpClient::create();
        $requestData = [
            'grant_type' => 'password',
            'username' => $username,
            'password' => $password,
            'audience' => 'hermes-backend',
            'scope' => $scope,
            'client_id' => $clientId,
        ];

        if (null !== $clientSecret) {
            $requestData['client_secret'] = $clientSecret;
        }

        try {
            $response = $client->request(
                method: 'POST',
                url: $_ENV['KEYCLOAK_URL'].'/realms/'.$_ENV['KEYCLOAK_REALM'].'/protocol/openid-connect/token',
                options: [
                    'body' => $requestData,
                ],
            );

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $content = $e instanceof HttpExceptionInterface ? $e->getResponse()->getContent(false) : '';

            $this->debugSection('Keycloak authentication request', $response->getInfo());
            $this->debugSection('Keycloak request data', $requestData);

            $this->fail(sprintf(
                'Failed to authenticate with Keycloak: %s Response: %s',
                $e->getMessage(),
                $content,
            ));

            return null;
        }

        $this->debugSection('Keycloak authentication request', $response->getInfo());
        $this->debugSection('Keycloak authentication response', $responseContent);

        try {
            /** @var array{access_token: string} $decodedResponse */
            $decodedResponse = json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);

            if (!isset($decodedResponse['access_token'])) {
                $this->fail('Keycloak response does not contain access token');

                return null;
            }

            return $decodedResponse['access_token'];
        } catch (\JsonException $e) {
            $this->fail('Failed to decode response from Keycloak: '.$e->getMessage());

            return null;
        }
    }

    public function grabCentrifugoHistory(
        string $channel,
        int $limit = 5,
    ): array {
        $httpClient = HttpClient::create();
        $requestData = [
            'channel' => $channel,
            'limit' => $limit,
            'reverse' => true, // Get the most recent messages first
        ];

        try {
            $response = $httpClient->request(
                'POST',
                $_ENV['CENTRIFUGO_ADMIN_URL'].'/api/history',
                [
                    'headers' => [
                        'X-API-Key' => $_ENV['CENTRIFUGO_API_KEY'],
                    ],
                    'json' => $requestData,
                ]
            );

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $content = $e instanceof HttpExceptionInterface ? $e->getResponse()->getContent(false) : '';

            $this->debugSection('Centrifugo history request', $response->getInfo());
            $this->debugSection('Centrifugo request data', $requestData);

            $this->fail(sprintf(
                'Failed to authenticate with Keycloak: %s Response: %s',
                $e->getMessage(),
                $content,
            ));
        }

        $this->debugSection('Centrifugo history request', $response->getInfo());
        $this->debugSection('Centrifugo request data', $requestData);
        $this->debugSection('Centrifugo history response', $responseContent);

        try {
            /** @var array{result?: array} $decodedResponse */
            $decodedResponse = json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);

            if (!isset($decodedResponse['result']['publications'])) {
                $this->fail('Centrifugo response does not contain messages');

                return [];
            }

            return $decodedResponse['result']['publications'];
        } catch (\JsonException $e) {
            $this->fail('Failed to decode response from Centrifugo: '.$e->getMessage());

            return [];
        }
    }
}
