<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait DakoEndpointsTrait
{
    use CommonTrait;

    public function callApiUploadDakoFile(
        string $dakoProcessId,
        string $fileName,
        string $fileContent,
        int $fileNumber,
        int $totalFiles,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $queryParams = http_build_query(array_filter([
            'fileNumber' => $fileNumber,
            'totalFiles' => $totalFiles,
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendPut(
            url: '/api/v2/dako-process/file/'.$dakoProcessId.'/'.rawurlencode($fileName).'?'.$queryParams,
            params: $fileContent,
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiRetrieveDakoFile(
        string $dakoProcessId,
        int $fileNumber,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendGet(url: '/api/v2/dako-process/file/'.$dakoProcessId.'/'.$fileNumber.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiCloseDakoSession(
        string $dakoProcessId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendDelete(
            '/api/v2/dako-process/'.$dakoProcessId.($debug ? '?XDEBUG_TRIGGER' : ''),
        );

        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiAbortDakoSession(
        string $dakoProcessId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendDelete(
            '/api/v2/dako-process/abort/'.$dakoProcessId.($debug ? '?XDEBUG_TRIGGER' : ''),
        );

        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiGetDakoProcess(
        string $dakoProcessId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet(
            '/api/v2/dako-process/'.$dakoProcessId.($debug ? '?XDEBUG_TRIGGER' : ''),
        );

        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiDakoGetAtr(
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/dako-process'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiDakoExchangeApdu(
        string $dakoProcessId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPatch(
            '/api/v2/dako-process/'.$dakoProcessId.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
