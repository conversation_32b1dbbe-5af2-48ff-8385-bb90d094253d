<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait InterruptionEndpointsTrait
{
    public function callApiInterruptionGetV2(
        bool $includeNotCompleted = false,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $query = [];
        if ($includeNotCompleted) {
            $query['includeNotCompleted'] = '';
        }

        if ($debug) {
            $query['XDEBUG_TRIGGER'] = '';
        }

        $this->sendGet('/api/v2/interruption?'.http_build_query($query));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiInterruptionTemplateGetV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/interruption-templates'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTourInterruptionUpdateTaskGroupV2(
        string $clientId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPatch(
            '/api/v2/tour/interruption/'.$clientId.'/taskgroup'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
