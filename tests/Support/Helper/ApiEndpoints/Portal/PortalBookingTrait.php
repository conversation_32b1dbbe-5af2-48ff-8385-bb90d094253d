<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalBookingTrait
{
    public function callApiPortalBookingList(
        string $tourId,
        string $sessionId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }
        $queryParams = http_build_query([
            'tourId' => $tourId,
            'sessionId' => $sessionId,
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
        ]);
        $this->sendGet('/portal-api/v1/booking'.('' !== $queryParams ? '?'.$queryParams : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
