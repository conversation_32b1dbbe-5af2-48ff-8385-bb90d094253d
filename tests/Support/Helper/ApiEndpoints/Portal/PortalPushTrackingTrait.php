<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalPushTrackingTrait
{
    public function callApiPortalPushTracking(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            '/portal-api/v1/equipment/push-tracking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
