<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalEquipmentTrait
{
    public function callApiPortalEquipmentList(
        ?array $branchIds = null,
        ?array $equipmentTypes = null,
        ?string $licensePlate = null,
        ?string $externalId = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
            'branchId' => $branchIds,
            'equipmentType' => $equipmentTypes,
            'licensePlate' => $licensePlate,
            'externalId' => $externalId,
        ]);

        $this->sendGet('/portal-api/v1/equipment?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentPositionList(
        ?array $branchIds = null,
        ?array $equipmentTypes = null,
        ?string $licensePlate = null,
        ?string $externalId = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
            'branchId' => $branchIds,
            'equipmentType' => $equipmentTypes,
            'licensePlate' => $licensePlate,
            'externalId' => $externalId,
        ]);

        $this->sendGet('/portal-api/v1/equipment-position?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentTypesList(
        string $branchId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
            'branchId' => $branchId,
        ]);

        $this->sendGet('/portal-api/v1/equipment-types?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiPortalEquipmentCheckList(
        ?string $equipmentId = null,
        ?\DateTimeImmutable $from = null,
        ?\DateTimeImmutable $to = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query([
            'equipmentId' => $equipmentId,
            'from' => $from?->format('Y-m-d'),
            'to' => $from?->format('Y-m-d'),
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
        ]);
        $this->sendGet('/portal-api/v1/equipment-check'.('' !== $queryParams ? '?'.$queryParams : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentCheckDetails(
        string $sessionEquipmentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/equipment-check/'.$sessionEquipmentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentItem(
        string $equipmentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/equipment/'.$equipmentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentSearch(
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/equipment/search/'.urlencode($search).($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentGet(
        string $equipmentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/equipment/'.$equipmentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentCreate(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/portal-api/v1/equipment'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentUpdate(
        string $equipmentId,
        string $data,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPatch('/portal-api/v1/equipment/'.$equipmentId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentDelete(
        string $equipmentId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendDelete('/portal-api/v1/equipment/'.$equipmentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalEquipmentTerminateSession(
        string $sessionEquipmentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/portal-api/v1/equipment/stop-sessions/'.$sessionEquipmentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
