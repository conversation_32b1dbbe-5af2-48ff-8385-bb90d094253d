<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalTourTrait
{
    public function callApiPortalTourList(
        string $date,
        ?string $externalId = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query(array_filter([
            'date' => $date,
            'externalId' => $externalId,
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendGet('/portal-api/v1/tour?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalTourDetails(
        string $tourId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }
        $queryParams = http_build_query(array_filter([
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendGet('/portal-api/v1/tour/'.$tourId.'?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalTourLabelsByDate(
        string $date,
        ?string $hasOrderFiles = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query([
            'hasOrderFiles' => $hasOrderFiles,
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]);

        $this->sendGet('/portal-api/v1/tour/list-by-date/'.$date.'?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalTourMastertours(
        string $tourId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPatch('/portal-api/v1/tour/'.urlencode($tourId).'/mastertours'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
