<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalStaffTrait
{
    public function callApiPortalStaffList(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
        ?string $query = null,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'query' => $query,
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]);

        $this->sendGet('/portal-api/v1/staff?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalStaffListByBranch(
        string $branchId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
            'branchId' => $branchId,
        ]);

        $this->sendGet('/portal-api/v1/staff-list?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiPortalStaffById(
        string $uuid,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/staff/'.$uuid.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalStaffResetPassword(
        string $staffUuid,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/portal-api/v1/staff/'.urlencode($staffUuid).'/password'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiPortalStaffDeviceAccessCreate(
        string $staffId,
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            sprintf('/portal-api/v1/staff/%s/device-access', $staffId).($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
