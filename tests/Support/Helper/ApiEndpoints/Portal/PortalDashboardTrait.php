<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalDashboardTrait
{
    public function callApiPortalDashboard(
        ?string $date = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query(array_filter([
            'date' => $date,
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]));

        $this->sendGet('/portal-api/v1/dashboard?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
