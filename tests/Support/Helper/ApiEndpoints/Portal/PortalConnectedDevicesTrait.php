<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalConnectedDevicesTrait
{
    public function callApiPortalConnectedDevicesHardwareTypes(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/connected-devices/hardware-types'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
