<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalMastertourTemplateTrait
{
    public function callApiPortalMastertourTemplateCreate(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/portal-api/v1/mastertour-template'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourTemplateList(
        ?string $externalIdFilter = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
            'filter' => $externalIdFilter,
        ]);

        $this->sendGet('/portal-api/v1/mastertour-template?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourTemplateDetails(
        string $templateUuid,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/mastertour-template/'.$templateUuid.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourTemplateUpdate(
        string $templateUuid,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPatch('/portal-api/v1/mastertour-template/'.$templateUuid.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourTemplateDelete(
        string $templateUuid,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendDelete('/portal-api/v1/mastertour-template/'.$templateUuid.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourProgressListWithDate(
        string $mastertourTemplateId,
        string $date,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/mastertour-progress/'.$mastertourTemplateId.'/'.$date.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourProgressListWith(
        string $mastertourTemplateId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/mastertour-progress/'.$mastertourTemplateId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourProgressList(
        string $mastertourTemplateId,
        string $date,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/mastertour-progress/'.$mastertourTemplateId.'/'.$date.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
