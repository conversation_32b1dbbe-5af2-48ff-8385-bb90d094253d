<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalTrackingDataTrait
{
    public function callApiPortalTrackingList(
        ?string $minDate = null,
        ?string $maxDate = null,
        ?string $search = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query(array_filter([
            'min_date' => $minDate,
            'max_date' => $maxDate,
            'search' => $search,
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendGet('/portal-api/v1/tracking/?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalTrackingDetails(
        string $trackingId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query(array_filter([
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendGet('/portal-api/v1/tracking/'.$trackingId.'?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
