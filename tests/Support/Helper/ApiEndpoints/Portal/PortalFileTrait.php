<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalFileTrait
{
    public function callApiPortalAppUserFileList(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/app-user-file'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalFilePut(
        string $directory,
        string $id,
        string $fileContents,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/octet-stream');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPut(
            '/portal-api/v1/file/'.$directory.'/'.$id.($debug ? '?XDEBUG_TRIGGER' : ''),
            $fileContents
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalMastertourImport(
        string $type,
        string $fileName,
        string $fileContents,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/octet-stream');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            '/portal-api/v1/mastertour-template-import/'.$type.'/'.$fileName.($debug ? '?XDEBUG_TRIGGER' : ''),
            $fileContents
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalFileGet(
        string $filePath,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/octet-stream');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/file/'.$filePath.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        // $this->validatePortalResponseAgainstOpenApiSchema(); // No way to define all possible file types in openapi schema
    }

    public function callApiPortalFilesInOrder(
        string $orderId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/order/'.$orderId.'/file'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalFilesInMultipleOrders(
        array $orderIds,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query(
            array_filter([
                'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
            ]),
        );
        foreach ($orderIds as $orderId) {
            $queryParams .= '&orderId[]='.urlencode($orderId);
        }
        $this->sendGet('/portal-api/v1/order/file?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
