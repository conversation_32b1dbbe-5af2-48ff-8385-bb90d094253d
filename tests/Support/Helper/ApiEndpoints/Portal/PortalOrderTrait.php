<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalOrderTrait
{
    public function callApiPortalOrdersByTour(
        string $tourId,
        ?string $hasFiles = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $queryParams = http_build_query([
            'hasFiles' => $hasFiles,
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]);

        $this->sendGet('/portal-api/v1/tour/'.$tourId.'/order?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
