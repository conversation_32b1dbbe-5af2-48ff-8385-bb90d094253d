<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Portal;

trait PortalDakoTrait
{
    public function callApiPortalDakoAccountCreate(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/portal-api/v1/dako-account'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalDakoAccountUpdate(
        string $dakoAccountId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPatch('/portal-api/v1/dako-account/'.$dakoAccountId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalDakoAccountList(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/dako-account'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }

    public function callApiPortalDakoAccountDetails(
        string $dakoAccountId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/portal-api/v1/dako-account/'.$dakoAccountId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validatePortalResponseAgainstOpenApiSchema();
    }
}
