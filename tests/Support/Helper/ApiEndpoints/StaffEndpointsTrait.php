<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait StaffEndpointsTrait
{
    public function callApiStaffInterruptionV2(
        string $interruptionTemplateId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/staff/interruption/'.$interruptionTemplateId.'/booking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiStaffFocus(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost('/api/v2/staff/focus'.($debug ? '?XDEBUG_TRIGGER' : ''), '');
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
