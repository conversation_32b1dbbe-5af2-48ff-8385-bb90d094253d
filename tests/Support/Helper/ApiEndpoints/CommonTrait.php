<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

use App\Tests\Support\DeviceCache;
use Faker\Factory;

trait CommonTrait
{
    public function applyApiV2Headers(
        ?string $deviceTimestamp = null,
        ?string $actionTimestamp = null,
        ?string $actionLatitude = null,
        ?string $actionLongitude = null,
        ?float $actionMileage = null,
        bool $profile = false,
    ): void {
        $faker = Factory::create();

        $deviceId = DeviceCache::get();
        $deviceTimestamp ??= (new \DateTimeImmutable())->format(\DateTimeInterface::RFC3339_EXTENDED);
        $actionTimestamp ??= $deviceTimestamp;
        $actionLatitude ??= $faker->latitude();
        $actionLongitude ??= $faker->longitude();
        $actionMileage ??= $faker->randomFloat(2, 10000, 9999999);

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-device-id', $deviceId);
        $this->haveHttpHeader('x-device-timestamp', $deviceTimestamp);
        $this->haveHttpHeader('x-action-timestamp', $actionTimestamp);
        $this->haveHttpHeader('x-action-latitude', (string) $actionLatitude);
        $this->haveHttpHeader('x-action-longitude', (string) $actionLongitude);
        $this->haveHttpHeader('x-action-mileage', (string) $actionMileage);
    }
}
