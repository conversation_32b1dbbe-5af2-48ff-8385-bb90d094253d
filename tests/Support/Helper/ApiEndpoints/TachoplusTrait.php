<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

use League\Flysystem\Filesystem;
use League\Flysystem\PhpseclibV3\SftpAdapter;
use League\Flysystem\PhpseclibV3\SftpConnectionProvider;

trait TachoplusTrait
{
    public function tachoplusFileExists(string $fileName): void
    {
        $adapter = new SftpAdapter(
            new SftpConnectionProvider(
                host: getenv('TACHOPLUS_FTP_HOST'),
                username: getenv('TACHOPLUS_FTP_USERNAME'),
                password: getenv('TACHOPLUS_FTP_PASSWORD'),
                port: (int) getenv('TACHOPLUS_FTP_PORT'),
                timeout: 10,
                maxTries: 2,
            ),
            root: getenv('TACHOPLUS_FTP_PATH'),
        );

        $filesystem = new Filesystem($adapter);
        $fileName = str_replace(['/', '\\'], '_', $fileName);
        $this->waitUntil(
            fn () => $this->assertTrue($filesystem->fileExists($fileName)),
        );
    }
}
