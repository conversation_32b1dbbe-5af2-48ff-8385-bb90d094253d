<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait LanguageEndpointsTrait
{
    public function callApiLanguageGetV2(
        ?string $locale = null,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendGet(
            '/api/v2/language'.(null !== $locale ? '/'.$locale : '').($debug ? '?XDEBUG_TRIGGER' : ''),
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
