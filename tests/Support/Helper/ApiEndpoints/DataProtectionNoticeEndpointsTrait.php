<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait DataProtectionNoticeEndpointsTrait
{
    public function callApiDataProtectionNoticeV2(
        string $isoLocale,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendGet(url: '/api/v2/data-protection-notice/'.$isoLocale.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
