<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait NotificationEndpointsTrait
{
    public function callApiNotificationAccessV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendGet(
            '/api/v2/notification/access'.($debug ? '?XDEBUG_TRIGGER' : '')
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
