<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\MobileAppReleases;

trait MobileAppReleasesEndpointsTrait
{
    public function callApiMobileAppReleases(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode('release-user:release-pass'));

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/mobile-app-releases/release'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
