<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait OrderEndpointsTrait
{
    public function callApiOrderBookingV2(
        string $orderId,
        string $data,
        ?string $deviceTimestamp = null,
        ?string $actionTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(
            deviceTimestamp: $deviceTimestamp,
            actionTimestamp: $actionTimestamp,
            profile: $profile,
        );

        $this->sendPost(
            '/api/v2/order/'.$orderId.'/booking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderGetV2(
        string $orderId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/order/'.$orderId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderNoteV2(
        string $orderId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/order/'.$orderId.'/note'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderNoteHistoryV2(
        string $orderId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/order/'.$orderId.'/note'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderTaskGroupPatchV2(
        string $orderId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPatch(
            '/api/v2/order/'.$orderId.'/taskgroup'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderTerminationV2(
        string $orderId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/order/'.$orderId.'/termination'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderTerminationHistoryV2(
        string $orderId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet(
            '/api/v2/order/'.$orderId.'/termination'.($debug ? '?XDEBUG_TRIGGER' : ''),
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTaskGroupBookingV2(
        string $taskGroupId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/taskgroup/'.$taskGroupId.'/booking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiOrderFocusV2(
        string $orderId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost('/api/v2/order/'.$orderId.'/focus'.($debug ? '?XDEBUG_TRIGGER' : ''), '');
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
