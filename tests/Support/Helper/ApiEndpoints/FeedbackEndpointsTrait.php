<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait FeedbackEndpointsTrait
{
    public function callApiFeedbackCreateV2(
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/feedback'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiFeedbackGetV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/feedback'.($debug ? '?XDEBUG_TRIGGER' : ''));

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
