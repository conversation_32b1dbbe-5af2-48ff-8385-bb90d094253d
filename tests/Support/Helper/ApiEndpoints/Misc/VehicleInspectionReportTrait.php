<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints\Misc;

trait VehicleInspectionReportTrait
{
    public function callGermanyVirMessage(
        string $data,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader(
            'Authorization',
            'Basic '.base64_encode($_ENV['GERMANY_VIR_INGEST_USERNAME'].':'.$_ENV['GERMANY_VIR_INGEST_PASSWORD'])
        );

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            '/ingest/germany-vir/device-message'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGermanyVirRepairReport(
        string $data,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader(
            'Authorization',
            'Basic '.base64_encode($_ENV['GERMANY_VIR_INGEST_USERNAME'].':'.$_ENV['GERMANY_VIR_INGEST_PASSWORD'])
        );

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            '/ingest/germany-vir/repair-report'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
