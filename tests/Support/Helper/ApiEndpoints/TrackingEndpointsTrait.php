<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait TrackingEndpointsTrait
{
    public function callApiTrackingV2(
        string $data,
        ?string $equipmentExternalId = null,
        ?string $tourExternalId = null,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $queryParams = http_build_query(array_filter([
            'equipmentExternalId' => $equipmentExternalId,
            'tourExternalId' => $tourExternalId,
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendPost('/api/v2/tracking?'.$queryParams, $data);

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
