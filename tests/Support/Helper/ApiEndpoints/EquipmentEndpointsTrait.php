<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait EquipmentEndpointsTrait
{
    use CommonTrait;

    public function callApiEquipmentListExtended(
        array $equipmentTypes = [],
        array $equipmentTypeCategories = [],
        ?string $query = null,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $query = http_build_query([
            'equipment_type' => $equipmentTypes,
            'equipment_type_category' => $equipmentTypeCategories,
            'query' => $query,
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]);

        $this->sendGet('/api/v2/equipment-list-extended?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiEquipmentGetCollectionV2(
        ?string $availability = null,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $query = http_build_query([
            'availability' => $availability,
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]);

        $this->sendGet('/api/v2/equipment?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiEquipmentBookingV2(
        string $equipmentId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/equipment/'.$equipmentId.'/booking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiEquipmentGetItemV2(
        string $equipmentId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/equipment/'.$equipmentId.($debug ? '?XDEBUG_TRIGGER' : ''));

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiEquipmentUpdateTaskGroupV2(
        string $equipmentId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPatch(
            '/api/v2/equipment/'.$equipmentId.'/taskgroup'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiEquipmentGetTypes(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $query = http_build_query([
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]);

        $this->sendGet('/api/v2/equipment-types?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiEquipmentGetTypeCategories(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $query = http_build_query([
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]);

        $this->sendGet('/api/v2/equipment-type-categories?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
