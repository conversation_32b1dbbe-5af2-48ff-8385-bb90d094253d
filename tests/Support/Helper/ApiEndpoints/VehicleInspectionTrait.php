<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait VehicleInspectionTrait
{
    public function callApiVehicleInspectionTemplate(
        string $equipmentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendGet('/api/v2/vehicle-inspection-report/'.$equipmentId.'/template'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiVehicleInspectionSubmitReport(
        string $equipmentId,
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Content-Type', 'application/json');

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost('/api/v2/vehicle-inspection-report/'.$equipmentId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
