<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait DeviceAccessEndpointsTrait
{
    public function callApiGetDeviceAccessV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $query = http_build_query([
            'XDEBUG_TRIGGER' => $debug ? '1' : null,
        ]);

        $this->sendGet('/api/v2/device-access?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
