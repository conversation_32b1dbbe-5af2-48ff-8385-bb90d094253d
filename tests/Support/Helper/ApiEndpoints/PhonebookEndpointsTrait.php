<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait PhonebookEndpointsTrait
{
    public function callApiPhonebookContactList(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: null, profile: $profile);

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $query = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
        ]);

        $this->sendGet('/api/v2/phonebook-contact?'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
