<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

use App\Tests\Support\DeviceCache;

trait UserEndpointsTrait
{
    use CommonTrait;

    public function callApiUserEndSessionIgnoreErrorV2(
        ?string $deviceTimestamp = null,
        bool $debug = false,
        bool $profile = false,
    ): void {
        // We must not push a session-end into queue if no session is active!
        if (false === $this->hasSessionRunningV2()) {
            return;
        }

        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost('/api/v2/user/end-session'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->verifyInvalidSessionV2();
    }

    public function callApiUserStartSessionV2(
        ?string $deviceTimestamp = null,
        int $expectedResponseCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost('/api/v2/user/start-session'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedResponseCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiUserProfileV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/user/me'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiUserUpdateTaskgroupV2(
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPatch(
            '/api/v2/user/taskgroup'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiPatchUserProfileV2(
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPatch(
            '/api/v2/user/me'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiUserGetCollectionV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/user'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiUserEndSessionV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        if (false === $this->hasSessionRunningV2()) {
            $this->fail(sprintf('no session running for device %s that could be ended', DeviceCache::get()));
        }
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendPost(
            '/api/v2/user/end-session'.($debug ? '?XDEBUG_TRIGGER' : ''),
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->verifyInvalidSessionV2();
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiUserChangePasswordV2(
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/user/change-password'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
