<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait TourEndpointsTrait
{
    public function callApiTourGetV2(
        string $tourId,
        ?string $deviceTimestamp = null,
        int $includeAll = 0,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $queryParams = http_build_query([
            'XDEBUG_TRIGGER' => (true !== $debug) ? null : '',
            'includeAll' => 1 !== $includeAll ? null : $includeAll,
        ]);

        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/tour/'.$tourId.'?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTourBookingV2(
        string $tourId,
        string $data,
        ?string $deviceTimestamp = null,
        ?string $actionTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(
            deviceTimestamp: $deviceTimestamp,
            actionTimestamp: $actionTimestamp,
            profile: $profile,
        );

        $this->sendPost(
            '/api/v2/tour/'.$tourId.'/booking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTourTerminationV2(
        string $tourId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/tour/'.$tourId.'/termination'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTourTerminationHistoryV2(
        string $tourId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/tour/'.$tourId.'/termination'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTourGetCollectionV2(
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendGet('/api/v2/tour'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiTourInterruptionV2(
        string $interruptionTemplateId,
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/tour/interruption/'.$interruptionTemplateId.'/booking'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
