<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait SapNetherlandsEndpointsTrait
{
    public function callSapNetherlandsTour(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode('ingest-sap-nl:ingest-sap-nl'));

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            '/ingest/sap-netherlands/tour'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        $this->seeResponseCodeIs($expectedStatusCode);

        return $this->grabDataFromResponseByJsonPath(
            '$.tour'
        )[0];
    }
}
