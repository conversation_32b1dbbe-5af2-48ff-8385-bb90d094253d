<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait FileEndpointsTrait
{
    public function callApiFileUploadInDirectoryV2(
        string $directory,
        string $uuid,
        string $fileContent,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendPut(
            url: '/api/v2/file/'.$directory.'/'.$uuid.($debug ? '?XDEBUG_TRIGGER' : ''),
            params: $fileContent,
        );
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiFileRetrieveV2(
        string $filePath,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);
        $this->sendGet(url: '/api/v2/file/'.$filePath.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
        // $this->validateResponseAgainstOpenApiSchema(); // No way to define all possible file types in openapi schema
    }
}
