<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait MastertourEndpointsTrait
{
    public function callApiMastertourGetV2(
        string $externalId,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $queryParams = http_build_query(array_filter([
            'XDEBUG_TRIGGER' => $debug ? 'XDEBUG_TRIGGER' : null,
        ]));

        $this->sendGet('/api/v2/mastertour/'.rawurlencode($externalId).'?'.$queryParams);
        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }

    public function callApiMastertourProgressV2(
        string $data,
        ?string $deviceTimestamp = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->applyApiV2Headers(deviceTimestamp: $deviceTimestamp, profile: $profile);

        $this->sendPost(
            '/api/v2/mastertour-progress'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data,
        );

        $this->seeResponseCodeIs($expectedStatusCode);
        $this->validateResponseAgainstOpenApiSchema();
    }
}
