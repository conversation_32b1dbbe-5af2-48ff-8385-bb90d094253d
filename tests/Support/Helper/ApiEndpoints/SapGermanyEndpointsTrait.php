<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait SapGermanyEndpointsTrait
{
    public function callSapGermanyTourUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode('ingest-sap-de:ingest-sap-de'));

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendPost(
            '/ingest/sap-germany/tour'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $tourId = $this->grabDataFromResponseByJsonPath('$.tour')[0];
        $this->assertNotEmpty($tourId);

        return $tourId;
    }

    public function callSapGermanyTourAbandon(
        string $tourExternalId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode('ingest-sap-de:ingest-sap-de'));

        if ($profile) {
            $this->setBlackfireProfileHeader();
        }

        $this->sendDelete(
            '/ingest/sap-germany/tour/'.$tourExternalId.($debug ? '?XDEBUG_TRIGGER' : ''),
        );
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
