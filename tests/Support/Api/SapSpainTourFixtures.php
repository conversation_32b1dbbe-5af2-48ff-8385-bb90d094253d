<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use App\Domain\Entity\Enum\Types\EquipmentType;

class SapSpainTourFixtures
{
    public static function tourRequestCreate(
        string $tourExtId = '9985544',
        string $orderExtId = 'ES00000000000083781455',
        string $tourName = 'Spanish ExampleTour with a name, that is a little longer',
        string $equipmentExtId = '599852',
        string $staffExtId = '1111ES',
        string $equipmentLicensePlate = 'ES-AK 8546',
        array $tourInfoFiles = [],
        array $orderInfoFiles = [],
        ?\DateTime $start = null,
        ?\DateTime $end = null,
    ): string {
        $startDate = ($start ?? (new \DateTime())->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? (new \DateTime())->setTime(23, 0))->format(DATE_ATOM);
        $orderInfoFiles = json_encode($orderInfoFiles);
        $tourInfoFiles = json_encode($tourInfoFiles);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "$staffExtId",
                    "personnelType": "DRIVER",
                    "firstName": "ESApi",
                    "lastName": "User1"
                }
            ],
            "addresses": [
                {
                    "name": "Werk Porta Westfalica",
                    "country": "ES",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066447"
                },
                {
                    "name": "Innenstadt",
                    "country": "ES",
                    "postalCode": "32423",
                    "city": "Minden",
                    "district": "Innenstadt",
                    "street": "Aminghauser Str.",
                    "houseNumber": "7",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2553219"
                },
                {
                    "name": "Example-name customer",
                    "country": "ES",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Eisbergen",
                    "street": "Albert-Schweitzer-Str.",
                    "houseNumber": "8",
                    "phoneNumber": "+4957518135",
                    "state": "",
                    "addressExtId": "8443622"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2022113"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "ES_EQUIPMENT_1",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "ES-AK 1",
                    "containerMounting": ""
                },
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "8443622",
                    "customerExtId": "2022113",
                    "additionalInformation": [
                        {
                            "sequence": 10,
                            "text": "1.04 B19 Kartonage-150101-ABF",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        },
                        {
                            "sequence": 20,
                            "text": "STANDARD ask-10-pr-BEHA",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        }
                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "8443622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "container_count",
                                    "taskName": "Container amount",
                                    "elements": [],
                                    "taskExtId": ""
                                },
                                {
                                    "taskType": "customer_weight",
                                    "taskName": "Container weight",
                                    "elements": [],
                                    "taskExtId": ""
                                },
                                {
                                    "taskType": "deliveryNote_sig",
                                    "taskName": "customer sign",
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066447",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2553219",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "weighingnote",
                                    "taskName": null,
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        }
                    ],
                    "orderType": "ES_50",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}",
                    "orderDocuments": [],
                    "infoFiles": {$orderInfoFiles},
                    "materialExternalId": "mat-es-1234"
                }
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "58741",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066447",
            "endAddressExtId": "2066447",
            "forcedOrder": false,
            "country": "ES",
            "disposalSites": [
                {
                    "addressExtId": "2066447",
                    "weigh": false,
                    "disposalSiteExtId": "18144",
                    "name": "Porta W.-PWE (UMS)"
                },
                {
                    "addressExtId": "2553219",
                    "weigh": false,
                    "disposalSiteExtId": "24792",
                    "name": "Minden-PWE ES Minden Holler"
                }
            ],
            "interruptionExternalId": "1234",
            "infoFiles": {$tourInfoFiles}
        }
        JSON;
    }

    public static function tourMultiOrderRequestCreate(
        string $tourExtId = '9985544',
        string $orderExtId = 'ES00000000000083781455',
        string $tourName = 'Spanish ExampleTour with a name, that is a little longer',
        string $equipmentExtId = '599852',
        string $staffExtId = '1111ES',
        string $equipmentLicensePlate = 'ES-AK 8546',
        ?\DateTime $start = null,
        ?\DateTime $end = null,
    ): string {
        $startDate = ($start ?? (new \DateTime())->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? (new \DateTime())->setTime(23, 0))->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "$staffExtId",
                    "personnelType": "DRIVER",
                    "firstName": "ESApi",
                    "lastName": "User1"
                }
            ],
            "addresses": [
                {
                    "name": "Werk Porta Westfalica",
                    "country": "ES",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066447"
                },
                {
                    "name": "Innenstadt",
                    "country": "ES",
                    "postalCode": "32423",
                    "city": "Minden",
                    "district": "Innenstadt",
                    "street": "Aminghauser Str.",
                    "houseNumber": "7",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2553219"
                },
                {
                    "name": "Example-name customer",
                    "country": "ES",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Eisbergen",
                    "street": "Albert-Schweitzer-Str.",
                    "houseNumber": "8",
                    "phoneNumber": "+4957518135",
                    "state": "",
                    "addressExtId": "8443622"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2022113"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "8443622",
                    "customerExtId": "2022113",
                    "additionalInformation": [],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "8443622",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066447",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2553219",
                            "additionalInformation": [],
                            "tasks": []
                        }
                    ],
                    "orderType": "ES_50",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}",
                    "orderDocuments": [],
                    "infoFiles": [],
                    "materialExternalId": "mat-es-1234"
                },
                {
                    "locationExtId": "8443622",
                    "customerExtId": "2022113",
                    "additionalInformation": [],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "8443622",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066447",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2553219",
                            "additionalInformation": [],
                            "tasks": []
                        }
                    ],
                    "orderType": "ES_50",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}-2",
                    "orderDocuments": [],
                    "infoFiles": [],
                    "materialExternalId": "mat-es-1234"
                },
                {
                    "locationExtId": "8443622",
                    "customerExtId": "2022113",
                    "additionalInformation": [],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "8443622",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066447",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2553219",
                            "additionalInformation": [],
                            "tasks": []
                        }
                    ],
                    "orderType": "ES_50",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}-3",
                    "orderDocuments": [],
                    "infoFiles": [],
                    "materialExternalId": "mat-es-1234"
                }
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "58741",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066447",
            "endAddressExtId": "2066447",
            "forcedOrder": false,
            "country": "ES",
            "disposalSites": [],
            "interruptionExternalId": "1234",
            "infoFiles": []
        }
        JSON;
    }

    public static function tourEquipmentCreate(
        ?string $externalId = null,
        ?string $licensePlate = null,
        ?EquipmentType $type = null,
        ?string $staffExtId = null,
    ): string {
        $startDate = ($start ?? new \DateTime()->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? new \DateTime()->setTime(23, 0))->format(DATE_ATOM);
        $tourExtId = uniqid();
        $tourName = 'ExampleTourForEquipment'.uniqid();
        $externalId = ($externalId ?? uniqid());
        $licensePlate = ($licensePlate ?? uniqid());
        $type = ($type ?? EquipmentType::ASK);
        $staff = [];

        if (null !== $staffExtId) {
            $staff[] = [
                'staffExtId' => $staffExtId,
                'personnelType' => 'DRIVER',
                'firstName' => 'Max',
                'lastName' => 'Mustermann',
            ];
        }

        $staff = json_encode($staff, JSON_THROW_ON_ERROR);

        return <<<JSON
        {
            "staff": {$staff},
            "addresses": [
                {
                    "country": "ES",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [
                {
                    "equipmentExtId": "{$externalId}",
                    "equipmentType": "{$type->value}",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$licensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "Oelbronn",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "ES",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }
}
