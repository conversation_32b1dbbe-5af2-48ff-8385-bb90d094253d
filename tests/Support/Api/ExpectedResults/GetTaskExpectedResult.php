<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\ExpectedResults;

class GetTaskExpectedResult
{
    /**
     * @param array<string>                    $expectedNames
     * @param array<string>                    $restrictedNames
     * @param array<string>                    $expectedAutocompleteOptions
     * @param array<GetTaskRuleExpectedResult> $taskRuleExpectedResults
     */
    public function __construct(
        public int $position,
        public int $taskCount,
        public array $expectedNames,
        public array $restrictedNames,
        public array $expectedAutocompleteOptions,
        public array $taskRuleExpectedResults,
    ) {
    }
}
