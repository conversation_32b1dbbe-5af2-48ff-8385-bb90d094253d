<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\ExpectedResults;

class GetTaskGroupsExpectedResult
{
    /**
     * @param array<GetTaskExpectedResult>          $taskExpectedResults
     * @param array<GetTaskGroupRuleExpectedResult> $taskGroupRuleExpectedResults
     */
    public function __construct(
        public int $position,
        public int $taskGroupCount,
        public array $taskExpectedResults,
        public array $taskGroupRuleExpectedResults,
    ) {
    }
}
