<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\ExpectedResults;

class GetTourExpectedResult
{
    /**
     * @param array<GetAdditionalServiceExpectedResult>  $additionalServiceExpectedResults
     * @param array<string>                              $equipmentNames
     * @param array<GetTaskGroupsExpectedResult>         $orderTaskGroupsExpectedResults
     * @param array<GetTaskGroupsExpectedResult>         $tourTerminationTaskGroupsExpectedResults
     * @param array<GetSubOrderTaskGroupsExpectedResult> $orderNoteTaskGroupExpectedResults
     */
    public function __construct(
        public string $tourName,
        public string $status,
        public bool $hasAdditionalServices,
        public array $additionalServiceExpectedResults,
        public int $equipmentCount,
        public array $equipmentNames,
        public int $orderCount,
        public array $orderTaskGroupsExpectedResults,
        public int $tourTerminationCount,
        public array $tourTerminationTaskGroupsExpectedResults,
        public array $orderNoteTaskGroupExpectedResults,
    ) {
    }
}
