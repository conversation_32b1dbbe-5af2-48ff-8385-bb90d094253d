<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

class SapNetherlandsTourFixtures
{
    public static function tourRequestCreate(
        string $tourExtId = '8523258',
        string $orderExtId = 'NL00000000000083781455',
        string $tourName = 'Dutch ExampleTour with a name, that is a little longer',
        string $equipmentExtId = '500852',
        string $staffExtId = 'nl111',
        string $equipmentLicensePlate = 'NL-AK 8546',
        ?\DateTime $start = null,
        ?\DateTime $end = null,
        string $branchExternalId = '55555',
        string $additionalEquipmentId = 'NL-EQ1',
    ): string {
        $startDate = ($start ?? (new \DateTime())->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? (new \DateTime())->setTime(23, 0))->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "$staffExtId",
                    "personnelType": "DRIVER",
                    "firstName": "dutch",
                    "lastName": "user1"
                }
            ],
            "addresses": [
                {
                    "name": "Werk Porta Westfalica",
                    "country": "NL",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                },
                {
                    "name": "Innenstadt",
                    "country": "NL",
                    "postalCode": "32423",
                    "city": "Minden",
                    "district": "Innenstadt",
                    "street": "Aminghauser Str.",
                    "houseNumber": "7",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2589219"
                },
                {
                    "name": "Example-name customer",
                    "country": "NL",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Eisbergen",
                    "street": "Albert-Schweitzer-Str.",
                    "houseNumber": "8",
                    "phoneNumber": "+4957518135",
                    "state": "",
                    "addressExtId": "83622"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2021112"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "{$additionalEquipmentId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "NL-AK 1",
                    "containerMounting": ""
                },
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                },
                {
                    "equipmentExtId": "{$equipmentExtId}-T",
                    "equipmentType": "EQ7800",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}-T",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [
                        {
                            "sequence": 10,
                            "text": "1.04 B19 Kartonage-150101-ABF",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        },
                        {
                            "sequence": 20,
                            "text": "STANDARD ask-10-pr-BEHA",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        }
                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "container_count",
                                    "taskName": "Container amount",
                                    "elements": [],
                                    "taskExtId": ""
                                },
                                {
                                    "taskType": "customer_weight",
                                    "taskName": "Container weight",
                                    "elements": [],
                                    "taskExtId": ""
                                },
                                {
                                    "taskType": "deliveryNote_sig",
                                    "taskName": "customer sign",
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "weighingnote",
                                    "taskName": null,
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        }
                    ],
                    "orderType": "NL_50",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}",
                    "materialExternalId": "mat-nl-1234",
                    "orderDocuments": [
                        {
                            "documentType": "NL_DTD1",
                            "documentName": "regard this",
                            "deliveryServices": [],
                            "textBlocks": [
                                {
                                    "blockId": "tourRef",
                                    "text": "NLNND2896/12564406"
                                },
                                {
                                    "blockId": "senderName",
                                    "text": "Example-name customer"
                                },
                                {
                                    "blockId": "senderStreet",
                                    "text": "Albert-Schweitzer-Str. 8"
                                },
                                {
                                    "blockId": "senderCity",
                                    "text": "32457 Porta Westfalica"
                                },
                                {
                                    "blockId": "senderVIHBnumber",
                                    "text": "ASD123123QWE"
                                },
                                {
                                    "blockId": "disposerName",
                                    "text": "Example-name disposer"
                                },
                                {
                                    "blockId": "disposerStreet",
                                    "text": "An der Pforte 2123"
                                },
                                {
                                    "blockId": "disposerCity",
                                    "text": "Paris"
                                },
                                {
                                    "blockId": "originName",
                                    "text": "<originName>"
                                },
                                {
                                    "blockId": "originStreet",
                                    "text": "<originStreet>"
                                },
                                {
                                    "blockId": "originCity",
                                    "text": "<originCity>"
                                },
                                {
                                    "blockId": "subcontractorName",
                                    "text": "Example-name subcontractor"
                                },
                                {
                                    "blockId": "subcontractorStreet",
                                    "text": "An der Pforte 2"
                                },
                                {
                                    "blockId": "subcontractorCity",
                                    "text": "London"
                                },
                                {
                                    "blockId": "subcontractorVIHBnumber",
                                    "text": "QWE98798ASD"
                                },
                                {
                                    "blockId": "wdplantName",
                                    "text": "<wdplantName>"
                                },
                                {
                                    "blockId": "wdplantStreet",
                                    "text": "<wdplantStreet>"
                                },
                                {
                                    "blockId": "wdplantCity",
                                    "text": "<wdplantCity>"
                                },
                                {
                                    "blockId": "transporter",
                                    "text": "Example-name transporter"
                                },

                                {
                                    "blockId": "transporterName",
                                    "text": "<transporterName>"
                                },

                                {
                                    "blockId": "transporterStreet",
                                    "text": "<transporterStreet>"
                                },

                                {
                                    "blockId": "transporterCity",
                                    "text": "<transporterCity>"
                                },

                                {
                                    "blockId": "transporterVIHBnumber",
                                    "text": "<transporterVIHBnumber>"
                                },
                                {
                                    "blockId": "routeCollection",
                                    "text": "Yes"
                                },
                                {
                                    "blockId": "collectionScheme",
                                    "text": "No"
                                },
                                {
                                    "blockId": "repetiveLoads",
                                    "text": "Yes"
                                },
                                {
                                    "blockId": "wastestreamnumber",
                                    "text": "qwerty"
                                },
                                {
                                    "blockId": "commonWasteName",
                                    "text": "Example-name waste"
                                },
                                {
                                    "blockId": "euralcode",
                                    "text": "AS1234"
                                },
                                {
                                    "blockId": "method",
                                    "text": "Dunno"
                                },
                                {
                                    "blockId": "startDate",
                                    "text": "2021-01-01 00:00:00"
                                },
                                {
                                    "blockId": "receiptDate",
                                    "text": "2023-05-21 12:23:54"
                                }
                            ]
                        }
                    ]
                },
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [

                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [

                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [

                            ],
                            "tasks": []
                        }
                    ],
                    "orderType": "NL_50",
                    "orderPosNr": 2,
                    "orderExtId": "{$orderExtId}-2",
                    "materialExternalId": "mat-nl-1234",
                    "orderDocuments": [
                    ]
                },
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [

                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [

                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [

                            ],
                            "tasks": []
                        }
                    ],
                    "orderType": "NL_50",
                    "orderPosNr": 3,
                    "orderExtId": "{$orderExtId}-3",
                    "materialExternalId": "mat-nl-1234",
                    "orderDocuments": [
                    ]
                }
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "{$branchExternalId}",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "NL",
            "disposalSites": [
                {
                    "addressExtId": "2066747",
                    "weigh": false,
                    "disposalSiteExtId": "18144",
                    "name": "Porta W.-PWE (UMS)"
                },
                {
                    "addressExtId": "2589219",
                    "weigh": false,
                    "disposalSiteExtId": "24792",
                    "name": "Minden-PWE NL Minden Holler"
                }
            ],
            "interruptionExternalId": "1234",
            "tourDocuments": [
                {
                    "documentType": "NL_DTD1",
                    "documentName": "test tour document",
                    "deliveryServices": [],
                    "textBlocks": [
                        {
                            "blockId": "tourRef",
                            "text": "NLNND2896/12564406"
                        },
                        {
                            "blockId": "senderName",
                            "text": "Example-name customer"
                        },
                        {
                            "blockId": "senderStreet",
                            "text": "Albert-Schweitzer-Str. 8"
                        },
                        {
                            "blockId": "senderCity",
                            "text": "32457 Porta Westfalica"
                        },
                        {
                            "blockId": "senderVIHBnumber",
                            "text": "ASD123123QWE"
                        },
                        {
                            "blockId": "disposerName",
                            "text": "Example-name disposer"
                        },
                        {
                            "blockId": "disposerStreet",
                            "text": "An der Pforte 2123"
                        },
                        {
                            "blockId": "disposerCity",
                            "text": "Paris"
                        },
                        {
                            "blockId": "originName",
                            "text": "<originName>"
                        },
                        {
                            "blockId": "originStreet",
                            "text": "<originStreet>"
                        },
                        {
                            "blockId": "originCity",
                            "text": "<originCity>"
                        },
                        {
                            "blockId": "subcontractorName",
                            "text": "Example-name subcontractor"
                        },
                        {
                            "blockId": "subcontractorStreet",
                            "text": "An der Pforte 2"
                        },
                        {
                            "blockId": "subcontractorCity",
                            "text": "London"
                        },
                        {
                            "blockId": "subcontractorVIHBnumber",
                            "text": "QWE98798ASD"
                        },
                        {
                            "blockId": "wdplantName",
                            "text": "<wdplantName>"
                        },
                        {
                            "blockId": "wdplantStreet",
                            "text": "<wdplantStreet>"
                        },
                        {
                            "blockId": "wdplantCity",
                            "text": "<wdplantCity>"
                        },
                        {
                            "blockId": "transporter",
                            "text": "Example-name transporter"
                        },
                        {
                            "blockId": "transporterName",
                            "text": "<transporterName>"
                        },
                        {
                            "blockId": "transporterStreet",
                            "text": "<transporterStreet>"
                        },
                        {
                            "blockId": "transporterCity",
                            "text": "<transporterCity>"
                        },
                        {
                            "blockId": "transporterVIHBnumber",
                            "text": "<transporterVIHBnumber>"
                        },
                        {
                            "blockId": "routeCollection",
                            "text": "Yes"
                        },
                        {
                            "blockId": "collectionScheme",
                            "text": "No"
                        },
                        {
                            "blockId": "repetiveLoads",
                            "text": "Yes"
                        },
                        {
                            "blockId": "wastestreamnumber",
                            "text": "qwerty"
                        },
                        {
                            "blockId": "commonWasteName",
                            "text": "Example-name waste"
                        },
                        {
                            "blockId": "euralcode",
                            "text": "AS1234"
                        },
                        {
                            "blockId": "method",
                            "text": "Dunno"
                        },
                        {
                            "blockId": "startDate",
                            "text": "2021-01-01 00:00:00"
                        },
                        {
                            "blockId": "receiptDate",
                            "text": "2023-05-21 12:23:54"
                        }
                    ]
                }
            ]
        }
        JSON;
    }
}
