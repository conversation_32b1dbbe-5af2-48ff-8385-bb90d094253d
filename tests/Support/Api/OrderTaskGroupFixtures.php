<?php

namespace App\Tests\Support\Api;

use Codeception\Util\JsonArray;

class OrderTaskGroupFixtures
{
    public static function patchTaskGroupRequestV2(string $tourResponse): string
    {
        $taskgroup = self::getByJsonPath($tourResponse, '$.orders[0].taskgroups[2]')[0];

        $taskgroupJson = json_encode($taskgroup);

        $repeatableTask = self::getByJsonPath($taskgroupJson, '$.tasks[?(@.repeatable==true)]')[0];
        $selectTask = self::getByJsonPath($taskgroupJson, '$.tasks[?(@.name=="weighingnoteSentBySAP")]')[0];
        $additionalServiceTemplate = self::getByJsonPath($tourResponse, '$.additional_service_templates[0]')[0];

        return <<<JSON
        {
          "uuid": "{$taskgroup['uuid']}",
          "tasks": [
            {
              "uuid": "",
              "skipped": false,
              "additional_service": false,
              "repeat_service": true,
              "repeat_service_template_uuid": "{$repeatableTask['uuid']}",
              "inputs": [
                {
                  "type": "input-template",
                  "template_uuid": "{$repeatableTask['inputs'][0]['uuid']}",
                  "value": true
                }
              ]
            },
            {
              "uuid": "{$selectTask['uuid']}",
              "skipped": false,
              "additional_service": false,
              "repeat_service": false,
              "repeat_service_template_uuid": "",
              "inputs": [
                {
                  "type": "input",
                  "uuid": "{$selectTask['inputs'][1]['uuid']}",
                  "value": "{$selectTask['inputs'][1]['options'][0]['key']}"
                }
              ]
            },
            {
              "uuid": "",
              "skipped": false,
              "additional_service": true,
              "repeat_service": false,
              "repeat_service_template_uuid": "",
              "additional_service_template_uuid": "{$additionalServiceTemplate['template_uuid']}",
              "inputs": [
                {
                  "type": "input-template",
                  "template_uuid": "{$additionalServiceTemplate['inputs'][0]['template_uuid']}",
                  "value": "{$additionalServiceTemplate['inputs'][0]['options'][0]['key']}"
                },
                {
                  "type": "input-template",
                  "template_uuid": "{$additionalServiceTemplate['inputs'][1]['template_uuid']}",
                  "value": "{$additionalServiceTemplate['inputs'][1]['options'][0]['key']}"
                },
                {
                  "type": "input-template",
                  "template_uuid": "{$additionalServiceTemplate['inputs'][2]['template_uuid']}",
                  "value": 1234
                }
              ]
            }
          ]

        }
        JSON;
    }

    public static function patchSignatureTaskGroupRequestV2(string $tourResponse): string
    {
        $taskgroup = self::getByJsonPath($tourResponse, '$.orders[0].taskgroups[1]')[0];
        $task = self::getByJsonPath(json_encode($taskgroup), '$.tasks.[?(@.name=="Unterschrift (api-test t5)")]')[0];

        return <<<JSON
        {
          "uuid": "{$taskgroup['uuid']}",
          "tasks": [{
          "uuid": "{$task['uuid']}",
          "skipped": false,
          "additional_service": false,
          "repeat_service": false,
          "repeat_service_template_uuid": "",
          "inputs": [
            {
              "type": "input",
              "uuid": "{$task['inputs'][0]['uuid']}",
              "value": [
                {
                  "file": "app-user-files/9660aab1-fe84-4253-b45a-237be4678736",
                  "name": "test1.jpg",
                  "label": "test1"
                },
                {
                  "file": "app-user-files/5f082df8-3aaf-45a4-943b-238086cd6840",
                  "name": "test2.jpg",
                  "label": "test2"
                }
              ]
            }
          ]
          }]
        }
        JSON;
    }

    private static function getByJsonPath(string $json, string $jsonPath): array
    {
        return (new JsonArray($json))->filterByJsonPath($jsonPath);
    }
}
