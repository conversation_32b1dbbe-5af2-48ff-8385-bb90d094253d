<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Tests\Support\Api\ExpectedResults\GetAdditionalServiceExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetInputExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetSubOrderTaskGroupsExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskGroupRuleExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskGroupsExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskRuleExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTourExpectedResult;
use App\Tests\Support\ApiTester;

class SapGermanyTourFixtures
{
    public static function getDisposalSiteJsonOrg(): string
    {
        return <<<JSON
        [
            {
                "addressExtId": "2066747",
                "weigh": true,
                "disposalSiteExtId": "18144",
                "name": "weigh and 2 materials",
                "materialOptions": [
                  {
                    "name": "glas",
                    "sourceSystemId": "G123"
                  }, {
                    "name": "paper",
                    "sourceSystemId": "P321"
                  }
                ]
            },
            {
                "addressExtId": "2589219",
                "weigh": true,
                "disposalSiteExtId": "24792",
                "name": "weigh without materials"
            },
            {
                "addressExtId": "2066747",
                "weigh": false,
                "disposalSiteExtId": "55555",
                "name": "no weigh and no materials",
                "materialOptions": [
                  {
                    "name": "glas",
                    "sourceSystemId": "G123"
                  }, {
                    "name": "paper",
                    "sourceSystemId": "P321"
                  }
                ]
            },
            {
                "addressExtId": "2066747",
                "weigh": true,
                "disposalSiteExtId": "55553",
                "name": "weigh and 1 material",
                "materialOptions": [
                  {
                    "name": "glas",
                    "sourceSystemId": "G123"
                  }
                ]
            }
        ]
        JSON;
    }

    public static function getDisposalSiteJsonSingle(): string
    {
        return <<<JSON
        [
            {
                "addressExtId": "2589219",
                "weigh": true,
                "disposalSiteExtId": "24792",
                "name": "weigh without materials"
            }
        ]
        JSON;
    }

    public static function getDisposalSiteJsonExtended(): string
    {
        return <<<JSON
        [
            {
                "addressExtId": "2066747",
                "weigh": true,
                "disposalSiteExtId": "18144",
                "name": "weigh and 2+1 materials",
                "materialOptions": [
                  {
                    "name": "glas",
                    "sourceSystemId": "G123"
                  }, {
                    "name": "paper",
                    "sourceSystemId": "P321"
                  }, {
                    "name": "plastic",
                    "sourceSystemId": "P323"
                  }
                ]
            },
            {
                "addressExtId": "2589219",
                "weigh": true,
                "disposalSiteExtId": "24792",
                "name": "weigh without materials"
            },
            {
                "addressExtId": "2066747",
                "weigh": false,
                "disposalSiteExtId": "55555",
                "name": "no weigh and no materials",
                "materialOptions": [
                  {
                    "name": "glas",
                    "sourceSystemId": "G123"
                  }, {
                    "name": "paper",
                    "sourceSystemId": "P321"
                  }
                ]
            },
            {
                "addressExtId": "2066747",
                "weigh": true,
                "disposalSiteExtId": "55553",
                "name": "weigh and 1+1 material",
                "materialOptions": [
                  {
                    "name": "glas",
                    "sourceSystemId": "G123"
                  }, {
                    "name": "plastic",
                    "sourceSystemId": "P323"
                  }
                ]
            }
        ]
        JSON;
    }

    public static function tourRequestCreate(
        string $tourExtId = '7611351',
        string $orderExtId = 'WE00000000000083781455',
        string $tourName = 'ExampleTour with a name, that is a little longer',
        string $equipmentExtId = '10001234',
        string $equipmentLicensePlate = 'MI-AK 8546',
        ?\DateTime $start = null,
        ?\DateTime $end = null,
        ?array $mastertourExternalIds = null,
        bool $includeActivatedBySapTask = true,
        string $staffExternalId = ApiTester::TEST_STAFF_EXT_ID_PREFIX,
        string $staffExternalId2 = ApiTester::TEST_STAFF_EXT_ID_PREFIX,
        string $equipmentExtId2 = '10004635',
        string $equipmentLicensePlate2 = 'MI-AK 1092',
        string $branchExternalId = 'Oelbronn',
        ?string $disposalSiteJson = null,
    ): string {
        $startDate = ($start ?? new \DateTime()->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? new \DateTime()->setTime(23, 0))->format(DATE_ATOM);
        $mastertourAdditionToTour = '';

        $staffExternalId = ApiTester::TEST_STAFF_EXT_ID_PREFIX === $staffExternalId
            ? $staffExternalId.uniqid()
            : $staffExternalId;

        $staffExternalId2 = ApiTester::TEST_STAFF_EXT_ID_PREFIX === $staffExternalId2
            ? $staffExternalId2.uniqid()
            : $staffExternalId2;

        if (null !== $mastertourExternalIds && count($mastertourExternalIds) > 0) {
            $mastertourAdditionToTour = ',"mastertours":'.json_encode($mastertourExternalIds);
        }

        $weighingNoteJson = <<<JSON
        {
            "taskType": "weighingnote",
            "taskName": "weighingnoteSentBySAP",
            "elements": [],
            "taskExtId": ""
        }
        JSON;

        if (false === $includeActivatedBySapTask) {
            $weighingNoteJson = '';
        }

        if (null === $disposalSiteJson) {
            $disposalSiteJson = json_encode([]);
        }

        /* @TODO remove not-exiting task from testdata after SAP-update */
        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "{$staffExternalId}",
                    "personnelType": "DRIVER",
                    "firstName": "FirstName1",
                    "lastName": "LastName1"
                },
                {
                    "staffExtId": "{$staffExternalId2}",
                    "personnelType": "DRIVER",
                    "firstName": "FirstName2",
                    "lastName": "LastName2"
                },
                {
                  "staffExtId": "EXTSTAFFAPIUSER1",
                  "personnelType": "DRIVER",
                  "firstName": "Peter",
                  "lastName": "Parker"
                },
                {
                  "staffExtId": "EXTSTAFFAPIUSER2",
                  "personnelType": "DRIVER",
                  "firstName": "d2",
                  "lastName": "d2"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                },
                {
                    "country": "DE",
                    "postalCode": "32423",
                    "city": "Minden",
                    "district": "Innenstadt",
                    "street": "Aminghauser Str.",
                    "houseNumber": "7",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2589219"
                },
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Eisbergen",
                    "street": "Albert-Schweitzer-Str.",
                    "houseNumber": "8",
                    "phoneNumber": "+4957518135",
                    "state": "",
                    "addressExtId": "83622"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2021112"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                },
                {
                    "equipmentExtId": "{$equipmentExtId2}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate2}",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [
                        {
                            "sequence": 10,
                            "text": "1.04 B19 Kartonage-150101-ABF",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        },
                        {
                            "sequence": 20,
                            "text": "STANDARD ask-10-pr-BEHA",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        }
                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "container_count",
                                    "taskName": "Container amount",
                                    "elements": [],
                                    "taskExtId": ""
                                },
                                {
                                    "taskType": "not-existing",
                                    "taskName": "unknown task",
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {$weighingNoteJson}
                            ]
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "materialExternalId": "mat-de-1234",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": [
                        {
                            "documentType": "delivery_note",
                            "documentName": "deliverynote",
                            "deliveryServices": [
                                {
                                    "materialNumber": "969595",
                                    "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam hendrerit libero tincidunt, accumsan lacus sit amet, placerat odio. Curabitur arcu dolor, luctus nec consectetur viverra, rhoncus sit amet odio.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list",
                                    "unit": "kg"
                                },
                                {
                                    "materialNumber": "456784",
                                    "text": "Integer pulvinar eros non arcu faucibus euismod ut sed turpis. Nulla sollicitudin at magna id pellentesque. Duis facilisis ut nisi a molestie.",
                                    "taskExternalId": "pdfT2",
                                    "elementReferenceType": "scale_weighing_data",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "456841",
                                    "text": "Natus consequatur reiciendis sit et.",
                                    "taskExternalId": "pdfT3",
                                    "elementReferenceType": "scale_weighing_data",
                                    "templateField": "material-list",
                                    "unit": "test"
                                },
                                {
                                    "materialNumber": "123123",
                                    "text": "Duis feugiat justo ac porttitor elementum. Duis et ultricies elit. Nam mattis nisi sed tellus iaculis, id hendrerit dolor tincidunt.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "9632025",
                                    "text": "Mauris quis convallis tortor.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "7854521",
                                    "text": "Integer at arcu velit.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "12341",
                                    "text": "Nullam aliquam massa eu urna mattis, nec scelerisque urna rutrum.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "8452134",
                                    "text": "Nullam accumsan, libero sed congue sodales, neque felis aliquet mi, vitae ultricies metus sem quis nibh.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list",
                                    "unit": "really? anything?"
                                },
                                {
                                    "materialNumber": "123456789",
                                    "text": "Etiam lacus nisi, tincidunt sollicitudin neque eu, congue volutpat mauris. Nulla pellentesque iaculis neque sit amet volutpat. Nam lacinia metus tellus, vel bibendum felis tempor at.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "969595",
                                    "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam hendrerit libero tincidunt, accumsan lacus sit amet, placerat odio. Curabitur arcu dolor, luctus nec consectetur viverra, rhoncus sit amet odio.",
                                    "taskExternalId": "pdfT1",
                                    "templateField": "material-list",
                                    "elementReferenceType": "containerAmount",
                                    "unit": "give me a break :D"
                                },
                                {
                                    "materialNumber": "456784",
                                    "text": "Integer pulvinar eros non arcu faucibus euismod ut sed turpis. Nulla sollicitudin at magna id pellentesque. Duis facilisis ut nisi a molestie.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "456841",
                                    "text": "Natus consequatur reiciendis sit et.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "123123",
                                    "text": "Duis feugiat justo ac porttitor elementum. Duis et ultricies elit. Nam mattis nisi sed tellus iaculis, id hendrerit dolor tincidunt.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "9632025",
                                    "text": "Mauris quis convallis tortor.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "7854521",
                                    "text": "Integer at arcu velit.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "12341",
                                    "text": "Nullam aliquam massa eu urna mattis, nec scelerisque urna rutrum.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "969595",
                                    "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam hendrerit libero tincidunt, accumsan lacus sit amet, placerat odio. Curabitur arcu dolor, luctus nec consectetur viverra, rhoncus sit amet odio.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "456784",
                                    "text": "Integer pulvinar eros non arcu faucibus euismod ut sed turpis. Nulla sollicitudin at magna id pellentesque. Duis facilisis ut nisi a molestie.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "456841",
                                    "text": "Natus consequatur reiciendis sit et.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "123123",
                                    "text": "Duis feugiat justo ac porttitor elementum. Duis et ultricies elit. Nam mattis nisi sed tellus iaculis, id hendrerit dolor tincidunt.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "9632025",
                                    "text": "Mauris quis convallis tortor.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "7854521",
                                    "text": "Integer at arcu velit.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "12341",
                                    "text": "Nullam aliquam massa eu urna mattis, nec scelerisque urna rutrum.",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "containerAmount",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "000110022",
                                    "text": "i have an empty ref-type :(",
                                    "taskExternalId": "pdfT1",
                                    "elementReferenceType": "",
                                    "templateField": "material-list"
                                },
                                {
                                    "materialNumber": "000110023",
                                    "text": "i have no ref-type at all :(",
                                    "taskExternalId": "pdfT1",
                                    "templateField": "material-list"
                                }
                            ],
                            "textBlocks": [
                                {
                                    "blockId": "header-1",
                                    "text": "PreZero Service Westfalen GmbH & Co. KG\\nAn der Pforte 2, 32457 Porta <br>Westfalica\\npls regard"
                                },
                                {
                                    "blockId": "footer-1",
                                    "text": "Placeat rerum ut et enim ex eveniet facere sunt quia delectus aut nam et eum architecto fugit repellendus illo veritatis qui ex esse."
                                },
                                {
                                    "blockId": "footer-2",
                                    "text": "Voluptate vel possimus omnis aut incidunt sunt cumque asperiores incidunt iure sequi cum."
                                },
                                {
                                    "blockId": "footer-3",
                                    "text": "Rem aut rerum exercitationem est rem dicta voluptas fuga totam reiciendis qui architecto fugiat nemo omnis consequatur recusandae qui cupiditate eos quod."
                                },
                                {
                                    "blockId": "footer-4",
                                    "text": "Vel\\n optio provident non incidunt magnam molestias et quibusdam et ab quo voluptatum quia."
                                },
                                {
                                    "blockId": "tos",
                                    "text": "Es gelten die Allgemeinen Geschäftsbedingungen des Auftragnehmers, diese\\nkönnen angefordert oder im Internet unter www.prezero.com eingesehen\\nwerden."
                                },
                                {
                                    "blockId": "deliveryNoteInfo",
                                    "text": "some new \\ntext inside \\nshould do the job Voluptatibus est accusantium eveniet aut atque possimus aut dolores quis totam incidunt ducimus aperiam nesciunt est quia assumenda minima sunt qui similique.Voluptatibus est accusantium eveniet aut atque."
                                }
                            ]
                        }
                    ]
                }
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "{$branchExternalId}",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": {$disposalSiteJson},
            "additionalInformation": [
                {
                    "sequence": 10,
                    "text": "custom tour-leven addinfo 1",
                    "alsoFrontView": true,
                    "bold": false,
                    "icon": ""
                },
                {
                    "sequence": 20,
                    "text": "custom tour-leven addinfo 2",
                    "alsoFrontView": true,
                    "bold": false,
                    "icon": ""
                }
                            ],
            "interruptionExternalId": "1234"
            {$mastertourAdditionToTour}
        }
        JSON;
    }

    public static function order2TourRequestCreate(
        string $tourExtId = '7611351',
        string $order1ExtId = 'WE00000000000083781455',
        string $order2ExtId = 'WE00000000000083781455',
        string $tourName = 'ExampleTour with a name, that is a little longer',
        string $equipmentExtId = '10001234',
        string $equipmentLicensePlate = 'MI-AK 8546',
        string $staffExternalId = '681',
        ?\DateTime $start = null,
        ?\DateTime $end = null,
    ): string {
        $startDate = ($start ?? new \DateTime()->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? new \DateTime()->setTime(23, 0))->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "{$staffExternalId}",
                    "personnelType": "DRIVER",
                    "firstName": "Andreas",
                    "lastName": "Peters"
                },
                {
                  "staffExtId": "EXTSTAFFAPIUSER1",
                  "personnelType": "DRIVER",
                  "firstName": "Peter",
                  "lastName": "Parker"
                },
                {
                  "staffExtId": "EXTSTAFFAPIUSER2",
                  "personnelType": "DRIVER",
                  "firstName": "d2",
                  "lastName": "d2"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                },
                {
                    "country": "DE",
                    "postalCode": "32423",
                    "city": "Minden",
                    "district": "Innenstadt",
                    "street": "Aminghauser Str.",
                    "houseNumber": "7",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2589219"
                },
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Eisbergen",
                    "street": "Albert-Schweitzer-Str.",
                    "houseNumber": "8",
                    "phoneNumber": "+4957518135",
                    "state": "",
                    "addressExtId": "83622"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2021112"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "10004635",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "MI-AK 1092",
                    "containerMounting": ""
                },
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                },
                {
                    "equipmentExtId": "EQUIPMENT_1",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "DE PZ 1",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [
                        {
                            "sequence": 10,
                            "text": "1.04 B19 Kartonage-150101-ABF",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        },
                        {
                            "sequence": 20,
                            "text": "STANDARD ask-10-pr-BEHA",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        }
                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "container_count",
                                    "taskName": "Container amount",
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "weighingnote",
                                    "taskName": null,
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 1,
                    "orderExtId": "{$order1ExtId}",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": [
                    ]
                },
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [
                        {
                            "sequence": 10,
                            "text": "1.04 B19 Kartonage-150101-ABF",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        },
                        {
                            "sequence": 20,
                            "text": "STANDARD ask-10-pr-BEHA",
                            "alsoFrontView": true,
                            "bold": false,
                            "icon": ""
                        }
                    ],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "container_count",
                                    "taskName": "Container amount",
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [
                                {
                                    "sequence": 10,
                                    "text": "1.04 B19 Kartonage-150101-ABF",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                },
                                {
                                    "sequence": 20,
                                    "text": "STANDARD ask-10-pr-BEHA",
                                    "alsoFrontView": true,
                                    "bold": false,
                                    "icon": ""
                                }
                            ],
                            "tasks": [
                                {
                                    "taskType": "weighingnote",
                                    "taskName": null,
                                    "elements": [],
                                    "taskExtId": ""
                                }
                            ]
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 2,
                    "orderExtId": "{$order2ExtId}",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": [
                    ]
                }
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "Oelbronn",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [
                {
                    "addressExtId": "2066747",
                    "weigh": false,
                    "disposalSiteExtId": "18144",
                    "name": "Porta W.-PWE (UMS)"
                },
                {
                    "addressExtId": "2589219",
                    "weigh": false,
                    "disposalSiteExtId": "24792",
                    "name": "Minden-PWE NL Minden Holler"
                }
            ],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function tourMultiOrderRequestCreate(
        string $tourExtId = '7611352',
        string $orderExtId = 'WE00000000000083781456',
        string $tourName = 'ExampleTour with 3 orders',
        string $equipmentExtId = '10001235',
        string $equipmentLicensePlate = 'MI-AK 8547',
        ?\DateTime $start = null,
        ?\DateTime $end = null,
        bool $includeActivatedBySapTask = true,
        string $staffExternalId = ApiTester::TEST_STAFF_EXT_ID_PREFIX,
        string $branchExternalId = 'Oelbronn',
    ): string {
        $startDate = ($start ?? new \DateTime()->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? new \DateTime()->setTime(23, 0))->format(DATE_ATOM);
        $mastertourAdditionToTour = '';

        $staffExternalId = ApiTester::TEST_STAFF_EXT_ID_PREFIX === $staffExternalId
            ? $staffExternalId.uniqid()
            : $staffExternalId;

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "{$staffExternalId}",
                    "personnelType": "DRIVER",
                    "firstName": "FirstName1",
                    "lastName": "LastName1"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                },
                {
                    "country": "DE",
                    "postalCode": "32423",
                    "city": "Minden",
                    "district": "Innenstadt",
                    "street": "Aminghauser Str.",
                    "houseNumber": "7",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2589219"
                },
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Eisbergen",
                    "street": "Albert-Schweitzer-Str.",
                    "houseNumber": "8",
                    "phoneNumber": "+4957518135",
                    "state": "",
                    "addressExtId": "83622"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2021112"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [],
                            "tasks": []
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExtId}",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "materialExternalId": "mat-de-1234",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": []
                },
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [],
                            "tasks": []
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 2,
                    "orderExtId": "{$orderExtId}-2",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "materialExternalId": "mat-de-1234",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": []
                },
                {
                    "locationExtId": "83622",
                    "customerExtId": "2021112",
                    "additionalInformation": [],
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "83622",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DEPOT",
                            "locationExtId": "2066747",
                            "additionalInformation": [],
                            "tasks": []
                        },
                        {
                            "orderLocationType": "DISPOSALSITE",
                            "locationExtId": "2589219",
                            "additionalInformation": [],
                            "tasks": []
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 3,
                    "orderExtId": "{$orderExtId}-3",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "materialExternalId": "mat-de-1234",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": []
                }
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "{$branchExternalId}",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "additionalInformation": [],
            "interruptionExternalId": "1234",
            "mastertours": []
        }
        JSON;
    }

    public static function tourStaffCreate(
        string $staffExtId,
        string $tourExtId = '123456789',
        string $tourName = 'ExampleTourForStaff',
        ?\DateTime $start = null,
        ?\DateTime $end = null,
    ): string {
        $startDate = ($start ?? new \DateTime()->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? new \DateTime()->setTime(23, 0))->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "{$staffExtId}",
                    "personnelType": "DRIVER",
                    "firstName": "Max",
                    "lastName": "Mustermann"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [
            ],
            "equipments": [
                {
                    "equipmentExtId": "10004635",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "MI-AK 1092",
                    "containerMounting": ""
                }
            ],
            "orders": [
            ],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "Oelbronn",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [

            ],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function tourEquipmentCreate(
        ?string $externalId = null,
        ?string $licensePlate = null,
        ?EquipmentType $type = null,
        ?string $staffExtId = null,
    ): string {
        $startDate = ($start ?? new \DateTime()->setTime(2, 0))->format(DATE_ATOM);
        $endDate = ($end ?? new \DateTime()->setTime(23, 0))->format(DATE_ATOM);
        $tourExtId = uniqid();
        $tourName = 'ExampleTourForEquipment'.uniqid();
        $externalId = ($externalId ?? uniqid());
        $licensePlate = ($licensePlate ?? uniqid());
        $type = ($type ?? EquipmentType::ASK);
        $staff = [];

        if (null !== $staffExtId) {
            $staff[] = [
                'staffExtId' => $staffExtId,
                'personnelType' => 'DRIVER',
                'firstName' => 'Max',
                'lastName' => 'Mustermann',
            ];
        }

        $staff = json_encode($staff, JSON_THROW_ON_ERROR);

        return <<<JSON
        {
            "staff": {$staff},
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [
                {
                    "equipmentExtId": "{$externalId}",
                    "equipmentType": "{$type->value}",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$licensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "Oelbronn",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function getCreateTourResult(?string $tourName = null): GetTourExpectedResult
    {
        $tourName = $tourName ?? 'ExampleTour with a name, that is a little longer';

        return new GetTourExpectedResult(
            tourName: $tourName,
            status: 'created',
            hasAdditionalServices: true,
            additionalServiceExpectedResults: [
                new GetAdditionalServiceExpectedResult(
                    // serviceName: 'additional service task1',
                    serviceName: 'sap-api-test-as3',
                    inputCount: 3,
                    inputExpectedResults: [
                        new GetInputExpectedResult(
                            inputPosition: 0, label: 'input.addservice.mat', type: 'select', optionCount: 2,
                        ),
                        new GetInputExpectedResult(
                            inputPosition: 1, label: 'input.addservice.unit', type: 'select', optionCount: 2,
                        ),
                    ],
                ),
            ],
            equipmentCount: 2,
            equipmentNames: [],
            orderCount: 1,
            orderTaskGroupsExpectedResults: [
                new GetTaskGroupsExpectedResult(
                    position: 0,
                    taskGroupCount: 8,
                    taskExpectedResults: [
                        new GetTaskExpectedResult(
                            position: 1,
                            taskCount: 4,
                            expectedNames: [
                                'Ankunft (api-test t2)', // from config
                                'Fertig (api-test t6)', // from config
                                'Container amount', // replaced by payload
                            ],
                            restrictedNames: [
                                'Kundenwiegung (api-test t4)', // onlyForAssignment
                                // 'Unterschrift (api-test t5)', // onlyForAssignment
                                'Behälteranzahl (api-test t3)', // replaced by payload
                            ],
                            expectedAutocompleteOptions: [
                            ],
                            taskRuleExpectedResults: [
                                new GetTaskRuleExpectedResult(taskIndex: 1),
                                new GetTaskRuleExpectedResult(taskIndex: 2),
                            ]
                        ),
                        new GetTaskExpectedResult(
                            position: 2,
                            taskCount: 6,
                            expectedNames: [
                            ],
                            restrictedNames: [
                            ],
                            expectedAutocompleteOptions: [
                                'Auto 1',
                            ],
                            taskRuleExpectedResults: [
                            ]
                        ),
                    ],

                    taskGroupRuleExpectedResults: [
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 1),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 4),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 5),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 2),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 3),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 5),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 6),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 7),
                    ]
                ),
            ],
            tourTerminationCount: 1,
            tourTerminationTaskGroupsExpectedResults: [
                new GetTaskGroupsExpectedResult(
                    position: 0,
                    taskGroupCount: 2,
                    taskExpectedResults: [
                        new GetTaskExpectedResult(
                            position: 0,
                            taskCount: 4,
                            expectedNames: [
                                'Init task for t-rules (api-test)',
                                'Choose for t-taskgrouprule (api-test)',
                                'Choose for t-taskrule (api-test)',
                                'Optional only by t-taskrule (api-test)',
                            ],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [
                            ],
                            taskRuleExpectedResults: [
                                new GetTaskRuleExpectedResult(taskIndex: 3),
                            ],
                        ),
                    ],
                    taskGroupRuleExpectedResults: [
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 1),
                    ]
                ),
            ], orderNoteTaskGroupExpectedResults: [
                new GetSubOrderTaskGroupsExpectedResult(
                    orderPosition: 0,
                    taskGroupsExpectedResult: [new GetTaskGroupsExpectedResult(
                        position: 0,
                        taskGroupCount: 2,
                        taskExpectedResults: [
                            new GetTaskExpectedResult(
                                position: 0,
                                taskCount: 4,
                                expectedNames: [
                                    'Init task for n-rules (api-test)',
                                    'Choose for n-taskgrouprule (api-test)',
                                    'Choose for n-taskrule (api-test)',
                                    'Optional only by n-taskrule (api-test)',
                                ],
                                restrictedNames: [],
                                expectedAutocompleteOptions: [
                                ],
                                taskRuleExpectedResults: [
                                    new GetTaskRuleExpectedResult(taskIndex: 3),
                                ],
                            ),
                        ],
                        taskGroupRuleExpectedResults: [
                            new GetTaskGroupRuleExpectedResult(taskGroupIndex: 1),
                        ]
                    )]
                ),
            ]
        );
    }
}
