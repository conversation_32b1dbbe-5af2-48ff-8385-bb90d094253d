<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

class NoteFixtures
{
    public static function noteOrderRequestV2(array $noteTemplate): string
    {
        $value1 = json_encode(self::getExampleInputValue($noteTemplate['taskgroups'][0]['tasks'][0]['inputs'][0]));

        return <<<JSON
        {
            "template_uuid": "{$noteTemplate['template_uuid']}",
            "taskgroups": [
              {
                "template_uuid": "{$noteTemplate['taskgroups'][0]['template_uuid']}",
                "tasks": [
                  {
                    "template_uuid": "{$noteTemplate['taskgroups'][0]['tasks'][0]['template_uuid']}",
                    "skipped": false,
                    "inputs": [
                      {
                        "template_uuid": "{$noteTemplate['taskgroups'][0]['tasks'][0]['inputs'][0]['template_uuid']}",
                        "value": {$value1}
                      }
                    ]
                  }
                ]
              }
            ]
        }
        JSON;
    }

    private static function getExampleInputValue(array $input): mixed
    {
        return match ($input['type']) {
            'accept', 'boolean' => true,
            'select' => $input['options'][0]['key'],
            'number' => 123,
            'string', 'text' => uniqid(),
            'signature', 'photo' => [
                [
                    'file' => 'some-file-name.jpg',
                    'name' => 'Some file name',
                ],
            ],
            'qr' => ['some', 'values'],
            default => throw new \RuntimeException('Unknown input type'),
        };
    }
}
