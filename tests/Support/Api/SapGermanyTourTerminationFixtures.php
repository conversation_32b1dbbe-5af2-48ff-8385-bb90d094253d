<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use App\Tests\Support\Api\ExpectedResults\GetTaskExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskGroupsExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTerminationTemplateExpectedResult;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnHasTaskGroupsTemplateDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputScaleDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputTemplateDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskGroupTemplateDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskTemplateDescriber;

class SapGermanyTourTerminationFixtures
{
    public static function getTourTermination(): GetTerminationTemplateExpectedResult
    {
        return new GetTerminationTemplateExpectedResult(
            name: '{{Customer_not_on_site}}',
            taskGroupExpectedResults: [
                new GetTaskGroupsExpectedResult(
                    position: 0,
                    taskGroupCount: 2,
                    taskExpectedResults: [
                        new GetTaskExpectedResult(
                            position: 0,
                            taskCount: 4,
                            expectedNames: [
                                'Init task for t-rules (api-test)',
                                'Choose for t-taskgrouprule (api-test)',
                                'Choose for t-taskrule (api-test)',
                                'Optional only by t-taskrule (api-test)',
                            ],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                        new GetTaskExpectedResult(
                            position: 1,
                            taskCount: 1,
                            expectedNames: ['home of t-tg-rule (api-test)'],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                    ],
                    taskGroupRuleExpectedResults: [],
                ),
            ],
        );
    }

    public static function getReturnToBackendTourTerminationDescription(): ReturnHasTaskGroupsTemplateDescriber
    {
        return new ReturnHasTaskGroupsTemplateDescriber(
            position: 0,
            taskGroups: [
                new ReturnTaskGroupTemplateDescriber(
                    position: 0,
                    tasks: [
                        new ReturnTaskTemplateDescriber(
                            position: 0,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0,
                                    value: true,
                                    selectOption: null,
                                ),
                            ]
                        ),
                        new ReturnTaskTemplateDescriber(
                            position: 1,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0,
                                    value: null,
                                    selectOption: 1,
                                ),
                            ]
                        ),
                        new ReturnTaskTemplateDescriber(
                            position: 2,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0,
                                    value: null,
                                    selectOption: 0,
                                ),
                            ]
                        ),
                        new ReturnTaskTemplateDescriber(
                            position: 3,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0,
                                    value: 4711,
                                    selectOption: null,
                                ),
                            ]
                        ),
                    ]
                ),
                new ReturnTaskGroupTemplateDescriber(
                    position: 0,
                    tasks: [
                        new ReturnTaskTemplateDescriber(
                            position: 0,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0,
                                    value: 1174,
                                    selectOption: null,
                                ),
                                new ReturnInputTemplateDescriber(
                                    position: 1,
                                    value: [new ReturnInputScaleDescriber(
                                        netto: 0.7,
                                        status: 'ok',
                                        unit: 'to',
                                        isManualInput: true,
                                        brutto: 0.8,
                                        timestamp: '2024-06-10T12:33:00.000+02:00',
                                        timestampReceivedAt: '2024-06-10T12:32:59.000+02:00',
                                        tara: null,
                                        weighingUuid: 'd4963c9f-4b69-4644-9a86-ff912c004dba',
                                    )],
                                    selectOption: null,
                                ),
                            ]
                        ),
                    ]
                ),
            ]
        );
    }
}
