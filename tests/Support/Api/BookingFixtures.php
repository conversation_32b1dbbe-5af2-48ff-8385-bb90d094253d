<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

readonly class BookingFixtures
{
    public static function bookingStartRequestV2(bool $force = true): string
    {
        $force = $force ? 'true' : 'false';

        return <<<JSON
        {
            "booking": "start",
            "force": {$force}
        }
        JSON;
    }

    public static function bookingEndRequestV2(): string
    {
        return <<<JSON
        {
            "booking": "end",
            "force": true
        }
        JSON;
    }
}
