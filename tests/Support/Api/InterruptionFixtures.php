<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

class InterruptionFixtures
{
    public static function bookingInterruptionStartRequestV2(string $clientUuid): string
    {
        return <<<JSON
        {
            "booking": "start",
            "client_uuid": "{$clientUuid}"
        }
        JSON;
    }

    public static function bookingInterruptionEndRequestV2(string $clientUuid): string
    {
        return <<<JSON
        {
            "booking": "end",
            "client_uuid": "$clientUuid"
        }
        JSON;
    }
}
