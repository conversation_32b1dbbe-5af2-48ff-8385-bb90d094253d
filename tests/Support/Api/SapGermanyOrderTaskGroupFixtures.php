<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputFileDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputScaleDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskGroupDescriber;
use Symfony\Component\Uid\Uuid;

class SapGermanyOrderTaskGroupFixtures
{
    public static function getReturnToBackendOrderTaskGroupDescription(): ReturnTaskGroupDescriber
    {
        return new ReturnTaskGroupDescriber(
            position: 2,
            tasks: [
                new ReturnTaskDescriber(
                    position: 0,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0, value: true, selectOption: null,
                        ),
                    ]
                ),
                new ReturnTaskDescriber(
                    position: 1,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: 'ws123',
                            selectOption: null
                        ),
                        new ReturnInputDescriber(
                            position: 1,
                            value: null,
                            selectOption: 1
                        ),
                        new ReturnInputDescriber(
                            position: 2,
                            value: 42,
                            selectOption: null
                        ),
                        new ReturnInputDescriber(
                            position: 3,
                            value: ['qr123'],
                            selectOption: null
                        ),
                        new ReturnInputDescriber(
                            position: 4,
                            value: [
                                new ReturnInputFileDescriber(file: 'app-user-files/'.Uuid::v1()->toRfc4122(), name: 'signature.png', label: 'Entsorgungsanlage (api-test)'),
                            ],
                            selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 5,
                            value: [
                                new ReturnInputScaleDescriber(
                                    netto: 3.3,
                                    status: 'ok',
                                    unit: 'kg',
                                    isManualInput: true,
                                    brutto: 2.2,
                                    timestamp: '2024-06-10T10:46:00.000+02:00',
                                    timestampReceivedAt: '2024-06-10T10:45:59.000+02:00',
                                    puk: 'puk333',
                                    tara: 4.4,
                                    weighingUuid: '76f386fa-02f0-448e-979a-6f65609bc5cc',
                                    wasteType: 42,
                                ),
                            ],
                            selectOption: null,
                        ),
                    ],
                ),
                new ReturnTaskDescriber(
                    position: 2,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: true,
                            selectOption: null
                        ),
                    ],
                ),

                // repeat service
                new ReturnTaskDescriber(
                    position: 2,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: true,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: true,
                            selectOption: null
                        ),
                    ],
                ),
                // Scales
                new ReturnTaskDescriber(
                    position: 3,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: [
                                new ReturnInputScaleDescriber(
                                    netto: 1.1,
                                    status: 'ok',
                                    unit: 'kg',
                                    isManualInput: false,
                                    brutto: 2.2,
                                    timestamp: '2024-06-10T14:46:00.000+02:00',
                                    timestampReceivedAt: '2024-06-14T10:45:59.000+02:00',
                                    puk: 'puk333',
                                    tara: 0.1,
                                    weighingUuid: '76f386fa-02f0-448e-979a-6f65609bc5cc',
                                ),
                                new ReturnInputScaleDescriber(
                                    netto: 1.5,
                                    status: 'ok',
                                    unit: 'kg',
                                    isManualInput: false,
                                    brutto: 2.2,
                                    timestamp: '2024-06-10T14:46:00.000+02:00',
                                    timestampReceivedAt: '2024-06-10T14:45:59.000+02:00',
                                    puk: 'puk333',
                                    tara: 0.1,
                                    weighingUuid: '76f386fa-02f0-448e-979a-6f65609bc5cc',
                                ),
                                new ReturnInputScaleDescriber(
                                    netto: 2.3,
                                    status: 'ok',
                                    unit: 'kg',
                                    isManualInput: false,
                                    brutto: 2.2,
                                    timestamp: '2024-06-10T14:46:00.000+02:00',
                                    timestampReceivedAt: '2024-06-10T14:45:59.000+02:00',
                                    puk: 'puk333',
                                    tara: 0.1,
                                    weighingUuid: '76f386fa-02f0-448e-979a-6f65609bc5cc',
                                    wasteType: 55,
                                ),
                                new ReturnInputScaleDescriber(
                                    netto: 14,
                                    status: 'underload',
                                    unit: 'kg',
                                    isManualInput: true,
                                    brutto: null,
                                    timestamp: '2025-03-17T15:07:21.185+01:00',
                                    timestampReceivedAt: '2025-03-17T15:07:21.185+01:00',
                                    puk: null,
                                    tara: null,
                                    weighingUuid: 'd332167a-e75a-4fa3-af90-1c7b75ee46bd',
                                    wasteType: 66,
                                ),
                            ],
                            selectOption: null
                        ),
                    ],
                ),
                new ReturnTaskDescriber(
                    position: 4,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: [
                                new ReturnInputScaleDescriber(
                                    netto: 2.2,
                                    status: 'underload',
                                    unit: 'kg',
                                    isManualInput: true,
                                    brutto: 3.3,
                                    timestamp: '2024-06-10T10:46:00.000+02:00',
                                    timestampReceivedAt: '2024-06-10T10:45:59.000+02:00',
                                    puk: 'puk333',
                                    tara: 0.1,
                                    weighingUuid: '76f386fa-02f0-448e-979a-6f65609bc5cc',
                                ),
                            ],
                            selectOption: null
                        ),
                    ],
                ),
                new ReturnTaskDescriber(
                    position: 5,
                    completedAt: new \DateTimeImmutable(),
                    skipped: true,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: []
                ),

                // additional services
                new ReturnTaskDescriber(
                    position: 0,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: true,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: null,
                            selectOption: 1
                        ),
                        new ReturnInputDescriber(
                            position: 1,
                            value: null,
                            selectOption: 1
                        ),
                        new ReturnInputDescriber(
                            position: 2,
                            value: 44,
                            selectOption: null
                        ),
                    ],
                ),

                new ReturnTaskDescriber(
                    position: 0,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: true,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0,
                            value: null,
                            selectOption: 0
                        ),
                        new ReturnInputDescriber(
                            position: 1,
                            value: null,
                            selectOption: 0
                        ),
                        new ReturnInputDescriber(
                            position: 2,
                            value: 45,
                            selectOption: null
                        ),
                    ],
                ),
            ]
        );
    }
}
