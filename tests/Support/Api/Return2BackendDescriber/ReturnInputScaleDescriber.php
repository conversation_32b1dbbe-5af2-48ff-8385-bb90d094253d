<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\Return2BackendDescriber;

class ReturnInputScaleDescriber implements \JsonSerializable
{
    public function __construct(
        public float $netto,
        public string $status,
        public string $unit,
        public bool $isManualInput,
        public ?float $brutto = null,
        public ?string $timestamp = null,
        public ?string $timestampReceivedAt = null,
        public ?string $puk = null,
        public ?float $tara = null,
        public ?string $weighingUuid = null,
        public ?int $wasteType = null,
    ) {
    }

    #[\Override]
    public function jsonSerialize(): mixed
    {
        return [
            'netto' => $this->netto,
            'status' => $this->status,
            'unit' => $this->unit,
            'is_manual_input' => $this->isManualInput,
            'brutto' => $this->brutto,
            'timestamp' => $this->timestamp,
            'timestamp_received_at' => $this->timestampReceivedAt,
            'puk' => $this->puk,
            'tara' => $this->tara,
            'weighing_uuid' => $this->weighingUuid,
            'waste_type' => $this->wasteType,
        ];
    }
}
