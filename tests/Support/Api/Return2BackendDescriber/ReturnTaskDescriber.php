<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\Return2BackendDescriber;

class ReturnTaskDescriber
{
    /**
     * @param array<ReturnInputDescriber> $inputs
     */
    public function __construct(
        public int $position,
        public \DateTimeImmutable $completedAt,
        public bool $skipped,
        public bool $additionalService,
        public string $additionalServiceTemplateUuid,
        public bool $repeatService,
        public string $repeatServiceTemplateUuid,
        public array $inputs,
    ) {
    }
}
