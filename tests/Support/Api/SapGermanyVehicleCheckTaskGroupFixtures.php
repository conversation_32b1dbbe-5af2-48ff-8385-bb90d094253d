<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputFileDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputScaleDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskGroupDescriber;
use Symfony\Component\Uid\Uuid;

class SapGermanyVehicleCheckTaskGroupFixtures
{
    public static function getReturnToBackendVehicleCheck1TaskGroupDescription(): ReturnTaskGroupDescriber
    {
        return new ReturnTaskGroupDescriber(
            position: 2,
            tasks: [
                new ReturnTaskDescriber(
                    position: 0,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0, value: true, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 1, value: true, selectOption: null,
                        ),
                    ]
                ),

                new ReturnTaskDescriber(
                    position: 1,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0, value: true, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 1, value: true, selectOption: null,
                        ),
                    ]
                ),
            ],
        );
    }

    public static function getReturnToBackendVehicleCheck2TaskGroupDescription(): ReturnTaskGroupDescriber
    {
        return new ReturnTaskGroupDescriber(
            position: 3,
            tasks: [
                new ReturnTaskDescriber(
                    position: 0,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0, value: true, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 1, value: true, selectOption: null,
                        ),
                    ]
                ),

                new ReturnTaskDescriber(
                    position: 1,
                    completedAt: new \DateTimeImmutable(),
                    skipped: false,
                    additionalService: false,
                    additionalServiceTemplateUuid: '',
                    repeatService: false,
                    repeatServiceTemplateUuid: '',
                    inputs: [
                        new ReturnInputDescriber(
                            position: 0, value: true, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 1, value: false, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 2, value: true, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 3, value: 'test123', selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 4, value: 122, selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 5, value: ['qr1', 'qr2'], selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 6, value: '2024-09-12T09:45:00.000+00:00', selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 7, value: '2024-09-12T09:45:00.000+00:00', selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 8, value: null, selectOption: 1,
                        ),
                        new ReturnInputDescriber(
                            position: 9,
                            value: [
                                new ReturnInputFileDescriber(file: 'app-user-files/'.Uuid::v1()->toRfc4122(), name: 'sample-image.jpg', label: 'control-picture'),
                            ],
                            selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 10,
                            value: [
                                new ReturnInputFileDescriber(file: 'app-user-files/'.Uuid::v1()->toRfc4122(), name: 'signature.png', label: 'control-signature'),
                            ],
                            selectOption: null,
                        ),
                        new ReturnInputDescriber(
                            position: 11,
                            value: [
                                new ReturnInputScaleDescriber(
                                    netto: 3.3,
                                    status: 'ok',
                                    unit: 'kg',
                                    isManualInput: true,
                                    brutto: 2.2,
                                    timestamp: '2024-06-10T10:46:00.000+02:00',
                                    timestampReceivedAt: '2024-06-10T10:45:59.000+02:00',
                                    puk: 'puk333',
                                    tara: 4.4,
                                    weighingUuid: '76f386fa-02f0-448e-979a-6f65609bc5cc'
                                ),
                            ],
                            selectOption: null,
                        ),
                    ]
                ),
            ],
        );
    }
}
