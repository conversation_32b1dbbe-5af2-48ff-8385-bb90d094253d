<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\WireMock;

trait WireMockMappingTrait
{
    public function wireMocksetIotStatusCallMappings(
        string $url,
        string $activeUser,
        ?string $contractNumber,
        int $responseCode = 200,
    ): string {
        return $this->setIotGeneralStatusCallMappings(
            $url,
            $activeUser,
            $contractNumber,
            $responseCode,
        );
    }

    private function setIotGeneralStatusCallMappings(
        string $url,
        string $activeUser,
        ?string $contractNumber,
        int $responseCode = 200,
    ): string {
        $jsonBody = [
            [
                'sensorId' => '${json-unit.any-string}',
                'containerAction' => '${json-unit.any-string}',
                'activeUser' => $activeUser,
                'inputType' => '${json-unit.any-string}',
                'locationExternalId' => '${json-unit.any-string}',
                'locationType' => '${json-unit.any-string}',
                'customerExternalId' => '${json-unit.any-string}',
                'contractNumber' => $contractNumber,
                'timestamp' => '${json-unit.any-string}',
                'materialExternalId' => '${json-unit.any-string}',
                'latitude' => null,
                'longitude' => null,
                'branchExternalId' => '${json-unit.any-string}',
            ],
        ];

        return $this->wireMockAddJsonRequestMapping(
            method: 'PATCH',
            url: $url,
            requestBodyJson: $jsonBody,
            testScenario: self::class,
            responseCode: $responseCode,
        );
    }

    public function wireMockAddSapGermanyStatusCallMapping(
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        string $status,
        int $responseCode = 200,
    ): string {
        $url = '/sap-germany/objectStatus';

        return $this->wireMockAddSapGeneralStatusCallMapping(
            $url,
            $objectType,
            $objectExternalId,
            $subType,
            $subTypeExternalId,
            $staffExtIds,
            $equipmentExtIds,
            $status,
            $responseCode,
        );
    }

    public function wireMockAddSapGermanyConfirmationCallMapping(
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        array $orderLocations,
        ?array $termination,
        ?array $notes,
    ): string {
        $url = '/sap-germany/objectConfirmation';

        return $this->wireMockAddSapGeneralConfirmationCallMapping(
            $url,
            $objectType,
            $objectExternalId,
            $subType,
            $subTypeExternalId,
            $staffExtIds,
            $equipmentExtIds,
            $orderLocations,
            $termination,
            $notes,
        );
    }

    /**
     * @return string[]
     */
    public function wireMockSetSapGermanyStatusTourStartMappings(
        string $tourExtId,
        array $staffExtIds,
        array $equipmentExtIds,
        int $responseCode = 200,
    ): array {
        $mappingIds = [];

        $mappingIds[] = $this->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: $staffExtIds,
            equipmentExtIds: $equipmentExtIds,
            status: 'started',
            responseCode: $responseCode
        );

        foreach ($staffExtIds as $staffExtId) {
            $mappingIds[] = $this->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $tourExtId,
                subType: 'staff',
                subTypeExternalId: $staffExtId,
                staffExtIds: $staffExtIds,
                equipmentExtIds: $equipmentExtIds,
                status: 'started',
                responseCode: $responseCode,
            );
        }

        foreach ($equipmentExtIds as $equipmentExtId) {
            $mappingIds[] = $this->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $tourExtId,
                subType: 'equipment',
                subTypeExternalId: $equipmentExtId,
                staffExtIds: $staffExtIds,
                equipmentExtIds: $equipmentExtIds,
                status: 'started',
                responseCode: $responseCode,
            );
        }

        return $mappingIds;
    }

    public function wireMockSetSapNetherlandsStatusTourStartMappings(
        string $tourExtId,
        array $staffExtIds,
        array $equipmentExtIds,
        int $responseCode = 200,
    ): array {
        $expectationIds = [];

        $expectationIds[] = $this->wireMockAddSapNetherlandsStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: $staffExtIds,
            equipmentExtIds: $equipmentExtIds,
            status: 'started',
            responseCode: $responseCode
        );

        foreach ($staffExtIds as $staffExtId) {
            $expectationIds[] = $this->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $tourExtId,
                subType: 'staff',
                subTypeExternalId: $staffExtId,
                staffExtIds: $staffExtIds,
                equipmentExtIds: $equipmentExtIds,
                status: 'started',
                responseCode: $responseCode,
            );
        }

        foreach ($equipmentExtIds as $equipmentExtId) {
            $expectationIds[] = $this->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $tourExtId,
                subType: 'equipment',
                subTypeExternalId: $equipmentExtId,
                staffExtIds: $staffExtIds,
                equipmentExtIds: $equipmentExtIds,
                status: 'started',
                responseCode: $responseCode,
            );
        }

        return $expectationIds;
    }

    public function wireMockSetSapSpainStatusTourStartMappings(
        string $tourExtId,
        array $staffExtIds,
        array $equipmentExtIds,
        int $responseCode = 200,
    ): array {
        $expectationIds = [];

        $expectationIds[] = $this->wireMockAddSapSpainStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: $staffExtIds,
            equipmentExtIds: $equipmentExtIds,
            status: 'started',
            responseCode: $responseCode
        );

        foreach ($staffExtIds as $staffExtId) {
            $expectationIds[] = $this->wireMockAddSapSpainStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $tourExtId,
                subType: 'staff',
                subTypeExternalId: $staffExtId,
                staffExtIds: $staffExtIds,
                equipmentExtIds: $equipmentExtIds,
                status: 'started',
                responseCode: $responseCode,
            );
        }

        foreach ($equipmentExtIds as $equipmentExtId) {
            $expectationIds[] = $this->wireMockAddSapSpainStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $tourExtId,
                subType: 'equipment',
                subTypeExternalId: $equipmentExtId,
                staffExtIds: $staffExtIds,
                equipmentExtIds: $equipmentExtIds,
                status: 'started',
                responseCode: $responseCode,
            );
        }

        return $expectationIds;
    }

    public function wireMockAddSapNetherlandsStatusCallMapping(
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        string $status,
        int $responseCode = 200,
    ): string {
        $url = '/sap-netherlands/Status';

        return $this->wireMockAddSapGeneralStatusCallMapping(
            $url,
            $objectType,
            $objectExternalId,
            $subType,
            $subTypeExternalId,
            $staffExtIds,
            $equipmentExtIds,
            $status,
            $responseCode,
        );
    }

    public function wireMockAddSapNetherlandsConfirmationCallMapping(
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        array $orderLocations,
        ?array $termination,
        ?array $notes,
    ): string {
        $url = '/sap-netherlands/ObjectConfirmation';

        return $this->wireMockAddSapGeneralConfirmationCallMapping(
            $url,
            $objectType,
            $objectExternalId,
            $subType,
            $subTypeExternalId,
            $staffExtIds,
            $equipmentExtIds,
            $orderLocations,
            $termination,
            $notes,
        );
    }

    private function wireMockAddSapGeneralStatusCallMapping(
        string $url,
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        string $status,
        int $responseCode = 200,
    ): string {
        $jsonBody = [
            'objectType' => $objectType,
            'objectExternalId' => $objectExternalId,
            'subType' => $subType,
            'subTypeExternalId' => $subTypeExternalId,
            'uuid' => '${json-unit.any-string}',
            'status' => $status,
            'mileage' => '${json-unit.any-number}',
            'timestamp' => '${json-unit.any-string}',
            'latitude' => '${json-unit.any-number}',
            'longitude' => '${json-unit.any-number}',
        ];
        if (null !== $staffExtIds) {
            $jsonBody['staff'] = $staffExtIds;
        }
        if (null !== $equipmentExtIds) {
            $jsonBody['equipments'] = $equipmentExtIds;
        }

        return $this->wireMockAddJsonRequestMapping(
            method: 'POST',
            url: $url,
            requestBodyJson: $jsonBody,
            headers: [
                'Idempotency-Key' => ['matches' => '[A-Za-z0-9_-]{10,50}'],
            ],
            responseCode: $responseCode,
        );
    }

    private function wireMockAddSapGeneralConfirmationCallMapping(
        string $url,
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        array $orderLocations,
        ?array $termination,
        ?array $notes,
    ): string {
        $jsonBody = [
            'objectType' => $objectType,
            'objectExternalId' => $objectExternalId,
            'subType' => $subType,
            'subTypeExternalId' => $subTypeExternalId,
            'uuid' => '${json-unit.any-string}',
            'objectOrderLocations' => $orderLocations,
            'termination' => $termination,
            'notes' => $notes,
        ];
        if (null !== $staffExtIds) {
            $jsonBody['staff'] = $staffExtIds;
        }
        if (null !== $equipmentExtIds) {
            $jsonBody['equipments'] = $equipmentExtIds;
        }

        return $this->wireMockAddJsonRequestMapping(
            method: 'POST',
            url: $url,
            requestBodyJson: $jsonBody,
            headers: [
                'Idempotency-Key' => ['matches' => '[A-Za-z0-9_-]{10,50}'],
            ],
        );
    }

    public function wireMockAddSapSpainStatusCallMapping(
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        string $status,
        int $responseCode = 200,
    ): string {
        $url = '/sap-spain/nl.gmt.clear.api.objectStatus';

        return $this->wireMockAddSapGeneralStatusCallMapping(
            $url,
            $objectType,
            $objectExternalId,
            $subType,
            $subTypeExternalId,
            $staffExtIds,
            $equipmentExtIds,
            $status,
            $responseCode,
        );
    }

    public function wireMockAddSapSpainConfirmationCallMapping(
        string $objectType,
        string $objectExternalId,
        ?string $subType,
        ?string $subTypeExternalId,
        ?array $staffExtIds,
        ?array $equipmentExtIds,
        array $orderLocations,
        ?array $termination,
        ?array $notes,
    ): string {
        $url = '/sap-spain/nl.gmt.clear.api.objectConfirmation';

        return $this->wireMockAddSapGeneralConfirmationCallMapping(
            $url,
            $objectType,
            $objectExternalId,
            $subType,
            $subTypeExternalId,
            $staffExtIds,
            $equipmentExtIds,
            $orderLocations,
            $termination,
            $notes,
        );
    }

    public function wireMockClearGeotabMappings(string $geotabDeviceId): void
    {
        $this->wireMockClearTestScenarioMappings('geotab-'.$geotabDeviceId);
    }

    public function wireMockSetGeotabMappings(
        string $geotabDeviceId,
        float $mileage,
    ): void {
        $this->wireMockAddRegexRequestMapping(
            method: 'POST',
            url: '/geotab/APIV1',
            requestRegex: '(\S+)(%22method%22%3A%22Authenticate%22)(\S+)',
            testScenario: 'geotab-'.$geotabDeviceId,
            responseBody: json_encode([
                'result' => [
                    'credentials' => [
                        'sessionId' => 'geotab-test-token',
                        'database' => 'test-db',
                        'userName' => 'test-user',
                    ],
                ],
                'jsonrpc' => '2.0',
            ]),
        );

        $this->wireMockAddRegexRequestMapping(
            method: 'POST',
            url: '/geotab/APIV1',
            requestRegex: '(\S+)(%22method%22%3A%22GET%22)(\S+)',
            testScenario: 'geotab-'.$geotabDeviceId,
            responseBody: json_encode([
                'result' => [
                    [
                        'data' => $mileage,
                        'dateTime' => '2024-06-24T12:45:29.000Z',
                        'device' => [
                            'id' => $geotabDeviceId,
                        ],
                        'diagnostic' => [
                            'id' => 'DiagnosticOdometerAdjustmentId',
                        ],
                        'controller' => 'ControllerNoneId',
                        'id' => null,
                    ],
                ],
                'jsonrpc' => '2.0',
            ]),
        );
    }
}
