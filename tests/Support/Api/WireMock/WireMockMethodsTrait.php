<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\WireMock;

use App\Tests\Support\ApiTester;

trait WireMockMethodsTrait
{
    public function wireMockAddBinaryFileMapping(
        string $method,
        string $base64Content,
        string $testScenario = 'hermes-test',
        ?array $headers = null,
        string $responseBody = '',
        int $responseCode = 200,
        ?string $url = null,
        ?string $urlPattern = null,
    ): string {
        if (false === (null === $url xor null === $urlPattern)) {
            throw new \InvalidArgumentException('Either url or urlPattern must be set, but not both');
        }

        $mappingData = [
            'request' => [
                'method' => $method,
                'headers' => $headers,
                'bodyPatterns' => [
                    [
                        'binaryEqualTo' => $base64Content,
                    ],
                ],
            ],
            'response' => [
                'status' => $responseCode,
                'body' => $responseBody,
            ],
            'metadata' => [
                'test-scenario' => $testScenario,
            ],
        ];

        if (null !== $url) {
            $mappingData['request']['url'] = $url;
        } elseif (null !== $urlPattern) {
            $mappingData['request']['urlPattern'] = $urlPattern;
        }

        $this->sendPost(
            $this->getWireMockUrl().'/__admin/mappings',
            json_encode($mappingData)
        );
        $this->seeResponseCodeIsSuccessful();

        return $this->grabDataFromResponseByJsonPath('$.id')[0];
    }

    public function wireMockAddJsonRequestMapping(
        string $method,
        string $url,
        array $requestBodyJson,
        string $testScenario = 'hermes-test',
        ?array $headers = null,
        string $responseBody = '',
        int $responseCode = 200,
    ): string {
        $mappingData = [
            'request' => [
                'url' => $url,
                'method' => $method,
                'headers' => $headers,
                'bodyPatterns' => [
                    [
                        'equalToJson' => $requestBodyJson,
                        'ignoreArrayOrder' => true,
                        'ignoreExtraElements' => true,
                    ],
                ],
            ],
            'response' => [
                'status' => $responseCode,
                'body' => $responseBody,
            ],
            'metadata' => [
                'test-scenario' => $testScenario,
            ],
        ];

        $this->sendPost(
            $this->getWireMockUrl().'/__admin/mappings',
            json_encode($mappingData)
        );
        $this->seeResponseCodeIsSuccessful();

        return $this->grabDataFromResponseByJsonPath('$.id')[0];
    }

    public function wireMockAddRegexRequestMapping(
        string $method,
        string $url,
        string $requestRegex,
        string $testScenario = 'hermes-test',
        ?array $headers = null,
        string $responseBody = '',
        int $responseCode = 200,
    ): string {
        $mappingData = [
            'request' => [
                'url' => $url,
                'method' => $method,
                'headers' => $headers,
                'bodyPatterns' => [
                    [
                        'matches' => $requestRegex,
                    ],
                ],
            ],
            'response' => [
                'status' => $responseCode,
                'body' => $responseBody,
            ],
            'metadata' => [
                'test-scenario' => $testScenario,
            ],
        ];

        $this->sendPost(
            $this->getWireMockUrl().'/__admin/mappings',
            json_encode($mappingData)
        );
        $this->seeResponseCodeIsSuccessful();

        return $this->grabDataFromResponseByJsonPath('$.id')[0];
    }

    /**
     * @throws \Throwable
     */
    public function wireMockVerifyRequestMapping(array $mappingIds, bool $removeAfterVerification = true): void
    {
        foreach ($mappingIds as $mappingId) {
            $this->waitUntil(function (ApiTester $I) use ($mappingId): void {
                $this->sendGet(
                    $this->getWireMockUrl().'/__admin/requests?matchingStub='.$mappingId,
                );
                $this->seeResponseCodeIsSuccessful();
                $matchedMappingIds = $this->grabDataFromResponseByJsonPath('$.requests[*].stubMapping.id');

                if (!in_array($mappingId, $matchedMappingIds)) {
                    $this->fail(
                        sprintf('wiremock matching failed: no match-request found for mapping %s',
                            $mappingId,
                        )
                    );
                }
            });
        }
        if ($removeAfterVerification) {
            $this->wireMockRemoveMappings($mappingIds);
        }
    }

    public function wireMockVerifyNegativeRequestMapping(array $mappingIds, bool $removeAfterVerification = true): void
    {
        foreach ($mappingIds as $mappingId) {
            $this->waitUntil(function (ApiTester $I) use ($mappingId): void {
                $this->sendGet(
                    $this->getWireMockUrl().'/__admin/requests?matchingStub='.$mappingId,
                );
                $this->seeResponseCodeIsSuccessful();
                $matchedMappingIds = $this->grabDataFromResponseByJsonPath('$.requests[*].stubMapping.id');
                if (is_array($matchedMappingIds) && count($matchedMappingIds) > 0) {
                    $this->fail(
                        sprintf('wiremock non-matching failed: match-request was found for mapping %s',
                            $mappingId,
                        )
                    );
                }
            });
        }
        if ($removeAfterVerification) {
            $this->wireMockRemoveMappings($mappingIds);
        }
    }

    /**
     * @param string[] $mappingIds
     */
    public function wireMockRemoveMappings(array $mappingIds): void
    {
        foreach ($mappingIds as $mappingId) {
            $this->sendDelete(
                $this->getWireMockUrl().'/__admin/mappings/'.$mappingId,
            );
            $this->seeResponseCodeIsSuccessful();
        }
    }

    public function wireMockFindOneRequestBody(
        string $method,
        string $url,
        array $requestBody,
    ): array {
        $requestFilter = [
            'url' => $url,
            'method' => $method,
            'bodyPatterns' => [
                [
                    'equalToJson' => json_encode($requestBody),
                    'ignoreArrayOrder' => true,
                    'ignoreExtraElements' => true,
                ],
            ],
        ];

        $this->sendPost(
            $this->getWireMockUrl().'/__admin/requests/find',
            json_encode($requestFilter)
        );
        $this->seeResponseCodeIsSuccessful();
        $requests = $this->grabDataFromResponseByJsonPath('$.requests')[0];
        $this->assertIsArray($requests);
        $this->assertCount(1, $requests);

        return json_decode($requests[0]['body'], true);
    }

    public function wireMockSetNetherlandsTrackRecordingMapping(
        string $tourExternalId,
        string $equipmentExternalId,
        int $numberTrackLocations = 1,
    ): string {
        $trackLocationExpectation = [
            'latitude' => '${json-unit.any-string}',
            'longitude' => '${json-unit.any-string}',
            'timestamp' => '${json-unit.any-string}',
        ];
        $trackLocations = array_fill(0, $numberTrackLocations, $trackLocationExpectation);

        return $this->wireMockAddJsonRequestMapping(
            method: 'POST',
            url: '/sap-netherlands/Tracks',
            requestBodyJson: [
                'tourExternalId' => $tourExternalId,
                'equipmentExternalId' => $equipmentExternalId,
                'tracks' => $trackLocations,
            ],
            headers: [
                'Idempotency-Key' => ['matches' => '[A-Za-z0-9_-]{10,50}'],
            ]
        );
    }

    public function wireMockClearTestScenarioMappings(string $testScenario): void
    {
        $mappingData = [
            'matchesJsonPath' => [
                'expression' => '$.test-scenario',
                'equalTo' => $testScenario,
            ],
        ];

        $this->sendPost(
            $this->getWireMockUrl().'/__admin/mappings/remove-by-metadata',
            json_encode($mappingData)
        );
        $this->seeResponseCodeIsSuccessful();
    }
}
