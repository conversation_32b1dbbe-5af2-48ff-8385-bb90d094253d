<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

class SampleGermanyTour
{
    public static function additionalInformationFixture(
        string $tourExternalId,
        string $staffExternalId,
        array $tourAdditionalInformation,
        array $orderAdditionalInformation,
        array $taskGroupAdditionalInformation,
    ): string {
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);
        $tourAdditionalInformation = json_encode($tourAdditionalInformation, JSON_THROW_ON_ERROR);
        $orderAdditionalInformation = json_encode($orderAdditionalInformation, JSON_THROW_ON_ERROR);
        $taskGroupAdditionalInformation = json_encode($taskGroupAdditionalInformation, JSON_THROW_ON_ERROR);
        static $orderExternalId = uniqid();
        static $equipmentExtId = uniqid();
        static $equipmentLicensePlate = uniqid();

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "{$staffExternalId}",
                    "personnelType": "DRIVER",
                    "firstName": "Andreas",
                    "lastName": "Peters"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [
                {
                    "customerName": "E neukauf",
                    "customerExtId": "2021112"
                }
            ],
            "equipments": [
                {
                    "equipmentExtId": "{$equipmentExtId}",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [
                {
                    "locationExtId": "2066747",
                    "customerExtId": "2021112",
                    "additionalInformation": {$orderAdditionalInformation},
                    "orderLocations": [
                        {
                            "orderLocationType": "CUSTOMER",
                            "locationExtId": "2066747",
                            "additionalInformation": {$taskGroupAdditionalInformation},
                            "tasks": []
                        }
                    ],
                    "orderType": "12",
                    "orderPosNr": 1,
                    "orderExtId": "{$orderExternalId}",
                    "contractNumber": "cn-76688339",
                    "transportNumber": "tn-33282714",
                    "materialExternalId": "mat-de-1234",
                    "purchaseOrder": "po-54535992",
                    "orderDocuments": []
                }
            ],
            "tourExtId": "{$tourExternalId}",
            "name": "ExampleTourAdditionalInformation",
            "branchExternalId": "equipment-test-branch-1",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234",
            "additionalInformation": {$tourAdditionalInformation}
        }
        JSON;
    }

    public static function tourWithoutEquipment(
        string $tourExternalId,
    ): string {
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [],
            "orders": [],
            "tourExtId": "{$tourExternalId}",
            "name": "ExampleTourWithoutEquipment",
            "branchExternalId": "equipment-test-branch-1",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function tourWithEquipmentType(
        string $tourExternalId,
        string $equipmentType,
        string $equipmentLicensePlate,
    ): string {
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);
        $equipmentExternalId = uniqid();

        return <<<JSON
        {
            "staff": [],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [
                {
                    "equipmentExtId": "{$equipmentExternalId}",
                    "equipmentType": "{$equipmentType}",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "{$equipmentLicensePlate}",
                    "containerMounting": ""
                }
            ],
            "orders": [],
            "tourExtId": "{$tourExternalId}",
            "name": "ExampleTourWithEquipmentType",
            "branchExternalId": "equipment-test-branch-1",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function tourWithoutStaff(): string
    {
        $tourExternalId = uniqid();
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [
                {
                    "equipmentExtId": "equipment-external-id-1",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "equipment-licence-plate-1",
                    "containerMounting": ""
                }
            ],
            "orders": [],
            "tourExtId": "{$tourExternalId}",
            "name": "ExampleTourWithoutStaff",
            "branchExternalId": "equipment-test-branch-1",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function tourWithoutStaffAndEquipment(): string
    {
        $tourExternalId = uniqid();
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [],
            "orders": [],
            "tourExtId": "{$tourExternalId}",
            "name": "ExampleTourWithoutStaffAndEquipment",
            "branchExternalId": "equipment-test-branch-1",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }

    public static function tourWithoutEquipmentExpired(
        string $staffId1,
    ): string {
        $tourExternalId = uniqid();
        $startDate = new \DateTime('-1 day')->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime('-1 day')->setTime(23, 0)->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "$staffId1",
                    "personnelType": "DRIVER",
                    "firstName": "Andreas",
                    "lastName": "Peters"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [],
            "orders": [],
            "tourExtId": "{$tourExternalId}",
            "name": "ExampleTourWithoutEquipment",
            "branchExternalId": "equipment-test-branch-1",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }
}
