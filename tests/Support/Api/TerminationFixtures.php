<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use Symfony\Component\Uid\Uuid;

class TerminationFixtures
{
    public static function terminateOrderRequestV2(array $terminationTemplate): string
    {
        $value = json_encode(self::getExampleInputValue($terminationTemplate['taskgroups'][0]['tasks'][0]['inputs'][0]));
        $fileUuid = Uuid::v4()->toRfc4122();

        return <<<JSON
        {
          "template_uuid": "{$terminationTemplate['template_uuid']}",
          "taskgroups": [{
          "template_uuid": "{$terminationTemplate['taskgroups'][0]['template_uuid']}",
          "tasks": [
            {
              "template_uuid": "{$terminationTemplate['taskgroups'][0]['tasks'][0]['template_uuid']}",
              "skipped": false,
              "inputs": [{
                "template_uuid": "{$terminationTemplate['taskgroups'][0]['tasks'][0]['inputs'][0]['template_uuid']}",
                "value": {$value}
              },
              {
                "template_uuid": "{$terminationTemplate['taskgroups'][0]['tasks'][0]['inputs'][1]['template_uuid']}",
                "value": [{
                  "file": "app-user-files/$fileUuid",
                  "name": "test1.jpg"
                }]
              }]
            }
          ]
          }]
        }
        JSON;
    }

    public static function terminateTourRequestV2(array $terminationTemplate): string
    {
        $value = json_encode(self::getExampleInputValue($terminationTemplate['taskgroups'][0]['tasks'][0]['inputs'][0]));

        return <<<JSON
        {
            "template_uuid": "{$terminationTemplate['template_uuid']}",
            "taskgroups": [{
              "template_uuid": "{$terminationTemplate['taskgroups'][0]['template_uuid']}",
              "tasks": [{
                "template_uuid": "{$terminationTemplate['taskgroups'][0]['tasks'][0]['template_uuid']}",
                "skipped": false,
                "inputs": [{
                  "template_uuid": "{$terminationTemplate['taskgroups'][0]['tasks'][0]['inputs'][0]['template_uuid']}",
                  "value": {$value}
                }]
              }]
            }]
        }
        JSON;
    }

    private static function getExampleInputValue(array $input): mixed
    {
        return match ($input['type']) {
            'accept', 'boolean' => true,
            'select' => $input['options'][0]['key'],
            'number' => 123,
            'string', 'text' => uniqid(),
            'signature', 'photo' => [
                [
                    'file' => 'some-file-name.jpg',
                    'name' => 'Some file name',
                ],
            ],
            'qr' => ['some', 'values'],
            default => throw new \RuntimeException('Unknown input type'),
        };
    }
}
