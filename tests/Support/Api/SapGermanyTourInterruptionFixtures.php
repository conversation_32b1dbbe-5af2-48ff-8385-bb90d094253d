<?php

declare(strict_types=1);

namespace App\Tests\Support\Api;

use App\Tests\Support\Api\ExpectedResults\GetInterruptionTemplateExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskGroupRuleExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskGroupsExpectedResult;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnHasTaskGroupsTemplateDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputTemplateDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskGroupTemplateDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskTemplateDescriber;

class SapGermanyTourInterruptionFixtures
{
    public static function getTourDisposalInterruption(): GetInterruptionTemplateExpectedResult
    {
        return new GetInterruptionTemplateExpectedResult(
            source: 'tour',
            name: '{{disposal_site}}',
            taskGroupExpectedResults: [
                new GetTaskGroupsExpectedResult(
                    position: 0,
                    taskGroupCount: 5,
                    taskExpectedResults: [
                        new GetTaskExpectedResult(
                            position: 0,
                            taskCount: 1,
                            expectedNames: ['{{disposal_site_choice}}'],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                        new GetTaskExpectedResult(
                            position: 1,
                            taskCount: 3,
                            expectedNames: ['dispose'],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                        new GetTaskExpectedResult(
                            position: 2,
                            taskCount: 2,
                            expectedNames: ['dispose'],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                        new GetTaskExpectedResult(
                            position: 3,
                            taskCount: 1,
                            expectedNames: ['dispose'],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                        new GetTaskExpectedResult(
                            position: 4,
                            taskCount: 3,
                            expectedNames: ['dispose'],
                            restrictedNames: [],
                            expectedAutocompleteOptions: [],
                            taskRuleExpectedResults: [],
                        ),
                    ],
                    taskGroupRuleExpectedResults: [
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 1),
                        new GetTaskGroupRuleExpectedResult(taskGroupIndex: 2),
                    ],
                ),
            ]
        );
    }

    public static function getReturnToBackendDisposalSiteInterruptionDescription(): ReturnHasTaskGroupsTemplateDescriber
    {
        return new ReturnHasTaskGroupsTemplateDescriber(
            position: 0,
            taskGroups: [
                new ReturnTaskGroupTemplateDescriber(
                    position: 0,
                    tasks: [
                        new ReturnTaskTemplateDescriber(
                            position: 0,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0, value: null, selectOption: 3,
                                ),
                            ]
                        ),
                    ]
                ),
                new ReturnTaskGroupTemplateDescriber(
                    position: 1,
                    tasks: [
                        new ReturnTaskTemplateDescriber(
                            position: 0,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0, value: true, selectOption: null,
                                ),
                                new ReturnInputTemplateDescriber(
                                    position: 1, value: true, selectOption: null,
                                ),
                            ],
                        ),
                        new ReturnTaskTemplateDescriber(
                            position: 2,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0, value: null, selectOption: 2,
                                ),
                            ],
                        ),
                    ],
                ),
                new ReturnTaskGroupTemplateDescriber(
                    position: 2,
                    tasks: [
                        new ReturnTaskTemplateDescriber(
                            position: 0,
                            completedAt: new \DateTimeImmutable(),
                            skipped: false,
                            inputs: [
                                new ReturnInputTemplateDescriber(
                                    position: 0, value: true, selectOption: null,
                                ),
                                new ReturnInputTemplateDescriber(
                                    position: 1, value: true, selectOption: null,
                                ),
                            ]
                        ),
                    ],
                ),
            ]
        );
    }
}
