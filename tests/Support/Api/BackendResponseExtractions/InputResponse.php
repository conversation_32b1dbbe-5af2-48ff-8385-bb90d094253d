<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class InputResponse
{
    /**
     * @var array<string>
     */
    public array $options = [];

    public function __construct(
        public string $uuid,
        public string $type,
    ) {
    }

    /**
     * @param array<int, array{key: string}> $options
     */
    public static function createFromResponse(string $uuid, string $type, array $options = []): self
    {
        $input = new self($uuid, $type);

        foreach ($options as $option) {
            $input->options[] = $option['key'];
        }

        return $input;
    }
}
