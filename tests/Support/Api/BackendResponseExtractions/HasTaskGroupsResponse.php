<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class HasTaskGroupsResponse
{
    /**
     * @var array<TaskGroupResponse>
     */
    public array $taskGroups = [];

    public function addTaskGroup(TaskGroupResponse $taskGroupResponse): self
    {
        $this->taskGroups[] = $taskGroupResponse;

        return $this;
    }

    public static function createFromResponse(array $reponseData): self
    {
        $hasTaskGroupsResponse = new self();
        foreach ($reponseData['taskgroups'] as $taskGroupData) {
            $hasTaskGroupsResponse->addTaskGroup(TaskGroupResponse::createFromResponse($taskGroupData['uuid'], $taskGroupData['tasks']));
        }

        return $hasTaskGroupsResponse;
    }
}
