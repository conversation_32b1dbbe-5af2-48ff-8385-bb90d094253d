<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class TaskResponse
{
    /** @var array<InputResponse> */
    public array $inputs = [];

    public function __construct(
        public string $uuid)
    {
    }

    public function addInput(InputResponse $inputResponse): self
    {
        $this->inputs[] = $inputResponse;

        return $this;
    }

    /**
     * @param array<array{
     *     uuid: string,
     *     type: string,
     *     options: array<int, array{
     *          key: string,
     *     }>
     * }> $inputs
     */
    public static function createFromResponse(string $uuid, array $inputs): self
    {
        $taskResponse = new self($uuid);

        foreach ($inputs as $inputData) {
            $taskResponse->addInput(InputResponse::createFromResponse(
                $inputData['uuid'],
                $inputData['type'],
                $inputData['options'] ?? []
            ));
        }

        return $taskResponse;
    }
}
