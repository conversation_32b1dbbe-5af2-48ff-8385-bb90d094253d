<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskGroupTemplateDescriber;

class TaskGroupTemplateResponse
{
    /** @var array<TaskTemplateResponse> */
    public array $taskTemplates = [];

    public function __construct(
        public string $templateUuid,
    ) {
    }

    public function addTaskTemplate(TaskTemplateResponse $taskTemplateResponse): self
    {
        $this->taskTemplates[] = $taskTemplateResponse;

        return $this;
    }

    /**
     * @param array<int, array{
     *     template_uuid: string,
     *     inputs: array{
     *      type: string,
     *      template_uuid: string,
     *      options: array{
     *          key: string,
     *          name: string,
     *      }
     *     }
     *  }> $tasks
     */
    public static function createFromReponse(string $templateUuid, array $tasks): self
    {
        $template = new self($templateUuid);

        foreach ($tasks as $taskData) {
            $task = TaskTemplateResponse::createFromReponse($taskData['template_uuid'], $taskData['inputs']);
            $template->addTaskTemplate($task);
        }

        return $template;
    }

    public function getReturn2Backend(ReturnTaskGroupTemplateDescriber $returnDescription): array
    {
        $faker = \Faker\Factory::create();

        $ret = [];

        $ret['template_uuid'] = $this->templateUuid;

        $ret['tasks'] = [];
        foreach ($returnDescription->tasks as $taskDescription) {
            $taskData = [];
            $taskTemplateData = $this->taskTemplates[$taskDescription->position];
            $taskData['template_uuid'] = $taskTemplateData->templateUuid;
            $taskData['completed_at'] = $taskDescription->completedAt->format(\DateTimeInterface::RFC3339_EXTENDED);
            $taskData['skipped'] = $taskDescription->skipped;
            $taskData['inputs'] = [];
            $taskData['latitude'] = $faker->latitude();
            $taskData['longitude'] = $faker->longitude();
            $taskData['mileage'] = $faker->randomFloat(2, 10000, 9999999);

            foreach ($taskDescription->inputs as $inputDescription) {
                $inputData = [];
                $inputTemplate = $taskTemplateData->inputTemplates[$inputDescription->position];
                $inputData['template_uuid'] = $inputTemplate->templateUuid;
                if ('select' === $inputTemplate->type && null !== $inputDescription->selectOption) {
                    $inputData['value'] = $inputTemplate->options[$inputDescription->selectOption];
                } else {
                    $inputData['value'] = $inputDescription->value;
                }
                $taskData['inputs'][] = $inputData;
            }
            $ret['tasks'][] = $taskData;
        }

        return $ret;
    }
}
