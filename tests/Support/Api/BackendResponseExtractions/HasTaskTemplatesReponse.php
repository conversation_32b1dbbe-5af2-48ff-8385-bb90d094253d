<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class HasTaskTemplatesReponse
{
    /**
     * @var array<TaskTemplateResponse>
     */
    public array $taskTemplates = [];

    public function addTaskGroupTemplate(TaskTemplateResponse $taskTemplateResponse): self
    {
        $this->taskTemplates[] = $taskTemplateResponse;

        return $this;
    }

    public static function createFromResponse(array $reponseData): self
    {
        $template = new self();
        foreach ($reponseData as $reponseDataSet) {
            $template->addTaskGroupTemplate(
                TaskTemplateResponse::createFromReponse($reponseDataSet['template_uuid'], $reponseDataSet['inputs'])
            );
        }

        return $template;
    }
}
