<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class HasTaskGroupTemplateResponse
{
    /**
     * @var array<TaskGroupTemplateResponse>
     */
    public array $taskGroupTemplates = [];

    public function addTaskGroupTemplate(TaskGroupTemplateResponse $taskGroupTemplateResponse): self
    {
        $this->taskGroupTemplates[] = $taskGroupTemplateResponse;

        return $this;
    }

    public static function createFromResponse(array $reponseData): self
    {
        $template = new self();
        foreach ($reponseData['taskgroups'] as $taskGroupData) {
            $template->addTaskGroupTemplate(TaskGroupTemplateResponse::createFromReponse($taskGroupData['template_uuid'], $taskGroupData['tasks']));
        }

        return $template;
    }
}
