<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnTaskGroupDescriber;
use Symfony\Component\Uid\Uuid;

class TaskGroupResponse
{
    /** @var array<TaskResponse> */
    public array $tasks = [];

    public function __construct(
        public string $uuid,
    ) {
    }

    public function addTask(TaskResponse $taskResponse): self
    {
        $this->tasks[] = $taskResponse;

        return $this;
    }

    /**
     * @param array<int, array{
     *     uuid: string,
     *     inputs: array{
     *      type: string,
     *      uuid: string,
     *      options: array{
     *          key: string,
     *          name: string,
     *      }
     *     }
     *  }> $tasks
     */
    public static function createFromResponse(string $uuid, array $tasks): self
    {
        $taskGroupResponse = new self($uuid);

        foreach ($tasks as $taskData) {
            $task = TaskResponse::createFromResponse($taskData['uuid'], $taskData['inputs']);
            $taskGroupResponse->addTask($task);
        }

        return $taskGroupResponse;
    }

    public function getReturn2Backend(
        ReturnTaskGroupDescriber $returnDescription,
        ?HasTaskTemplatesReponse $additionalServices,
    ): array {
        $ret = [];

        $ret['uuid'] = $this->uuid;

        $ret['tasks'] = [];
        foreach ($returnDescription->tasks as $returnTaskDescription) {
            $realTourTaskResponse = $this->tasks[$returnTaskDescription->position];

            if (true === $returnTaskDescription->additionalService) {
                if (null === $additionalServices) {
                    throw new \RuntimeException('additional services not set');
                }
                $taskData = $this->getAdditionalServiceTaskData(
                    $returnTaskDescription,
                    $additionalServices
                );
            } elseif (true === $returnTaskDescription->repeatService) {
                $taskData = $this->getRepeatTaskData($realTourTaskResponse, $returnTaskDescription);
            } else {
                $taskData = $this->getRegularTaskData($realTourTaskResponse, $returnTaskDescription);
            }
            $ret['tasks'][] = $taskData;
        }

        return $ret;
    }

    private function getRegularTaskData(
        TaskResponse $realTourTaskResponse,
        ReturnTaskDescriber $returnTaskDescription,
    ): array {
        $faker = \Faker\Factory::create();

        $taskData = [];
        $taskData['uuid'] = $realTourTaskResponse->uuid;
        $taskData['completed_at'] = $returnTaskDescription->completedAt->format(\DateTimeInterface::RFC3339_EXTENDED);
        $taskData['skipped'] = $returnTaskDescription->skipped;
        $taskData['additional_service'] = false;
        $taskData['additional_service_template_uuid'] = '';
        $taskData['repeat_service'] = false;
        $taskData['repeat_service_template_uuid'] = '';
        $taskData['inputs'] = [];
        $taskData['latitude'] = $faker->latitude();
        $taskData['longitude'] = $faker->longitude();
        $taskData['mileage'] = $faker->randomFloat(2, 10000, 9999999);

        foreach ($returnTaskDescription->inputs as $inputDescription) {
            $inputData = ['type' => 'input'];
            $inputBlueprint = $realTourTaskResponse->inputs[$inputDescription->position];
            $inputData['uuid'] = $inputBlueprint->uuid;
            if ('select' === $inputBlueprint->type && null !== $inputDescription->selectOption) {
                $inputData['value'] = [$inputBlueprint->options[$inputDescription->selectOption]];
            } else {
                $inputData['value'] = $inputDescription->value;
            }

            $taskData['inputs'][] = $inputData;
        }

        return $taskData;
    }

    private function getRepeatTaskData(
        TaskResponse $realTourTaskResponse,
        ReturnTaskDescriber $returnTaskDescription,
    ): array {
        $faker = \Faker\Factory::create();

        $taskData = [];
        $taskData['uuid'] = Uuid::v4()->toRfc4122();
        $taskData['completed_at'] = $returnTaskDescription->completedAt->format(\DateTimeInterface::RFC3339_EXTENDED);
        $taskData['skipped'] = $returnTaskDescription->skipped;
        $taskData['additional_service'] = false;
        $taskData['additional_service_template_uuid'] = '';
        $taskData['repeat_service'] = true;
        $taskData['repeat_service_template_uuid'] = $realTourTaskResponse->uuid;
        $taskData['inputs'] = [];
        $taskData['latitude'] = $faker->latitude();
        $taskData['longitude'] = $faker->longitude();
        $taskData['mileage'] = $faker->randomFloat(2, 10000, 9999999);

        foreach ($returnTaskDescription->inputs as $inputDescription) {
            $inputData = ['type' => 'input-template'];
            $inputBlueprint = $realTourTaskResponse->inputs[$inputDescription->position];
            $inputData['template_uuid'] = $inputBlueprint->uuid;
            $inputData['uuid'] = Uuid::v4()->toRfc4122();
            if ('select' === $inputBlueprint->type && null !== $inputDescription->selectOption) {
                $inputData['value'] = [$inputBlueprint->options[$inputDescription->selectOption]];
            } else {
                $inputData['value'] = $inputDescription->value;
            }

            $taskData['inputs'][] = $inputData;
        }

        return $taskData;
    }

    private function getAdditionalServiceTaskData(
        ReturnTaskDescriber $returnTaskDescription,
        HasTaskTemplatesReponse $additionalServices,
    ): array {
        $faker = \Faker\Factory::create();

        /** @var TaskTemplateResponse $additionalService */
        $additionalService = $additionalServices->taskTemplates[$returnTaskDescription->position];
        $taskData = [];
        $taskData['uuid'] = Uuid::v4()->toRfc4122();
        $taskData['completed_at'] = $returnTaskDescription->completedAt->format(\DateTimeInterface::RFC3339_EXTENDED);
        $taskData['skipped'] = $returnTaskDescription->skipped;
        $taskData['additional_service'] = true;
        $taskData['additional_service_template_uuid'] = $additionalService->templateUuid;
        $taskData['repeat_service'] = false;
        $taskData['repeat_service_template_uuid'] = '';
        $taskData['inputs'] = [];
        $taskData['latitude'] = $faker->latitude();
        $taskData['longitude'] = $faker->longitude();
        $taskData['mileage'] = $faker->randomFloat(2, 10000, 9999999);

        foreach ($returnTaskDescription->inputs as $inputDescription) {
            $inputData = ['type' => 'input-template'];
            $inputBlueprint = $additionalService->inputTemplates[$inputDescription->position];
            $inputData['template_uuid'] = $inputBlueprint->templateUuid;
            $inputData['uuid'] = Uuid::v4()->toRfc4122();
            if ('select' === $inputBlueprint->type && null !== $inputDescription->selectOption) {
                $inputData['value'] = [$inputBlueprint->options[$inputDescription->selectOption]];
            } else {
                $inputData['value'] = $inputDescription->value;
            }

            $taskData['inputs'][] = $inputData;
        }

        return $taskData;
    }
}
