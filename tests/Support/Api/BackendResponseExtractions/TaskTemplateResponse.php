<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class TaskTemplateResponse
{
    /** @var array<InputTemplateResponse> */
    public array $inputTemplates = [];

    public function __construct(
        public string $templateUuid)
    {
    }

    public function addInputTemplate(InputTemplateResponse $inputTemplateResponse): self
    {
        $this->inputTemplates[] = $inputTemplateResponse;

        return $this;
    }

    /**
     * @param array<array{
     *     template_uuid: string,
     *     type: string,
     *     options: array<int, array{
     *          key: string,
     *     }>
     * }> $inputs
     */
    public static function createFromReponse(string $templateUuid, array $inputs): self
    {
        $template = new self($templateUuid);

        foreach ($inputs as $inputData) {
            $template->addInputTemplate(InputTemplateResponse::createFromResponse(
                $inputData['template_uuid'],
                $inputData['type'],
                $inputData['options'] ?? []
            ));
        }

        return $template;
    }
}
