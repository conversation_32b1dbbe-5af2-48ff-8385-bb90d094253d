<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\BackendResponseExtractions;

class InputTemplateResponse
{
    /**
     * @var array<string>
     */
    public array $options = [];

    public function __construct(
        public string $templateUuid,
        public string $type,
    ) {
    }

    /**
     * @param array<int, array{key: string}> $options
     */
    public static function createFromResponse(string $templateUuid, string $type, array $options = []): self
    {
        $input = new self($templateUuid, $type);

        foreach ($options as $option) {
            $input->options[] = $option['key'];
        }

        return $input;
    }
}
