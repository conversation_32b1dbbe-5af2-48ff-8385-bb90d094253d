<?php

declare(strict_types=1);

namespace App\Tests\Support;

class DeviceCache
{
    private static ?string $deviceId = null;

    public static function get(): string
    {
        return self::$deviceId ?? throw new \RuntimeException('device id was not set!');
    }

    public static function set(string $deviceId): void
    {
        self::$deviceId = $deviceId;
    }

    public static function clear(): void
    {
        self::$deviceId = null;
    }
}
