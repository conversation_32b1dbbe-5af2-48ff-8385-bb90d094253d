<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Support;

use App\Tests\Support\_generated\ApiTesterActions;
use App\Tests\Support\Api\ExpectedResults\GetInputExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTaskGroupsExpectedResult;
use App\Tests\Support\Api\ExpectedResults\GetTourExpectedResult;
use App\Tests\Support\Api\WireMock\WireMockMappingTrait;
use App\Tests\Support\Api\WireMock\WireMockMethodsTrait;
use App\Tests\Support\Helper\ApiEndpoints\DakoEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\DataProtectionNoticeEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\DeviceAccessEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\DeviceMessageEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\EquipmentEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\FaqEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\FeedbackEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\FileEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\InterruptionEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\LanguageEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\LoggingEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\MastertourEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\Misc\VehicleInspectionReportTrait;
use App\Tests\Support\Helper\ApiEndpoints\MobileAppReleases\MobileAppReleasesEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\NotificationEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\OrderEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\PhonebookEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\PointOfInterestEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalBookingTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalBranchTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalConnectedDevicesTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalCountryTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalDakoTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalDashboardTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalDeviceMessageTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalDocumentTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalEquipmentTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalFaqTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalFileTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalMastertourTemplateTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalMobileAppReleaseTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalNotificationEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalOrderTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalPhonebookTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalPointOfInterestTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalPushTrackingTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalStaffTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalTourTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalTrackingDataTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalTranslationTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalUserTrait;
use App\Tests\Support\Helper\ApiEndpoints\Portal\PortalVehicleInspectionTrait;
use App\Tests\Support\Helper\ApiEndpoints\SapGermanyEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\SapNetherlandsEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\SapSpainEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\StaffEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\TachoplusTrait;
use App\Tests\Support\Helper\ApiEndpoints\TourEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\TrackingEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\UserEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\VehicleInspectionTrait;
use App\Tests\Support\Helper\CentrifugoTrait;
use App\Tests\Support\Helper\Generate\GenerateStaffTrait;
use Blackfire\Client;
use Blackfire\Profile\Configuration;

/**
 * Inherited Methods.
 *
 * @method void wantTo($text)
 * @method void wantToTest($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause($vars = [])
 *
 * @SuppressWarnings(PHPMD)
 */
class ApiTester extends \Codeception\Actor
{
    use ApiTesterActions;

    // Portal
    use PortalBookingTrait;
    use PortalBranchTrait;
    use PortalConnectedDevicesTrait;
    use PortalCountryTrait;
    use PortalDakoTrait;
    use PortalDashboardTrait;
    use PortalDeviceMessageTrait;
    use PortalDocumentTrait;
    use PortalEquipmentTrait;
    use PortalFaqTrait;
    use PortalFileTrait;
    use PortalTranslationTrait;
    use PortalMastertourTemplateTrait;
    use PortalMobileAppReleaseTrait;
    use PortalNotificationEndpointsTrait;
    use PortalOrderTrait;
    use PortalPhonebookTrait;
    use PortalPointOfInterestTrait;
    use PortalPushTrackingTrait;
    use PortalStaffTrait;
    use PortalTourTrait;
    use PortalTrackingDataTrait;
    use PortalUserTrait;
    use PortalVehicleInspectionTrait;

    // Mobile app releases
    use MobileAppReleasesEndpointsTrait;

    use DataProtectionNoticeEndpointsTrait;
    use DeviceAccessEndpointsTrait;
    use DeviceMessageEndpointsTrait;
    use DakoEndpointsTrait;
    use TachoplusTrait;
    use EquipmentEndpointsTrait;
    use FaqEndpointsTrait;
    use FeedbackEndpointsTrait;
    use FileEndpointsTrait;
    use GenerateStaffTrait;
    use InterruptionEndpointsTrait;
    use LanguageEndpointsTrait;
    use LoggingEndpointsTrait;
    use MastertourEndpointsTrait;
    use NotificationEndpointsTrait;
    use OrderEndpointsTrait;
    use PointOfInterestEndpointsTrait;
    use SapGermanyEndpointsTrait;
    use SapNetherlandsEndpointsTrait;
    use SapSpainEndpointsTrait;
    use StaffEndpointsTrait;
    use TourEndpointsTrait;
    use TrackingEndpointsTrait;
    use UserEndpointsTrait;
    use VehicleInspectionTrait;
    use PhonebookEndpointsTrait;

    // Misc
    use GenerateStaffTrait;
    use CentrifugoTrait;
    use VehicleInspectionReportTrait;

    // WireMock
    use WireMockMethodsTrait;
    use WireMockMappingTrait;

    public const string TEST_STAFF_EXT_ID_PREFIX = 'test-staff-ext-id-';

    public function amNotAuthenticated(): void
    {
        $this->amHttpAuthenticated('', '');
        $this->unsetHttpHeader('Authorization');
        $this->resetCookie(session_name());
        $this->resetCookie('JSESSIONID');
    }

    public function amUsingDeviceWithId(string $deviceId): void
    {
        DeviceCache::set($deviceId);
    }

    public function retrySapCalls(string $staffExtId, ?string $tenant = null): void
    {
        $this->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $tenant);

        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('device-uuid', 'abc123');
        $this->haveHttpHeader('app-time-reference', '2023-05-31T17:27:30.000+02:00');
        $url = '/internal/sap/retry'.(null !== $tenant ? '?tenant='.$tenant : '');
        $this->sendPost($url);
        $this->seeResponseCodeIsSuccessful();
        $this->assertTrue($this->grabDataFromResponseByJsonPath('$.success')[0]);

        $this->amNotAuthenticated();
    }

    public function waitUntil(
        callable $callable,
    ): void {
        codecept_debug('Waiting for condition to be met for 30 attempts...');
        $attempts = 30;
        $delayBetweenAttempts = 200_000; // microseconds = 200ms

        while (--$attempts > 0) {
            codecept_debug('Attempts left: '.$attempts);
            $isLastAttempt = 1 === $attempts;

            try {
                $callable($this);
            } catch (\Throwable $e) {
                if ($isLastAttempt) {
                    /** @noinspection PhpUnhandledExceptionInspection */
                    throw $e;
                }

                usleep($delayBetweenAttempts);

                continue;
            }

            break;
        }
    }

    protected function getWireMockUrl(): string
    {
        $parts = parse_url($_ENV['WIRE_MOCK_ENDPOINT']);

        $url = sprintf('%s://%s', $parts['scheme'], $parts['host']);

        if (!empty($parts['port'])) {
            $url .= ':'.$parts['port'];
        }

        return $url;
    }

    public function setBlackfireProfileHeader(?string $profileName = null): void
    {
        if (null === $profileName) {
            /** @noinspection DebugFunctionUsageInspection */
            $callingFunction = debug_backtrace(!DEBUG_BACKTRACE_PROVIDE_OBJECT | DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'];
            $profileName = 'codeception-test-'.$callingFunction.'-'.microtime(true);
        }

        $blackfireToken = $this->getBlackfireProfileQuery($profileName);
        $this->haveHttpHeader('X-Blackfire-Query', $blackfireToken);
    }

    public function getBlackfireProfileQuery(?string $profileName = null): string
    {
        if (null === $profileName) {
            /** @noinspection DebugFunctionUsageInspection */
            $callingFunction = debug_backtrace(!DEBUG_BACKTRACE_PROVIDE_OBJECT | DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'];
            $profileName = 'codeception-test-'.$callingFunction.'-'.microtime(true);
        }

        $profileConfiguration = new Configuration();
        $profileConfiguration->setDebug(true);
        $profileConfiguration->setTitle($profileName);
        $blackfireClient = new Client();
        $request = $blackfireClient->createRequest($profileConfiguration);

        return $request->getToken();
    }

    public function checkSapTourResult(GetTourExpectedResult $expectedResult): void
    {
        codecept_debug('checking backend tour result against expected object');
        $this->assertEquals($expectedResult->tourName, $this->grabDataFromResponseByJsonPath('$.name')[0]);
        $this->assertEquals($expectedResult->status, $this->grabDataFromResponseByJsonPath('$.status')[0]);

        codecept_debug('... additionalServices');
        $additionalServices = $this->grabDataFromResponseByJsonPath('$.additional_service_templates')[0];
        $this->assertIsArray($additionalServices);

        if (false === $expectedResult->hasAdditionalServices) {
            $this->assertEmpty($additionalServices);
        } else {
            foreach ($expectedResult->additionalServiceExpectedResults as $additionalServiceExpectedResult) {
                $checkService = array_filter($additionalServices, static fn (array $service) => isset($service['name']) && $additionalServiceExpectedResult->serviceName === $service['name']);
                $this->assertNotEmpty($checkService);
                $this->assertCount(1, $checkService);
                $inputs = $checkService[0]['inputs'];
                $this->assertCount($additionalServiceExpectedResult->inputCount, $inputs);

                foreach ($additionalServiceExpectedResult->inputExpectedResults as $inputExpectedResult) {
                    /* @var GetInputExpectedResult $inputExpectedResult */
                    $this->assertEquals($inputExpectedResult->label, $inputs[$inputExpectedResult->inputPosition]['label']);
                    if (null !== $inputExpectedResult->optionCount) {
                        $this->assertCount($inputExpectedResult->optionCount, $inputs[$inputExpectedResult->inputPosition]['options']);
                    }
                }
            }
        }

        codecept_debug('... equipments');
        $equipments = $this->grabDataFromResponseByJsonPath('$.equipments')[0];
        $this->assertIsArray($equipments);
        $this->assertCount($expectedResult->equipmentCount, $equipments);
        $equipmentNames = array_map(static fn ($equ) => $equ['name'], $equipments);

        foreach ($expectedResult->equipmentNames as $equipmentName) {
            $this->assertContains($equipmentName, $equipmentNames);
        }

        codecept_debug('...tour-terminations');
        $tourTerminations = $this->grabDataFromResponseByJsonPath('$.termination_templates')[0];
        $this->assertIsArray($tourTerminations);
        $this->assertCount($expectedResult->tourTerminationCount, $tourTerminations);
        foreach ($expectedResult->tourTerminationTaskGroupsExpectedResults as $taskGroupsExpectedResult) {
            $taskGroups = $tourTerminations[$taskGroupsExpectedResult->position]['taskgroups'];
            $this->checkTaskGroupData($taskGroupsExpectedResult, $taskGroups, true);
        }

        codecept_debug('...orders');
        $orders = $this->grabDataFromResponseByJsonPath('$.orders')[0];
        $this->assertIsArray($orders);
        $this->assertCount($expectedResult->orderCount, $orders);
        foreach ($expectedResult->orderTaskGroupsExpectedResults as $taskGroupsExpectedResult) {
            $taskGroups = $orders[$taskGroupsExpectedResult->position]['taskgroups'];
            $this->checkTaskGroupData($taskGroupsExpectedResult, $taskGroups);
        }

        codecept_debug('...order-notes');
        foreach ($expectedResult->orderNoteTaskGroupExpectedResults as $orderNoteTaskGroupExpectedResult) {
            $order = $orders[$orderNoteTaskGroupExpectedResult->orderPosition];
            $this->assertIsArray($order);
            $notes = $order['note_templates'];
            foreach ($orderNoteTaskGroupExpectedResult->taskGroupsExpectedResult as $taskGroupsExpectedResult) {
                $taskGroups = $notes[$taskGroupsExpectedResult->position]['taskgroups'];
                $this->checkTaskGroupData($taskGroupsExpectedResult, $taskGroups, true);
            }
        }
    }

    public function checkTaskGroupData(
        GetTaskGroupsExpectedResult $taskGroupsExpectedResult,
        array $taskGroups,
        bool $isTemplate = false,
    ): void {
        codecept_debug('... taskgroups inside');
        $this->assertIsArray($taskGroups);
        $this->assertCount($taskGroupsExpectedResult->taskGroupCount, $taskGroups);

        $autoCompleteValues = [];
        foreach ($taskGroupsExpectedResult->taskExpectedResults as $expectedTaskResult) {
            $taskGroup = $taskGroups[$expectedTaskResult->position];
            $tasks = $taskGroup['tasks'];
            $this->assertIsArray($tasks);
            $this->assertCount($expectedTaskResult->taskCount, $tasks);
            $names = array_map(static fn ($checkTask) => $checkTask['name'], $tasks);
            foreach ($expectedTaskResult->expectedNames as $expectedName) {
                $this->assertContains($expectedName, $names);
            }
            foreach ($expectedTaskResult->restrictedNames as $restrictedName) {
                $this->assertNotContains($restrictedName, $names);
            }
            foreach ($tasks as $task) {
                foreach ($task['inputs'] as $input) {
                    if (isset($input['auto_completion_values'])) {
                        $autoCompleteValues = array_merge($autoCompleteValues, $input['auto_completion_values']);
                    }
                }
            }
            foreach ($expectedTaskResult->expectedAutocompleteOptions as $expectedValue) {
                $this->assertContains($expectedValue, $autoCompleteValues);
            }
            codecept_debug('...taskrules inside single taskgroup');
            foreach ($expectedTaskResult->taskRuleExpectedResults as $taskRuleCheck) {
                $this->checkTaskRules(
                    tasksAll: $tasks,
                    ruleContainingTask: $tasks[$taskRuleCheck->taskIndex],
                    isTemplate: $isTemplate
                );
            }
        }

        codecept_debug('...taskgrouprules inside');
        foreach ($taskGroupsExpectedResult->taskGroupRuleExpectedResults as $expectedTaskGroupRule) {
            $this->checkTaskGroupRules(
                taskGroupsAll: $taskGroups,
                ruleContainingTaskGroup: $taskGroups[$expectedTaskGroupRule->taskGroupIndex],
                isTemplate: $isTemplate
            );
        }
    }

    public function amAuthenticatedAsGeneratedUserAndVerify(string $staffExtId, string $tenant): void
    {
        $this->waitUntil(function () use ($staffExtId, $tenant): void {
            $this->amAuthenticatedAsGeneratedUser($staffExtId, $tenant);
        });
    }

    private function checkTaskGroupRules(
        array $taskGroupsAll,
        array $ruleContainingTaskGroup,
        bool $isTemplate = false,
    ): void {
        $fieldname = $isTemplate ? 'template_uuid' : 'uuid';
        $this->assertNotEmpty($ruleContainingTaskGroup['rules']);
        $checkedRuleCount = 0;

        $tasksAll = array_merge(...array_column($taskGroupsAll, 'tasks'));
        $taskGroupInputs = array_merge(...array_column($tasksAll, 'inputs'));
        $this->assertNotEmpty($taskGroupInputs);

        foreach ($ruleContainingTaskGroup['rules'] as $taskGroupRule) {
            $keyTaskGroupMasterInputs = array_values(array_filter(
                $taskGroupInputs,
                static fn (array $input) => $taskGroupRule['input_uuid'] === $input[$fieldname]
            ));

            $this->assertGreaterOrEquals(1, count($keyTaskGroupMasterInputs));
            $keyInput = $keyTaskGroupMasterInputs[0];

            if ('select' === $keyInput['type']) {
                $this->assertNotEmpty($keyInput['options']);
                $fittingtaskGroupOption = array_filter($keyInput['options'], static fn (array $option) => $option['key'] === $taskGroupRule['value']);
                $this->assertCount(1, $fittingtaskGroupOption);
            }
            ++$checkedRuleCount;
        }

        $this->assertEquals(count($ruleContainingTaskGroup['rules']), $checkedRuleCount);
    }

    public function verifyInvalidSessionV2(): void
    {
        $this->waitUntil(function (): void {
            $this->applyApiV2Headers();

            $this->sendGet('/api/v2/user/me');
            $this->seeResponseCodeIs(401);
        });
    }

    public function hasSessionRunningV2(): bool
    {
        $this->applyApiV2Headers();

        $this->sendGet('/api/v2/user/me');
        $result = $this->grabDataFromResponseByJsonPath('$');

        if (count($result) > 0) {
            return true;
        }

        return false;
    }

    private function checkTaskRules(
        array $tasksAll,
        array $ruleContainingTask,
        bool $isTemplate = false,
    ): void {
        $fieldname = $isTemplate ? 'template_uuid' : 'uuid';
        $this->assertNotEmpty($ruleContainingTask['rules']);
        $checkedRuleCount = 0;

        $taskInputs = array_merge(...array_column($tasksAll, 'inputs'));
        $this->assertNotEmpty($taskInputs);

        foreach ($ruleContainingTask['rules'] as $taskRule) {
            $keyMasterInputs = array_values(array_filter($taskInputs, static fn ($input) => $taskRule['input_uuid'] === $input[$fieldname]));
            $this->assertGreaterOrEquals(1, count($keyMasterInputs));
            $keyInput = $keyMasterInputs[0];

            if ('select' === $keyInput['type']) {
                $this->assertNotEmpty($keyInput['options']);
                $fittingTaskOptions = array_filter($keyInput['options'], static fn (array $option) => $option['key'] === $taskRule['value']);
                $this->assertCount(1, $fittingTaskOptions);
            }
            ++$checkedRuleCount;
        }

        $this->assertEquals(count($ruleContainingTask['rules']), $checkedRuleCount);
    }
}
