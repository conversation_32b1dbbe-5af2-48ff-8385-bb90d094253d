<?php

declare(strict_types=1);

namespace App\Tests\Codeception\Extension;

use App\Tests\Support\DeviceCache;
use Codeception\Event\TestEvent;
use Codeception\Events;
use Codeception\Extension;

class DeviceCacheInitializer extends Extension
{
    private static ?string $fileContext = null;

    /**
     * @var array<string, string>
     */
    public static $events = [
        Events::TEST_BEFORE => 'beforeTest',
    ];

    public function beforeTest(TestEvent $e): void
    {
        $filename = $e->getTest()->getMetadata()->getFilename();
        if (self::$fileContext === $filename) {
            return;
        }

        self::$fileContext = $filename;
        DeviceCache::clear();
    }
}
