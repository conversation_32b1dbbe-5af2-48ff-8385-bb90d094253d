<?php

declare(strict_types=1);

/**
 * This file is needed for PHPStan to be able to infer the types of the query builder/DQL.
 *
 * @see https://github.com/phpstan/phpstan-doctrine#configuration
 */

use App\Kernel;
use Symfony\Component\Dotenv\Dotenv;

require __DIR__.'/../vendor/autoload.php';

(new Dotenv())->bootEnv(__DIR__.'/../.env');

$kernel = new Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);
$kernel->boot();

return $kernel->getContainer()->get('doctrine')->getManager();
