<?php

declare(strict_types=1);

namespace App\Tests\Architecture;

use App\Domain\Services\Domain;
use PHPat\Selector\Selector;
use PHPat\Test\Builder\Rule;
use PHPat\Test\PHPat;

class DomainClassShouldNotBeUsedOutsideDomainNamespace
{
    public function test_domain_class_should_not_be_used_outside_domain_namespace(): Rule
    {
        return PHPat::rule()
            ->classes(
                Selector::AND(
                    Selector::all(),
                    Selector::NOT(Selector::inNamespace('App\Domain')),
                    Selector::NOT(Selector::inNamespace('App\Tests'))
                )
            )
            ->shouldNotDependOn()
            ->classes(Selector::classname(Domain::class))
            ->because('Domain class should not be used outside Domain namespace');
    }
}
