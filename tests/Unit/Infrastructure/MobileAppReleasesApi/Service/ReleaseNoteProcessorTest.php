<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\MobileAppReleasesApi\Service;

use App\Infrastructure\MobileAppReleasesApi\Service\ReleaseNoteProcessor;
use Codeception\Test\Unit;

class ReleaseNoteProcessorTest extends Unit
{
    public function testCleanupReleaseNotes(): void
    {
        $releaseNoteProcessor = new ReleaseNoteProcessor();
        $releaseNotes = self::getSampleReleaseNotes();

        $cleanedReleaseNotes = $releaseNoteProcessor->cleanReleaseNotes($releaseNotes);

        $this->assertSame(self::getCleanedSampleReleaseNotes(), $cleanedReleaseNotes);
    }

    public static function getSampleReleaseNotes(): string
    {
        return <<<RELEASE_NOTES_BLA
        ## [4.10.0](https://github.com/prezero/hermes-flutter/compare/v4.9.2...v4.10.0) (2025-05-15)


        ### Features

        * **HERMES-3289:** add log level setting to admin view and restrict access via /device-access endpoint ([#1486](https://github.com/prezero/hermes-flutter/issues/1486)) ([1cad107](https://github.com/prezero/hermes-flutter/commit/1cad1075ace0ed59263a47360e62d5e13611dc2a))


        ### Bug Fixes

        * **HERMES-3328:** improved error handling if unauthorized error on file download happens ([#1494](https://github.com/prezero/hermes-flutter/issues/1494)) ([f0df690](https://github.com/prezero/hermes-flutter/commit/f0df690f07a1797ed68434a425f4677b34cc2aac))
        * **HERMES-3428:** fixed navigation problems so that it runs smooth again ([#1497](https://github.com/prezero/hermes-flutter/issues/1497)) ([a434748](https://github.com/prezero/hermes-flutter/commit/a434748354ab4d8f66f93b36028893e1ed9134f1))
        * take current amount of selected items into account when selecting max items for weighing data ([#1501](https://github.com/prezero/hermes-flutter/issues/1501)) ([5e768cb](https://github.com/prezero/hermes-flutter/commit/5e768cb6c0048e27f5018b46c981e5d24b859aaa))
        * **user.dart:** format code for improved readability #master ([5e6975e](https://github.com/prezero/hermes-flutter/commit/5e6975e54b0033270e1b0b7efd11f6f611f444cc))


        ### Miscellaneous Chores

        * logging improved ([#1500](https://github.com/prezero/hermes-flutter/issues/1500)) ([ac9edfd](https://github.com/prezero/hermes-flutter/commit/ac9edfd56679f06d0ea6beabf7b2bbcd2b57a2bd))


        ### Code Refactoring

        * **HERMES-3250:** caching of vehicle profile ([#1493](https://github.com/prezero/hermes-flutter/issues/1493)) ([8a39f79](https://github.com/prezero/hermes-flutter/commit/8a39f795c704dd17bfe0275d3fff13471442641d))
        * **HERMES-3314:** use 'escaped + CRC' protocol to communicate with squarell box ([#1496](https://github.com/prezero/hermes-flutter/issues/1496)) ([3ee9d06](https://github.com/prezero/hermes-flutter/commit/3ee9d06fdd46f7af5bc4bfe9985bcc8340335bd1))
        * log dcf name ([#1499](https://github.com/prezero/hermes-flutter/issues/1499)) ([d4db9f0](https://github.com/prezero/hermes-flutter/commit/d4db9f05eb18f27889792248faf8c62b1436b2dc))


        ### Continuous Integration

        * change test concurrency and remove available cli packages ([#1489](https://github.com/prezero/hermes-flutter/issues/1489)) ([ad547ff](https://github.com/prezero/hermes-flutter/commit/ad547ff24d36ef55019b56a1b85f19a346559024))
        RELEASE_NOTES_BLA;
    }

    public static function getCleanedSampleReleaseNotes(): string
    {
        return <<<RELEASE_NOTES_BLA
        ## 4.10.0 (2025-05-15)


        ### Features

        * add log level setting to admin view and restrict access via /device-access endpoint


        ### Bug Fixes

        * improved error handling if unauthorized error on file download happens
        * fixed navigation problems so that it runs smooth again
        * take current amount of selected items into account when selecting max items for weighing data
        * **user.dart:** format code for improved readability #master


        ### Miscellaneous Chores

        * logging improved


        ### Code Refactoring

        * caching of vehicle profile
        * use 'escaped + CRC' protocol to communicate with squarell box
        * log dcf name


        ### Continuous Integration

        * change test concurrency and remove available cli packages
        RELEASE_NOTES_BLA;
    }
}
