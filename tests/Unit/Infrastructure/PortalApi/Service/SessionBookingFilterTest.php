<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\PortalApi\Service;

use App\Domain\Entity\Enum\BookingSource;
use App\Domain\Entity\Enum\Types\BookingType;
use App\Domain\Entity\SessionBooking;
use App\Domain\Entity\ValueObject\Booking;
use App\Infrastructure\PortalApi\Service\SessionBookingFilter;
use Codeception\Attribute\DataProvider;
use Codeception\Test\Unit;

class SessionBookingFilterTest extends Unit
{
    /**
     * @param SessionBooking[] $sessionBookings
     * @param SessionBooking[] $expectedResult
     */
    #[DataProvider('sessionBookingFilterDataProvider')]
    public function testBookingFilteringByTour(array $sessionBookings, string $tourId, array $expectedResult): void
    {
        $sessionBookingFilter = new SessionBookingFilter();
        $sessionBooking = $sessionBookingFilter->filterSessionBookingByTour($sessionBookings, $tourId);

        $this->assertEquals($expectedResult, $sessionBooking);
    }

    /**
     * @return array<string, array{sessionBookings: SessionBooking[], tourId: string, expectedResult: SessionBooking[]}>
     */
    private function sessionBookingFilterDataProvider(): array
    {
        $oneNotFinishedTourBookings = [
            self::interruptionSessionBooking('interruption-1', BookingType::START, new \DateTimeImmutable('-60 minutes')),
            self::interruptionSessionBooking('interruption-1', BookingType::END, new \DateTimeImmutable('-59 minutes')),
            self::tourSessionBooking('tour-1', BookingType::START, new \DateTimeImmutable('-58 minutes')),
            self::interruptionSessionBooking('interruption-2', BookingType::START, new \DateTimeImmutable('-57 minutes')),
            self::interruptionSessionBooking('interruption-2', BookingType::END, new \DateTimeImmutable('-56 minutes')),
        ];
        $oneFinishedTourBookings = [
            ...$oneNotFinishedTourBookings,
            self::tourSessionBooking('tour-1', BookingType::END, new \DateTimeImmutable('-55 minutes')),
            self::interruptionSessionBooking('interruption-3', BookingType::START, new \DateTimeImmutable('-54 minutes')),
            self::interruptionSessionBooking('interruption-3', BookingType::END, new \DateTimeImmutable('-53 minutes')),
        ];
        $twoToursSecondNotFinishedBookings = [
            ...$oneFinishedTourBookings,
            self::tourSessionBooking('tour-2', BookingType::START, new \DateTimeImmutable('-52 minutes')),
            self::interruptionSessionBooking('interruption-4', BookingType::START, new \DateTimeImmutable('-51 minutes')),
            self::interruptionSessionBooking('interruption-4', BookingType::END, new \DateTimeImmutable('-50 minutes')),
        ];
        $twoToursAllFinishedBookings = [
            ...$twoToursSecondNotFinishedBookings,
            self::tourSessionBooking('tour-2', BookingType::END, new \DateTimeImmutable('-49 minutes')),
            self::interruptionSessionBooking('interruption-5', BookingType::START, new \DateTimeImmutable('-48 minutes')),
            self::interruptionSessionBooking('interruption-5', BookingType::END, new \DateTimeImmutable('-47 minutes')),
        ];
        $threeToursLastUnfinishedBookings = [
            ...$twoToursAllFinishedBookings,
            self::tourSessionBooking('tour-3', BookingType::START, new \DateTimeImmutable('-46 minutes')),
            self::interruptionSessionBooking('interruption-6', BookingType::START, new \DateTimeImmutable('-45 minutes')),
            self::interruptionSessionBooking('interruption-6', BookingType::END, new \DateTimeImmutable('-44 minutes')),
        ];
        $threeToursAllFinishedBookings = [
            ...$threeToursLastUnfinishedBookings,
            self::tourSessionBooking('tour-3', BookingType::END, new \DateTimeImmutable('-43 minutes')),
            self::interruptionSessionBooking('interruption-7', BookingType::START, new \DateTimeImmutable('-42 minutes')),
            self::interruptionSessionBooking('interruption-7', BookingType::END, new \DateTimeImmutable('-41 minutes')),
        ];

        return [
            'no bookings' => [
                'sessionBookings' => [],
                'tourId' => 'tour-1',
                'expectedResult' => [],
            ],
            'no tours' => [
                'sessionBookings' => [
                    self::interruptionSessionBooking('interruption-1', BookingType::START, new \DateTimeImmutable()),
                    self::interruptionSessionBooking('interruption-1', BookingType::END, new \DateTimeImmutable()),
                ],
                'tourId' => 'tour-1',
                'expectedResult' => [],
            ],
            'one tour not finished' => [
                'sessionBookings' => $oneNotFinishedTourBookings,
                'tourId' => 'tour-1',
                'expectedResult' => $oneNotFinishedTourBookings,
            ],
            'one tour finished' => [
                'sessionBookings' => $oneFinishedTourBookings,
                'tourId' => 'tour-1',
                'expectedResult' => $oneFinishedTourBookings,
            ],
            'two tours, second not finished, check first' => [
                'sessionBookings' => $twoToursSecondNotFinishedBookings,
                'tourId' => 'tour-1',
                'expectedResult' => array_slice($twoToursSecondNotFinishedBookings, 0, 8),
            ],
            'two tours, second not finished, check second' => [
                'sessionBookings' => $twoToursSecondNotFinishedBookings,
                'tourId' => 'tour-2',
                'expectedResult' => array_slice($twoToursSecondNotFinishedBookings, 8),
            ],
            'two finished tours, check first' => [
                'sessionBookings' => $twoToursAllFinishedBookings,
                'tourId' => 'tour-1',
                'expectedResult' => array_slice($twoToursAllFinishedBookings, 0, 8),
            ],
            'two finished tours, check second' => [
                'sessionBookings' => $twoToursAllFinishedBookings,
                'tourId' => 'tour-2',
                'expectedResult' => array_slice($twoToursAllFinishedBookings, 8),
            ],
            'three tours, last unfinished, check first' => [
                'sessionBookings' => $threeToursLastUnfinishedBookings,
                'tourId' => 'tour-1',
                'expectedResult' => array_slice($threeToursLastUnfinishedBookings, 0, 8),
            ],
            'three tours, last unfinished, check second' => [
                'sessionBookings' => $threeToursLastUnfinishedBookings,
                'tourId' => 'tour-2',
                'expectedResult' => array_slice($threeToursLastUnfinishedBookings, 8, 6),
            ],
            'three tours, last unfinished, check third' => [
                'sessionBookings' => $threeToursLastUnfinishedBookings,
                'tourId' => 'tour-3',
                'expectedResult' => array_slice($threeToursLastUnfinishedBookings, 14),
            ],
            'three finished tours, check first' => [
                'sessionBookings' => $threeToursAllFinishedBookings,
                'tourId' => 'tour-1',
                'expectedResult' => array_slice($threeToursAllFinishedBookings, 0, 8),
            ],
            'three finished tours, check second' => [
                'sessionBookings' => $threeToursAllFinishedBookings,
                'tourId' => 'tour-2',
                'expectedResult' => array_slice($threeToursAllFinishedBookings, 8, 6),
            ],
            'three finished tours, check third' => [
                'sessionBookings' => $threeToursAllFinishedBookings,
                'tourId' => 'tour-3',
                'expectedResult' => array_slice($threeToursAllFinishedBookings, 14),
            ],
        ];
    }

    public static function tourSessionBooking(
        string $label,
        BookingType $type,
        \DateTimeImmutable $timestamp,
    ): SessionBooking {
        return new SessionBooking(
            sessionId: 'session-1',
            source: BookingSource::TOUR,
            sourceId: $label,
            booking: new Booking(
                sessionId: 'session-1',
                label: $label,
                timestamp: $timestamp,
                status: 'status',
                type: $type,
            ),
            description: $label,
        );
    }

    private static function interruptionSessionBooking(
        string $label,
        BookingType $type,
        \DateTimeImmutable $timestamp,
    ): SessionBooking {
        return new SessionBooking(
            sessionId: 'session-1',
            source: BookingSource::INTERRUPTION,
            sourceId: $label,
            booking: new Booking(
                sessionId: 'session-1',
                label: $label,
                timestamp: $timestamp,
                status: 'status',
                type: $type,
            ),
            description: $label,
        );
    }
}
