<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\PortalApi\Service;

use App\Domain\Entity\ValueObject\TrackLocation;
use App\Infrastructure\HermesAppApi\Service\TrackingReducer;
use Codeception\Test\Unit;

/**
 * @covers \App\Infrastructure\HermesAppApi\Service\TrackingReducer
 */
class TrackingReducerTest extends Unit
{
    private TrackingReducer $reducer;

    protected function _before(): void
    {
        $this->reducer = new TrackingReducer();
    }

    public function testReduceEmptyArray(): void
    {
        $result = $this->reducer->reduce(trackLocations: []);
        self::assertEmpty($result);
    }

    public function testReduceLessThanFiveLocations(): void
    {
        $locations = [
            $this->createTrackLocation(timestamp: 100, latitude: 50.0, longitude: 10.0, bearing: 0),
            $this->createTrackLocation(timestamp: 200, latitude: 50.1, longitude: 10.1, bearing: 10),
            $this->createTrackLocation(timestamp: 300, latitude: 50.2, longitude: 10.2, bearing: 20),
            $this->createTrackLocation(timestamp: 400, latitude: 50.3, longitude: 10.3, bearing: 30),
        ];

        $result = $this->reducer->reduce(trackLocations: $locations);
        self::assertCount(expectedCount: 4, haystack: $result);
        self::assertSame(expected: $locations, actual: $result);
    }

    public function testReduceSortsLocations(): void
    {
        $location1 = $this->createTrackLocation(timestamp: 300, latitude: 50.2, longitude: 10.2, bearing: 20);
        $location2 = $this->createTrackLocation(timestamp: 100, latitude: 50.0, longitude: 10.0, bearing: 0);
        $location3 = $this->createTrackLocation(timestamp: 400, latitude: 50.3, longitude: 10.3, bearing: 30);
        $location4 = $this->createTrackLocation(timestamp: 200, latitude: 50.1, longitude: 10.1, bearing: 10);
        $location5 = $this->createTrackLocation(timestamp: 500, latitude: 50.4, longitude: 10.4, bearing: 40);

        $locations = [$location1, $location2, $location3, $location4, $location5];

        $result = $this->reducer->reduce(trackLocations: $locations);

        self::assertNotEmpty($result);
        self::assertSame(expected: $location2, actual: $result[0]);
    }

    public function testReduceByDistance(): void
    {
        // minDistance = 1000m
        $locations = [
            // Approx 0.009 degrees latitude change is ~1000m.
            $l1 = $this->createTrackLocation(timestamp: 100, latitude: 52.50000, longitude: 13.30000, bearing: 0), // Start -> KEEP
            $l2 = $this->createTrackLocation(timestamp: 200, latitude: 52.50010, longitude: 13.30000, bearing: 5), // ~11m from l1 -> REDUCE
            $l3 = $this->createTrackLocation(timestamp: 300, latitude: 52.50020, longitude: 13.30000, bearing: 10), // ~11m from l1 (as l2 reduced) -> REDUCE
            $l4 = $this->createTrackLocation(timestamp: 400, latitude: 52.51000, longitude: 13.30000, bearing: 15), // ~1110m from l1 -> KEEP
            $l5 = $this->createTrackLocation(timestamp: 500, latitude: 52.51010, longitude: 13.30000, bearing: 20), // ~11m from l4 -> REDUCE
            $l6 = $this->createTrackLocation(timestamp: 600, latitude: 52.52000, longitude: 13.30000, bearing: 25), // ~1110m from l4 -> KEEP
            $l7 = $this->createTrackLocation(timestamp: 700, latitude: 52.52010, longitude: 13.30000, bearing: 30), // ~11m from l6 -> REDUCE
        ];

        $result = $this->reducer->reduce(trackLocations: $locations);

        // Expected: Keep l1, l4, l6
        self::assertCount(expectedCount: 3, haystack: $result);
        self::assertSame(expected: $l1, actual: $result[0]);
        self::assertSame(expected: $l4, actual: $result[1]);
        self::assertSame(expected: $l6, actual: $result[2]);
    }

    public function testReduceByBearing(): void
    {
        // minBearing = 40 degrees, assume distance is always < 1000m
        $locations = [
            $l1 = $this->createTrackLocation(timestamp: 100, latitude: 52.5163, longitude: 13.3777, bearing: 0),   // Start -> KEEP
            $l2 = $this->createTrackLocation(timestamp: 200, latitude: 52.5164, longitude: 13.3778, bearing: 10),  // Dist < 1000m. Bearing diff |10-0|=10 (<40) -> REDUCE
            $l3 = $this->createTrackLocation(timestamp: 300, latitude: 52.5165, longitude: 13.3779, bearing: 40),  // Dist < 1000m. Bearing diff |40-0|=40 (>=40) -> KEEP
            $l4 = $this->createTrackLocation(timestamp: 400, latitude: 52.5166, longitude: 13.3780, bearing: 85),  // Dist < 1000m. Bearing diff |85-40|=45 (>=40) -> KEEP
            $l5 = $this->createTrackLocation(timestamp: 500, latitude: 52.5167, longitude: 13.3781, bearing: 95),  // Dist < 1000m. Bearing diff |95-85|=10 (<40) -> REDUCE
            $l6 = $this->createTrackLocation(timestamp: 600, latitude: 52.5168, longitude: 13.3782, bearing: 140), // Dist < 1000m. Bearing diff |140-85|=55 (>=40) -> KEEP
            $l7 = $this->createTrackLocation(timestamp: 700, latitude: 52.5169, longitude: 13.3783, bearing: 150), // Dist < 1000m. Bearing diff |150-140|=10 (<40) -> REDUCE
        ];

        $result = $this->reducer->reduce(trackLocations: $locations);

        // Trace:
        // l1: prev=l1(0) -> KEEP
        // l2: dist(l1,l2)<1000. bear(l1,l2)=|10-0|=10 < 40. prev=l1(0) -> REDUCE
        // l3: dist(l1,l3)<1000. bear(l1,l3)=|40-0|=40 >= 40. prev=l3(40) -> KEEP
        // l4: dist(l3,l4)<1000. bear(l3,l4)=|85-40|=45 >= 40. prev=l4(85) -> KEEP
        // l5: dist(l4,l5)<1000. bear(l4,l5)=|95-85|=10 < 40. prev=l4(85) -> REDUCE
        // l6: dist(l4,l6)<1000. bear(l4,l6)=|140-85|=55 >= 40. prev=l6(140) -> KEEP
        // l7: dist(l6,l7)<1000. bear(l6,l7)=|150-140|=10 < 40. prev=l6(140) -> REDUCE
        // Expected: Keep l1, l3, l4, l6
        self::assertCount(expectedCount: 4, haystack: $result);
        self::assertSame(expected: $l1, actual: $result[0]);
        self::assertSame(expected: $l3, actual: $result[1]);
        self::assertSame(expected: $l4, actual: $result[2]);
        self::assertSame(expected: $l6, actual: $result[3]);
    }

    public function testReduceNoReductionNeededDueToLargeChanges(): void
    {
        // minDistance = 1000m, minBearing = 40 degrees
        // All points here are < 1000m apart, but bearing diffs are >= 40 degrees
        $locations = [
            $l1 = $this->createTrackLocation(timestamp: 100, latitude: 52.5163, longitude: 13.3777, bearing: 0),   // Start -> KEEP
            $l2 = $this->createTrackLocation(timestamp: 200, latitude: 52.5168, longitude: 13.3782, bearing: 50),  // Dist ~60m (<1000). Bearing diff |50-0|=50 (>=40) -> KEEP
            $l3 = $this->createTrackLocation(timestamp: 300, latitude: 52.5173, longitude: 13.3787, bearing: 100), // Dist ~60m (<1000). Bearing diff |100-50|=50 (>=40) -> KEEP
            $l4 = $this->createTrackLocation(timestamp: 400, latitude: 52.5178, longitude: 13.3792, bearing: 150), // Dist ~60m (<1000). Bearing diff |150-100|=50 (>=40) -> KEEP
            $l5 = $this->createTrackLocation(timestamp: 500, latitude: 52.5183, longitude: 13.3797, bearing: 200), // Dist ~60m (<1000). Bearing diff |200-150|=50 (>=40) -> KEEP
        ];

        $result = $this->reducer->reduce(trackLocations: $locations);

        // Expected: Keep all 5 because bearing changes meet threshold
        self::assertCount(expectedCount: 5, haystack: $result);
        self::assertSame(expected: $locations, actual: $result);
    }

    public function testReduceByDistanceAndBearing(): void
    {
        // minDistance = 1000m, minBearing = 40 degrees
        $locations = [
            $l1 = $this->createTrackLocation(timestamp: 100, latitude: 52.50000, longitude: 13.30000, bearing: 0),   // Start -> KEEP
            $l2 = $this->createTrackLocation(timestamp: 200, latitude: 52.50010, longitude: 13.30000, bearing: 10),  // Dist ~11m (<1000). Bearing diff |10-0|=10 (<40) -> REDUCE
            $l3 = $this->createTrackLocation(timestamp: 300, latitude: 52.51000, longitude: 13.30000, bearing: 20),  // Dist ~1110m from l1 (>=1000) -> KEEP (due to distance)
            $l4 = $this->createTrackLocation(timestamp: 400, latitude: 52.51010, longitude: 13.30000, bearing: 30),  // Dist ~11m from l3 (<1000). Bearing diff |30-20|=10 (<40) -> REDUCE
            $l5 = $this->createTrackLocation(timestamp: 500, latitude: 52.51020, longitude: 13.30000, bearing: 70),  // Dist ~22m from l3 (<1000). Bearing diff |70-20|=50 (>=40) -> KEEP (due to bearing)
            $l6 = $this->createTrackLocation(timestamp: 600, latitude: 52.51030, longitude: 13.30000, bearing: 80),  // Dist ~11m from l5 (<1000). Bearing diff |80-70|=10 (<40) -> REDUCE
            $l7 = $this->createTrackLocation(timestamp: 700, latitude: 52.60000, longitude: 13.30000, bearing: 90),  // Dist ~9970m from l5 (>=1000) -> KEEP (due to distance)
        ];

        $result = $this->reducer->reduce(trackLocations: $locations);

        // Expected: Keep l1, l3, l5, l7
        self::assertCount(expectedCount: 4, haystack: $result);
        self::assertSame(expected: $l1, actual: $result[0]);
        self::assertSame(expected: $l3, actual: $result[1]);
        self::assertSame(expected: $l5, actual: $result[2]);
        self::assertSame(expected: $l7, actual: $result[3]);
    }

    private function createTrackLocation(
        int $timestamp,
        float $latitude,
        float $longitude,
        int $bearing,
        bool $protected = false,
    ): TrackLocation {
        return new TrackLocation(
            latitude: $latitude,
            longitude: $longitude,
            timestamp: new \DateTimeImmutable('@'.$timestamp),
            protected: $protected,
            bearing: (float) $bearing,
        );
    }
}
