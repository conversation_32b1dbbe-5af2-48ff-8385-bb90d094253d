POST {{url}}/ingest/sap-netherlands/tour
Content-Type: application/json
Authorization: Basic aW5nZXN0LXNhcC1ubDppbmdlc3Qtc2FwLW5s

{
  "staff": [
    {
      "staffExtId": "nl111",
      "personnelType": "DRIVER",
      "firstName": "dutch",
      "lastName": "user1"
    },
    {
      "staffExtId": "nl222",
      "personnelType": "DRIVER",
      "firstName": "dutch",
      "lastName": "user2"
    }
  ],
  "addresses": [
    {
      "name": "Werk Porta Westfalica",
      "country": "NL",
      "postalCode": "32457",
      "city": "Porta Westfalica",
      "district": "Lerbeck",
      "street": "An der Pforte",
      "houseNumber": "2",
      "phoneNumber": "",
      "state": "",
      "addressExtId": "2066747"
    },
    {
      "name": "Innenstadt",
      "country": "NL",
      "postalCode": "32423",
      "city": "Minden",
      "district": "Innenstadt",
      "street": "Aminghauser Str.",
      "houseNumber": "7",
      "phoneNumber": "",
      "state": "",
      "addressExtId": "2589219"
    },
    {
      "name": "Example-name customer",
      "country": "NL",
      "postalCode": "32457",
      "city": "Porta Westfalica",
      "district": "Eisbergen",
      "street": "Albert-Schweitzer-Str.",
      "houseNumber": "8",
      "phoneNumber": "+4957518135",
      "state": "",
      "addressExtId": "83622"
    }
  ],
  "customers": [
    {
      "customerName": "E neukauf",
      "customerExtId": "2021112"
    }
  ],
  "equipments": [
    {
      "equipmentExtId": "NL-EQ1",
      "equipmentType": "ASK",
      "height": 3550,
      "length": 6815,
      "width": 2550,
      "weight": 12130,
      "minimumLoad": 0,
      "overload": 0,
      "totalPermissibleWeight": 0,
      "maxAxleLoad": 0,
      "licencePlate": "NL-AK 1",
      "containerMounting": ""
    }
  ],
  "orders": [
    {
      "locationExtId": "83622",
      "customerExtId": "2021112",
      "additionalInformation": [
        {
          "sequence": 10,
          "text": "1.04 B19 Kartonage-150101-ABF",
          "alsoFrontView": true,
          "bold": false,
          "icon": ""
        },
        {
          "sequence": 20,
          "text": "STANDARD ask-10-pr-BEHA",
          "alsoFrontView": true,
          "bold": false,
          "icon": ""
        }
      ],
      "orderLocations": [
        {
          "orderLocationType": "CUSTOMER",
          "locationExtId": "83622",
          "additionalInformation": [
            {
              "sequence": 10,
              "text": "1.04 B19 Kartonage-150101-ABF",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            },
            {
              "sequence": 20,
              "text": "STANDARD ask-10-pr-BEHA",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            }
          ],
          "tasks": [
            {
              "taskType": "container_count",
              "taskName": "Container amount",
              "elements": [],
              "taskExtId": ""
            },
            {
              "taskType": "customer_weight",
              "taskName": "Container weight",
              "elements": [],
              "taskExtId": ""
            },
            {
              "taskType": "deliveryNote_sig",
              "taskName": "customer sign",
              "elements": [],
              "taskExtId": ""
            }
          ]
        },
        {
          "orderLocationType": "DEPOT",
          "locationExtId": "2066747",
          "additionalInformation": [
            {
              "sequence": 10,
              "text": "1.04 B19 Kartonage-150101-ABF",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            },
            {
              "sequence": 20,
              "text": "STANDARD ask-10-pr-BEHA",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            }
          ],
          "tasks": []
        },
        {
          "orderLocationType": "DISPOSALSITE",
          "locationExtId": "2589219",
          "additionalInformation": [
            {
              "sequence": 10,
              "text": "1.04 B19 Kartonage-150101-ABF",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            },
            {
              "sequence": 20,
              "text": "STANDARD ask-10-pr-BEHA",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            }
          ],
          "tasks": [
            {
              "taskType": "weighingnote",
              "taskName": null,
              "elements": [],
              "taskExtId": ""
            }
          ]
        }
      ],
      "orderType": "NL_50",
      "orderPosNr": 1,
      "orderExtId": "NL00000000000083781455"
    }
  ],
  "tourExtId": "7611351",
  "name": "Dutch ExampleTour",
  "branchExternalId": "55555",
  "startTimestamp": "2023-07-31T06:00:00+02:00",
  "endTimestamp": "2023-07-31T16:00:00+02:00",
  "startAddressExtId": "2066747",
  "endAddressExtId": "2066747",
  "forcedOrder": false,
  "country": "NL",
  "disposalSites": [
    {
      "addressExtId": "2066747",
      "weigh": false,
      "disposalSiteExtId": "18144",
      "name": "Porta W.-PWE (UMS)"
    },
    {
      "addressExtId": "2589219",
      "weigh": false,
      "disposalSiteExtId": "24792",
      "name": "Minden-PWE NL Minden Holler"
    }
  ],
  "interruptionExternalId": "1234"
}
