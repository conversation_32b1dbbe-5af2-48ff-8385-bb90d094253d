POST {{url}}/ingest/sap-germany/tour
Content-Type: application/json
Authorization: Basic aW5nZXN0LXNhcC1kZTppbmdlc3Qtc2FwLWRl

{
  "staff": [
    {
      "staffExtId": "681",
      "personnelType": "DRIVER",
      "firstName": "<PERSON>",
      "lastName": "<PERSON>"
    },
    {
      "staffExtId": "EXTSTAFF3",
      "personnelType": "DRIVER",
      "firstName": "<PERSON>",
      "lastName": "<PERSON>"
    }
  ],
  "addresses": [
    {
      "name": "Werk Porta Westfalica",
      "country": "DE",
      "postalCode": "32457",
      "city": "Porta Westfalica",
      "district": "Lerbeck",
      "street": "An der Pforte",
      "houseNumber": "2",
      "phoneNumber": "",
      "state": "",
      "addressExtId": "2066747"
    },
    {
      "name": "Innenstadt",
      "country": "DE",
      "postalCode": "32423",
      "city": "Minden",
      "district": "Innenstadt",
      "street": "Aminghauser Str.",
      "houseNumber": "7",
      "phoneNumber": "",
      "state": "",
      "addressExtId": "2589219"
    },
    {
      "name": "Example-name customer",
      "country": "DE",
      "postalCode": "32457",
      "city": "Porta Westfalica",
      "district": "Eisbergen",
      "street": "Albert-Schweitzer-Str.",
      "houseNumber": "8",
      "phoneNumber": "+4957518135",
      "state": "",
      "addressExtId": "83622"
    }
  ],
  "customers": [
    {
      "customerName": "E neukauf",
      "customerExtId": "2021112"
    }
  ],
  "equipments": [
    {
      "equipmentExtId": "10004635",
      "equipmentType": "ASK",
      "height": 3550,
      "length": 6815,
      "width": 2550,
      "weight": 12130,
      "minimumLoad": 0,
      "overload": 0,
      "totalPermissibleWeight": 0,
      "maxAxleLoad": 0,
      "licencePlate": "MI-AK 1092",
      "containerMounting": ""
    }
  ],
  "orders": [
    {
      "locationExtId": "83622",
      "customerExtId": "2021112",
      "additionalInformation": [
        {
          "sequence": 10,
          "text": "1.04 B19 Kartonage-150101-ABF",
          "alsoFrontView": true,
          "bold": false,
          "icon": ""
        },
        {
          "sequence": 20,
          "text": "STANDARD ask-10-pr-BEHA",
          "alsoFrontView": true,
          "bold": false,
          "icon": ""
        }
      ],
      "orderLocations": [
        {
          "orderLocationType": "CUSTOMER",
          "locationExtId": "83622",
          "additionalInformation": [
            {
              "sequence": 10,
              "text": "1.04 B19 Kartonage-150101-ABF",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            },
            {
              "sequence": 20,
              "text": "STANDARD ask-10-pr-BEHA",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            }
          ],
          "tasks": [
            {
              "taskType": "container_count",
              "taskName": "Container amount",
              "elements": [],
              "taskExtId": ""
            },
            {
              "taskType": "customer_weight",
              "taskName": "Container weight",
              "elements": [],
              "taskExtId": ""
            },
            {
              "taskType": "deliveryNote_sig",
              "taskName": "customer sign",
              "elements": [],
              "taskExtId": ""
            }
          ]
        },
        {
          "orderLocationType": "DEPOT",
          "locationExtId": "2066747",
          "additionalInformation": [
            {
              "sequence": 10,
              "text": "1.04 B19 Kartonage-150101-ABF",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            },
            {
              "sequence": 20,
              "text": "STANDARD ask-10-pr-BEHA",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            }
          ],
          "tasks": []
        },
        {
          "orderLocationType": "DISPOSALSITE",
          "locationExtId": "2589219",
          "additionalInformation": [
            {
              "sequence": 10,
              "text": "1.04 B19 Kartonage-150101-ABF",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            },
            {
              "sequence": 20,
              "text": "STANDARD ask-10-pr-BEHA",
              "alsoFrontView": true,
              "bold": false,
              "icon": ""
            }
          ],
          "tasks": [
            {
              "taskType": "weighingnote",
              "taskName": null,
              "elements": [],
              "taskExtId": ""
            }
          ]
        }
      ],
      "orderType": "12",
      "orderPosNr": 1,
      "orderExtId": "WE00000000000083781455"
    }
  ],
  "tourExtId": "7611351",
  "name": "ExampleTour http",
  "branchExternalId": "0381",
  "startTimestamp": "2023-10-02T06:00:00+02:00",
  "endTimestamp": "2023-10-02T16:00:00+02:00",
  "startAddressExtId": "2066747",
  "endAddressExtId": "2066747",
  "forcedOrder": false,
  "country": "DE",
  "interruptionExternalId": "dsi1",
  "disposalSites": [
    {
      "addressExtId": "2066747",
      "weigh": false,
      "disposalSiteExtId": "18144",
      "name": "Porta W.-PWE (UMS)"
    },
    {
      "addressExtId": "2589219",
      "weigh": false,
      "disposalSiteExtId": "24792",
      "name": "Minden-PWE NL Minden Holler"
    }
  ],
  "interruptionExternalId": "1234"
}
