<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\Api\TerminationFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class OrderTerminationCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';

    private string $orderId = '';

    public function canTerminateOrder(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $this->staffExtId,
        ));

        $deviceId = 'random-device-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($sapTourId);

        $orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
        $terminationTemplate = $I->grabDataFromResponseByJsonPath('$.orders[0].termination_templates[0]')[0];

        $I->callApiOrderBookingV2(
            $orderId,
            BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(
            function (ApiTester $I) use ($sapTourId): void {
                $I->callApiTourGetV2($sapTourId);
                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals('started', $firstOrderStatus);
            }
        );

        $I->callApiOrderTerminationV2(
            $orderId,
            TerminationFixtures::terminateOrderRequestV2($terminationTemplate),
        );

        $I->waitUntil(
            function (ApiTester $I) use ($sapTourId): void {
                $I->callApiTourGetV2($sapTourId);
                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals('terminated', $firstOrderStatus);
            }
        );

        $I->callApiOrderTerminationHistoryV2($orderId);

        $terminations = $I->grabDataFromResponseByJsonPath('$.items');
        $I->assertCount(1, $terminations[0]);

        $this->orderId = $orderId;
    }

    #[Depends('canTerminateOrder')]
    public function canFindTerminatedOrderFilesInPortal(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiPortalFilesInMultipleOrders([$this->orderId]);
            $itemFileNames = $I->grabDataFromResponseByJsonPath('$.items.*.file_name');
            $I->assertIsArray($itemFileNames);

            $I->assertContains('first tg in first order-termination', $itemFileNames);
            $I->assertContains('deliverynote', $itemFileNames);
            $I->assertNotContains('file', $itemFileNames);
        });
    }
}
