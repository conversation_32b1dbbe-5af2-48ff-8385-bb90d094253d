<?php

declare(strict_types=1);

namespace App\Tests\Api\IotSpain;

use App\Tests\Support\Api\SapSpainTourFixtures;
use App\Tests\Support\ApiTester;

class IotSpainContainerActionCest
{
    public function canSendToIotPortalV2(ApiTester $I): void
    {
        ['wireMockMappingIds' => $wireMockMappingIds, 'order' => $order] = $this->createTourAndOrder($I);
        $this->completeOrderTaskGroups($I, $order);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function createTourAndOrder(ApiTester $I): array
    {
        $tourExtId = 'random-es-tour-id-'.uniqid();
        $orderExtId = 'random-es-order-id-'.uniqid();
        $tour1Name = 'random-es-tour-name-'.uniqid();
        $deviceId = 'random-es-device-'.uniqid();
        $equipmentExtId = 'random-es-equipment-'.uniqid();
        $staffExtId = 'random-es-staff-'.uniqid();
        $equipmentLicensePlate = 'random-es-license-plate-'.uniqid();
        $sapTourId = $I->callSapSpainTour(SapSpainTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            staffExtId: $staffExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
        ));

        $wireMockMappingId = $I->wireMocksetIotStatusCallMappings(
            url: '/api/v1/sensors',
            activeUser: 'pze'.$staffExtId,
            contractNumber: null,
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, 'pze');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);
        });

        $order = $I->grabDataFromResponseByJsonPath('$.orders[0]')[0];

        return [
            'wireMockMappingIds' => [$wireMockMappingId],
            'order' => $order,
        ];
    }

    private function completeOrderTaskGroups(ApiTester $I, $order): void
    {
        foreach ($order['taskgroups'] as $taskgroup) {
            foreach ($taskgroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    if ('qr' === $input['type'] && 'es-iot' === $input['label']) {
                        $taskgroupId = $taskgroup['uuid'];
                        $taskId = $task['uuid'];
                        $inputId = $input['uuid'];

                        break 3;
                    }
                }
            }
        }

        $data = <<<JSON
        {
            "uuid": "{$taskgroupId}",
            "tasks": [
                {
                    "uuid": "{$taskId}",
                    "skipped": false,
                    "additional_service": false,
                    "repeat_service": false,
                    "repeat_service_template_uuid": "",
                    "inputs": [
                        {
                            "type": "input",
                            "uuid": "{$inputId}",
                            "value": ["50"]
                        }
                    ]
                }
            ]
        }
        JSON;

        $I->callApiOrderTaskGroupPatchV2($order['uuid'], $data);
    }
}
