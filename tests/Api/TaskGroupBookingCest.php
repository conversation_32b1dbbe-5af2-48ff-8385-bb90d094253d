<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class TaskGroupBookingCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';

    public function canBookTaskGroup(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();

        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($sapTourId);

        $firstOrderTaskGroup = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[0]')[0];

        $I->callApiTaskGroupBookingV2(
            $firstOrderTaskGroup['uuid'],
            BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);
            $firstOrderTaskGroupStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[0].status')[0];

            $I->assertEquals('started', $firstOrderTaskGroupStatus);
        });
    }
}
