<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class EquipmentUpdateTaskGroupCest
{
    private string $tenant = 'pz';
    private string $equipmentId;
    private string $staffExtId = '';
    private string $deviceId = '';

    public function canUpdateEquipmentTaskGroupV2(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->deviceId = 'random-device-id-'.uniqid();
        $equipmentLicensePlate = 'random-plate-'.uniqid();
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2(availability: 'all');
        $equipments = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($equipments as $equipment) {
            if ($equipment['name'] === $equipmentLicensePlate) {
                $this->equipmentId = $equipment['uuid'];
            }
        }
        $I->assertNotEmpty($this->equipmentId);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentId,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $processingStatus = $I->grabDataFromResponseByJsonPath('$.booking')[0];
        $I->assertEquals('processed', $processingStatus);

        $I->callApiEquipmentGetItemV2(equipmentId: $this->equipmentId);

        $taskGroupId = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].uuid')[0];
        $task1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].uuid')[0];
        $task1Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].uuid')[0];
        $task1Input1Options = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].options')[0];
        $task2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].uuid')[0];
        $task2Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].inputs[0].uuid')[0];
        $task2Input2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].inputs[1].uuid')[0];

        $taskGroupRequest = [
            'uuid' => $taskGroupId,
            'tasks' => [
                [
                    'uuid' => $task1Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task1Input1Id,
                            'value' => array_column($task1Input1Options, 'key'),
                        ],
                    ],
                ],
                [
                    'uuid' => $task2Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task2Input1Id,
                            'value' => 'some value',
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $task2Input2Id,
                            'value' => 12345,
                        ],
                    ],
                ],
            ],
        ];

        $I->callApiEquipmentUpdateTaskGroupV2(
            equipmentId: $this->equipmentId,
            data: json_encode($taskGroupRequest),
        );

        $I->waitUntil(function (ApiTester $I) use ($task1Input1Options): void {
            $I->callApiEquipmentGetItemV2(equipmentId: $this->equipmentId);

            $responseTaskGroup1Task1Input1Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[0].tasks[0].inputs[0].options.*.name'
            );
            $responseTaskGroup1Task2Input1Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[0].tasks[1].inputs[0].value'
            )[0];
            $responseTaskGroup1Task2Input2Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[0].tasks[1].inputs[1].value'
            )[0];

            $I->assertEquals(array_column($task1Input1Options, 'name'), $responseTaskGroup1Task1Input1Value);
            $I->assertEquals('some value', $responseTaskGroup1Task2Input1Value);
            $I->assertEquals('12345', $responseTaskGroup1Task2Input2Value);
        });
    }
}
