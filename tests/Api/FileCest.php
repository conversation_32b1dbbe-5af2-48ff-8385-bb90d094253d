<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Domain\Services\ObjectStorage\ObjectPrefix;
use App\Tests\Support\ApiTester;
use Symfony\Component\Uid\Uuid;

class FileCest
{
    private string $tenant = 'pz';

    private string $staffExtId = '';

    public function cannotAccessFileWithInvalidUuid(ApiTester $I): void
    {
        $uuid = Uuid::v1()->toRfc4122();
        $deviceId = 'random-device-'.uniqid();

        $this->staffExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiFileRetrieveV2(
            ObjectPrefix::APP_USER_FILES->value.$uuid,
            expectedStatusCode: 404
        );
    }

    public function cannotAccessFileIfNotAuthenticated(ApiTester $I): void
    {
        $uuid = Uuid::v1()->toRfc4122();
        $fileContents = file_get_contents(codecept_data_dir('sample-image.jpeg'));
        $deviceId = 'random-device-'.uniqid();

        $this->staffExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiFileUploadInDirectoryV2('app-user-files', $uuid, $fileContents);

        $I->amNotAuthenticated();
        $I->callApiFileRetrieveV2(ObjectPrefix::APP_USER_FILES->value.$uuid, expectedStatusCode: 401);
    }
}
