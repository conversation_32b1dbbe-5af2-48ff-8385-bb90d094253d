<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class InputValuesCest
{
    private string $deviceId = '';
    private string $tenant = 'pz';
    private $staffExternalId = '';

    public function elementValuesAreStoredAndRetrievedCorrectly(ApiTester $I): void
    {
        $this->deviceId = 'random-device-'.uniqid();
        $this->staffExternalId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        // Get user profile and extract IDs of each element type (and their task/taskgroup)
        $I->callApiUserProfileV2();

        $taskGroupId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].uuid')[0];

        $task1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[0].uuid')[0];
        $selectInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[0].inputs[0].uuid')[0];
        $selectInputOptions = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[0].inputs[0].options')[0];

        $task2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].uuid')[0];
        $numberInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[0].uuid')[0];
        $stringInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[1].uuid')[0];
        $textInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[2].uuid')[0];
        $acceptInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[3].uuid')[0];
        $dateInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[4].uuid')[0];
        $photoInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[5].uuid')[0];
        $qrInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[6].uuid')[0];
        $timeInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[7].uuid')[0];
        $booleanInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[8].uuid')[0];
        $signatureInputId = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks[1].inputs[9].uuid')[0];

        // Change the values of each element type
        $taskGroupRequest = [
            'uuid' => $taskGroupId,
            'tasks' => [
                [
                    'uuid' => $task1Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $selectInputId,
                            'value' => array_column($selectInputOptions, 'key'),
                        ],
                    ],
                ],
                [
                    'uuid' => $task2Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $numberInputId,
                            'value' => 12345,
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $stringInputId,
                            'value' => 'some value',
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $textInputId,
                            'value' => 'some text',
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $acceptInputId,
                            'value' => true,
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $dateInputId,
                            'value' => '2023-04-20T00:00:00.000+02:00',
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $photoInputId,
                            'value' => [
                                [
                                    'file' => 'app-user-files/693ee4f7-1c34-4db6-8dc2-78c0338bb460',
                                    'name' => 'photo.jpg',
                                    'label' => 'staff-taskgroup2',
                                ],
                            ],
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $qrInputId,
                            'value' => [
                                'value 1',
                                'value 2',
                            ],
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $timeInputId,
                            'value' => '2023-04-20T15:46:00.000+02:00',
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $booleanInputId,
                            'value' => false,
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $signatureInputId,
                            'value' => [
                                [
                                    'file' => 'app-user-files/693ee4f7-1c34-4db6-8dc2-78c0338bb460',
                                    'name' => 'tim tester',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $I->callApiUserUpdateTaskgroupV2(
            data: json_encode($taskGroupRequest),
        );

        // Get user profile and check that the values have been stored correctly
        $I->waitUntil(function (ApiTester $I) use ($selectInputOptions): void {
            $I->callApiUserProfileV2();

            $responseTaskGroup1Task1Input1Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[0].inputs[0].value'
            )[0];
            $responseTaskGroup1Task2Input1Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[0].value'
            )[0];
            $responseTaskGroup1Task2Input2Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[1].value'
            )[0];
            $responseTaskGroup1Task2Input3Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[2].value'
            )[0];
            $responseTaskGroup1Task2Input4Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[3].value'
            )[0];
            $responseTaskGroup1Task2Input5Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[4].value'
            )[0];
            $responseTaskGroup1Task2Input6Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[5].value'
            )[0];
            $responseTaskGroup1Task2Input7Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[6].value'
            )[0];
            $responseTaskGroup1Task2Input8Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[7].value'
            )[0];
            $responseTaskGroup1Task2Input9Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[8].value'
            )[0];
            $responseTaskGroup1Task2Input10Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[1].tasks[1].inputs[9].value'
            )[0];

            foreach (array_column($selectInputOptions, 'key') as $selectInputOptionKey) {
                $I->assertContains($selectInputOptionKey, $responseTaskGroup1Task1Input1Value);
            }
            $I->assertCount(count($selectInputOptions), $responseTaskGroup1Task1Input1Value);

            $I->assertEquals(12345, $responseTaskGroup1Task2Input1Value);
            $I->assertSame('some value', $responseTaskGroup1Task2Input2Value);
            $I->assertSame('some text', $responseTaskGroup1Task2Input3Value);
            $I->assertSame(true, $responseTaskGroup1Task2Input4Value);
            $I->assertSame('2023-04-20T00:00:00.000+02:00', $responseTaskGroup1Task2Input5Value);
            $I->assertContains(
                [
                    'file' => 'pz/app-user-files/693ee4f7-1c34-4db6-8dc2-78c0338bb460',
                    'name' => 'photo.jpg',
                    'label' => 'staff-taskgroup2',
                ],
                $responseTaskGroup1Task2Input6Value
            );

            $I->assertCount(2, $responseTaskGroup1Task2Input7Value);
            $I->assertContains('value 1', $responseTaskGroup1Task2Input7Value);
            $I->assertContains('value 2', $responseTaskGroup1Task2Input7Value);

            $I->assertSame('2023-04-20T15:46:00.000+02:00', $responseTaskGroup1Task2Input8Value);
            $I->assertSame(false, $responseTaskGroup1Task2Input9Value);
            $I->assertContains(
                [
                    'file' => 'pz/app-user-files/693ee4f7-1c34-4db6-8dc2-78c0338bb460',
                    'name' => 'tim tester',
                    'label' => 'tim tester',
                ],
                $responseTaskGroup1Task2Input10Value
            );
        });
    }

    #[Depends('elementValuesAreStoredAndRetrievedCorrectly')]
    public function taskRuleValuesHaveCorrectTypes(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, $this->tenant);
        $I->callApiUserProfileV2();

        $tasks = $I->grabDataFromResponseByJsonPath('$.taskgroups[1].tasks')[0];

        $elementTypesByUuid = [];

        foreach ($tasks as $task) {
            foreach ($task['inputs'] as $input) {
                $elementTypesByUuid[$input['uuid']] = $input['type'];
            }
        }

        foreach ($tasks as $task) {
            foreach ($task['rules'] as $rule) {
                $I->assertArrayHasKey($rule['input_uuid'], $elementTypesByUuid);

                if ('empty' === $rule['operator'] || 'n_empty' === $rule['operator']) {
                    continue;
                }

                match ($elementTypesByUuid[$rule['input_uuid']]) {
                    'number' => $I->assertTrue(is_int($rule['value']) || is_float($rule['value'])),
                    'boolean', 'accept' => $I->assertIsBool($rule['value']),
                    default => $I->assertIsString($rule['value']),
                };
            }
        }
    }

    #[Depends('elementValuesAreStoredAndRetrievedCorrectly')]
    public function taskGroupRuleValuesHaveCorrectTypes(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, $this->tenant);
        $I->callApiUserProfileV2();

        $tasksGroups = $I->grabDataFromResponseByJsonPath('$.taskgroups')[0];

        $elementTypesByUuid = [];

        foreach ($tasksGroups as $tasksGroup) {
            foreach ($tasksGroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    $elementTypesByUuid[$input['uuid']] = $input['type'];
                }
            }
        }

        foreach ($tasksGroups as $tasksGroup) {
            foreach ($tasksGroup['rules'] as $rule) {
                $I->assertArrayHasKey($rule['input_uuid'], $elementTypesByUuid);

                if ('empty' === $rule['operator'] || 'n_empty' === $rule['operator']) {
                    continue;
                }

                match ($elementTypesByUuid[$rule['input_uuid']]) {
                    'number' => $I->assertTrue(is_int($rule['value']) || is_float($rule['value'])),
                    'boolean', 'accept' => $I->assertIsBool($rule['value']),
                    default => $I->assertIsString($rule['value']),
                };
            }
        }
    }
}
