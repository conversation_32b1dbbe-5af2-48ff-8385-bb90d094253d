<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class UserChangePasswordCest
{
    private string $tenant = 'pz';

    public function canChangePassword(ApiTester $I): void
    {
        // Generate user
        $deviceId = 'random-device-id-'.uniqid();
        $staffExternalId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExternalId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        // Check password reset is required
        $I->callApiUserProfileV2();
        $passwordResetRequired = $I->grabDataFromResponseByJsonPath('$.password_reset_required')[0];
        $I->assertTrue($passwordResetRequired);

        // Change password
        $I->callApiUserChangePasswordV2(json_encode(['new_password' => 'new-password']));

        // Authenticate with new password
        $I->amAuthenticatedAsDriverUser(uniqid(), $this->tenant.$staffExternalId, 'new-password');

        // Check password reset is not required anymore
        $I->callApiUserProfileV2();
        $passwordResetRequired = $I->grabDataFromResponseByJsonPath('$.password_reset_required')[0];
        $I->assertFalse($passwordResetRequired);

        // Verify you cannot change password again
        $I->callApiUserChangePasswordV2(json_encode(['new_password' => 'new-password-2']), expectedStatusCode: 400);

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');

        $I->callApiPortalStaffList(query: $staffExternalId);
        $data = $I->grabDataFromResponseByJsonPath('$.items.*')[0];
        $I->assertIsArray($data);
        $I->assertArrayHasKey('staff_id', $data);
        $I->callApiPortalStaffResetPassword($data['staff_id']);

        // Authenticate with default password
        $I->amAuthenticatedAsDriverUser(uniqid(), $this->tenant.$staffExternalId, $staffExternalId);

        // Check password reset is not required anymore
        $I->callApiUserProfileV2();
        $passwordResetRequired = $I->grabDataFromResponseByJsonPath('$.password_reset_required')[0];
        $I->assertTrue($passwordResetRequired);
    }
}
