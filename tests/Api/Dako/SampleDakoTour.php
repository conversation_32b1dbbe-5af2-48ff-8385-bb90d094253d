<?php

declare(strict_types=1);

namespace App\Tests\Api\Dako;

class SampleDakoTour
{
    public static function jsonPayload(
        string $branchExternalId,
        string $staffExternalId,
        string $equipmentExternalId,
        string $country = 'DE',
        ?string $tourExternalId = null,
    ): string {
        $tourExternalId = $tourExternalId ?? uniqid();
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "$staffExternalId",
                    "personnelType": "DRIVER",
                    "firstName": "Donny",
                    "lastName": "Dako"
                }
            ],
            "addresses": [
                {
                    "country": "$country",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [
                {
                    "equipmentExtId": "$equipmentExternalId",
                    "equipmentType": "ASK",
                    "height": 3550,
                    "length": 6815,
                    "width": 2550,
                    "weight": 12130,
                    "minimumLoad": 0,
                    "overload": 0,
                    "totalPermissibleWeight": 0,
                    "maxAxleLoad": 0,
                    "licencePlate": "$equipmentExternalId",
                    "containerMounting": ""
                }
            ],
            "orders": [],
            "tourExtId": "{$tourExternalId}",
            "name": "Dako-Tour",
            "branchExternalId": "$branchExternalId",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }
}
