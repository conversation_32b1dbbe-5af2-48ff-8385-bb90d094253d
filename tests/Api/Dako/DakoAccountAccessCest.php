<?php

declare(strict_types=1);

namespace App\Tests\Api\Dako;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class DakoAccountAccessCest
{
    private string $branchExternalIdLu = '';
    private string $branchExternalIdDe = '';
    private string $branchExternalIdToggle = '';
    private string $branchIdLu = '';
    private string $branchIdDe = '';
    private string $branchIdToggle = '';

    private string $dakoIdLu = '';
    private string $dakoIdDe = '';

    private string $dakoAccountIdLu = '';
    private string $dakoAccountIdDe = '';

    private string $toggleTourExternalId = '';

    public function canNotAccessAsUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDakoAccountList(expectedStatusCode: 403);
    }

    public function canAccessAsPortalAdmin(ApiTester $I): void
    {
        $this->branchExternalIdLu = 'random-branch-ext-lu-'.uniqid();
        $this->dakoIdLu = 'random-account-lu-'.uniqid();

        $I->callSapGermanyTourUpsert(data: SampleDakoTour::jsonPayload(
            branchExternalId: $this->branchExternalIdLu,
            staffExternalId: $I::TEST_STAFF_EXT_ID_PREFIX.uniqid(),
            equipmentExternalId: 'random-equipment-ext-id-'.uniqid(),
            country: 'LU',
        ));

        $I->amAuthenticatedAsPortalUser('portal-admin-de-lu', 'portal-admin-de-lu', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $branches = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($branches as $branch) {
            if ($this->branchExternalIdLu === $branch['external_id']) {
                $this->branchIdLu = $branch['branch_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->branchIdLu);

        $I->callApiPortalDakoAccountCreate(
            $this->getDakoAccountJson($this->dakoIdLu, $this->dakoIdLu, []),
        );
        $I->callApiPortalDakoAccountList();
        $dakoAccounts = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($dakoAccounts as $dakoAccount) {
            if ($this->dakoIdLu === $dakoAccount['dako_id']) {
                $this->dakoAccountIdLu = $dakoAccount['dako_account_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->dakoAccountIdLu);

        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdLu,
            $this->getDakoAccountJson($this->dakoIdLu, $this->dakoIdLu, [$this->branchIdLu]),
        );

        $I->callApiPortalDakoAccountList();
        $dakoAccountNames = $I->grabDataFromResponseByJsonPath('$.items.*.name');
        $I->assertContains($this->dakoIdLu, $dakoAccountNames);
    }

    #[Depends('canAccessAsPortalAdmin')]
    public function canAccessAsCountryAdmin(ApiTester $I): void
    {
        $this->branchExternalIdDe = 'random-branch-ext-de-'.uniqid();
        $this->dakoIdDe = 'random-account-de-'.uniqid();

        $I->callSapGermanyTourUpsert(data: SampleDakoTour::jsonPayload(
            branchExternalId: $this->branchExternalIdDe,
            staffExternalId: $I::TEST_STAFF_EXT_ID_PREFIX.uniqid(),
            equipmentExternalId: 'random-equipment-ext-id-'.uniqid(),
        ));

        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');

        $I->callApiPortalBranchListAccessible();
        $branches = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($branches as $branch) {
            if ($this->branchExternalIdDe === $branch['external_id']) {
                $this->branchIdDe = $branch['branch_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->branchIdDe);

        $I->callApiPortalDakoAccountCreate(
            $this->getDakoAccountJson($this->dakoIdDe, $this->dakoIdDe, [$this->branchIdDe]),
        );
        $I->callApiPortalDakoAccountList();
        $dakoAccounts = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($dakoAccounts as $dakoAccount) {
            if ($this->dakoIdDe === $dakoAccount['dako_id']) {
                $this->dakoAccountIdDe = $dakoAccount['dako_account_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->dakoAccountIdDe);
        $I->callApiPortalDakoAccountList();
        $dakoAccountNames = $I->grabDataFromResponseByJsonPath('$.items.*.name');
        $I->assertContains($this->dakoIdDe, $dakoAccountNames);
        $I->assertNotContains($this->dakoIdLu, $dakoAccountNames);
    }

    #[Depends('canAccessAsCountryAdmin')]
    public function canNotSeeForeignCountryAccounts(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de-lu', 'portal-admin-de-lu', 'portal-password');
        $I->callApiPortalDakoAccountList();
        $dakoAccountNames = $I->grabDataFromResponseByJsonPath('$.items.*.name');
        $I->assertNotContains($this->dakoIdDe, $dakoAccountNames);
        $I->assertContains($this->dakoIdLu, $dakoAccountNames);
    }

    #[Depends('canNotSeeForeignCountryAccounts')]
    public function canNotAccessRestrictedDakoAccounts(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de-lu', 'portal-admin-de-lu', 'portal-password');
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdLu);
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe, 403);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdLu, 403);
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe);

        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdLu, 403);
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe);
    }

    #[Depends('canNotAccessRestrictedDakoAccounts')]
    public function canNotUpdateRestrictedDakoAccounts(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de-lu', 'portal-admin-de-lu', 'portal-password');
        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdLu,
            $this->getDakoAccountJson($this->dakoIdLu, $this->dakoIdLu, [$this->branchIdLu]),
        );
        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdDe,
            $this->getDakoAccountJson($this->dakoIdDe, $this->dakoIdDe, [$this->branchIdDe]),
            403,
        );

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdLu,
            $this->getDakoAccountJson($this->dakoIdLu, $this->dakoIdLu, [$this->branchIdLu]),
            403
        );
        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdDe,
            $this->getDakoAccountJson($this->dakoIdDe, $this->dakoIdDe, [$this->branchIdDe]),
        );

        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdLu,
            $this->getDakoAccountJson($this->dakoIdLu, $this->dakoIdLu, [$this->branchIdLu]),
            403
        );
        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdDe,
            $this->getDakoAccountJson($this->dakoIdDe, $this->dakoIdDe, [$this->branchIdDe]),
        );
    }

    #[Depends('canNotUpdateRestrictedDakoAccounts')]
    public function canCreateToggleCountryBranch(ApiTester $I): void
    {
        $this->branchExternalIdToggle = 'random-branch-ext-toggle-'.uniqid();
        $this->toggleTourExternalId = 'toggle-tour-'.uniqid();
        $I->callSapGermanyTourUpsert(data: SampleDakoTour::jsonPayload(
            branchExternalId: $this->branchExternalIdToggle,
            staffExternalId: $I::TEST_STAFF_EXT_ID_PREFIX.uniqid(),
            equipmentExternalId: 'random-equipment-ext-id-'.uniqid(),
            country: 'DE',
            tourExternalId: $this->toggleTourExternalId,
        ));
        $I->amAuthenticatedAsPortalUser('portal-admin-de-both', 'portal-admin-de-both', 'portal-password');

        $I->callApiPortalBranchListAccessible();
        $branches = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($branches as $branch) {
            if ($this->branchExternalIdToggle === $branch['external_id']) {
                $this->branchIdToggle = $branch['branch_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->branchIdToggle);

        $I->callApiPortalDakoAccountUpdate(
            $this->dakoAccountIdDe,
            $this->getDakoAccountJson($this->dakoIdDe, $this->dakoIdDe, [$this->branchIdDe, $this->branchIdToggle]),
        );

        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe);
        $branchIds = $I->grabDataFromResponseByJsonPath('$.branches')[0];
        $I->assertContains($this->branchIdDe, $branchIds);
        $I->assertContains($this->branchIdToggle, $branchIds);
    }

    #[Depends('canCreateToggleCountryBranch')]
    public function canSeeBranchInMyCountry(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe);
        $branchIds = $I->grabDataFromResponseByJsonPath('$.branches')[0];
        $I->assertContains($this->branchIdDe, $branchIds);
        $I->assertContains($this->branchIdToggle, $branchIds);
    }

    #[Depends('canSeeBranchInMyCountry')]
    public function canToggleBranchLocationCountryWithoutLosingAccess(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(data: SampleDakoTour::jsonPayload(
            branchExternalId: $this->branchExternalIdToggle,
            staffExternalId: $I::TEST_STAFF_EXT_ID_PREFIX.uniqid(),
            equipmentExternalId: 'random-equipment-ext-id-'.uniqid(),
            country: 'LU',
            tourExternalId: $this->toggleTourExternalId,
        ));

        $I->amAuthenticatedAsPortalUser('portal-admin-de-both', 'portal-admin-de-both', 'portal-password');
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe);
        $branchIds = $I->grabDataFromResponseByJsonPath('$.branches')[0];
        $I->assertContains($this->branchIdDe, $branchIds);
        $I->assertContains($this->branchIdToggle, $branchIds);
        $I->callApiPortalBranchListAccessible();
        $accessibleBranchIds = $I->grabDataFromResponseByJsonPath('$.items.*.branch_id');
        $I->assertContains($this->branchIdDe, $accessibleBranchIds);
        $I->assertContains($this->branchIdToggle, $accessibleBranchIds);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountDetails($this->dakoAccountIdDe);
        $branchIds = $I->grabDataFromResponseByJsonPath('$.branches')[0];
        $I->assertContains($this->branchIdDe, $branchIds);
        $I->assertContains($this->branchIdToggle, $branchIds);
        $I->callApiPortalBranchListAccessible();
        $accessibleBranchIds = $I->grabDataFromResponseByJsonPath('$.items.*.branch_id');
        $I->assertContains($this->branchIdDe, $accessibleBranchIds);
        $I->assertContains($this->branchIdToggle, $accessibleBranchIds);

        $I->amAuthenticatedAsPortalUser('portal-admin-de-lu', 'portal-admin-de-lu', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $accessibleBranchIds = $I->grabDataFromResponseByJsonPath('$.items.*.branch_id');
        $I->assertNotContains($this->branchIdToggle, $accessibleBranchIds);
    }

    private function getDakoAccountJson(string $name, string $dakoId, array $branches = []): string
    {
        return json_encode([
            'name' => $name,
            'dako_id' => $dakoId,
            'branches' => $branches,
        ]);
    }
}
