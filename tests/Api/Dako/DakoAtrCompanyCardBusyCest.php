<?php

declare(strict_types=1);

namespace Api\Dako;

use App\Tests\Api\Dako\SampleDakoTour;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class DakoAtrCompanyCardBusyCest
{
    private string $branchId = '';
    private string $dakoAccountId = '';
    private string $equipmentId = '';
    private const string DAKO_ACCOUNT_ID = 'dako-fail-3';
    private const string DAKO_ACCOUNT_NAME = 'Dako Fail 3';
    private const string DAKO_BRANCH_EXTERNAL_ID = 'dako-branch-fail-3';
    private const string STAFF_EXTERNAL_ID = ApiTester::TEST_STAFF_EXT_ID_PREFIX.'dako-staff-fail-3';
    private const string EQUIPMENT_EXTERNAL_ID = 'dako-equipment-fail-3';

    public function canCreateBranchAndEquipmentForDako(ApiTester $I): void
    {
        $deviceId = uniqid();
        $I->amUsingDeviceWithId($deviceId);

        $I->callSapGermanyTourUpsert(data: SampleDakoTour::jsonPayload(
            branchExternalId: self::DAKO_BRANCH_EXTERNAL_ID,
            staffExternalId: self::STAFF_EXTERNAL_ID,
            equipmentExternalId: self::EQUIPMENT_EXTERNAL_ID,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsDriverUser(
                self::STAFF_EXTERNAL_ID,
                'pz'.self::STAFF_EXTERNAL_ID,
                self::STAFF_EXTERNAL_ID
            );
        });
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipments = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($equipments as $equipment) {
            if (self::EQUIPMENT_EXTERNAL_ID === $equipment['license_plate']) {
                $this->equipmentId = $equipment['uuid'];
                break;
            }
        }
        $I->assertNotEmpty($this->equipmentId);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $branches = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($branches as $branch) {
            if (self::DAKO_BRANCH_EXTERNAL_ID === $branch['external_id']) {
                $this->branchId = $branch['branch_id'];
                break;
            }
        }

        $I->assertNotEmpty($this->branchId);
    }

    #[Depends('canCreateBranchAndEquipmentForDako')]
    public function canCreateDakoAccount(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalDakoAccountCreate(
            $this->getDakoAccountJson(self::DAKO_ACCOUNT_NAME, self::DAKO_ACCOUNT_ID, [$this->branchId]),
        );

        $I->callApiPortalDakoAccountList();
        $dakoAccounts = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($dakoAccounts as $dakoAccount) {
            if (self::DAKO_ACCOUNT_ID === $dakoAccount['dako_id']) {
                $this->dakoAccountId = $dakoAccount['dako_account_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->dakoAccountId);
    }

    #[Depends('canCreateDakoAccount')]
    public function canGetDakoEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );

        $I->callApiEquipmentGetItemV2($this->equipmentId);
        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertTrue($equipmentDetails['ddd_file_required']);
    }

    #[Depends('canGetDakoEquipment')]
    public function canNotGetDakoAtrForBusyCard(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );

        $I->callApiDakoGetAtr(
            data: json_encode(['equipment_id' => $this->equipmentId]),
            expectedStatusCode: 409,
        );
        $errorResponse = $I->grabDataFromResponseByJsonPath('$.')[0];
        $I->assertEquals('company_card_busy', $errorResponse['error_type']);
        $I->assertTrue($errorResponse['retry_later']);
    }

    private function getDakoAccountJson(string $name, string $dakoId, array $branches = []): string
    {
        return json_encode([
            'name' => $name,
            'dako_id' => $dakoId,
            'branches' => $branches,
        ]);
    }
}
