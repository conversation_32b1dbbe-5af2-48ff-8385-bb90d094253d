<?php

declare(strict_types=1);

namespace App\Tests\Api\Dako;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class DakoSuccessAfterAbortCest
{
    private string $branchId = '';
    private string $equipmentId = '';
    private string $dakoAccountId = '';
    private string $dakoProcessId = '';
    private const string DAKO_ACCOUNT_ID = 'dako-success-5';
    private const string DAKO_ACCOUNT_NAME = 'dako-success-5';
    private const string DAKO_BRANCH_EXTERNAL_ID = 'dako-branch-success-5';
    private const string STAFF_EXTERNAL_ID = ApiTester::TEST_STAFF_EXT_ID_PREFIX.'dako-staff-success-5';
    private const string EQUIPMENT_EXTERNAL_ID = 'dako-equipment-success-5';

    public function canCreateBranchAndEquipmentForDako(ApiTester $I): void
    {
        $deviceId = uniqid();
        $I->amUsingDeviceWithId($deviceId);

        $I->callSapGermanyTourUpsert(data: SampleDakoTour::jsonPayload(
            branchExternalId: self::DAKO_BRANCH_EXTERNAL_ID,
            staffExternalId: self::STAFF_EXTERNAL_ID,
            equipmentExternalId: self::EQUIPMENT_EXTERNAL_ID,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsDriverUser(
                self::STAFF_EXTERNAL_ID,
                'pz'.self::STAFF_EXTERNAL_ID,
                self::STAFF_EXTERNAL_ID
            );
        });
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipments = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($equipments as $equipment) {
            if (self::EQUIPMENT_EXTERNAL_ID === $equipment['license_plate']) {
                $this->equipmentId = $equipment['uuid'];
                break;
            }
        }
        $I->assertNotEmpty($this->equipmentId);
        $I->callApiEquipmentGetItemV2($this->equipmentId);
        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertFalse($equipmentDetails['ddd_file_required']);
        $I->assertNull($equipmentDetails['last_ddd_file_upload'] ?? null);

        $I->callApiUserProfileV2();
        $userDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertFalse($userDetails['ddd_file_required']);
        $I->assertNull($userDetails['last_ddd_file_upload'] ?? null);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $branches = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($branches as $branch) {
            if (self::DAKO_BRANCH_EXTERNAL_ID === $branch['external_id']) {
                $this->branchId = $branch['branch_id'];
                break;
            }
        }

        $I->assertNotEmpty($this->branchId);
    }

    #[Depends('canCreateBranchAndEquipmentForDako')]
    public function canCreateDakoAccount(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDakoAccountCreate(
            $this->getDakoAccountJson(self::DAKO_ACCOUNT_NAME, self::DAKO_ACCOUNT_ID, [$this->branchId]),
        );

        $I->callApiPortalDakoAccountList();
        $dakoAccounts = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($dakoAccounts as $dakoAccount) {
            if (self::DAKO_ACCOUNT_ID === $dakoAccount['dako_id']) {
                $this->dakoAccountId = $dakoAccount['dako_account_id'];
                break;
            }
        }
        $I->assertNotEmpty($this->dakoAccountId);

        $I->callApiPortalDakoAccountDetails($this->dakoAccountId);
        $dakoAccountDetails = $I->grabDataFromResponseByJsonPath('$.')[0];
        $I->assertEquals(self::DAKO_ACCOUNT_NAME, $dakoAccountDetails['name']);
        $I->assertEquals(self::DAKO_ACCOUNT_ID, $dakoAccountDetails['dako_id']);
        $I->assertContains($this->branchId, $dakoAccountDetails['branches']);
    }

    #[Depends('canCreateDakoAccount')]
    public function canGetDakoEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );

        $I->callApiEquipmentGetItemV2($this->equipmentId);
        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertTrue($equipmentDetails['ddd_file_required']);

        $I->callApiUserProfileV2();
        $userDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertTrue($userDetails['ddd_file_required']);
    }

    #[Depends('canGetDakoEquipment')]
    public function canGetDakoAtrSuccessfully(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );

        $I->callApiDakoGetAtr(
            json_encode(['equipment_id' => $this->equipmentId]),
        );
        $this->dakoProcessId = $I->grabDataFromResponseByJsonPath('$.dako_process_id')[0];
        $I->assertNotEmpty($this->dakoProcessId);

        $I->assertGreaterThan(
            new \DateTimeImmutable(),
            new \DateTimeImmutable($I->grabDataFromResponseByJsonPath('$.expires')[0]),
        );

        $I->callApiGetDakoProcess($this->dakoProcessId);
        $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        $I->assertIsArray($I->grabDataFromResponseByJsonPath('$.dako_apdu_exchanges')[0]);
        $I->assertEmpty($I->grabDataFromResponseByJsonPath('$.dako_apdu_exchanges')[0]);
    }

    #[Depends('canGetDakoAtrSuccessfully')]
    public function canAbortDakoProcess(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );
        $I->callApiAbortDakoSession(
            dakoProcessId: $this->dakoProcessId,
        );

        $I->callApiGetDakoProcess($this->dakoProcessId);
        $I->assertEquals('aborted', $I->grabDataFromResponseByJsonPath('$.status')[0]);
    }

    #[Depends('canAbortDakoProcess')]
    public function canUploadFirstDddFile(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );

        $fileContents = file_get_contents(codecept_data_dir('testfile.ddd'));
        $filename = 'test% & / file '.Uuid::v4()->toRfc4122().'.ddd';

        $I->callApiUploadDakoFile($this->dakoProcessId, $filename, $fileContents, 1, 2);
        $I->callApiGetDakoProcess($this->dakoProcessId);
        $I->assertContains($I->grabDataFromResponseByJsonPath('$.status')[0], ['uploaded']);

        $I->callApiRetrieveDakoFile($this->dakoProcessId, 1);

        $I->callApiEquipmentGetItemV2($this->equipmentId);
        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertTrue($equipmentDetails['ddd_file_required']);
        $I->assertNull($equipmentDetails['last_ddd_file_upload'] ?? null);

        $I->callApiUserProfileV2();
        $userDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertTrue($userDetails['ddd_file_required']);
        $I->assertNull($userDetails['last_ddd_file_upload'] ?? null);
        $I->tachoplusFileExists($filename);
    }

    #[Depends('canUploadFirstDddFile')]
    public function canUploadSecondDddFileAndCompleteProcess(ApiTester $I): void
    {
        $I->amAuthenticatedAsDriverUser(
            self::STAFF_EXTERNAL_ID,
            'pz'.self::STAFF_EXTERNAL_ID,
            self::STAFF_EXTERNAL_ID
        );

        $fileContents = file_get_contents(codecept_data_dir('testfile.ddd'));
        $filename = 'test& file_'.Uuid::v4()->toRfc4122().'.ddd';

        $I->callApiUploadDakoFile($this->dakoProcessId, $filename, $fileContents, 2, 2);
        $I->callApiRetrieveDakoFile($this->dakoProcessId, 1);

        $I->waitUntil(
            function (ApiTester $I) {
                $I->callApiGetDakoProcess($this->dakoProcessId);
                $I->assertContains($I->grabDataFromResponseByJsonPath('$.status')[0], ['transfered']);

                $I->callApiEquipmentGetItemV2($this->equipmentId);
                $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
                $I->assertFalse($equipmentDetails['ddd_file_required']);
                $I->assertNotNull($equipmentDetails['last_ddd_file_upload']);

                $I->callApiUserProfileV2();
                $userDetails = $I->grabDataFromResponseByJsonPath('$')[0];
                $I->assertFalse($userDetails['ddd_file_required']);
                $I->assertNotNull($userDetails['last_ddd_file_upload']);
            }
        );
        $I->tachoplusFileExists($filename);
    }

    private function getDakoAccountJson(string $name, string $dakoId, array $branches = []): string
    {
        return json_encode([
            'name' => $name,
            'dako_id' => $dakoId,
            'branches' => $branches,
        ]);
    }
}
