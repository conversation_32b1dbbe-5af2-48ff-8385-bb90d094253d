<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapSpain;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapSpainTourFixtures;
use App\Tests\Support\ApiTester;
use Symfony\Component\Uid\Uuid;

class SapSpainZOrderCest
{
    private string $geotabDeviceId = 'b101D';

    public function canBookOrder(ApiTester $I): void
    {
        $I->wireMockSetGeotabMappings($this->geotabDeviceId, 5555.667);
        $order = $this->createTourAndOrder($I);
        $I->callApiOrderBookingV2($order['uuid'], BookingFixtures::bookingStartRequestV2());
        $this->completeOrderTaskGroups($I, $order);
        $I->callApiOrderBookingV2($order['uuid'], BookingFixtures::bookingEndRequestV2());
        $I->wireMockClearGeotabMappings($this->geotabDeviceId);
    }

    private function createTourAndOrder(ApiTester $I): array
    {
        $tourExtId = 'random-es-tour-id-'.uniqid();
        $orderExtId = 'random-es-order-id-'.uniqid();
        $tour1Name = 'random-es-tour-name-'.uniqid();
        $deviceId = 'random-es-device-'.uniqid();
        $equipmentExtId = 'random-es-equipment-'.uniqid();
        $staffExtId = 'random-es-staff-'.uniqid();
        $equipmentLicensePlate = 'random-es-license-plate-'.uniqid();
        $sapTourId = $I->callSapSpainTour(SapSpainTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            staffExtId: $staffExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, 'pze');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);
        });

        return $I->grabDataFromResponseByJsonPath('$.orders[0]')[0];
    }

    private function completeOrderTaskGroups(ApiTester $I, $order): void
    {
        foreach ($order['taskgroups'] as $taskgroup) {
            foreach ($taskgroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    if ('signature' === $input['type']) {
                        $taskgroupId = $taskgroup['uuid'];
                        $taskId = $task['uuid'];
                        $inputId = $input['uuid'];

                        break 3;
                    }
                }
            }
        }

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $file1 = $fileUuid;

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $file2 = $fileUuid;

        $data = <<<JSON
        {

            "uuid": "{$taskgroupId}",
            "tasks": [
                {
                    "uuid": "{$taskId}",
                    "skipped": false,
                    "additional_service": false,
                    "repeat_service": false,
                    "repeat_service_template_uuid": "",
                    "inputs": [
                        {
                            "type": "input",
                            "uuid": "{$inputId}",
                            "value": [
                                {
                                    "file": "app-user-files/{$file1}",
                                    "name": "test1.png",
                                    "label": "test1"
                                },
                                {
                                    "file": "app-user-files/{$file2}",
                                    "name": "test2.png",
                                    "label": "test2"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        JSON;

        $I->callApiOrderTaskGroupPatchV2($order['uuid'], $data);
    }
}
