<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapSpain;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapSpainTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class SapSpainSapUnavailableCest
{
    private string $sapTourId = '';
    private string $orderId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $deviceId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $staffExtId = '';
    private string $tenant = 'pze';

    /**
     * @var string[]
     */
    private array $equipmentExtIds = [
    ];
    private string $equipmentUuid = '';

    public function canStartTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];

        $mappingIds = $I->wireMockSetSapSpainStatusTourStartMappings(
            tourExtId: $this->tourExtId,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            responseCode: 503,
        );

        $this->sapTourId = $I->callSapSpainTour(SapSpainTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    #[Depends('canStartTour')]
    public function canStartOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrder($I);
        $I->callApiOrderBookingV2(
            orderId: $this->orderId,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $this->validateOrderStatus($I, 'started');
        $order = $I->grabDataFromResponseByJsonPath('$.orders[0]')[0];
        $this->completeOrderTaskGroups($I, $order);
    }

    #[Depends('canStartOrder')]
    public function canEndOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2(
            orderId: $this->orderId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $this->validateOrderStatus($I, 'completed');
    }

    #[Depends('canEndOrder')]
    public function canEndTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        // End the tour
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's ended
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $I->assertEquals('completed', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });
    }

    #[Depends('canEndTour')]
    public function canRetrySapCalls(ApiTester $I): void
    {
        $mappingIds = $I->wireMockSetSapSpainStatusTourStartMappings(
            $this->tourExtId,
            [$this->staffExtId],
            $this->equipmentExtIds
        );
        $mappingIds[] = $I->wireMockAddSapSpainStatusCallMapping(
            objectType: 'order',
            objectExternalId: $this->orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'started'
        );
        $mappingIds[] = $I->wireMockAddSapSpainStatusCallMapping(
            objectType: 'order',
            objectExternalId: $this->orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'completed'
        );
        $mappingIds[] = $I->wireMockAddSapSpainStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'completed'
        );

        $mappingIds[] = $I->wireMockAddSapSpainConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            orderLocations: [],
            termination: null,
            notes: null
        );

        $I->retrySapCalls($this->staffExtId, 'pze');

        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    private function setCreatedOrder(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);

        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');

        $I->assertEquals('created', $firstOrderStatus[0]);

        $this->orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }

    private function completeOrderTaskGroups(ApiTester $I, $order): void
    {
        foreach ($order['taskgroups'] as $taskgroup) {
            foreach ($taskgroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    if ('signature' === $input['type']) {
                        $taskgroupId = $taskgroup['uuid'];
                        $taskId = $task['uuid'];
                        $inputId = $input['uuid'];

                        break 3;
                    }
                }
            }
        }

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $file1 = $fileUuid;

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $file2 = $fileUuid;

        $data = <<<JSON
        {

            "uuid": "{$taskgroupId}",
            "tasks": [
                {
                    "uuid": "{$taskId}",
                    "skipped": false,
                    "additional_service": false,
                    "repeat_service": false,
                    "repeat_service_template_uuid": "",
                    "inputs": [
                        {
                            "type": "input",
                            "uuid": "{$inputId}",
                            "value": [
                                {
                                    "file": "app-user-files/{$file1}",
                                    "name": "test1.png",
                                    "label": "test1"
                                },
                                {
                                    "file": "app-user-files/{$file2}",
                                    "name": "test2.png",
                                    "label": "test2"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        JSON;

        $I->callApiOrderTaskGroupPatchV2($order['uuid'], $data);
    }
}
