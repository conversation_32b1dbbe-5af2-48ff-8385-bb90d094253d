<?php

declare(strict_types=1);

namespace App\Tests\Api\SapSpain;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapSpainTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapSpainOrderRestartCest
{
    private string $tenant = 'pze';
    private string $staffExtId = '';
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $deviceId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';

    /**
     * @var string[]
     */
    private array $orderIds = [];

    public function canStartSapSpainTourOrder(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->sapTourId = $I->callSapSpainTour(SapSpainTourFixtures::tourMultiOrderRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);
        $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
        $mappingId = $this->setOrderStatusExpectations($I, $this->orderExtId, 'started');

        $I->assertCount(3, $orders);
        $this->orderIds = array_column($orders, 'uuid');

        $I->callApiOrderBookingV2($this->orderIds[0], BookingFixtures::bookingStartRequestV2());
        $I->wireMockVerifyRequestMapping([$mappingId]);
    }

    #[Depends('canStartSapSpainTourOrder')]
    public function canSwitchOrderByBooking(ApiTester $I): void
    {
        $mappingIds = [
            $this->setOrderStatusExpectations($I, $this->orderExtId, 'break'),
            $this->setOrderStatusExpectations($I, $this->orderExtId.'-2', 'started'),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2($this->orderIds[1], BookingFixtures::bookingStartRequestV2());
        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    #[Depends('canSwitchOrderByBooking')]
    public function canSwitchOrderByFocus(ApiTester $I): void
    {
        $mappingIds = [
            $this->setOrderStatusExpectations($I, $this->orderExtId, 'restart'),
            $this->setOrderStatusExpectations($I, $this->orderExtId.'-2', 'break'),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderFocusV2($this->orderIds[0]);
        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    #[Depends('canSwitchOrderByFocus')]
    public function canSwitchBackOrderByFocus(ApiTester $I): void
    {
        $mappingIds = [
            $this->setOrderStatusExpectations($I, $this->orderExtId, 'break'),
            $this->setOrderStatusExpectations($I, $this->orderExtId.'-2', 'restart'),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderFocusV2($this->orderIds[1]);
        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    #[Depends('canSwitchOrderByFocus')]
    public function canEndOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $mappingIds = [
            $this->setOrderStatusExpectations($I, $this->orderExtId, 'completed'),
        ];

        $I->callApiOrderBookingV2($this->orderIds[0], BookingFixtures::bookingEndRequestV2());
        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    private function setOrderStatusExpectations(
        ApiTester $I,
        string $orderExtId, string $status,
    ): string {
        return $I->wireMockAddSapSpainStatusCallMapping(
            objectType: 'order',
            objectExternalId: $orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: [],
            status: $status,
        );
    }
}
