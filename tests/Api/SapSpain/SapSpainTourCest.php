<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapSpain;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapSpainTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use League\Flysystem\PhpseclibV3\SftpAdapter;
use League\Flysystem\PhpseclibV3\SftpConnectionProvider;

class SapSpainTourCest
{
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tour1Name = '';
    private string $deviceId = '';
    private string $equipmentExtId = '';
    private string $equipmentUuid = '';
    private string $staffExtId = '';
    private string $equipmentLicensePlate = '';
    private string $geotabDeviceId = '';
    private float $mileage = 0;
    private Filesystem $filesystem;
    private string $tenant = 'pze';

    public function canCreateSpanishTour(ApiTester $I): void
    {
        $this->geotabDeviceId = uniqid();
        $this->mileage = rand(1000, 10000);

        $this->uploadTestFiles();
        $I->wireMockSetGeotabMappings($this->geotabDeviceId, $this->mileage);

        $this->tourExtId = 'random-es-tour-id-'.uniqid();
        $this->orderExtId = 'random-es-order-id-'.uniqid();
        $this->tour1Name = 'random-es-tour-name-'.uniqid();
        $this->deviceId = 'random-es-device-'.uniqid();
        $this->equipmentExtId = 'random-es-equipment-'.uniqid();
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->equipmentLicensePlate = 'random-es-license-plate-'.uniqid();
        $infoFiles = [
            [
                'name' => 'instruction no 1',
                'identifier' => 'ers-sdf-ghh-1.pdf',
                'source' => 'spain_sftp',
                'fileType' => 'pdf',
            ],
            [
                'name' => 'instruction no 2',
                'identifier' => 'ers-sdf-ghh-2.pdf',
                'source' => 'spain_sftp',
                'fileType' => 'pdf',
            ],
        ];

        $this->sapTourId = $I->callSapSpainTour(SapSpainTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            tourInfoFiles: $infoFiles,
            orderInfoFiles: $infoFiles,
        ));
        $I->amUsingDeviceWithId($this->deviceId);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2($this->sapTourId);

            $infoFile1 = $I->grabDataFromResponseByJsonPath('$.orders.0.additional_informations.4.description_file_name')[0];
            $infoFile2 = $I->grabDataFromResponseByJsonPath('$.orders.0.additional_informations.5.description_file_name')[0];

            $I->assertEquals('instruction no 1', $infoFile1);
            $I->assertEquals('instruction no 2', $infoFile2);

            $infoFile1 = $I->grabDataFromResponseByJsonPath('$.additional_informations.0.description_file_name')[0];
            $infoFile2 = $I->grabDataFromResponseByJsonPath('$.additional_informations.1.description_file_name')[0];

            $I->assertEquals('instruction no 1', $infoFile1);
            $I->assertEquals('instruction no 2', $infoFile2);
        });
    }

    #[Depends('canCreateSpanishTour')]
    public function canConnectDevice(ApiTester $I): void
    {
        $this->equipmentUuid = $this->getEquipmentUuid($I, $this->equipmentExtId);

        $I->amAuthenticatedAsPortalUser('portal-admin-es', 'portal-admin-es', 'portal-password');
        $data = $this->getConnectedDevicesRequest(
            macAddress: '',
            hardwareType: 'geotab',
            connectionType: 'cloud_api',
            pin: $this->geotabDeviceId,
        );

        $I->callApiPortalEquipmentItem($this->equipmentUuid);
        $equipment = $I->grabDataFromResponseByJsonPath('$')[0];
        $equipmentData = array_intersect_key($equipment, array_flip([
            'branch_id',
            'external_id',
            'type',
            'license_plate',
        ])) + [
            'connected_devices' => [$data],
        ];

        $I->callApiPortalEquipmentUpdate($this->equipmentUuid, json_encode($equipmentData));
        $I->callApiPortalEquipmentItem($this->equipmentUuid);
        $equipment = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->waitUntil(function (ApiTester $I) use ($equipment): void {
            $connectedDevices = $equipment['connected_devices'];
            assert(is_array($connectedDevices));
            $I->assertContains('geotab', $connectedDevices[0]);
            $I->assertContains('cloud_api', $connectedDevices[0]);
            $I->assertContains($this->geotabDeviceId, $connectedDevices[0]);
        });
    }

    #[Depends('canConnectDevice')]
    public function canGetTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2($this->sapTourId);

            $infoFile1 = $I->grabDataFromResponseByJsonPath('$.orders.0.additional_informations.4.description_file_name')[0];
            $infoFile2 = $I->grabDataFromResponseByJsonPath('$.orders.0.additional_informations.5.description_file_name')[0];

            $I->assertEquals('instruction no 1', $infoFile1);
            $I->assertEquals('instruction no 2', $infoFile2);
        });
    }

    #[Depends('canGetTour')]
    public function canUpdateSpanishTour(ApiTester $I): void
    {
        $this->uploadTestFiles();

        $infoFiles = [
            [
                'name' => 'instruction no 1',
                'identifier' => 'ers-sdf-ghh-1.pdf',
                'source' => 'spain_sftp',
                'fileType' => 'pdf',
            ],
            [
                'name' => 'instruction no 3',
                'identifier' => 'ers-sdf-ghh-3.pdf',
                'source' => 'spain_sftp',
                'fileType' => 'pdf',
            ],
        ];

        $this->sapTourId = $I->callSapSpainTour(SapSpainTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            tourInfoFiles: $infoFiles,
            orderInfoFiles: $infoFiles
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2($this->sapTourId);

            $infoFile1 = $I->grabDataFromResponseByJsonPath('$.orders.0.additional_informations.4.description_file_name')[0];
            $infoFile2 = $I->grabDataFromResponseByJsonPath('$.orders.0.additional_informations.5.description_file_name')[0];

            $I->assertEquals('instruction no 1', $infoFile1);
            $I->assertEquals('instruction no 3', $infoFile2);

            $infoFile1 = $I->grabDataFromResponseByJsonPath('$.additional_informations.0.description_file_name')[0];
            $infoFile2 = $I->grabDataFromResponseByJsonPath('$.additional_informations.1.description_file_name')[0];

            $I->assertEquals('instruction no 1', $infoFile1);
            $I->assertEquals('instruction no 3', $infoFile2);
        });
    }

    #[Depends('canUpdateSpanishTour')]
    public function canNotStartSpanishTourWithoutEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        // try to start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('unprocessed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's not started
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);
    }

    #[Depends('canNotStartSpanishTourWithoutEquipment')]
    public function canBookEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentGetCollectionV2();
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
        });
    }

    #[Depends('canBookEquipment')]
    public function canStartSpanishTourWithEquipment(ApiTester $I): void
    {
        $mappingIds = [
            $this->setStatusCallExpectation(
                apiTester: $I,
                status: 'started',
                tourExtId: $this->tourExtId,
                staffExtId: $this->staffExtId,
                equipmentExtId: $this->equipmentExtId
            ),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $I->wireMockVerifyRequestMapping($mappingIds);
    }

    #[Depends('canStartSpanishTourWithEquipment')]
    public function canEndSpanishTour(ApiTester $I): void
    {
        $mappingIds = [
            $this->setStatusCallExpectation(
                apiTester: $I,
                status: 'completed',
                tourExtId: $this->tourExtId,
                staffExtId: $this->staffExtId,
                equipmentExtId: $this->equipmentExtId
            ),
        ];
        $mappingIds[] = $I->wireMockAddSapSpainConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: [$this->equipmentExtId],
            orderLocations: [],
            termination: null,
            notes: null
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('completed', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $I->wireMockVerifyRequestMapping($mappingIds);
        $I->wireMockClearGeotabMappings($this->geotabDeviceId);
    }

    private function setStatusCallExpectation(ApiTester $apiTester, string $status, string $tourExtId, ?string $staffExtId, ?string $equipmentExtId): string
    {
        return $apiTester->wireMockAddSapSpainStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$staffExtId],
            equipmentExtIds: [$equipmentExtId],
            status: $status,
        );
    }

    private function uploadTestFiles(): void
    {
        $adapter = new SftpAdapter(
            new SftpConnectionProvider(
                host: getenv('SAP_SPAIN_FTP_HOST'),
                username: getenv('SAP_SPAIN_FTP_USERNAME'),
                password: getenv('SAP_SPAIN_FTP_PASSWORD'),
                port: (int) getenv('SAP_SPAIN_FTP_PORT'),
                timeout: 10,
                maxTries: 2,
            ),
            root: getenv('SAP_SPAIN_FTP_ROOT_PATH'),
        );

        $this->filesystem = new Filesystem($adapter);

        $out = getenv('SAP_SPAIN_FTP_OUT_PATH');

        $this->upload(
            fileName: $out.'ers-sdf-ghh-1.pdf',
            content: file_get_contents(codecept_data_dir('ers-sdf-ghh-1.pdf')),
        );

        $this->upload(
            fileName: $out.'ers-sdf-ghh-2.pdf',
            content: file_get_contents(codecept_data_dir('ers-sdf-ghh-2.pdf')),
        );

        $this->upload(
            fileName: $out.'ers-sdf-ghh-3.pdf',
            content: file_get_contents(codecept_data_dir('ers-sdf-ghh-3.pdf')),
        );
    }

    private function upload(string $fileName, string $content): void
    {
        try {
            $this->filesystem->write($fileName, $content);
        } catch (FilesystemException $e) {
            throw new \RuntimeException('Failed to upload file to Spain FTP: '.$e->getMessage());
        }
    }

    private function getEquipmentUuid(ApiTester $I, string $externalId): string
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-es', 'portal-admin-es', 'portal-password');
        $I->callApiPortalEquipmentList();

        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');

        foreach ($responseData as $data) {
            if ($data['external_id'] === $externalId) {
                return $data['equipment_id'];
            }
        }

        throw new \RuntimeException('new equipment not found');
    }

    private function getConnectedDevicesRequest(string $macAddress, string $hardwareType, string $connectionType, string $pin): array
    {
        return [
            'mac_address' => $macAddress,
            'hardware_type' => $hardwareType,
            'connection_type' => $connectionType,
            'pin' => $pin,
        ];
    }
}
