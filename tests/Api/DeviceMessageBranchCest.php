<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Infrastructure\PortalApi\Resource\Dto\DeviceMessage\RecipientKind;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class DeviceMessageBranchCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    private string $branchExtId1 = '';
    private string $branchExtId2 = '';
    private string $deviceId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $branch1Id = '';
    private string $branch2Id = '';

    private string $thread1Id = '';
    private string $thread2Id = '';

    private string $strangeBranchExternalId = '';
    private string $strangeBranchStaffExtId = '';

    public function driverCanCreateMessageInBranch1(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-1-id-'.uniqid();
        $this->orderExtId = 'random-order-1-id-'.uniqid();
        $this->tourName = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->branchExtId1 = 'random-branch-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            branchExternalId: $this->branchExtId1,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('small-img.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'device-message',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $message = 'Some message body '.uniqid();

        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'message' => $message,
                'attachments' => ['device-message/'.$fileUuid],
            ]),
        );

        $this->thread1Id = $I->grabDataFromResponseByJsonPath('$.thread_id')[0];

        $I->callApiDeviceMessagesV2();
        $response = $I->grabDataFromResponseByJsonPath('$.items')[0];

        $I->assertNotEmpty($response);
        $found = false;
        $messageId = null;

        foreach ($response as $data) {
            $I->assertNotEmpty($data['recipient']);
            if ($data['id'] === $this->thread1Id) {
                $I->assertCount(1, $data['messages']);
                $I->assertEquals('device', $data['messages'][0]['from']);
                $I->assertEquals($message, $data['messages'][0]['message']);
                $I->assertEquals('device-message/'.$fileUuid, $data['messages'][0]['attachments'][0]);
                $I->assertEquals(true, $data['messages'][0]['is_read']);
                $I->callApiFileRetrieveV2('device-message/'.$fileUuid);
                $I->assertEquals($fileContent, $I->grabResponse());
                $found = true;
                $messageId = $data['messages'][0]['id'];
                break;
            }
        }

        $I->assertTrue($found, 'Thread not found');
        $this->branch1Id = $this->getBranchId($I, $this->branchExtId1);
        $I->assertNotEmpty($this->branch1Id);

        // Validate centrifugo has received the message for branch
        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'branch:'.bin2hex($this->branch1Id),
            messageParts: [
                'id' => $messageId,
                'username' => $this->tenant.$this->staffExtId,
                'thread_id' => $this->thread1Id,
                'message' => $message,
                'attachments' => ['device-message/'.$fileUuid],
                'type' => 'device_message',
            ],
        );

        // Validate centrifugo has received the message for country
        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'country:'.bin2hex('de'),
            messageParts: [
                'id' => $messageId,
                'username' => $this->tenant.$this->staffExtId,
                'thread_id' => $this->thread1Id,
                'message' => $message,
                'attachments' => ['device-message/'.$fileUuid],
                'type' => 'device_message',
            ],
        );
    }

    #[Depends('driverCanCreateMessageInBranch1')]
    public function driverFromAnotherBranchCanJoin(ApiTester $I): void
    {
        $this->strangeBranchStaffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tourExtId = 'random-tour-1-id-'.uniqid();
        $orderExtId = 'random-order-1-id-'.uniqid();
        $tourName = 'random-tour-1-name-'.uniqid();
        $this->strangeBranchExternalId = 'random-branch-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tourName,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $this->strangeBranchStaffExtId,
            branchExternalId: $this->strangeBranchExternalId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->strangeBranchStaffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $message = 'Some message body in a strange branch '.uniqid();

        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'message' => $message,
                'attachments' => [],
            ]),
        );
        $I->callApiDeviceMessagesV2(debug: false);
        $threads = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($threads);
        $I->assertCount(2, $threads);
        $messageCounts = array_map(fn (array $thread): int => count($thread['messages']), $threads);
        $I->assertEquals([1, 1], $messageCounts);
    }

    #[Depends('driverFromAnotherBranchCanJoin')]
    public function cannotCreateDeviceMessageWithInvalidAttachment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('small-img.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'message' => 'Some message body '.uniqid(),
                'attachments' => ['device-message/'.$fileUuid],
            ]),
            expectedStatusCode: 400
        );
    }

    #[Depends('cannotCreateDeviceMessageWithInvalidAttachment')]
    public function canReplyToExistingThread(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'thread_id' => $this->thread1Id,
                'message' => 'Some message reply body '.uniqid(),
            ]),
        );
    }

    #[Depends('canReplyToExistingThread')]
    public function canGetMessageHistory(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiDeviceMessagesV2();
        $threads = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($threads);
        $I->assertCount(2, $threads);
        $messageCounts = array_map(fn (array $thread): int => count($thread['messages']), $threads);
        sort($messageCounts);
        $I->assertEquals([1, 2], $messageCounts);
    }

    #[Depends('canGetMessageHistory')]
    public function driverCanSwitchBranches(ApiTester $I): void
    {
        $this->branchExtId2 = 'random-branch-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            branchExternalId: $this->branchExtId2,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $message = 'Some message body in branch2 '.uniqid();
        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'message' => $message,
                'attachments' => [],
            ]),
        );
        $this->thread2Id = $I->grabDataFromResponseByJsonPath('$.thread_id')[0];
        $I->assertNotEquals($this->thread1Id, $this->thread2Id);

        $I->callApiDeviceMessagesV2(debug: false);
        $threads = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertCount(1, $threads);
        $I->assertCount(1, $threads[0]['messages']);
    }

    #[Depends('driverCanSwitchBranches')]
    public function canMarkMessageAsReadV2(ApiTester $I): void
    {
        // First create a new message to the user from the portal
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalStaffList(query: $this->staffExtId);

        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $staffId = $data['items'][0]['staff_id'];

        $data = [
            'recipient' => [
                'kind' => RecipientKind::STAFF->value,
                'staff_id' => $staffId,
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $I->callApiPortalDeviceMessageThreadList();
        $threads = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($threads as $thread) {
            if ($thread['last_message_excerpt'] === $data['message']) {
                break;
            }
        }

        $I->assertNotEmpty($thread, 'Thread not found');
        $I->callApiPortalDeviceMessagesInThread($thread['id']);
        $message = $I->grabDataFromResponseByJsonPath('$.items[?(@.message == "'.$data['message'].'")]')[0];

        // See that the message is unread in the history
        $deviceId = uniqid();
        $I->amUsingDeviceWithId($deviceId);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();
        $I->callApiDeviceMessagesV2();
        $messagesReceivedFromDevice = $I->grabDataFromResponseByJsonPath('$.items[?(@.id == "'.$thread['id'].'")].messages')[0];

        $I->assertGreaterOrEquals(1, count($messagesReceivedFromDevice));
        $found = false;

        foreach ($messagesReceivedFromDevice as $receivedMessage) {
            if ($receivedMessage['id'] === $message['id']) {
                $found = true;
                $I->assertFalse($receivedMessage['is_read']);
                break;
            }
        }
        $I->assertTrue($found, 'Message not found in thread');

        // Set message as read
        $I->callApiDeviceMessageMarkAsReadV2(json_encode([
            'message_ids' => [$message['id']],
        ]));

        // See that the message is now read in the history
        $I->waitUntil(function (ApiTester $I) use ($thread, $message): void {
            $I->callApiDeviceMessagesV2();
            $messagesReceivedFromDevice = $I->grabDataFromResponseByJsonPath('$.items[?(@.id == "'.$thread['id'].'")].messages')[0];

            $I->assertGreaterOrEquals(1, count($messagesReceivedFromDevice));
            $found = false;

            foreach ($messagesReceivedFromDevice as $receivedMessage) {
                if ($receivedMessage['id'] === $message['id']) {
                    $found = true;
                    $I->assertTrue($receivedMessage['is_read']);
                    break;
                }
            }

            $I->assertTrue($found, 'Message not found in thread');
        });

        // Reply to the message
        $I->callApiDeviceMessageCreateV2(json_encode([
            'thread_id' => $thread['id'],
            'message' => 'Some message reply body '.uniqid(),
        ]));

        // See that the portal thread now has unread messages
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->waitUntil(function (ApiTester $I) use ($thread): void {
            $I->callApiPortalDeviceMessageThreadList();
            $currentThreads = $I->grabDataFromResponseByJsonPath('$.items.*');
            $found = false;

            foreach ($currentThreads as $currentThread) {
                if ($currentThread['id'] === $thread['id']) {
                    $found = true;
                    $I->assertTrue($currentThread['has_unread_messages']);
                    break;
                }
            }

            $I->assertTrue($found, 'Thread not found');
        });

        // Get messages in the thread
        $I->callApiPortalDeviceMessagesInThread(threadId: $thread['id']);
        $I->assertNotEmpty($I->grabDataFromResponseByJsonPath('$.items.*'));

        // See that portal thread now has no unread messages
        $I->callApiPortalDeviceMessageThreadList();
        $currentThreads = $I->grabDataFromResponseByJsonPath('$.items.*');
        $found = false;

        foreach ($currentThreads as $currentThread) {
            if ($currentThread['id'] === $thread['id']) {
                $found = true;
                $I->assertFalse($currentThread['has_unread_messages']);
                break;
            }
        }

        $I->assertTrue($found, 'Thread not found');
    }

    public function canReceiveMessageFromVirSystem(ApiTester $I): void
    {
        $equipmentExternalId = 'vir-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-plate-'.uniqid();
        $from = 'vir-system';
        $message = 'Initial VIR message '.uniqid();
        $staffExtId = 'random-staff-'.uniqid();
        $deviceId = 'random-device-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: $equipmentExternalId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExtId,
            equipmentExtId2: 'random-equipment-'.uniqid(),
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$equipmentLicensePlate}')]")[0];
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );

        // 1. Thread is started from the external VIR system
        $I->callGermanyVirMessage(json_encode([
            'equipmentExternalId' => $equipmentExternalId,
            'from' => $from,
            'message' => $message,
        ]));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDeviceMessageThreadList();
        $thread = $I->grabDataFromResponseByJsonPath('$.items[?(@.last_message_excerpt == "'.$message.'")]')[0];
        $I->assertNotEmpty($thread, 'Thread not found');
        $I->assertTrue($thread['has_unread_messages']);
        $I->assertEquals('equipment', $thread['target']);
        $I->assertStringContainsString($equipmentExternalId, $thread['target_identifier']);

        $I->callApiPortalDeviceMessagesInThread($thread['id']);
        $portalMessage = $I->grabDataFromResponseByJsonPath('$.items[?(@.message == "'.$message.'")]')[0];
        $I->assertNotEmpty($portalMessage, 'Message not found in portal');
        $I->assertEquals($from, $portalMessage['username']);

        // Hardcoded ID for branch Oelbronn from fixtures, which is used for the current user (see germany tour fixtures)
        $branchId = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

        // Validate centrifugo has received the message for branch
        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'branch:'.bin2hex($branchId),
            messageParts: [
                'thread_id' => $thread['id'],
                'message' => $message,
                'type' => 'device_message',
            ],
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);

        $I->callApiDeviceMessagesV2();
        $messagesReceivedFromDevice = $I->grabDataFromResponseByJsonPath('$.items[?(@.id == "'.$thread['id'].'")].messages')[0];
        $I->assertCount(1, $messagesReceivedFromDevice);
        $I->assertFalse($messagesReceivedFromDevice[0]['is_read']);
        $I->assertEquals($message, $messagesReceivedFromDevice[0]['message']);

        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'device:channel#'.bin2hex($deviceId),
            messageParts: [
                'thread_id' => $thread['id'],
                'message' => $message,
                'type' => 'device_message',
            ],
        );

        // 2. Message is sent from the VIR system to an existing message thread
        $replyMessage = 'VIR reply message '.uniqid();
        $I->callGermanyVirMessage(json_encode([
            'equipmentExternalId' => $equipmentExternalId,
            'from' => $from,
            'message' => $replyMessage,
        ]));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDeviceMessageThreadList();
        $thread = $I->grabDataFromResponseByJsonPath('$.items[?(@.id == "'.$thread['id'].'")]')[0];
        $I->assertTrue($thread['has_unread_messages']);
        $I->assertEquals($replyMessage, $thread['last_message_excerpt']);

        $I->callApiPortalDeviceMessagesInThread($thread['id']);
        $portalReplyMessage = $I->grabDataFromResponseByJsonPath('$.items[?(@.message == "'.$replyMessage.'")]')[0];
        $I->assertNotEmpty($portalReplyMessage, 'Reply message not found in portal');
        $I->assertEquals($from, $portalReplyMessage['username']);

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $this->tenant);
        $I->callApiDeviceMessagesV2();
        $messagesReceivedFromDevice = $I->grabDataFromResponseByJsonPath('$.items[?(@.id == "'.$thread['id'].'")].messages')[0];
        $I->assertCount(2, $messagesReceivedFromDevice);
        $I->assertFalse($messagesReceivedFromDevice[1]['is_read']);
        $I->assertEquals($replyMessage, $messagesReceivedFromDevice[1]['message']);

        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'device:channel#'.bin2hex($deviceId),
            messageParts: [
                'thread_id' => $thread['id'],
                'message' => $replyMessage,
                'type' => 'device_message',
            ],
        );
    }

    #[Depends('canReceiveMessageFromVirSystem')]
    public function unreadMessageCounterIsUpdatedForDeviceMessages(ApiTester $I): void
    {
        $staffExtId = 'unread-counter-staff-'.uniqid();
        $deviceId = 'unread-counter-device-'.uniqid();
        $branchExtId = 'unread-counter-branch-'.uniqid();

        // Create a tour and staff for testing
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'unread-counter-tour-'.uniqid(),
            orderExtId: 'unread-counter-order-'.uniqid(),
            tourName: 'unread-counter-tour-name-'.uniqid(),
            equipmentExtId: 'unread-counter-equipment-'.uniqid(),
            equipmentLicensePlate: 'unread-counter-plate-'.uniqid(),
            staffExternalId: $staffExtId,
            branchExternalId: $branchExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $message = 'Unread counter test message '.uniqid();

        // Send a message from device (should trigger unread counter)
        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'message' => $message,
                'attachments' => [],
            ]),
        );

        $branchId = $this->getBranchId($I, $branchExtId);

        // Validate centrifugo received the unread count notification for branch
        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'branch:'.bin2hex($branchId),
            messageParts: [
                'unread_count' => 1,
                'type' => 'unread_message_count',
            ],
        );

        // Validate centrifugo received the unread count notification for country (Germany = 'de')
        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'country:'.bin2hex('de'),
            messageParts: [
                'type' => 'unread_message_count',
            ],
        );
    }

    #[Depends('unreadMessageCounterIsUpdatedForDeviceMessages')]
    public function unreadMessageCounterIsNotUpdatedForDispatcherMessages(ApiTester $I): void
    {
        $staffExtId = 'dispatcher-test-staff-'.uniqid();
        $branchExtId = 'dispatcher-test-branch-'.uniqid();

        // Create a tour and staff for testing
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'dispatcher-test-tour-'.uniqid(),
            orderExtId: 'dispatcher-test-order-'.uniqid(),
            tourName: 'dispatcher-test-tour-name-'.uniqid(),
            equipmentExtId: 'dispatcher-test-equipment-'.uniqid(),
            equipmentLicensePlate: 'dispatcher-test-plate-'.uniqid(),
            staffExternalId: $staffExtId,
            branchExternalId: $branchExtId,
        ));

        // Get staff ID for portal message
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalStaffList(query: $staffExtId);
        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $staffId = $data['items'][0]['staff_id'];

        $branchId = $this->getBranchId($I, $branchExtId);

        // Get the current unread count before sending dispatcher message
        $I->clearCentrifugoMessages();

        // Send a message from dispatcher (should NOT increase unread counter)
        $I->callApiPortalDeviceMessageCreate(json_encode([
            'recipient' => [
                'kind' => RecipientKind::STAFF->value,
                'staff_id' => $staffId,
            ],
            'message' => 'Dispatcher message '.uniqid(),
        ]));

        // Verify that NO unread count notification was sent for dispatcher messages
        // Since dispatcher messages don't create unread threads for branch,
        // no unread count notification should be sent at all
        $I->assertCentrifugoDidNotReceiveChannelMessage(
            channel: 'branch:'.bin2hex($branchId),
            messageParts: [
                'type' => 'unread_message_count',
            ],
        );
    }

    #[Depends('unreadMessageCounterIsNotUpdatedForDispatcherMessages')]
    public function unreadMessageCounterIsDecrementedWhenMarkedAsRead(ApiTester $I): void
    {
        $staffExtId = 'read-test-staff-'.uniqid();
        $deviceId = 'read-test-device-'.uniqid();
        $branchExtId = 'read-test-branch-'.uniqid();

        // Create a tour and staff for testing
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'read-test-tour-'.uniqid(),
            orderExtId: 'read-test-order-'.uniqid(),
            tourName: 'read-test-tour-name-'.uniqid(),
            equipmentExtId: 'read-test-equipment-'.uniqid(),
            equipmentLicensePlate: 'read-test-plate-'.uniqid(),
            staffExternalId: $staffExtId,
            branchExternalId: $branchExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $message = 'Read test message '.uniqid();

        // Send a message from device (should trigger unread counter)
        $I->callApiDeviceMessageCreateV2(
            data: json_encode([
                'message' => $message,
                'attachments' => [],
            ]),
        );

        $threadId = $I->grabDataFromResponseByJsonPath('$.thread_id')[0];
        $branchId = $this->getBranchId($I, $branchExtId);

        // Clear centrifugo messages before checking read status
        $I->clearCentrifugoMessages();

        // Mark the thread as read from portal
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDeviceMessagesInThread($threadId);

        // Validate centrifugo received the updated unread count notification (should be 0 now)
        $I->assertCentrifugoReceivedChannelMessage(
            channel: 'branch:'.bin2hex($branchId),
            messageParts: [
                'unread_count' => 0,
                'type' => 'unread_message_count',
            ],
        );
    }

    private function getBranchId(ApiTester $I, string $branchExtId): string
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchListAccessible(externalId: $branchExtId);
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertIsArray($data);
        $I->assertCount(1, $data);

        return $data[0]['branch_id'];
    }
}
