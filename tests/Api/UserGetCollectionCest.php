<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class UserGetCollectionCest
{
    public function cannotGetUserCollectionIfNotLoggedIn(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserGetCollectionV2(expectedStatusCode: 401);
    }
}
