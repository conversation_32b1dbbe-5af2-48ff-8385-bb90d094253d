<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\Api\NoteFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class OrderNoteCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';

    public function canNoteOrder(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();

        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiTourGetV2($sapTourId);
        $orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
        $noteTemplate = $I->grabDataFromResponseByJsonPath('$.orders[0].note_templates')[0];

        $I->callApiOrderNoteV2(
            orderId: $orderId,
            data: NoteFixtures::noteOrderRequestV2($noteTemplate[0]),
        );

        $I->waitUntil(function (ApiTester $I) use ($orderId): void {
            $I->callApiOrderNoteHistoryV2($orderId);
            $notes = $I->grabDataFromResponseByJsonPath('$.items');
            $I->assertCount(1, $notes[0]);
        });
    }
}
