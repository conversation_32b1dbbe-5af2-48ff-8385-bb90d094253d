<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class FaqCest
{
    private string $tenant = 'pz';

    private string $staffExtId = '';

    public function missingLanguageIsoReturnsEmptyFaqListV2(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();

        $this->staffExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiFaqGetLanguageV2('xx_XX');

        $data = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertCount(0, $data['questions']);
    }

    public function cannotGetFaqListIfNotAuthenticatedV2(ApiTester $I): void
    {
        $I->callApiFaqGetLanguageV2('de_DE', expectedStatusCode: 401);
    }

    public function cannotGetFaqInformationIfNotAuthenticatedV2(ApiTester $I): void
    {
        $I->callApiFaqGetInformationV2(expectedStatusCode: 401);
    }
}
