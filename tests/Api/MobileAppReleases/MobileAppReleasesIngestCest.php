<?php

declare(strict_types=1);

namespace App\Tests\Api\MobileAppReleases;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\S3Helper;
use App\Tests\Unit\Infrastructure\MobileAppReleasesApi\Service\ReleaseNoteProcessorTest;
use Codeception\Attribute\Depends;

readonly class MobileAppReleasesIngestCest
{
    public function canStoreMobileAppRelease(ApiTester $I): void
    {
        $filePath = '/mobile-app-releases/1.2.3.apk';
        $binary = random_bytes(1024);

        S3Helper::getS3Client()->putObject([
            'Bucket' => getenv('S3_BUCKET_NAME'),
            'Key' => ltrim($filePath, '/'),
            'Body' => $binary,
            'ContentType' => 'application/vnd.android.package-archive',
            'Metadata' => [],
        ]);

        $I->callApiMobileAppReleases(json_encode([
            'version' => '1.2.3',
            'releaseNotes' => ReleaseNoteProcessorTest::getSampleReleaseNotes(),
            'type' => 'hermes_app',
            'files' => [
                [
                    'path' => $filePath,
                    'type' => 'apk',
                ],
            ],
        ]));
    }

    public function cannotStoreInvalidAppVersion(ApiTester $I): void
    {
        $filePath = '/mobile-app-releases/1.2.3.apk';
        $binary = random_bytes(1024);

        S3Helper::getS3Client()->putObject([
            'Bucket' => getenv('S3_BUCKET_NAME'),
            'Key' => ltrim($filePath, '/'),
            'Body' => $binary,
            'ContentType' => 'application/vnd.android.package-archive',
            'Metadata' => [],
        ]);

        $I->callApiMobileAppReleases(json_encode([
            'version' => '1-non-sem-version',
            'releaseNotes' => ReleaseNoteProcessorTest::getSampleReleaseNotes(),
            'type' => 'hermes_app',
            'files' => [
                [
                    'path' => $filePath,
                    'type' => 'apk',
                ],
            ],
        ]), 422);
        $I->canSeeResponseContains('Invalid semantic version: 1-non-sem-version');
    }

    public function cannotStoreInvalidFilePath(ApiTester $I): void
    {
        $I->callApiMobileAppReleases(json_encode([
            'version' => '1.2.3',
            'releaseNotes' => ReleaseNoteProcessorTest::getSampleReleaseNotes(),
            'type' => 'hermes_app',
            'files' => [
                [
                    'path' => 'invalid-file-path',
                    'type' => 'apk',
                ],
            ],
        ]), 422);
        $I->canSeeResponseContains('Invalid file path. Must start with \/mobile-app-releases\/');
    }

    public function cannotStoreNonExistingFile(ApiTester $I): void
    {
        $I->callApiMobileAppReleases(json_encode([
            'version' => '1.3.5',
            'releaseNotes' => ReleaseNoteProcessorTest::getSampleReleaseNotes(),
            'type' => 'hermes_app',
            'files' => [
                [
                    'path' => '/mobile-app-releases/non-existing-file.apk',
                    'type' => 'apk',
                ],
            ],
        ]), 422);
        $I->canSeeResponseContains('File not found');
    }

    #[Depends('canStoreMobileAppRelease')]
    public function cannotCreateDuplicateAppVersion(ApiTester $I): void
    {
        $filePath = '/mobile-app-releases/1.2.3.apk';
        $binary = random_bytes(1024);

        S3Helper::getS3Client()->putObject([
            'Bucket' => getenv('S3_BUCKET_NAME'),
            'Key' => ltrim($filePath, '/'),
            'Body' => $binary,
            'ContentType' => 'application/vnd.android.package-archive',
            'Metadata' => [],
        ]);

        $I->callApiMobileAppReleases(json_encode([
            'version' => '1.2.3',
            'releaseNotes' => ReleaseNoteProcessorTest::getSampleReleaseNotes(),
            'type' => 'hermes_app',
            'files' => [
                [
                    'path' => $filePath,
                    'type' => 'apk',
                ],
            ],
        ]), 422);
        $I->canSeeResponseContains('Version already exists: 1.2.3');
    }
}
