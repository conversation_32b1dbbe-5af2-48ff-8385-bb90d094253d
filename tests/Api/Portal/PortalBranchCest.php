<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalBranchCest
{
    private string $country = 'de';

    public function canGetPortalBranches(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchList($this->country);
    }

    public function cannotGetPortalBranchesIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalBranchList($this->country, expectedStatusCode: 401);
    }

    public function userCanGetAccessibleBranches(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $branchIds = $I->grabDataFromResponseByJsonPath('$.items.*.external_id');
        $I->assertIsArray($branchIds);
        $I->assertNotEmpty($branchIds);
        $search = strtoupper(substr($branchIds[0], 0, -1));

        $I->callApiPortalBranchListAccessible($search);
        $I->assertIsArray($branchIds);
        $I->assertNotEmpty($branchIds);
    }

    public function adminCanGetAccessibleBranches(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();
    }
}
