<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalCountryCest
{
    public function canGetPortalCountries(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalCountryList();
        $I->seeResponseMatchesJsonType([
            'items' => 'array:!empty',
        ]);
        $countries = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($countries as $country) {
            $I->assertIsString($country);
        }
    }

    public function cannotGetPortalCountriesIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalCountryList(expectedStatusCode: 401);
    }
}
