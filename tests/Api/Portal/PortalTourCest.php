<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Enum\WaypointColor;
use App\Tests\Support\Api\SampleGermanyTour;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Faker\Factory;

class PortalTourCest
{
    protected const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';
    protected int $distance = 0;
    protected string $tenant = 'pz';
    protected ?string $mastertourTemplateId = null;
    protected array $mastertourTemplateExternalIds = [];
    protected array $waypointIds = [];
    protected string $sapTourId = '';
    protected string $tourExtId = '';
    protected string $orderExtId = '';
    protected string $tourName = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';
    protected string $mastertourExternalId = '';

    public function canSearchTours(ApiTester $I): void
    {
        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-1-id-'.uniqid();
        $this->orderExtId = 'random-order-1-id-'.uniqid();
        $this->tourName = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $staffExtId,
        ));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalTourList(date('Y-m-d'), $this->tourExtId);

        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertEquals($this->tourExtId, $data[0]['external_id']);
        $I->assertEquals($data[0]['status_label'], sprintf('{{tour/status/%s}}', $data[0]['status']));
    }

    public function listTourLabelsByDate(ApiTester $I): void
    {
        // Create 3 tours with different equipment configurations:

        // 1. Without equipment
        $tourWithoutEquipmentExternalId = uniqid();
        $tourId1 = $I->callSapGermanyTourUpsert(SampleGermanyTour::tourWithoutEquipment($tourWithoutEquipmentExternalId));

        // 2. With equipment of truck type
        $tourWithTruckExternalId = uniqid();
        $equipmentLicensePlate1 = 'random-license-plate-'.uniqid();
        $tourId2 = $I->callSapGermanyTourUpsert(SampleGermanyTour::tourWithEquipmentType(
            tourExternalId: $tourWithTruckExternalId,
            equipmentType: EquipmentType::ASK->value,
            equipmentLicensePlate: $equipmentLicensePlate1,
        ));

        // 3. With equipment of trailer type
        $tourWithTrailerExternalId = uniqid();
        $equipmentLicensePlate2 = 'random-license-plate-'.uniqid();
        $tourId3 = $I->callSapGermanyTourUpsert(SampleGermanyTour::tourWithEquipmentType(
            tourExternalId: $tourWithTrailerExternalId,
            equipmentType: EquipmentType::ANH->value,
            equipmentLicensePlate: $equipmentLicensePlate2,
        ));

        // Get tour labels by date and verify the labels
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalTourLabelsByDate(date: date('Y-m-d'), hasOrderFiles: '0');

        $tour1Response = $I->grabDataFromResponseByJsonPath('$.items[?(@.tour_id == "'.$tourId1.'")]')[0];
        $tour2Response = $I->grabDataFromResponseByJsonPath('$.items[?(@.tour_id == "'.$tourId2.'")]')[0];
        $tour3Response = $I->grabDataFromResponseByJsonPath('$.items[?(@.tour_id == "'.$tourId3.'")]')[0];

        $I->assertEquals($tourWithoutEquipmentExternalId, $tour1Response['selector_label']);
        $I->assertEquals($tourWithTruckExternalId.' - '.$equipmentLicensePlate1, $tour2Response['selector_label']);
        $I->assertEquals($tourWithTrailerExternalId, $tour3Response['selector_label']);
    }

    public function cannotSearchToursWithInvalidDate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalTourList('invalid-date', expectedStatusCode: 422);
    }

    public function cannotSearchToursNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalTourList(date('Y-m-d'), expectedStatusCode: 401);
    }

    public function canSearchToursWithInsensitiveExternalId(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalTourList(date('Y-m-d'), ucfirst($this->tourExtId));

        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertEquals($this->tourExtId, $data[0]['external_id']);
    }

    public function canPatchMastertoursToTour(ApiTester $I): void
    {
        $this->mastertourExternalId = $this->createMastertour($I);
        $deviceId = 'random-device-id-'.uniqid();
        $I->generateStaffAndAuthenticate($this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiMastertourGetV2($this->mastertourExternalId);
        $response = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertEquals($this->mastertourExternalId, $response['external_id']);
        $I->assertCount(100, $response['waypoints']);
        $this->mastertourTemplateId = $response['id'];

        foreach ($response['waypoints'] as $key => $waypoint) {
            $this->mastertourTemplateExternalIds[$key] = $this->mastertourExternalId;
            $this->waypointIds[$key] = (string) $response['waypoints'][$key]['id'];
        }

        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-1-id-'.uniqid();
        $this->orderExtId = 'random-order-1-id-'.uniqid();
        $this->tourName = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            mastertourExternalIds: [$this->mastertourTemplateExternalIds[array_key_first($this->mastertourTemplateExternalIds)]],
            staffExternalId: $staffExtId
        ));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalTourDetails($this->sapTourId);

        $data = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertEquals($this->tourExtId, $data['external_id']);
        $I->assertIsArray($data['mastertours']);
        $I->assertContains($this->mastertourExternalId, array_column($data['mastertours'], 'external_id'));

        $mastertoursArray = [$this->mastertourExternalId];
        $data = json_encode([
            'mastertours' => $mastertoursArray,
        ]);

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalTourMastertours($this->sapTourId, $data);

        $I->callApiPortalTourDetails($this->sapTourId);

        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertIsArray($data['mastertours']);
        $I->assertEquals($mastertoursArray, array_column($data['mastertours'], 'external_id'));

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            mastertourExternalIds: [$this->mastertourTemplateExternalIds[array_key_first($this->mastertourTemplateExternalIds)]],
            staffExternalId: $staffExtId
        ));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalTourDetails($this->sapTourId);

        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertIsArray($data['mastertours']);
        $I->assertEquals($mastertoursArray, array_column($data['mastertours'], 'external_id'));
    }

    private function createMastertour(ApiTester $I): string
    {
        $externalId = uniqid();
        $name = md5((string) rand(0, 100));
        $branchId = self::BRANCH_ID;

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $data = $this->getMastertourTemplateCreateJson($branchId, $externalId, $name);
        $I->callApiPortalMastertourTemplateCreate($data);
        $responseItems = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals($this->distance, $responseItems['distance_in_meters']);

        return $externalId;
    }

    private function getMastertourTemplateCreateJson(string $branchId, string $externalId, string $name): string
    {
        $faker = Factory::create();

        $ret = [
            'branch_id' => $branchId,
            'external_id' => $externalId,
            'name' => $name,
            'waypoints' => [],
        ];

        for ($i = 0; $i < 100; ++$i) {
            $distanceToPreviousWaypointMeter = $faker->randomNumber(2, true);
            $this->distance = $this->distance + $distanceToPreviousWaypointMeter;
            $ret['waypoints'][] = [
                'longitude' => $faker->latitude(),
                'latitude' => $faker->longitude(),
                'distance_to_previous_waypoint_meters' => $distanceToPreviousWaypointMeter,
                'bearing' => $faker->numberBetween(0, 360),
                'description' => '{{'.$faker->word().'}}',
                'text_to_speech' => $faker->boolean(),
                'color' => $faker->randomElement(WaypointColor::class)->value,
            ];
        }

        return json_encode($ret);
    }
}
