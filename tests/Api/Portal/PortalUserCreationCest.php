<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class PortalUserCreationCest
{
    private string $username;
    private string $email;

    private const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

    public function canCreateUserAsPortalAdmin(ApiTester $I): void
    {
        $this->username = 'P-U-'.uniqid();
        $this->email = $this->username.'@test.com';

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getUserCreateJson($this->username, $this->email);

        $I->callApiPortalUserCreate($data);
    }

    #[Depends('canCreateUserAsPortalAdmin')]
    public function canFindCreatedUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalUserList();
        $users = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $found = false;
        foreach ($users as $user) {
            if ($user['username'] === strtolower($this->username)) {
                $found = true;
                break;
            }
        }
        $I->assertTrue($found);
    }

    #[Depends('canFindCreatedUser')]
    public function canNotCreateUnaccessedCountry(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $this->username = 'P-U-'.uniqid();
        $this->email = $this->username.'@test.com';
        $data = $this->getUserCreateJson(
            username: $this->username,
            email: $this->email,
            country: 'lu',
            groups: ['PORTAL_USER'],
            branchAccess: [self::BRANCH_ID],
            countryAccess: [],
        );

        $I->callApiPortalUserCreate($data, 403);
    }

    private function getUserCreateJson(
        string $username,
        string $email,
        string $country = 'de',
        array $groups = ['COUNTRY_ADMIN'],
        array $branchAccess = [],
        array $countryAccess = ['at', 'de'],
    ): string {
        return json_encode(
            [
                'username' => $username,
                'first_name' => 'fname',
                'last_name' => 'lname',
                'email' => $email,
                'country' => $country,
                'groups' => $groups,
                'branch_access' => $branchAccess,
                'country_access' => $countryAccess,
            ]);
    }
}
