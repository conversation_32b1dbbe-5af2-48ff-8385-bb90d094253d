<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalUserUpdateCest
{
    private string $username;
    private string $email;

    private const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

    public function canUpdateUserAsPortalAdmin(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $this->username = 'rand-portal-user'.uniqid();
        $this->email = 'rand-portal-user'.uniqid().'@test.com';

        $data = $this->getUserCreateJson(
            $this->username,
            $this->email,
            $firstName,
            $lastName,
        );

        $I->callApiPortalUserCreate($data);

        $userId = $I->grabDataFromResponseByJsonPath('$.user_id')[0];

        $firstName = 'first-name-c-'.uniqid();
        $lastName = 'last-name-c-'.uniqid();
        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $this->email,
            groups: ['COUNTRY_ADMIN'],
            branchAccess: [],
            countryAccess: ['de'],
        );

        $I->callApiPortalUserUpdate($userId, $data);

        $userResponse = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertNotEmpty($userResponse);
        $I->assertArrayHasKey('user_id', $userResponse);
        $userId = $userResponse['user_id'];

        $I->assertEquals($firstName, $userResponse['first_name']);
        $I->assertEquals($lastName, $userResponse['last_name']);

        $I->callApiPortalUserList();

        $users = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $found = false;

        foreach ($users as $user) {
            if ($user['user_id'] == $userId) {
                $found = true;
                $I->assertEquals($firstName, $user['first_name']);
                $I->assertEquals($lastName, $user['last_name']);
                break;
            }
        }

        $I->assertTrue($found);
    }

    public function canUpdateOwnAccountAsPortalAdmin(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $this->username = 'P-U-'.uniqid();
        $this->email = $this->username.'@test.com';

        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $this->email,
            groups: ['PORTAL_ADMIN'],
            branchAccess: [],
            countryAccess: ['de'],
        );

        $I->callApiPortalUserUpdate('8f93452f-c58b-4720-85a5-1a97db5f88bb', $data);
    }

    public function cannotUpdateUserWithBranchOutsideOfCountryAccess(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $email = '<EMAIL>';

        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $email,
            groups: ['PORTAL_USER'],
            branchAccess: ['c42db2fb-01e0-40e6-b7ea-dc1203bd33c8', '58ded041-e2d5-44f4-adcd-b7c165de1cb9'],
            countryAccess: [],
        );

        $I->callApiPortalUserUpdate('ddba0342-cb56-4f81-b2d5-56d979118df5', $data);

        $I->callApiPortalUserGet('ddba0342-cb56-4f81-b2d5-56d979118df5');

        $userResponse = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertContains('c42db2fb-01e0-40e6-b7ea-dc1203bd33c8', $userResponse['branch_access']);
        $I->assertNotContains('58ded041-e2d5-44f4-adcd-b7c165de1cb9', $userResponse['branch_access']);
    }

    public function canNotUpdateOtherPortalAdmin(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $this->username = 'P-U-'.uniqid();
        $this->email = $this->username.'@test.com';

        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $this->email,
            groups: ['PORTAL_ADMIN'],
            branchAccess: [],
            countryAccess: ['de'],
        );

        $I->callApiPortalUserUpdate('a971a45c-a5f3-4369-bb07-63a289d4d3ab', $data, 403);
    }

    public function canNotChangePortalAdminGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $this->username = 'P-U-'.uniqid();
        $this->email = $this->username.'@test.com';

        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $this->email,
            groups: ['COUNTRY_ADMIN'],
            branchAccess: [],
            countryAccess: ['de'],
        );

        $I->callApiPortalUserUpdate('8f93452f-c58b-4720-85a5-1a97db5f88bb', $data, 200);

        $I->callApiPortalUserGet('8f93452f-c58b-4720-85a5-1a97db5f88bb');
        $userResponse = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertContains('PORTAL_ADMIN', $userResponse['groups']);
    }

    public function canNotUpdateOwnAccountWithExistingEmail(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();

        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $this->email,
            groups: ['PORTAL_ADMIN'],
            branchAccess: [],
            countryAccess: ['de'],
        );

        $I->callApiPortalUserUpdate('8f93452f-c58b-4720-85a5-1a97db5f88bb', $data, expectedStatusCode: 400);

        $I->callApiPortalUserGet('8f93452f-c58b-4720-85a5-1a97db5f88bb');
        $userResponse = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertContains('PORTAL_ADMIN', $userResponse['groups']);
    }

    public function canNotUpdateUnaccessedCountry(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $this->username = 'rand-portal-user'.uniqid();
        $this->email = 'rand-portal-user'.uniqid().'@test.com';

        $data = $this->getUserCreateJson(
            username: $this->username,
            email: $this->email,
            fname: $firstName,
            lname: $lastName,
            country: 'de',
            branchAccess: [self::BRANCH_ID],
            countryAccess: [],
            groups: ['PORTAL_USER'],
        );

        $I->callApiPortalUserCreate($data);
        $userId = $I->grabDataFromResponseByJsonPath('$.user_id')[0];

        $firstName = 'first-name-c-'.uniqid();
        $lastName = 'last-name-c-'.uniqid();
        $data = $this->getUserUpdateJson(
            firstName: $firstName,
            lastName: $lastName,
            email: $this->email,
            groups: ['PORTAL_USER'],
            branchAccess: [self::BRANCH_ID],
            countryAccess: [],
            country: 'lu',
        );

        $I->callApiPortalUserUpdate($userId, $data, 403);
    }

    private function getUserUpdateJson(
        string $firstName,
        string $lastName,
        string $email,
        array $groups,
        array $branchAccess,
        array $countryAccess,
        string $country = 'de',
    ): string {
        $ret = [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $email,
            'country' => $country,
            'groups' => $groups,
            'branch_access' => $branchAccess,
            'country_access' => $countryAccess,
        ];

        return json_encode($ret);
    }

    private function getUserCreateJson(
        string $username,
        string $email,
        string $fname,
        string $lname,
        string $country = 'de',
        array $branchAccess = [],
        array $countryAccess = ['de'],
        array $groups = ['COUNTRY_ADMIN'],
    ): string {
        return json_encode(
            [
                'username' => $username,
                'first_name' => $fname,
                'last_name' => $lname,
                'email' => $email,
                'country' => $country,
                'groups' => $groups,
                'branch_access' => $branchAccess,
                'country_access' => $countryAccess,
            ]);
    }
}
