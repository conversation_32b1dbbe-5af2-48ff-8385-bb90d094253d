<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalPointOfInterestCest
{
    public function canGetPortalPointOfInterestList(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalPointOfInterestList();

        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');

        foreach ($responseData as $poi) {
            $I->assertArrayHasKey('name', $poi, 'Name is missing');
            $I->assertArrayHasKey('longitude', $poi, 'longitude is missing');
            $I->assertArrayHasKey('latitude', $poi, 'latitude is missing');
            $I->assertArrayHasKey('icon', $poi, 'icon is missing');
        }
    }

    public function cannotGetPortalPointOfInterestIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalPointOfInterestList(expectedStatusCode: 401);
    }

    /**
     * @throws \Throwable
     */
    public function canPostPortalPointOfInterest(ApiTester $I): void
    {
        $name = md5((string) rand(0, 100));
        $longitude = (float) rand(0, 50) / 10;
        $latitude = (float) rand(0, 50) / 10;
        $icon = md5((string) rand(0, 50));

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getPortalPointOfInterestRequestJson($name, $longitude, $latitude, $icon);
        $I->callApiPortalPointOfInterestCreate($data);
        $poi = $I->grabDataFromResponseByJsonPath('$')[0];
        assert(is_array($poi));
        $I->assertContains($name, $poi);
        $I->assertContains($longitude, $poi);
        $I->assertContains($latitude, $poi);
        $I->assertContains($icon, $poi);
    }

    private function getPortalPointOfInterestRequestJson(string $name, float $longitude, float $latitude, string $icon): string
    {
        return <<<JSON
        {
            "name": "{$name}",
            "longitude": {$longitude},
            "latitude": {$latitude},
            "icon": "{$icon}"
        }
        JSON;
    }
}
