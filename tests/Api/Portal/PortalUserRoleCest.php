<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalUserRoleCest
{
    private const array PORTAL_USER_ROLES = [
        [
            'user' => 'portal-user-de',
            'allowed' => [
                'ROLE_PORTAL_VIEW_FILES',
                'ROLE_PORTAL_CHANGE_PROFILE',
                'ROLE_PORTAL_VIEW_DASHBOARD',
                'ROLE_PORTAL_VIEW_STAFF',
                'ROLE_PORTAL_VIEW_EQUIPMENT',
                'ROLE_PORTAL_VIEW_BRANCH_DATA',
                'ROLE_PORTAL_ACCESS',
            ],
            'forbidden' => [
                'ROLE_PORTAL_MANAGE_COUNTRY_ADMINS',
                'ROLE_PORTAL_MANAGE_SUPPORT_USERS',
                'ROLE_PORTAL_MANAGE_PORTAL_USERS',
                'ROLE_PORTAL_MANAGE_DEVICE_ACCESS',
                'ROL<PERSON>_PORTAL_MANAGE_FAQ',
                'R<PERSON><PERSON>_PORTAL_MANAGE_FAQ_ADMINS',
            ],
            'groups' => ['PORTAL_USER'],
        ],
        [
            'user' => 'portal-admin-de',
            'allowed' => [
                'ROLE_PORTAL_VIEW_DASHBOARD',
                'ROLE_PORTAL_VIEW_STAFF',
                'ROLE_PORTAL_MANAGE_COUNTRY_ADMINS',
                'ROLE_PORTAL_MANAGE_SUPPORT_USERS',
                'ROLE_PORTAL_MANAGE_PORTAL_USERS',
                'ROLE_PORTAL_MANAGE_DEVICE_ACCESS',
                'ROLE_PORTAL_VIEW_FILES',
                'ROLE_PORTAL_VIEW_COUNTRY_DATA',
                'ROLE_PORTAL_CHANGE_PROFILE',
                'ROLE_PORTAL_MANAGE_FAQ',
                'ROLE_PORTAL_VIEW_EQUIPMENT',
                'ROLE_PORTAL_ACCESS',
                'ROLE_PORTAL_MANAGE_FAQ_ADMINS',
            ],
            'forbidden' => [
                'ROLE_PORTAL_VIEW_BRANCH_DATA',
            ],
            'groups' => ['PORTAL_ADMIN'],
        ],
        [
            'user' => 'portal-country-admin-de',
            'allowed' => [
                'ROLE_PORTAL_VIEW_DASHBOARD',
                'ROLE_PORTAL_VIEW_STAFF',
                'ROLE_PORTAL_MANAGE_SUPPORT_USERS',
                'ROLE_PORTAL_MANAGE_PORTAL_USERS',
                'ROLE_PORTAL_MANAGE_DEVICE_ACCESS',
                'ROLE_PORTAL_VIEW_FILES',
                'ROLE_PORTAL_VIEW_COUNTRY_DATA',
                'ROLE_PORTAL_CHANGE_PROFILE',
                'ROLE_PORTAL_VIEW_EQUIPMENT',
                'ROLE_PORTAL_ACCESS',
                'ROLE_PORTAL_MANAGE_FAQ',
            ],
            'forbidden' => [
                'ROLE_PORTAL_VIEW_BRANCH_DATA',
                'ROLE_PORTAL_MANAGE_COUNTRY_ADMINS',
                'ROLE_PORTAL_MANAGE_FAQ_ADMINS',
            ],
            'groups' => ['COUNTRY_ADMIN'],
        ],
        [
            'user' => 'portal-support-de',
            'allowed' => [
                'ROLE_PORTAL_VIEW_FILES',
                'ROLE_PORTAL_CHANGE_PROFILE',
                'ROLE_PORTAL_VIEW_DASHBOARD',
                'ROLE_PORTAL_VIEW_STAFF',
                'ROLE_PORTAL_VIEW_EQUIPMENT',
                'ROLE_PORTAL_MANAGE_DEVICE_ACCESS',
                'ROLE_PORTAL_VIEW_BRANCH_DATA',
                'ROLE_PORTAL_ACCESS',
            ],
            'forbidden' => [
                'ROLE_PORTAL_VIEW_COUNTRY_DATA',
                'ROLE_PORTAL_MANAGE_FAQ',
                'ROLE_PORTAL_MANAGE_COUNTRY_ADMINS',
                'ROLE_PORTAL_MANAGE_SUPPORT_USERS',
                'ROLE_PORTAL_MANAGE_PORTAL_USERS',
                'ROLE_PORTAL_MANAGE_FAQ_ADMINS',
                'ROLE_PORTAL_MANAGE_FAQ_ADMINS',
            ],
            'groups' => ['SUPPORT_USER'],
        ],
        [
            'user' => 'portal-faq-admin-de',
            'allowed' => [
                'ROLE_PORTAL_CHANGE_PROFILE',
                'ROLE_PORTAL_MANAGE_FAQ',
                'ROLE_PORTAL_ACCESS',
            ],
            'forbidden' => [
                'ROLE_PORTAL_VIEW_DASHBOARD',
                'ROLE_PORTAL_VIEW_STAFF',
                'ROLE_PORTAL_MANAGE_COUNTRY_ADMINS',
                'ROLE_PORTAL_MANAGE_SUPPORT_USERS',
                'ROLE_PORTAL_MANAGE_PORTAL_USERS',
                'ROLE_PORTAL_MANAGE_DEVICE_ACCESS',
                'ROLE_PORTAL_VIEW_FILES',
                'ROLE_PORTAL_VIEW_COUNTRY_DATA',
                'ROLE_PORTAL_VIEW_BRANCH_DATA',
                'ROLE_PORTAL_VIEW_EQUIPMENT',
                'ROLE_PORTAL_MANAGE_FAQ_ADMINS',
            ],
            'groups' => ['FAQ_ADMIN'],
        ],
    ];

    private const array PORTAL_USER_DELEGATE_GROUPS = [
        'portal-user-de' => [
            'allowed' => [],
            'forbidden' => [
                'PORTAL_USER',
                'FAQ_ADMIN',
                'SUPPORT_USER',
                'COUNTRY_ADMIN',
            ],
        ],
        'portal-admin-de' => [
            'allowed' => [
                'PORTAL_USER',
                'FAQ_ADMIN',
                'SUPPORT_USER',
                'COUNTRY_ADMIN',
            ],
            'forbidden' => [],
        ],
        'portal-country-admin-de' => [
            'allowed' => [
                'PORTAL_USER',
                'SUPPORT_USER',
            ],
            'forbidden' => [
                'FAQ_ADMIN',
                'COUNTRY_ADMIN',
            ],
        ],
        'portal-support-de' => [
            'allowed' => [],
            'forbidden' => [
                'PORTAL_USER',
                'FAQ_ADMIN',
                'SUPPORT_USER',
                'COUNTRY_ADMIN',
            ],
        ],
        'portal-faq-admin-de' => [
            'allowed' => [],
            'forbidden' => [
                'PORTAL_USER',
                'FAQ_ADMIN',
                'SUPPORT_USER',
                'COUNTRY_ADMIN',
            ],
        ],
    ];

    private const string TEST_PW = 'portal-password';

    public function canGetPortalUserRoles(ApiTester $I): void
    {
        foreach (self::PORTAL_USER_ROLES as $userTest) {
            $I->amAuthenticatedAsPortalUser($userTest['user'], $userTest['user'], self::TEST_PW);
            $I->callApiPortalProfile();
            $roles = $I->grabDataFromResponseByJsonPath('$.roles')[0];
            $groups = $I->grabDataFromResponseByJsonPath('$.groups')[0];
            $delegateGroups = $I->grabDataFromResponseByJsonPath('$.groups_user_can_delegate')[0];

            $I->assertIsArray($roles);
            foreach ($userTest['allowed'] as $allowedRole) {
                $I->assertContains($allowedRole, $roles);
            }
            foreach ($userTest['forbidden'] as $forbiddenRole) {
                $I->assertNotContains($forbiddenRole, $roles);
            }
            foreach ($userTest['groups'] as $group) {
                $I->assertContains($group, $groups);
            }

            $delegateGroupTestData = self::PORTAL_USER_DELEGATE_GROUPS[$userTest['user']];
            foreach ($delegateGroupTestData['allowed'] as $group) {
                $I->assertContains($group, $delegateGroups);
            }
            foreach ($delegateGroupTestData['forbidden'] as $group) {
                $I->assertNotContains($group, $delegateGroups);
            }
        }
    }
}
