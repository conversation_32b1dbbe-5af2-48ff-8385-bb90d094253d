<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalDashboardCest
{
    public function canGetPortalDashboard(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDashboard();

        $tours = $I->grabDataFromResponseByJsonPath('$.tours.*');

        $I->assertIsArray($tours);

        foreach ($tours as $tour) {
            $I->assertIsString($tour['id']);
            $I->assertIsNumeric($tour['completed_percentage']);
        }
        $I->assertIsNumeric($I->grabDataFromResponseByJsonPath('$.completed_tours')[0]);
        $I->assertIsNumeric($I->grabDataFromResponseByJsonPath('$.created_tours')[0]);
        $I->assertIsNumeric($I->grabDataFromResponseByJsonPath('$.active_tours')[0]);
        $I->assertIsNumeric($I->grabDataFromResponseByJsonPath('$.terminated_tours')[0]);
        $I->assertIsNumeric($I->grabDataFromResponseByJsonPath('$.active_equipments')[0]);

        $I->assertRegExp('/^\d{4}-\d{2}-\d{2}/', $I->grabDataFromResponseByJsonPath('$.selected_date')[0]);
    }

    public function cannotGetDashboardForInvalidDate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDashboard(date: 'invalid-date', expectedStatusCode: 422);
        $I->canSeeResponseContains('Invalid value(s) for query parameter');
        $I->canSeeResponseContains('This value is not a valid date.');

        // Test with german locale
        $I->haveHttpHeader('Accept-Language', 'de');
        $I->callApiPortalDashboard(date: 'invalid-date', expectedStatusCode: 422);
        $I->canSeeResponseContains('Invalid value(s) for query parameter');
        $I->canSeeResponseContains('Dieser Wert entspricht keiner g\u00fcltigen Datumsangabe.');
    }

    public function cannotGetPortalDashboardIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalDashboard(expectedStatusCode: 401);
    }
}
