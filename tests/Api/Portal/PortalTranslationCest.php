<?php

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalTranslationCest
{
    private const string LANGUAGE_CODE = 'de_DE';

    public function cannotGetPortalTranslationIsoCodeListIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalTranslationIsoCodeList(expectedStatusCode: 401);
    }

    public function cannotGetPortalTranslationsByCodeIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalTranslationByCode(self::LANGUAGE_CODE, expectedStatusCode: 401);
    }

    public function cannotGetPortalWaypointTranslationsByCodeIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalWaypointTranslationByCode(self::LANGUAGE_CODE, expectedStatusCode: 401);
    }
}
