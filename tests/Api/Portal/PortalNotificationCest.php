<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalNotificationCest
{
    public function canGetNotificationAccessDataAsPortalAdmin(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalNotificationAccess();
    }

    public function canGetNotificationAccessDataAsPortalUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalNotificationAccess();
    }
}
