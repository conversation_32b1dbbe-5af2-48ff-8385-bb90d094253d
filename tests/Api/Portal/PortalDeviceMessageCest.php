<?php

/** @noinspection PhpUnhandledExceptionInspection */

namespace App\Tests\Api\Portal;

use App\Infrastructure\PortalApi\Resource\Dto\DeviceMessage\RecipientKind;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Symfony\Component\Uid\Uuid;

class PortalDeviceMessageCest
{
    private string $tenant = 'pz';
    private string $country = 'de';
    private string $staffExtId;
    private string $staffId;
    private string $equipment_branch_extid = 'Oelbronn';

    public function canGetListOfDeviceMessageThreads(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDeviceMessageThreadList();
        $threads = $I->grabDataFromResponseByJsonPath('$.items');
        $I->assertGreaterThan(0, count($threads));

        $I->seeResponseMatchesJsonType([
            'total_items' => 'integer',
            'items' => [
                [
                    'id' => 'string:!empty',
                    'has_unread_messages' => 'boolean',
                    'updated_at' => 'string:!empty',
                    'last_message_excerpt' => 'string',
                    'target' => 'string:!empty', // 'staff' or 'equipment'
                    'target_identifier' => 'string:!empty',
                ],
            ],
        ]);
    }

    public function canSendMessageToStaff(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffList();
        $staffId = $I->grabDataFromResponseByJsonPath('$.items[0].staff_id')[0];

        $data = [
            'recipient' => [
                'kind' => RecipientKind::STAFF->value,
                'staff_id' => $staffId,
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $this->validatePortalSeesHaveTheMessage($I, $data['message'], 'staff');
    }

    public function canSendMessageToEquipment(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $I::TEST_STAFF_EXT_ID_PREFIX.uniqid(),
            equipmentExtId2: 'random-equipment-'.uniqid(),
            branchExternalId: $this->equipment_branch_extid,
        ));
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentList();
        $equipmentId = $I->grabDataFromResponseByJsonPath('$.items[0].equipment_id')[0];

        $data = [
            'recipient' => [
                'kind' => RecipientKind::EQUIPMENT->value,
                'equipment_id' => $equipmentId,
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $this->validatePortalSeesHaveTheMessage($I, $data['message'], 'equipment');
    }

    public function canSendMessageToBranchStaff(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $branchId = $I->grabDataFromResponseByJsonPath('$.items[0].branch_id')[0];

        $data = [
            'recipient' => [
                'kind' => RecipientKind::BRANCH_STAFF->value,
                'branch_id' => $branchId,
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $this->validatePortalSeesHaveTheMessage($I, $data['message'], 'staff');
    }

    #[Depends('canSendMessageToEquipment')]
    public function canSendMessageToBranchEquipmentType(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();
        $branchId = $I->grabDataFromResponseByJsonPath('$.items[0].branch_id')[0];

        $data = [
            'recipient' => [
                'kind' => RecipientKind::BRANCH_EQUIPMENT_TYPE->value,
                'branch_id' => $branchId,
                'equipment_type' => 'ask',
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $this->validatePortalSeesHaveTheMessage($I, $data['message'], 'equipment');
    }

    public function canReplyToThread(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalDeviceMessageThreadList();
        $thread = $I->grabDataFromResponseByJsonPath('$.items[0]')[0];

        $data = [
            'recipient' => [
                'kind' => RecipientKind::THREAD->value,
                'thread_id' => $thread['id'],
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $this->validatePortalSeesHaveTheMessage($I, $data['message']);
    }

    public function canCreateDeviceMessageAndReply(ApiTester $I): void
    {
        $deviceId = uniqid();
        $this->staffExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffList(query: $this->staffExtId);

        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $this->staffId = $data['items'][0]['staff_id'];
        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContents = file_get_contents(codecept_data_dir('small-img.png'));

        $I->callApiPortalFilePut(
            directory: 'device-message',
            id: $fileUuid,
            fileContents: $fileContents,
        );

        $dataOrig = [
            'recipient' => [
                'kind' => RecipientKind::STAFF,
                'staff_id' => $this->staffId,
            ],
            'message' => uniqid(),
            'attachments' => ['device-message/'.$fileUuid],
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($dataOrig));

        $I->callApiPortalDeviceMessageThreadList();
        $thread = $I->grabDataFromResponseByJsonPath('$.items[?(@.last_message_excerpt == "'.$dataOrig['message'].'")]')[0];
        $I->assertNotEmpty($thread, 'Thread not found');

        // Validate portal sees the message
        $I->callApiPortalDeviceMessagesInThread(threadId: $thread['id']);
        $message = $I->grabDataFromResponseByJsonPath('$.items[?(@.message == "'.$dataOrig['message'].'")]')[0];
        $I->assertEquals('portal-user-de', $message['username']);
        $I->assertEquals('dispatcher', $message['from']);
        $createdAt = (new \DateTimeImmutable($message['created_at']))->getTimestamp();
        $I->assertGreaterThan(strtotime('-10 second'), $createdAt);
        $I->assertLessThan(strtotime('+10 second'), $createdAt);
        $I->assertEquals('device-message/'.$fileUuid, $message['attachments'][0]);

        // Validate centrifugo has received the message
        $history = $I->grabCentrifugoHistory(channel: 'device:channel#'.bin2hex($deviceId));
        $I->assertNotEmpty($history);
        $I->assertGreaterOrEquals(1, count($history));
        $historyMessage = null;

        foreach ($history as $historyItem) {
            if ($historyItem['data']['id'] === $message['id']) {
                $historyMessage = $historyItem['data'];
                break;
            }
        }

        $I->assertNotEmpty($historyMessage, 'History message not found');

        $I->assertEquals($thread['id'], $historyMessage['thread_id']);
        $I->assertEquals('portal-user-de', $historyMessage['username']);
        $I->assertEquals($dataOrig['message'], $historyMessage['message']);
        $I->assertEquals('device_message', $historyMessage['type']);
        $createdAt = (new \DateTimeImmutable($historyMessage['created_at']))->getTimestamp();
        $I->assertGreaterThan(strtotime('-10 second'), $createdAt);
        $I->assertLessThan(strtotime('+10 second'), $createdAt);

        $data = [
            'recipient' => [
                'kind' => RecipientKind::THREAD->value,
                'thread_id' => $thread['id'],
            ],
            'message' => uniqid(),
        ];
        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        $history = $I->grabCentrifugoHistory(channel: 'device:channel#'.bin2hex($deviceId));
        $I->assertNotEmpty($history);
        $I->assertGreaterOrEquals(2, count($history));
        $firstKey = array_key_first($history);
        $I->assertEquals($data['message'], $history[$firstKey]['data']['message']);
        $I->assertEquals('device_message', $history[$firstKey]['data']['type']);
    }

    public function canCreateDeviceMessagesWithBranchId(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $branchId = null;
        $deviceId = 'random-device-'.uniqid();
        $branchExtId = 'random-branch-'.uniqid();
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $this->staffExtId,
            equipmentExtId2: 'random-equipment-'.uniqid(),
            branchExternalId: $branchExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchList($this->country);

        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');

        foreach ($responseData as $item) {
            if ($item['external_id'] == $branchExtId) {
                $branchId = $item['branch_id'];
                break;
            }
        }

        $data = [
            'recipient' => [
                'kind' => RecipientKind::BRANCH_STAFF->value,
                'branch_id' => $branchId,
            ],
            'message' => 'Test message for branch recipient',
            'attachments' => [],
        ];

        $I->callApiPortalDeviceMessageCreate(json_encode($data));

        // Validate centrifugo has received the message
        $history = $I->grabCentrifugoHistory(channel: 'device:channel#'.bin2hex($deviceId));
        $I->assertNotEmpty($history);
        $I->assertEquals('Test message for branch recipient', $history[0]['data']['message']);
        $I->assertEquals('device_message', $history[0]['data']['type']);
    }

    public function cannotCreateDeviceMessageIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalDeviceMessageCreate('', expectedStatusCode: 401);
    }

    public function canSearchForDeviceMessageThreads(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalDeviceMessageThreadSearch(search: $this->staffExtId);
        $threads = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertGreaterThan(0, count($threads));
    }

    private function validatePortalSeesHaveTheMessage(
        ApiTester $I,
        string $message,
        ?string $target = null,
    ): void {
        $currentTime = new \DateTimeImmutable()->getTimestamp();

        // Validate portal sees the message
        $I->callApiPortalDeviceMessageThreadList(perPage: 100);
        $thread = $I->grabDataFromResponseByJsonPath('$.items[?(@.last_message_excerpt == "'.$message.'")]')[0];
        $I->assertNotEmpty($thread, 'Thread not found');
        $updatedAt = new \DateTimeImmutable($thread['updated_at'])->getTimestamp();
        $I->assertGreaterThan($currentTime - 6, $updatedAt);
        $I->assertLessThan($currentTime + 6, $updatedAt);

        if (null !== $target) {
            $I->assertEquals($target, $thread['target']);
        }

        $I->callApiPortalDeviceMessagesInThread($thread['id']);
        $message = $I->grabDataFromResponseByJsonPath('$.items[?(@.message == "'.$message.'")]')[0];
        $I->assertNotEmpty($message, 'Message not found');
        $I->assertEquals('portal-user-de', $message['username']);
        $I->assertEquals('dispatcher', $message['from']);
        $createdAt = new \DateTimeImmutable($message['created_at'])->getTimestamp();
        $I->assertGreaterThan($currentTime - 6, $createdAt);
        $I->assertLessThan($currentTime + 6, $createdAt);
    }
}
