<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalConnectedDevicesCest
{
    public function canGetPortalConnectedDevicesHardwareTypes(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalConnectedDevicesHardwareTypes();
        $items = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertIsArray($items);
        $I->assertGreaterOrEquals(1, $items);
    }

    public function cannotGetPortalConnectedDevicesHardwareTypesIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalConnectedDevicesHardwareTypes(expectedStatusCode: 401);
    }
}
