<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\S3Helper;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use App\Tests\Unit\Infrastructure\MobileAppReleasesApi\Service\ReleaseNoteProcessorTest;

class PortalMobileAppReleaseCest
{
    public function listAndDownloadMobileAppReleases(ApiTester $I): void
    {
        $fileNameApk = '/mobile-app-releases/app-release-4.11.0.apk';
        $fileNameTxt = '/mobile-app-releases/translationkey-4.11.0.txt';
        $binary = random_bytes(1024);
        $text = DataFaker::instance()->text();

        S3Helper::getS3Client()->putObject([
            'Bucket' => getenv('S3_BUCKET_NAME'),
            'Key' => ltrim($fileNameApk, '/'),
            'Body' => $binary,
            'ContentType' => 'application/vnd.android.package-archive',
            'Metadata' => [],
        ]);

        S3Helper::getS3Client()->putObject([
            'Bucket' => getenv('S3_BUCKET_NAME'),
            'Key' => ltrim($fileNameTxt, '/'),
            'Body' => $text,
            'ContentType' => 'text/plain; charset=utf-8',
            'Metadata' => [],
        ]);

        $I->callApiMobileAppReleases(json_encode([
            'version' => 'v4.11.0',
            'type' => 'hermes_app',
            'releaseNotes' => ReleaseNoteProcessorTest::getSampleReleaseNotes(),
            'files' => [
                [
                    'path' => $fileNameApk,
                    'type' => 'apk',
                ],
                [
                    'path' => $fileNameTxt,
                    'type' => 'translation_file',
                ],
            ],
        ]));

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMobileAppReleaseList();

        $appRelease = $this->findMobileAppRelease($I, 'hermes_app', 'v4.11.0');
        $I->assertNotNull($appRelease, 'Version 4.11.0 should be found in the list of releases.');
        $I->assertContains('{{mobile_app_release/recommended_version}}', $appRelease['status_badges']);
        $I->callApiPortalMobileAppReleaseDetails($appRelease['id']);
        $releaseDetails = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertCount(2, $releaseDetails['files']);
        $I->callApiPortalMobileAppReleaseDownload($appRelease['id'], $releaseDetails['files'][0]['id']);
        $url = $I->grabDataFromResponseByJsonPath('$.url')[0];
        $I->assertEquals($binary, file_get_contents($url));
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMobileAppReleaseDownload($appRelease['id'], $releaseDetails['files'][1]['id']);
        $url = $I->grabDataFromResponseByJsonPath('$.url')[0];
        $I->assertEquals($text, file_get_contents($url));
    }

    public function cannotGetPortalMobileAppReleaseListIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalMobileAppReleaseList(expectedStatusCode: 401);
    }

    private function findMobileAppRelease(ApiTester $I, string $type, string $version): ?array
    {
        $appReleases = $I->grabDataFromResponseByJsonPath('$.items')[0];
        assert(is_array($appReleases));
        foreach ($appReleases as $appRelease) {
            if (str_contains($appRelease['version'], $version) && str_contains($appRelease['type'], $type)) {
                return $appRelease;
            }
        }

        return null;
    }
}
