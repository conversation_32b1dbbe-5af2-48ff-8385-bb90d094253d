<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Faker\Factory;

class PortalMastertourTemplateCest
{
    private string $branchId = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

    private string $branchExternalId = 'Oelbronn';
    private string $externalId = '';
    private string $externalId2 = '';
    private string $name = '';
    private string $templateUuid = '';
    private string $description = '';

    public function canPostMastertourTemplate(ApiTester $I): void
    {
        $this->externalId = 'BLA filter-one-'.uniqid();
        $this->externalId2 = 'BLA filter-two-'.uniqid();
        $this->name = md5((string) rand(0, 100));
        $this->description = md5((string) rand(0, 255));

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $data = $this->getMastertourTemplateRequestJson($this->branchId, $this->externalId, $this->name, $this->description);
        $I->callApiPortalMastertourTemplateCreate($data);

        $data2 = $this->getMastertourTemplateRequestJson($this->branchId, $this->externalId2, uniqid(), $this->description);
        $I->callApiPortalMastertourTemplateCreate($data2);
    }

    public function canNotCreateDuplicateExternalId(ApiTester $I): void
    {
        $this->externalId = 'BLA filter-one-'.uniqid();
        $this->name = md5((string) rand(0, 100));
        $this->description = md5((string) rand(0, 255));

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $data = $this->getMastertourTemplateRequestJson($this->branchId, $this->externalId, $this->name, $this->description);
        $I->callApiPortalMastertourTemplateCreate($data);
        $I->callApiPortalMastertourTemplateCreate($data, expectedStatusCode: 400);
    }

    #[Depends('canPostMastertourTemplate')]
    public function canGetMastertourTemplateList(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalMastertourTemplateList();
        $expectedExternalIds = [$this->externalId, $this->externalId2];
        $list = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($list as $listEntry) {
            if (in_array($listEntry['external_id'], $expectedExternalIds, true)) {
                unset($expectedExternalIds[array_search($listEntry['external_id'], $expectedExternalIds, true)]);
            }

            $I->assertGreaterThan(0, $listEntry['waypoint_count']);

            if ($listEntry['external_id'] == $this->externalId) {
                $this->templateUuid = $listEntry['mastertour_template_id'];
            }
            $I->assertEquals($listEntry['branch_external_id'], $this->branchExternalId);
        }

        $I->assertEmpty($expectedExternalIds, 'List should contain all expected external IDs');
    }

    #[Depends('canPostMastertourTemplate')]
    public function canFilterMasterTourTemplateList(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalMastertourTemplateList('filter-one');
        $list = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $found = false;

        foreach ($list as $listEntry) {
            $I->assertTrue(str_contains($listEntry['external_id'], 'filter-one'), 'List should contain only items with filter-one');
            $I->assertFalse(str_contains($listEntry['external_id'], 'filter-two'), 'List should not contain items with filter-two');
            if ($listEntry['external_id'] === $this->externalId) {
                $found = true;
            }
        }

        $I->assertTrue($found);
    }

    #[Depends('canGetMastertourTemplateList')]
    public function canGetMastertourTemplateDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->assertNotEmpty($this->templateUuid);
        $I->callApiPortalMastertourTemplateDetails($this->templateUuid);
        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals(100, count($data['waypoints']));
        $I->assertEquals(100, $data['waypoint_count']);

        foreach ($data['waypoints'] as $waypoint) {
            $I->assertNotEmpty($waypoint['id']);
            $I->assertEquals(36, strlen($waypoint['id']));
            $I->assertNotEmpty($waypoint['description']);
            $I->assertNotEmpty($waypoint['info']);
        }
    }

    public function canNotGetUnknownMastertourTemplateDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $unknownTemplateUuid = '*************-9999-9999-************';
        $I->callApiPortalMastertourTemplateDetails($unknownTemplateUuid, expectedStatusCode: 404);
    }

    #[Depends('canGetMastertourTemplateDetails')]
    public function canUpdateMastertourTemplate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $this->name = md5((string) rand(0, 100));
        $this->description = md5((string) rand(0, 255));
        $data = $this->getMastertourTemplateRequestJson($this->branchId, $this->externalId, $this->name, $this->description);
        $I->callApiPortalMastertourTemplateUpdate($this->templateUuid, $data);

        $I->callApiPortalMastertourTemplateDetails($this->templateUuid);
        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals($this->name, $data['name']);
    }

    #[Depends('canUpdateMastertourTemplate')]
    public function canDeleteMastertourTemplate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMastertourTemplateDelete($this->templateUuid);
    }

    private function getMastertourTemplateRequestJson(string $branchId, string $externalId, string $name, string $description): string
    {
        $faker = Factory::create();
        $ret = [
            'branch_id' => $branchId,
            'external_id' => $externalId,
            'name' => $name,
            'waypoints' => [],
            'description' => $description,
        ];

        for ($i = 0; $i < 100; ++$i) {
            $ret['waypoints'][] = [
                'longitude' => 4.2,
                'latitude' => 6.1,
                'distance_to_previous_waypoint_meters' => 10,
                'bearing' => 345.2,
                'description' => '{{str/i-ng}}',
                'info' => $faker->word(),
                'text_to_speech' => true,
                'color' => 'color_1',
                'road_name' => $faker->streetName(),
            ];
        }

        return json_encode($ret);
    }
}
