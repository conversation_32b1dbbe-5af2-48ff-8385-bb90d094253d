<?php

/** @noinspection PhpUnhandledExceptionInspection */

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalStaffCest
{
    private const string STAFF_UUID = '84ec9a14-f951-41ef-b8ce-65ae511eb2df';
    private const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

    private string $tenant = 'pz';

    public function canGetPortalStaffList(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffList();
    }

    public function canGetPortalStaffListSearch(ApiTester $I): void
    {
        $staffId = $I->generateStaffAndAuthenticate($this->tenant);
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffList(query: $staffId);

        $staff = $I->grabDataFromResponseByJsonPath('$.items.*');

        $I->assertIsArray($staff);
        $I->assertEquals($staff[0]['external_id'], $staffId);
    }

    public function canGetPortalStaffById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffById(self::STAFF_UUID);
    }

    public function cannotGetPortalStaffListIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalStaffList(expectedStatusCode: 401);
    }

    public function cannotGetPortalStaffByIdIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalStaffById(self::STAFF_UUID, expectedStatusCode: 401);
    }

    public function canSearchStaff(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffList(query: 'STAFF');
        $staff = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($staff, 'No equipment found');
    }

    public function canGetPortalStaffListByBranch(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffListByBranch(self::BRANCH_ID);

        $staff = $I->grabDataFromResponseByJsonPath('$.items.*');

        $I->assertIsArray($staff, 'No staff found');
    }
}
