<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class PortalFaqCest
{
    private string $faqId = '';

    public function canGetPortalFaq(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalFaqList();
    }

    public function cannotGetPortalFaqIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalFaqList(expectedStatusCode: 401);
    }

    public function canPostPortalFaq(ApiTester $I): void
    {
        $question = md5((string) rand(0, 100));
        $answer = md5((string) rand(0, 100));
        $sequence = rand(0, 50);
        $iso = 'de_DE';

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getFaqRequestJson($question, $answer, $sequence, $iso);
        $I->callApiPortalFaqCreate($data);
        $I->waitUntil(function (ApiTester $I) use ($question, $answer, $sequence, $iso): void {
            $faqs = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($faqs));
            $this->faqId = $faqs['faq_id'];
            $I->assertContains($question, $faqs);
            $I->assertContains($answer, $faqs);
            $I->assertContains($sequence, $faqs);
            $I->assertContains($iso, $faqs);
        });
    }

    #[Depends('canPostPortalFaq')]
    public function canUpdatePortalFaq(ApiTester $I): void
    {
        $question = md5((string) rand(0, 100));
        $answer = md5((string) rand(0, 100));
        $sequence = rand(0, 50);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getFaqRequestJson($question, $answer, $sequence);
        $I->callApiPortalFaqUpdate($this->faqId, $data);
        $I->waitUntil(function (ApiTester $I) use ($question, $answer, $sequence): void {
            $faqs = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($faqs));
            $I->assertContains($question, $faqs);
            $I->assertContains($answer, $faqs);
            $I->assertContains($sequence, $faqs);
        });
    }

    private function getFaqRequestJson(string $question, string $answer, int $sequence, ?string $iso = null): string
    {
        $data = [
            'question' => $question,
            'answer' => $answer,
            'sequence' => $sequence,
        ];

        if (null !== $iso) {
            $data['iso'] = $iso;
        }

        return json_encode($data, JSON_THROW_ON_ERROR);
    }
}
