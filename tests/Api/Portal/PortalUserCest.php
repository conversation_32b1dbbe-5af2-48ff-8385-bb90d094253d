<?php

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalUserCest
{
    public function canGetOwnProfileData(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalProfile();
        $I->seeResponseMatchesJsonType([
            'first_name' => 'string',
            'last_name' => 'string',
            'username' => 'string',
            'language' => 'string',
            'groups' => 'array',
            'groups_user_can_delegate' => 'array',
            'last_update' => 'string',
        ]);
    }

    public function cannotGetOwnProfileDataIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalProfile(expectedStatusCode: 401);
    }

    public function canGetPortalUserList(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserList(perPage: 100);
        $I->seeResponseMatchesJsonType([
            'total_items' => 'integer',
            'items' => [
                [
                    'user_id' => 'string',
                    'username' => 'string',
                    'first_name' => 'string',
                    'last_name' => 'string',
                    'email' => 'string',
                ],
            ],
        ]);

        $userNames = array_column($I->grabDataFromResponseByJsonPath('$.items')[0], 'username');
        $itemCount = $I->grabDataFromResponseByJsonPath('$.total_items')[0];
        $I->assertEquals(count($userNames), $itemCount);
        $I->assertNotEmpty($userNames);
        $forbiddenUserNames = [
            'portal-admin-nl',
            'portal-user-nl',
            'portal-admin-es',
            'portal-user-es',
            'portal-admin-lu',
            'portal-user-lu',
        ];
        foreach ($forbiddenUserNames as $forbiddenUserName) {
            $I->assertNotContains($forbiddenUserName, $userNames);
        }
    }

    public function canGetPortalUserListSearch(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $firstName = 'first-name-'.uniqid();
        $lastName = 'last-name-'.uniqid();
        $username = 'rand-portal-user'.uniqid();
        $email = 'rand-portal-user'.uniqid().'@test.com';

        $data = $this->getUserCreateJson(
            $username,
            $email,
            $firstName,
            $lastName,
        );

        $I->callApiPortalUserCreate($data);

        $I->callApiPortalUserList(query: $username);
        $users = $I->grabDataFromResponseByJsonPath('$.items')[0][0];

        $I->assertNotEmpty($users);
        $I->assertEquals($username, $users['username']);
        $I->assertEquals($firstName, $users['first_name']);
        $I->assertEquals($lastName, $users['last_name']);
        $I->assertEquals($email, $users['email']);
    }

    public function cannotGetPortalUserListIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalUserList(expectedStatusCode: 401);
    }

    public function cannotGetPortalUserListIfNotAdmin(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalUserList(expectedStatusCode: 403);
    }

    /**
     * @throws \Throwable
     */
    public function canUpdateOwnProfile(ApiTester $I): void
    {
        $language = 'de_DE';
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $data = $this->getProfileUpdateRequestJson($language);
        $I->callApiPortalUserProfilePatch($data);
        $I->waitUntil(function (ApiTester $I) use ($language): void {
            $I->callApiPortalProfile();
            $user = $I->grabDataFromResponseByJsonPath('$.language')[0];
            $I->assertEquals($language, $user);
        });
    }

    public function countryAdminCanGetPortalUserDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $I->callApiPortalUserGet('ddba0342-cb56-4f81-b2d5-56d979118df5');
        $I->seeResponseMatchesJsonType([
            'user_id' => 'string',
            'username' => 'string',
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
            'country' => 'string',
            'groups' => 'array',
            'branch_access' => 'array',
            'country_access' => 'array',
        ]);
    }

    public function countryAdminCannotGetCountryAdminDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $I->callApiPortalUserGet('d191400e-0e16-4768-a1d2-9abfafe75dbb', expectedStatusCode: 403);
    }

    public function portalAdminCanGetPortalUserDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserGet('ddba0342-cb56-4f81-b2d5-56d979118df5');
        $I->seeResponseMatchesJsonType([
            'user_id' => 'string',
            'username' => 'string',
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
            'country' => 'string',
            'groups' => 'array',
            'branch_access' => 'array',
            'country_access' => 'array',
        ]);
    }

    public function portalAdminCanGetCountryAdminDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserGet('d191400e-0e16-4768-a1d2-9abfafe75dbb');
        $I->seeResponseMatchesJsonType([
            'user_id' => 'string',
            'username' => 'string',
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
            'country' => 'string',
            'groups' => 'array',
            'branch_access' => 'array',
            'country_access' => 'array',
        ]);
    }

    public function portalAdminCanGetOwnPortalAdminDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserGet('8f93452f-c58b-4720-85a5-1a97db5f88bb', expectedStatusCode: 200);
    }

    public function portalAdminCanNotGetOtherPortalAdminDetails(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin2-de', 'portal-admin2-de', 'portal-password');
        $I->callApiPortalUserGet('8f93452f-c58b-4720-85a5-1a97db5f88bb', expectedStatusCode: 403);
    }

    public function countryAdminCanGetUserInOwnCountry(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $I->callApiPortalUserGet('ddba0342-cb56-4f81-b2d5-56d979118df5', expectedStatusCode: 200);
    }

    public function countryAdminCanNotGetUserInOtherCountry(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-country-admin-de', 'portal-country-admin-de', 'portal-password');
        $I->callApiPortalUserGet('e5afd1f0-197c-4e06-92d8-09b1e1bd4de8', expectedStatusCode: 403);
    }

    public function portalAdminCanGetUserInAllCountry(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserGet('e5afd1f0-197c-4e06-92d8-09b1e1bd4de8', expectedStatusCode: 200);
        $I->callApiPortalUserGet('ddba0342-cb56-4f81-b2d5-56d979118df5', expectedStatusCode: 200);
    }

    public function checkUsernameExists(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserCheckUsername('portal-user-de', expectedStatusCode: 409);
        $I->callApiPortalUserCheckUsername('portal-user-de'.uniqid());
    }

    public function checkEmailExists(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalUserCheckEmail('<EMAIL>', expectedStatusCode: 409);
        $I->callApiPortalUserCheckEmail('api1'.uniqid().'@prezero.com');
    }

    private function getProfileUpdateRequestJson(string $language): string
    {
        return <<<JSON
        {
            "language": "{$language}"
        }
        JSON;
    }

    private function getUserCreateJson(
        string $username,
        string $email,
        string $fname,
        string $lname,
    ): string {
        return <<<JSON
        {
          "username": "$username",
          "first_name": "$fname",
          "last_name": "$lname",
          "email": "$email",
          "country": "de",
          "groups": [
            "COUNTRY_ADMIN"
          ],
          "branch_access": [],
          "country_access": ["de"]
        }
        JSON;
    }
}
