<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;

class PortalMastertourImportCest
{
    public function canImportMastertourTemplate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $fileContents = file_get_contents(codecept_data_dir('9558_36875.xlsx'));

        $I->callApiPortalMastertourImport(
            type: 'couplink',
            fileName: '9558_36875',
            fileContents: $fileContents,
        );
        $waypoints = $I->grabDataFromResponseByJsonPath('$.waypoints')[0];
        $I->assertIsArray($waypoints);
        $I->assertNotEmpty($waypoints);
        $I->assertCount(683, $waypoints);
        $I->assertEquals(0, $waypoints[0]['distance_to_previous_waypoint_meters']);
        $I->assertEquals(0, $waypoints[count($waypoints) - 1]['bearing']);

        $I->assertEquals(75, $waypoints[1]['distance_to_previous_waypoint_meters']);
        $I->assertEquals(251, $waypoints[1]['bearing']);
        $I->assertEquals(137, $waypoints[2]['distance_to_previous_waypoint_meters']);
        $I->assertEquals(270, $waypoints[2]['bearing']);
        $I->assertEquals(28, $waypoints[3]['distance_to_previous_waypoint_meters']);
        $I->assertEquals(270, $waypoints[3]['bearing']);
    }
}
