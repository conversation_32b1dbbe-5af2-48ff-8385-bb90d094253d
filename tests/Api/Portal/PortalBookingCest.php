<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class PortalBookingCest
{
    private string $equipmentExtId = '';
    private string $equipmentId = '';
    private string $tourId = '';
    private string $staffExtId = '';
    private string $staff2ExtId = '';
    private string $deviceId = '';
    private string $tenant = 'pz';
    private string $tourExternalId = '';
    private string $sessionId = '';

    public function canGetTourBookings(ApiTester $I): void
    {
        $this->createTourBooking($I);
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalBookingList(tourId: $this->tourId, sessionId: $this->sessionId);
        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertCount(1, $responseData);
        $I->assertEquals('start', $responseData[0]['type']);
    }

    #[Depends('canGetTourBookings')]
    public function cannotGetBookingsWithoutRequiredFilters(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalBookingList(tourId: '', sessionId: '', expectedStatusCode: 400);
        $I->callApiPortalBookingList(tourId: $this->tourId, sessionId: '', expectedStatusCode: 400);
        $I->callApiPortalBookingList(tourId: '', sessionId: $this->sessionId, expectedStatusCode: 400);
    }

    public function cannotGetBookingsIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalBookingList(tourId: $this->tourId, sessionId: $this->sessionId, expectedStatusCode: 401);
    }

    private function createTourBooking(ApiTester $I): void
    {
        $this->tourExternalId = 'random-tour-id-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->staff2ExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->deviceId = 'random-device-id-'.uniqid();
        $orderExtId = 'random-order-id-'.uniqid();
        $tour1Name = 'random-tour-name-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->tourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExternalId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            staffExternalId2: $this->staff2ExtId,
        ));
        $I->amUsingDeviceWithId($this->deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();
        $this->sessionId = $I->grabDataFromResponseByJsonPath('$.session_uuid')[0];

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipmentFromList = $this->getEquipmentFromList($I->grabDataFromResponseByJsonPath('$.items')[0], $equipmentLicensePlate);
        $this->equipmentId = $equipmentFromList['uuid'];
        $I->assertEquals('unequipped', $equipmentFromList['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentId,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I) use ($equipmentLicensePlate): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipmentFromList = $this->getEquipmentFromList($I->grabDataFromResponseByJsonPath('$.items')[0], $equipmentLicensePlate);
            $I->assertEquals('equipped', $equipmentFromList['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->tourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);
    }

    private function getEquipmentFromList(array $equipments, string $equipmentLicensePlate): array
    {
        foreach ($equipments as $set) {
            if (isset($set['name']) && $equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
