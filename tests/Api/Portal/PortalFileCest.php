<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Symfony\Component\Uid\Uuid;

class PortalFileCest
{
    public function canUploadAndRetrieveDeviceMessageFile(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');

        $uuid = Uuid::v4()->toRfc4122();
        $fileContents = file_get_contents(codecept_data_dir('small-img.png'));

        $I->callApiPortalFilePut(
            directory: 'device-message',
            id: $uuid,
            fileContents: $fileContents,
        );

        $I->callApiPortalFileGet(
            filePath: 'device-message/'.$uuid,
        );

        $receivedFileContents = $I->grabResponse();

        $I->assertEquals($fileContents, $receivedFileContents);
    }

    public function canGetListOfFilesInOrder(ApiTester $I): void
    {
        /**
         * @todo remove after portal-change
         */
        $orderId = $this->createOrderWithFiles($I);
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');

        $I->waitUntil(function (ApiTester $I) use ($orderId) {
            $I->callApiPortalFilesInOrder($orderId);
            $items = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertIsArray($items);
            $I->assertCount(1, $items);
        });
    }

    public function canGetListOfFilesInMultipleOrders(ApiTester $I): void
    {
        $orderId1 = $this->createOrderWithFiles($I);
        $orderId2 = $this->createOrderWithFiles($I);

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');

        $I->waitUntil(function (ApiTester $I) use ($orderId1) {
            $I->callApiPortalFilesInMultipleOrders([$orderId1]);
            $items = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertIsArray($items);
            $I->assertCount(1, $items);
        });

        $I->waitUntil(function (ApiTester $I) use ($orderId2) {
            $I->callApiPortalFilesInMultipleOrders([$orderId2]);
            $items = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertIsArray($items);
            $I->assertCount(1, $items);
        });

        $I->waitUntil(function (ApiTester $I) use ($orderId1, $orderId2) {
            $I->callApiPortalFilesInMultipleOrders([$orderId1, $orderId2]);
            $items = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertIsArray($items);
            $I->assertCount(2, $items);
        });
    }

    private function createOrderWithFiles(ApiTester $I): string
    {
        $tourExtId = 'random-tour-id-'.uniqid();
        $orderExtId = 'random-order-id-'.uniqid();
        $tour1Name = 'random-tour-name-'.uniqid();
        $deviceId = 'random-device-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();
        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);
        });

        $order = $I->grabDataFromResponseByJsonPath('$.orders[0]')[0];
        $I->assertNotEmpty($order['uuid']);

        $this->completeOrderTaskGroups($I, $order);

        return $order['uuid'];
    }

    private function completeOrderTaskGroups(ApiTester $I, $order): void
    {
        foreach ($order['taskgroups'] as $taskgroup) {
            foreach ($taskgroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    if ('signature' === $input['type']) {
                        $taskgroupId = $taskgroup['uuid'];
                        $taskId = $task['uuid'];
                        $inputId = $input['uuid'];

                        break 3;
                    }
                }
            }
        }

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('small-img.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $timestamp = (new \DateTimeImmutable())->format(\DateTimeInterface::RFC3339_EXTENDED);
        $data = <<<JSON
        {
            "uuid": "{$taskgroupId}",
            "tasks": [
                {
                    "uuid": "{$taskId}",
                    "skipped": false,
                    "additional_service": false,
                    "repeat_service": false,
                    "repeat_service_template_uuid": "",
                    "inputs": [
                        {
                            "type": "input",
                            "uuid": "{$inputId}",
                            "value": [
                                {
                                    "file": "app-user-files/{$fileUuid}",
                                    "name": "test1.png",
                                    "label": "test1"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        JSON;

        $I->callApiOrderTaskGroupPatchV2($order['uuid'], $data);
    }
}
