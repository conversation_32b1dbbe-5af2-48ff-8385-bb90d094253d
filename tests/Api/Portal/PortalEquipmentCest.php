<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Domain\Entity\Enum\Types\ConnectionType;
use App\Domain\Entity\Enum\Types\HardwareType;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class PortalEquipmentCest
{
    private string $createdEquipmentId = '';
    private string $tenant = 'pz';
    private string $staffExtId = '';
    private string $sapTourId = '';
    private string $equipmentUuid = '';
    private string $sessionEquipmentId = '';
    private string $equipmentLicensePlate = '';
    private const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

    public function canGetPortalEquipment(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $deviceId = 'random-device-id-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentList();
        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');
        $equipmentId = null;

        foreach ($responseData as $equipment) {
            $I->assertArrayHasKey('license_plate', $equipment, 'License plate is missing');
            if ($equipment['license_plate'] === $this->equipmentLicensePlate) {
                $I->assertArrayNotHasKey('active_tour', $responseData, 'Active Tour is in array but should not');
                $I->assertArrayHasKey('equipment_id', $equipment, 'Equipment ID is missing');
                $I->assertArrayHasKey('external_id', $equipment, 'External ID is missing');
                $I->assertArrayHasKey('type', $equipment, 'Type is missing');

                $I->assertArrayHasKey('last_staff', $equipment, 'Last staff is missing');
                $I->assertArrayHasKey('status', $equipment, 'Status is missing');
                $equipmentId = $equipment['equipment_id'];
                break;
            }
        }

        $I->assertNotNull($equipmentId, 'Equipment ID is not found');

        $I->callApiPortalEquipmentItem($equipmentId);
        $responseData = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertArrayHasKey('equipment_id', $responseData, 'Equipment ID is missing');
        $I->assertArrayHasKey('external_id', $responseData, 'External ID is missing');
        $I->assertArrayHasKey('type', $responseData, 'Type is missing');
        $I->assertArrayHasKey('license_plate', $responseData, 'Overload is missing');
        $I->assertArrayHasKey('last_staff', $responseData, 'Last staff is missing');
        $I->assertArrayNotHasKey('active_tour', $responseData, 'Active Tour is in array but should not');

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentList();
        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');

        foreach ($responseData as $equipment) {
            if ($equipment['equipment_id'] === $equipmentId) {
                $I->assertArrayHasKey('license_plate', $equipment, 'License plate is missing');
                $I->assertArrayHasKey('equipment_id', $equipment, 'Equipment ID is missing');
                $I->assertArrayHasKey('external_id', $equipment, 'External ID is missing');
                $I->assertArrayHasKey('type', $equipment, 'Type is missing');
                $I->assertArrayHasKey('last_staff', $equipment, 'Last staff is missing');
                $I->assertArrayHasKey('status', $equipment, 'Status is missing');
                $I->assertArrayHasKey('active_tour', $equipment, 'Active Tour is missing');
                break;
            }
        }

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        // End the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's ended
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('completed', $tourStatus[0]);
        });

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentList();
        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');

        foreach ($responseData as $equipment) {
            if ($equipment['equipment_id'] === $equipmentId) {
                $I->assertArrayHasKey('license_plate', $equipment, 'License plate is missing');
                $I->assertArrayHasKey('equipment_id', $equipment, 'Equipment ID is missing');
                $I->assertArrayHasKey('external_id', $equipment, 'External ID is missing');
                $I->assertArrayHasKey('type', $equipment, 'Type is missing');
                $I->assertArrayHasKey('last_staff', $equipment, 'Last staff is missing');
                $I->assertArrayHasKey('status', $equipment, 'Status is missing');
                $I->assertArrayNotHasKey('active_tour', $equipment, 'Active Tour should not include due tour completet');
                break;
            }
        }
    }

    public function canGetPortalEquipmentWithFilters(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->sapTourId = $I->callSapGermanyTourUpsert(
            SapGermanyTourFixtures::tourRequestCreate(
                tourExtId: 'random-tour-id-'.uniqid(),
                orderExtId: 'random-order-id-'.uniqid(),
                tourName: 'random-tour-name-'.uniqid(),
                equipmentExtId: 'random-equipment-'.uniqid(),
                equipmentLicensePlate: $this->equipmentLicensePlate,
                staffExternalId: $this->staffExtId,
            )
        );

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentList(branchIds: [self::BRANCH_ID], licensePlate: substr($this->equipmentLicensePlate, 0, -1));
        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');
        $equipmentId = null;

        foreach ($responseData as $equipment) {
            $I->assertArrayHasKey('license_plate', $equipment, 'License plate is missing');
            if ($equipment['license_plate'] === $this->equipmentLicensePlate) {
                $I->assertArrayNotHasKey('active_tour', $responseData, 'Active Tour is in array but should not');
                $I->assertArrayHasKey('equipment_id', $equipment, 'Equipment ID is missing');
                $I->assertArrayHasKey('external_id', $equipment, 'External ID is missing');
                $I->assertArrayHasKey('type', $equipment, 'Type is missing');

                $I->assertArrayHasKey('last_staff', $equipment, 'Last staff is missing');
                $I->assertArrayHasKey('status', $equipment, 'Status is missing');
                $equipmentId = $equipment['equipment_id'];
                break;
            }
        }

        $I->assertNotNull($equipmentId, 'Equipment ID is not found');
    }

    #[Depends('canGetPortalEquipmentWithFilters')]
    public function canGetPortalEquipmentPositionWithFilters(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentPositionList(branchIds: [self::BRANCH_ID], licensePlate: substr($this->equipmentLicensePlate, 0, -1));
        $responseData = $I->grabDataFromResponseByJsonPath('$.items.*');
        $equipmentId = null;

        foreach ($responseData as $equipment) {
            $I->assertArrayHasKey('license_plate', $equipment, 'License plate is missing');
            if ($equipment['license_plate'] === $this->equipmentLicensePlate) {
                $I->assertArrayHasKey('equipment_id', $equipment, 'Equipment ID is missing');
                $I->assertArrayHasKey('external_id', $equipment, 'External ID is missing');
                $I->assertArrayHasKey('type', $equipment, 'Type is missing');

                $I->assertArrayHasKey('status', $equipment, 'Status is missing');
                $equipmentId = $equipment['equipment_id'];
                break;
            }
        }

        $I->assertNotNull($equipmentId, 'Equipment ID is not found');
    }

    public function cannotGetPortalEquipmentIfNotAuthenticated(ApiTester $I): void
    {
        $I->callApiPortalEquipmentList(expectedStatusCode: 401);
    }

    public function canGetEquipmentTypes(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-license-plate-'.uniqid(),
        ));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentTypesList(self::BRANCH_ID);
        $equipments = $I->grabDataFromResponseByJsonPath('$.items.*');

        $I->assertIsArray($equipments, 'No equipment-types found');
    }

    public function canSearchEquipment(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-license-plate-'.uniqid(),
        ));

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentSearch('EQUIPMENT');
        $equipments = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($equipments, 'No equipment found');
    }

    public function canCreateAndDeleteEquipmentWithMinimumRequiredData(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $equipmentData = [
            'external_id' => 'EQUIPMENT-'.uniqid(),
            'type' => 'ask',
            'branch_id' => 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8',
            'license_plate' => uniqid(),
        ];
        $I->callApiPortalEquipmentCreate(json_encode($equipmentData));
        $I->callApiPortalEquipmentSearch($equipmentData['external_id']);

        $equipments = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($equipments, 'Equipment not found');

        $equipmentId = $equipments[0]['equipment_id'];
        $I->callApiPortalEquipmentGet($equipmentId);
        $I->canSeeResponseContainsJson($equipmentData);

        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals('draft', $equipmentDetails['status']);

        $I->callApiPortalEquipmentDelete($equipmentId);
        $I->callApiPortalEquipmentGet($equipmentId, 404);
    }

    public function canCreateAndSapUpdateEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $equipmentExternalId = 'EQUIPMENT-SAP-'.uniqid();

        $equipmentData = [
            'external_id' => $equipmentExternalId,
            'type' => 'ask',
            'branch_id' => 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8',
            'license_plate' => uniqid(),
        ];
        $I->callApiPortalEquipmentCreate(json_encode($equipmentData));
        $I->callApiPortalEquipmentSearch($equipmentData['external_id']);

        $equipments = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($equipments, 'Equipment not found');

        $equipmentId = $equipments[0]['equipment_id'];
        $I->callApiPortalEquipmentGet($equipmentId);
        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals('draft', $equipmentDetails['status']);

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: $equipmentExternalId,
            equipmentLicensePlate: 'random-license-plate-'.uniqid(),
        ));

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentGet($equipmentId);
        $equipmentDetails = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals('available', $equipmentDetails['status']);

        $I->callApiPortalEquipmentDelete($equipmentId, 403);
    }

    public function canCreateEquipmentWithAllData(ApiTester $I): void
    {
        $macAddress = $this->generateMacAddress();
        $hardwareType = $this->getRandomHardwareType();
        $connectionType = $this->getRandomConnectionType();
        $pin = $this->generatePin();
        $data = $this->getConnectedDevicesRequest($macAddress, $hardwareType, $connectionType, $pin);

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $equipmentData = [
            'external_id' => 'EQUIPMENT-'.uniqid(),
            'type' => 'ask',
            'branch_id' => 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8',
            'height' => 100,
            'length' => 200,
            'width' => 300,
            'weight' => 400,
            'minimum_load' => 500,
            'overload' => 600,
            'load_capacity' => 700,
            'total_permissible_weight' => 800,
            'max_axle_load' => 900,
            'license_plate' => uniqid(),
            'container_mounting' => uniqid(),
            'connected_devices' => [$data],
        ];

        $I->callApiPortalEquipmentCreate(json_encode($equipmentData));
        $I->callApiPortalEquipmentSearch($equipmentData['external_id']);

        $equipments = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($equipments, 'Equipment not found');

        $this->createdEquipmentId = $equipments[0]['equipment_id'];

        $I->callApiPortalEquipmentItem($this->createdEquipmentId);
        $equipment = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->waitUntil(function (ApiTester $I) use ($equipment, $macAddress, $hardwareType, $connectionType, $pin): void {
            $connectedDevices = $equipment['connected_devices'];
            assert(is_array($connectedDevices));
            $I->assertContains($macAddress, $connectedDevices[0]);
            $I->assertContains($hardwareType, $connectedDevices[0]);
            $I->assertContains($connectionType, $connectedDevices[0]);
            $I->assertContains($pin, $connectedDevices[0]);
        });

        $I->callApiPortalEquipmentGet($this->createdEquipmentId);
        $I->canSeeResponseContainsJson($equipmentData);
    }

    #[Depends('canCreateEquipmentWithAllData')]
    public function canUpdateEquipmentWithConnectedDeviceData(ApiTester $I): void
    {
        $macAddress = $this->generateMacAddress();
        $hardwareType = $this->getRandomHardwareType();
        $connectionType = $this->getRandomConnectionType();
        $pin = $this->generatePin();
        $data = $this->getConnectedDevicesRequest($macAddress, $hardwareType, $connectionType, $pin);

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $equipmentData = [
            'external_id' => 'EQUIPMENT-'.uniqid(),
            'type' => 'ask',
            'branch_id' => 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8',
            'license_plate' => uniqid(),
            'connected_devices' => [$data],
        ];

        $I->callApiPortalEquipmentUpdate($this->createdEquipmentId, json_encode($equipmentData));

        $I->callApiPortalEquipmentSearch($equipmentData['external_id']);

        $equipments = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertNotEmpty($equipments, 'Equipment not found');

        $equipmentId = $equipments[0]['equipment_id'];

        $I->callApiPortalEquipmentItem($this->createdEquipmentId);
        $equipment = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->waitUntil(function (ApiTester $I) use ($equipment, $macAddress, $hardwareType, $connectionType, $pin): void {
            $connectedDevices = $equipment['connected_devices'];
            assert(is_array($connectedDevices));
            $I->assertContains($macAddress, $connectedDevices[0]);
            $I->assertContains($hardwareType, $connectedDevices[0]);
            $I->assertContains($connectionType, $connectedDevices[0]);
            $I->assertContains($pin, $connectedDevices[0]);
        });

        $I->callApiPortalEquipmentGet($equipmentId);
        $I->canSeeResponseContainsJson($equipmentData);
    }

    private function generateMacAddress(): string
    {
        $address = '01';
        for ($i = 0; $i < 5; ++$i) {
            $oct = strtoupper(dechex(mt_rand(0, 255)));
            strlen($oct) < 2 ? $address .= ":0$oct" : $address .= ":$oct";
        }

        return $address;
    }

    private function generatePin(): string
    {
        $digits = 4;

        return str_pad((string) rand(0, pow(10, $digits) - 1), $digits, '0', STR_PAD_LEFT);
    }

    private function getRandomHardwareType(): string
    {
        $arr = array_column(HardwareType::cases(), 'value');

        return $arr[array_rand($arr)];
    }

    private function getRandomConnectionType(): string
    {
        $arr = array_column(ConnectionType::cases(), 'value');

        return $arr[array_rand($arr)];
    }

    private function getConnectedDevicesRequest(string $macAddress, string $hardwareType, string $connectionType, string $pin): array
    {
        return [
            'mac_address' => $macAddress,
            'hardware_type' => $hardwareType,
            'connection_type' => $connectionType,
            'pin' => $pin,
        ];
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    public function canTerminateEquipmentSession(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();
        $sessionEquipmentLicensePlate = 'random-plate-'.uniqid();
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'session-stop-equipment-'.uniqid(),
            equipmentLicensePlate: $sessionEquipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));

        $I->amUsingDeviceWithId($deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2();
        $equipmentFromList = $this->getEquipmentFromList($I->grabDataFromResponseByJsonPath('$.items')[0], $sessionEquipmentLicensePlate);
        $this->sessionEquipmentId = $equipmentFromList['uuid'];
        $I->assertEquals('unequipped', $equipmentFromList['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->sessionEquipmentId,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I) use ($sessionEquipmentLicensePlate): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipmentFromList = $this->getEquipmentFromList($I->grabDataFromResponseByJsonPath('$.items')[0], $sessionEquipmentLicensePlate);
            $I->assertEquals('equipped', $equipmentFromList['status']);
        });
        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        $orderId = $I->grabDataFromResponseByJsonPath('$.orders[*].uuid')[0];
        $I->assertNotEmpty($orderId);

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentTerminateSession($this->sessionEquipmentId, expectedStatusCode: 201);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2($orderId, BookingFixtures::bookingStartRequestV2(), expectedStatusCode: 401);
    }

    #[Depends('canTerminateEquipmentSession')]
    public function canNotTerminateEquipmentSessionWithoutSession(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalEquipmentTerminateSession($this->sessionEquipmentId, expectedStatusCode: 404);
    }

    private function getEquipmentFromList(array $equipments, string $equipmentLicensePlate): array
    {
        foreach ($equipments as $set) {
            if (isset($set['name']) && $equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
