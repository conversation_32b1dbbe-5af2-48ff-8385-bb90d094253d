<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Symfony\Component\Uid\Uuid;

class PortalPushTrackingCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $tourExtId = '';
    protected string $orderExtId = '';
    protected string $tourName = '';

    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';
    protected string $nonExistingEquipmentUuid = '';

    /**
     * @var string[]
     */
    protected array $equipmentExtIds = [];

    public function canPushTracking(ApiTester $I): void
    {
        $this->createTourAndStartBooking($I);
        $I->callApiEquipmentGetCollectionV2();
        $equipmentIdWithoutSession = $I->grabDataFromResponseByJsonPath('$.items.1')[0];
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');

        $equipmentIds = [
            $this->equipmentUuid,
            $equipmentIdWithoutSession['uuid'],
        ];

        $data = $this->getPushTrackingRequestJson($equipmentIds);
        $I->callApiPortalPushTracking($data);

        $responseData = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertCount(2, $responseData['equipments']);
        $I->assertEquals(true, $responseData['equipments'][$this->equipmentUuid]);
        $I->assertEquals(false, $responseData['equipments'][$equipmentIdWithoutSession['uuid']]);
    }

    public function canNotPushTracking(ApiTester $I): void
    {
        $this->nonExistingEquipmentUuid = Uuid::v4()->toRfc4122();

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');

        $equipmentIds = [
            $this->nonExistingEquipmentUuid,
        ];

        $data = $this->getPushTrackingRequestJson($equipmentIds);
        $I->callApiPortalPushTracking($data, expectedStatusCode: 404);
    }

    private function createTourAndStartBooking(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            equipmentExtId2: 'random-unbooked-equipment-'.uniqid(),
            equipmentLicensePlate2: 'random-unbooked-plate-'.uniqid(),
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    private function getPushTrackingRequestJson(array $equipmentIds): string
    {
        $equipmentsJson = json_encode($equipmentIds);

        return <<<JSON
        {
            "equipments": $equipmentsJson
        }
        JSON;
    }
}
