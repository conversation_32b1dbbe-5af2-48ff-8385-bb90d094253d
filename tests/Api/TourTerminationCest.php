<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\Api\TerminationFixtures;
use App\Tests\Support\ApiTester;

class TourTerminationCest
{
    private string $tenant = 'pz';

    public function canTerminateTour(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();
        $equipmentLicensePlate = 'random-plate-'.uniqid();
        $staffExternalId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();

        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExternalId
        ));

        $I->amUsingDeviceWithId($deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExternalId, $this->tenant);
        $I->callApiUserStartSessionV2();

        // Ensure tour is created (initial status)
        $I->callApiTourGetV2($sapTourId);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);
        $terminationTemplate = $I->grabDataFromResponseByJsonPath('$.termination_templates[0]')[0];

        // Book equipment
        $I->callApiEquipmentGetCollectionV2(availability: 'all');
        $equipment = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$equipmentLicensePlate}')]")[0];
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $processingStatus = $I->grabDataFromResponseByJsonPath('$.booking')[0];
        $I->assertEquals('processed', $processingStatus);

        // Start the tour
        $I->callApiTourBookingV2(
            tourId: $sapTourId,
            data: BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });

        // Terminate the tour
        $I->callApiTourTerminationV2(
            $sapTourId,
            TerminationFixtures::terminateTourRequestV2($terminationTemplate),
        );

        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);

            // Validate it's terminated
            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('terminated', $tourStatus[0]);
        });

        $I->callApiTourTerminationHistoryV2($sapTourId);
        $terminations = $I->grabDataFromResponseByJsonPath('$.items');
        $I->assertCount(1, $terminations);
    }
}
