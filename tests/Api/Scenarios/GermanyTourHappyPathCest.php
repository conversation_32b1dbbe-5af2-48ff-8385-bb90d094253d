<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\Scenarios;

use App\Tests\Support\Helper\Scenario\Actor\AppActor;
use App\Tests\Support\Helper\Scenario\Actor\PortalActor;
use App\Tests\Support\Helper\Scenario\Actor\SapActor;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use App\Tests\Support\Helper\Scenario\TestScenario\TestScenario;
use Codeception\Attribute\Depends;

class GermanyTourHappyPathCest extends TestScenario
{
    private const string TOUR_ALIAS_MAIN = 'happyTour';
    private const string TOUR_ALIAS_SHARED_EQ_OUTSIDER = 'sharedEqHappyTour';
    private const string SESSION_MAIN = 'happyTourSession';
    private const string SESSION_USER_CHANGE = 'secondHappyTourSession';
    private const string SESSION_SHARED_EQ_OUTSIDER = 'thirdHappyTourSession';
    private const string STAFF_ONE_ALIAS = 'firstStaff';
    private const string STAFF_TWO_ALIAS = 'secondStaff';
    private const string STAFF_OUTSIDER_ALIAS = 'thirdStaff';
    private const string STAFF_IDLE = 'idleStaff';
    private const string EQUIPMENT_ONE_ALIAS = 'firstEq';
    private const string EQUIPMENT_TWO_SHARED_ALIAS = 'secondEq';
    private const string TENANT = 'pz';
    private const string BRANCH_MAIN_ALIAS = 'firstBranch';
    private const string PHONEBOOK_1_ALIAS = 'firstPhonebook';

    private const string PHONEBOOK_1_CONTACT_1_ALIAS = 'fistPhonebookContact1';
    private const string PHONEBOOK_1_CONTACT_2_ALIAS = 'fistPhonebookContact2';
    private const string PHONEBOOK_1_CONTACT_3_ALIAS = 'fistPhonebookContact3';

    private SapActor $sapGermany;
    private AppActor $driverMain;
    private AppActor $driverCo;
    private AppActor $driverThree;
    private PortalActor $portalAdmin;
    private PortalActor $portalUser;

    public function ingestTourData(): void
    {
        $this->tourContext->reset();
        $this->staffContext->reset();
        $this->equipmentContext->reset();
        $this->sessionContext->reset();
        $this->phonebookContext->reset();

        $this->sapGermany = new SapActor(tenant: 'pz', alias: 'germanSap', testScenario: $this)
            ->importTour(
                tourAlias: self::TOUR_ALIAS_MAIN,
                equipments: [['alias' => self::EQUIPMENT_ONE_ALIAS], ['alias' => self::EQUIPMENT_TWO_SHARED_ALIAS]],
                staff: [['alias' => self::STAFF_ONE_ALIAS], ['alias' => self::STAFF_TWO_ALIAS]],
                branchAlias: self::BRANCH_MAIN_ALIAS,
                orderCount: 2,
            )->importTour(
                tourAlias: self::TOUR_ALIAS_SHARED_EQ_OUTSIDER,
                equipments: [['alias' => self::EQUIPMENT_TWO_SHARED_ALIAS]],
                staff: [['alias' => self::STAFF_OUTSIDER_ALIAS], ['alias' => self::STAFF_IDLE]],
                branchAlias: self::BRANCH_MAIN_ALIAS,
            );
    }

    #[Depends('ingestTourData')]
    public function canLoginWithTestUsers(): void
    {
        $this->driverMain = new AppActor(tenant: self::TENANT, alias: self::STAFF_ONE_ALIAS, testScenario: $this)
            ->authenticateWithSessionDevice(sessionAlias: self::SESSION_MAIN)
            ->startHermesAppSession(self::SESSION_MAIN);

        $this->driverCo = new AppActor(tenant: self::TENANT, alias: self::STAFF_TWO_ALIAS, testScenario: $this)
            ->authenticateWithSessionDevice(sessionAlias: self::SESSION_MAIN)
            ->startHermesAppSession(self::SESSION_MAIN);

        $this->driverThree = new AppActor(tenant: self::TENANT, alias: self::STAFF_OUTSIDER_ALIAS, testScenario: $this)
            ->authenticateWithSessionDevice(sessionAlias: self::SESSION_SHARED_EQ_OUTSIDER)
            ->startHermesAppSession(self::SESSION_SHARED_EQ_OUTSIDER);

        $this->portalAdmin = new PortalActor(tenant: self::TENANT, alias: 'portal-admin-de', testScenario: $this)
            ->authenticate(password: 'portal-password');

        $this->portalUser = new PortalActor(tenant: self::TENANT, alias: 'portal-user-de', testScenario: $this)
            ->authenticate(password: 'portal-password');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canAccessProfileWithAppVersions(): void
    {
        $this->driverMain
            ->useNewAppVersion()
            ->getProfile()
            ->hasAppVersionCentrifugoMessage(expectedResult: false);

        $this->driverMain->useOutDatedAppVersion()
            ->getProfile(expectedStatusCode: 412);

        $this->driverMain->useOlderAppVersion()
            ->getProfile()
            ->hasAppVersionCentrifugoMessage()
            ->useNewAppVersion();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetNotificationAccess(): void
    {
        $this->driverMain->getNotificationAccess();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canPatchProfile(): void
    {
        $this->driverMain->patchProfile();
        $this->driverCo->patchProfile(colormode: 'dark', language: 'en_GB');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetDataProtectionNotice(): void
    {
        $this->driverMain->getDataProtectionNotice();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetPointsOfInterest(): void
    {
        $this->driverMain->getPointsOfInterest();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetSessionUserCollection(): void
    {
        $this->driverMain->getUserCollection();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canStoreAppLog(): void
    {
        $this->driverMain->sendAppLog();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canStoreAppNavigationLog(): void
    {
        $this->driverMain->sendAppNavigationLog();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetAppLanguageInformation(): void
    {
        $this->driverMain->getLanguageInformation();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetAppLanguageData(): void
    {
        $this->driverMain->getLanguageData('en_GB');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetPortalLanguageInformation(): void
    {
        $this->portalAdmin->getLanguageInformation();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetPortalLanguageData(): void
    {
        $this->portalAdmin->getLanguageData('en_GB');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetPortalWaypointLanguageData(): void
    {
        $this->portalAdmin->getWaypointLanguageData('en_GB');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetFaqLanguages(): void
    {
        $this->driverMain->getFaqForLanguage();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetFaqInfo(): void
    {
        $this->driverMain->getFaqInfo();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canCreateNewDeviceAccess(): void
    {
        $this->portalAdmin->createDeviceAccess(
            staffExternalId: $this->staffContext->getStaff(self::STAFF_ONE_ALIAS)->staffExtId,
            accessType: 'admin',
        );
    }

    #[Depends('canLoginWithTestUsers')]
    public function canNotCreateNewDeviceAccessForIdleStaff(): void
    {
        $this->portalAdmin->createDeviceAccess(
            staffExternalId: $this->staffContext->getStaff(self::STAFF_IDLE)->staffExtId,
            accessType: 'admin',
            expectedStatusCode: 404,
        );
    }

    #[Depends('canCreateNewDeviceAccess')]
    public function canReceiveAdminDeviceAccess(): void
    {
        $this->driverMain->hasDeviceAccess(['admin']);
        $this->driverCo->hasDeviceAccess(['admin']);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canReceiveEmptyDeviceAccess(): void
    {
        $this->driverThree->hasDeviceAccess([]);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canSendFeedbackWithoutComment(): void
    {
        $this->driverMain->sendFeedback(
            logFile: DataFaker::instance()->uuid(),
            screenshotFile: DataFaker::instance()->uuid(),
        );
    }

    #[Depends('canLoginWithTestUsers')]
    public function canSendFeedbackWithComment(): void
    {
        $this->driverMain->sendFeedback(
            logFile: DataFaker::instance()->uuid(),
            screenshotFile: DataFaker::instance()->uuid(),
            comment: DataFaker::instance()->sentence(),
        );
    }

    #[Depends('canLoginWithTestUsers')]
    public function canFindEquipmentsOfTour(): void
    {
        $this->driverCo->findEquipmentOfTour(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canFindMainTourInMainSession(): void
    {
        $this->driverCo->findInTourList(tourAlias: self::TOUR_ALIAS_MAIN, status: 'created');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canNotFindMainTourInOutsiderSession(): void
    {
        $this->driverThree->canNotFindInTourList(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canBookEquipmentsOfOutsiderTour(): void
    {
        $this->driverThree->bookInEquipmentOfTour(tourAlias: self::TOUR_ALIAS_SHARED_EQ_OUTSIDER);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canFindMainTourInOutsiderSessionBySharedEquipment(): void
    {
        $this->driverThree->findInTourList(tourAlias: self::TOUR_ALIAS_MAIN, status: 'created');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGiveFreeSharedEquipmentInOutsiderSession(): void
    {
        $this->driverThree->bookOutEquipmentOfTour(self::TOUR_ALIAS_SHARED_EQ_OUTSIDER);
    }

    #[Depends('canLoginWithTestUsers')]
    public function driverCanWriteDeviceMessage(): void
    {
        $this->driverMain->sendDeviceMessage();
    }

    #[Depends('driverCanWriteDeviceMessage')]
    public function portalAdminCanWriteDeviceMessage(): void
    {
        $this->portalAdmin
            ->writeDeviceMessageToEquipment(equipmentAlias: self::EQUIPMENT_ONE_ALIAS);
    }

    #[Depends('portalAdminCanWriteDeviceMessage')]
    public function canChangeStaffName(): void
    {
        $this->tourContext->getTour(self::TOUR_ALIAS_MAIN)->staff[self::STAFF_ONE_ALIAS]->firstName = 'Doctor';
        $this->tourContext->getTour(self::TOUR_ALIAS_MAIN)->staff[self::STAFF_ONE_ALIAS]->lastName = 'Who';
        $this->tourContext->getTour(self::TOUR_ALIAS_MAIN)->equipments[self::EQUIPMENT_ONE_ALIAS]->licencePlate = 'AB-CDEF';
        $this->sapGermany->reimportTour(self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canChangeStaffName')]
    public function canFindThreadInPortal(): void
    {
        $this->portalAdmin
            ->findThreadInSearch('Doctor Who')
            ->findThreadInSearch('AB-CDEF');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canBookEquipmentsOfTour(): void
    {
        $this->driverMain->bookInEquipmentOfTour(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canViewNewTour(): void
    {
        $this->driverMain->viewTourDetails(tourAlias: self::TOUR_ALIAS_MAIN, status: 'created');
    }

    #[Depends('canLoginWithTestUsers')]
    public function canViewSingleOrder(): void
    {
        $this->driverMain->viewSingleOrderDetails(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canStartTour(): void
    {
        $this->driverMain->bookTourStart(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canSendTrackData(): void
    {
        $this->driverMain
            ->sendTrackData(waypointCount: 20)
            ->sendTrackData(
                waypointCount: 20,
                tourExternalId: $this->tourContext->getTour(self::TOUR_ALIAS_MAIN)->tourExtId,
            )
            ->sendTrackData(
                waypointCount: 20,
                equipmentExternalId: $this->equipmentContext->getEquipment(self::EQUIPMENT_ONE_ALIAS)->equipmentExtId,
            )
            ->sendTrackData(
                waypointCount: 20,
                tourExternalId: $this->tourContext->getTour(self::TOUR_ALIAS_MAIN)->tourExtId,
                equipmentExternalId: $this->equipmentContext->getEquipment(self::EQUIPMENT_ONE_ALIAS)->equipmentExtId,
            );
    }

    #[Depends('canSendTrackData')]
    public function canSeeTrackingData(): void
    {
        $this->apiTester->waitUntil(
            function (): void {
                $this->portalAdmin->getTrackingList(
                    minDate: new \DateTimeImmutable(),
                    maxDate: new \DateTimeImmutable()->modify('+1 day'),
                    search: $this->tourContext->getTour(self::TOUR_ALIAS_MAIN)->tourExtId,
                    expectedListCount: 2,
                    maxWaypointCount: 20,
                );
            }
        );
    }

    #[Depends('canLoginWithTestUsers')]
    public function oneUserCanLeaveSession(): void
    {
        $this->driverCo->logoutUserFromSession();
    }

    #[Depends('oneUserCanLeaveSession')]
    public function secondUserCanStartOwnSession(): void
    {
        $this->driverCo
            ->authenticateWithSessionDevice(self::SESSION_USER_CHANGE)
            ->startHermesAppSession(self::SESSION_USER_CHANGE);
    }

    #[Depends('secondUserCanStartOwnSession')]
    public function canNotFindTourFromOtherSession(): void
    {
        $this->driverCo->canNotFindInTourList(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canFindNotAvailableEquipment(): void
    {
        $this->driverThree->canNotFindInEquipmentList(equipmentAlias: self::EQUIPMENT_ONE_ALIAS)
            ->findInEquipmentList(equipmentAlias: self::EQUIPMENT_ONE_ALIAS, allAvaileable: true);
    }

    #[Depends('secondUserCanStartOwnSession')]
    public function canNotViewTourFromOtherSession(): void
    {
        $this->driverCo->canNotViewTourDetails(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canStartTour')]
    public function canStartOrder(): void
    {
        $this->driverMain->bookOrderStart(tourAlias: self::TOUR_ALIAS_MAIN, orderPosNumber: 1);
    }

    #[Depends('canStartOrder')]
    public function canFocusOrder(): void
    {
        $this->driverMain->focusOrder(tourAlias: self::TOUR_ALIAS_MAIN, orderPosNumber: 1);
    }

    #[Depends('canStartTour')]
    public function canGetInterruptionTemplates(): void
    {
        $this->driverMain->getInteruptionTemplates();
    }

    #[Depends('canStartTour')]
    public function canGetInterruptions(): void
    {
        $this->driverMain->getInteruptionTemplates();
    }

    #[Depends('canLoginWithTestUsers')]
    public function canNotSeeTourInPortalTourFileSelection(): void
    {
        $this->portalAdmin
            ->searchTourInTourFileDd(searchedTourAlias: self::TOUR_ALIAS_MAIN, tourFoundInResponse: false)
            ->searchTourInTourFileDd(searchedTourAlias: self::TOUR_ALIAS_MAIN, tourFoundInResponse: false, hasFiles: '1')
            ->searchTourInTourFileDd(searchedTourAlias: self::TOUR_ALIAS_MAIN, tourFoundInResponse: true, hasFiles: '0');
    }

    #[Depends('canStartOrder')]
    public function canEndOrder(): void
    {
        $this->driverMain->bookOrderEnd(tourAlias: self::TOUR_ALIAS_MAIN, orderPosNumber: 1);
    }

    #[Depends('canEndOrder')]
    public function canNowSeeTourInPortalTourFileDd(): void
    {
        $this->portalAdmin
            ->searchTourInTourFileDd(searchedTourAlias: self::TOUR_ALIAS_MAIN, tourFoundInResponse: true)
            ->searchTourInTourFileDd(searchedTourAlias: self::TOUR_ALIAS_MAIN, tourFoundInResponse: true, hasFiles: '1')
            ->searchTourInTourFileDd(searchedTourAlias: self::TOUR_ALIAS_MAIN, tourFoundInResponse: true, hasFiles: '0');
    }

    #[Depends('canStartTour')]
    public function canEndTour(): void
    {
        $this->driverMain->bookTourEnd(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canEndTour')]
    public function canNotFindCompletedTourInMainSession(): void
    {
        $this->driverMain->canNotFindInTourList(tourAlias: self::TOUR_ALIAS_MAIN);
    }

    #[Depends('canLoginWithTestUsers')]
    public function canGetAccessibleBranches(): void
    {
        $this->portalAdmin->getAccessibleBranches();
    }

    #[Depends('canGetAccessibleBranches')]
    public function canCreatePhonebook(): void
    {
        $this->portalAdmin->createPhonebook(self::PHONEBOOK_1_ALIAS, [self::BRANCH_MAIN_ALIAS]);
    }

    #[Depends('canCreatePhonebook')]
    public function canFindPhonebook(): void
    {
        $this->portalAdmin->findPhonebook(self::PHONEBOOK_1_ALIAS);
    }

    #[Depends('canCreatePhonebook')]
    public function canCreatePhonebookContacts(): void
    {
        $this->portalAdmin->createPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_1_ALIAS)
            ->createPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_2_ALIAS)
            ->createPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_3_ALIAS);
    }

    #[Depends('canCreatePhonebookContacts')]
    public function canGetPhonebookWithContacts(): void
    {
        $this->portalAdmin->getPhonebook(self::PHONEBOOK_1_ALIAS);
    }

    #[Depends('canCreatePhonebook')]
    public function canPatchPhonebook(): void
    {
        $this->portalAdmin->patchPhonebook(self::PHONEBOOK_1_ALIAS, [self::BRANCH_MAIN_ALIAS])
            ->getPhonebook(self::PHONEBOOK_1_ALIAS);
    }

    #[Depends('canCreatePhonebookContacts')]
    public function canPatchPhonebookContact(): void
    {
        $this->portalAdmin->patchPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_1_ALIAS)
            ->getPhonebook(self::PHONEBOOK_1_ALIAS);
    }

    #[Depends('canCreatePhonebookContacts')]
    public function canSeePhonebookInApp(): void
    {
        $this->driverMain->findPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_1_ALIAS)
            ->findPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_2_ALIAS)
            ->findPhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_3_ALIAS);
    }

    #[Depends('canCreatePhonebookContacts')]
    public function canDeletePhonebookContact(): void
    {
        $this->portalAdmin->deletePhonebookContact(self::PHONEBOOK_1_ALIAS, self::PHONEBOOK_1_CONTACT_3_ALIAS)
            ->getPhonebook(self::PHONEBOOK_1_ALIAS);
    }

    #[Depends('canCreatePhonebook')]
    public function canNotAccessPhonebookWithoutBranchAccess(): void
    {
        $this->portalUser->getPhonebook(self::PHONEBOOK_1_ALIAS, 403);
    }

    #[Depends('canBookEquipmentsOfTour')]
    public function canBookOutEquipmentsOfTour(): void
    {
        $this->driverMain->bookOutEquipmentOfTour(tourAlias: self::TOUR_ALIAS_MAIN);
    }
}
