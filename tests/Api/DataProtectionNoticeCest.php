<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class DataProtectionNoticeCest
{
    public function cannotGetMissingDataProtectionNoticePzDe(ApiTester $I): void
    {
        $I->generateStaffAndAuthenticate('pz');
        $I->amUsingDeviceWithId(uniqid());
        $I->callApiUserStartSessionV2();
        $I->callApiDataProtectionNoticeV2('bg_BG', expectedStatusCode: 404);
    }
}
