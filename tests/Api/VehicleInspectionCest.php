<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Domain\Entity\Enum\Tenant;
use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\Api\SapSpainTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class VehicleInspectionCest
{
    private string $equipmentLicensePlate;
    private string $equipmentExternalId;
    private string $staffExternalId;

    public function vehicleInspectionReportScenario1(ApiTester $I): void
    {
        // Create user, equipment, start session and book equipment
        $this->createEquipment($I, EquipmentType::GAK);
        $deviceId = 'device-id-'.uniqid();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipmentId = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$this->equipmentLicensePlate}')].uuid")[0];
        $I->callApiEquipmentBookingV2(equipmentId: $equipmentId, data: BookingFixtures::bookingStartRequestV2());

        // Set external systems expectation for the defect components
        $mappingId = $this->setExternalSystemDefectExpectation($I, [
            'defect-components' => ['03-headlight'],
            'equipment-id' => $this->equipmentExternalId,
            'reported-by' => $this->staffExternalId,
        ]);

        // Get vehicle inspection template and submit report
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $report = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'working',
            description: 'test description',
        );
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($report));

        // Verify that the template now has the report values as previously submitted
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];

        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $componentTemplate) {
                $expected = $componentTemplate['critical'] ? 'working' : 'broken';
                $I->assertEquals(
                    $expected,
                    $componentTemplate['last_value']['value'],
                    "Component {$componentTemplate['external_id']} should have value '{$expected}'",
                );
            }
        }

        // Portal sees the equipment as defect
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentGet($equipmentId);
        $I->assertEquals('defect', $I->grabDataFromResponseByJsonPath('$.condition')[0]);

        // Validate that the vir device message was received
        $currentTime = new \DateTimeImmutable()->getTimestamp();
        $I->callApiPortalDeviceMessageThreadList(perPage: 100);
        $thread = $I->grabDataFromResponseByJsonPath('$.items[?(@.last_message_excerpt == "'.$report['description'].'")]')[0];
        $I->assertNotEmpty($thread, 'Thread not found');
        $updatedAt = new \DateTimeImmutable($thread['updated_at'])->getTimestamp();
        $I->assertGreaterThan($currentTime - 6, $updatedAt);
        $I->assertLessThan($currentTime + 6, $updatedAt);

        $I->assertEquals('equipment', $thread['target']);

        $I->callApiPortalDeviceMessagesInThread($thread['id']);
        $message = $I->grabDataFromResponseByJsonPath('$.items[?(@.message == "'.$report['description'].'")]')[0];
        $I->assertNotEmpty($message, 'Message not found');

        // Verify that the external system was called with the correct data
        $I->wireMockVerifyRequestMapping([$mappingId]);

        usleep(500000); // Ensure the timestamp is updated, so the next report can be at least one second later

        // Submit another report with change to working
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, 'pz');
        $report = $this->createReportFromTemplate($template);
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($report));

        // Verify that the template now has the updated report values to working_unconfirmed
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];

        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $componentTemplate) {
                $expected = $componentTemplate['critical'] ? 'working' : 'working_unconfirmed';
                $I->assertEquals(
                    $expected,
                    $componentTemplate['last_value']['value'],
                    "Component {$componentTemplate['external_id']} should have value '{$expected}'"
                );
            }
        }

        // Portal sees the equipment as clarify
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentGet($equipmentId);
        $I->assertEquals('clarify', $I->grabDataFromResponseByJsonPath('$.condition')[0]);

        usleep(500000); // Ensure the timestamp is updated, so the next report can be at least one second later

        // Portal can see the outcome as warning, because the broken is not critical, the critical is working
        $I->callApiPortalVehicleInspectionList($equipmentId);
        $I->assertEquals('{{vir_outcome/warning}}', $I->grabDataFromResponseByJsonPath('$.items[0].label')[0]);
        $I->assertEquals('warning', $I->grabDataFromResponseByJsonPath('$.items[0].outcome')[0]);
        $reportId = $I->grabDataFromResponseByJsonPath('$.items[0].report_id')[0];

        // Portal can now retrieve the report
        $I->callApiPortalVehicleInspectionReport($equipmentId, $reportId);
        $reportedComponentGroups = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];

        foreach ($reportedComponentGroups as $reportedComponentGroup) {
            foreach ($reportedComponentGroup['components'] as $reportedComponent) {
                $expected = $reportedComponent['critical'] ? 'working' : 'working_unconfirmed';
                $I->assertEquals(
                    $expected,
                    $reportedComponent['value']['value'],
                    "Component {$reportedComponent['external_id']} should have value '{$expected}'"
                );
            }
        }

        // Portal admin can set the component state to working
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $report = $this->createReportFromTemplate($template);
        $I->callApiPortalVehicleInspectionSubmitReport($equipmentId, json_encode($report));

        // Verify that the template now has the report values to working
        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];

        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $componentTemplate) {
                $I->assertEquals(
                    'working',
                    $componentTemplate['last_value']['value'],
                    "Component {$componentTemplate['external_id']} should have value 'working'"
                );
            }
        }

        // Portal sees the equipment as working
        $I->callApiPortalEquipmentGet($equipmentId);
        $I->assertEquals('working', $I->grabDataFromResponseByJsonPath('$.condition')[0]);

        // Now the portal admin can see the outcome as passed, everything is working
        $I->callApiPortalVehicleInspectionList($equipmentId);
        $I->assertEquals('{{vir_outcome/pass}}', $I->grabDataFromResponseByJsonPath('$.items[0].label')[0]);
        $I->assertEquals('pass', $I->grabDataFromResponseByJsonPath('$.items[0].outcome')[0]);
    }

    public function canNotDestroySpanishEquipment(ApiTester $I): void
    {
        // Create user, equipment, start session and book equipment
        $this->createEquipment(I: $I, tenant: Tenant::SPAIN);
        $deviceId = 'device-id-'.uniqid();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, 'pze');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipmentId = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$this->equipmentLicensePlate}')].uuid")[0];
        $I->callApiEquipmentBookingV2(equipmentId: $equipmentId, data: BookingFixtures::bookingStartRequestV2());

        $I->callApiVehicleInspectionTemplate($equipmentId, debug: false);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        foreach ($template[0]['components'] as $component) {
            $I->assertEquals('working', $component['last_value']['value']);
        }

        $report = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
        );
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($report));

        // Verify that the template now is still green
        $I->callApiVehicleInspectionTemplate($equipmentId, debug: false);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $component) {
                $I->assertEquals('working', $component['last_value']['value']);
            }
        }

        // Even the mighty portal-admin can not destroy this equipment
        $I->amAuthenticatedAsPortalUser('portal-admin-es', 'portal-admin-es', 'portal-password');
        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        foreach ($template[0]['components'] as $component) {
            $I->assertEquals('working', $component['last_value']['value']);
        }
        $report = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
        );
        $I->callApiPortalVehicleInspectionSubmitReport($equipmentId, json_encode($report));
        // Verify that the template still has the report values to working
        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $componentTemplate) {
                $I->assertEquals(
                    'working',
                    $componentTemplate['last_value']['value'],
                    "Component {$componentTemplate['external_id']} should have value 'working'"
                );
            }
        }
        // Portal sees the equipment as working after all sent defects
        $I->callApiPortalEquipmentGet($equipmentId);
        $I->assertEquals('working', $I->grabDataFromResponseByJsonPath('$.condition')[0]);
    }

    public function cannotSubmitReportWithMissingComponentStates(ApiTester $I): void
    {
        $this->createEquipment($I);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentSearch($this->equipmentLicensePlate);

        $equipmentId = $I->grabDataFromResponseByJsonPath('$.items[0].equipment_id')[0];

        $I->callApiPortalVehicleInspectionSubmitReport(
            equipmentId: $equipmentId,
            data: json_encode([
                'components_state' => [],
            ]),
            expectedStatusCode: 422
        );

        $I->seeResponseContainsJson(['title' => 'Component state for external id "01-headlight" not found']);
    }

    public function cannotSubmitReportWithInvalidComponentStates(ApiTester $I): void
    {
        $this->createEquipment($I);

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentSearch($this->equipmentLicensePlate);

        $equipmentId = $I->grabDataFromResponseByJsonPath('$.items[0].equipment_id')[0];

        $I->callApiPortalVehicleInspectionSubmitReport(
            equipmentId: $equipmentId,
            data: json_encode([
                'equipment_id' => $equipmentId,
                'components_state' => [
                    [
                        'external_id' => '01-headlight',
                        'value' => [
                            'type' => 'working_state',
                            'value' => 'working_unconfirmed',
                        ],
                    ],
                ],
            ]),
            expectedStatusCode: 422
        );

        $I->seeResponseContainsJson(['message' => 'Invalid value, only (working, broken) are allowed']);
    }

    public function vehicleInspectionReportWithDescriptionAndFilesHermesApp(ApiTester $I): void
    {
        // Create user, equipment, start session and book equipment
        $this->createEquipment($I);
        $deviceId = 'device-id-'.uniqid();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipmentId = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$this->equipmentLicensePlate}')].uuid")[0];
        $I->callApiEquipmentBookingV2(equipmentId: $equipmentId, data: BookingFixtures::bookingStartRequestV2());

        // Upload files
        $file1Name = Uuid::v4()->toRfc4122();
        $file2Name = Uuid::v4()->toRfc4122();
        $file1Path = 'vehicle-inspection/'.$file1Name;
        $file2Path = 'vehicle-inspection/'.$file2Name;

        $I->callApiFileUploadInDirectoryV2('vehicle-inspection', $file1Name, 'Test content file 1');
        $I->callApiFileUploadInDirectoryV2('vehicle-inspection', $file2Name, 'Test content file 2');

        // Set external systems expectation for the defect components
        $mappingId = $this->setExternalSystemDefectExpectation($I, [
            'defect-components' => ['01-headlight', '02-tyres'],
            'equipment-id' => $this->equipmentExternalId,
            'reported-by' => $this->staffExternalId,
        ]);

        // Get vehicle inspection template and submit report
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $reportDescription = 'This is a test description for the report.';
        $reportPayload = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'broken',
            description: $reportDescription,
            uploadedFiles: [$file1Path, $file2Path]
        );
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($reportPayload));

        // Verify that the external system was called with the correct data
        $I->wireMockVerifyRequestMapping([$mappingId]);

        // Cannot submit report with files in wrong directory
        $file1Name = Uuid::v4()->toRfc4122();
        $file1Path = 'app-user-files/'.$file1Name;
        $I->callApiFileUploadInDirectoryV2('app-user-files', $file1Name, 'Test content file 1 wrong dir');

        $I->callApiVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $reportPayload = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'broken',
            description: $reportDescription,
            uploadedFiles: [$file1Path]
        );
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($reportPayload), 422);
        $I->seeResponseContainsJson([
            'violations' => [
                ['message' => sprintf('File "%s" is not in the allowed directory.', $file1Path)],
            ],
        ]);

        // Cannot submit report with non-existent file
        $nonExistentFilePath = 'vehicle-inspection/'.Uuid::v4()->toRfc4122();
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $reportPayload = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'broken',
            description: $reportDescription,
            uploadedFiles: [$nonExistentFilePath]
        );
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($reportPayload), 422);
        $I->seeResponseContainsJson([
            'violations' => [
                ['message' => sprintf('File "%s" does not exist.', $nonExistentFilePath)],
            ],
        ]);
    }

    #[Depends('vehicleInspectionReportWithDescriptionAndFilesHermesApp')]
    public function vehicleInspectionReportWithDescriptionAndFilesPortal(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentSearch($this->equipmentLicensePlate);
        $equipmentId = $I->grabDataFromResponseByJsonPath('$.items[0].equipment_id')[0];

        // Upload files via Portal API
        $file1Name = Uuid::v4()->toRfc4122();
        $file2Name = Uuid::v4()->toRfc4122();
        $file1Path = 'vehicle-inspection/'.$file1Name;
        $file2Path = 'vehicle-inspection/'.$file2Name;

        $I->callApiPortalFilePut('vehicle-inspection', $file1Name, 'Test content portal file 1');
        $I->callApiPortalFilePut('vehicle-inspection', $file2Name, 'Test content portal file 2');

        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $reportDescription = 'Portal API test description.';
        $reportPayload = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'broken',
            description: $reportDescription,
            uploadedFiles: [$file1Path, $file2Path]
        );
        $I->callApiPortalVehicleInspectionSubmitReport($equipmentId, json_encode($reportPayload));

        // Now the portal sees the outcome as failed, because both components are broken
        $I->callApiPortalVehicleInspectionList($equipmentId, new \DateTimeImmutable()->format('Y-m-d'));
        $I->assertEquals('{{vir_outcome/fail}}', $I->grabDataFromResponseByJsonPath('$.items[0].label')[0]);
        $I->assertEquals('fail', $I->grabDataFromResponseByJsonPath('$.items[0].outcome')[0]);

        // Portal sees the equipment as broken
        $I->callApiPortalEquipmentGet($equipmentId);
        $I->assertEquals('broken', $I->grabDataFromResponseByJsonPath('$.condition')[0]);

        // Cannot submit report with files in wrong directory
        $file1Name = Uuid::v4()->toRfc4122();
        $I->callApiPortalFilePut('device-message', $file1Name, 'Test content file in wrong dir portal');
        $wrongFilePath = 'device-message/'.$file1Name;
        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $reportPayload = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'broken',
            description: $reportDescription,
            uploadedFiles: [$wrongFilePath]
        );
        $I->callApiPortalVehicleInspectionSubmitReport($equipmentId, json_encode($reportPayload), 422);
        $I->seeResponseContainsJson([
            'violations' => [
                ['message' => sprintf('File "%s" is not in the allowed directory.', $wrongFilePath)],
            ],
        ]);

        // Cannot submit report with non-existent file
        $nonExistentFilePath = 'vehicle-inspection/'.Uuid::v4()->toRfc4122();
        $I->callApiPortalVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $reportPayload = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'broken',
            description: $reportDescription,
            uploadedFiles: [$nonExistentFilePath]
        );
        $I->callApiPortalVehicleInspectionSubmitReport($equipmentId, json_encode($reportPayload), 422);
        $I->seeResponseContainsJson([
            'violations' => [
                ['message' => sprintf('File "%s" does not exist.', $nonExistentFilePath)],
            ],
        ]);
    }

    public function externalSystemCanRepairEquipment(ApiTester $I): void
    {
        // Create user, equipment, start session and book equipment
        $this->createEquipment($I, EquipmentType::GAK);
        $deviceId = 'device-id-'.uniqid();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipmentId = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$this->equipmentLicensePlate}')].uuid")[0];
        $I->callApiEquipmentBookingV2(equipmentId: $equipmentId, data: BookingFixtures::bookingStartRequestV2());

        // Get vehicle inspection template and submit report
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $template = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];
        $report = $this->createReportFromTemplate(
            template: $template,
            state: 'broken',
            criticalState: 'working',
        );
        $I->callApiVehicleInspectionSubmitReport($equipmentId, json_encode($report));

        // Verify that the template now has a broken component
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];

        $repairComponents = [];

        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $componentTemplate) {
                if (true === $componentTemplate['critical']) {
                    $I->assertEquals('working', $componentTemplate['last_value']['value']);
                } else {
                    $I->assertEquals('broken', $componentTemplate['last_value']['value']);
                    $repairComponents[] = $componentTemplate['external_id'];
                }
            }
        }

        $data = [
            'equipmentExternalId' => $this->equipmentExternalId,
            'repairedComponents' => array_map(
                fn (string $componentExternalId): array => [
                    'externalId' => $componentExternalId,
                    'datetime' => new \DateTimeImmutable()->format(\DateTimeInterface::RFC3339_EXTENDED),
                ],
                $repairComponents
            ),
        ];
        sleep(1); // Ensure the timestamp is updated, so the next report can be at least one second later
        $I->callGermanyVirRepairReport(data: json_encode($data), debug: false);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, 'pz');
        // Now, the component must work again
        $I->callApiVehicleInspectionTemplate($equipmentId);
        $componentGroupTemplates = $I->grabDataFromResponseByJsonPath('$.component_groups')[0];

        foreach ($componentGroupTemplates as $componentGroupTemplate) {
            foreach ($componentGroupTemplate['components'] as $componentTemplate) {
                $I->assertEquals('working', $componentTemplate['last_value']['value']);
            }
        }
    }

    private function createEquipment(
        ApiTester $I,
        EquipmentType $equipmentType = EquipmentType::ASK,
        Tenant $tenant = Tenant::GERMANY,
    ): void {
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExternalId = 'random-equipment-'.uniqid();
        $this->staffExternalId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();

        switch ($tenant) {
            case Tenant::GERMANY:
                $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourEquipmentCreate(
                    externalId: $this->equipmentExternalId,
                    licensePlate: $this->equipmentLicensePlate,
                    type: $equipmentType,
                    staffExtId: $this->staffExternalId,
                ));
                break;
            case Tenant::SPAIN:
                $I->callSapSpainTour(SapSpainTourFixtures::tourEquipmentCreate(
                    externalId: $this->equipmentExternalId,
                    licensePlate: $this->equipmentLicensePlate,
                    type: $equipmentType,
                    staffExtId: $this->staffExternalId,
                ));
                break;
            default:
                throw new \Exception('not implemented yet');
        }
    }

    private function createReportFromTemplate(
        mixed $template,
        string $state = 'working',
        string $criticalState = 'working',
        ?string $description = null,
        ?array $uploadedFiles = null,
    ): array {
        $report = [
            'components_state' => [],
        ];

        if (null !== $description) {
            $report['description'] = $description;
        }

        if (null !== $uploadedFiles) {
            $report['uploaded_files'] = $uploadedFiles;
        }

        foreach ($template as $componentGroup) {
            foreach ($componentGroup['components'] as $component) {
                $report['components_state'][] = [
                    'external_id' => $component['external_id'],
                    'value' => [
                        'type' => $component['value_type'],
                        'value' => $component['critical'] ? $criticalState : $state,
                    ],
                ];
            }
        }

        return $report;
    }

    /**
     * @param array{defect-components: array<string>, equipment-id: string, reported-by: string} $data
     */
    private function setExternalSystemDefectExpectation(ApiTester $I, array $data): string
    {
        return $I->wireMockAddJsonRequestMapping(
            method: 'POST',
            url: '/vir-germany/report',
            requestBodyJson: [
                'equipmentExternalId' => $data['equipment-id'],
                'reportedBy' => $data['reported-by'],
                'defectComponents' => array_map(
                    static fn (string $component): array => ['externalId' => $component],
                    $data['defect-components'],
                ),
            ],
        );
    }
}
