<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Domain\Entity\Enum\WaypointColor;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Faker\Factory;

class MastertourCest
{
    private const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';

    private array $mastertourTemplateExternalIds = [];

    private ?string $mastertourTemplateId = null;

    private array $waypointIds = [];
    private int $distance = 0;

    private string $tenant = 'pz';

    public function canGetMastertourByExternalId(ApiTester $I): void
    {
        $externalId = $this->createMastertour($I);
        $deviceId = 'random-device-id-'.uniqid();
        $I->generateStaffAndAuthenticate($this->tenant);
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiMastertourGetV2($externalId);
        $response = $I->grabDataFromResponseByJsonPath('$')[0];

        $I->assertEquals($externalId, $response['external_id']);
        $I->assertCount(100, $response['waypoints']);
        $this->mastertourTemplateId = $response['id'];

        foreach ($response['waypoints'] as $key => $waypoint) {
            $this->mastertourTemplateExternalIds[$key] = $externalId;
            $this->waypointIds[$key] = (string) $response['waypoints'][$key]['id'];
        }
    }

    #[Depends('canGetMastertourByExternalId')]
    public function canSendMastertourProgress(ApiTester $I): void
    {
        $deviceId = 'random-device-id-'.uniqid();

        $tourExtId = 'random-tour-id-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $orderExtId = 'random-order-id-'.uniqid();
        $tour1Name = 'random-tour-name-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExtId,
        ));
        $I->amUsingDeviceWithId($deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipmentFromList = $this->getEquipmentFromList($I->grabDataFromResponseByJsonPath('$.items')[0], $equipmentLicensePlate);
        $equipmentId = $equipmentFromList['uuid'];
        $I->assertEquals('unequipped', $equipmentFromList['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipmentId,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I) use ($equipmentLicensePlate): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipmentFromList = $this->getEquipmentFromList($I->grabDataFromResponseByJsonPath('$.items')[0], $equipmentLicensePlate);
            $I->assertEquals('equipped', $equipmentFromList['status']);
        });

        $jsonData = $this->getMastertourProgressRequestJson($this->mastertourTemplateExternalIds, $this->waypointIds);
        $I->callApiMastertourProgressV2($jsonData);
    }

    #[Depends('canGetMastertourByExternalId')]
    public function canAssignMastertourToTour(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            mastertourExternalIds: [$this->mastertourTemplateExternalIds[array_key_first($this->mastertourTemplateExternalIds)]],
        ));
    }

    public function canImportTourWithInvalidMastertourId(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            mastertourExternalIds: ['invalid-id'],
        ));
    }

    #[Depends('canSendMastertourProgress')]
    public function canGetExistingMastertourProgressWithDate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMastertourProgressListWithDate(
            $this->mastertourTemplateId,
            new \DateTimeImmutable()->format('Y-m-d'),
        );
        $responseItems = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertGreaterThanOrEqual(1, $responseItems);

        $data = $I->grabDataFromResponseByJsonPath('$.items')[0][0];
        $I->assertIsArray($data['equipment_actions']);

        foreach ($data['equipment_actions'] as $equipmentAction) {
            $I->assertIsArray($equipmentAction);
            $I->assertArrayHasKey('licensePlate', $equipmentAction);
            $I->assertNotEmpty($equipmentAction['licensePlate']);
        }
    }

    #[Depends('canSendMastertourProgress')]
    public function canGetExistingMastertourProgress(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMastertourProgressList(
            $this->mastertourTemplateId,
            new \DateTimeImmutable()->format('Y-m-d'),
        );
        $responseItems = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertGreaterThanOrEqual(1, $responseItems);

        $data = $I->grabDataFromResponseByJsonPath('$.items')[0][0];
        $I->assertIsArray($data['equipment_actions']);

        foreach ($data['equipment_actions'] as $equipmentAction) {
            $I->assertIsArray($equipmentAction);
            $I->assertArrayHasKey('licensePlate', $equipmentAction);
            $I->assertNotEmpty($equipmentAction['licensePlate']);
        }
    }

    public function cannotGetMastertourProgressForInvalidTemplate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMastertourProgressListWithDate(
            '00000000-0000-0000-0000-000000000000',
            new \DateTimeImmutable()->format('Y-m-d'),
            404
        );
    }

    public function cannotGetMastertourProgressForInvalidDate(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalMastertourProgressListWithDate(
            $this->mastertourTemplateId,
            'dddddd',
            404
        );
    }

    private function createMastertour(ApiTester $I): string
    {
        $externalId = uniqid().' @+%/\\';
        $name = md5((string) rand(0, 100));
        $branchId = self::BRANCH_ID;

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $data = $this->getMastertourTemplateCreateJson($branchId, $externalId, $name);
        $I->callApiPortalMastertourTemplateCreate($data);
        $responseItems = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals($this->distance, $responseItems['distance_in_meters']);

        return $externalId;
    }

    private function getMastertourTemplateCreateJson(string $branchId, string $externalId, string $name): string
    {
        $faker = Factory::create();

        $ret = [
            'branch_id' => $branchId,
            'external_id' => $externalId,
            'name' => $name,
            'waypoints' => [],
        ];

        for ($i = 0; $i < 100; ++$i) {
            $distanceToPreviousWaypointMeter = $faker->randomNumber(2, true);
            $this->distance = $this->distance + $distanceToPreviousWaypointMeter;
            $ret['waypoints'][] = [
                'longitude' => $faker->latitude(),
                'latitude' => $faker->longitude(),
                'distance_to_previous_waypoint_meters' => $distanceToPreviousWaypointMeter,
                'bearing' => $faker->numberBetween(0, 360),
                'description' => '{{'.$faker->word().'}}',
                'text_to_speech' => $faker->boolean(),
                'color' => $faker->randomElement(WaypointColor::class)->value,
            ];
        }

        return json_encode($ret);
    }

    private function getMastertourProgressRequestJson(array $mastertourTemplateExternalIds, array $waypointIds): string
    {
        $timestamp = (new \DateTimeImmutable())->format(\DateTimeInterface::RFC3339_EXTENDED);

        $items = [];
        foreach ($mastertourTemplateExternalIds as $key => $mastertourTemplateExternalId) {
            $items[] = [
                'timestamp' => $timestamp,
                'mastertour_template_external_id' => $mastertourTemplateExternalId,
                'waypoint_id' => $waypointIds[$key],
            ];
        }

        $jsonArray = [
            'progresses' => $items,
        ];

        return json_encode($jsonArray, JSON_PRETTY_PRINT);
    }

    private function getEquipmentFromList(array $equipments, string $equipmentLicensePlate): array
    {
        foreach ($equipments as $set) {
            if (isset($set['name']) && $equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
