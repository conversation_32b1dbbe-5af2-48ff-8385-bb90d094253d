<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class InterruptionGetCest
{
    public function cannotGetInterruptionIfNotLoggedIn(ApiTester $I): void
    {
        $deviceId = 'random-device-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiInterruptionGetV2(expectedStatusCode: 401);
    }

    public function cannotGetInterruptionTemplatesIfNotLoggedIn(ApiTester $I): void
    {
        $deviceId = 'random-device-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiInterruptionTemplateGetV2(expectedStatusCode: 401);
    }
}
