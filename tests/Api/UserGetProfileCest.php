<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class UserGetProfileCest
{
    public function cannotGetUserProfileIfNotLoggedIn(ApiTester $I): void
    {
        $deviceId = 'random-device-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserProfileV2(expectedStatusCode: 401);
    }
}
