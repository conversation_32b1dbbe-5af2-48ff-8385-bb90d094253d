<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\InterruptionFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class SapGermanyReturnInInterruptionCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $clientUuid = '';
    protected string $interruptionTemplateId = '';
    protected string $tourExtId = '';
    protected string $orderExtId = '';
    protected string $tourName = '';
    protected string $device1 = '';
    protected string $device2 = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected array $equipmentExtIds = [
        // '10004635',
    ];

    public function canInterruptStartedTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->device1 = 'random-device1-unique-id-'.uniqid();
        $this->device2 = 'random-device2-unique-id-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
        $wireMockMappingIds = [
            $this->setInterruptionSapMapping($I, 'started'),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1);
        $I->callApiUserStartSessionV2();

        // equipment start
        $I->callApiEquipmentGetCollectionV2();
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $equipment = array_values(array_filter($data, fn (array $eqData): bool => $eqData['name'] === $this->equipmentLicensePlate))[0];
        $I->assertNotEmpty($equipment['uuid']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $equipment = array_values(array_filter($data, fn (array $eqData): bool => $eqData['name'] === $this->equipmentLicensePlate))[0];
            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(),
        );

        // Start the interruption
        $I->callApiInterruptionTemplateGetV2();
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $staffInterruptions = array_values(array_filter($data, fn (array $entry): bool => 'staff' === $entry['source']));
        $I->assertNotEmpty($staffInterruptions);
        $this->interruptionTemplateId = $staffInterruptions[0]['template_uuid'];
        $I->assertNotEmpty($this->interruptionTemplateId);

        $this->clientUuid = Uuid::v4()->toRfc4122();
        $I->callApiStaffInterruptionV2(
            interruptionTemplateId: $this->interruptionTemplateId,
            data: InterruptionFixtures::bookingInterruptionStartRequestV2($this->clientUuid),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiInterruptionGetV2(includeNotCompleted: true);
            $interruptions = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertCount(1, $interruptions);
            $I->assertEquals('started', $interruptions[0]['status']);
        });
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canInterruptStartedTour')]
    public function canSwitchDevice(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $this->setInterruptionSapMapping($I, 'completed'),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device2);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('returned', $tourStatus[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function setInterruptionSapMapping(ApiTester $I, string $status): string
    {
        return $I->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'interruption',
            subTypeExternalId: '${json-unit.any-string}',
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: $status,
        );
    }
}
