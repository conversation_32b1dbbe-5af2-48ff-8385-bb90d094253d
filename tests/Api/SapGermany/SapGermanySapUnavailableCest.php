<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanySapUnavailableCest
{
    private string $sapTourId = '';
    private string $orderId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $deviceId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $staffExtId = '';
    /**
     * @var string[]
     */
    private array $equipmentExtIds = [
    ];
    private string $equipmentUuid = '';
    private string $tenant = 'pz';

    public function canStartTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];

        $wireMockMappingIds = $I->wireMockSetSapGermanyStatusTourStartMappings(
            tourExtId: $this->tourExtId,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            responseCode: 503,
        );

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);
        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartTour')]
    public function canStartOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrder($I);
        $I->callApiOrderBookingV2(
            orderId: $this->orderId,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $this->validateOrderStatus($I, 'started');
    }

    #[Depends('canStartOrder')]
    public function canEndOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2(
            orderId: $this->orderId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $this->validateOrderStatus($I, 'completed');
    }

    #[Depends('canEndOrder')]
    public function canEndTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        // End the tour
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's ended
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('completed', $tourStatus[0]);
        });
    }

    #[Depends('canEndTour')]
    public function canRetrySapCalls(ApiTester $I): void
    {
        $wireMockMappingIds = $I->wireMockSetSapGermanyStatusTourStartMappings(
            $this->tourExtId,
            [$this->staffExtId],
            $this->equipmentExtIds
        );
        $wireMockMappingIds[] = $I->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'order',
            objectExternalId: $this->orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'started'
        );
        $wireMockMappingIds[] = $I->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'order',
            objectExternalId: $this->orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'completed'
        );
        $wireMockMappingIds[] = $I->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'completed'
        );

        $wireMockMappingIds[] = $I->wireMockAddSapGermanyConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            orderLocations: [],
            termination: null,
            notes: null
        );

        $I->retrySapCalls($this->staffExtId, 'pz');

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    private function setCreatedOrder(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);

        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');

        $I->assertEquals('created', $firstOrderStatus[0]);

        $this->orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }
}
