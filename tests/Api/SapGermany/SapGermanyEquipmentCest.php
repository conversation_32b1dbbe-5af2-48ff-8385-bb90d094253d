<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use JetBrains\PhpStorm\NoReturn;

class SapGermanyEquipmentCest
{
    private string $tenant = 'pz';
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $deviceId1 = '';
    private string $deviceId2 = '';
    private string $staffExtId1 = '';
    private string $staffExtId2 = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $equipmentExtId2 = '';
    private string $equipmentLicensePlate2 = '';
    protected string $equipmentUuid = '';

    /**
     * @throws \Exception
     */
    #[NoReturn]
    public function canBookAndCountEquipmentCorrectly(ApiTester $I): void
    {
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId1 = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->staffExtId1 = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->staffExtId2 = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId1,
            staffExternalId2: $this->staffExtId2,
        ));
        $I->amUsingDeviceWithId($this->deviceId1);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId1, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2();
        $equipmentListBeforeBookingStart = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertGreaterOrEquals(1, $equipmentListBeforeBookingStart);
        $bookingEquipment = [];

        foreach ($equipmentListBeforeBookingStart as $equipment) {
            if ($equipment['license_plate'] === $this->equipmentLicensePlate) {
                $bookingEquipment = $equipment;
                break;
            }
        }

        $I->callApiEquipmentBookingV2(
            equipmentId: $bookingEquipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $processingStatus = $I->grabDataFromResponseByJsonPath('$.booking')[0];
        $I->assertEquals('processed', $processingStatus);
        $I->callApiEquipmentGetCollectionV2();
        $equipmentListAfterBookingStart = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$this->equipmentLicensePlate}')]");
        $I->assertCount(1, $equipmentListAfterBookingStart);
        $I->assertEquals('equipped', $equipmentListAfterBookingStart[0]['status']);

        $I->callApiEquipmentBookingV2(
            equipmentId: $bookingEquipment['uuid'],
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->callApiEquipmentGetCollectionV2();
        $equipmentListAfterBookingEnd = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$this->equipmentLicensePlate}')]");
        $I->assertCount(1, $equipmentListAfterBookingEnd);
        $I->assertEquals('unequipped', $equipmentListAfterBookingEnd[0]['status']);
    }

    /**
     * @throws \Exception
     */
    #[NoReturn]
    public function canSeeEquipmentIsBlockedCorrectly(ApiTester $I): void
    {
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId1 = 'random-device-'.uniqid();
        $this->deviceId2 = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->staffExtId1 = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->staffExtId2 = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId1,
            staffExternalId2: $this->staffExtId2,
        ));

        $I->amUsingDeviceWithId($this->deviceId1);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId1, $this->tenant);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);
        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);
        $I->callApiEquipmentGetCollectionV2();
        $equipmentListBeforeBookingStart = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertGreaterOrEquals(1, $equipmentListBeforeBookingStart);

        foreach ($equipmentListBeforeBookingStart as $equipment) {
            if ($equipment['license_plate'] === $this->equipmentLicensePlate) {
                $I->assertEquals(false, $equipment['blocked']);
                $this->equipmentUuid = $equipment['uuid'];
                $I->callApiEquipmentBookingV2(
                    equipmentId: $this->equipmentUuid,
                    data: BookingFixtures::bookingStartRequestV2(),
                );
            }
        }

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId2, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId2);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipmentListAfterBookedFromAnotherDriver = $I->grabDataFromResponseByJsonPath('$.items')[0];

        foreach ($equipmentListAfterBookedFromAnotherDriver as $equipment) {
            if ($equipment['license_plate'] === $this->equipmentLicensePlate) {
                $I->assertEquals(true, $equipment['blocked']);
            }
        }
    }

    public function cannotEndEquipmentThatIsNotEquipped(ApiTester $I): void
    {
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId1 = 'random-device-'.uniqid();
        $this->deviceId2 = 'random-device-'.uniqid();
        $this->staffExtId1 = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->staffExtId2 = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExtId2 = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate2 = 'random-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId1,
            staffExternalId2: $this->staffExtId2,
            equipmentExtId2: $this->equipmentExtId2,
            equipmentLicensePlate2: $this->equipmentLicensePlate2,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId1, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId1);
        $I->callApiUserStartSessionV2();

        // Get equipment ID and status, verify it's unequipped
        $I->callApiEquipmentGetCollectionV2(availability: 'all');
        $equipments = $I->grabDataFromResponseByJsonPath('$.items')[0];

        $I->assertLessOrEquals(50, count($equipments));

        foreach ($equipments as $equipment) {
            if ($equipment['name'] === $this->equipmentLicensePlate) {
                $status = $equipment['status'];
                $this->equipmentUuid = $equipment['uuid'];
            }
        }

        $I->assertEquals('unequipped', $status);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingEndRequestV2(),
        );

        $status = $I->grabDataFromResponseByJsonPath('$.booking')[0];
        $reason = $I->grabDataFromResponseByJsonPath('$.reasons[0].key')[0];

        $I->assertEquals('unprocessed', $status);
        $I->assertEquals('equipment_not_in_session', $reason);
    }
}
