<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyEquipmentTaskGroupCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $tour1ExtId = '';
    protected string $order1ExtId = '';
    protected string $tour1Name = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';
    protected array $taskGroupRequest = [];

    public function canCreateTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->order1ExtId = 'random-order-1-id-'.uniqid();
        $this->tour1Name = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tour1ExtId,
            orderExtId: $this->order1ExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
    }

    #[Depends('canCreateTour')]
    public function canBookEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
    }

    #[Depends('canBookEquipment')]
    public function canGetEquipmentTaskGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiEquipmentGetItemV2($this->equipmentUuid);
        });
        $taskGroupId = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].uuid')[0];
        $task1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].uuid')[0];
        $task1Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].uuid')[0];
        $task1Input1Options = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].options')[0];
        $task2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].uuid')[0];
        $task2Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].inputs[0].uuid')[0];
        $task2Input2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].inputs[1].uuid')[0];

        $this->taskGroupRequest = [
            'uuid' => $taskGroupId,
            'tasks' => [
                [
                    'uuid' => $task1Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task1Input1Id,
                            'value' => array_column($task1Input1Options, 'key'),
                        ],
                    ],
                ],
                [
                    'uuid' => $task2Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task2Input1Id,
                            'value' => 110,
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $task2Input2Id,
                            'value' => 'some value for obsolete task',
                        ],
                    ],
                ],
            ],
        ];
    }

    #[Depends('canGetEquipmentTaskGroups')]
    public function canBookOutEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingEndRequestV2(),
        );
    }

    #[Depends('canBookOutEquipment')]
    public function canPatchTaskGroupAfterBookout(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentUpdateTaskGroupV2(
            equipmentId: $this->equipmentUuid,
            data: json_encode($this->taskGroupRequest) ?: throw new \RuntimeException('payload could not be encoded'),
        );
    }

    #[Depends('canPatchTaskGroupAfterBookout')]
    public function cannotGetNonExistentEquipmentTaskgroupTemplates(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentGetItemV2(
            equipmentId: '19cc32ca-f6fc-4a42-94fa-321ed048d1aa', expectedStatusCode: 404);
    }

    #[Depends('cannotGetNonExistentEquipmentTaskgroupTemplates')]
    public function cannotGetEquipmentItemIfNotAuthenticated(ApiTester $I): void
    {
        $I->amNotAuthenticated();
        $I->callApiEquipmentGetItemV2($this->equipmentUuid, expectedStatusCode: 401);
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
