<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use App\Tests\Support\InMemoryCache;

class SapGermanyEndSessionCest
{
    private string $tenant = 'pz';
    protected string $sapTour1Id = '';
    protected string $sapTour2Id = '';

    protected string $device1Id = '';
    protected string $device2Id = '';

    protected string $tour1ExtId = '';
    protected string $tour2ExtId = '';

    protected string $staff1ExtId = '';
    protected string $staff2ExtId = '';

    public function canEndSingleUserSessionDuringTour(ApiTester $I): void
    {
        $this->staff1ExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->device1Id = 'random-device-1-id-'.uniqid();

        $wireMockPositiveExpectationIds = [
            // User and tour started
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff1ExtId],
            ),
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
            // User 1 completed and confirmed
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [],
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [],
            ),
        ];

        $this->sapTour1Id = $this->importTestTour($I, $this->tour1ExtId, $this->staff1ExtId);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staff1ExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1Id);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTour1Id);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);
        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        $I->callApiUserEndSessionV2();
        $I->wireMockVerifyRequestMapping($wireMockPositiveExpectationIds);
    }

    public function canLeaveSingleUserSessionDuringTour(ApiTester $I): void
    {
        $this->staff1ExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->device1Id = 'random-device-1-id-'.uniqid();
        $this->device2Id = 'random-device-2-id-'.uniqid();

        $wireMockPositiveMappingIds = [
            // User and tour started
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff1ExtId],
            ),
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
        ];
        $wireMockNegativeMappingIds = [
            // User 1 completed and confirmed
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
        ];

        $this->sapTour1Id = $this->importTestTour($I, $this->tour1ExtId, $this->staff1ExtId);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staff1ExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1Id);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTour1Id);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);
        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Switch device during tour
        InMemoryCache::clear();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staff1ExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device2Id);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTour1Id);
            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('returned', $tourStatus[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockPositiveMappingIds);
        $I->wireMockVerifyNegativeRequestMapping($wireMockNegativeMappingIds);
    }

    public function canDoTwoTours(ApiTester $I): void
    {
        $this->staff1ExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->tour2ExtId = 'random-tour-2-id-'.uniqid();
        $this->device1Id = 'random-device-1-id-'.uniqid();
        $this->sapTour1Id = $this->importTestTour($I, $this->tour1ExtId, $this->staff1ExtId);
        $this->sapTour2Id = $this->importTestTour($I, $this->tour2ExtId, $this->staff1ExtId);

        $wireMockPositiveMappingIds = [
            // User and tour started
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff1ExtId],
            ),
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
            // Tour 1 completed and confirmed
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
        ];
        $wireMockNegativeMappingIds = [
            // User not completed or confirmed
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
            // User 1 not completed
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staff1ExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1Id);
        $I->callApiUserStartSessionV2();

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // End the tour
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        $I->wireMockVerifyRequestMapping($wireMockPositiveMappingIds);
        $I->wireMockVerifyNegativeRequestMapping($wireMockNegativeMappingIds);

        $wireMockPositiveMappingIds = [
            // User 1 completed in tour 1
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff1ExtId],
            ),
            // Tour 2 started
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour2ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
            // User 1 started in tour 2
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour2ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff1ExtId],
            ),
            // Tour 2 completed and confirmed
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour2ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour2ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId],
            ),
        ];
        $wireMockNegativeMappingIds = [
            // User still not confirmed in any tour
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour2ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
            // User 1 not completed yet in tour 2
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour2ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
        ];

        // Start the second tour
        $I->callApiTourBookingV2(
            $this->sapTour2Id,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // End the second tour
        $I->callApiTourBookingV2(
            $this->sapTour2Id,
            BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        $I->wireMockVerifyRequestMapping($wireMockPositiveMappingIds);
        $I->wireMockVerifyNegativeRequestMapping($wireMockNegativeMappingIds);

        $wireMockPositiveMappingIds = [
            // User 1 now completed and confirmed in tour 2
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour2ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [],
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour2ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [],
            ),
        ];
        $wireMockNegativeMappingIds = [
            // but never confirmed in tour 2
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
        ];

        $I->callApiUserEndSessionV2();
        $I->wireMockVerifyRequestMapping($wireMockPositiveMappingIds);
        $I->wireMockVerifyNegativeRequestMapping($wireMockNegativeMappingIds);
    }

    public function canLogoutFromDoubleUserSessionDuringTour(ApiTester $I): void
    {
        $this->staff1ExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $this->staff2ExtId = $I->generateStaffAndAuthenticate($this->tenant);
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->device1Id = 'random-device-1-id-'.uniqid();
        $this->sapTour1Id = $this->importTestTour($I, $this->tour1ExtId, $this->staff1ExtId, $this->staff2ExtId);

        $wireMockPositiveMappingIds = [
            // both users started
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff1ExtId, $this->staff2ExtId],
            ),
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: $this->staff2ExtId, staffExtIds: [$this->staff1ExtId, $this->staff2ExtId],
            ),
            // tour started
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'started', tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: [$this->staff1ExtId, $this->staff2ExtId],
            ),
            // user 1 completed and confirmed
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff2ExtId],
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff1ExtId, staffExtIds: [$this->staff2ExtId],
            ),
        ];
        $wireMockNegativeMappingIds = [
            // tour end not reported
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour1ExtId, staffExtId: null, staffExtIds: null,
            ),
            // user 2 end not reported
            $this->setStaffStatusCallWireMockMapping(
                apiTester: $I, status: 'completed', tourExtId: $this->tour2ExtId, staffExtId: $this->staff1ExtId, staffExtIds: null,
            ),
            $this->setStaffConfirmationCallWireMockMapping(
                apiTester: $I, tourExtId: $this->tour1ExtId, staffExtId: $this->staff2ExtId, staffExtIds: null,
            ),
        ];

        $this->sapTour1Id = $I->callSapGermanyTourUpsert(
            SapGermanyTourFixtures::tourRequestCreate(
                tourExtId: $this->tour1ExtId,
                staffExternalId: $this->staff1ExtId,
                staffExternalId2: $this->staff2ExtId,
            )
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staff2ExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1Id);
        $I->callApiUserStartSessionV2();

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staff1ExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->callApiTourGetV2($this->sapTour1Id);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);
        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // user 1 logs out
        $I->callApiUserEndSessionV2();
        $I->wireMockVerifyRequestMapping($wireMockPositiveMappingIds);
        $I->wireMockVerifyNegativeRequestMapping($wireMockNegativeMappingIds);
    }

    private function setStaffStatusCallWireMockMapping(ApiTester $apiTester, string $status, string $tourExtId, ?string $staffExtId, ?array $staffExtIds): string
    {
        return $apiTester->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $tourExtId,
            subType: null !== $staffExtId ? 'staff' : null,
            subTypeExternalId: $staffExtId,
            staffExtIds: $staffExtIds,
            equipmentExtIds: [],
            status: $status
        );
    }

    private function setStaffConfirmationCallWireMockMapping(ApiTester $apiTester, string $tourExtId, ?string $staffExtId, ?array $staffExtIds): string
    {
        return $apiTester->wireMockAddSapGermanyConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $tourExtId,
            subType: null !== $staffExtId ? 'staff' : null,
            subTypeExternalId: $staffExtId,
            staffExtIds: $staffExtIds,
            equipmentExtIds: [],
            orderLocations: [],
            termination: null,
            notes: null,
        );
    }

    private function importTestTour(ApiTester $I, string $tourExtId, string $staffExternalId, string $staffExternalId2 = ApiTester::TEST_STAFF_EXT_ID_PREFIX): string
    {
        return $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            staffExternalId: $staffExternalId,
            staffExternalId2: $staffExternalId2
        ));
    }
}
