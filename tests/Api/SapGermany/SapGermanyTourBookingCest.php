<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Domain\Services\ObjectStorage\ObjectPrefix;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupsResponse;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskTemplatesReponse;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputFileDescriber;
use App\Tests\Support\Api\SapGermanyOrderTaskGroupFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use App\Tests\Support\DeviceCache;
use App\Tests\Support\InMemoryCache;
use Codeception\Attribute\Depends;

class SapGermanyTourBookingCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    private string $staffExtId2 = '';
    protected string $sapTourId = '';

    protected string $orderId = '';

    protected ?HasTaskGroupsResponse $orderTaskGroups = null;

    protected ?HasTaskTemplatesReponse $additionalServices = null;

    protected string $tourExtId = '';
    protected string $orderExtId = '';
    protected string $tourName = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';

    /**
     * @var string[]
     */
    protected array $equipmentExtIds = [
    ];
    protected string $equipmentUuid = '';

    public function canStartSapGermanyTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];

        $wireMockMappingIds = $I->wireMockSetSapGermanyStatusTourStartMappings($this->tourExtId, [$this->staffExtId], $this->equipmentExtIds);

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);
        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartSapGermanyTour')]
    public function canSeeOrderedInterruptionTemplates(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiInterruptionTemplateGetV2();
        $interruptionTemplates = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $lastSequence = -1;
        foreach ($interruptionTemplates as $interruptionTemplate) {
            $I->assertGreaterThan($lastSequence, (int) $interruptionTemplate['sequence_number']);
            $lastSequence = (int) $interruptionTemplate['sequence_number'];
        }
    }

    #[Depends('canSeeOrderedInterruptionTemplates')]
    public function canNotStartSapGermanyTourInSecondDevice(ApiTester $I): void
    {
        $this->staffExtId2 = $I->generateStaffAndAuthenticate($this->tenant);
        $tempDevice = 'temp-device-id-'.uniqid();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId2, $this->tenant);
        $I->amUsingDeviceWithId($tempDevice);
        $I->callApiUserStartSessionV2();

        // Get tour, check if it's created
        $I->callApiTourGetV2($this->sapTourId, expectedStatusCode: 403);

        // Start the tour
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingStartRequestV2(force: false),
            expectedStatusCode: 403,
        );
        $I->amUsingDeviceWithId($this->deviceId);
    }

    #[Depends('canNotStartSapGermanyTourInSecondDevice')]
    public function canStartOrderInSapGermanyTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrder($I);
        $this->setAdditionalServices($I);

        $wireMockMappingIds = [
            $I->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'order',
                objectExternalId: $this->orderExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: $this->equipmentExtIds,
                status: 'started'
            ),
        ];

        $I->callApiOrderBookingV2($this->orderId, BookingFixtures::bookingStartRequestV2());

        $this->validateOrderStatus($I, 'started');
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartOrderInSapGermanyTour')]
    public function canUpdateTaskGroupsInOrder(ApiTester $I): void
    {
        $taskGroupDescriber = SapGermanyOrderTaskGroupFixtures::getReturnToBackendOrderTaskGroupDescription();
        $taskGroupToReturn = $this->orderTaskGroups->taskGroups[$taskGroupDescriber->position];

        $taskGroupRequest = $taskGroupToReturn->getReturn2Backend($taskGroupDescriber, $this->additionalServices);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $I->callApiOrderTaskGroupPatchV2(
            orderId: $this->orderId,
            data: json_encode($taskGroupRequest),
        );

        // validate taskGroup is completed now
        $I->waitUntil(
            function (ApiTester $I) use ($taskGroupDescriber): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderTaskGroupTasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups['.$taskGroupDescriber->position.'][tasks]')[0];
                $I->assertIsArray($firstOrderTaskGroupTasks);
                $I->assertCount(count($taskGroupDescriber->tasks), $firstOrderTaskGroupTasks);

                foreach ($firstOrderTaskGroupTasks as $task) {
                    if ('skipme' === $task['name']) {
                        $I->assertEquals('skipped', $task['status']);
                    } else {
                        $I->assertEquals('completed', $task['status']);
                        $I->assertNotEmpty($task['inputs']);
                        foreach ($task['inputs'] as $input) {
                            $I->assertNotEmpty($input['value']);
                        }
                    }
                }
            }
        );

        // upload files
        $this->uploadTaskFiles($taskGroupRequest['tasks'], $I);
    }

    #[Depends('canUpdateTaskGroupsInOrder')]
    public function canEndSapGermanyOrder(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'order',
                objectExternalId: $this->orderExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: $this->equipmentExtIds,
                status: 'completed'
            ),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2($this->orderId, BookingFixtures::bookingEndRequestV2());

        $this->validateOrderStatus($I, 'completed');
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canEndSapGermanyOrder')]
    public function canEndSapGermanyTour(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tourExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: $this->equipmentExtIds,
                status: 'completed'
            ),
        ];
        $wireMockMappingIds[] = $I->wireMockAddSapGermanyConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            orderLocations: [],
            termination: null,
            notes: null
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        // End the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's ended
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('completed', $tourStatus[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canEndSapGermanyTour')]
    public function canBookOutEquipmentAfterTour(ApiTester $I): void
    {
        $equipmentExtId = array_pop($this->equipmentExtIds);
        sort($this->equipmentExtIds);
        $wireMockMappingIds = [
            $I->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tourExtId,
                subType: 'equipment',
                subTypeExternalId: $equipmentExtId,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: $this->equipmentExtIds,
                status: 'completed',
            ),
        ];
        $wireMockMappingIds[] = $I->wireMockAddSapGermanyConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'equipment',
            subTypeExternalId: $equipmentExtId,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            orderLocations: [],
            termination: null,
            notes: null
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $status = $I->grabDataFromResponseByJsonPath('$.booking')[0];
        $I->assertEquals('processed', $status);

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canBookOutEquipmentAfterTour')]
    public function canEndUserSessionAfterTour(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tourExtId,
                subType: 'staff',
                subTypeExternalId: $this->staffExtId,
                staffExtIds: [],
                equipmentExtIds: $this->equipmentExtIds,
                status: 'completed',
            ),
        ];

        $wireMockMappingIds[] = $I->wireMockAddSapGermanyConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'staff',
            subTypeExternalId: $this->staffExtId,
            staffExtIds: [],
            equipmentExtIds: $this->equipmentExtIds,
            orderLocations: [],
            termination: null,
            notes: null
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserEndSessionV2();

        $I->amNotAuthenticated();
        InMemoryCache::clear();
        DeviceCache::clear();

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function uploadTaskFiles(array $sentTasks, ApiTester $I): void
    {
        foreach ($sentTasks as $sentTask) {
            foreach ($sentTask['inputs'] as $sentInput) {
                if (is_array($sentInput['value'])) {
                    foreach ($sentInput['value'] as $sentValue) {
                        if ($sentValue instanceof ReturnInputFileDescriber) {
                            $fileContents = file_get_contents(codecept_data_dir($sentValue->name));

                            $uuid = str_replace('app-user-files/', '', $sentValue->file);

                            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
                            $I->callApiFileUploadInDirectoryV2('app-user-files', $uuid, $fileContents);

                            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
                            $I->callApiFileRetrieveV2(ObjectPrefix::APP_USER_FILES->value.$uuid);
                            $I->seeHttpHeader('Content-Type', 'image/png');
                            $I->assertEquals($fileContents, $I->grabResponse());
                        }
                    }
                }
            }
        }
    }

    private function setCreatedOrder(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);

        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');

        $I->assertEquals('created', $firstOrderStatus[0]);

        $this->orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];

        $this->orderTaskGroups = HasTaskGroupsResponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.orders[0]')[0]);
        $I->assertCount(8, $this->orderTaskGroups->taskGroups);
    }

    private function setAdditionalServices(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);
        $this->additionalServices = HasTaskTemplatesReponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.additional_service_templates')[0]);
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
