<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupsResponse;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\Api\SapGermanyVehicleCheckTaskGroupFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyVehicleCheckCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $tour1ExtId = '';
    protected string $order1ExtId = '';
    protected string $tour1Name = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';
    protected string $deviceId = '';
    protected ?HasTaskGroupsResponse $equipmentTaskGroups = null;

    public function canCreateTourForVehicleCheck(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->order1ExtId = 'random-order-1-id-'.uniqid();
        $this->tour1Name = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tour1ExtId,
            orderExtId: $this->order1ExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
    }

    #[Depends('canCreateTourForVehicleCheck')]
    public function getStartEquipmentToCheck(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $this->equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $this->equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('unequipped', $testEquipment[0]['status']);

        // Eq. needs to be booked started to see the old tour in error-case
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $this->equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $this->equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('equipped', $testEquipment[0]['status']);
    }

    #[Depends('getStartEquipmentToCheck')]
    public function canGetVehicleCheckTaskGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiEquipmentGetItemV2($this->equipmentUuid);
        });
        $this->equipmentTaskGroups = HasTaskGroupsResponse::createFromResponse($I->grabDataFromResponseByJsonPath('$')[0]);
        $I->assertCount(4, $this->equipmentTaskGroups->taskGroups);
        $I->assertCount(1, $I->grabDataFromResponseByJsonPath('$.taskgroups[3].rules')[0]);
    }

    #[Depends('canGetVehicleCheckTaskGroups')]
    public function canUpdateVehicleCheckTaskGroups(ApiTester $I): void
    {
        $taskGroupDescriber1 = SapGermanyVehicleCheckTaskGroupFixtures::getReturnToBackendVehicleCheck1TaskGroupDescription();
        $taskGroupToReturn1 = $this->equipmentTaskGroups->taskGroups[$taskGroupDescriber1->position];

        $taskGroupDescriber2 = SapGermanyVehicleCheckTaskGroupFixtures::getReturnToBackendVehicleCheck2TaskGroupDescription();
        $taskGroupToReturn2 = $this->equipmentTaskGroups->taskGroups[$taskGroupDescriber2->position];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $taskGroupRequest = $taskGroupToReturn1->getReturn2Backend($taskGroupDescriber1, null);
        $I->callApiEquipmentUpdateTaskGroupV2(
            equipmentId: $this->equipmentUuid,
            data: json_encode($taskGroupRequest),
        );

        $I->waitUntil(function (ApiTester $I) {
            $this->checkResultInPortal(
                I: $I,
                equipmentId: $this->equipmentUuid,
                countPositive: 2,
                countNegative: 0,
                countNotAnswered: 3,
            );
        });

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $taskGroupRequest = $taskGroupToReturn2->getReturn2Backend($taskGroupDescriber2, null);
        $I->callApiEquipmentUpdateTaskGroupV2(
            equipmentId: $this->equipmentUuid,
            data: json_encode($taskGroupRequest),
        );

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiEquipmentGetItemV2($this->equipmentUuid);
            $I->assertEquals(
                expected: 'test123',
                actual: $I->grabDataFromResponseByJsonPath('$.taskgroups[3].tasks[1].inputs[3].value')[0],
            );
        });

        $sessionEquipmentId = $this->checkResultInPortal(
            I: $I,
            equipmentId: $this->equipmentUuid,
            countPositive: 4,
            countNegative: 2,
            countNotAnswered: 0,
        );

        $this->checkResultDetails($I, $sessionEquipmentId);
    }

    private function checkResultInPortal(
        ApiTester $I,
        string $equipmentId,
        int $countPositive,
        int $countNegative,
        int $countNotAnswered,
    ): string {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalEquipmentCheckList(
            equipmentId: $equipmentId,
            from: new \DateTimeImmutable(),
            to: new \DateTimeImmutable(),
        );

        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertCount(1, $data);
        $result = $data[0];
        $I->assertNotNull($result);
        $I->assertEquals($result['count_positive'], $countPositive);
        $I->assertEquals($result['count_negative'], $countNegative);
        $I->assertEquals($result['count_not_answered'], $countNotAnswered);

        return $result['session_equipment_id'];
    }

    private function checkResultDetails(ApiTester $I, string $sessionEquipmentId): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalEquipmentCheckDetails($sessionEquipmentId);
        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertIsArray($data['task_groups']);
        $I->assertCount(2, $data['task_groups']);

        foreach ($data['task_groups'] as $taskGroup) {
            $I->assertIsArray($taskGroup['tasks']);
            $I->assertNotEmpty($taskGroup['tasks']);
            foreach ($taskGroup['tasks'] as $taskData) {
                $I->assertEquals('completed', $taskData['status']);
            }
        }
    }
}
