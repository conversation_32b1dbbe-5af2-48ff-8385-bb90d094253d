<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\InterruptionFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class SapGermanyPendingInterruptionsCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    private const string DEVICE_1 = 'testdev01';
    protected string $sapTourId = '';
    protected string $clientUuid = '';
    protected string $interruptionTemplateId = '';
    protected string $tourExtId = 'WE123456';
    protected array $equipmentExtIds = [
        '10004635',
    ];

    public function canInterruptBeforeTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->sapTourId = $I->callSapGermanyTourUpsert(
            SapGermanyTourFixtures::tourRequestCreate(
                tourExtId: $this->tourExtId,
                staffExternalId: $this->staffExtId,
            )
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId(self::DEVICE_1);
        $I->callApiUserStartSessionV2();

        // equipment start
        $I->callApiEquipmentGetCollectionV2();
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $equipment = array_values(array_filter($data, fn (array $eqData): bool => 'MI-AK 1092' === $eqData['name']))[0];
        $I->assertNotEmpty($equipment['uuid']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $equipment = array_values(array_filter($data, fn (array $eqData): bool => 'MI-AK 1092' === $eqData['name']))[0];
            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the interruption
        $I->callApiInterruptionTemplateGetV2();
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $staffInterruptions = array_values(array_filter($data, fn (array $entry): bool => 'staff' === $entry['source']));
        $I->assertNotEmpty($staffInterruptions);
        $this->interruptionTemplateId = $staffInterruptions[0]['template_uuid'];
        $I->assertNotEmpty($this->interruptionTemplateId);

        $this->clientUuid = Uuid::v4()->toRfc4122();
        $I->callApiStaffInterruptionV2(
            interruptionTemplateId: $this->interruptionTemplateId,
            data: InterruptionFixtures::bookingInterruptionStartRequestV2($this->clientUuid),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiInterruptionGetV2(includeNotCompleted: true);
            $interruptions = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertCount(1, $interruptions);
            $I->assertEquals('started', $interruptions[0]['status']);
        });

        // End the interruption
        $I->callApiStaffInterruptionV2(
            interruptionTemplateId: $this->interruptionTemplateId,
            data: InterruptionFixtures::bookingInterruptionEndRequestV2($this->clientUuid),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiInterruptionGetV2();
            $interruptions = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertCount(1, $interruptions);
            $I->assertEquals('completed', $interruptions[0]['status']);
        });
    }

    #[Depends('canInterruptBeforeTour')]
    public function canSendPendingInterruptionsOnTourStart(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $this->setInterruptionSapMapping($I, 'started'),
            $this->setInterruptionSapMapping($I, 'completed'),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function setInterruptionSapMapping(ApiTester $I, string $status): string
    {
        return $I->wireMockAddSapGermanyStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'interruption',
            subTypeExternalId: '${json-unit.any-string}',
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: $status,
        );
    }
}
