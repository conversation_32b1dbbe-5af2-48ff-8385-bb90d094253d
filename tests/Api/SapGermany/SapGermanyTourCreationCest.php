<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyTourCreationCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';

    protected string $tooOldTourId = '';

    protected string $tour1ExtId = '';
    protected string $order1ExtId = '';
    protected string $tour1Name = '';
    protected string $tour2ExtId = '';
    protected string $order2ExtId = '';
    protected string $tour2Name = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';

    public function canCreateTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->order1ExtId = 'random-order-1-id-'.uniqid();
        $this->tour1Name = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tour1ExtId,
            orderExtId: $this->order1ExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
    }

    #[Depends('canCreateTour')]
    public function canCreateTooOldTour(ApiTester $I): void
    {
        $this->tour2ExtId = 'random-tour-2-id-'.uniqid();
        $this->order2ExtId = 'random-order-2-id-'.uniqid();
        $this->tour2Name = 'random-tour-2-name-'.uniqid();

        $this->tooOldTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tour2ExtId,
            orderExtId: $this->order2ExtId,
            tourName: $this->tour2Name,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            start: (new \DateTime('yesterday'))->setTime(2, 0),
            end: (new \DateTime('yesterday'))->setTime(23, 50),
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $this->equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $this->equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('unequipped', $testEquipment[0]['status']);

        // Eq. needs to be booked started to see the old tour in error-case
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $this->equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $this->equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('equipped', $testEquipment[0]['status']);
    }

    #[Depends('canCreateTooOldTour')]
    public function canFindCorrectlyCreatedTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetCollectionV2();

        $uuids = $I->grabDataFromResponseByJsonPath('$.items.*.uuid');
        $names = $I->grabDataFromResponseByJsonPath('$.items.*.name');
        $addInfos = $I->grabDataFromResponseByJsonPath('$.items.*.orders.*.additional_informations.*.text');

        $orderLocationFound = false;
        foreach ($addInfos as $addInfo) {
            if (str_contains($addInfo, 'Albert-Schweitzer-Str. 8')) {
                $orderLocationFound = true;
            }
        }
        $I->assertTrue($orderLocationFound);

        $I->assertContains($this->sapTourId, $uuids);
        $I->assertContains($this->tour1Name, $names);

        $I->assertNotContains($this->tooOldTourId, $uuids);
        $I->assertNotContains($this->tour2Name, $names);
    }

    #[Depends('canFindCorrectlyCreatedTour')]
    public function canReadCreatedTourCest(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);

        $I->assertNotEmpty($I->grabDataFromResponseByJsonPath('$.additional_informations')[0]);
        $I->assertCount(3, $I->grabDataFromResponseByJsonPath('$.additional_informations')[0]);
        $I->assertCount(1, $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[2].tasks[4].task_actions')[0]);

        $expectedResult = SapGermanyTourFixtures::getCreateTourResult($this->tour1Name);

        $I->checkSapTourResult($expectedResult);
    }

    #[Depends('canReadCreatedTourCest')]
    public function canNotReadCreatedTourCestIfNotLoggedIn(ApiTester $I): void
    {
        $I->amNotAuthenticated();
        $I->callApiTourGetV2($this->sapTourId, expectedStatusCode: 401);
    }

    #[Depends('canNotReadCreatedTourCestIfNotLoggedIn')]
    public function canStartCreatedTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
    }
}
