<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\ApiTester;

class SapGermanyStaffTaskGroupCest
{
    private string $tenant = 'pz';
    private string $deviceId = '';

    public function canUpdateStaffTaskGroup(ApiTester $I): void
    {
        $this->deviceId = 'random-device-'.uniqid();
        $I->generateStaffAndAuthenticate($this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiUserProfileV2();

        $taskGroupId = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].uuid')[0];
        $task1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].uuid')[0];
        $task1Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].uuid')[0];
        $task1Input1Options = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].options')[0];
        $task2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].uuid')[0];
        $task2Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].inputs[0].uuid')[0];
        $task2Input2Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[1].inputs[1].uuid')[0];

        $taskGroupRequest = [
            'uuid' => $taskGroupId,
            'tasks' => [
                [
                    'uuid' => $task1Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task1Input1Id,
                            'value' => array_column($task1Input1Options, 'key'),
                        ],
                    ],
                ],
                [
                    'uuid' => $task2Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task2Input1Id,
                            'value' => 12345,
                        ],
                        [
                            'type' => 'input',
                            'uuid' => $task2Input2Id,
                            'value' => 'some value',
                        ],
                    ],
                ],
            ],
        ];

        $I->callApiUserUpdateTaskgroupV2(data: json_encode($taskGroupRequest));

        $I->waitUntil(function (ApiTester $I) use ($task1Input1Options): void {
            $I->callApiUserProfileV2();

            $responseTaskGroup1Task1Input1Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[0].tasks[0].inputs[0].options.*.name'
            );
            $responseTaskGroup1Task2Input1Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[0].tasks[1].inputs[0].value'
            )[0];
            $responseTaskGroup1Task2Input2Value = $I->grabDataFromResponseByJsonPath(
                '$.taskgroups[0].tasks[1].inputs[1].value'
            )[0];

            $I->assertEquals(array_column($task1Input1Options, 'name'), $responseTaskGroup1Task1Input1Value);
            $I->assertEquals('12345', $responseTaskGroup1Task2Input1Value);
            $I->assertEquals('some value', $responseTaskGroup1Task2Input2Value);
        });
    }
}
