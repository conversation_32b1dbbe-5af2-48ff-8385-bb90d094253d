<?php

/** @noinspection PhpUnhandledExceptionInspection */

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupsResponse;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskTemplatesReponse;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputFileDescriber;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputScaleDescriber;
use App\Tests\Support\Api\SapGermanyOrderTaskGroupFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class SapGermanySecondOrderCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';

    protected string $orderIdOld = '';
    protected string $orderIdNew = '';

    protected ?HasTaskGroupsResponse $orderTaskGroupsOld = null;
    protected ?HasTaskTemplatesReponse $additionalServices = null;

    protected string $tourExtId = '';
    protected string $orderExtIdOld = '';
    protected string $orderExtIdNew = '';

    protected string $tourName = '';

    protected string $deviceId;

    public function canStartTourToAbandonOrder(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->deviceId = 'random-device-id-'.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->orderExtIdOld = 'random-old-order-id-'.uniqid();
        $this->orderExtIdNew = 'random-new-order-id-'.uniqid();
        $equipmentLicensePlate = 'random-plate-'.uniqid();
        $equipmentExtId = 'random-eq-id-'.uniqid();
        $equipmentLicensePlate2 = 'random-plate-2-'.uniqid();
        $equipmentExtId2 = 'random-eq-2-id-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(
            SapGermanyTourFixtures::tourRequestCreate(
                tourExtId: $this->tourExtId,
                orderExtId: $this->orderExtIdOld,
                tourName: $this->tourName,
                equipmentExtId: $equipmentExtId,
                equipmentLicensePlate: $equipmentLicensePlate,
                staffExternalId: $this->staffExtId,
                equipmentExtId2: $equipmentExtId2,
                equipmentLicensePlate2: $equipmentLicensePlate2,
            ),
        );
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        // Book equipment
        $I->callApiEquipmentGetCollectionV2(availability: 'all');
        $equipment = $I->grabDataFromResponseByJsonPath("$.items[?(@.license_plate=='{$equipmentLicensePlate}')]")[0];
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $processingStatus = $I->grabDataFromResponseByJsonPath('$.booking')[0];
        $I->assertEquals('processed', $processingStatus);

        $I->callApiTourGetV2(tourId: $this->sapTourId);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);

        // Start the tour
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2(tourId: $this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);

            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(1, $orders);
            $I->assertEquals('created', $orders[0]['status']);
        });
    }

    public function canStartOrderInSapGermanyTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->loadAndValidateFirstOrder($I);
        $this->setAdditionalServices($I);

        $I->callApiOrderBookingV2(
            orderId: $this->orderIdOld,
            data: $this->orderBookingRequestJson('start'),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2(tourId: $this->sapTourId);
            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(1, $orders);
            $I->assertEquals('started', $orders[0]['status']);
        });
    }

    public function canExchangeOrder(ApiTester $I): void
    {
        $tourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate($this->tourExtId, $this->orderExtIdNew));
        $I->assertEquals($this->sapTourId, $tourId);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->loadAndValidateSecondOrder($I);
    }

    public function canUpdateTaskGroupsInOldOrder(ApiTester $I): void
    {
        $taskGroupDescriber = SapGermanyOrderTaskGroupFixtures::getReturnToBackendOrderTaskGroupDescription();
        $taskGroupToReturn = $this->orderTaskGroupsOld->taskGroups[$taskGroupDescriber->position];

        $taskGroupRequest = $taskGroupToReturn->getReturn2Backend($taskGroupDescriber, $this->additionalServices);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $I->callApiOrderTaskGroupPatchV2(
            orderId: $this->orderIdOld,
            data: json_encode($taskGroupRequest),
        );

        $I->waitUntil(function (ApiTester $I) use ($taskGroupDescriber): void {
            $I->callApiTourGetV2(tourId: $this->sapTourId);
            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $oldOrder = $orders[0]['uuid'] === $this->orderIdOld ? $orders[0] : $orders[1];

            // new current taskgroups are still in created, no additionalservices or repeated stored at this
            $firstOrderTaskGroupTasks = $oldOrder['taskgroups'][$taskGroupDescriber->position]['tasks'];
            $I->assertIsArray($firstOrderTaskGroupTasks);
            $I->assertCount(count($taskGroupDescriber->tasks), $firstOrderTaskGroupTasks);

            foreach ($firstOrderTaskGroupTasks as $task) {
                if ('skipme' === $task['name']) {
                    $I->assertEquals('skipped', $task['status']);
                } else {
                    $I->assertEquals('completed', $task['status']);
                }
            }
        });

        // check the old order
        $I->waitUntil(function (ApiTester $I) use ($taskGroupDescriber): void {
            $I->callApiTourGetV2(tourId: $this->sapTourId, includeAll: 1);
            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(2, $orders);
            $oldOrder = array_values(array_filter(
                $orders,
                fn (array $orderData): bool => $this->orderIdOld === $orderData['uuid']
            ))[0];
            $I->assertIsArray($oldOrder);
            $I->assertNotEmpty($oldOrder);

            $taskGroupToReturn = $this->orderTaskGroupsOld->taskGroups[$taskGroupDescriber->position];
            $sentTaskGroupRequest = $taskGroupToReturn->getReturn2Backend($taskGroupDescriber, $this->additionalServices);
            $taskGroupFound = false;
            foreach ($oldOrder['taskgroups'] as $oldTaskGroup) {
                if ($oldTaskGroup['uuid'] === $sentTaskGroupRequest['uuid']) {
                    $taskGroupFound = true;

                    foreach ($sentTaskGroupRequest['tasks'] as $sentTaskRequest) {
                        if ($sentTaskRequest['additional_service'] || $sentTaskRequest['repeat_service']) {
                            continue;
                        }
                        $oldTask = array_values(array_filter(
                            $oldTaskGroup['tasks'],
                            fn (array $oldCheckTask): bool => $sentTaskRequest['uuid'] === $oldCheckTask['uuid']
                        ))[0];
                        $I->assertIsArray($oldTask);
                        $I->assertNotEmpty($oldTask);

                        foreach ($sentTaskRequest['inputs'] as $sentInput) {
                            $oldInput = array_values(array_filter(
                                $oldTask['inputs'], fn (array $inputData): bool => $inputData['uuid'] === $sentInput['uuid']
                            ))[0];
                            $I->assertIsArray($oldInput);
                            $I->assertNotEmpty($oldInput);

                            $sentValue = $sentInput['value'];
                            if (is_array($sentValue)) {
                                $tmp = [];
                                for ($i = 0; $i < count($sentValue); ++$i) {
                                    if ($sentValue[$i] instanceof ReturnInputFileDescriber) {
                                        $tmp[] = [
                                            'file' => 'pz/'.$sentValue[$i]->file,
                                            'name' => $sentValue[$i]->name,
                                            'label' => $sentValue[$i]->name,
                                        ];
                                    } elseif ($sentValue[$i] instanceof ReturnInputScaleDescriber) {
                                        $tmpEntry = [
                                            'netto' => $sentValue[$i]->netto,
                                            'status' => $sentValue[$i]->status,
                                            'unit' => $sentValue[$i]->unit,
                                            'is_manual_input' => $sentValue[$i]->isManualInput,
                                            'timestamp' => $sentValue[$i]->timestamp,
                                            'timestamp_received_at' => $sentValue[$i]->timestampReceivedAt,
                                            'weighing_uuid' => $sentValue[$i]->weighingUuid,
                                        ];
                                        if (null !== $sentValue[$i]->brutto) {
                                            $tmpEntry['brutto'] = $sentValue[$i]->brutto;
                                        }
                                        if (null !== $sentValue[$i]->puk) {
                                            $tmpEntry['puk'] = $sentValue[$i]->puk;
                                        }
                                        if (null !== $sentValue[$i]->tara) {
                                            $tmpEntry['tara'] = $sentValue[$i]->tara;
                                        }
                                        if (null !== $sentValue[$i]->wasteType) {
                                            $tmpEntry['waste_type'] = $sentValue[$i]->wasteType;
                                        }
                                        $tmp[] = $tmpEntry;
                                    } else {
                                        $tmp[] = $sentValue[$i];
                                    }
                                }
                                $sentValue = $tmp;
                            }

                            $expectedValue = $this->getOldValue($oldInput);

                            if (is_array($expectedValue)) {
                                $expectedValue = array_filter(
                                    $expectedValue,
                                    static fn ($value): bool => null !== $value
                                );
                            }
                            $I->assertEquals($expectedValue, $sentValue);
                        }
                    }

                    break;
                }
            }
            $I->assertTrue($taskGroupFound);
        });
    }

    public function canEndOldOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2(
            orderId: $this->orderIdOld,
            data: $this->orderBookingRequestJson('end'),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2(tourId: $this->sapTourId);
            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(2, $orders);

            $oldOrder = $orders[0]['uuid'] === $this->orderIdOld ? $orders[0] : $orders[1];
            $newOrder = $orders[0]['uuid'] === $this->orderIdNew ? $orders[0] : $orders[1];

            $I->assertEquals('completed', $oldOrder['status']);
            $I->assertEquals('created', $newOrder['status']);
        });
    }

    public function canStartNewOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2(
            orderId: $this->orderIdNew,
            data: $this->orderBookingRequestJson('start'),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2(tourId: $this->sapTourId);
            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(2, $orders);

            $oldOrder = $orders[0]['uuid'] === $this->orderIdOld ? $orders[0] : $orders[1];
            $newOrder = $orders[0]['uuid'] === $this->orderIdNew ? $orders[0] : $orders[1];

            $I->assertEquals('completed', $oldOrder['status']);
            $I->assertEquals('started', $newOrder['status']);
        });
    }

    private function getOldValue(array $oldInput): mixed
    {
        if (!isset($oldInput['options']) || empty($oldInput['options'])) {
            return $oldInput['value'];
        }
        $ret = [];
        foreach ($oldInput['options'] as $option) {
            if (in_array($option['key'], $oldInput['value'])) {
                $ret[] = $option['key'];
            }
        }

        return $ret;
    }

    private function loadAndValidateFirstOrder(ApiTester $I): void
    {
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2(tourId: $this->sapTourId);

            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(1, $orders);
            $I->assertEquals('created', $orders[0]['status']);
            $this->orderIdOld = $orders[0]['uuid'];
            $this->orderTaskGroupsOld = HasTaskGroupsResponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.orders[0]')[0]);
            $I->assertCount(8, $this->orderTaskGroupsOld->taskGroups);
        });
    }

    private function loadAndValidateSecondOrder(ApiTester $I): void
    {
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2(tourId: $this->sapTourId);

            $orders = $I->grabDataFromResponseByJsonPath('$.orders')[0];
            $I->assertCount(2, $orders);

            foreach ($orders as $order) {
                if ($order['uuid'] === $this->orderIdOld) {
                    continue;
                }

                $I->assertEquals('created', $order['status']);
                $this->orderIdNew = $order['uuid'];
                $I->assertNotEquals($this->orderIdNew, $this->orderIdOld);
            }
        });
    }

    private function setAdditionalServices(ApiTester $I): void
    {
        $I->callApiTourGetV2(tourId: $this->sapTourId);
        $this->additionalServices = HasTaskTemplatesReponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.additional_service_templates')[0]);
    }

    private function orderBookingRequestJson(string $booking): string
    {
        return <<<JSON
        {
            "booking": "{$booking}",
            "force": true
        }
        JSON;
    }
}
