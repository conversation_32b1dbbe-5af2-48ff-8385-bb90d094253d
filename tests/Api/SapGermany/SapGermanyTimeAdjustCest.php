<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class SapGermanyTimeAdjustCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    private string $currentDeviceId = '';
    private string $currentTestTourId = '';
    private string $currentEquipmentLicensePlate = '';
    private string $currentEquipmentUuid = '';
    private string $currentTourExtId = '';
    private string $currentOrderExtId = '';
    private string $currentOrderId = '';
    private string $currentTourName = '';
    private string $currentEquipmentExtId = '';

    public function canStartATourWithSameTs(ApiTester $I): void
    {
        $now = new \DateTimeImmutable();
        $halfHourAgo = $now->modify('- 30 minutes');

        $action = $this->startATour($I, $halfHourAgo, $halfHourAgo);
        $diff = $now->getTimestamp() - $action->getTimestamp();
        $I->assertLessThanOrEqual(25, abs($diff));

        $action = $this->startFirstOrder($I, $halfHourAgo, $halfHourAgo);
        $diff = $now->getTimestamp() - $action->getTimestamp();
        $I->assertLessThanOrEqual(50, abs($diff));
    }

    public function canStartATourWithDelay(ApiTester $I): void
    {
        $now = new \DateTimeImmutable();
        $halfHourAgo = $now->modify('- 30 minutes');
        $twoHoursAgo = $now->modify('- 2 hours');
        $oneAndHalfOursAgo = $now->modify('- 90 minutes');

        $action = $this->startATour($I, $halfHourAgo, $twoHoursAgo);
        $diff = $oneAndHalfOursAgo->getTimestamp() - $action->getTimestamp();
        $I->assertLessThanOrEqual(25, abs($diff));

        $action = $this->startFirstOrder($I, $halfHourAgo, $twoHoursAgo);
        $diff = $oneAndHalfOursAgo->getTimestamp() - $action->getTimestamp();
        $I->assertLessThanOrEqual(50, abs($diff));
    }

    public function canDoActionsInFuture(ApiTester $I): void
    {
        $now = new \DateTimeImmutable();
        $inHalfAnHour = $now->modify('+ 30 minutes');
        $inTwoHours = $now->modify('+ 2 hours');
        $inOneAndAHalfHours = $now->modify('+ 90 minutes');

        $action = $this->startATour($I, $inHalfAnHour, $inTwoHours);
        $diff = $inOneAndAHalfHours->getTimestamp() - $action->getTimestamp();
        $I->assertLessThanOrEqual(25, abs($diff));

        $action = $this->startFirstOrder($I, $inHalfAnHour, $inTwoHours);
        $diff = $inOneAndAHalfHours->getTimestamp() - $action->getTimestamp();
        $I->assertLessThanOrEqual(50, abs($diff));
    }

    private function startATour(
        ApiTester $I,
        \DateTimeImmutable $headerTime,
        \DateTimeImmutable $actionTime,
    ): \DateTimeImmutable {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->createTestScenarioForTour($I);
        $wireMockMappingIds = $I->wireMockSetSapGermanyStatusTourStartMappings($this->currentTourExtId, [$this->staffExtId], [$this->currentEquipmentExtId]);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->currentDeviceId);
        $I->callApiUserStartSessionV2();

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForCurrentTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->currentEquipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->currentEquipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForCurrentTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $currentTime = new \DateTimeImmutable();
        $I->haveHttpHeader('x-request-start', $currentTime->getTimestamp().'.'.$currentTime->getMicrosecond());
        $I->callApiTourBookingV2(
            tourId: $this->currentTestTourId,
            data: BookingFixtures::bookingStartRequestV2(
                force: false,
            ),
            deviceTimestamp: $headerTime->format(\DateTimeInterface::RFC3339_EXTENDED),
            actionTimestamp: $actionTime->format(\DateTimeInterface::RFC3339_EXTENDED),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->currentTestTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);

        return $this->getTimestampFromCurrentTourStartCall($I);
    }

    private function startFirstOrder(
        ApiTester $I,
        \DateTimeImmutable $headerTime,
        \DateTimeImmutable $actionTime,
    ): \DateTimeImmutable {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrder($I);

        $wireMockMappingIds = [
            $I->wireMockAddSapGermanyStatusCallMapping(
                objectType: 'order',
                objectExternalId: $this->currentOrderExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->currentEquipmentExtId],
                status: 'started'
            ),
        ];

        $currentTime = new \DateTimeImmutable();
        $I->haveHttpHeader('x-request-start', $currentTime->getTimestamp().'.'.$currentTime->getMicrosecond());
        $I->callApiOrderBookingV2(
            orderId: $this->currentOrderId,
            data: $this->orderBookingRequestJson('start'),
            deviceTimestamp: $headerTime->format(\DateTimeInterface::RFC3339_EXTENDED),
            actionTimestamp: $actionTime->format(\DateTimeInterface::RFC3339_EXTENDED),
        );

        $this->validateOrderStatus($I, 'started');
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);

        return $this->getTimestampFromCurrentOrderStartCall($I);
    }

    private function createTestScenarioForTour(ApiTester $I): void
    {
        $this->currentDeviceId = 'random-device-'.uniqid();
        $this->currentTourExtId = 'random-tour-id-'.uniqid();
        $this->currentOrderExtId = 'random-order-id-'.uniqid();
        $this->currentTourName = 'random-tour-name-'.uniqid();
        $this->currentEquipmentExtId = 'random-equipment-'.uniqid();
        $this->currentEquipmentLicensePlate = 'random-plate-'.uniqid();

        $this->currentTestTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->currentTourExtId,
            orderExtId: $this->currentOrderExtId,
            tourName: $this->currentTourName,
            equipmentExtId: $this->currentEquipmentExtId,
            equipmentLicensePlate: $this->currentEquipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
    }

    private function setCreatedOrder(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->currentTestTourId);
        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');
        $I->assertEquals('created', $firstOrderStatus[0]);
        $this->currentOrderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
    }

    private function orderBookingRequestJson(string $booking): string
    {
        return <<<JSON
        {
            "booking": "{$booking}",
            "force": true
        }
        JSON;
    }

    private function getEquipmentForCurrentTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->currentEquipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    private function getTimestampFromCurrentTourStartCall(ApiTester $I): \DateTimeImmutable
    {
        $data = $I->wireMockFindOneRequestBody(
            method: 'POST',
            url: '/sap-germany/objectStatus',
            requestBody: [
                'objectType' => 'tour',
                'uuid' => $this->currentTestTourId,
                'subType' => null,
                'status' => 'started',
            ],
        );

        $ts = $data['timestamp'];
        $I->assertNotEmpty($ts);

        return new \DateTimeImmutable($ts);
    }

    private function getTimestampFromCurrentOrderStartCall(ApiTester $I)
    {
        $data = $I->wireMockFindOneRequestBody(
            method: 'POST',
            url: '/sap-germany/objectStatus',
            requestBody: [
                'objectType' => 'order',
                'uuid' => $this->currentOrderId,
                'subType' => null,
                'status' => 'started',
            ],
        );

        $ts = $data['timestamp'];
        $I->assertNotEmpty($ts);

        return new \DateTimeImmutable($ts);
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->currentTestTourId);

                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }
}
