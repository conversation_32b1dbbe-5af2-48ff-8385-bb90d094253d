<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupTemplateResponse;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\InterruptionFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\Api\SapGermanyTourInterruptionFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class SapGermanyTourInterruptionCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $deviceId = '';
    protected string $sapTourId = '';
    protected string $orderExtId = '';
    protected string $tourName = '';
    protected string $tourExtId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';

    protected string $clientUuid = '';
    protected string $interruptionTemplateId = '';
    protected ?HasTaskGroupTemplateResponse $disposalSiteTaskGroupTemplates = null;

    public function canReadDisposalSiteInterruption(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            disposalSiteJson: SapGermanyTourFixtures::getDisposalSiteJsonOrg(),
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $this->equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $this->equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('unequipped', $testEquipment[0]['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals('processed', $data['booking']);

        $I->callApiInterruptionTemplateGetV2();
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $expectedResult = SapGermanyTourInterruptionFixtures::getTourDisposalInterruption();
        $interruptions = array_filter($data, fn (array $interruptionData): bool => $interruptionData['name'] === $expectedResult->name && $interruptionData['source'] === $expectedResult->source);
        $I->assertCount(1, $interruptions);
        $disposalInterruption = $interruptions[0];

        $this->interruptionTemplateId = $disposalInterruption['template_uuid'];
        $I->assertNotEmpty($this->interruptionTemplateId);

        foreach ($expectedResult->taskGroupExpectedResults as $taskGroupExpectedResult) {
            $I->checkTaskGroupData($taskGroupExpectedResult, $disposalInterruption['taskgroups'], true);
        }

        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'weigh and 2 materials',
            interruption: $disposalInterruption,
            taskGroupIndex: 1,
            hasWeigh: true,
            hasWastePicker: true,
            materialCount: 2,
        );
        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'weigh without materials',
            interruption: $disposalInterruption,
            taskGroupIndex: 2,
            hasWeigh: true,
            hasWastePicker: false,
        );
        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'no weigh and no materials',
            interruption: $disposalInterruption,
            taskGroupIndex: 3,
            hasWeigh: false,
            hasWastePicker: false,
        );
        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'weigh and 1 material',
            interruption: $disposalInterruption,
            taskGroupIndex: 4,
            hasWeigh: true,
            hasWastePicker: true,
            materialCount: 1,
        );
    }

    #[Depends('canReadDisposalSiteInterruption')]
    public function canStartDisposalSiteInterruptionAfterUpdate(ApiTester $I): void
    {
        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            disposalSiteJson: SapGermanyTourFixtures::getDisposalSiteJsonExtended(),
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);

        $I->callApiInterruptionTemplateGetV2();
        $data = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $expectedResult = SapGermanyTourInterruptionFixtures::getTourDisposalInterruption();
        $interruptions = array_filter($data, fn (array $interruptionData): bool => $interruptionData['name'] === $expectedResult->name && $interruptionData['source'] === $expectedResult->source);
        $I->assertCount(1, $interruptions);
        $disposalInterruption = $interruptions[0];

        $this->interruptionTemplateId = $disposalInterruption['template_uuid'];
        $I->assertNotEmpty($this->interruptionTemplateId);

        foreach ($expectedResult->taskGroupExpectedResults as $taskGroupExpectedResult) {
            $I->checkTaskGroupData($taskGroupExpectedResult, $disposalInterruption['taskgroups'], true);
        }

        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'weigh and 2+1 materials',
            interruption: $disposalInterruption,
            taskGroupIndex: 1,
            hasWeigh: true,
            hasWastePicker: true,
            materialCount: 3,
        );
        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'weigh without materials',
            interruption: $disposalInterruption,
            taskGroupIndex: 2,
            hasWeigh: true,
            hasWastePicker: false,
        );
        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'no weigh and no materials',
            interruption: $disposalInterruption,
            taskGroupIndex: 3,
            hasWeigh: false,
            hasWastePicker: false,
        );
        $this->checkDisposalSite(
            I: $I,
            taskGroupName: 'weigh and 1+1 material',
            interruption: $disposalInterruption,
            taskGroupIndex: 4,
            hasWeigh: true,
            hasWastePicker: true,
            materialCount: 2,
        );

        $this->clientUuid = Uuid::v4()->toRfc4122();
        $I->callApiTourInterruptionV2(
            $this->interruptionTemplateId,
            InterruptionFixtures::bookingInterruptionStartRequestV2($this->clientUuid),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiInterruptionGetV2(includeNotCompleted: true);
            $interruptions = $I->grabDataFromResponseByJsonPath('$.items')[0];
            $I->assertCount(1, $interruptions);
            $I->assertEquals('started', $interruptions[0]['status']);
        });

        $interruptionTaskGroups = HasTaskGroupTemplateResponse::createFromResponse($disposalInterruption);

        $I->assertCount(5, $interruptionTaskGroups->taskGroupTemplates);
        $this->disposalSiteTaskGroupTemplates = $interruptionTaskGroups;
    }

    #[Depends('canStartDisposalSiteInterruptionAfterUpdate')]
    public function canUpdateTaskGroupsInInterruption(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $returnDescribers = SapGermanyTourInterruptionFixtures::getReturnToBackendDisposalSiteInterruptionDescription();
        $expectedCompletedTaskgroups = 0;

        foreach ($returnDescribers->taskGroups as $taskGroupDescriber) {
            $template = $this->disposalSiteTaskGroupTemplates->taskGroupTemplates[$taskGroupDescriber->position];
            $taskGroupRequest = $template->getReturn2Backend($taskGroupDescriber);

            $I->callApiTourInterruptionUpdateTaskGroupV2($this->clientUuid, json_encode($taskGroupRequest));
            ++$expectedCompletedTaskgroups;
        }

        $I->waitUntil(function (ApiTester $I) use ($expectedCompletedTaskgroups): void {
            $I->callApiInterruptionGetV2(includeNotCompleted: true);
            $completedTaskgroups = $I->grabDataFromResponseByJsonPath('$.items.*.taskgroups.[?(@.status=="completed")]');
            $I->assertCount($expectedCompletedTaskgroups, $completedTaskgroups);
        });
    }

    #[Depends('canUpdateTaskGroupsInInterruption')]
    public function canEndDisposalSiteInterruption(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourInterruptionV2(
            $this->interruptionTemplateId,
            InterruptionFixtures::bookingInterruptionEndRequestV2($this->clientUuid),
        );
    }

    #[Depends('canEndDisposalSiteInterruption')]
    public function canGetInterruptionHistory(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $I->waitUntil(
            function (ApiTester $I): void {
                $I->callApiInterruptionGetV2();
                $interruptionHistory = $I->grabDataFromResponseByJsonPath('$.items')[0];

                $I->assertCount(1, $interruptionHistory);
                $I->assertEquals('completed', $interruptionHistory[0]['status']);
                $I->assertEquals('disposalsite', $interruptionHistory[0]['name']);
                $I->assertIsArray($interruptionHistory[0]['taskgroups']);
                $I->assertCount(3, $interruptionHistory[0]['taskgroups']);
            },
        );
    }

    private function checkDisposalSite(
        ApiTester $I,
        string $taskGroupName,
        array $interruption,
        int $taskGroupIndex,
        bool $hasWeigh,
        bool $hasWastePicker,
        int $materialCount = 0,
    ): void {
        $taskGroup = $interruption['taskgroups'][$taskGroupIndex];
        $I->assertIsArray($taskGroup);
        $I->assertNotEmpty($taskGroup);
        $I->assertEquals($taskGroupName, $taskGroup['name']);

        $weighFound = false;
        $wastePickerFound = false;
        $foundMaterialCount = 0;

        foreach ($taskGroup['tasks'] as $task) {
            if ('weight' === $task['name']) {
                $weighFound = true;
            }

            if ('wastePicker' === $task['name']) {
                $wastePickerFound = true;
                $foundMaterialCount = count($task['inputs'][0]['options']);
                if (1 === $foundMaterialCount) {
                    $I->assertContains($task['inputs'][0]['options'][0]['key'], $task['inputs'][0]['expected_value']);
                }
            }
        }

        $I->assertEquals($hasWeigh, $weighFound);
        $I->assertEquals($hasWastePicker, $wastePickerFound);
        $I->assertEquals($materialCount, $foundMaterialCount);
    }
}
