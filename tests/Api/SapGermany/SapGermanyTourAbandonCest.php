<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class SapGermanyTourAbandonCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $deviceId = '';

    public function canAbandonCreatedTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tourExtId = 'random-abandon-tour-id-'.uniqid();
        $orderExtId = 'random-abandon-order-id-'.uniqid();
        $tourName = 'random-abandon-tour-name-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tourName,
            staffExternalId: $this->staffExtId,
        ));

        $I->callSapGermanyTourAbandon($tourExtId);
    }

    public function cannotAbandonStartedTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tourExtId = 'random-tour-id-'.uniqid();
        $orderExtId = 'random-order-id-'.uniqid();
        $tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tourName,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);

        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('unequipped', $testEquipment[0]['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });

        $I->callSapGermanyTourAbandon(
            tourExternalId: $tourExtId,
            expectedStatusCode: 409,
        );
    }
}
