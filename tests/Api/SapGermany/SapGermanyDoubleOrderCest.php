<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyDoubleOrderCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $tour1ExtId = '';
    protected string $order1ExtId = '';
    protected string $tour1Name = '';
    protected string $order2ExtId = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';

    public function canCreateTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tour1ExtId = 'random-tour-1-id-'.uniqid();
        $this->order1ExtId = 'random-order-1-id-'.uniqid();
        $this->order2ExtId = 'random-order-2-id-'.uniqid();
        $this->tour1Name = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::order2TourRequestCreate(
            tourExtId: $this->tour1ExtId,
            order1ExtId: $this->order1ExtId,
            order2ExtId: $this->order2ExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
    }

    #[Depends('canCreateTour')]
    public function canReadCreatedTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);

        $order1 = $I->grabDataFromResponseByJsonPath('$.orders[0]')[0];
        $order2 = $I->grabDataFromResponseByJsonPath('$.orders[1]')[0];

        $I->assertEquals(
            count($order1['additional_informations']),
            count($order2['additional_informations']),
        );

        $I->assertEquals(
            count($order1['termination_templates']),
            count($order2['termination_templates']),
        );

        foreach ($order1['termination_templates'] as $terminationTemplateIndex => $terminationTemplate) {
            $I->assertEquals(
                count($order1['termination_templates'][$terminationTemplateIndex]['taskgroups']),
                count($order2['termination_templates'][$terminationTemplateIndex]['taskgroups'])
            );

            $this->compareTaskgroups(
                $I,
                $order1['termination_templates'][$terminationTemplateIndex]['taskgroups'],
                $order2['termination_templates'][$terminationTemplateIndex]['taskgroups']
            );
        }

        $I->assertEquals(
            count($order1['note_templates']),
            count($order2['note_templates']),
        );

        foreach ($order1['note_templates'] as $noteTemplateIndex => $noteTemplate) {
            $I->assertEquals(
                count($order1['note_templates'][$noteTemplateIndex]['taskgroups']),
                count($order2['note_templates'][$noteTemplateIndex]['taskgroups'])
            );

            $this->compareTaskgroups(
                $I,
                $order1['note_templates'][$noteTemplateIndex]['taskgroups'],
                $order2['note_templates'][$noteTemplateIndex]['taskgroups']
            );
        }

        $I->assertEquals(
            count($order1['taskgroups']),
            count($order2['taskgroups']),
        );

        $this->compareTaskgroups(
            $I,
            $order1['taskgroups'],
            $order2['taskgroups']
        );
    }

    private function compareTaskgroups(ApiTester $I, array $tg1, array $tg2): void
    {
        foreach ($tg1 as $tgIndex => $taskGroupTemplate) {
            $I->assertEquals(
                count($tg1[$tgIndex]['rules']),
                count($tg2[$tgIndex]['rules'])
            );
            $I->assertEquals(
                count($tg1[$tgIndex]['tasks']),
                count($tg2[$tgIndex]['tasks'])
            );
            $I->assertEquals(
                count($tg1[$tgIndex]['additional_informations']),
                count($tg2[$tgIndex]['additional_informations'])
            );

            foreach ($taskGroupTemplate['tasks'] as $taskIndex => $taskTemplate) {
                $I->assertEquals(
                    count($tg1[$tgIndex]['tasks'][$taskIndex]['rules']),
                    count($tg2[$tgIndex]['tasks'][$taskIndex]['rules'])
                );
                $I->assertEquals(
                    count($tg1[$tgIndex]['tasks'][$taskIndex]['inputs']),
                    count($tg2[$tgIndex]['tasks'][$taskIndex]['inputs'])
                );
            }
        }
    }
}
