<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyStaffNameSpecialCharsCest
{
    private string $staffExtId = '';
    private string $tourExtId = '';
    private string $tourName = '';

    public function canCreateDriverWithSpecialChars(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-1-id-'.uniqid();
        $this->tourName = 'random-tour-1-name-'.uniqid();

        $deviceId = 'random-device-'.uniqid();
        $firstName = 'Some-v-one (ext)';
        $lastName = 'el%$s^e';

        $sapTourId = $I->callSapGermanyTourUpsert(
            $this->getTourJson(
                staffExternalId: $this->staffExtId,
                firstName: $firstName,
                lastName: $lastName,
                tourExtId: $this->tourExtId,
                tourName: $this->tourName,
            ),
            debug: false,
        );

        $I->assertNotEmpty($sapTourId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiUserProfileV2();
        $userData = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals('Some-v-one ext', $userData['first_name']);
        $I->assertEquals('else', $userData['last_name']);
    }

    #[Depends('canCreateDriverWithSpecialChars')]
    public function canUpdateDriverWithSpecialChars(ApiTester $I): void
    {
        $firstName = 'Some-v-one (ext)-added';
        $lastName = 'el%$s^e';

        $sapTourId = $I->callSapGermanyTourUpsert(
            $this->getTourJson(
                staffExternalId: $this->staffExtId,
                firstName: $firstName,
                lastName: $lastName,
                tourExtId: $this->tourExtId,
                tourName: $this->tourName,
            ),
            debug: false,
        );

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalStaffList(query: $this->staffExtId);

        $staff = $I->grabDataFromResponseByJsonPath('$.items.*');
        $I->assertIsArray($staff);
        $I->assertEquals('Some-v-one ext-added', $staff[0]['firstname']);
    }

    private function getTourJson(
        string $staffExternalId,
        string $firstName,
        string $lastName,
        string $tourExtId,
        string $tourName,
    ): string {
        $startDate = new \DateTime()->setTime(2, 0)->format(DATE_ATOM);
        $endDate = new \DateTime()->setTime(23, 0)->format(DATE_ATOM);

        return <<<JSON
        {
            "staff": [
                {
                    "staffExtId": "{$staffExternalId}",
                    "personnelType": "DRIVER",
                    "firstName": "{$firstName}",
                    "lastName": "{$lastName}"
                }
            ],
            "addresses": [
                {
                    "country": "DE",
                    "postalCode": "32457",
                    "city": "Porta Westfalica",
                    "district": "Lerbeck",
                    "street": "An der Pforte",
                    "houseNumber": "2",
                    "phoneNumber": "",
                    "state": "",
                    "addressExtId": "2066747"
                }
            ],
            "customers": [],
            "equipments": [],
            "orders": [],
            "tourExtId": "{$tourExtId}",
            "name": "{$tourName}",
            "branchExternalId": "Oelbronn",
            "startTimestamp": "{$startDate}",
            "endTimestamp": "{$endDate}",
            "startAddressExtId": "2066747",
            "endAddressExtId": "2066747",
            "forcedOrder": false,
            "country": "DE",
            "disposalSites": [],
            "additionalInformation": [],
            "interruptionExternalId": "1234"
        }
        JSON;
    }
}
