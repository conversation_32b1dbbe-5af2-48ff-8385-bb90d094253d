<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use App\Tests\Support\InMemoryCache;
use Codeception\Attribute\Depends;

class SapGermanyReturnTourCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $orderExtId = '';
    protected string $device1 = '';
    protected string $device2 = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $tourExtId = '';
    protected string $tourName = '';

    /**
     * @var string[]
     */
    protected array $equipmentExtIds = [
    ];

    public function canStartSessionWithEquipment(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->device1 = 'random-device1-unique-id-'.uniqid();
        $this->device2 = 'random-device2-unique-id-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });
    }

    #[Depends('canStartSessionWithEquipment')]
    public function canStartTourToReturn(ApiTester $I): void
    {
        $wireMockMappingIds = $I->wireMockSetSapGermanyStatusTourStartMappings($this->tourExtId, [$this->staffExtId], $this->equipmentExtIds);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device1);
        $I->callApiTourGetCollectionV2();

        $uuids = $I->grabDataFromResponseByJsonPath('$.items.*.uuid');
        $names = $I->grabDataFromResponseByJsonPath('$.items.*.name');
        $I->assertContains($this->sapTourId, $uuids);
        $I->assertContains($this->tourName, $names);

        $I->callApiTourGetV2($this->sapTourId);
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartTourToReturn')]
    public function canStartOrderInTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrderId($I);

        $I->callApiOrderBookingV2($this->orderExtId, $this->orderBookingRequestJson('start'));
        $this->validateOrderStatus($I, 'started', $this->device1);
    }

    #[Depends('canStartOrderInTour')]
    public function canReturnTour(ApiTester $I): void
    {
        InMemoryCache::clear();
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device2);
        $I->callApiUserStartSessionV2();

        $I->callApiTourGetCollectionV2();

        $uuids = $I->grabDataFromResponseByJsonPath('$.items.*.uuid');
        $names = $I->grabDataFromResponseByJsonPath('$.items.*.name');
        $I->assertContains($this->sapTourId, $uuids);
        $I->assertContains($this->tourName, $names);

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('returned', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $equipment['uuid'],
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });
    }

    #[Depends('canReturnTour')]
    public function canRestartTour(ApiTester $I): void
    {
        $wireMockMappingIds = $I->wireMockSetSapGermanyStatusTourStartMappings($this->tourExtId, [$this->staffExtId], $this->equipmentExtIds);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->device2);
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $this->validateOrderStatus($I, 'started', $this->device2);
        $I->wireMockVerifyNegativeRequestMapping($wireMockMappingIds);
    }

    #[Depends('canRestartTour')]
    public function canEndReturnedOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2(
            orderId: $this->orderExtId,
            data: $this->orderBookingRequestJson('end'),
        );

        $this->validateOrderStatus($I, 'completed', $this->device2);
    }

    private function setCreatedOrderId(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);
        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');
        $I->assertEquals('created', $firstOrderStatus[0]);

        $this->orderExtId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    private function orderBookingRequestJson(string $booking): string
    {
        return <<<JSON
        {
            "booking": "$booking",
            "force": true
        }
        JSON;
    }

    private function validateOrderStatus(ApiTester $I, string $status, string $deviceId): void
    {
        $I->amUsingDeviceWithId($deviceId);
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            }
        );
    }
}
