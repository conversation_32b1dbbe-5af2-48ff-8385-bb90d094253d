<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Domain\Services\ObjectStorage\ObjectPrefix;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupsResponse;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupTemplateResponse;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskTemplatesReponse;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\Return2BackendDescriber\ReturnInputFileDescriber;
use App\Tests\Support\Api\SapGermanyOrderTaskGroupFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\Api\SapGermanyTourTerminationFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyTourTerminationCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $terminationTemplateId = '';
    protected ?HasTaskGroupTemplateResponse $terminationTaskGroupTemplates = null;
    protected string $orderId = '';
    protected ?HasTaskGroupsResponse $orderTaskGroups = null;
    protected ?HasTaskTemplatesReponse $additionalServices = null;
    protected string $tourExtId = '';
    protected string $orderExtId = '';
    protected string $tourName = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected string $equipmentUuid = '';

    public function canCreateTourToTerminate(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);

        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);

        $I->callApiEquipmentGetCollectionV2();
        $testEquipment = array_values(array_filter(
            $I->grabDataFromResponseByJsonPath('$.items[*]'),
            fn (array $equipment): bool => $equipment['name'] === $this->equipmentLicensePlate,
        ));
        $I->assertCount(1, $testEquipment);
        $this->equipmentUuid = $testEquipment[0]['uuid'];
        $I->assertEquals('unequipped', $testEquipment[0]['status']);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2(
                $this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('started', $tourStatus[0]);
        });
    }

    #[Depends('canCreateTourToTerminate')]
    public function canSeeTourTermination(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);

        $rawData = $I->grabDataFromResponseByJsonPath('$.termination_templates')[0];
        $expectedResult = SapGermanyTourTerminationFixtures::getTourTermination();
        $terminations = array_filter($rawData, fn (array $terminationData): bool => $terminationData['name'] === $expectedResult->name);
        $I->assertCount(1, $terminations);
        $tourTermination = $terminations[0];
        $this->terminationTemplateId = $tourTermination['template_uuid'];
        $I->assertNotEmpty($this->terminationTemplateId);

        foreach ($expectedResult->taskGroupExpectedResults as $taskGroupExpectedResult) {
            $I->checkTaskGroupData($taskGroupExpectedResult, $tourTermination['taskgroups'], true);
        }

        $this->terminationTaskGroupTemplates = HasTaskGroupTemplateResponse::createFromResponse($tourTermination);
    }

    #[Depends('canSeeTourTermination')]
    public function canStartOrderInSapGermanyTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrder($I);
        $this->setAdditionalServices($I);

        $I->callApiOrderBookingV2($this->orderId, $this->orderBookingRequestJson('start'));

        $this->validateOrderStatus($I, 'started');
    }

    #[Depends('canStartOrderInSapGermanyTour')]
    public function canUpdateTaskGroupsInOrder(ApiTester $I): void
    {
        $taskGroupDescriber = SapGermanyOrderTaskGroupFixtures::getReturnToBackendOrderTaskGroupDescription();
        $taskGroupToReturn = $this->orderTaskGroups->taskGroups[$taskGroupDescriber->position];
        $taskGroupRequest = $taskGroupToReturn->getReturn2Backend($taskGroupDescriber, $this->additionalServices);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderTaskGroupPatchV2(
            orderId: $this->orderId,
            data: json_encode($taskGroupRequest),
        );

        // validate taskGroup is completed now
        $I->waitUntil(
            function (ApiTester $I) use ($taskGroupDescriber): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderTaskGroupTasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups['.$taskGroupDescriber->position.'][tasks]')[0];
                $I->assertIsArray($firstOrderTaskGroupTasks);
                $I->assertCount(count($taskGroupDescriber->tasks), $firstOrderTaskGroupTasks);

                foreach ($firstOrderTaskGroupTasks as $task) {
                    if ('skipme' === $task['name']) {
                        $I->assertEquals('skipped', $task['status']);
                    } else {
                        $I->assertEquals('completed', $task['status']);
                    }
                }
            }
        );

        // upload files
        $this->uploadTaskFiles($taskGroupRequest['tasks'], $I);
    }

    #[Depends('canUpdateTaskGroupsInOrder')]
    public function canTerminateTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $returnDescribers = SapGermanyTourTerminationFixtures::getReturnToBackendTourTerminationDescription();

        $terminationRequest['template_uuid'] = $this->terminationTemplateId;
        $terminationRequest['taskgroups'] = [];
        for ($i = 0; $i < count($this->terminationTaskGroupTemplates->taskGroupTemplates); ++$i) {
            $terminationRequest['taskgroups'][] = $this->terminationTaskGroupTemplates->taskGroupTemplates[$i]
                ->getReturn2Backend($returnDescribers->taskGroups[$i]);
        }

        $I->callApiTourTerminationV2(
            $this->sapTourId,
            json_encode($terminationRequest),
        );

        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('terminated', $tourStatus[0]);
        });

        $I->callApiTourTerminationHistoryV2($this->sapTourId);

        $terminations = $I->grabDataFromResponseByJsonPath('$.items');
        $I->assertCount(1, $terminations);
    }

    private function setCreatedOrder(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);
        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');

        $I->assertEquals('created', $firstOrderStatus[0]);

        $this->orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];

        $this->orderTaskGroups = HasTaskGroupsResponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.orders[0]')[0]);
        $I->assertCount(8, $this->orderTaskGroups->taskGroups);
    }

    private function orderBookingRequestJson(string $booking): string
    {
        return <<<JSON
        {
            "booking": "$booking",
            "force": true
        }
        JSON;
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);
                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }

    private function setAdditionalServices(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);
        $this->additionalServices = HasTaskTemplatesReponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.additional_service_templates')[0]);
    }

    private function uploadTaskFiles(array $sentTasks, ApiTester $I)
    {
        foreach ($sentTasks as $sentTask) {
            foreach ($sentTask['inputs'] as $sentInput) {
                if (is_array($sentInput['value'])) {
                    foreach ($sentInput['value'] as $sentValue) {
                        if ($sentValue instanceof ReturnInputFileDescriber) {
                            $fileContents = file_get_contents(codecept_data_dir($sentValue->name));
                            $uuid = str_replace('app-user-files/', '', $sentValue->file);

                            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
                            $I->amUsingDeviceWithId($this->deviceId);
                            $I->callApiFileUploadInDirectoryV2('app-user-files', $uuid, $fileContents);

                            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
                            $I->callApiFileRetrieveV2(ObjectPrefix::APP_USER_FILES->value.$uuid);
                            $I->seeHttpHeader('Content-Type', 'image/png');
                            $I->assertEquals($fileContents, $I->grabResponse());
                        }
                    }
                }
            }
        }
    }
}
