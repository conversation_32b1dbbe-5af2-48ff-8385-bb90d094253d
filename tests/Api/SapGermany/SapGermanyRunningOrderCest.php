<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BackendResponseExtractions\HasTaskGroupsResponse;
use App\Tests\Support\Api\BackendResponseExtractions\HasTaskTemplatesReponse;
use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyOrderTaskGroupFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyRunningOrderCest
{
    private string $tenant = 'pz';
    private string $staffExtId = '';
    protected string $sapTourId = '';
    protected string $tourExtId = '';
    protected string $orderExtId = '';
    protected string $orderId = '';
    protected string $tourName = '';
    protected string $deviceId = '';
    protected string $equipmentExtId = '';
    protected string $equipmentLicensePlate = '';
    protected ?HasTaskGroupsResponse $orderTaskGroups = null;
    protected ?HasTaskTemplatesReponse $additionalServices = null;

    public function canCreateTourWithoutSapTasks(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-1-id-'.uniqid();
        $this->orderExtId = 'random-order-1-id-'.uniqid();
        $this->tourName = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sendTourUpdateToHermes($I, false);
    }

    #[Depends('canCreateTourWithoutSapTasks')]
    public function canStartTour(ApiTester $I): void
    {
        $I->amUsingDeviceWithId($this->deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: true),
        );
        $this->setOrderId($I);
    }

    #[Depends('canStartTour')]
    public function canStartOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2($this->orderId, BookingFixtures::bookingStartRequestV2());

        $this->validateOrderStatus($I, 'started');
        $I->assertNull($this->getWeightNoteTaskStatus($I));
    }

    #[Depends('canStartOrder')]
    public function canActivateTasksBySap(ApiTester $I): void
    {
        $this->sendTourUpdateToHermes($I, true);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->assertEquals('created', $this->getWeightNoteTaskStatus($I));
        $this->setOrderTaskGroups($I);
    }

    #[Depends('canActivateTasksBySap')]
    public function canDeactivateTasksBySap(ApiTester $I): void
    {
        $this->sendTourUpdateToHermes($I, false);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->assertNull($this->getWeightNoteTaskStatus($I));
    }

    #[Depends('canDeactivateTasksBySap')]
    public function canCompleteUnassignedTask(ApiTester $I): void
    {
        $taskGroupDescriber = SapGermanyOrderTaskGroupFixtures::getReturnToBackendOrderTaskGroupDescription();
        $taskGroupToReturn = $this->orderTaskGroups->taskGroups[$taskGroupDescriber->position];

        $taskGroupRequest = $taskGroupToReturn->getReturn2Backend($taskGroupDescriber, $this->additionalServices);

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderTaskGroupPatchV2(
            orderId: $this->orderId,
            data: json_encode($taskGroupRequest),
        );

        // validate taskGroup is completed now
        $I->waitUntil(
            function (ApiTester $I) use ($taskGroupDescriber): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderTaskGroupTasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups['.$taskGroupDescriber->position.'][tasks]')[0];
                $I->assertIsArray($firstOrderTaskGroupTasks);
                $I->assertCount(count($taskGroupDescriber->tasks), $firstOrderTaskGroupTasks);

                foreach ($firstOrderTaskGroupTasks as $task) {
                    if ('skipme' === $task['name']) {
                        $I->assertEquals('skipped', $task['status']);
                    } else {
                        $I->assertEquals('completed', $task['status']);
                    }
                }
            }
        );
        $I->assertEquals('completed', $this->getWeightNoteTaskStatus($I));
    }

    #[Depends('canCompleteUnassignedTask')]
    public function setOrderTaskGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);
        $this->orderTaskGroups = HasTaskGroupsResponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.orders[0]')[0]);
        $I->assertCount(8, $this->orderTaskGroups->taskGroups);
        $this->additionalServices = HasTaskTemplatesReponse::createFromResponse($I->grabDataFromResponseByJsonPath('$.additional_service_templates')[0]);
    }

    private function sendTourUpdateToHermes(ApiTester $I, bool $includeActivatedBySapTasks): void
    {
        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            includeActivatedBySapTask: $includeActivatedBySapTasks,
            staffExternalId: $this->staffExtId,
        ));
    }

    private function getWeightNoteTaskStatus(ApiTester $I): ?string
    {
        $I->callApiTourGetV2($this->sapTourId);

        $taskGroups = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups')[0];

        foreach ($taskGroups as $taskGroup) {
            foreach ($taskGroup['tasks'] as $task) {
                if ('weighingnoteSentBySAP' === $task['name']) {
                    return $task['status'];
                }
            }
        }

        return null;
    }

    private function setOrderId(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);
        $this->orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);

                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }
}
