<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Domain\Entity\Enum\Tenant;
use App\Tests\Support\Api\SampleGermanyTour;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapGermanyAdditionalInformationCest
{
    private array $tourAdditionalInformation = [];
    private array $orderAdditionalInformation = [];
    private array $taskGroupAdditionalInformation = [];
    private string $tourExternalId = '';
    private string $deviceId = '';
    private string $staffExternalId = '';

    public function canCreateTourWithAdditionalInformation(ApiTester $I): void
    {
        $this->deviceId = uniqid();
        $this->tourExternalId = uniqid();
        $this->staffExternalId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF TOUR START',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->orderAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF ORDER START',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->taskGroupAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF TASK GROUP START',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->ingestTourAndRetrieveTourInformation($I);

        // Assert 2 tour additional information - one from config and one from this request
        $createdTourAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.additional_informations')[0];
        $I->assertCount(2, $createdTourAdditionalInformation);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF TOUR START', $createdTourAdditionalInformation[0]['text']);
        $I->assertEquals('default tour additional information 1 - be28ba3b-6311-46d2-a6ba-054911124f43', $createdTourAdditionalInformation[1]['text']);

        // Assert 4 order additional information - two from location, one from this request and one from config
        $createdOrderAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].additional_informations')[0];
        $I->assertCount(4, $createdOrderAdditionalInformation);
        $I->assertEquals('person', $createdOrderAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderAdditionalInformation[1]['icon']);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF ORDER START', $createdOrderAdditionalInformation[2]['text']);
        $I->assertEquals('handyman', $createdOrderAdditionalInformation[3]['icon']);

        // Assert order second taskgroup (customer) contains 3 additional information - from location and one from this request
        $createdOrderTaskGroupAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[1].additional_informations')[0];
        $I->assertCount(3, $createdOrderTaskGroupAdditionalInformation);
        $I->assertEquals('person', $createdOrderTaskGroupAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderTaskGroupAdditionalInformation[1]['icon']);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF TASK GROUP START', $createdOrderTaskGroupAdditionalInformation[2]['text']);
    }

    #[Depends('canCreateTourWithAdditionalInformation')]
    public function settingAdditionalInformationAfterCreateWorks(ApiTester $I): void
    {
        $this->tourAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF TOUR',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
            [
                'sequence' => 20,
                'text' => 'STANDARD ask-10-pr-BEHA TOUR',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->orderAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF ORDER',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
            [
                'sequence' => 20,
                'text' => 'STANDARD ask-10-pr-BEHA ORDER',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->taskGroupAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF TASK GROUP',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
            [
                'sequence' => 20,
                'text' => 'STANDARD ask-10-pr-BEHA TASK GROUP',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->ingestTourAndRetrieveTourInformation($I);

        // Now there should be 3 tour additional information - one from config and 2 from this request
        $createdTourAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.additional_informations')[0];
        $I->assertCount(3, $createdTourAdditionalInformation);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF TOUR', $createdTourAdditionalInformation[0]['text']);
        $I->assertEquals('STANDARD ask-10-pr-BEHA TOUR', $createdTourAdditionalInformation[1]['text']);
        $I->assertEquals('default tour additional information 1 - be28ba3b-6311-46d2-a6ba-054911124f43', $createdTourAdditionalInformation[2]['text']);

        // Assert 5 order additional information - two from location, 2 from this request and one from config
        $createdOrderAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].additional_informations')[0];
        $I->assertCount(5, $createdOrderAdditionalInformation);
        $I->assertEquals('person', $createdOrderAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderAdditionalInformation[1]['icon']);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF ORDER', $createdOrderAdditionalInformation[2]['text']);
        $I->assertEquals('STANDARD ask-10-pr-BEHA ORDER', $createdOrderAdditionalInformation[3]['text']);
        $I->assertEquals('handyman', $createdOrderAdditionalInformation[4]['icon']);

        // Assert order second taskgroup (customer) contains 4 additional information - from location and 2 from this request
        $createdOrderTaskGroupAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[1].additional_informations')[0];
        $I->assertCount(4, $createdOrderTaskGroupAdditionalInformation);
        $I->assertEquals('person', $createdOrderTaskGroupAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderTaskGroupAdditionalInformation[1]['icon']);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF TASK GROUP', $createdOrderTaskGroupAdditionalInformation[2]['text']);
        $I->assertEquals('STANDARD ask-10-pr-BEHA TASK GROUP', $createdOrderTaskGroupAdditionalInformation[3]['text']);
    }

    #[Depends('settingAdditionalInformationAfterCreateWorks')]
    public function updatingAdditionalInformationWorks(ApiTester $I): void
    {
        $this->tourAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF TOUR 123',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->orderAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF ORDER 123',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->taskGroupAdditionalInformation = [
            [
                'sequence' => 10,
                'text' => '1.04 B19 Kartonage-150101-ABF TASK GROUP 123',
                'alsoFrontView' => true,
                'bold' => false,
                'icon' => '',
            ],
        ];

        $this->ingestTourAndRetrieveTourInformation($I);

        // Now there should be 2 tour additional information - one from config and one from this request
        $createdTourAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.additional_informations')[0];
        $I->assertCount(2, $createdTourAdditionalInformation);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF TOUR 123', $createdTourAdditionalInformation[0]['text']);
        $I->assertEquals('default tour additional information 1 - be28ba3b-6311-46d2-a6ba-054911124f43', $createdTourAdditionalInformation[1]['text']);

        // Assert 4 order additional information - two from location, one from this request and one from config
        $createdOrderAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].additional_informations')[0];
        $I->assertCount(4, $createdOrderAdditionalInformation);
        $I->assertEquals('person', $createdOrderAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderAdditionalInformation[1]['icon']);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF ORDER 123', $createdOrderAdditionalInformation[2]['text']);
        $I->assertEquals('handyman', $createdOrderAdditionalInformation[3]['icon']);

        // Assert order second taskgroup (customer) contains 3 additional information - from location and one from this request
        $createdOrderTaskGroupAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[1].additional_informations')[0];
        $I->assertCount(3, $createdOrderTaskGroupAdditionalInformation);
        $I->assertEquals('person', $createdOrderTaskGroupAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderTaskGroupAdditionalInformation[1]['icon']);
        $I->assertEquals('1.04 B19 Kartonage-150101-ABF TASK GROUP 123', $createdOrderTaskGroupAdditionalInformation[2]['text']);
    }

    #[Depends('updatingAdditionalInformationWorks')]
    public function removingAdditionalInformationWorks(ApiTester $I): void
    {
        $this->tourAdditionalInformation = [];
        $this->orderAdditionalInformation = [];
        $this->taskGroupAdditionalInformation = [];

        $this->ingestTourAndRetrieveTourInformation($I);

        // Assert a single tour additional information - one coming from config
        $createdTourAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.additional_informations')[0];
        $I->assertCount(1, $createdTourAdditionalInformation);
        $I->assertEquals('default tour additional information 1 - be28ba3b-6311-46d2-a6ba-054911124f43', $createdTourAdditionalInformation[0]['text']);

        // Assert three order additional information - two from location and one from config
        $createdOrderAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].additional_informations')[0];
        $I->assertCount(3, $createdOrderAdditionalInformation);
        $I->assertEquals('person', $createdOrderAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderAdditionalInformation[1]['icon']);
        $I->assertEquals('handyman', $createdOrderAdditionalInformation[2]['icon']);

        // Assert order second taskgroup (customer) contains two additional information - from location
        $createdOrderTaskGroupAdditionalInformation = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[1].additional_informations')[0];
        $I->assertCount(2, $createdOrderTaskGroupAdditionalInformation);
        $I->assertEquals('person', $createdOrderTaskGroupAdditionalInformation[0]['icon']);
        $I->assertEquals('pin_drop', $createdOrderTaskGroupAdditionalInformation[1]['icon']);
    }

    private function ingestTourAndRetrieveTourInformation(ApiTester $I): void
    {
        $tourId = $I->callSapGermanyTourUpsert(SampleGermanyTour::additionalInformationFixture(
            tourExternalId: $this->tourExternalId,
            staffExternalId: $this->staffExternalId,
            tourAdditionalInformation: $this->tourAdditionalInformation,
            orderAdditionalInformation: $this->orderAdditionalInformation,
            taskGroupAdditionalInformation: $this->taskGroupAdditionalInformation,
        ));
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExternalId, Tenant::GERMANY->value);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($tourId);
    }
}
