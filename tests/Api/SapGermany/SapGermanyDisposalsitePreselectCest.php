<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class SapGermanyDisposalsitePreselectCest
{
    private string $staffExtId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $deviceId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $sapTourId = '';
    private string $tenant = 'pz';

    public function canCreateSingleDisposalSiteTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            disposalSiteJson: SapGermanyTourFixtures::getDisposalSiteJsonSingle(),
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: true),
        );
        $data = $I->grabDataFromResponseByJsonPath('$')[0];
        $I->assertEquals('processed', $data['booking']);

        $I->callApiInterruptionTemplateGetV2();
        $disposalSiteInterruption = $I->grabDataFromResponseByJsonPath('$.items[?(@.name=={{disposal_site}})]')[0];
        $I->assertIsArray($disposalSiteInterruption);
        $disposalsiteSelect = $disposalSiteInterruption['taskgroups'][0]['tasks'][0]['inputs'][0];
        $I->assertIsArray($disposalsiteSelect);

        $I->assertCount(1, $disposalsiteSelect['options']);
        $I->assertEquals($disposalsiteSelect['expected_value'][0], $disposalsiteSelect['options'][0]['key']);
    }
}
