<?php

declare(strict_types=1);

namespace App\Tests\Api\SapGermany;

use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class SapGermanyAdditionalServiceConfigCest
{
    public function canCreateTourWithBranchConfig(ApiTester $I): void
    {
        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tour1ExtId = 'random-tour-1-id-'.uniqid();
        $order1ExtId = 'random-order-1-id-'.uniqid();
        $tour1Name = 'random-tour-1-name-'.uniqid();
        $deviceId = 'random-device-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tour1ExtId,
            orderExtId: $order1ExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExtId,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($sapTourId);

        $I->assertCount(2, $I->grabDataFromResponseByJsonPath('$.additional_service_templates')[0]);
        $serviceName1 = $I->grabDataFromResponseByJsonPath('$.additional_service_templates[0].name')[0];
        $serviceName2 = $I->grabDataFromResponseByJsonPath('$.additional_service_templates[1].name')[0];

        $I->assertEquals('sap-api-test-as3', $serviceName1);
        $I->assertEquals('sap-api-test-as4', $serviceName2);
    }

    public function canCreateTourWithOnlyCountryConfig(ApiTester $I): void
    {
        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tour1ExtId = 'random-tour-1-id-'.uniqid();
        $order1ExtId = 'random-order-1-id-'.uniqid();
        $tour1Name = 'random-tour-1-name-'.uniqid();
        $deviceId = 'random-device-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tour1ExtId,
            orderExtId: $order1ExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExtId,
            branchExternalId: 'somewhereElse'
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($sapTourId);

        $I->assertCount(2, $I->grabDataFromResponseByJsonPath('$.additional_service_templates')[0]);
        $serviceName1 = $I->grabDataFromResponseByJsonPath('$.additional_service_templates[0].name')[0];
        $serviceName2 = $I->grabDataFromResponseByJsonPath('$.additional_service_templates[1].name')[0];

        $I->assertEquals('sap-api-test-as1', $serviceName1);
        $I->assertEquals('sap-api-test-as2', $serviceName2);
    }
}
