<?php

/**
 * @noinspection PhpDocMissingThrowsInspection
 * @noinspection PhpUnhandledExceptionInspection
 */

namespace App\Tests\Api;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\OrderTaskGroupFixtures;
use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class OrderTaskGroupCest
{
    private string $sapTourId;

    private string $tenant = 'pz';
    private string $staffExtId = 'pz';

    public function canUpdateTaskGroups(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $this->staffExtId,
        ));

        $deviceId = 'random-device-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->callApiTourGetV2($this->sapTourId);
        $orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];

        $I->callApiOrderBookingV2($orderId, BookingFixtures::bookingStartRequestV2());

        $I->waitUntil(
            function (ApiTester $I): void {
                $I->callApiTourGetV2($this->sapTourId);
                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals('started', $firstOrderStatus);
            }
        );

        $response = $I->grabResponse();
        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');

        $I->assertEquals('started', $firstOrderStatus[0]);
        $tasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[2].tasks[?(@.status=="completed")]');
        $I->assertCount(0, $tasks);

        $I->callApiOrderTaskGroupPatchV2(
            $orderId,
            OrderTaskGroupFixtures::patchTaskGroupRequestV2($response),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2($this->sapTourId);
            $tasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[2].tasks[?(@.status=="completed")]');

            $I->assertCount(3, $tasks);
        });
    }

    public function canUpdateSignatureTaskGroups(ApiTester $I): void
    {
        $staffExternalId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: 'random-tour-id-'.uniqid(),
            orderExtId: 'random-order-id-'.uniqid(),
            tourName: 'random-tour-name-'.uniqid(),
            equipmentExtId: 'random-equipment-'.uniqid(),
            equipmentLicensePlate: 'random-plate-'.uniqid(),
            staffExternalId: $staffExternalId,
        ));
        $deviceId = 'random-device-'.uniqid();
        $I->amUsingDeviceWithId($deviceId);
        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExternalId, $this->tenant);
        $I->callApiUserStartSessionV2();

        $I->callApiTourGetV2($this->sapTourId);
        $orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];

        $I->callApiOrderBookingV2($orderId, BookingFixtures::bookingStartRequestV2());

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2($this->sapTourId);
            $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status');

            $I->assertEquals('started', $firstOrderStatus[0]);
            $tasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[1].tasks[?(@.status=="completed")]');
            $I->assertCount(0, $tasks);
        });

        $response = $I->grabResponse();

        $I->callApiOrderTaskGroupPatchV2(
            $orderId,
            OrderTaskGroupFixtures::patchSignatureTaskGroupRequestV2($response),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiTourGetV2($this->sapTourId);
            $tasks = $I->grabDataFromResponseByJsonPath('$.orders[0].taskgroups[1].tasks[?(@.status=="completed")]');

            $I->assertCount(1, $tasks);
        });
    }
}
