<?php

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;

class SapNetherlandsEquipmentListCest
{
    private string $staffExtId = '';
    private string $deviceId = '';

    public function canGetTourEquipment(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->deviceId = 'random-nl-device-'.uniqid();
        $branchExternalId = 'random-branch-'.uniqid();
        $additionalEquipmentId = 'random-eq-'.uniqid();
        $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: 'random-nl-tour-id-'.uniqid(),
            orderExtId: 'random-nl-order-id-'.uniqid(),
            tourName: 'random-nl-tour-name-'.uniqid(),
            equipmentExtId: 'random-nl-equipment-'.uniqid(),
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: 'random-nl-license-plate-'.uniqid(),
            branchExternalId: $branchExternalId,
            additionalEquipmentId: $additionalEquipmentId,
        ));

        $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: 'random-nl-tour-id-'.uniqid(),
            orderExtId: 'random-nl-order-id-'.uniqid(),
            tourName: 'random-nl-tour-name-'.uniqid(),
            equipmentExtId: 'random-nl-equipment-'.uniqid(),
            staffExtId: $I::TEST_STAFF_EXT_ID_PREFIX.uniqid(),
            equipmentLicensePlate: 'random-nl-license-plate-'.uniqid(),
            branchExternalId: $branchExternalId,
            additionalEquipmentId: $additionalEquipmentId,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, 'pzn');
            $I->amUsingDeviceWithId($this->deviceId);
            $I->callApiUserStartSessionV2();
        });

        $I->callApiEquipmentGetCollectionV2();
        $I->assertCount(3, $I->grabDataFromResponseByJsonPath('$.items')[0]);

        $I->callApiEquipmentListExtended();
        $I->assertCount(5, $I->grabDataFromResponseByJsonPath('$.items')[0]);

        $I->callApiEquipmentListExtended(
            equipmentTypes: ['ask', 'eq7800'],
        );
        $I->assertCount(5, $I->grabDataFromResponseByJsonPath('$.items')[0]);

        $I->callApiEquipmentListExtended(
            equipmentTypes: ['eq7800'],
        );
        $I->assertCount(2, $I->grabDataFromResponseByJsonPath('$.items')[0]);

        $I->callApiEquipmentListExtended(
            equipmentTypeCategories: ['truck'],
        );
        $I->assertCount(3, $I->grabDataFromResponseByJsonPath('$.items')[0]);

        $I->callApiEquipmentListExtended(
            equipmentTypes: ['ask'],
            equipmentTypeCategories: ['trailer'],
        );
        $I->assertCount(5, $I->grabDataFromResponseByJsonPath('$.items')[0]);

        $I->callApiEquipmentListExtended(
            equipmentTypes: ['ask'],
            equipmentTypeCategories: ['trailer'],
            query: 'NL-AK 1'
        );
        $I->assertCount(1, $I->grabDataFromResponseByJsonPath('$.items')[0]);
    }
}
