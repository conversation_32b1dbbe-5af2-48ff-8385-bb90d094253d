<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

class SapNetherlandsOrderCest
{
    /**
     * @var string[]
     */
    private array $orderIds = [];

    private string $staffExtId = '';
    private string $deviceId = '';
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $equipmentExtId = '';
    private string $orderExtId = '';

    private string $tenant = 'pzn';

    public function canBookOrder(ApiTester $I): void
    {
        $orders = $this->createTourAndOrder($I);

        $wireMockMappingId = $this->setOrderStatusWireMockMapping($I, $this->orderExtId, 'started');

        $I->assertCount(3, $orders);
        $this->orderIds = array_column($orders, 'uuid');
        $I->callApiTourBookingV2($this->sapTourId, BookingFixtures::bookingStartRequestV2(force: true));
        $I->callApiOrderBookingV2($this->orderIds[0], BookingFixtures::bookingStartRequestV2());
        $I->wireMockVerifyRequestMapping([$wireMockMappingId]);
        $this->completeOrderTaskGroups($I, $orders[0]);
    }

    #[Depends('canBookOrder')]
    public function canSwitchOrderByBooking(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId, 'break'),
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId.'-2', 'started'),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2($this->orderIds[1], BookingFixtures::bookingStartRequestV2());
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canSwitchOrderByBooking')]
    public function canSwitchOrderByFocus(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId, 'restart'),
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId.'-2', 'break'),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderFocusV2($this->orderIds[0]);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canSwitchOrderByFocus')]
    public function canSwitchBackOrderByFocus(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId, 'break'),
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId.'-2', 'restart'),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderFocusV2($this->orderIds[1]);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canSwitchBackOrderByFocus')]
    public function canFocusStaff(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $this->setStaffStatusWireMockMapping($I, $this->staffExtId, 'user_switch'),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiStaffFocus();

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canFocusStaff')]
    public function canEndOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $wireMockMappingIds = [
            $this->setOrderStatusWireMockMapping($I, $this->orderExtId, 'completed'),
            $I->wireMockAddBinaryFileMapping(
                method: 'POST',
                base64Content: base64_encode(file_get_contents(codecept_data_dir('signature_nl_control.jpg'))),
                headers: [
                    'Idempotency-Key' => ['matches' => '[A-Za-z0-9_-]{10,50}'],
                ],
                url: '/sap-netherlands/FileUpload?tourExternalId='.$this->tourExtId.'&orderExternalId='.$this->orderExtId.'&tasktype=deliveryNote_sig&elementReferenceType=signature',
            ),
        ];

        $I->callApiOrderBookingV2($this->orderIds[0], BookingFixtures::bookingEndRequestV2());
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function createTourAndOrder(ApiTester $I): array
    {
        $this->deviceId = 'random-nl-device-'.uniqid();
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-nl-tour-id-'.uniqid();
        $this->orderExtId = 'random-nl-order-id-'.uniqid();
        $tour1Name = 'random-nl-tour-name-'.uniqid();
        $this->equipmentExtId = 'random-nl-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-nl-license-plate-'.uniqid();
        $this->sapTourId = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
            $I->amUsingDeviceWithId($this->deviceId);
            $I->callApiUserStartSessionV2();
            $I->callApiTourGetV2($this->sapTourId);
        });

        return $I->grabDataFromResponseByJsonPath('$.orders')[0];
    }

    private function completeOrderTaskGroups(ApiTester $I, array $order): void
    {
        foreach ($order['taskgroups'] as $taskgroup) {
            foreach ($taskgroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    if ('signature' === $input['type']) {
                        $taskgroupId = $taskgroup['uuid'];
                        $taskId = $task['uuid'];
                        $inputId = $input['uuid'];

                        break 3;
                    }
                }
            }
        }

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $file1 = $fileUuid;

        $fileUuid = Uuid::v4()->toRfc4122();
        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
        $I->callApiFileUploadInDirectoryV2(
            directory: 'app-user-files',
            uuid: $fileUuid,
            fileContent: $fileContent,
        );

        $file2 = $fileUuid;

        $data = <<<JSON
        {

            "uuid": "{$taskgroupId}",
            "tasks": [
                {
                    "uuid": "{$taskId}",
                    "skipped": false,
                    "additional_service": false,
                    "repeat_service": false,
                    "repeat_service_template_uuid": "",
                    "inputs": [
                        {
                            "type": "input",
                            "uuid": "{$inputId}",
                            "value": [
                                {
                                    "file": "app-user-files/{$file1}",
                                    "name": "test1.png",
                                    "label": "test1"
                                },
                                {
                                    "file": "app-user-files/{$file2}",
                                    "name": "test2.png",
                                    "label": "test2"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        JSON;

        $I->callApiOrderTaskGroupPatchV2($order['uuid'], $data);
    }

    private function setOrderStatusWireMockMapping(
        ApiTester $I,
        string $orderExtId, string $status,
    ): string {
        return $I->wireMockAddSapNetherlandsStatusCallMapping(
            objectType: 'order',
            objectExternalId: $orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: [],
            status: $status,
        );
    }

    private function setStaffStatusWireMockMapping(
        ApiTester $I,
        string $staffExtId, string $status,
    ): string {
        return $I->wireMockAddSapNetherlandsStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'staff',
            subTypeExternalId: $this->staffExtId,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: [],
            status: $status,
        );
    }
}
