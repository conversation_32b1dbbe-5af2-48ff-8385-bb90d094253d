<?php

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapNetherlandsSecondTourCest
{
    private string $sapTour1Id = '';
    private string $tour1ExtId = '';

    private string $sapTour2Id = '';
    private string $tour2ExtId = '';
    private string $deviceId = '';
    private string $staffExtId = '';
    private string $equipmentExtId = '';
    private string $equipmentUuid = '';
    private string $equipmentLicensePlate = '';
    private string $tenant = 'pzn';

    public function canCreateTours(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();

        $this->deviceId = 'random-nl-device-'.uniqid();
        $this->equipmentExtId = 'random-nl-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-nl-license-plate-'.uniqid();

        $this->tour1ExtId = 'random-nl-tour-id-'.uniqid();
        $tourName = 'random-nl-tour-name-'.uniqid();
        $orderExtId = 'random-nl-order-id-'.uniqid();
        $this->sapTour1Id = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tour1ExtId,
            orderExtId: $orderExtId,
            tourName: $tourName,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $this->tour2ExtId = 'random-nl-tour-id-'.uniqid();
        $tourName = 'random-nl-tour-name-'.uniqid();
        $orderExtId = 'random-nl-order-id-'.uniqid();
        $this->sapTour2Id = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tour2ExtId,
            orderExtId: $orderExtId,
            tourName: $tourName,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
            $I->amUsingDeviceWithId($this->deviceId);
            $I->callApiUserStartSessionV2();
            $I->callApiTourGetV2($this->sapTour1Id);
            $I->callApiTourGetV2($this->sapTour2Id);
        });
    }

    #[Depends('canCreateTours')]
    public function canBookEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);
    }

    #[Depends('canBookEquipment')]
    public function canStartFirstTour(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'started',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: 'staff',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'started',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: 'equipment',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'started',
            ),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartFirstTour')]
    public function canEndFirstTour(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'completed',
            ),
            $I->wireMockAddSapNetherlandsConfirmationCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                orderLocations: [],
                termination: null,
                notes: null,
            ),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourBookingV2(
            $this->sapTour1Id,
            BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canEndFirstTour')]
    public function canStartSecondTour(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'started',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: 'staff',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'started',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: 'equipment',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'started',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: 'staff',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'completed',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour1ExtId,
                subType: 'equipment',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'completed',
            ),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourBookingV2(
            $this->sapTour2Id,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartSecondTour')]
    public function canEndSecondTour(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                status: 'completed',
            ),
            $I->wireMockAddSapNetherlandsConfirmationCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: null,
                subTypeExternalId: null,
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [$this->equipmentExtId],
                orderLocations: [],
                termination: null,
                notes: null,
            ),
        ];
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourBookingV2(
            $this->sapTour2Id,
            BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canEndSecondTour')]
    public function canLogoutEquipment(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: 'equipment',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [],
                status: 'completed',
            ),
            $I->wireMockAddSapNetherlandsConfirmationCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: 'equipment',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [],
                orderLocations: [],
                termination: null,
                notes: null,
            ),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canLogoutEquipment')]
    public function canLogoutStaff(ApiTester $I): void
    {
        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: 'staff',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [],
                equipmentExtIds: [],
                status: 'completed',
            ),
            $I->wireMockAddSapNetherlandsConfirmationCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tour2ExtId,
                subType: 'staff',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [],
                equipmentExtIds: [],
                orderLocations: [],
                termination: null,
                notes: null,
            ),
        ];

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiUserEndSessionV2();

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
