<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;
use Faker\Factory;

class SapNetherlandsTrackRecordingCest
{
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tour1Name = '';
    private string $deviceId = '';
    private string $staffExtId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $tenant = 'pzn';

    public function canSendSapNetherlandsTrackRecordings(ApiTester $I): void
    {
        $numberTrackLocations = 1;
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-nl-tour-id-'.uniqid();
        $this->orderExtId = 'random-nl-order-id-'.uniqid();
        $this->tour1Name = 'random-nl-tour-name-'.uniqid();
        $this->deviceId = 'random-nl-device-'.uniqid();
        $this->equipmentExtId = 'random-nl-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-nl-license-plate-'.uniqid();
        $this->sapTourId = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);
        $wireMockMappingId = $I->wireMockSetNetherlandsTrackRecordingMapping(
            tourExternalId: $this->tourExtId,
            equipmentExternalId: $this->equipmentExtId,
            numberTrackLocations: $numberTrackLocations,
        );

        $I->callApiTrackingV2(
            data: self::generateTrackingData($numberTrackLocations),
            equipmentExternalId: $this->equipmentExtId,
            tourExternalId: $this->tourExtId,
        );

        $I->amAuthenticatedAsPortalUser('portal-admin-nl', 'portal-admin-nl', 'portal-password');

        $I->waitUntil(
            function (ApiTester $I) {
                $I->callApiPortalTrackingList(
                    minDate: new \DateTimeImmutable()->format('Y-m-d'),
                    maxDate: new \DateTimeImmutable()->format('Y-m-d'),
                    search: $this->tourExtId
                );
                $I->assertEquals(1, $I->grabDataFromResponseByJsonPath('$.total_items')[0]);
            }
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);

        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingEndRequestV2(),
        );

        $I->wireMockVerifyRequestMapping([$wireMockMappingId]);
    }

    private static function generateTrackingData(int $numberTrackLocations): string
    {
        $faker = Factory::create();

        $locations = [];
        $startDateTime = \DateTimeImmutable::createFromInterface($faker->dateTimeThisMonth());

        for ($i = 0, $count = $numberTrackLocations; $i < $count; ++$i) {
            $startDateTime = $startDateTime->add(\DateInterval::createFromDateString(rand(1, 60).' minutes'));
            $locations[] = [
                'latitude' => $faker->latitude(),
                'longitude' => $faker->longitude(),
                'bearing' => rand(0, 360),
                'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
                'protected' => $faker->boolean(),
            ];
        }

        return json_encode([
            'locations' => $locations,
        ]);
    }
}
