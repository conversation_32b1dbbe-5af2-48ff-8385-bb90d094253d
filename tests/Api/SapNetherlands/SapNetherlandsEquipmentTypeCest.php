<?php

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;

class SapNetherlandsEquipmentTypeCest
{
    private string $staffExtId = '';
    private string $deviceId = '';

    public function canGetEquipmentTypes(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tourExtId = 'random-nl-tour-id-'.uniqid();
        $orderExtId = 'random-nl-order-id-'.uniqid();
        $tour1Name = 'random-nl-tour-name-'.uniqid();
        $this->deviceId = 'random-nl-device-'.uniqid();
        $equipmentExtId = 'random-nl-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-nl-license-plate-'.uniqid();
        $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, 'pzn');
            $I->amUsingDeviceWithId($this->deviceId);
            $I->callApiUserStartSessionV2();
        });

        $I->callApiEquipmentGetTypes();
        $equipmentTypes = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertEquals(
            2,
            count($equipmentTypes),
        );
        $I->assertContains('ask', array_column($equipmentTypes, 'key'));
        $I->assertContains('eq7800', array_column($equipmentTypes, 'key'));

        $I->callApiEquipmentGetTypeCategories();
        $equipmentTypeCategories = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertEquals(
            2,
            count($equipmentTypeCategories),
        );
        $I->assertContains('truck', array_column($equipmentTypeCategories, 'key'));
        $I->assertContains('trailer', array_column($equipmentTypeCategories, 'key'));
    }
}
