<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\InterruptionFixtures;
use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\Scenario\IngestData\DataFaker;
use Symfony\Component\Uid\Uuid;

class SapNetherlandsTourCest
{
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tour1Name = '';
    private string $deviceId = '';
    private string $staffExtId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';
    private string $tenant = 'pzn';

    public function canCreateDutchTourWithOrderDocument(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-nl-tour-id-'.uniqid();
        $this->orderExtId = 'random-nl-order-id-'.uniqid();
        $this->tour1Name = 'random-nl-tour-name-'.uniqid();
        $this->deviceId = 'random-nl-device-'.uniqid();
        $this->equipmentExtId = 'random-nl-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-nl-license-plate-'.uniqid();
        $this->sapTourId = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
            $I->amUsingDeviceWithId($this->deviceId);
            $I->callApiUserStartSessionV2();
            $I->callApiTourGetV2($this->sapTourId);
            $this->assertAdditionalInformationExistWithDescriptionFile($I);
        });
    }

    public function canNotStartDutchTourWithoutEquipment(ApiTester $I)
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        // try to start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('unprocessed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's not started
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);
    }

    public function canStartDutchTourWithForce(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiTourGetV2($this->sapTourId);
        $I->assertEquals('created', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });
    }

    public function canInterruptDutchTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiInterruptionTemplateGetV2();

        $interruption = $I->grabDataFromResponseByJsonPath('$.items[?(@.name=="tour-interruption-1-break")]')[0];
        $I->assertNotEmpty($interruption);

        $clientUuid = Uuid::v4()->toRfc4122();

        $wireMockMappingIds = [
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tourExtId,
                subType: 'interruption',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [],
                status: 'started',
            ),
            $I->wireMockAddSapNetherlandsStatusCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tourExtId,
                subType: 'interruption',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [],
                status: 'completed',
            ),
            $I->wireMockAddSapNetherlandsConfirmationCallMapping(
                objectType: 'tour',
                objectExternalId: $this->tourExtId,
                subType: 'interruption',
                subTypeExternalId: '${json-unit.any-string}',
                staffExtIds: [$this->staffExtId],
                equipmentExtIds: [],
                orderLocations: [],
                termination: null,
                notes: null,
            ),
            $I->wireMockAddBinaryFileMapping(
                method: 'POST',
                base64Content: base64_encode(file_get_contents(codecept_data_dir('signature.png'))),
                headers: [
                    'Idempotency-Key' => ['matches' => '[A-Za-z0-9_-]{10,50}'],
                ],
                urlPattern: '/sap-netherlands/FileUpload\?tourExternalId='.$this->tourExtId.'&interruptionId=.*&tasktype=interruption-type-1&elementReferenceType=asd1',
            ),
        ];

        $I->callApiTourInterruptionV2(
            $interruption['template_uuid'],
            InterruptionFixtures::bookingInterruptionStartRequestV2($clientUuid),
        );

        $I->callApiTourInterruptionUpdateTaskGroupV2(
            clientId: $clientUuid,
            data: $this->interruptionFirstTaskgroupPatch($I, $interruption),
        );

        $I->callApiTourInterruptionV2(
            $interruption['template_uuid'],
            InterruptionFixtures::bookingInterruptionEndRequestV2($clientUuid),
        );

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    public function canEndDutchTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('completed', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });
    }

    private function assertAdditionalInformationExistWithDescriptionFile(ApiTester $I): void
    {
        $ai = $I->grabDataFromResponseByJsonPath('$.orders[0].additional_informations')[0];
        $found = false;

        foreach ($ai as $additionalInformation) {
            if (empty($additionalInformation['description_file'])) {
                continue;
            }

            $found = true;
            $file = preg_replace('/^[^\/]+\/(.*)$/', '$1', $additionalInformation['description_file']);
            $I->callApiFileRetrieveV2($file);

            break;
        }

        $I->assertTrue($found, 'Additional information with description file not found');
    }

    private function interruptionFirstTaskgroupPatch(ApiTester $I, array $interruption): string
    {
        $faker = DataFaker::instance();

        $data = [
            'tasks' => [],
        ];

        foreach ($interruption['taskgroups'] as $taskGroup) {
            $data['template_uuid'] = $taskGroup['template_uuid'];

            foreach ($taskGroup['tasks'] as $task) {
                $inputs = [];

                foreach ($task['inputs'] as $input) {
                    if ('photo' === $input['type']) {
                        $fileUuid = Uuid::v4()->toRfc4122();
                        $fileContent = file_get_contents(codecept_data_dir('signature.png'));
                        $I->callApiFileUploadInDirectoryV2(
                            directory: 'app-user-files',
                            uuid: $fileUuid,
                            fileContent: $fileContent,
                        );

                        $inputs[] = [
                            'template_uuid' => $input['template_uuid'],
                            'value' => [
                                [
                                    'file' => "app-user-files/{$fileUuid}",
                                    'name' => 'interruption-signature.png',
                                    'label' => uniqid(),
                                ],
                            ],
                        ];
                    }
                }

                $data['tasks'][] = [
                    'template_uuid' => $task['template_uuid'],
                    'completed_at' => new \DateTimeImmutable()->format(\DateTimeInterface::RFC3339_EXTENDED),
                    'skipped' => false,
                    'inputs' => $inputs,
                    'latitude' => $faker->latitude(),
                    'longitude' => $faker->longitude(),
                    'mileage' => $faker->randomFloat(2, 10000, 9999999),
                ];
            }

            break;
        }

        return json_encode($data);
    }
}
