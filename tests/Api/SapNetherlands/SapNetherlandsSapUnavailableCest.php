<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Faker\Factory;

class SapNetherlandsSapUnavailableCest
{
    private string $sapTourId = '';
    private string $orderId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tourName = '';
    private string $deviceId = '';
    private string $equipmentExtId = '';
    private string $equipmentLicensePlate = '';

    private string $staffExtId = '';

    /**
     * @var string[]
     */
    private array $equipmentExtIds = [
    ];
    private string $equipmentUuid = '';
    private string $tenant = 'pzn';

    public function canStartTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-tour-id-'.uniqid();
        $this->orderExtId = 'random-order-id-'.uniqid();
        $this->tourName = 'random-tour-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->equipmentExtId = 'random-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-plate-'.uniqid();
        $this->equipmentExtIds = [$this->equipmentExtId];

        $wireMockMappingIds = $I->wireMockSetSapNetherlandsStatusTourStartMappings(
            tourExtId: $this->tourExtId,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            responseCode: 503,
        );

        $this->sapTourId = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tourName,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiTourGetV2($this->sapTourId);
        $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
        $I->assertEquals('created', $tourStatus[0]);

        // Tracks for additional tests
        $I->callApiTrackingV2(
            data: self::generateTrackingData(1),
            equipmentExternalId: $this->equipmentExtId,
            tourExternalId: $this->tourExtId,
        );

        // Book Eq
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getTruckForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetCollectionV2();
            $equipment = $this->getTruckForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);

            $I->assertEquals('equipped', $equipment['status']);
        });

        // Start the tour
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's started
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);
            $I->assertEquals('started', $I->grabDataFromResponseByJsonPath('$.status')[0]);
        });

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    #[Depends('canStartTour')]
    public function canStartOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->setCreatedOrder($I);
        $I->callApiOrderBookingV2(
            orderId: $this->orderId,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $this->validateOrderStatus($I, 'started');
    }

    #[Depends('canStartOrder')]
    public function canEndOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiOrderBookingV2(
            orderId: $this->orderId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $this->validateOrderStatus($I, 'completed');
    }

    #[Depends('canEndOrder')]
    public function canEndTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        // End the tour
        $I->callApiTourBookingV2(
            tourId: $this->sapTourId,
            data: BookingFixtures::bookingEndRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$.booking')[0]);

        // Validate it's ended
        $I->waitUntil(function (ApiTester $I) {
            $I->callApiTourGetV2($this->sapTourId);

            $tourStatus = $I->grabDataFromResponseByJsonPath('$.status');
            $I->assertEquals('completed', $tourStatus[0]);
        });
    }

    #[Depends('canEndTour')]
    public function canRetrySapCalls(ApiTester $I): void
    {
        $wireMockMappingIds = $I->wireMockSetSapNetherlandsStatusTourStartMappings(
            $this->tourExtId,
            [$this->staffExtId],
            $this->equipmentExtIds
        );
        $wireMockMappingIds[] = $I->wireMockAddSapNetherlandsStatusCallMapping(
            objectType: 'order',
            objectExternalId: $this->orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'started'
        );
        $wireMockMappingIds[] = $I->wireMockAddSapNetherlandsStatusCallMapping(
            objectType: 'order',
            objectExternalId: $this->orderExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'completed'
        );
        $wireMockMappingIds[] = $I->wireMockAddSapNetherlandsStatusCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            status: 'completed'
        );

        $wireMockMappingIds[] = $I->wireMockAddSapNetherlandsConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: null,
            subTypeExternalId: null,
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: $this->equipmentExtIds,
            orderLocations: [],
            termination: null,
            notes: null
        );
        $wireMockMappingIds[] = $I->wireMockSetNetherlandsTrackRecordingMapping(
            tourExternalId: $this->tourExtId,
            equipmentExternalId: $this->equipmentExtId,
        );

        $I->retrySapCalls($this->staffExtId, 'pzn');

        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function getTruckForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }

    private function setCreatedOrder(ApiTester $I): void
    {
        $I->callApiTourGetV2($this->sapTourId);

        $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

        $I->assertEquals('created', $firstOrderStatus);

        $this->orderId = $I->grabDataFromResponseByJsonPath('$.orders[0].uuid')[0];
    }

    private function validateOrderStatus(ApiTester $I, string $status): void
    {
        $I->waitUntil(
            function (ApiTester $I) use ($status): void {
                $I->callApiTourGetV2($this->sapTourId);
                $firstOrderStatus = $I->grabDataFromResponseByJsonPath('$.orders[0].status')[0];

                $I->assertEquals($status, $firstOrderStatus);
            },
        );
    }

    private static function generateTrackingData(int $numberTrackLocations): string
    {
        $faker = Factory::create();

        $locations = [];
        $startDateTime = \DateTimeImmutable::createFromInterface($faker->dateTimeThisMonth());

        for ($i = 0, $count = $numberTrackLocations; $i < $count; ++$i) {
            $startDateTime = $startDateTime->add(\DateInterval::createFromDateString(rand(1, 60).' minutes'));
            $locations[] = [
                'latitude' => $faker->latitude(),
                'longitude' => $faker->longitude(),
                'bearing' => rand(0, 360),
                'timestamp' => $startDateTime->format(\DateTimeInterface::RFC3339_EXTENDED),
                'protected' => $faker->boolean(),
            ];
        }

        return json_encode([
            'locations' => $locations,
        ]);
    }
}
