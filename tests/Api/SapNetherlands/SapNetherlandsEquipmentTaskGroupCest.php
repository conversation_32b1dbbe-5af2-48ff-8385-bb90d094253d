<?php

declare(strict_types=1);

namespace App\Tests\Api\SapNetherlands;

use App\Tests\Support\Api\BookingFixtures;
use App\Tests\Support\Api\SapNetherlandsTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class SapNetherlandsEquipmentTaskGroupCest
{
    private string $sapTourId = '';
    private string $tourExtId = '';
    private string $orderExtId = '';
    private string $tour1Name = '';
    private string $deviceId = '';
    private string $staffExtId = '';
    private string $equipmentExtId = '';
    private string $equipmentUuid = '';
    private string $equipmentLicensePlate = '';
    private string $tenant = 'pzn';

    public function canCreateTour(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $this->tourExtId = 'random-nl-tour-id-'.uniqid();
        $this->orderExtId = 'random-nl-order-id-'.uniqid();
        $this->tour1Name = 'random-nl-tour-name-'.uniqid();
        $this->deviceId = 'random-nl-device-'.uniqid();
        $this->equipmentExtId = 'random-nl-equipment-'.uniqid();
        $this->equipmentLicensePlate = 'random-nl-license-plate-'.uniqid();
        $this->sapTourId = $I->callSapNetherlandsTour(SapNetherlandsTourFixtures::tourRequestCreate(
            tourExtId: $this->tourExtId,
            orderExtId: $this->orderExtId,
            tourName: $this->tour1Name,
            equipmentExtId: $this->equipmentExtId,
            staffExtId: $this->staffExtId,
            equipmentLicensePlate: $this->equipmentLicensePlate,
        ));

        $I->waitUntil(function (ApiTester $I): void {
            $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
            $I->amUsingDeviceWithId($this->deviceId);
            $I->callApiUserStartSessionV2();
            $I->callApiTourGetV2($this->sapTourId);
        });
    }

    #[Depends('canCreateTour')]
    public function canBookEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiEquipmentGetCollectionV2();
        $equipment = $this->getEquipmentForTour($I->grabDataFromResponseByJsonPath('$.items')[0]);
        $I->assertEquals('unequipped', $equipment['status']);
        $this->equipmentUuid = $equipment['uuid'];
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);
    }

    #[Depends('canBookEquipment')]
    public function canStartTour(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiTourGetV2($this->sapTourId);
        $I->callApiTourBookingV2(
            $this->sapTourId,
            BookingFixtures::bookingStartRequestV2(force: false),
        );
    }

    #[Depends('canStartTour')]
    public function canSendEquipmentTaskGroupsOnce(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->sendEquipmentTaskgroup($I, 'alpha');
    }

    #[Depends('canSendEquipmentTaskGroupsOnce')]
    public function canBookoutEquipmentOnce(ApiTester $I): void
    {
        $wireMockMapId = $I->wireMockAddSapNetherlandsConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'equipment',
            subTypeExternalId: '${json-unit.any-string}',
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: [],
            orderLocations: [
                [
                    'orderLocationType' => 'VEHICLECHECK',
                    'tasks' => [
                        [
                            'taskType' => '${json-unit.any-string}',
                            'elements' => [
                                [
                                    'elementType' => 'string',
                                    'elementValues' => [
                                        'alpha',
                                    ],
                                    'referenceType' => 'eq-tg',
                                ],
                            ],
                            'taskName' => 'equipment.nl.task',
                            'taskExternalId' => 'NlEquTgTask1',
                            'latitude' => null,
                            'longitude' => null,
                            'timestamp' => null,
                        ],
                    ],
                ],
            ],
            termination: null,
            notes: null,
        );
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingEndRequestV2(),
        );

        $I->wireMockVerifyRequestMapping([$wireMockMapId]);
    }

    #[Depends('canBookoutEquipmentOnce')]
    public function canReUseEquipment(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingStartRequestV2(),
        );
        $I->assertEquals('processed', $I->grabDataFromResponseByJsonPath('$booking')[0]);
    }

    #[Depends('canReUseEquipment')]
    public function canSendEquipmentTaskGroupsTwice(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $this->sendEquipmentTaskgroup($I, 'bravo', true);
    }

    #[Depends('canSendEquipmentTaskGroupsTwice')]
    public function canBookoutEquipmentTwice(ApiTester $I): void
    {
        $wireMockMapId = $I->wireMockAddSapNetherlandsConfirmationCallMapping(
            objectType: 'tour',
            objectExternalId: $this->tourExtId,
            subType: 'equipment',
            subTypeExternalId: '${json-unit.any-string}',
            staffExtIds: [$this->staffExtId],
            equipmentExtIds: [],
            orderLocations: [
                [
                    'orderLocationType' => 'VEHICLECHECK',
                    'tasks' => [
                        [
                            'taskType' => '${json-unit.any-string}',
                            'elements' => [
                                [
                                    'elementType' => 'string',
                                    'elementValues' => [
                                        'bravo',
                                    ],
                                    'referenceType' => 'eq-tg',
                                ],
                            ],
                            'taskName' => 'equipment.nl.task',
                            'taskExternalId' => 'NlEquTgTask1',
                            'latitude' => null,
                            'longitude' => null,
                            'timestamp' => null,
                        ],
                    ],
                ],
            ],
            termination: null,
            notes: null,
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, $this->tenant);
        $I->callApiEquipmentBookingV2(
            equipmentId: $this->equipmentUuid,
            data: BookingFixtures::bookingEndRequestV2(),
        );

        $I->wireMockVerifyRequestMapping([$wireMockMapId]);
    }

    private function sendEquipmentTaskgroup(ApiTester $I, string $inputText, bool $debug = false): void
    {
        $I->callApiEquipmentGetItemV2($this->equipmentUuid);
        $taskGroupId = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].uuid')[0];
        $task1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].uuid')[0];
        $task1Input1Id = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].tasks[0].inputs[0].uuid')[0];

        $taskGroupRequest = [
            'uuid' => $taskGroupId,
            'tasks' => [
                [
                    'uuid' => $task1Id,
                    'skipped' => false,
                    'additional_service' => false,
                    'inputs' => [
                        [
                            'type' => 'input',
                            'uuid' => $task1Input1Id,
                            'value' => $inputText,
                        ],
                    ],
                ],
            ],
        ];
        $I->callApiEquipmentUpdateTaskGroupV2(
            equipmentId: $this->equipmentUuid,
            data: json_encode($taskGroupRequest) ?: throw new \RuntimeException('payload could not be encoded'),
        );

        $I->waitUntil(function (ApiTester $I): void {
            $I->callApiEquipmentGetItemV2($this->equipmentUuid);
            $taskGroupStatus = $I->grabDataFromResponseByJsonPath('$.taskgroups[0].status')[0];
            $I->assertEquals('completed', $taskGroupStatus);
        });
    }

    private function getEquipmentForTour(array $data): array
    {
        foreach ($data as $set) {
            if (isset($set['name']) && $this->equipmentLicensePlate == $set['name']) {
                return $set;
            }
        }

        return [];
    }
}
