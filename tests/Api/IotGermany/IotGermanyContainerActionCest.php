<?php

declare(strict_types=1);

namespace App\Tests\Api\IotGermany;

use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;

class IotGermanyContainerActionCest
{
    public function canSendToIotPortalV2(ApiTester $I): void
    {
        ['wireMockMappingIds' => $wireMockMappingIds, 'order' => $order] = $this->createTourAndOrder($I);
        $this->completeOrderTaskGroups($I, $order);
        $I->wireMockVerifyRequestMapping($wireMockMappingIds);
    }

    private function createTourAndOrder(ApiTester $I): array
    {
        $tourExtId = 'random-de-tour-id-'.uniqid();
        $orderExtId = 'random-de-order-id-'.uniqid();
        $tour1Name = 'random-de-tour-name-'.uniqid();
        $deviceId = 'random-de-device-'.uniqid();
        $equipmentExtId = 'random-de-equipment-'.uniqid();
        $staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $staffExt2Id = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $equipmentLicensePlate = 'random-de-license-plate-'.uniqid();
        $sapTourId = $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tourExtId,
            orderExtId: $orderExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $staffExtId,
            staffExternalId2: $staffExt2Id,
        ));

        $wireMockMappingId = $I->wireMocksetIotStatusCallMappings(
            url: '/api/v1/sensors',
            activeUser: 'pz'.$staffExtId,
            contractNumber: 'cn-76688339',
        );

        $I->amAuthenticatedAsGeneratedUserAndVerify($staffExtId, 'pz');
        $I->amUsingDeviceWithId($deviceId);
        $I->callApiUserStartSessionV2();

        $I->waitUntil(function (ApiTester $I) use ($sapTourId): void {
            $I->callApiTourGetV2($sapTourId);
        });

        $order = $I->grabDataFromResponseByJsonPath('$.orders[0]')[0];

        return [
            'wireMockMappingIds' => [$wireMockMappingId],
            'order' => $order,
        ];
    }

    private function completeOrderTaskGroups(ApiTester $I, $order): void
    {
        foreach ($order['taskgroups'] as $taskgroup) {
            foreach ($taskgroup['tasks'] as $task) {
                foreach ($task['inputs'] as $input) {
                    if ('qr' === $input['type'] && 'de-iot' === $input['label']) {
                        $taskgroupId = $taskgroup['uuid'];
                        $taskId = $task['uuid'];
                        $inputId = $input['uuid'];

                        break 3;
                    }
                }
            }
        }

        $data = <<<JSON
        {
            "uuid": "{$taskgroupId}",
            "tasks": [
                {
                    "uuid": "{$taskId}",
                    "skipped": false,
                    "additional_service": false,
                    "repeat_service": false,
                    "repeat_service_template_uuid": "",
                    "inputs": [
                        {
                            "type": "input",
                            "uuid": "{$inputId}",
                            "value": ["30"]
                        }
                    ]
                }
            ]
        }
        JSON;

        $I->callApiOrderTaskGroupPatchV2($order['uuid'], $data);
    }
}
