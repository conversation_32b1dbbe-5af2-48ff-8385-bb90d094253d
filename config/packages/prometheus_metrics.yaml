artprima_prometheus_metrics:
    namespace: hermes_backend
    storage:
        url: '%env(PROM_METRICS_DSN)%'
        options:
            prefix: '%kernel.environment%_prometheus_hermes_backend_'

    ignored_routes:
        - prometheus_bundle_prometheus
        - _wdt

    # used to disable default application metrics
    #disable_default_metrics: false

    # Recommended to disable default metrics from promphp/prometheus_client_php
    # see https://github.com/PromPHP/prometheus_client_php/issues/62
    disable_default_promphp_metrics: true

    # used to enable console metrics
    #enable_console_metrics: false

when@test:
    artprima_prometheus_metrics:
        storage: in_memory
