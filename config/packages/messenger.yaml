framework:
    messenger:
        default_bus: messenger.bus.default
        buses:
            messenger.bus.default:
                default_middleware: true
                middleware:
                    - Blackfire\Bridge\Symfony\MonitoredMiddleware
                    - App\Infrastructure\MessageQueue\Middleware\ContextLoaderMiddleware
                    - App\Infrastructure\MessageQueue\Middleware\ContextSetterMiddleware
                    - App\Infrastructure\MessageQueue\Middleware\DeviceShardingMiddleware
                    - validation
                    - doctrine_ping_connection
                    - App\Infrastructure\MessageQueue\Middleware\DoctrineTransactionMiddleware

        failure_transport: dead_letter

        transports:
            failed: 'doctrine://default?queue_name=failed'
            sync: 'sync://'

            # When messages fail to be processed from amqp, external_system or device transports, they are sent to the
            # dead_letter transport. This transport is used to wait for manual action on those messages.
            dead_letter:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Infrastructure\MessageQueue\ExternalSerializer
                retry_strategy:
                    max_retries: 100
                    delay: 0
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: fanout
                        name: dead_letter_exchange
                    queues:
                        dead_letter:
                            binding_keys:
                                - dead_letter
                            arguments:
                                x-queue-type: quorum
                                x-quorum-initial-group-size: '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%'

            # Special transport for messages that are sent to external systems and have to be retried a lot in case of
            # external system unavailability. Order of messages is not important, they can be processed in parallel.
            external_system:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Infrastructure\MessageQueue\ExternalSerializer
                failure_transport: dead_letter
                retry_strategy:
                    max_retries: 100
                    delay: 1000 # 1 second
                    multiplier: 2 # 1s 2s 4s 8s 16s 32s 64s 128s 256s 512s 1,024s 2,048s 3600s 3600s 3600s ...
                    max_delay: 3600000 # 1 hour
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: direct
                        name: external_system_exchange
                        default_publish_routing_key: external_system
                    queues:
                        external_system:
                            binding_keys:
                                - external_system
                            arguments:
                                x-queue-type: quorum
                                x-quorum-initial-group-size: '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%'

            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                failure_transport: failed
                retry_strategy:
                    max_retries: 3
                    delay: 1000
                    multiplier: 2
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: direct
                        name: async_exchange
                        default_publish_routing_key: async
                    queues:
                        async:
                            binding_keys:
                                - async
                            arguments:
                                x-queue-type: quorum
                                x-quorum-initial-group-size: '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%'

            amqp:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Infrastructure\MessageQueue\ExternalSerializer
                failure_transport: dead_letter
                retry_strategy:
                    max_retries: 3
                    delay: 1000
                    multiplier: 2
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: direct
                        name: amqp_exchange
                        default_publish_routing_key: messages
                    queues:
                        messages:
                            binding_keys:
                                - messages
                            arguments:
                                x-queue-type: quorum
                                x-quorum-initial-group-size: '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%'

            device:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Infrastructure\MessageQueue\ExternalSerializer
                failure_transport: dead_letter
                retry_strategy:
                    max_retries: 3
                    delay: 1000
                    multiplier: 2
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: direct
                        name: device_exchange
                    # queues: Defined in Kernel.php, to allow dynamic queue creation based on device sharding

        routing:
            Symfony\Component\Mailer\Messenger\SendEmailMessage: async
            Symfony\Component\Notifier\Message\ChatMessage: async
            Symfony\Component\Notifier\Message\SmsMessage: async

            App\Domain\MessageQueue\AsyncMessage: amqp
            App\Domain\MessageQueue\DeviceMessage: device
            App\Domain\MessageQueue\ExternalSystemMessage: external_system

services:
    messenger.failure.add_error_details_stamp_listener:
        class: App\Infrastructure\MessageQueue\AddErrorDetailsStampListener
    Blackfire\Bridge\Symfony\MonitoredMiddleware: ~
