doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URI)%'
        server_version: '16.2'
        logging: true

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '16'

        profiling_collect_backtrace: '%kernel.debug%'
        use_savepoints: true
        types:
            datetime_immutable: App\Infrastructure\Framework\Database\DateTimeTypes\UtcDateTimeImmutable
            datetime: App\Infrastructure\Framework\Database\DateTimeTypes\UtcDateTime

    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        report_fields_where_declared: true
        validate_xml_mapping: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        enable_native_lazy_objects: true
        mappings:
            App:
                type: attribute
                dir: '%kernel.project_dir%/src'
                prefix: 'App'
        filters:
            tenant:
                class: App\Domain\Services\Tenant\DoctrineTenantFilter
                enabled: true
        controller_resolver:
            auto_mapping: true
        dql:
            string_functions:
                JSON_ARRAY_LENGTH: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\JsonArrayLength
                JSON_ARRAY_ELEMENTS: App\Infrastructure\Framework\Database\Functions\JsonArrayElements
                CONTAINS: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\Contains
                ALL_ON_RIGHT_EXIST_ON_LEFT: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\AllOnTheRightExistOnTheLeft
                ANY_ON_RIGHT_EXISTS_ON_LEFT: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\AnyOnTheRightExistsOnTheLeft
                ARRAY: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\Arr
                ILIKE: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\Ilike
                CAST: MartinGeorgiev\Doctrine\ORM\Query\AST\Functions\Cast

services:
    database.odm_serializer:
        class: App\Infrastructure\Framework\Database\JsonOdm\JsonOdmSerializer
        public: true
        autowire: true

when@test:
    doctrine:
        dbal:
            # "TEST_TOKEN" is typically set by ParaTest
            dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod: &prod
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            metadata_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system

when@qa: *prod
when@e2e: *prod
