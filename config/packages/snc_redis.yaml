snc_redis:
    clients:
        default:
            type: phpredis
            alias: default
            dsn: '%env(REDIS_URL)%'
            logging: false
            options:
                connection_persistent: true

when@dev: &deployed
    snc_redis:
        clients:
            default:
                type: phpredis
                alias: default
                dsn: '%env(REDIS_URL)%'
                logging: false
                options:
                    cluster: true
                    connection_persistent: true

when@qa: *deployed
when@e2e: *deployed
when@prod: *deployed
