framework:
    workflows:
        tour:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\Tour
            initial_marking: [created]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\TourStatus::CREATED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStatus::TERMINATED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStatus::RETURNED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStatus::OBSOLETE->value
            transitions:
                start:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TourStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TourStatus::RETURNED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStatus::STARTED->value
                return:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStatus::RETURNED->value
                end:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStatus::COMPLETED->value
                terminate:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStatus::TERMINATED->value
                abandon:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TourStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TourStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStatus::OBSOLETE->value


        order:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\Order
            initial_marking: [created]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::CREATED->value
                - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::TERMINATED->value
                - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::OBSOLETE->value
            transitions:
                abandon:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::COMPLETED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::TERMINATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\OrderStatus::OBSOLETE->value
                start:
                    from: !php/enum App\Domain\Entity\Enum\Status\OrderStatus::CREATED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\OrderStatus::STARTED->value
                end:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\OrderStatus::COMPLETED->value
                terminate:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\OrderStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\OrderStatus::TERMINATED->value

        taskgroup:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\TaskGroup
            initial_marking: [created]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::CREATED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::OBSOLETE->value
            transitions:
                abandon:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::COMPLETED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::OBSOLETE->value
                start:
                    from: !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::CREATED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::STARTED->value
                end:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskGroupStatus::COMPLETED->value

        task:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\Task
            initial_marking: [created]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::SKIPPED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::OBSOLETE->value
            transitions:
                assign:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                unassign:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::SKIPPED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                abandon:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::SKIPPED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::OBSOLETE->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::COMPLETED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskStatus::OBSOLETE->value
                complete:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::SKIPPED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::OBSOLETE->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskStatus::COMPLETED->value
                skip:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::UNASSIGNED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::CREATED->value
                        - !php/enum App\Domain\Entity\Enum\Status\TaskStatus::OBSOLETE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TaskStatus::SKIPPED->value

        interruption:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\Interruption
            initial_marking: [created]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::CREATED->value
                - !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::COMPLETED->value
            transitions:
                start:
                    from: !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::CREATED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::STARTED->value
                complete:
                    from: !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\InterruptionStatus::COMPLETED->value

        equipment:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\Equipment
            initial_marking: [available]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value
                - !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::IN_USE->value
            transitions:
                start:
                    from: !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::IN_USE->value
                end:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::IN_USE->value
                        - !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value
                abort:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::IN_USE->value
                        - !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value
                    to: !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value
                activate:
                    from: !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::DRAFT->value
                    to: !php/enum App\Domain\Entity\Enum\Status\EquipmentStatus::AVAILABLE->value


        staff:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\Staff
            initial_marking: [idle]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::IDLE->value
                - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::WORKING->value
            transitions:
                start:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::IDLE->value
                        - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::WORKING->value
                    to: !php/enum App\Domain\Entity\Enum\Status\StaffStatus::WORKING->value
                end:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::IDLE->value
                        - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::WORKING->value
                    to: !php/enum App\Domain\Entity\Enum\Status\StaffStatus::IDLE->value
                abort:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::IDLE->value
                        - !php/enum App\Domain\Entity\Enum\Status\StaffStatus::WORKING->value
                    to: !php/enum App\Domain\Entity\Enum\Status\StaffStatus::IDLE->value

        tour_staff:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\TourStaff
            initial_marking: [dispatched]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::DISPATCHED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::ABORTED->value
            transitions:
                start:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::DISPATCHED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::STARTED->value
                end:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::COMPLETED->value
                abort:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourStaffStatus::ABORTED->value

        tour_equipment:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\TourEquipment
            initial_marking: [dispatched]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::DISPATCHED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::ABORTED->value
            transitions:
                start:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::DISPATCHED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::STARTED->value
                end:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::COMPLETED->value
                abort:
                    from: !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::STARTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\TourEquipmentStatus::ABORTED->value

        dakoProcess:
            type: 'state_machine'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'status'
            supports:
                - App\Domain\Entity\DakoProcess
            initial_marking: [ started ]
            places:
                - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::STARTED->value
                - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::IN_PROGRESS->value
                - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::COMPLETED->value
                - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::UPLOADED->value
                - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::TRANSFERED->value
            transitions:
                process:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::IN_PROGRESS->value
                    to: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::IN_PROGRESS->value
                complete:
                    from: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::IN_PROGRESS->value
                    to: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::COMPLETED->value
                upload:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::IN_PROGRESS->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::COMPLETED->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::UPLOADED->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::ABORTED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::UPLOADED->value
                transfer:
                    from: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::UPLOADED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::TRANSFERED->value
                abort:
                    from:
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::STARTED->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::IN_PROGRESS->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::COMPLETED->value
                        - !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::UPLOADED->value
                    to: !php/enum App\Domain\Entity\Enum\Status\DakoProcessStatus::ABORTED->value
