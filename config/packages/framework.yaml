# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    http_method_override: false
    handle_all_throwables: true
    set_locale_from_accept_language: true
    set_content_language_from_locale: true

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    session:
        enabled: false

    trusted_proxies: '**********/16,10.0.0.0/8,***********/16,**********/12,***********/16,0.0.0.0/8,240.0.0.0/4'
    trusted_headers: [ 'x-forwarded-for', 'x-forwarded-host', 'x-forwarded-proto', 'x-forwarded-port', 'x-forwarded-prefix' ]

    #esi: true
    #fragments: true

    php_errors:
        log: true

    http_client:
        scoped_clients:
            keycloak.client:
                base_uri: '%env(KEYCLOAK_URL)%'

            sap.germany.document.client:
                base_uri: '%env(SAP_GERMANY_DOCUMENT_URL)%'
                auth_basic: '%env(SAP_GERMANY_USERNAME)%:%env(SAP_GERMANY_PASSWORD)%'

            germany.vir.client:
                base_uri: '%env(GERMANY_VIR_URL)%'
                auth_basic: '%env(GERMANY_VIR_USERNAME)%:%env(GERMANY_VIR_PASSWORD)%'

            sap.netherlands.data.client:
                base_uri: '%env(SAP_NETHERLANDS_URL)%'
                auth_basic: '%env(SAP_NETHERLANDS_USERNAME)%:%env(SAP_NETHERLANDS_PASSWORD)%'

            netherlands.vir.client:
                base_uri: '%env(NETHERLANDS_VIR_URL)%'
                auth_basic: '%env(NETHERLANDS_VIR_USERNAME)%:%env(NETHERLANDS_VIR_PASSWORD)%'

            spain.vir.client:
                base_uri: '%env(SPAIN_VIR_URL)%'
                auth_basic: '%env(SPAIN_VIR_USERNAME)%:%env(SPAIN_VIR_PASSWORD)%'

            geotab.api.client:
                base_uri: '%env(GEOTAB_URL)%'

            dako.api.client:
                base_uri: '%env(DAKO_URL)%'
                auth_basic: '%env(DAKO_USERNAME)%:%env(DAKO_PASSWORD)%'

            centrifugo.client:
                base_uri: '%env(CENTRIFUGO_ADMIN_URL)%'
                headers:
                    X-API-Key: '%env(CENTRIFUGO_API_KEY)%'

            iot.client:
                base_uri: '%env(IOT_URL)%'
                auth_basic: '%env(IOT_USERNAME)%:%env(IOT_PASSWORD)%'

            user.management.client:
                base_uri: '%env(USER_MANAGEMENT_API_HOST)%'

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
