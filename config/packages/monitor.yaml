liip_monitor:
    # enabling the controller requires that `assets` are enabled in the framework bundle
    enable_controller: true
    checks:
        groups:
            default:
                php_extensions: ['apcu', 'Zend OPcache', 'amqp']
                php_version:
                    '8.4': '>='
                writable_directory: ['%kernel.cache_dir%']
                doctrine_dbal: [ default ]
                doctrine_migrations:
                    migrations_with_doctrine_bundle_v2: default
                disk_usage:
                    warning: 70
                    critical: 90
                    path: '%kernel.project_dir%'
            optional:
                security_advisory:
                    lock_file: '%kernel.project_dir%/composer.lock'
