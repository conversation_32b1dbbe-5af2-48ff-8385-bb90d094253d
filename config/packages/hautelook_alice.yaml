when@fixdev: &dev
    hautelook_alice:
        root_dirs:
            - '%kernel.project_dir%'
        fixtures_path: fixtures

    services:
        app.fixtures.reflection_property_accessor:
            class: Nelmio\Alice\PropertyAccess\ReflectionPropertyAccessor
            public: false
            decorates: nelmio_alice.property_accessor
            decoration_priority: -10
            arguments: [ '@app.fixtures.reflection_property_accessor.inner' ]

when@fixprod: *dev
when@fixinitemptyprod: *dev
when@fixglobal: *dev
