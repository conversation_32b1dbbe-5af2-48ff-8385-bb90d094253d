security:
    role_hierarchy:

    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\InMemoryUser: plaintext
        App\Domain\Entity\User: 'auto'

    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Domain\Entity\User
                property: username

        sap_germany_user_provider:
            memory:
                users:
                    '%env(SAP_GERMANY_INGEST_USERNAME)%': { password: '%env(SAP_GERMANY_INGEST_PASSWORD)%', roles: 'ROLE_INGEST' }

        germany_vir_user_provider:
            memory:
                users:
                    '%env(GERMANY_VIR_INGEST_USERNAME)%': { password: '%env(GERMANY_VIR_INGEST_PASSWORD)%', roles: 'ROLE_INGEST' }

        sap_netherlands_user_provider:
            memory:
                users:
                    '%env(SAP_NETHERLANDS_INGEST_USERNAME)%': { password: '%env(SAP_NETHERLANDS_INGEST_PASSWORD)%', roles: 'ROLE_INGEST' }

        netherlands_vir_user_provider:
            memory:
                users:
                    '%env(NETHERLANDS_VIR_INGEST_USERNAME)%': { password: '%env(NETHERLANDS_VIR_INGEST_PASSWORD)%', roles: 'ROLE_INGEST' }

        sap_spain_user_provider:
            memory:
                users:
                    '%env(SAP_SPAIN_INGEST_USERNAME)%': { password: '%env(SAP_SPAIN_INGEST_PASSWORD)%', roles: 'ROLE_INGEST' }

        spain_vir_user_provider:
            memory:
                users:
                    '%env(SPAIN_VIR_INGEST_USERNAME)%': { password: '%env(SPAIN_VIR_INGEST_PASSWORD)%', roles: 'ROLE_INGEST' }

        mobile_app_releases_provider:
            memory:
                users:
                    '%env(MOBILE_APP_RELEASES_USERNAME)%': { password: '%env(MOBILE_APP_RELEASES_PASSWORD)%', roles: 'ROLE_MOBILE_APP_RELEASES' }

        metrics_user_provider:
            memory:
                users:
                    metrics: { password: '$78Nine.', roles: 'ROLE_METRICS' }

        keycloak:
            id: App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUserProvider
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
            stateless: true

        metrics:
            pattern: ^/metrics
            security: true
            stateless: true
            provider: metrics_user_provider
            http_basic:
                realm: "Metrics"

        api_docs:
            pattern: ^/doc/
            security: false
            stateless: true

        health_checks:
            pattern: ^/monitor/health
            security: false
            stateless: true

        sap_germany:
            pattern: ^/ingest/sap-germany
            security: true
            stateless: true
            provider: sap_germany_user_provider
            http_basic:
                realm: "SAP Germany"

        germany_vir:
            pattern: ^/ingest/germany-vir
            security: true
            stateless: true
            provider: germany_vir_user_provider
            http_basic:
                realm: "Germany VIR"

        sap_netherlands:
            pattern: ^/ingest/sap-netherlands
            security: true
            stateless: true
            provider: sap_netherlands_user_provider
            http_basic:
                realm: "SAP Netherlands"

        netherlands_vir:
            pattern: ^/ingest/netherlands-vir
            security: true
            stateless: true
            provider: netherlands_vir_user_provider
            http_basic:
                realm: "Netherlands VIR"

        sap_spain:
            pattern: ^/ingest/sap-spain
            security: true
            stateless: true
            provider: sap_spain_user_provider
            http_basic:
                realm: "SAP Spain"

        spain_vir:
            pattern: ^/ingest/spain-vir
            security: true
            stateless: true
            provider: spain_vir_user_provider
            http_basic:
                realm: "Spain VIR"

        mobile_app_releases:
            pattern: ^/mobile-app-releases
            security: true
            stateless: true
            provider: mobile_app_releases_provider
            http_basic:
                realm: "Mobile App Releases"

        api:
            pattern: ^/(api|portal-api)
            security: true
            stateless: true
            provider: keycloak
            access_token:
                token_handler:
                    oidc:
                        claim: preferred_username
                        algorithms: ['ES256','RS256']
                        audience: 'hermes-backend'
                        discovery:
                            base_uri: '%env(KEYCLOAK_URL)%/realms/%env(KEYCLOAK_REALM)%/'
                            cache:
                                id: keycloak.cache
                        issuers:
                            - '%env(KEYCLOAK_ISSUER)%'
                            - 'http://localhost:11000/realms/%env(KEYCLOAK_REALM)%' # For local development, won't be an issue on prod

        main:
            lazy: true
            form_login:
                login_path: unlocalized_login
                check_path: app_login
            logout:
                path: app_logout
            provider: app_user_provider

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/api, roles: !php/enum App\Domain\Entity\Enum\UserRole::ROLE_DRIVER->value }
        - { path: ^/ingest, roles: ROLE_INGEST }
        - { path: ^/metrics, roles: ROLE_METRICS }
        - { path: ^/portal-api, roles: !php/enum App\Domain\Entity\Enum\UserRole::ROLE_PORTAL_ACCESS->value }
        - { path: ^/mobile-app-releases, roles: ROLE_MOBILE_APP_RELEASES }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
