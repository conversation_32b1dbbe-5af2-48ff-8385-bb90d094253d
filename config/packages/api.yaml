api:
    consider_nullable_properties_as_optional: true
    areas:
        portal:
            resource_path: 'src/Infrastructure/PortalApi/Resource'
            url_prefix: '/portal-api/v1'
            global_request_headers:
                Accept-Language:
                    type: 'string'
                    example: 'en'
                    description: 'The language of the response, default is en'
            open_api:
                info:
                    title: Portal API
                    description: Autogenerated documentation for Portal API
                    version: 1.0.0
                components:
                    securitySchemes:
                        Bearer:
                            type: http
                            scheme: bearer
                            bearerFormat: JWT
                security:
                    -   Bearer: [ ]
        hermes_app:
            resource_path: 'src/Infrastructure/HermesAppApi/Resource'
            url_prefix: '/api/v2'
            global_request_headers:
                x-device-timestamp:
                    format: 'date-time'
                    example: '2024-11-29T13:32:26.154+01:00'
                    description: 'Current device timestamp at the moment of sending the request'
                    required: true
                x-device-id:
                    type: 'string'
                    example: '9133ca32-3889-448b-ac2f-f03e41cec6c4'
                    description: 'The unique identifier of the device'
                    required: true
                Accept-Language:
                    type: 'string'
                    example: 'en'
                    description: 'The language of the response, default is en'
            open_api:
                info:
                    title: Hermes APP API
                    description: Autogenerated documentation for Hermes APP API
                    version: 2.0.0
                components:
                    securitySchemes:
                        Bearer:
                            type: http
                            scheme: bearer
                            bearerFormat: JWT
                security:
                    -   Bearer: [ ]
        sap_germany:
            resource_path: 'src/Infrastructure/SapGermany/Resource'
            url_prefix: '/ingest/sap-germany'
            open_api:
                info:
                    title: SAP Germany Ingest API
                    description: Autogenerated documentation for SAP Germany Ingest API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]

        germany_vir:
            resource_path: 'src/Infrastructure/VehicleInspectionReport/Germany'
            url_prefix: '/ingest/germany-vir'
            open_api:
                info:
                    title: Germany VIR Ingest API
                    description: Autogenerated documentation for Germany VIR Ingest API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]

        sap_netherlands:
            resource_path: 'src/Infrastructure/SapNetherlands/Resource'
            url_prefix: '/ingest/sap-netherlands'
            open_api:
                info:
                    title: SAP Netherlands Ingest API
                    description: Autogenerated documentation for SAP Netherlands Ingest API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]

        netherlands_vir:
            resource_path: 'src/Infrastructure/VehicleInspectionReport/Netherlands'
            url_prefix: '/ingest/netherlands-vir'
            open_api:
                info:
                    title: Netherlands VIR Ingest API
                    description: Autogenerated documentation for Netherlands VIR Ingest API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]

        sap_spain:
            resource_path: 'src/Infrastructure/SapSpain/Resource'
            url_prefix: '/ingest/sap-spain'
            open_api:
                info:
                    title: SAP Spain Ingest API
                    description: Autogenerated documentation for SAP Spain Ingest API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]

        spain_vir:
            resource_path: 'src/Infrastructure/VehicleInspectionReport/Spain'
            url_prefix: '/ingest/spain-vir'
            open_api:
                info:
                    title: Spain VIR Ingest API
                    description: Autogenerated documentation for Spain VIR Ingest API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]

        mobile_app_releases:
            resource_path: 'src/Infrastructure/MobileAppReleasesApi/Resource'
            url_prefix: '/mobile-app-releases'
            open_api:
                info:
                    title: Mobile APP Releases API
                    description: Autogenerated documentation for Mobile APP Releases API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]
