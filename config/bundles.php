<?php

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
    Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle::class => ['all' => true],
    Symfony\Bundle\DebugBundle\DebugBundle::class => ['localdev' => true],
    Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
    Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => ['localdev' => true, 'test' => true],
    Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => ['all' => true],
    Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    Symfony\Bundle\MakerBundle\MakerBundle::class => ['localdev' => true],
    Nelmio\Alice\Bridge\Symfony\NelmioAliceBundle::class => ['fixdev' => true, 'fixprod' => true, 'fixinitemptyprod' => true, 'fixglobal' => true],
    Fidry\AliceDataFixtures\Bridge\Symfony\FidryAliceDataFixturesBundle::class => ['fixdev' => true, 'fixprod' => true, 'fixinitemptyprod' => true, 'fixglobal' => true],
    Hautelook\AliceBundle\HautelookAliceBundle::class => ['fixdev' => true, 'fixprod' => true, 'fixinitemptyprod' => true, 'fixglobal' => true],
    Liip\MonitorBundle\LiipMonitorBundle::class => ['all' => true],
    Artprima\PrometheusMetricsBundle\ArtprimaPrometheusMetricsBundle::class => ['all' => true],
    Snc\RedisBundle\SncRedisBundle::class => ['all' => true],
    Nelmio\CorsBundle\NelmioCorsBundle::class => ['all' => true],
    PreZero\ApiBundle\ApiBundle::class => ['all' => true],
    Vuryss\DoctrineLazyJsonOdm\DoctrineLazyJsonOdmBundle::class => ['all' => true],
];
