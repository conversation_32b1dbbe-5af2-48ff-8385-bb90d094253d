###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

.devcontainer/data

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###
.DS_Store
.idea
###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

.cf-vars.*.local.yaml
.env.local

docker/local/db/data
!docker/local/db/data/.gitkeep

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

.clinerules
.devcontainer
.vscode
