<?php

declare(strict_types=1);

/**
 * This is project's console commands configuration for Robo task runner.
 *
 * @see https://robo.li/
 */
class RoboFile extends Robo\Tasks
{
    public function tests(
        Robo\Symfony\ConsoleIO $io,
        array $opts = [
            'no-reset-db' => false,
            'no-reset-wiremock' => false,
        ],
    ): void {
        $io->say('Generating Hermes APP API openapi definitions for the tests...');
        $this->taskExec('php bin/console prezero:api:dump-documentation --format hermes_app tests/Support/Data/openapi.json')
            ->env('STDOUT_LOG_LEVEL', 'alert')
            ->printOutput(true)
            ->run();

        $io->say('Generating Portal API openapi definitions for the tests...');
        $this->taskExec('php bin/console prezero:api:dump-documentation --format portal tests/Support/Data/portal-openapi.json')
            ->env('STDOUT_LOG_LEVEL', 'alert')
            ->printOutput(true)
            ->run();

        if (!$opts['no-reset-db']) {
            $io->say('Resetting the database...');
            $this->taskExec('php bin/console app:database:load-dump fixtures/dump.sql')
                ->env('STDOUT_LOG_LEVEL', 'alert')
                ->printOutput(true)
                ->run();
        }

        if (!$opts['no-reset-wiremock']) {
            $io->say('Resetting the wiremock...');
            $this->taskExec('php bin/console app:wiremock:reset')
                ->env('STDOUT_LOG_LEVEL', 'alert')
                ->printOutput(true)
                ->run();

            $this->taskExec('php bin/console app:dako:create-wiremock-mappings')
                ->env('STDOUT_LOG_LEVEL', 'alert')
                ->printOutput(true)
                ->run();
        }

        $io->say('Running the tests...');
        $disablePHPDeprecationErrorsConfig = 'error_reporting="E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED"';
        $this->taskParallelExec()
            ->process("php -d {$disablePHPDeprecationErrorsConfig} vendor/bin/codecept run Api -g api_1")
            ->process("php -d {$disablePHPDeprecationErrorsConfig} vendor/bin/codecept run Api -g api_2")
            ->process("php -d {$disablePHPDeprecationErrorsConfig} vendor/bin/codecept run Api -g api_3")
            ->process("php -d {$disablePHPDeprecationErrorsConfig} vendor/bin/codecept run Api -g api_4")
            ->process("php -d {$disablePHPDeprecationErrorsConfig} vendor/bin/codecept run Api -g api_5")
            ->printOutput(true)
            ->run();
    }

    public function testsSplitByTime(Robo\Symfony\ConsoleIO $io, array $opts = ['groups' => 5]): void
    {
        $io->say('Splitting the test by time. Please make sure you can run the complete test suite without parallel execution, so the times are accurate');

        $statFile = 'tests/_output/timeReport.json';
        $testExecutionTimeStats = json_decode(file_get_contents($statFile), true);

        // Group tests by time for the whole Cest, not by individual tests
        $testFileExecutionTimes = [];

        foreach ($testExecutionTimeStats as $testName => $time) {
            [$file, $method] = explode(':', $testName);
            $testFileExecutionTimes[$file] = $testFileExecutionTimes[$file] ?? 0;
            $testFileExecutionTimes[$file] += $time;
        }

        $groups = array_fill(0, $opts['groups'], []);
        $groupsTime = array_fill(0, $opts['groups'], 0);

        foreach ($testFileExecutionTimes as $file => $time) {
            $minTime = min($groupsTime);
            $minTimeIndex = array_search($minTime, $groupsTime);
            $groups[$minTimeIndex][] = $file;
            $groupsTime[$minTimeIndex] += $time;
        }

        foreach ($groups as $index => $tests) {
            $filename = 'tests/Codeception/api_group_'.($index + 1);
            $this->taskWriteToFile($filename)
                ->lines($tests)
                ->run();
        }
    }
}
