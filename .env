
# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=localdev
APP_SECRET=6386472e4c88b72d9f6ceb6a548bf7f7
###< symfony/framework-bundle ###

###> symfony/webapp-pack ###
MESSENGER_TRANSPORT_DSN=
MESSENGER_TRANSPORT_CACERT=
###< symfony/webapp-pack ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=8&charset=utf8mb4"
DATABASE_URI=
###< doctrine/doctrine-bundle ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
# MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/mailer ###
MAILER_DSN=smtp://mailcatcher:1025
###< symfony/mailer ###

APP_NAME=Hermes
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Hermes
EMAIL_REPLY_TO=<EMAIL>
EMAIL_MONOLOG=<EMAIL>
MAIL_BASE_LINK=http://localhost:8000
MAIL_NOREPLY=<EMAIL>
MAIL_ENABLED=1

# S3 compatible storage configuration
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_ENDPOINT_URL=
S3_BUCKET_NAME=

# SAP Germany
SAP_GERMANY_INGEST_USERNAME=to-be-replaced
SAP_GERMANY_INGEST_PASSWORD=to-be-replaced
SAP_GERMANY_URL=
SAP_GERMANY_DOCUMENT_URL='http://localhost'
SAP_GERMANY_USERNAME=to-be-replaced
SAP_GERMANY_PASSWORD=to-be-replaced
GERMANY_VIR_ENABLED=true
GERMANY_VIR_URL='http://localhost'
GERMANY_VIR_USERNAME=to-be-replaced
GERMANY_VIR_PASSWORD=to-be-replaced
GERMANY_VIR_INGEST_USERNAME=to-be-replaced
GERMANY_VIR_INGEST_PASSWORD=to-be-replaced

# IOT
IOT_URL='http://localhost'
IOT_USERNAME=to-be-replaced
IOT_PASSWORD=to-be-replaced
IOT_ENABLED=true

# SAP Netherlands
SAP_NETHERLANDS_INGEST_USERNAME=to-be-replaced
SAP_NETHERLANDS_INGEST_PASSWORD=to-be-replaced
SAP_NETHERLANDS_URL=
SAP_NETHERLANDS_USERNAME=to-be-replaced
SAP_NETHERLANDS_PASSWORD=to-be-replaced
NETHERLANDS_VIR_ENABLED=true
NETHERLANDS_VIR_URL='http://localhost'
NETHERLANDS_VIR_USERNAME=to-be-replaced
NETHERLANDS_VIR_PASSWORD=to-be-replaced
NETHERLANDS_VIR_INGEST_USERNAME=to-be-replaced
NETHERLANDS_VIR_INGEST_PASSWORD=to-be-replaced

# SAP Spain
SAP_SPAIN_INGEST_USERNAME=to-be-replaced
SAP_SPAIN_INGEST_PASSWORD=to-be-replaced
SAP_SPAIN_URL=
SAP_SPAIN_USERNAME=to-be-replaced
SAP_SPAIN_PASSWORD=to-be-replaced
SPAIN_VIR_ENABLED=true
SPAIN_VIR_URL='http://localhost'
SPAIN_VIR_USERNAME=to-be-replaced
SPAIN_VIR_PASSWORD=to-be-replaced
SPAIN_VIR_INGEST_USERNAME=to-be-replaced
SPAIN_VIR_INGEST_PASSWORD=to-be-replaced

# Staff Password
STAFF_PASSWORD_LENGTH=5
STAFF_PASSWORD_CHARS=abcdefghjkmnpqrstuvwxyz23456789
STAFF_PASSWORD_EMAIL_FALLBACK=
STAFF_PASSWORD_EMAIL_GERMANY=
STAFF_PASSWORD_EMAIL_NETHERLANDS=
STAFF_PASSWORD_EMAIL_LUXEMBOURG=
STAFF_PASSWORD_EMAIL_SPAIN=

# Keycloak
KEYCLOAK_URL='http://localhost'
KEYCLOAK_ISSUER=
KEYCLOAK_REALM='hermes'
KEYCLOAK_CLIENT_ID=to-be-replaced
KEYCLOAK_PORTAL_CLIENT_ID=to-be-replaced
KEYCLOAK_BACKEND_CLIENT_ID=hermes-backend
KEYCLOAK_BACKEND_CLIENT_SECRET=BojmiWo5Vgiu5Ql59X4LMwJbnKimdnTO

# Misc
WIRE_MOCK_ENDPOINT=http://wiremock:8089
ALLOW_FIXTURE_RESET_ENDPOINT=0
DEVICE_SESSION_TIME_LIMIT=50400
WORKFLOW_OBSOLETE_TOLERANCE_SECONDS=10
DEVICE_MESSAGES_NUMBER_OF_SHARDS=6
RABBITMQ_QUORUM_GROUP_SIZE=1
STDOUT_LOG_LEVEL=debug
HERE_TOKEN=
MASS_MESSAGING_LAST_ACTIVE_LIMIT_DAYS=7

# Geotab
GEOTAB_USERNAME=
GEOTAB_PASSWORD=
GEOTAB_DATABASE=
GEOTAB_URL='http://localhost'

# DAKO
DAKO_USERNAME=
DAKO_PASSWORD=
DAKO_URL='http://localhost'

# TACHOPLUS
TACHOPLUS_FTP_HOST=
TACHOPLUS_FTP_PORT=
TACHOPLUS_FTP_USERNAME=
TACHOPLUS_FTP_PASSWORD=
TACHOPLUS_FTP_PATH=

###> artprima/prometheus-metrics-bundle ###
PROM_METRICS_DSN=custom-redis
###< artprima/prometheus-metrics-bundle ###

###> snc/redis-bundle ###
# passwords that contain special characters (@, %, :, +) must be urlencoded
REDIS_URL=
###< snc/redis-bundle ###

# Notifications
CENTRIFUGO_URL=http://centrifugo:8000
CENTRIFUGO_ADMIN_URL=http://centrifugo:8000
CENTRIFUGO_API_KEY=d4d22184-41e9-4da5-96e2-9ffb87961746
CENTRIFUGO_HMAC_SECRET_KEY=747891fb-c4bd-4667-9e7b-1381b1a1735f

# App Versioning
APP_MINIMUM_VERSION=4.10.0
APP_RECOMMENDED_VERSION=4.11.0

# Mobile app releases
MOBILE_APP_RELEASES_USERNAME=to-be-replaced
MOBILE_APP_RELEASES_PASSWORD=to-be-replaced

# User management API
USER_MANAGEMENT_API_HOST=http://host.docker.internal:11002
