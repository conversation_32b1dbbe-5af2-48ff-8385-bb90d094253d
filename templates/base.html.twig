{% extends "@Crud/layout.html.twig" %}

{% block title %}PreZero{% endblock %}

{% block logo %}
    {% if app.user %}
        <a href="{{path('app_home')}}">
            <img src="{{asset('build/svg/prezero_logo.svg')}}" alt="">
            <div class="text-pzred" style="position: fixed;z-index: 99999;">{{ app.user.tenant.shortcut|upper }}</div>
        </a>
    {% else %}
        <a href="{{path('app_login')}}">
            <img src="{{asset('build/svg/prezero_logo.svg')}}" alt="">
        </a>
    {% endif %}
{% endblock %}

{% block leftnavigation %}
    <div class="flex space-x-5 justify-between items-center font-extralight">
        {% if is_granted('ROLE_ADMIN') %}
            <div class="relative inline-block text-left">
                <div class="admin-button">
                    <a href="{{ path('app_localized_home') }}" class="block px-4 py-2 rounded-b-md hover:bg-gray-50 ">
                        <i class="fas fa-map text-xl mr-2"></i>
                        <span class="hidden md:inline">{{'map'|trans}}</span>
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block rightnavigation %}
        <div class="flex space-x-5 justify-between items-center font-extralight">
            {% if is_granted('ROLE_ADMIN') %}
                <div class="relative inline-block text-left">
                    <div class="admin-button">
                        <a href="{{ path('documentfile-list') }}" class="block px-4 py-2 rounded-b-md hover:bg-gray-50 ">
                            <i class="fas fa-file text-xl mr-2"></i>
                            <span class="hidden md:inline">{{'document'|trans}}</span>
                        </a>
                    </div>
                </div>
                <div class="relative inline-block text-left">
                    <div class="admin-button">
                        <a href="{{ path('generalfile-list') }}" class="block px-4 py-2 rounded-b-md hover:bg-gray-50 ">
                            <i class="fas fa-photo-film text-xl mr-2"></i>
                            <span class="hidden md:inline">{{'photos/files'|trans}}</span>
                        </a>
                    </div>
                </div>
                <div class="relative inline-block text-left">
                    <div class="admin-button">
                        <a href="{{ path('jsonfile-list') }}" class="block px-4 py-2 rounded-b-md hover:bg-gray-50 ">
                            <i class="fas fa-file-code text-xl mr-2"></i>
                            <span class="hidden md:inline">{{'json'|trans}}</span>
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
{% endblock %}

{% block flashMessage %}
    {% if app.session.flashBag.has('error') %}
        <div class="p-4 m-6 text-sm bg-red-100 rounded-lg text-center" role="alert">
            {% for msg in app.session.flashBag.get('error') %}
                {{ msg }}
            {% endfor %}
        </div>
    {% endif %}
{% endblock %}

{% block usermanagement %}{% endblock %}