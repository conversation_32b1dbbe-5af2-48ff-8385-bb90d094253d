{% extends 'base.html.twig' %}

{% block title %}{{'application.title'|trans({}, 'application')}} - {{'login.title'|trans({}, 'admin')}}{% endblock %}
{% block desktoponly %}{% endblock %}
{% block breadcrumb %}{% endblock %}
{% block body %}
    <section class="p-4 flex flex-col justify-center items-center bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light" style="min-height: 700px; height: calc(100vh - 110px)">
        <h1 class="font-thin text-3xl text-center text-white mb-10">
            {{'login.title'|trans({}, 'admin')}}
        </h1>
        <form method="post" class="p-10 bg-white rounded-md font-extralight w-full sm:w-3/4 max-w-2xl">
            {% if error %}
                <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
            {% endif %}
            <h2 class="font-thin text-2xl text-center mb-6">
                {{'login.message'|trans({}, 'admin')}}
            </h2>
            <label for="username">{{'login.field.user'|trans({}, 'admin')}}</label>
            <input type="text" value="{{ last_username }}" name="_username" id="username" autocomplete="username" placeholder="{{'login.placeholder.user'|trans({}, 'admin')}}" required autofocus class="block h-12 rounded border-2 border-pzgrey w-full mb-6 placeholder-pzgrey px-4 py-[14px]">
            <label for="password">{{'login.field.password'|trans({}, 'admin')}}</label>
            <input type="password" name="_password" id="password" placeholder="{{'login.placeholder.password'|trans({}, 'admin')}}" autocomplete="current-password" required class="block h-12 rounded border-2 border-pzgrey w-full mb-2 placeholder-pzgrey px-4 py-[14px]">
            <input type="hidden" name="_csrf_token"
                   value="{{ csrf_token('authenticate') }}"
            >
            <input type="hidden" name="_target_path" value="{{ target_path }}"/>

            <a href="{{path('app_forgotpassword')}}" class="underline">{{'login.action.forgotpassword'|trans({}, 'admin')}}</a>

            <div class="pt-6 flex justify-between items-center mb-5">
                <div class="flex justify-between items-center">
                    <input type="checkbox" name="savepw" id="savepw" class="border-2 border-pzgrey rounded mr-2">
                    <p>{{'login.field.rememberpassword'|trans({}, 'admin')}}</p>
                </div>
                <button class="inline-block bg-pzblue rounded-md p-4 text-white font-normal text-xl hover:shadow-lg px-4 py-3" type="submit">
                    {{'login.action.login'|trans({}, 'admin')}}
                </button>
            </div>
            <div class="hidden border-t-2 pt-2">
                <h3 class="text-xl text-pzgreen">
                    {{'login.title.support'|trans({}, 'admin')}}
                </h3>
                <p class="text-pzgrey-dark text-base">
                    {{'company.support.mail'|trans({}, 'company')}} <br>
                    {{'company.support.phone'|trans({}, 'company')}}
                </p>
            </div>
        </form>
    </section>
{% endblock %}
{% block company %}{% endblock %}