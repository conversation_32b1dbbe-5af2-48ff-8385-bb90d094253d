{% extends('base.html.twig') %}
{% block title %}{{'application.title'|trans({}, 'application')}} - {{'newpassword.title'|trans({}, 'admin')}}{% endblock %}
{% block desktoponly %}{% endblock %}
{% block body %}
    <section class="p-4 flex flex-col justify-center items-center bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light" style="min-height: 700px; height: calc(100vh - 91px)">

        <h1 class="font-thin text-3xl text-center text-white mb-10">{{'newpassword.title'|trans({}, 'admin')}}</h1>

        <div class="p-10 bg-white rounded-md font-extralight w-full sm:w-3/4 lg:w-2/4 xl:w-2/4 2xl:w-1/4">

            <h2 class="font-thin text-2xl mb-6 text-center">{{'newpassword.header'|trans({}, 'admin')}}</h2>

            {{ form_start(form) }}

            {% if errorMessage is defined %}
                <div class="flex justify-center items-center m-1 font-medium py-1 px-2 bg-white rounded-md border border-pzblue mb-6">
                    <div class="text-xl font-normal max-w-full flex-initial text-pzblue">{{ errorMessage|trans({}, 'admin') }}</div>
                </div>
            {% endif %}

            <label for="newpassword">{{'newpassword.rulehint'|trans({}, 'admin')}}</label>

            {{ form_widget(form.password_1, { 'attr': { 'class':'block rounded border-2 border-pzgrey w-full mb-10 placeholder-pzgrey px-4 py-[14px]'} }) }}
            {{ form_errors(form.password_1, { 'attr': { 'class':'text-xl font-normal max-w-full flex-initial text-pzblue' } }) }}


            <label for="newpassword">{{'newpassword.repeat'|trans({}, 'admin')}}</label>

            {{ form_widget(form.password_2, { 'attr': { 'class':'block rounded border-2 border-pzgrey w-full mb-10 placeholder-pzgrey px-4 py-[14px]' } }) }}
            {{ form_errors(form.password_2, { 'attr': { 'class':'text-xl font-normal max-w-full flex-initial text-pzblue' } }) }}


            <div class="flex justify-center">
                {{ form_widget(form.submit, { 'attr': { 'class':'bg-pzblue rounded-md p-4 text-white font-normal text-xl hover:shadow-lg px-4 py-[14px]' } }) }}
            </div>

            {{ form_end(form) }}

        </div>

    </section>
{% endblock %}
