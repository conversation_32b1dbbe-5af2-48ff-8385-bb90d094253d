<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Notifications test</title>

        <script src="https://unpkg.com/centrifuge@5.2.2/dist/centrifuge.js"></script>
        <script type="text/javascript">
            const secure = new URLSearchParams(window.location.search).get('secure') || 'false';
            const serverUrl = new URLSearchParams(window.location.search).get('server-url') || 'localhost:8016';
            const token = new URLSearchParams(window.location.search).get('token') || '{{ token }}';
        </script>
    </head>

    <body>
        <h1>Notifications test</h1>

        <strong style="color: #900">Check development console for debug output</strong>

        <br /><br />

        <form method="get">
            <label for="secure">Secure:</label>
            <input id="secure" name="secure" type="checkbox" value="true" />

            <label for="server-url">Server:</label>
            <input id="server-url" name="server-url" type="text" />

            <label for="token">Token:</label>
            <input id="token" name="token" type="text" />
            <button type="submit">Connect</button>
        </form>

        <script type="text/javascript">
            const container = document.getElementById('counter');
            document.getElementById('server-url').value = serverUrl;
            document.getElementById('token').value = token;
            document.getElementById('secure').checked = secure === 'true';
            const centrifuge = new Centrifuge("ws"+(secure === 'true' ? 's' : '')+"://"+serverUrl+"/connection/websocket", {
                token: token
            });

            centrifuge
                .on('connecting', function (ctx) {
                    console.log(`connecting: ${ctx.code}, ${ctx.reason}`, ctx);
                })
                .on('connected', function (ctx) {
                    console.log(`connected over ${ctx.transport}`, ctx);
                })
                .on('disconnected', function (ctx) {
                    console.log(`disconnected: ${ctx.code}, ${ctx.reason}`, ctx);
                })
                .on('error', function (ctx) {
                    console.log(`error: ${ctx.code}, ${ctx.reason}`, ctx);
                })
                .on('subscribed', function (ctx) {
                    console.log(`subscribed: ${ctx.channel}`, ctx);
                })
                .on('unsubscribed', function (ctx) {
                    console.log(`unsubscribed: ${ctx.channel}`, ctx);
                })
                .on('subscribing', function (ctx) {
                    console.log(`subscribing: ${ctx.channel}`, ctx);
                })
                .on('publication', function (ctx) {
                    console.log(`publication: ${ctx.channel}`, ctx.data, ctx);
                })
                .connect();

            const sub = centrifuge.newSubscription('other-channel')
                .on('subscribed', function (ctx) {
                    console.log(`subscribed: ${ctx.channel}`, ctx);
                })
                .on('unsubscribed', function (ctx) {
                    console.log(`unsubscribed: ${ctx.channel}`, ctx);
                })
                .on('subscribing', function (ctx) {
                    console.log(`subscribing: ${ctx.channel}`, ctx);
                })
                .on('publication', function (ctx) {
                    console.log(`publication: ${ctx.channel}`, ctx.data, ctx);
                })
                .subscribe();

        </script>
    </body>
</html>
