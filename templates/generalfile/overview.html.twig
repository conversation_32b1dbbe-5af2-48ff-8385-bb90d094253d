{% extends 'base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('generalfile-overview') }}
{% endblock %}


{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('generalfile-overview') }}
{% endblock %}

{% block body %}
    {{ include('@Crud/pzbreadcrumb.html.twig', { breadcrumb: 'breadcrumb' }) }}

    <div class="container mx-auto px-4">
        <div class="px-5">
            <div id="filters-table-order-details"></div>
            <div class="blackgrid-responsive-wrapper">
                <table id="pallet-order-list" class="blackgrid blackgrid-generalfile theme-prezero"
                       data-translation='{{'blackgrid.translationfile'|trans({}, 'system')}}'
                       data-columns="{{ tableColumns|json_encode|escape('html_attr') }}"
                ></table>
            </div>
        </div>
    </div>
{% endblock %}