#!/usr/bin/env bash

# Stop execution on error
set -e

export STDOUT_LOG_LEVEL=alert

if [[ "$APP_ENV" != *"dev" ]]; then
    echo "fixture reload only allowed on *dev"
    exit;
fi

function set_wire_mock_mappings() {
    echo "Setting the wiremock-mappings"
    php bin/console app:wiremock:reset -vv
    php bin/console app:dako:create-wiremock-mappings -vv
}

function generate_checksum() {
    (find fixtures -type f ! -path "fixtures/fixprod/*" -exec md5sum {} +; find migrations -type f -exec md5sum {} +) | sort -k 2 | md5sum | awk '{ print $1 }'
}

if ! command -v pg_restore &> /dev/null
then
    echo "Postres cli could not be found. Loading the fixtures from files..."

    php bin/console doctrine:schema:drop --full-database --force
    php bin/console doctrine:migrations:migrate --no-interaction
    php bin/console hautelook:fixtures:load --env=fixdev --no-interaction --append
    php bin/console app:database:check-rulemaster -vv

    set_wire_mock_mappings

    exit 0
fi

# Get the checksum of the fixtures folder (excluding fixtures/fixprod) and migrations folder
CHECKSUM=$(generate_checksum)

# Stored checksum with default value is not exist
if [ ! -f fixtures-checksum.lock ]; then
    echo "0" > fixtures-checksum.lock
fi
STORED_CHECKSUM=$(cat fixtures-checksum.lock)

# If checksum is different, we need to reset the database
if [ "$CHECKSUM" != "$STORED_CHECKSUM" ]; then
    echo "Fixtures have changed, resetting the database"

    rm -f fixtures-checksum.lock

    php bin/console doctrine:schema:drop --full-database --force
    php bin/console doctrine:migrations:migrate --no-interaction
    php bin/console hautelook:fixtures:load --env=fixdev --no-interaction --append

    # Make sure, test-users are on keycloak and groups are set correctly
    php bin/console app:database:create-test-users -vv
    php bin/console app:database:users-migrate-missing-data -vv

    php bin/console app:database:dump /application/fixtures/dump.sql
    php bin/console app:database:check-rulemaster -vv

    # Store the new checksum (same logic as above)
    generate_checksum > fixtures-checksum.lock

    set_wire_mock_mappings

    exit 0
fi

# If checksum is the same, just load the SQL dump
echo "Fixtures have not changed, loading the SQL dump"
php bin/console app:database:load-dump fixtures/dump.sql

# make sure, users are on keycloak
php bin/console app:database:create-test-users -vv

set_wire_mock_mappings
