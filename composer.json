{"name": "prezero/hermes-sf", "description": "<PERSON><PERSON>", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": "^8.4", "ext-amqp": "*", "ext-bcmath": "*", "ext-calendar": "*", "ext-ctype": "*", "ext-gd": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_pgsql": "*", "ext-redis": "*", "ext-simplexml": "*", "ext-sockets": "*", "ext-xsl": "*", "ext-zip": "*", "artprima/prometheus-metrics-bundle": "^1.20", "aws/aws-sdk-php": "^3.354.0", "blackfire/php-sdk": "^2.5.8", "composer/semver": "^3.4.3", "doctrine/annotations": "^2.0.2", "doctrine/doctrine-bundle": "^2.15.1", "doctrine/doctrine-migrations-bundle": "^3.4.2, !=3.4.0", "doctrine/orm": "^3.5.2", "dragonmantank/cron-expression": "^3.4.0", "hautelook/alice-bundle": "^2.15.1", "intervention/image": "^3.11.4", "league/flysystem": "^3.30.0", "league/flysystem-sftp-v3": "^3.30", "liip/monitor-bundle": "^2.24.0", "martin-georgiev/postgresql-for-doctrine": "^3.4.1", "nelmio/cors-bundle": "^2.5", "phpdocumentor/reflection-docblock": "^5.6.2", "phpoffice/phpspreadsheet": "^5.0.0", "phpstan/phpdoc-parser": "^2.2.0", "prezero/api-bundle": "^3.0.0", "snc/redis-bundle": "^4.10.0", "symfony/amqp-messenger": "7.3.*", "symfony/asset": "7.3.*", "symfony/console": "7.3.*", "symfony/doctrine-messenger": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/expression-language": "7.3.*", "symfony/finder": "7.3.*", "symfony/flex": "^2.8.1", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/intl": "7.3.*", "symfony/lock": "7.3.*", "symfony/mailer": "7.3.*", "symfony/messenger": "7.3.*", "symfony/mime": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/notifier": "7.3.*", "symfony/password-hasher": "7.3.*", "symfony/process": "7.3.*", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/runtime": "7.3.*", "symfony/scheduler": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/serializer": "7.3.*", "symfony/string": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/uid": "7.3.*", "symfony/validator": "7.3.*", "symfony/var-exporter": "7.3.*", "symfony/workflow": "7.3.*", "symfony/yaml": "7.3.*", "tecnickcom/tcpdf": "^6.10.0", "twig/extra-bundle": "^3.21", "twig/twig": "^3.21.1", "vuryss/doctrine-lazy-json-odm": "dev-master", "vuryss/serializer": "^2.0.0", "web-token/jwt-library": "^4.0.6"}, "config": {"allow-plugins": {"symfony/flex": true, "symfony/runtime": true, "php-http/discovery": true, "phpstan/extension-installer": true, "captainhook/hook-installer": true, "tbachert/spi": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-mbstring": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php83": "*", "symfony/polyfill-php84": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}, "captainhook": {"force-install": true, "only-enabled": false}, "ramsey/conventional-commits": {"config": {"types": ["perf", "revert", "chore", "docs", "style", "refactor", "test", "build", "ci"]}}}, "require-dev": {"captainhook/captainhook": "^5.25.11", "captainhook/hook-installer": "^1.0.4", "codeception/codeception": "^5.3.2", "codeception/module-asserts": "^3.2", "codeception/module-phpbrowser": "^3.0.1", "codeception/module-rest": ">=3.4.1", "consolidation/robo": "^5.1", "friendsofphp/php-cs-fixer": "^3.86.0", "league/openapi-psr7-validator": "^0.22.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpat/phpat": "^0.11.9", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.22", "phpstan/phpstan-doctrine": "^2.0.4", "phpstan/phpstan-symfony": "^2.0.7", "ramsey/conventional-commits": "^1.6.0", "rector/rector": "^2.1.3", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/debug-bundle": "7.3.*", "symfony/maker-bundle": "^1.64.0", "symfony/phpunit-bridge": "7.3.*", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}, "repositories": [{"name": "prezero/api-bundle", "type": "vcs", "url": "https://github.com/prezero/api-bundle"}, {"name": "vuryss/doctrine-lazy-json-odm", "type": "vcs", "url": "https://github.com/vuryss/doctrine-lazy-json-odm"}]}