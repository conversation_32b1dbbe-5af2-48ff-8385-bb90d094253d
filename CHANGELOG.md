# Changelog

## [4.298.1](https://github.com/prezero/hermes-sf/compare/v4.298.0...v4.298.1) (2025-08-19)


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#3338](https://github.com/prezero/hermes-sf/issues/3338)) ([31ef0ff](https://github.com/prezero/hermes-sf/commit/31ef0ff9c2fcdbf16aac6d1d5568b400842fc24c))
* **HERMES-4030:** changing filter to ilike ([#3351](https://github.com/prezero/hermes-sf/issues/3351)) ([80108f3](https://github.com/prezero/hermes-sf/commit/80108f3ca531aadd497dcaa1a6b4664ea15ff3b9))
* Update libraries, fix styles & fix test users ([#3349](https://github.com/prezero/hermes-sf/issues/3349)) ([ddc7e61](https://github.com/prezero/hermes-sf/commit/ddc7e61c5e16a516ea882a2a88980ca10ecaf592))


### Miscellaneous Chores

* **deps:** update update github actions (major) ([#3339](https://github.com/prezero/hermes-sf/issues/3339)) ([037028f](https://github.com/prezero/hermes-sf/commit/037028fc78173f19cce323328624ed57312ea6b2))


### Tests

* **HEREMES-3593:** phonebook-tests ([#3350](https://github.com/prezero/hermes-sf/issues/3350)) ([37c9c2c](https://github.com/prezero/hermes-sf/commit/37c9c2c612285996b66598d98f8b0502bf216927))

## [4.298.0](https://github.com/prezero/hermes-sf/compare/v4.297.0...v4.298.0) (2025-08-15)


### Features

* **HERMES-4041:** changed accessible countries for portal-admins ([#3345](https://github.com/prezero/hermes-sf/issues/3345)) ([c885bf6](https://github.com/prezero/hermes-sf/commit/c885bf6908d7bcf2d8e11d2e24c0b6afbf1c9b14))


### Bug Fixes

* **HERMES-4040:** only persist on upcoming file-adding ([#3346](https://github.com/prezero/hermes-sf/issues/3346)) ([00442c9](https://github.com/prezero/hermes-sf/commit/00442c91f00a92dd26d432379da1091ebf1f4f77))

## [4.297.0](https://github.com/prezero/hermes-sf/compare/v4.296.0...v4.297.0) (2025-08-15)


### Features

* **HERMES-3593:** xliff parsing ([#3342](https://github.com/prezero/hermes-sf/issues/3342)) ([172508e](https://github.com/prezero/hermes-sf/commit/172508e06f5c7b1cdf08578de8168673c7583ed1))
* **HERMES-4030:** adding filter, corrected unique index ([#3344](https://github.com/prezero/hermes-sf/issues/3344)) ([03abe08](https://github.com/prezero/hermes-sf/commit/03abe08baf522715c418a8454ab44dd850c041ea))

## [4.296.0](https://github.com/prezero/hermes-sf/compare/v4.295.3...v4.296.0) (2025-08-14)


### Features

* **HERMES-4019:** added attribute to dto ([#3335](https://github.com/prezero/hermes-sf/issues/3335)) ([dff22ba](https://github.com/prezero/hermes-sf/commit/dff22baae6fac7fafee2ab2e4ead22095f628d94))


### Bug Fixes

* Hermes translations 135 ([#3334](https://github.com/prezero/hermes-sf/issues/3334)) ([8059ca6](https://github.com/prezero/hermes-sf/commit/8059ca6f879acbbbe34b2180b8a66a686825719b))


### Miscellaneous Chores

* **deps:** update update github actions ([#3336](https://github.com/prezero/hermes-sf/issues/3336)) ([5d61887](https://github.com/prezero/hermes-sf/commit/5d6188782c1e29af3cdae6ab456448ceeda1d578))
* **deps:** update update local docker-compose env dependencies ([#3337](https://github.com/prezero/hermes-sf/issues/3337)) ([73134e3](https://github.com/prezero/hermes-sf/commit/73134e33090b5dfd4fdd8a57e4c6391198203ec4))

## [4.295.3](https://github.com/prezero/hermes-sf/compare/v4.295.2...v4.295.3) (2025-08-13)


### Bug Fixes

* translations 135 ([#3332](https://github.com/prezero/hermes-sf/issues/3332)) ([f341a0f](https://github.com/prezero/hermes-sf/commit/f341a0f2c9d87e874e1c69d6f6c9b326de27841f))

## [4.295.2](https://github.com/prezero/hermes-sf/compare/v4.295.1...v4.295.2) (2025-08-13)


### Bug Fixes

* empty translation fix ([#3330](https://github.com/prezero/hermes-sf/issues/3330)) ([811eb99](https://github.com/prezero/hermes-sf/commit/811eb992a997e908f3246621a772eaa76014c134))

## [4.295.1](https://github.com/prezero/hermes-sf/compare/v4.295.0...v4.295.1) (2025-08-13)


### Bug Fixes

* fix in config ([#3328](https://github.com/prezero/hermes-sf/issues/3328)) ([438d2c8](https://github.com/prezero/hermes-sf/commit/438d2c854900aceb6f6c9bd775c0b8d9d2552d29))

## [4.295.0](https://github.com/prezero/hermes-sf/compare/v4.294.0...v4.295.0) (2025-08-13)


### Features

* new translations ([#3326](https://github.com/prezero/hermes-sf/issues/3326)) ([4d0adb7](https://github.com/prezero/hermes-sf/commit/4d0adb72a5464e7420c8c50c1eca29f3135262e4))

## [4.294.0](https://github.com/prezero/hermes-sf/compare/v4.293.0...v4.294.0) (2025-08-12)


### Features

* **HERMES-4003:** added filter ([#3324](https://github.com/prezero/hermes-sf/issues/3324)) ([7a0f231](https://github.com/prezero/hermes-sf/commit/7a0f2312f81b60cced3a44d23aff052f386ed7ef))

## [4.293.0](https://github.com/prezero/hermes-sf/compare/v4.292.1...v4.293.0) (2025-08-12)


### Features

* new config for lu-int ([#3321](https://github.com/prezero/hermes-sf/issues/3321)) ([113da31](https://github.com/prezero/hermes-sf/commit/113da311c44f5a0074d0bd0b9ca08e4d216ac68a))
* new translations ([#3323](https://github.com/prezero/hermes-sf/issues/3323)) ([668409f](https://github.com/prezero/hermes-sf/commit/668409fe99d07464518821fa045f84e52e6849c2))

## [4.292.1](https://github.com/prezero/hermes-sf/compare/v4.292.0...v4.292.1) (2025-08-12)


### Bug Fixes

* **HERMES-3986:** checking access after edit ([#3319](https://github.com/prezero/hermes-sf/issues/3319)) ([aa5a14c](https://github.com/prezero/hermes-sf/commit/aa5a14c7e506004c38849ed5172b0e58e15645e2))

## [4.292.0](https://github.com/prezero/hermes-sf/compare/v4.291.0...v4.292.0) (2025-08-11)


### Features

* **HERMES-3986:** Limiting assignable branch access to portal user's own country access ([#3316](https://github.com/prezero/hermes-sf/issues/3316)) ([06e6b59](https://github.com/prezero/hermes-sf/commit/06e6b59c401856eaee4d1c73477e59af2f679ab7))


### Bug Fixes

* new translations fixed ([#3318](https://github.com/prezero/hermes-sf/issues/3318)) ([7ac1f71](https://github.com/prezero/hermes-sf/commit/7ac1f71578794e46d260c2c2d607f09d1875f3fd))

## [4.291.0](https://github.com/prezero/hermes-sf/compare/v4.290.0...v4.291.0) (2025-08-08)


### Features

* **HERMES-3982:** Single country access for users ([#3313](https://github.com/prezero/hermes-sf/issues/3313)) ([710dc50](https://github.com/prezero/hermes-sf/commit/710dc50f981fb73dd95672d0be0f201a31ac546f))
* Update pz config and translations ([#3315](https://github.com/prezero/hermes-sf/issues/3315)) ([a6f0cb8](https://github.com/prezero/hermes-sf/commit/a6f0cb868ed3cffd03824fb5be798992ba431ea7))

## [4.290.0](https://github.com/prezero/hermes-sf/compare/v4.289.0...v4.290.0) (2025-08-07)


### Features

* **HERMES-3981:** Move to client based authentication instead of user for the backend ([#3312](https://github.com/prezero/hermes-sf/issues/3312)) ([9103d9c](https://github.com/prezero/hermes-sf/commit/9103d9cd508c7d0f6997652994331d97fb7381f6))
* Update translations to version 130 ([#3310](https://github.com/prezero/hermes-sf/issues/3310)) ([2687237](https://github.com/prezero/hermes-sf/commit/2687237431318d0ff0ad389a2b4df38a05ea3582))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.352.3 ([#3290](https://github.com/prezero/hermes-sf/issues/3290)) ([c249b3a](https://github.com/prezero/hermes-sf/commit/c249b3ad7b213a0233623b2e431ac54e5484807c))


### Miscellaneous Chores

* **deps:** update update local docker-compose env dependencies ([#3309](https://github.com/prezero/hermes-sf/issues/3309)) ([252756b](https://github.com/prezero/hermes-sf/commit/252756b6ae0c91f6f847d5510916032cb1b057d3))

## [4.289.0](https://github.com/prezero/hermes-sf/compare/v4.288.1...v4.289.0) (2025-08-06)


### Features

* **HERMES-3956:** API Bundle update with new serializer version ([#3305](https://github.com/prezero/hermes-sf/issues/3305)) ([edb00d9](https://github.com/prezero/hermes-sf/commit/edb00d9b0b4d503c313affe54ba3c200d7600ca6))
* Reactivate nl vir config ([#3306](https://github.com/prezero/hermes-sf/issues/3306)) ([471d313](https://github.com/prezero/hermes-sf/commit/471d313169234a33bf1be6b62ed84c4c48038054))
* Update pz config ([#3308](https://github.com/prezero/hermes-sf/issues/3308)) ([9f3add4](https://github.com/prezero/hermes-sf/commit/9f3add4751f53bceb0adca40f2599443dd132bc5))

## [4.288.1](https://github.com/prezero/hermes-sf/compare/v4.288.0...v4.288.1) (2025-08-05)


### Bug Fixes

* **HERMES-3952:** Fixing user retrieval from user management API ([#3303](https://github.com/prezero/hermes-sf/issues/3303)) ([9ffa63e](https://github.com/prezero/hermes-sf/commit/9ffa63e712c0d12efb617f05866cf56ef7a9bde1))

## [4.288.0](https://github.com/prezero/hermes-sf/compare/v4.287.0...v4.288.0) (2025-08-05)


### Features

* Update translations to version 129 ([#3302](https://github.com/prezero/hermes-sf/issues/3302)) ([2ded049](https://github.com/prezero/hermes-sf/commit/2ded049a93283d328492446eb50e94dbcb23a63e))


### Bug Fixes

* Encoding in dutch translations ([#3300](https://github.com/prezero/hermes-sf/issues/3300)) ([6c138bb](https://github.com/prezero/hermes-sf/commit/6c138bb487f2cbcc7022072061f82fcb6caa5288))

## [4.287.0](https://github.com/prezero/hermes-sf/compare/v4.286.0...v4.287.0) (2025-08-05)


### Features

* **HERMES-3922:** Updating project with api-bundle's changes ([#3296](https://github.com/prezero/hermes-sf/issues/3296)) ([8d47e34](https://github.com/prezero/hermes-sf/commit/8d47e3408e9a4f722122901cc099cff41deee216))
* **HERMES-3947:** Test user cleanup ([#3298](https://github.com/prezero/hermes-sf/issues/3298)) ([c3fd349](https://github.com/prezero/hermes-sf/commit/c3fd3497b1972e39c0355496baba1c6e2e7ab095))
* **HERMES-3951:** VIR Add Reported By field ([#3299](https://github.com/prezero/hermes-sf/issues/3299)) ([33fb7f3](https://github.com/prezero/hermes-sf/commit/33fb7f3481e1938d6fc62e0ea2d8e3dfbaedadd3))


### Bug Fixes

* **HERMES-3946:** Updating libraries & fixing domain dep on infra ([#3297](https://github.com/prezero/hermes-sf/issues/3297)) ([d1c2289](https://github.com/prezero/hermes-sf/commit/d1c2289436c64365e00bfde35d4d53c944cdbb1c))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.32.2 ([#3291](https://github.com/prezero/hermes-sf/issues/3291)) ([e3d17fe](https://github.com/prezero/hermes-sf/commit/e3d17febe35332a39ef73bc285e6b361af933061))
* **deps:** update update local docker-compose env dependencies ([#3289](https://github.com/prezero/hermes-sf/issues/3289)) ([7c4b6b0](https://github.com/prezero/hermes-sf/commit/7c4b6b0e055e13ffa2200afe9bb6f6231f5a2055))

## [4.286.0](https://github.com/prezero/hermes-sf/compare/v4.285.0...v4.286.0) (2025-08-01)


### Features

* Config and translation update ([#3293](https://github.com/prezero/hermes-sf/issues/3293)) ([452981c](https://github.com/prezero/hermes-sf/commit/452981c968a053658060e9b3e846650b770366b4))

## [4.285.0](https://github.com/prezero/hermes-sf/compare/v4.284.0...v4.285.0) (2025-07-31)


### Features

* **HERMES-3608:** xliff test ([#3187](https://github.com/prezero/hermes-sf/issues/3187)) ([bba07be](https://github.com/prezero/hermes-sf/commit/bba07be50cb9c0b55d425edcf59ce042c9e47186))
* **HERMES-3930:** Moving to valkey from Redis ([a576d0e](https://github.com/prezero/hermes-sf/commit/a576d0e7559e86672ebd0952a311563d6438acfe))
* **HERMES-3930:** Moving to valkey from Redis ([04f8830](https://github.com/prezero/hermes-sf/commit/04f8830564e5dcadf69bdca73c5a2fa0f3f4e879))
* **HERMES-3930:** Moving to valkey from Redis ([fc0a3fd](https://github.com/prezero/hermes-sf/commit/fc0a3fdccc71d81c442d7bb27a2e5e34d769a2a6))
* **HERMES-3930:** Moving to valkey from Redis ([7d319b8](https://github.com/prezero/hermes-sf/commit/7d319b864384950ebc9b7a8c002e21cd2e613351))
* **HERMES-3930:** Moving to valkey from Redis ([b7fca2f](https://github.com/prezero/hermes-sf/commit/b7fca2f926d7f332dcd032611c001459e64d4b74))
* **HERMES-3930:** Moving to valkey from Redis ([4588db1](https://github.com/prezero/hermes-sf/commit/4588db1954d03cd91ade95e35806aa64ecc1afcc))
* **HERMES-3930:** Moving to valkey from Redis ([451b293](https://github.com/prezero/hermes-sf/commit/451b293bb38a6c005f68957ff944468e07b1f3cc))
* **HERMES-3930:** Moving to valkey from Redis ([96b297b](https://github.com/prezero/hermes-sf/commit/96b297b09005f7ce50c2ec56ed622ec4eca1f5ae))

## [4.284.0](https://github.com/prezero/hermes-sf/compare/v4.283.0...v4.284.0) (2025-07-30)


### Features

* **HERMES-3924:** Adding new equipment type - spain rear loader ([#3287](https://github.com/prezero/hermes-sf/issues/3287)) ([8deeb1d](https://github.com/prezero/hermes-sf/commit/8deeb1dbbc643ef414164328e81e5350c8e08927))

## [4.283.0](https://github.com/prezero/hermes-sf/compare/v4.282.0...v4.283.0) (2025-07-30)


### Features

* **HERMES-3920:** Adjust NL VIR Endpoint ([#3285](https://github.com/prezero/hermes-sf/issues/3285)) ([dc289cc](https://github.com/prezero/hermes-sf/commit/dc289cceb5d45080ea1450ac9535bb2f7126eb81))

## [4.282.0](https://github.com/prezero/hermes-sf/compare/v4.281.0...v4.282.0) (2025-07-29)


### Features

* **HERMES-3916:** Updating driver in IAM if already exists, instead of failing ([#3282](https://github.com/prezero/hermes-sf/issues/3282)) ([1a49105](https://github.com/prezero/hermes-sf/commit/1a49105387f524ef893909688db458218020a3c2))
* **HERMES-3919:** Log request client IPs ([#3283](https://github.com/prezero/hermes-sf/issues/3283)) ([3cfc95f](https://github.com/prezero/hermes-sf/commit/3cfc95ffda727ba6806801b9bb7a399d940f0864))

## [4.281.0](https://github.com/prezero/hermes-sf/compare/v4.280.0...v4.281.0) (2025-07-28)


### Features

* **HERMES-3893:** changed optional fields ([#3274](https://github.com/prezero/hermes-sf/issues/3274)) ([411f6a5](https://github.com/prezero/hermes-sf/commit/411f6a5472c8b0dfc7b1c6d12eb7ddbdbf40cef9))
* **HERMES-3912:** Allow signed URLs download from portal by returning the URL instead of redirect ([#3281](https://github.com/prezero/hermes-sf/issues/3281)) ([5036125](https://github.com/prezero/hermes-sf/commit/50361256c9d0b4a4baeafe0b84278cec0971a40a))


### Tests

* **3593:** replaced some more tests ([#3279](https://github.com/prezero/hermes-sf/issues/3279)) ([b58f8cf](https://github.com/prezero/hermes-sf/commit/b58f8cf1f4067406ab2ab7ba003215e40e061a72))

## [4.280.0](https://github.com/prezero/hermes-sf/compare/v4.279.0...v4.280.0) (2025-07-25)


### Features

* **HERMES-3910:** Command for creating test users on pre-prod envs ([#3277](https://github.com/prezero/hermes-sf/issues/3277)) ([6007d3f](https://github.com/prezero/hermes-sf/commit/6007d3f9b325998c41531b419ed2e386f0d19086))

## [4.279.0](https://github.com/prezero/hermes-sf/compare/v4.278.0...v4.279.0) (2025-07-25)


### Features

* Update german config ([#3273](https://github.com/prezero/hermes-sf/issues/3273)) ([0320db1](https://github.com/prezero/hermes-sf/commit/0320db126ed179e1543efde950c27264d16fe270))


### Bug Fixes

* realm from env ([#3276](https://github.com/prezero/hermes-sf/issues/3276)) ([25bfc4a](https://github.com/prezero/hermes-sf/commit/25bfc4a8e3479bf3eeffc35be8c4b7751ee397d3))

## [4.278.0](https://github.com/prezero/hermes-sf/compare/v4.277.0...v4.278.0) (2025-07-24)


### Features

* **HERMES-3878:** Integration with IAM ([#3262](https://github.com/prezero/hermes-sf/issues/3262)) ([6629a84](https://github.com/prezero/hermes-sf/commit/6629a8425d8068f080fa7de69a089a3f109d2c8e))
* **HERMES-3885:** country specific handlers in pz-tenant ([#3263](https://github.com/prezero/hermes-sf/issues/3263)) ([7304b6d](https://github.com/prezero/hermes-sf/commit/7304b6d875c568cd1f23c7cd0792d361bd785a2a))
* **HERMES-3890:** Removing role usage except for checks of current user access, using groups to manage users ([#3265](https://github.com/prezero/hermes-sf/issues/3265)) ([fc2468e](https://github.com/prezero/hermes-sf/commit/fc2468eb83c8cacc6ab5a2c1950500cc56e15370))
* **HERMES-3894:** Password change endpoint ([#3269](https://github.com/prezero/hermes-sf/issues/3269)) ([97dfb35](https://github.com/prezero/hermes-sf/commit/97dfb355a86c0d0070814a678037d37db3340ec0))
* **HERMES-3902:** Passing request ID to IAM for tracability ([#3272](https://github.com/prezero/hermes-sf/issues/3272)) ([30e3b50](https://github.com/prezero/hermes-sf/commit/30e3b5040d385858398c8d73df428dc82c78eb30))
* **HERMES-3903:** Return all available equipment types ([#3271](https://github.com/prezero/hermes-sf/issues/3271)) ([c4ea73c](https://github.com/prezero/hermes-sf/commit/c4ea73cc48169afcb29c0df2e85df5ca79606065))


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#3268](https://github.com/prezero/hermes-sf/issues/3268)) ([dd9f0c7](https://github.com/prezero/hermes-sf/commit/dd9f0c7cfce7b9797f4fc39bc335698dc7587bd2))
* **HERMES-3900:** apk stream ([#3270](https://github.com/prezero/hermes-sf/issues/3270)) ([78246f7](https://github.com/prezero/hermes-sf/commit/78246f72644b7bfb9a7ecdde4186a1b35d241c54))


### Miscellaneous Chores

* **deps:** update update github actions ([#3266](https://github.com/prezero/hermes-sf/issues/3266)) ([abb6c60](https://github.com/prezero/hermes-sf/commit/abb6c60d458b415c5553bf80d32166347dcab9ca))
* **deps:** update update local docker-compose env dependencies ([#3267](https://github.com/prezero/hermes-sf/issues/3267)) ([af43810](https://github.com/prezero/hermes-sf/commit/af43810cdeb125f28a024e6717c910243b255777))

## [4.277.0](https://github.com/prezero/hermes-sf/compare/v4.276.0...v4.277.0) (2025-07-21)


### Features

* **HERMES-3866:** Dako Tenant Switch ([#3260](https://github.com/prezero/hermes-sf/issues/3260)) ([c457026](https://github.com/prezero/hermes-sf/commit/c457026449036f7eb1277c566e842ecf05790c74))


### Tests

* **HERMES-3859:** testing nl sap flow-logic ([#3259](https://github.com/prezero/hermes-sf/issues/3259)) ([60e3328](https://github.com/prezero/hermes-sf/commit/60e3328e349128b13372c2c3475c489c9ad8fcd9))

## [4.276.0](https://github.com/prezero/hermes-sf/compare/v4.275.0...v4.276.0) (2025-07-18)


### Features

* **HERMES-3851:** dako process expires info ([#3254](https://github.com/prezero/hermes-sf/issues/3254)) ([8e5a797](https://github.com/prezero/hermes-sf/commit/8e5a797942d7907b52bdd0de13cf8751a403b67e))
* Update translations to version 126 ([#3256](https://github.com/prezero/hermes-sf/issues/3256)) ([edd5d10](https://github.com/prezero/hermes-sf/commit/edd5d107facb0e43022086492d5c0823650bd2c6))


### Bug Fixes

* **HERMES-3855:** added cleanup after tenant-migration ([#3255](https://github.com/prezero/hermes-sf/issues/3255)) ([e60f441](https://github.com/prezero/hermes-sf/commit/e60f441eb579dc579716264b3726b54fa85d5db4))
* **HERMES-3858:** eq-tg-selection fixed ([#3258](https://github.com/prezero/hermes-sf/issues/3258)) ([3d492c6](https://github.com/prezero/hermes-sf/commit/3d492c697903a5ec0ff48cf1f31fae4e5e759571))

## [4.275.0](https://github.com/prezero/hermes-sf/compare/v4.274.1...v4.275.0) (2025-07-17)


### Features

* **HERMES-3837:** release ep change ([#3252](https://github.com/prezero/hermes-sf/issues/3252)) ([2d42a5d](https://github.com/prezero/hermes-sf/commit/2d42a5debc9487b095e8880ba3fe601e4a3ec2f6))

## [4.274.1](https://github.com/prezero/hermes-sf/compare/v4.274.0...v4.274.1) (2025-07-17)


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#3239](https://github.com/prezero/hermes-sf/issues/3239)) ([0252119](https://github.com/prezero/hermes-sf/commit/0252119ebd160426abe814dd57da607c031c6549))
* **HERMES-3836:** catching empty string ([#3248](https://github.com/prezero/hermes-sf/issues/3248)) ([253392d](https://github.com/prezero/hermes-sf/commit/253392d72b34879b5e13ff890f6e64f9a4175d20))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.28.0 ([#3250](https://github.com/prezero/hermes-sf/issues/3250)) ([8fb3a96](https://github.com/prezero/hermes-sf/commit/8fb3a96be5ee59e605330a10dbd8b5070cd5be01))
* **deps:** update update local docker-compose env dependencies ([#3249](https://github.com/prezero/hermes-sf/issues/3249)) ([86bbdea](https://github.com/prezero/hermes-sf/commit/86bbdea867a087506c82d26a6b6fa414da51a737))

## [4.274.0](https://github.com/prezero/hermes-sf/compare/v4.273.1...v4.274.0) (2025-07-16)


### Features

* **HERMES-3810:** portal order file view ([#3244](https://github.com/prezero/hermes-sf/issues/3244)) ([6c3f10c](https://github.com/prezero/hermes-sf/commit/6c3f10cc8915eebb7c37a0045e762869ed35445b))

## [4.273.1](https://github.com/prezero/hermes-sf/compare/v4.273.0...v4.273.1) (2025-07-15)


### Bug Fixes

* User migration command ([d5ad302](https://github.com/prezero/hermes-sf/commit/d5ad302a684251c43247919729f9bb15acc11d20))

## [4.273.0](https://github.com/prezero/hermes-sf/compare/v4.272.3...v4.273.0) (2025-07-15)


### Features

* **HERMES-3803:** Test connection to user management api via keycloak ([#3235](https://github.com/prezero/hermes-sf/issues/3235)) ([f7041e5](https://github.com/prezero/hermes-sf/commit/f7041e591e049fccf4bbf431d8985292278214f5))
* **HERMES-3817:** Enforcing email on users, processing JWT token groups ([#3243](https://github.com/prezero/hermes-sf/issues/3243)) ([479470c](https://github.com/prezero/hermes-sf/commit/479470cd69bfee70ad5d40ecab7e137038bfc50b))
* **HERMES-3824:** new endpoint for tours in portal ([#3245](https://github.com/prezero/hermes-sf/issues/3245)) ([0c47951](https://github.com/prezero/hermes-sf/commit/0c4795143afc1751c5ade03e11bc4bcc5f97f325))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.27.0 ([#3240](https://github.com/prezero/hermes-sf/issues/3240)) ([aa8ce63](https://github.com/prezero/hermes-sf/commit/aa8ce6321a484a3bb7f2496d4a7bf051f849fe6e))
* **deps:** update update local docker-compose env dependencies ([#3238](https://github.com/prezero/hermes-sf/issues/3238)) ([675d41b](https://github.com/prezero/hermes-sf/commit/675d41b60badc82fb032ec2af0263969c3048e35))
* **deps:** Updating packages ([b2490b8](https://github.com/prezero/hermes-sf/commit/b2490b81384a85d9f9bd00e5d3558ca773495d8d))


### Tests

* **HERMES-3593:** test reducement ([#3241](https://github.com/prezero/hermes-sf/issues/3241)) ([e096e3c](https://github.com/prezero/hermes-sf/commit/e096e3ca2e0366c86d4641ade9cb3ebb1c0631f3))

## [4.272.3](https://github.com/prezero/hermes-sf/compare/v4.272.2...v4.272.3) (2025-07-09)


### Bug Fixes

* removed qa-test-config ([#3236](https://github.com/prezero/hermes-sf/issues/3236)) ([1be18dc](https://github.com/prezero/hermes-sf/commit/1be18dcecea9cbecb65e4f936fbd127d324ad0c6))

## [4.272.2](https://github.com/prezero/hermes-sf/compare/v4.272.1...v4.272.2) (2025-07-09)


### Bug Fixes

* Element pattern escaping ([#3233](https://github.com/prezero/hermes-sf/issues/3233)) ([f734b82](https://github.com/prezero/hermes-sf/commit/f734b82f14ee20b5ad78d65d81796559ba7ff69a))

## [4.272.1](https://github.com/prezero/hermes-sf/compare/v4.272.0...v4.272.1) (2025-07-09)


### Bug Fixes

* feedback without screenshot ([#3231](https://github.com/prezero/hermes-sf/issues/3231)) ([8b26c26](https://github.com/prezero/hermes-sf/commit/8b26c261dd2b7c29b29c440ca52b27dbf72e0c04))

## [4.272.0](https://github.com/prezero/hermes-sf/compare/v4.271.0...v4.272.0) (2025-07-09)


### Features

* translation and config update ([#3230](https://github.com/prezero/hermes-sf/issues/3230)) ([b782cab](https://github.com/prezero/hermes-sf/commit/b782cab5179272d61052b6789cc75b0fd3125d80))


### Bug Fixes

* test for user-profile failed ([#3229](https://github.com/prezero/hermes-sf/issues/3229)) ([73707e6](https://github.com/prezero/hermes-sf/commit/73707e6cc7885b3e1d842ce8caac2002760dc826))


### Tests

* **HERMES-3593:** Test refactoring ([#3228](https://github.com/prezero/hermes-sf/issues/3228)) ([8203593](https://github.com/prezero/hermes-sf/commit/820359383ce399c8bf4e613f40cf9ddf1dc3ee13))
* **HERMES-3593:** Testscenario refactoring ([#3226](https://github.com/prezero/hermes-sf/issues/3226)) ([e90b650](https://github.com/prezero/hermes-sf/commit/e90b650b1648e810707b8816db9df62ccebfa29b))

## [4.271.0](https://github.com/prezero/hermes-sf/compare/v4.270.0...v4.271.0) (2025-07-08)


### Features

* **HERMES-3782:** Adjusting config for keycloak, removing obsolete functionality ([#3221](https://github.com/prezero/hermes-sf/issues/3221)) ([9391f4f](https://github.com/prezero/hermes-sf/commit/9391f4ffec2b8c2d3c0dfe4dfddba414cdc30663))


### Bug Fixes

* **HERMES-3773:** sorted by type ([#3224](https://github.com/prezero/hermes-sf/issues/3224)) ([a121196](https://github.com/prezero/hermes-sf/commit/a12119675e5111ccb2200aa43999ec4f6b08bbf9))
* **HERMES-3784:** fixed index-error ([#3223](https://github.com/prezero/hermes-sf/issues/3223)) ([0ced42f](https://github.com/prezero/hermes-sf/commit/0ced42fdd78a2e18792072b508cd295f531be5ae))

## [4.270.0](https://github.com/prezero/hermes-sf/compare/v4.269.0...v4.270.0) (2025-07-07)


### Features

* **HERMES-3772:** Extract keycloak docker local setup ([#3218](https://github.com/prezero/hermes-sf/issues/3218)) ([ee2b595](https://github.com/prezero/hermes-sf/commit/ee2b59540d89c699a535c8246f4ad8da3fa78793))
* **HERMES-3780:** added date-filter ([#3222](https://github.com/prezero/hermes-sf/issues/3222)) ([dde0995](https://github.com/prezero/hermes-sf/commit/dde0995c9aee073ed0ed4d92955caba40e955c0d))
* Update translations to version 122 ([#3219](https://github.com/prezero/hermes-sf/issues/3219)) ([9b4db01](https://github.com/prezero/hermes-sf/commit/9b4db01f1e2a57ba1ac3f6d3e8df2a1bac771c2e))

## [4.269.0](https://github.com/prezero/hermes-sf/compare/v4.268.0...v4.269.0) (2025-07-04)


### Features

* config updates ([#3216](https://github.com/prezero/hermes-sf/issues/3216)) ([1eb4197](https://github.com/prezero/hermes-sf/commit/1eb41972ca7d0333d43f5a1a29c3da898c1e64a8))

## [4.268.0](https://github.com/prezero/hermes-sf/compare/v4.267.0...v4.268.0) (2025-07-04)


### Features

* **HERMES-3763:** removed ep for nav-token and old language-ep ([#3213](https://github.com/prezero/hermes-sf/issues/3213)) ([2165dd5](https://github.com/prezero/hermes-sf/commit/2165dd52c79a79336f948da11adfd365fbe3ffb3))


### Bug Fixes

* removed whitespace ([#3214](https://github.com/prezero/hermes-sf/issues/3214)) ([e55d4bc](https://github.com/prezero/hermes-sf/commit/e55d4bc46576bd43a0589d3935112695c51dad28))

## [4.267.0](https://github.com/prezero/hermes-sf/compare/v4.266.0...v4.267.0) (2025-07-03)


### Features

* Update pz config ([#3209](https://github.com/prezero/hermes-sf/issues/3209)) ([fc72188](https://github.com/prezero/hermes-sf/commit/fc7218829bfd7dd53026647c06f847ab035e4d11))
* Update translations to version 121 ([#3212](https://github.com/prezero/hermes-sf/issues/3212)) ([5633b19](https://github.com/prezero/hermes-sf/commit/5633b196bd7d51cba4c444881cf0133bfdee218e))


### Bug Fixes

* Adding validations, fixing feedback files, fixing update event ([#3211](https://github.com/prezero/hermes-sf/issues/3211)) ([bc200ea](https://github.com/prezero/hermes-sf/commit/bc200ea54dfbfd1a7d12b6c52073cb7289919179))

## [4.266.0](https://github.com/prezero/hermes-sf/compare/v4.265.0...v4.266.0) (2025-07-03)


### Features

* **HERMES-3746:** Reduce tracking data on ingest ([#3205](https://github.com/prezero/hermes-sf/issues/3205)) ([3e4b9eb](https://github.com/prezero/hermes-sf/commit/3e4b9eb5215642ed68a2e11867f7626d2e2f31a4))
* **HERMES-3750:** update of search-field in threads, extended tests ([#3204](https://github.com/prezero/hermes-sf/issues/3204)) ([e81b87c](https://github.com/prezero/hermes-sf/commit/e81b87cd786a382c96edecd284690f7e2ccb5b55))
* **HERMES-3762:** phonebook change ([#3207](https://github.com/prezero/hermes-sf/issues/3207)) ([84362e7](https://github.com/prezero/hermes-sf/commit/84362e793faa549f2418a6fcf75659d26460d73c))
* Update nl config ([#3206](https://github.com/prezero/hermes-sf/issues/3206)) ([70b2626](https://github.com/prezero/hermes-sf/commit/70b2626b8822ffa0c88246fea4625476b2fb6048))


### Bug Fixes

* Adjust tour list endpoint ([#3197](https://github.com/prezero/hermes-sf/issues/3197)) ([4e5535d](https://github.com/prezero/hermes-sf/commit/4e5535d7785cc4e086f213d97890c442e1947410))
* **deps:** update update composer packages to non-major versions ([#3203](https://github.com/prezero/hermes-sf/issues/3203)) ([203dd88](https://github.com/prezero/hermes-sf/commit/203dd88990649a2502bc38e2d27b11dbc5b8fcd4))
* Fix fixture name conflict ([#3199](https://github.com/prezero/hermes-sf/issues/3199)) ([3356b3c](https://github.com/prezero/hermes-sf/commit/3356b3c66ef52a3cd4236070bc8ef93eb855913b))
* **HERMES-3762:** phonebook change ([#3208](https://github.com/prezero/hermes-sf/issues/3208)) ([e37d716](https://github.com/prezero/hermes-sf/commit/e37d71617a5dab2383dcf744604fff6b125d2a10))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.3.0 ([#3201](https://github.com/prezero/hermes-sf/issues/3201)) ([e2e6402](https://github.com/prezero/hermes-sf/commit/e2e6402a59f595d30ecd59feaadd85dcd6d5d783))
* **deps:** update prezero/workflows action to v1.26.0 ([#3202](https://github.com/prezero/hermes-sf/issues/3202)) ([fe7f68a](https://github.com/prezero/hermes-sf/commit/fe7f68a92c7a2a4b56905534ee7ea8693b07ba91))
* **deps:** update update local docker-compose env dependencies ([#3200](https://github.com/prezero/hermes-sf/issues/3200)) ([778e151](https://github.com/prezero/hermes-sf/commit/778e1519c7bbce3bf545ec754da6731228ad5811))

## [4.265.0](https://github.com/prezero/hermes-sf/compare/v4.264.0...v4.265.0) (2025-07-02)


### Features

* **HERMES-3735:** Interruption file upload ([#3193](https://github.com/prezero/hermes-sf/issues/3193)) ([c5d567e](https://github.com/prezero/hermes-sf/commit/c5d567eca9d503c046d285e06672251da84e38ec))
* Update translations to version 120 ([#3194](https://github.com/prezero/hermes-sf/issues/3194)) ([3788af1](https://github.com/prezero/hermes-sf/commit/3788af164f16aebdaff65f1ea01cc4d9c5eda198))


### Bug Fixes

* Adjust tour selector list endpoint to kebab-case ([#3196](https://github.com/prezero/hermes-sf/issues/3196)) ([35cb98a](https://github.com/prezero/hermes-sf/commit/35cb98a6272a3804e318d8c7d5325323696ce99e))

## [4.264.0](https://github.com/prezero/hermes-sf/compare/v4.263.0...v4.264.0) (2025-07-02)


### Features

* **HERMES-3731:** Add endpoint for portal tour selector ([#3188](https://github.com/prezero/hermes-sf/issues/3188)) ([fe7c0e1](https://github.com/prezero/hermes-sf/commit/fe7c0e125fee0374e6244f8f47abca2daf7faed5))

## [4.263.0](https://github.com/prezero/hermes-sf/compare/v4.262.0...v4.263.0) (2025-07-02)


### Features

* Update translations to version 119 ([#3191](https://github.com/prezero/hermes-sf/issues/3191)) ([24a9318](https://github.com/prezero/hermes-sf/commit/24a9318364c9b1e15126961944364c116ca77267))


### Continuous Integration

* fix argocd command ([d69ac34](https://github.com/prezero/hermes-sf/commit/d69ac345b71086f39ff4bf678a9cd06d7ede3a99))
* improve dev argocd sync ([#3186](https://github.com/prezero/hermes-sf/issues/3186)) ([14f73d5](https://github.com/prezero/hermes-sf/commit/14f73d5bbb1d719cd1d7521e22e3cf83d405eb62))
* separate restart actions ([#3190](https://github.com/prezero/hermes-sf/issues/3190)) ([5b39532](https://github.com/prezero/hermes-sf/commit/5b3953232e002619636ab64ca7d9caff8e890c6d))

## [4.262.0](https://github.com/prezero/hermes-sf/compare/v4.261.1...v4.262.0) (2025-07-01)


### Features

* Add new equipment types for trailers ([#3184](https://github.com/prezero/hermes-sf/issues/3184)) ([216aa9b](https://github.com/prezero/hermes-sf/commit/216aa9b76b8e8f841cabf30416f359bddf179aa3))
* **HERMES-3695:** eps for spain and nl ([#3181](https://github.com/prezero/hermes-sf/issues/3181)) ([e8df655](https://github.com/prezero/hermes-sf/commit/e8df655c93daa57c73664367fdecda56e9a008f2))
* **HERMES-3699:** ep for setting debug-level on device ([#3183](https://github.com/prezero/hermes-sf/issues/3183)) ([6af81b7](https://github.com/prezero/hermes-sf/commit/6af81b77431e423df6a8bae6c71c3d5ecf97e94b))
* **HERMES-3710:** Portal phonebook endpoint adjustments ([#3171](https://github.com/prezero/hermes-sf/issues/3171)) ([1b3056c](https://github.com/prezero/hermes-sf/commit/1b3056cbc5c9222a72b308bb4f3cf7d4ddf98db3))


### Bug Fixes

* EquipmentType lower case ([32b97e2](https://github.com/prezero/hermes-sf/commit/32b97e2a64d6e66b1795a3268f604c21a167d61c))
* **HERMES-3721:** Fix file dates appearing wrong on portal ([#3185](https://github.com/prezero/hermes-sf/issues/3185)) ([88013c2](https://github.com/prezero/hermes-sf/commit/88013c27ff5993c706ac1b0f7c08ad6d8a49982c))
* **HERMES-3730:** dako upload error catch ([#3180](https://github.com/prezero/hermes-sf/issues/3180)) ([3d5d486](https://github.com/prezero/hermes-sf/commit/3d5d486c2c6b385fe16d750d68e76fd75b02f898))
* More verbose error log ([8952d43](https://github.com/prezero/hermes-sf/commit/8952d438a2827e9dde9364eaa4337b53a2e11c53))

## [4.261.1](https://github.com/prezero/hermes-sf/compare/v4.261.0...v4.261.1) (2025-06-30)


### Bug Fixes

* **HERMES-3732:** Send vir device message from equipment ([#3178](https://github.com/prezero/hermes-sf/issues/3178)) ([bd78857](https://github.com/prezero/hermes-sf/commit/bd788579c8f8437c972846d8dd37d579128b0c1c))


### Tests

* fixed deleted fixtures ([#3177](https://github.com/prezero/hermes-sf/issues/3177)) ([4942a4a](https://github.com/prezero/hermes-sf/commit/4942a4aeb5602b0ebe3b0f5d04991ebd237deb8e))

## [4.261.0](https://github.com/prezero/hermes-sf/compare/v4.260.0...v4.261.0) (2025-06-30)


### Features

* Update nl config ([#3175](https://github.com/prezero/hermes-sf/issues/3175)) ([16d91d0](https://github.com/prezero/hermes-sf/commit/16d91d04c9166e0356c8fd810ff7816f0b4a8c12))

## [4.260.0](https://github.com/prezero/hermes-sf/compare/v4.259.0...v4.260.0) (2025-06-30)


### Features

* **HERMES-3580:** Inspection report message ([#3167](https://github.com/prezero/hermes-sf/issues/3167)) ([99f36f3](https://github.com/prezero/hermes-sf/commit/99f36f30db06470a92e623d81546be989d852cd7))
* **HERMES-3695:** first version for germany ([#3153](https://github.com/prezero/hermes-sf/issues/3153)) ([2ac2ca3](https://github.com/prezero/hermes-sf/commit/2ac2ca3cbda8f58506d9fbd4dd6e1678b04d8c77))
* Update translations to version 118 ([#3173](https://github.com/prezero/hermes-sf/issues/3173)) ([feb7e2c](https://github.com/prezero/hermes-sf/commit/feb7e2c5f34ef04176fd1a093006f8742c9202af))


### Miscellaneous Chores

* **deps:** update holomekc/wiremock-gui docker tag to v3.13.5 ([#3164](https://github.com/prezero/hermes-sf/issues/3164)) ([87c6576](https://github.com/prezero/hermes-sf/commit/87c6576d290593b55cbda858e17078519f9184fe))
* **deps:** update prezero/workflows action to v1.25.2 ([#3165](https://github.com/prezero/hermes-sf/issues/3165)) ([ce502b4](https://github.com/prezero/hermes-sf/commit/ce502b4ef0eefd4a273b0b943d1967fdacd7ed70))


### Tests

* **HERMES-3593:** extended scenarion, removed obsolete cest ([#3174](https://github.com/prezero/hermes-sf/issues/3174)) ([7cac2a4](https://github.com/prezero/hermes-sf/commit/7cac2a413feaa9961ae7f62c95047ad70f8c43f4))
* **HERMES-3722:** Adding app logs to happy path scenario, small refactoring ([#3169](https://github.com/prezero/hermes-sf/issues/3169)) ([7a3ebe5](https://github.com/prezero/hermes-sf/commit/7a3ebe5d7122ab85df1079ea547a1b8e64474849))

## [4.259.0](https://github.com/prezero/hermes-sf/compare/v4.258.0...v4.259.0) (2025-06-27)


### Features

* **HERMES-3708:** Portal message count notification ([#3159](https://github.com/prezero/hermes-sf/issues/3159)) ([3f33f4a](https://github.com/prezero/hermes-sf/commit/3f33f4a55f62ef2a1e9c6d912f9a4c1fc070b8e8))
* Update translations to version 117 ([#3163](https://github.com/prezero/hermes-sf/issues/3163)) ([64f1333](https://github.com/prezero/hermes-sf/commit/64f1333a2e17a93f7653698bcc521e7dc5ce7068))


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#3166](https://github.com/prezero/hermes-sf/issues/3166)) ([9dffead](https://github.com/prezero/hermes-sf/commit/9dffead9c921158511a2f1eb68a7eda51a7ae4f6))
* **HERMES-3721:** Fix file dates appearing wrong on portal ([#3168](https://github.com/prezero/hermes-sf/issues/3168)) ([04a6946](https://github.com/prezero/hermes-sf/commit/04a69465a7eb255f7f3100797ce039d30a5c4e2d))


### Miscellaneous Chores

* **deps:** Updating renovate config ([5125190](https://github.com/prezero/hermes-sf/commit/51251902c4152a3597f5848817834bbcf166b1dd))

## [4.258.0](https://github.com/prezero/hermes-sf/compare/v4.257.0...v4.258.0) (2025-06-26)


### Features

* Update translations to version 116 ([#3161](https://github.com/prezero/hermes-sf/issues/3161)) ([b9b071c](https://github.com/prezero/hermes-sf/commit/b9b071cb3ef9129ea04010c01cc39915b3d86279))


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#3132](https://github.com/prezero/hermes-sf/issues/3132)) ([b3ad655](https://github.com/prezero/hermes-sf/commit/b3ad655304bc8ae9bb34ab3a29c64e4e65c3c7f8))


### Miscellaneous Chores

* **deps:** update update github actions ([#3157](https://github.com/prezero/hermes-sf/issues/3157)) ([c321367](https://github.com/prezero/hermes-sf/commit/c32136767f081be345adfaacc627cbaf53d01718))
* **deps:** update update local docker-compose env dependencies ([#3158](https://github.com/prezero/hermes-sf/issues/3158)) ([f24692a](https://github.com/prezero/hermes-sf/commit/f24692a251695da8eeceb0b2220a23c9e105cd9c))

## [4.257.0](https://github.com/prezero/hermes-sf/compare/v4.256.0...v4.257.0) (2025-06-26)


### Features

* **HERMES-3709:** adding note-files to upload-events ([#3154](https://github.com/prezero/hermes-sf/issues/3154)) ([75d1d41](https://github.com/prezero/hermes-sf/commit/75d1d41bc5fe5b9fda9cd880364847bf75bbe2e9))
* **HERMES-3711:** Truncate vehicle inspection config table on prod c… ([#3155](https://github.com/prezero/hermes-sf/issues/3155)) ([831606e](https://github.com/prezero/hermes-sf/commit/831606e23c06620083004052356bf4c7ea2dfdc4))

## [4.256.0](https://github.com/prezero/hermes-sf/compare/v4.255.0...v4.256.0) (2025-06-24)


### Features

* **HERMES-3694:** renamed attributes, added key ([#3144](https://github.com/prezero/hermes-sf/issues/3144)) ([58e9931](https://github.com/prezero/hermes-sf/commit/58e9931ec3d0d9d2448894873a02f6a73d07a809))
* **HERMES-3696:** no defects for spanish equipments ([#3150](https://github.com/prezero/hermes-sf/issues/3150)) ([d5adf80](https://github.com/prezero/hermes-sf/commit/d5adf803ca6b552806d7b0fb66a4356a199a1600))


### Bug Fixes

* **HERMES-3698:** fixed config ([#3151](https://github.com/prezero/hermes-sf/issues/3151)) ([a8e4403](https://github.com/prezero/hermes-sf/commit/a8e44039ebb4d0c0a21a1fb731e8f30e413194ab))

## [4.255.0](https://github.com/prezero/hermes-sf/compare/v4.254.1...v4.255.0) (2025-06-24)


### Features

* **HERMES-3697:** eq-type optional in VIR ([#3148](https://github.com/prezero/hermes-sf/issues/3148)) ([c12617f](https://github.com/prezero/hermes-sf/commit/c12617fd625cee5fe26a6e5a231fb426ad6e3a57))

## [4.254.1](https://github.com/prezero/hermes-sf/compare/v4.254.0...v4.254.1) (2025-06-24)


### Bug Fixes

* filename ([#3146](https://github.com/prezero/hermes-sf/issues/3146)) ([42010c7](https://github.com/prezero/hermes-sf/commit/42010c72e8ee45527ea35ba375a04b101f38775c))

## [4.254.0](https://github.com/prezero/hermes-sf/compare/v4.253.1...v4.254.0) (2025-06-24)


### Features

* **HERMES-3685:** here again in endpoints ([#3142](https://github.com/prezero/hermes-sf/issues/3142)) ([37d7a59](https://github.com/prezero/hermes-sf/commit/37d7a5919025e7a2ec0716eb42cf9d3468def3ee))
* Update nl config ([#3145](https://github.com/prezero/hermes-sf/issues/3145)) ([49c8c9d](https://github.com/prezero/hermes-sf/commit/49c8c9d31f0bc486a4fae67a15305cfc78e359a8))

## [4.253.1](https://github.com/prezero/hermes-sf/compare/v4.253.0...v4.253.1) (2025-06-23)


### Bug Fixes

* **HERMES-3665:** exception translation ([#3140](https://github.com/prezero/hermes-sf/issues/3140)) ([a0dbdbf](https://github.com/prezero/hermes-sf/commit/a0dbdbf661536f766c6b0b614a279016a5eaa3f3))


### Tests

* **HERMES-3539:** test reduce ([#3139](https://github.com/prezero/hermes-sf/issues/3139)) ([fab375c](https://github.com/prezero/hermes-sf/commit/fab375c2cc69e122ba922f2fad101f7d5df808be))

## [4.253.0](https://github.com/prezero/hermes-sf/compare/v4.252.0...v4.253.0) (2025-06-23)


### Features

* changed config ([#3138](https://github.com/prezero/hermes-sf/issues/3138)) ([2d100b1](https://github.com/prezero/hermes-sf/commit/2d100b15986196c39f9eeac75e4c0a8678047205))
* **HERMES-3676:** Dynamic queues sharding ([#3136](https://github.com/prezero/hermes-sf/issues/3136)) ([1f677f8](https://github.com/prezero/hermes-sf/commit/1f677f8ce81c4953d1601871fd50083c57334ffe))

## [4.252.0](https://github.com/prezero/hermes-sf/compare/v4.251.0...v4.252.0) (2025-06-20)


### Features

* new config nl ([#3135](https://github.com/prezero/hermes-sf/issues/3135)) ([16dd1ef](https://github.com/prezero/hermes-sf/commit/16dd1ef26db05cc70bfdee7e93bda733d4f52d6f))


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#3130](https://github.com/prezero/hermes-sf/issues/3130)) ([6d4b66c](https://github.com/prezero/hermes-sf/commit/6d4b66c5166d4d2e3538a95b922f279964ac5639))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.24.2 ([#3129](https://github.com/prezero/hermes-sf/issues/3129)) ([155a7f6](https://github.com/prezero/hermes-sf/commit/155a7f6f350e575732325e1c19a425813a38e5b7))
* **deps:** update update local docker-compose env dependencies ([#3128](https://github.com/prezero/hermes-sf/issues/3128)) ([210a492](https://github.com/prezero/hermes-sf/commit/210a492da8d754df779883ba328ffca50de45755))

## [4.251.0](https://github.com/prezero/hermes-sf/compare/v4.250.2...v4.251.0) (2025-06-18)


### Features

* **HERMES-3657:** Adding idempotency key for NL, improve existing ones & testing ([#3125](https://github.com/prezero/hermes-sf/issues/3125)) ([a44f7ed](https://github.com/prezero/hermes-sf/commit/a44f7ed5f21785352b80782d8239a7fdc16623fe))
* Update nl config ([#3127](https://github.com/prezero/hermes-sf/issues/3127)) ([b84807a](https://github.com/prezero/hermes-sf/commit/b84807ace92a1f84594bb1349967a9783c585757))

## [4.250.2](https://github.com/prezero/hermes-sf/compare/v4.250.1...v4.250.2) (2025-06-18)


### Bug Fixes

* **HERMES-3658:** No longer use device context for nl file upload ([#3123](https://github.com/prezero/hermes-sf/issues/3123)) ([b3b7069](https://github.com/prezero/hermes-sf/commit/b3b70698e9123db8294b74d39faa79f7642eb519))

## [4.250.1](https://github.com/prezero/hermes-sf/compare/v4.250.0...v4.250.1) (2025-06-17)


### Bug Fixes

* **HERMES-3639:** branch message change ([#3118](https://github.com/prezero/hermes-sf/issues/3118)) ([9c2194d](https://github.com/prezero/hermes-sf/commit/9c2194d2ae6cf7695b517fb7bd22aeb3369cb0fb))

## [4.250.0](https://github.com/prezero/hermes-sf/compare/v4.249.0...v4.250.0) (2025-06-17)


### Features

* **HERMES-3644:** External VIR systems to device message ([#3117](https://github.com/prezero/hermes-sf/issues/3117)) ([1d9bec5](https://github.com/prezero/hermes-sf/commit/1d9bec5f0dfb68c89b3ae6d3c5bd693438eba73b))
* removed nav-token-ep ([1ef873f](https://github.com/prezero/hermes-sf/commit/1ef873faf751b49a67452ced9c3fe2b372656a2a))


### Bug Fixes

* **HERMES-3638:** removed unused traits ([#3121](https://github.com/prezero/hermes-sf/issues/3121)) ([ba06c07](https://github.com/prezero/hermes-sf/commit/ba06c072b29caa00917a625e1e48fcb5cb3e7d7d))
* **HERMES-3652:** switched to multicell ([#3119](https://github.com/prezero/hermes-sf/issues/3119)) ([b6cbc20](https://github.com/prezero/hermes-sf/commit/b6cbc2060aa51142787291411c8b729f51a38cf5))

## [4.249.0](https://github.com/prezero/hermes-sf/compare/v4.248.0...v4.249.0) (2025-06-16)


### Features

* Update translations to version 115 ([#3115](https://github.com/prezero/hermes-sf/issues/3115)) ([1bb6666](https://github.com/prezero/hermes-sf/commit/1bb6666fe085b9f2a9228e9314f3ba316af1c224))
* using reall trailer-ttype from NL ([2364493](https://github.com/prezero/hermes-sf/commit/2364493e4be4662e113a2e0e373b1136a6e1cc65))

## [4.248.0](https://github.com/prezero/hermes-sf/compare/v4.247.0...v4.248.0) (2025-06-16)


### Features

* **HERMES-3629:** staff focus ([#3110](https://github.com/prezero/hermes-sf/issues/3110)) ([7a2ebe6](https://github.com/prezero/hermes-sf/commit/7a2ebe64b65a0805d6875b2708cc18d4eb00d0a2))


### Bug Fixes

* App phonebook tests ([#3113](https://github.com/prezero/hermes-sf/issues/3113)) ([c36b103](https://github.com/prezero/hermes-sf/commit/c36b1032f5b8d801e1f5f4261d21f26d419ecad9))
* **deps:** update update composer packages to non-major versions ([#3074](https://github.com/prezero/hermes-sf/issues/3074)) ([bac58ea](https://github.com/prezero/hermes-sf/commit/bac58ea790990c379cb50cbca28d1a5d1a444cba))
* Removing pagination param on non-collection endpoint ([ef5e43e](https://github.com/prezero/hermes-sf/commit/ef5e43e5735a777f5b5732812e667b678cc4a198))


### Miscellaneous Chores

* **deps:** Adjusting renovate schedule ([1bdc662](https://github.com/prezero/hermes-sf/commit/1bdc6628fb4ae8a0abab8b6da8007c90bebd14d2))
* **deps:** update bitnami/keycloak:26.2.5 docker digest to 17aff52 ([#3094](https://github.com/prezero/hermes-sf/issues/3094)) ([0271480](https://github.com/prezero/hermes-sf/commit/0271480a353b7bb5ad8a5cb3351fd3b3c2779623))
* **deps:** update clowdhaus/argo-cd-action action to v3 ([#3105](https://github.com/prezero/hermes-sf/issues/3105)) ([3319ec5](https://github.com/prezero/hermes-sf/commit/3319ec524c69857d07e9cf5211cbe99ddd55220f))
* **deps:** update prezero/workflows action to v1.24.1 ([#3104](https://github.com/prezero/hermes-sf/issues/3104)) ([c55845e](https://github.com/prezero/hermes-sf/commit/c55845e6671071c6a4a668aa6f6479b8d509f66d))
* **deps:** update update local docker-compose env dependencies ([#3073](https://github.com/prezero/hermes-sf/issues/3073)) ([3b63037](https://github.com/prezero/hermes-sf/commit/3b63037deb6c3d006925f59ef8af09242382600c))
* Updating code owners ([b0d8854](https://github.com/prezero/hermes-sf/commit/b0d8854d3d94262df56b996caedeb6695a60d636))

## [4.247.0](https://github.com/prezero/hermes-sf/compare/v4.246.1...v4.247.0) (2025-06-16)


### Features

* **HERMES-2486:** Send VIR defects to external systems per country ([#3109](https://github.com/prezero/hermes-sf/issues/3109)) ([02ed13b](https://github.com/prezero/hermes-sf/commit/02ed13b17c54ef855b42291237322095ab97a81c))


### Bug Fixes

* Update app phone book serialized names ([#3112](https://github.com/prezero/hermes-sf/issues/3112)) ([24ecd9c](https://github.com/prezero/hermes-sf/commit/24ecd9ce849d64b0b7a942fc196dbabdda8e11ed))

## [4.246.1](https://github.com/prezero/hermes-sf/compare/v4.246.0...v4.246.1) (2025-06-13)


### Bug Fixes

* **HERMES-3631:** removing session from reedis on idle staff ([#3106](https://github.com/prezero/hermes-sf/issues/3106)) ([ea91681](https://github.com/prezero/hermes-sf/commit/ea91681e99f7261fc37bc425eccda7afc3246207))
* **HERMES-3637:** set pagination::none ([#3107](https://github.com/prezero/hermes-sf/issues/3107)) ([f6b9868](https://github.com/prezero/hermes-sf/commit/f6b9868eef6676a9990ef6c7d5b28c991216a52f))

## [4.246.0](https://github.com/prezero/hermes-sf/compare/v4.245.1...v4.246.0) (2025-06-12)


### Features

* **HERMES-3618:** Delaying tour cleanup up ([#3097](https://github.com/prezero/hermes-sf/issues/3097)) ([27842f6](https://github.com/prezero/hermes-sf/commit/27842f6bea9309bd68bace0df1bbcd2697a64118))
* **HERMES-3618:** Delaying tour cleanup up ([#3102](https://github.com/prezero/hermes-sf/issues/3102)) ([eb29c1e](https://github.com/prezero/hermes-sf/commit/eb29c1eeb5c14f277a2e26128c0c26ef1c75839a))
* **HERMES-3622:** language eps ([#3100](https://github.com/prezero/hermes-sf/issues/3100)) ([c0df682](https://github.com/prezero/hermes-sf/commit/c0df68267e0a138716ae48e11a29bc6b93656cf0))
* **HERMES-3623:** setting translation-tag ([#3099](https://github.com/prezero/hermes-sf/issues/3099)) ([72bc540](https://github.com/prezero/hermes-sf/commit/72bc54064d7c7e7857b3345fe3b5dd8885e7289f))
* **HERMES-3623:** vir outcome label ([#3103](https://github.com/prezero/hermes-sf/issues/3103)) ([c4499cf](https://github.com/prezero/hermes-sf/commit/c4499cf83eb2c77495da5d568941452dc871829c))
* increased dako-max-session-time ([ccfb0e7](https://github.com/prezero/hermes-sf/commit/ccfb0e728a052f79463ad60c0f7e7626cbe27e08))
* increased session-time ([#3101](https://github.com/prezero/hermes-sf/issues/3101)) ([7420515](https://github.com/prezero/hermes-sf/commit/7420515f038562d99e1e4835ffdc365858312cd1))

## [4.245.1](https://github.com/prezero/hermes-sf/compare/v4.245.0...v4.245.1) (2025-06-12)


### Bug Fixes

* **HERMES-3609:** Fix missing total count for vir ([#3095](https://github.com/prezero/hermes-sf/issues/3095)) ([f429ab2](https://github.com/prezero/hermes-sf/commit/f429ab2abaa6bda458aabeb59b3a82b0451b2afc))

## [4.245.0](https://github.com/prezero/hermes-sf/compare/v4.244.1...v4.245.0) (2025-06-11)


### Features

* **HERMES-3564:** Equipment position portal endpoint ([#3093](https://github.com/prezero/hermes-sf/issues/3093)) ([c2e211c](https://github.com/prezero/hermes-sf/commit/c2e211c6573ded77bdc8732ac963fc427b80d7b0))
* **HERMES-3581:** Equipment condition ([#3090](https://github.com/prezero/hermes-sf/issues/3090)) ([0593dd9](https://github.com/prezero/hermes-sf/commit/0593dd93c05d8ffecdb7df69c970b6cfb3381bb0))
* **HERMES-3606:** Remove countries from phonebooks ([#3092](https://github.com/prezero/hermes-sf/issues/3092)) ([b491eb8](https://github.com/prezero/hermes-sf/commit/b491eb89c90707b18c094beeaf0cc23bfe50ba5b))

## [4.244.1](https://github.com/prezero/hermes-sf/compare/v4.244.0...v4.244.1) (2025-06-11)


### Bug Fixes

* **HERMES-3605:** server-side validation improvement ([#3088](https://github.com/prezero/hermes-sf/issues/3088)) ([4010250](https://github.com/prezero/hermes-sf/commit/4010250dfe207619ec8c78543dfb91d6c4687d11))

## [4.244.0](https://github.com/prezero/hermes-sf/compare/v4.243.0...v4.244.0) (2025-06-11)


### Features

* providing dummy-config ([#3086](https://github.com/prezero/hermes-sf/issues/3086)) ([5d4486f](https://github.com/prezero/hermes-sf/commit/5d4486f2f0cbf3f1ab4d423b9420aa8aa65a4528))

## [4.243.0](https://github.com/prezero/hermes-sf/compare/v4.242.0...v4.243.0) (2025-06-10)


### Features

* **HERMES-3597:** added label to component ([#3084](https://github.com/prezero/hermes-sf/issues/3084)) ([f517406](https://github.com/prezero/hermes-sf/commit/f517406017deeba1662f131acc0dab77dd4c9a63))

## [4.242.0](https://github.com/prezero/hermes-sf/compare/v4.241.0...v4.242.0) (2025-06-10)


### Features

* **HERMES-3578:** Retrieve a single vehicle inspection report ([#3080](https://github.com/prezero/hermes-sf/issues/3080)) ([6b3f2d2](https://github.com/prezero/hermes-sf/commit/6b3f2d2ed24577b98ee38b43248eb57f571b48bc))
* **HERMES-3579:** fixed bottom-line ([#3077](https://github.com/prezero/hermes-sf/issues/3077)) ([7c7c9f8](https://github.com/prezero/hermes-sf/commit/7c7c9f8141aaca2eda89978f81b1acaf210b4d96))
* **HERMES-3592:** app-phonebook-list ([#3083](https://github.com/prezero/hermes-sf/issues/3083)) ([a8bed98](https://github.com/prezero/hermes-sf/commit/a8bed981e3ce7a4a5bc11ecd24fa40d264bfc1b6))
* **HERMES-3594:** OIDC Discovery endpoint ([#3082](https://github.com/prezero/hermes-sf/issues/3082)) ([0c49072](https://github.com/prezero/hermes-sf/commit/0c49072c868834075b64c443d3849f258ed32ecc))


### Bug Fixes

* added param to api-doc ([#3078](https://github.com/prezero/hermes-sf/issues/3078)) ([6e50748](https://github.com/prezero/hermes-sf/commit/6e507487b79065bb311d6497a00e2f9ff77e004c))
* **HERMES-3591:** removed manual setting of utc ([#3079](https://github.com/prezero/hermes-sf/issues/3079)) ([e4494c6](https://github.com/prezero/hermes-sf/commit/e4494c634504e4904419b3b54f3662e8d197262a))

## [4.241.0](https://github.com/prezero/hermes-sf/compare/v4.240.0...v4.241.0) (2025-06-10)


### Features

* **HERMES-3577:** Portal list vehicle inspections for equipment ([#3075](https://github.com/prezero/hermes-sf/issues/3075)) ([2051688](https://github.com/prezero/hermes-sf/commit/205168825e9feadbc3f253b0e542a29e835a6e1d))
* Update nl config ([#3072](https://github.com/prezero/hermes-sf/issues/3072)) ([7f50a71](https://github.com/prezero/hermes-sf/commit/7f50a7191b1d4f7f76a5157d45d4232ed0ecfbd0))


### Bug Fixes

* translating ts to utc on db-level ([#3010](https://github.com/prezero/hermes-sf/issues/3010)) ([62f0881](https://github.com/prezero/hermes-sf/commit/62f08815996ea0571c0284445ac509173c3b8a46))

## [4.240.0](https://github.com/prezero/hermes-sf/compare/v4.239.1...v4.240.0) (2025-06-06)


### Features

* **HERMES-3508:** Phonebook endpoints ([#3062](https://github.com/prezero/hermes-sf/issues/3062)) ([37cb087](https://github.com/prezero/hermes-sf/commit/37cb0870a9832d3545c434af0c3f804736c0a1ea))
* **HERMES-3566:** Adding files and description to vehicle inspection reports ([#3069](https://github.com/prezero/hermes-sf/issues/3069)) ([a808915](https://github.com/prezero/hermes-sf/commit/a8089158123de94d09930e11d9a50222e58ff70a))
* **HERMES-3576:** Add equipment position in get endpoint ([#3070](https://github.com/prezero/hermes-sf/issues/3070)) ([b0299e1](https://github.com/prezero/hermes-sf/commit/b0299e1df60948fdcf986e0468788caef4d846e2))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.344.1 ([#3026](https://github.com/prezero/hermes-sf/issues/3026)) ([c6c477d](https://github.com/prezero/hermes-sf/commit/c6c477d4fcb93d3bc3f4efb87907eba74649fe42))
* **HERMES-3333:** Document names into both additional information fields, cause someone needs it like that... ([#3071](https://github.com/prezero/hermes-sf/issues/3071)) ([ef9434c](https://github.com/prezero/hermes-sf/commit/ef9434c2d756041fa639d67788e776f13d25d1a3))
* **HERMES-3569:** Fix duplicate tenant stamp ([#3066](https://github.com/prezero/hermes-sf/issues/3066)) ([8dc9dff](https://github.com/prezero/hermes-sf/commit/8dc9dffc974c13ad521b67bf323fa3444ab72fd5))
* **HERMES-3570:** Do not send empty payload to IOT ([#3067](https://github.com/prezero/hermes-sf/issues/3067)) ([86d0947](https://github.com/prezero/hermes-sf/commit/86d0947abc0e7d85c583e3beef62fbc00236d7d2))


### Miscellaneous Chores

* **deps:** update rabbitmq:4.1-management docker digest to 0a59497 ([#3065](https://github.com/prezero/hermes-sf/issues/3065)) ([65271c6](https://github.com/prezero/hermes-sf/commit/65271c63879ab5c5c07f865c89047b125ccfea8e))


### Tests

* **HERMES-3362:** wiremock ([#3059](https://github.com/prezero/hermes-sf/issues/3059)) ([415b6ab](https://github.com/prezero/hermes-sf/commit/415b6ab3839c5734acfc1fa0016fd7e345f220bb))

## [4.239.1](https://github.com/prezero/hermes-sf/compare/v4.239.0...v4.239.1) (2025-06-04)


### Bug Fixes

* **HERMES-3565:** removing special chars ([#3064](https://github.com/prezero/hermes-sf/issues/3064)) ([050c974](https://github.com/prezero/hermes-sf/commit/050c9742377eb3ca876f9d6a1da6eb4140db0114))
* Logs going to stdout, no need to monitor log directory ([571db12](https://github.com/prezero/hermes-sf/commit/571db1258fc51c225f8ceb39f2e148caccc21b77))


### Miscellaneous Chores

* **deps:** Updating packages ([3b6a629](https://github.com/prezero/hermes-sf/commit/3b6a629dc51fe773d2e905eb913fdaca1ead2a93))

## [4.239.0](https://github.com/prezero/hermes-sf/compare/v4.238.0...v4.239.0) (2025-06-04)


### Features

* **HERMES-3462:** Vehicle inspection report config & report base ([#3015](https://github.com/prezero/hermes-sf/issues/3015)) ([e830396](https://github.com/prezero/hermes-sf/commit/e83039664ba089b68f94eac7e2141c5e27d2a320))
* **HERMES-3559:** Updating PHP version, moving to alpine image, removing symfony cli from deployed images ([#3057](https://github.com/prezero/hermes-sf/issues/3057)) ([5bca61c](https://github.com/prezero/hermes-sf/commit/5bca61cc3591940e10fbf603ba94502023983b0d))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak:26.2.5 docker digest to e623429 ([#3054](https://github.com/prezero/hermes-sf/issues/3054)) ([1fb91e6](https://github.com/prezero/hermes-sf/commit/1fb91e6fbcaa1bb33fa6b1d5e8e53835ab573d56))
* **deps:** update prezero/workflows action to v1.24.0 ([#3061](https://github.com/prezero/hermes-sf/issues/3061)) ([25b6209](https://github.com/prezero/hermes-sf/commit/25b6209964ba8f2248f721eddcc0cf7c98492198))
* **deps:** update update local docker-compose env dependencies ([#3060](https://github.com/prezero/hermes-sf/issues/3060)) ([327efc7](https://github.com/prezero/hermes-sf/commit/327efc73587a68de32e5663b4352cf4a6c39619e))

## [4.238.0](https://github.com/prezero/hermes-sf/compare/v4.237.0...v4.238.0) (2025-06-02)


### Features

* Update translations to version 114 ([#3055](https://github.com/prezero/hermes-sf/issues/3055)) ([2a4f9b9](https://github.com/prezero/hermes-sf/commit/2a4f9b9918083d40572c7f001a1d8f1661eb28e3))

## [4.237.0](https://github.com/prezero/hermes-sf/compare/v4.236.0...v4.237.0) (2025-05-30)


### Features

* **HERMES-3530:** Portal notifications for branch and country ([#3051](https://github.com/prezero/hermes-sf/issues/3051)) ([79213c5](https://github.com/prezero/hermes-sf/commit/79213c5a60f064cf81539547bdf904a1a8366143))
* Update german config ([#3053](https://github.com/prezero/hermes-sf/issues/3053)) ([8a27b51](https://github.com/prezero/hermes-sf/commit/8a27b513f302d599ec6f1cca69bd6ce384896f5c))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.2.5 ([#3050](https://github.com/prezero/hermes-sf/issues/3050)) ([9f3bebe](https://github.com/prezero/hermes-sf/commit/9f3bebe4463cbde3f0dce6d9a56a6aa3860e889f))
* **deps:** update prezero/workflows action to v1.22.6 ([#3045](https://github.com/prezero/hermes-sf/issues/3045)) ([a12f906](https://github.com/prezero/hermes-sf/commit/a12f9063f01a1a8bdf194de1f57a160f49fde66e))
* **deps:** update update local docker-compose env dependencies ([#3044](https://github.com/prezero/hermes-sf/issues/3044)) ([34a5c8f](https://github.com/prezero/hermes-sf/commit/34a5c8f4a4082081dbbae8010bcc4bc182b6b00c))
* **deps:** Updating packages ([d1c35fd](https://github.com/prezero/hermes-sf/commit/d1c35fd693387f0ccf2919f7fa8731a97d915559))

## [4.236.0](https://github.com/prezero/hermes-sf/compare/v4.235.3...v4.236.0) (2025-05-28)


### Features

* **HERMES-3542:** added translations to eq-type-endpoints ([#3048](https://github.com/prezero/hermes-sf/issues/3048)) ([7833142](https://github.com/prezero/hermes-sf/commit/7833142484acba89757e7ede7357ac2e4f178f42))

## [4.235.3](https://github.com/prezero/hermes-sf/compare/v4.235.2...v4.235.3) (2025-05-28)


### Continuous Integration

* adjust build release image ([#3046](https://github.com/prezero/hermes-sf/issues/3046)) ([12076a8](https://github.com/prezero/hermes-sf/commit/12076a8fb3360deb585c2e94500c3a914421b012))

## [4.235.2](https://github.com/prezero/hermes-sf/compare/v4.235.1...v4.235.2) (2025-05-28)


### Bug Fixes

* **HERMES-3531:** break always triggered before restart ([#3042](https://github.com/prezero/hermes-sf/issues/3042)) ([2ff512e](https://github.com/prezero/hermes-sf/commit/2ff512e0978a70104fe6ae72d26067be6d945dff))


### Continuous Integration

* fix sonar quality gate ([#3041](https://github.com/prezero/hermes-sf/issues/3041)) ([f1c9138](https://github.com/prezero/hermes-sf/commit/f1c9138b990060615b40ca745db74abb5d593ee3))

## [4.235.1](https://github.com/prezero/hermes-sf/compare/v4.235.0...v4.235.1) (2025-05-27)


### Bug Fixes

* **HERMES-3525:** non unique eq categories ([#3040](https://github.com/prezero/hermes-sf/issues/3040)) ([26f144f](https://github.com/prezero/hermes-sf/commit/26f144f41f846f23187f838bd1bba71868d1c642))


### Miscellaneous Chores

* **deps:** update update prezero docker images to v4.10.0 ([#3039](https://github.com/prezero/hermes-sf/issues/3039)) ([f963e15](https://github.com/prezero/hermes-sf/commit/f963e15641b24ebcd91e0777c4dcc934cdf9617c))


### Continuous Integration

* Continue on error for stackit quality checks ([75c3eb7](https://github.com/prezero/hermes-sf/commit/75c3eb7820a8625d35bd8dc32b763b8faac9efbf))
* fix dev deployment ([#3038](https://github.com/prezero/hermes-sf/issues/3038)) ([eb3c3be](https://github.com/prezero/hermes-sf/commit/eb3c3be2552aa2e2f779795da302c2e5290728da))
* fix flow boolean values ([#3037](https://github.com/prezero/hermes-sf/issues/3037)) ([6d3e6f6](https://github.com/prezero/hermes-sf/commit/6d3e6f690d28d414b40030b7d20eb45f17cf1181))
* switch partly to central workflows and add sonarqube and snyk scan ([#3028](https://github.com/prezero/hermes-sf/issues/3028)) ([dca0bb7](https://github.com/prezero/hermes-sf/commit/dca0bb7360f2a03a370f9bdaa26c85f0161c7d57))

## [4.235.0](https://github.com/prezero/hermes-sf/compare/v4.234.0...v4.235.0) (2025-05-27)


### Features

* Update translations to version 113 ([#3035](https://github.com/prezero/hermes-sf/issues/3035)) ([e1738e0](https://github.com/prezero/hermes-sf/commit/e1738e022148ba26644e55417acb9c286182c175))


### Miscellaneous Chores

* Adding sync config file ([8ab3af6](https://github.com/prezero/hermes-sf/commit/8ab3af612838da2f21d1061a2274c226a30506d4))

## [4.234.0](https://github.com/prezero/hermes-sf/compare/v4.233.0...v4.234.0) (2025-05-27)


### Features

* **HERMES-3491:** setting expected value for single disposalsite ([#3032](https://github.com/prezero/hermes-sf/issues/3032)) ([647b8f7](https://github.com/prezero/hermes-sf/commit/647b8f78b1771c934bdcd2c9a7fb22c22dd77503))

## [4.233.0](https://github.com/prezero/hermes-sf/compare/v4.232.0...v4.233.0) (2025-05-26)


### Features

* **HERMES-3475:** equipment list extended ([#3029](https://github.com/prezero/hermes-sf/issues/3029)) ([92655b1](https://github.com/prezero/hermes-sf/commit/92655b1830231e93eb72966d81ba16e43f30732c))

## [4.232.0](https://github.com/prezero/hermes-sf/compare/v4.231.0...v4.232.0) (2025-05-26)


### Features

* Update nl config ([#3030](https://github.com/prezero/hermes-sf/issues/3030)) ([b8cff5c](https://github.com/prezero/hermes-sf/commit/b8cff5cfd370a83c3cf6199baddb93544a18cd64))


### Bug Fixes

* Improve generation of checksum - include migrations and exclude production fixtures ([#3025](https://github.com/prezero/hermes-sf/issues/3025)) ([6710597](https://github.com/prezero/hermes-sf/commit/671059773bb923b7f238541bdfe8722e3039ac67))

## [4.231.0](https://github.com/prezero/hermes-sf/compare/v4.230.2...v4.231.0) (2025-05-23)


### Features

* **HERMES-3381:** Different tracking reducing rules ([#3024](https://github.com/prezero/hermes-sf/issues/3024)) ([82be0cd](https://github.com/prezero/hermes-sf/commit/82be0cd03a9182004e90cb7582ddbbd2a44b653d))
* **HERMES-3448:** Phonebook crud eps ([#3022](https://github.com/prezero/hermes-sf/issues/3022)) ([5f455cc](https://github.com/prezero/hermes-sf/commit/5f455cc614dbaa68239c0f0cbb50e80d77da8456))
* Update lux int fixtures ([#3019](https://github.com/prezero/hermes-sf/issues/3019)) ([7b4d78f](https://github.com/prezero/hermes-sf/commit/7b4d78fae7384df6e639f210cb5858764b8f887b))


### Tests

* more buffer-time for device-access ([#3023](https://github.com/prezero/hermes-sf/issues/3023)) ([ad3e292](https://github.com/prezero/hermes-sf/commit/ad3e292fb67a0c529944e35721099fe47703a4b5))

## [4.230.2](https://github.com/prezero/hermes-sf/compare/v4.230.1...v4.230.2) (2025-05-23)


### Bug Fixes

* compare-ts for device accesses ([#3020](https://github.com/prezero/hermes-sf/issues/3020)) ([c0df8c9](https://github.com/prezero/hermes-sf/commit/c0df8c92a1c3e4c9fe520fed9cb1402a5866d1bc))
* **deps:** update update composer packages to non-major versions ([#3012](https://github.com/prezero/hermes-sf/issues/3012)) ([723180e](https://github.com/prezero/hermes-sf/commit/723180eeae52e03fca3c4145567bf93edd6ae471))


### Miscellaneous Chores

* **deps:** update update local docker-compose env dependencies ([#3011](https://github.com/prezero/hermes-sf/issues/3011)) ([a04756d](https://github.com/prezero/hermes-sf/commit/a04756d68ca141da614b8b9e3c47f724e570dcb1))

## [4.230.1](https://github.com/prezero/hermes-sf/compare/v4.230.0...v4.230.1) (2025-05-22)


### Bug Fixes

* **HERMES-3468:** Add hotfix for device access timestamp ([#3016](https://github.com/prezero/hermes-sf/issues/3016)) ([0258813](https://github.com/prezero/hermes-sf/commit/0258813402640f0b33408cc6cc61e1f851237d8b))

## [4.230.0](https://github.com/prezero/hermes-sf/compare/v4.229.1...v4.230.0) (2025-05-21)


### Features

* **HERMES-3451:** Add task actions to tasks ([#3002](https://github.com/prezero/hermes-sf/issues/3002)) ([530163d](https://github.com/prezero/hermes-sf/commit/530163d9ef8e7cce90c9d4b0a0c1d23bf7692acf))
* Update pz config and translations ([#3008](https://github.com/prezero/hermes-sf/issues/3008)) ([8c12d7a](https://github.com/prezero/hermes-sf/commit/8c12d7ab0071b24abcbbb50ae47a24bccd2f73ae))

## [4.229.1](https://github.com/prezero/hermes-sf/compare/v4.229.0...v4.229.1) (2025-05-20)


### Bug Fixes

* **HERMES-3440:** fixed upload-check, additional log ([#3007](https://github.com/prezero/hermes-sf/issues/3007)) ([3c90198](https://github.com/prezero/hermes-sf/commit/3c90198a46391bab642c0c5c604c4d47b4853535))
* **HERMES-3464:** fixed count-statement ([#3006](https://github.com/prezero/hermes-sf/issues/3006)) ([931da63](https://github.com/prezero/hermes-sf/commit/931da6352a78c1b062d5a178c8902970be3ba0be))

## [4.229.0](https://github.com/prezero/hermes-sf/compare/v4.228.0...v4.229.0) (2025-05-20)


### Features

* **HERMES-3463:** added new status calls to pz and pze ([#3004](https://github.com/prezero/hermes-sf/issues/3004)) ([fc2336c](https://github.com/prezero/hermes-sf/commit/fc2336c8aa8095b0dcee0162b79a9f5c4094ca1f))

## [4.228.0](https://github.com/prezero/hermes-sf/compare/v4.227.0...v4.228.0) (2025-05-19)


### Features

* **HERMES-3452:** added break/restart-flow for NL ([#2998](https://github.com/prezero/hermes-sf/issues/2998)) ([ebbbddf](https://github.com/prezero/hermes-sf/commit/ebbbddf9a671ce59c270893efea2a5d26acadc18))


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#2933](https://github.com/prezero/hermes-sf/issues/2933)) ([43d87da](https://github.com/prezero/hermes-sf/commit/43d87da5d006006923561553365f784dbfb709b3))
* S3 client library automatically prefixes with a slash ([#3003](https://github.com/prezero/hermes-sf/issues/3003)) ([07bafd9](https://github.com/prezero/hermes-sf/commit/07bafd91ccbade29a5b23d623537a85bcf8f84db))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.2.4 ([#2939](https://github.com/prezero/hermes-sf/issues/2939)) ([ed4d185](https://github.com/prezero/hermes-sf/commit/ed4d18580775a2e0cef3be90312f2a3418578bbd))
* **deps:** update docker/build-push-action digest to 1dc7386 ([#2992](https://github.com/prezero/hermes-sf/issues/2992)) ([c8d9fde](https://github.com/prezero/hermes-sf/commit/c8d9fdebfa5e081e97f528dfded6eae59b35f106))
* **deps:** update update local docker-compose env dependencies ([#2954](https://github.com/prezero/hermes-sf/issues/2954)) ([cb04b9c](https://github.com/prezero/hermes-sf/commit/cb04b9cde40f378b132b81b3a5278746b7bb9d2f))

## [4.227.0](https://github.com/prezero/hermes-sf/compare/v4.226.0...v4.227.0) (2025-05-19)


### Features

* **HERMES-3449:** Mobile app releases ([#2997](https://github.com/prezero/hermes-sf/issues/2997)) ([2b6baf6](https://github.com/prezero/hermes-sf/commit/2b6baf642cc37f90b50c7f236a6a3e83017ed495))
* lux int config and translation update ([#2995](https://github.com/prezero/hermes-sf/issues/2995)) ([58c0b76](https://github.com/prezero/hermes-sf/commit/58c0b7615b43c81d2153e2d527b35dc169d63982))
* Update pz config ([#2996](https://github.com/prezero/hermes-sf/issues/2996)) ([2438e1e](https://github.com/prezero/hermes-sf/commit/2438e1e621b9459ec6863ff3e6759b2d5945cc65))


### Bug Fixes

* **HERMES-3450:** Fix tour counting ([#3000](https://github.com/prezero/hermes-sf/issues/3000)) ([1d2efd6](https://github.com/prezero/hermes-sf/commit/1d2efd638df7d4613d317af141eee40885c67e05))

## [4.226.0](https://github.com/prezero/hermes-sf/compare/v4.225.0...v4.226.0) (2025-05-16)


### Features

* **HERMES-3412:** Dako single file upload ([#2988](https://github.com/prezero/hermes-sf/issues/2988)) ([e7f3924](https://github.com/prezero/hermes-sf/commit/e7f3924d878859927659595e60fbe8607301c37d))
* **HERMES-3412:** Dako single file upload ([#2994](https://github.com/prezero/hermes-sf/issues/2994)) ([abb8b28](https://github.com/prezero/hermes-sf/commit/abb8b283ca08e3b5181c60ab96a08c5d112d9684))
* **HERMES-3427:** Order focus endpoint & internal structure & async events ([#2986](https://github.com/prezero/hermes-sf/issues/2986)) ([4fd9b7f](https://github.com/prezero/hermes-sf/commit/4fd9b7f15cc6f46da74f27a45e4633b4bdca44f9))
* **HERMES-3437:** Add autoAssignment to scale element ([#2989](https://github.com/prezero/hermes-sf/issues/2989)) ([7476601](https://github.com/prezero/hermes-sf/commit/7476601df444bd0871e6f3fbb508a5b72c59e57e))

## [4.225.0](https://github.com/prezero/hermes-sf/compare/v4.224.0...v4.225.0) (2025-05-15)


### Features

* Add tour order types for pz sub ([#2987](https://github.com/prezero/hermes-sf/issues/2987)) ([b17a739](https://github.com/prezero/hermes-sf/commit/b17a739ed73c6317dab686dbacfb3e9c8340a034))
* **HERMES-3396:** Include recently dispatched staff and equipment in… ([#2984](https://github.com/prezero/hermes-sf/issues/2984)) ([a361058](https://github.com/prezero/hermes-sf/commit/a361058e48172cdb311ef25de16d620ac3a6bc10))
* **HERMES-3405:** Bugfix portal user creation cest ([#2982](https://github.com/prezero/hermes-sf/issues/2982)) ([2570707](https://github.com/prezero/hermes-sf/commit/25707079ba9819bcc8fcee589582ff5322f47a94))
* **HERMES-3438:** Removing tenant relation, adjusting indexes & fixtures ([#2990](https://github.com/prezero/hermes-sf/issues/2990)) ([14b796e](https://github.com/prezero/hermes-sf/commit/14b796ef448e7c9de24af36e588992a8b2fc118f))
* NL config and translation update ([#2991](https://github.com/prezero/hermes-sf/issues/2991)) ([aaf4dcd](https://github.com/prezero/hermes-sf/commit/aaf4dcd8ae88e482922e99d91d909250c2851675))


### Bug Fixes

* new dump ([#2985](https://github.com/prezero/hermes-sf/issues/2985)) ([26cc9c4](https://github.com/prezero/hermes-sf/commit/26cc9c490ba1f8294a2e57f417e27b8419484a17))

## [4.224.0](https://github.com/prezero/hermes-sf/compare/v4.223.1...v4.224.0) (2025-05-13)


### Features

* **HERMES-3402:** command to test mail and debug ([#2980](https://github.com/prezero/hermes-sf/issues/2980)) ([9429f12](https://github.com/prezero/hermes-sf/commit/9429f12c4991b02f8d4df0c077ccfcfc8b0be6f8))


### Bug Fixes

* reduced interval ([#2979](https://github.com/prezero/hermes-sf/issues/2979)) ([911c9a0](https://github.com/prezero/hermes-sf/commit/911c9a08de48607383ffc98fba169a00de3bfe24))

## [4.223.1](https://github.com/prezero/hermes-sf/compare/v4.223.0...v4.223.1) (2025-05-13)


### Bug Fixes

* **HERMES-3404:** upload overlap ([#2976](https://github.com/prezero/hermes-sf/issues/2976)) ([7f1a265](https://github.com/prezero/hermes-sf/commit/7f1a2651e6acc77f4fb5ea7b44294bdfdd08da56))

## [4.223.0](https://github.com/prezero/hermes-sf/compare/v4.222.0...v4.223.0) (2025-05-13)


### Features

* **HERMES-3222:** Portal ep tour set mastertours ([#2975](https://github.com/prezero/hermes-sf/issues/2975)) ([8711264](https://github.com/prezero/hermes-sf/commit/8711264655f7e4874816c848201def1517587b51))

## [4.222.0](https://github.com/prezero/hermes-sf/compare/v4.221.0...v4.222.0) (2025-05-13)


### Features

* **HERMES-3222:** Portal ep tour set mastertours ([#2963](https://github.com/prezero/hermes-sf/issues/2963)) ([403d4a0](https://github.com/prezero/hermes-sf/commit/403d4a0f751fda47ec4311f15bc4ddc880eef019))
* **HERMES-3374:** Tenant migration to embedded value inside each table ([#2972](https://github.com/prezero/hermes-sf/issues/2972)) ([0bfa7a4](https://github.com/prezero/hermes-sf/commit/0bfa7a43c266ec6c5b846ac37074163f2b57a038))
* Update german subcontractor config ([#2971](https://github.com/prezero/hermes-sf/issues/2971)) ([c912c0d](https://github.com/prezero/hermes-sf/commit/c912c0d3e0aec662c7c46d205513daa759ff05eb))


### Tests

* **HERMES-3045:** interruption order test ([#2974](https://github.com/prezero/hermes-sf/issues/2974)) ([28cc8e7](https://github.com/prezero/hermes-sf/commit/28cc8e799c749f91ef11b0a7671798c1b01a53ce))

## [4.221.0](https://github.com/prezero/hermes-sf/compare/v4.220.0...v4.221.0) (2025-05-09)


### Features

* **HERMES-3361:** User cleanup by more filters ([#2969](https://github.com/prezero/hermes-sf/issues/2969)) ([6c2ae5a](https://github.com/prezero/hermes-sf/commit/6c2ae5ad2af24d3761f6e99756205fccf5b42fe2))
* Update german config ([#2970](https://github.com/prezero/hermes-sf/issues/2970)) ([576632e](https://github.com/prezero/hermes-sf/commit/576632efe2bf45d9ae489c6e28f74522086f73e0))


### Bug Fixes

* **HERMES-3049:** fixed item-count ([#2966](https://github.com/prezero/hermes-sf/issues/2966)) ([c8436af](https://github.com/prezero/hermes-sf/commit/c8436af03489388e8ba550c88f6bd94889bd5914))

## [4.220.0](https://github.com/prezero/hermes-sf/compare/v4.219.0...v4.220.0) (2025-05-09)


### Features

* Add config for pz sub contractors ([#2967](https://github.com/prezero/hermes-sf/issues/2967)) ([a315cc1](https://github.com/prezero/hermes-sf/commit/a315cc186c76e65dfd653531aa81b326cea0da7a))
* **HERMES-3361:** User cleanup scripts ([#2964](https://github.com/prezero/hermes-sf/issues/2964)) ([96989db](https://github.com/prezero/hermes-sf/commit/96989dbb6c54ff1162fced6726a98aa05af6d59a))
* **HERMES-3367:** Support for property documentation with comments ([#2962](https://github.com/prezero/hermes-sf/issues/2962)) ([d71155c](https://github.com/prezero/hermes-sf/commit/d71155c473a5993ac97c17eb42c733616baf35f7))

## [4.219.0](https://github.com/prezero/hermes-sf/compare/v4.218.0...v4.219.0) (2025-05-07)


### Features

* **HERMES-3223:** Portal end equipment session endpoint ([#2952](https://github.com/prezero/hermes-sf/issues/2952)) ([62de9fb](https://github.com/prezero/hermes-sf/commit/62de9fb93e23e2d6c71cee10063de7411545dbe0))


### Bug Fixes

* reduced interval ([#2961](https://github.com/prezero/hermes-sf/issues/2961)) ([80776db](https://github.com/prezero/hermes-sf/commit/80776dbe25491b8ced0c0194a1f561cd8676206e))

## [4.218.0](https://github.com/prezero/hermes-sf/compare/v4.217.0...v4.218.0) (2025-05-07)


### Features

* **HERMES-3330:** Improving error responses & update libraries ([#2959](https://github.com/prezero/hermes-sf/issues/2959)) ([aa6a529](https://github.com/prezero/hermes-sf/commit/aa6a529ada3cf9d36dda35c0af108e9e5150cfc7))
* Update german config ([#2957](https://github.com/prezero/hermes-sf/issues/2957)) ([49a9c53](https://github.com/prezero/hermes-sf/commit/49a9c539fb67c8817f6eb42d6cd352d33524c0a8))
* Update translations to version 108 ([#2949](https://github.com/prezero/hermes-sf/issues/2949)) ([4af204e](https://github.com/prezero/hermes-sf/commit/4af204ece1ba085804446bad8650fbb9599ef9a5))

## [4.217.0](https://github.com/prezero/hermes-sf/compare/v4.216.0...v4.217.0) (2025-05-07)


### Features

* Update german config ([#2955](https://github.com/prezero/hermes-sf/issues/2955)) ([641a7fe](https://github.com/prezero/hermes-sf/commit/641a7fe0d2370ef3fd4c85f4a9f7d7513b1afd25))

## [4.216.0](https://github.com/prezero/hermes-sf/compare/v4.215.0...v4.216.0) (2025-05-06)


### Features

* **HERMES-3331:** scale new attribute ([#2950](https://github.com/prezero/hermes-sf/issues/2950)) ([d501425](https://github.com/prezero/hermes-sf/commit/d501425351b7c9a72278c462c35359ffc577c54f))

## [4.215.0](https://github.com/prezero/hermes-sf/compare/v4.214.3...v4.215.0) (2025-05-05)


### Features

* **HERMES-3326:** Adding support for localization in the API, using it for translation of validation messages ([#2948](https://github.com/prezero/hermes-sf/issues/2948)) ([61f67c2](https://github.com/prezero/hermes-sf/commit/61f67c2efe96826945a10c1fe1e869b9a57d8ffd))


### Bug Fixes

* **HERMES-2787:** set restriction for max-int ([#2946](https://github.com/prezero/hermes-sf/issues/2946)) ([8beb953](https://github.com/prezero/hermes-sf/commit/8beb95354183832b5fa31cc21337bbe428bb91d1))

## [4.214.3](https://github.com/prezero/hermes-sf/compare/v4.214.2...v4.214.3) (2025-05-02)


### Miscellaneous Chores

* **HERMES-3304:** Removing old cleanup scripts and already migrated parts of the system ([#2944](https://github.com/prezero/hermes-sf/issues/2944)) ([fdc5752](https://github.com/prezero/hermes-sf/commit/fdc5752ffa0e1139538543d70f55966354a49509))

## [4.214.2](https://github.com/prezero/hermes-sf/compare/v4.214.1...v4.214.2) (2025-05-02)


### Bug Fixes

* **HERMES-3312:** changed cleaning by id ([#2942](https://github.com/prezero/hermes-sf/issues/2942)) ([aa17962](https://github.com/prezero/hermes-sf/commit/aa17962a41491479030df554d9ce5b8aa28ed36d))

## [4.214.1](https://github.com/prezero/hermes-sf/compare/v4.214.0...v4.214.1) (2025-05-02)


### Bug Fixes

* **HERMES-3292:** log-level-change ([#2940](https://github.com/prezero/hermes-sf/issues/2940)) ([7bc0727](https://github.com/prezero/hermes-sf/commit/7bc0727cedc56f57cdbb33226215647428fb467c))

## [4.214.0](https://github.com/prezero/hermes-sf/compare/v4.213.0...v4.214.0) (2025-04-30)


### Features

* Update translations to version 107 ([#2937](https://github.com/prezero/hermes-sf/issues/2937)) ([8421e05](https://github.com/prezero/hermes-sf/commit/8421e055f0131f59e76bb35f133547198b5d0846))

## [4.213.0](https://github.com/prezero/hermes-sf/compare/v4.212.0...v4.213.0) (2025-04-30)


### Features

* **HERMES-3292:** reduced error-logs for common dako-problems ([#2930](https://github.com/prezero/hermes-sf/issues/2930)) ([22c9103](https://github.com/prezero/hermes-sf/commit/22c9103f65fc16ae5a45ba7242498349c4124fc1))

## [4.212.0](https://github.com/prezero/hermes-sf/compare/v4.211.0...v4.212.0) (2025-04-30)


### Features

* Update german config ([#2934](https://github.com/prezero/hermes-sf/issues/2934)) ([98b14e4](https://github.com/prezero/hermes-sf/commit/98b14e46657f136603db8013e3e62ca8db9e173e))


### Tests

* **HERMES-3295:** added test to call behaviour ([#2928](https://github.com/prezero/hermes-sf/issues/2928)) ([56c25df](https://github.com/prezero/hermes-sf/commit/56c25dfa24e8663310c3cb48c638273fda9515fc))

## [4.211.0](https://github.com/prezero/hermes-sf/compare/v4.210.0...v4.211.0) (2025-04-29)


### Features

* **HERMES-3045:** Interruption and termination sequenceing ([#2915](https://github.com/prezero/hermes-sf/issues/2915)) ([bb043cb](https://github.com/prezero/hermes-sf/commit/bb043cba81bd87c20d3366a70626dafd031acdbf))

## [4.210.0](https://github.com/prezero/hermes-sf/compare/v4.209.0...v4.210.0) (2025-04-29)


### Features

* Update german config ([#2924](https://github.com/prezero/hermes-sf/issues/2924)) ([afd7eb2](https://github.com/prezero/hermes-sf/commit/afd7eb2a5677db4ed07b2ea4e3a2b99246d522d5))
* Update translations to version 105 ([#2929](https://github.com/prezero/hermes-sf/issues/2929)) ([68a8b9c](https://github.com/prezero/hermes-sf/commit/68a8b9c5a54d66bd85b0b7a0b4f644de28a77b0f))
* Update translations to version 106 ([#2931](https://github.com/prezero/hermes-sf/issues/2931)) ([9afcc2d](https://github.com/prezero/hermes-sf/commit/9afcc2d81fb5f475af8d8ed185228025c4986967))


### Bug Fixes

* **deps:** update dependency prezero/api-bundle to v1.6.0 ([#2927](https://github.com/prezero/hermes-sf/issues/2927)) ([1d854c8](https://github.com/prezero/hermes-sf/commit/1d854c813921d6578cc8b11a03c45e3c1be2266f))
* **deps:** update update composer packages to non-major versions ([#2910](https://github.com/prezero/hermes-sf/issues/2910)) ([43b4f5c](https://github.com/prezero/hermes-sf/commit/43b4f5cbe6d851b16cdefd840e3962953089820d))


### Miscellaneous Chores

* **deps:** update centrifugo/centrifugo:v6 docker digest to 88bbe2a ([#2926](https://github.com/prezero/hermes-sf/issues/2926)) ([51d80ef](https://github.com/prezero/hermes-sf/commit/51d80ef3adc5c503936ce9eb5ba45f3adbd42a89))
* **deps:** update docker/build-push-action digest to 14487ce ([#2909](https://github.com/prezero/hermes-sf/issues/2909)) ([2358dd4](https://github.com/prezero/hermes-sf/commit/2358dd418e71503a4a6d65ba56cd7a437be78788))

## [4.209.0](https://github.com/prezero/hermes-sf/compare/v4.208.0...v4.209.0) (2025-04-28)


### Features

* Update translations to version 104 ([#2922](https://github.com/prezero/hermes-sf/issues/2922)) ([2d182cd](https://github.com/prezero/hermes-sf/commit/2d182cda546dd862782fe13b341d56a0f0f68dd2))


### Bug Fixes

* added to DTO ([#2920](https://github.com/prezero/hermes-sf/issues/2920)) ([a54aa3b](https://github.com/prezero/hermes-sf/commit/a54aa3b0574894a5e3398c703e7db0ae24564e61))

## [4.208.0](https://github.com/prezero/hermes-sf/compare/v4.207.0...v4.208.0) (2025-04-28)


### Features

* **HERMES-3261:** Add filter on user accessible branches for tour tracking listing and retrieval ([#2913](https://github.com/prezero/hermes-sf/issues/2913)) ([9f09796](https://github.com/prezero/hermes-sf/commit/9f09796c3f2cd90941d445be6fcda37ca3e2c546))


### Bug Fixes

* on transition for aborted processes ([#2918](https://github.com/prezero/hermes-sf/issues/2918)) ([264b67e](https://github.com/prezero/hermes-sf/commit/264b67eab850704950fd23dba7b793d79c1e99cb))

## [4.207.0](https://github.com/prezero/hermes-sf/compare/v4.206.0...v4.207.0) (2025-04-28)


### Features

* **HERMES-3280:** Adding automatic nested object validation ([#2912](https://github.com/prezero/hermes-sf/issues/2912)) ([d15c2b3](https://github.com/prezero/hermes-sf/commit/d15c2b3f790e1ed2e19f8f20898223d854a6ae0f))
* Update german config ([#2914](https://github.com/prezero/hermes-sf/issues/2914)) ([b407ea6](https://github.com/prezero/hermes-sf/commit/b407ea60f3b5d02dd98effd8e27ebe3d60d41e4b))

## [4.206.0](https://github.com/prezero/hermes-sf/compare/v4.205.1...v4.206.0) (2025-04-25)


### Features

* **HERMES-3258:** Document cleanup ([#2911](https://github.com/prezero/hermes-sf/issues/2911)) ([e8744e8](https://github.com/prezero/hermes-sf/commit/e8744e8a7aea07065d28c8f7350a9a73a2eda3b5))
* **HERMES-3259:** Change needed permissions to access connected devices ([#2905](https://github.com/prezero/hermes-sf/issues/2905)) ([958ec68](https://github.com/prezero/hermes-sf/commit/958ec6880a89b3318aef69e859a1cc4ddd836cb6))
* Update german fixtures ([#2907](https://github.com/prezero/hermes-sf/issues/2907)) ([b16d31c](https://github.com/prezero/hermes-sf/commit/b16d31c8181327ee682b284e6112029c9e7bce62))


### Bug Fixes

* **HERMES-3254:** Fix count and durations of tracking list data based on user permissions ([#2906](https://github.com/prezero/hermes-sf/issues/2906)) ([40231f9](https://github.com/prezero/hermes-sf/commit/40231f9855b2f2d9878e2bdf5a6979f23a9193c7))

## [4.205.1](https://github.com/prezero/hermes-sf/compare/v4.205.0...v4.205.1) (2025-04-24)


### Bug Fixes

* Delete documents with invalid link ([#2903](https://github.com/prezero/hermes-sf/issues/2903)) ([894e4d8](https://github.com/prezero/hermes-sf/commit/894e4d80fd3e80903d9c80283736be8f99259f40))

## [4.205.0](https://github.com/prezero/hermes-sf/compare/v4.204.0...v4.205.0) (2025-04-24)


### Features

* **HERMES-3129:** search in user ([#2894](https://github.com/prezero/hermes-sf/issues/2894)) ([82f9a47](https://github.com/prezero/hermes-sf/commit/82f9a471d5321ac608ea90f1d633195978f5cc96))
* **HERMES-3131:** Portal search in staff ([#2898](https://github.com/prezero/hermes-sf/issues/2898)) ([9a34ecb](https://github.com/prezero/hermes-sf/commit/9a34ecbbb2d2b6a6bba01882586d123bb1f391e5))
* Update translations to version 102 ([#2897](https://github.com/prezero/hermes-sf/issues/2897)) ([7491f99](https://github.com/prezero/hermes-sf/commit/7491f993709a5c8773bbca626a2e209a4725a52e))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.342.33 ([#2865](https://github.com/prezero/hermes-sf/issues/2865)) ([45c248f](https://github.com/prezero/hermes-sf/commit/45c248ffb427b23576e86aed1b5deb5dc1d63c79))
* **HERMES-3220:** Attaching document by order and tour IDs instead of external IDs ([#2895](https://github.com/prezero/hermes-sf/issues/2895)) ([8a96f38](https://github.com/prezero/hermes-sf/commit/8a96f3823793ae522954061ec878b1436a1a5959))
* **HERMES-3239:** Adding type to disposal site task template ([#2902](https://github.com/prezero/hermes-sf/issues/2902)) ([eb69912](https://github.com/prezero/hermes-sf/commit/eb69912ad490cc783cec98f6947515783d99ca0e))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.2.0 ([#2866](https://github.com/prezero/hermes-sf/issues/2866)) ([a02429e](https://github.com/prezero/hermes-sf/commit/a02429ef0bcd0e0634edc5da4f83d0da3fbb3903))
* **deps:** update bitnami/keycloak docker tag to v26.2.1 ([#2901](https://github.com/prezero/hermes-sf/issues/2901)) ([926193d](https://github.com/prezero/hermes-sf/commit/926193d2bb6beda0273a0c0093f7ea6d1900511b))
* **deps:** update update local docker-compose env dependencies ([#2885](https://github.com/prezero/hermes-sf/issues/2885)) ([0d1a9e3](https://github.com/prezero/hermes-sf/commit/0d1a9e37514184a35fd9435af677084d4eab0b1e))


### Code Refactoring

* **HERMES-3249:** Updating packages & rector refactoring with latest Symfony, PHP and Doctrine rules. ([#2900](https://github.com/prezero/hermes-sf/issues/2900)) ([51b1bad](https://github.com/prezero/hermes-sf/commit/51b1bad7dcbd116ce8bfce367d4db7beb124ea38))


### Tests

* **HERMES-3206:** Test for tour, order and taskgroup additional information ([#2899](https://github.com/prezero/hermes-sf/issues/2899)) ([5e41094](https://github.com/prezero/hermes-sf/commit/5e410946d9f926fda5f8a484827af5d1dab4c8ed))

## [4.204.0](https://github.com/prezero/hermes-sf/compare/v4.203.0...v4.204.0) (2025-04-16)


### Features

* Update pz config ([#2892](https://github.com/prezero/hermes-sf/issues/2892)) ([40013f6](https://github.com/prezero/hermes-sf/commit/40013f6edcffbcb3ab71c4aa123ce96b866e6f23))


### Bug Fixes

* rebuilding tg-add-infos ([#2891](https://github.com/prezero/hermes-sf/issues/2891)) ([e9e4549](https://github.com/prezero/hermes-sf/commit/e9e454945a664c296249f32d835ed8e262ae3b94))

## [4.203.0](https://github.com/prezero/hermes-sf/compare/v4.202.0...v4.203.0) (2025-04-16)


### Features

* Update german config ([#2888](https://github.com/prezero/hermes-sf/issues/2888)) ([0597323](https://github.com/prezero/hermes-sf/commit/05973234b5b920c9896e301a71536f689e0c8efa))


### Bug Fixes

* **HERMES-3202:** added translation-tag and fixed a filter ([#2889](https://github.com/prezero/hermes-sf/issues/2889)) ([f6adf31](https://github.com/prezero/hermes-sf/commit/f6adf318b45dd38bfa2b8d02d22ea45b44cf8fd1))

## [4.202.0](https://github.com/prezero/hermes-sf/compare/v4.201.0...v4.202.0) (2025-04-16)


### Features

* **HERMES-3183:** Equipment filter changes, filters adjustments ([#2879](https://github.com/prezero/hermes-sf/issues/2879)) ([0109962](https://github.com/prezero/hermes-sf/commit/01099624fdaf36735b57f36eca1b2a5bc988c559))
* Update translations to version 101 ([#2887](https://github.com/prezero/hermes-sf/issues/2887)) ([68ecca0](https://github.com/prezero/hermes-sf/commit/68ecca0393eadee1a3062de6ea5becf3df330521))


### Miscellaneous Chores

* **deps:** pin googleapis/release-please-action action to a02a34c ([#2884](https://github.com/prezero/hermes-sf/issues/2884)) ([8e0a1bc](https://github.com/prezero/hermes-sf/commit/8e0a1bc7cdf320861d9805eb9317b0a21ed848ba))

## [4.201.0](https://github.com/prezero/hermes-sf/compare/v4.200.0...v4.201.0) (2025-04-15)


### Features

* Config and translation update ([#2880](https://github.com/prezero/hermes-sf/issues/2880)) ([5023495](https://github.com/prezero/hermes-sf/commit/5023495028bbf1789d3c04efb5242ddd928614d6))


### Bug Fixes

* **HERMES-3195:** changed status-string ([#2883](https://github.com/prezero/hermes-sf/issues/2883)) ([d23d4b1](https://github.com/prezero/hermes-sf/commit/d23d4b15268328128110de151f0371108f275855))


### Miscellaneous Chores

* **deps:** Updating release please to supported repo ([0a9c824](https://github.com/prezero/hermes-sf/commit/0a9c82450b1cf1257350a1eb9b98fd2e1ffea1c3))

## [4.200.0](https://github.com/prezero/hermes-sf/compare/v4.199.0...v4.200.0) (2025-04-15)


### Features

* **HERMES-2418:** User delete by prefix ([#2868](https://github.com/prezero/hermes-sf/issues/2868)) ([9a7ea0c](https://github.com/prezero/hermes-sf/commit/9a7ea0c6741dd692e1dff037e35b095362a0241b))
* **HERMES-3165:** renamed attribute ([#2867](https://github.com/prezero/hermes-sf/issues/2867)) ([e25ceb9](https://github.com/prezero/hermes-sf/commit/e25ceb9a7218a2cb8961956381f40d5f0a2b1dbf))


### Bug Fixes

* **HERMES-3189:** Booking description column size ([#2877](https://github.com/prezero/hermes-sf/issues/2877)) ([c3ff74f](https://github.com/prezero/hermes-sf/commit/c3ff74f6b9077e0ca0c1bbc29fb27d12360cd408))

## [4.199.0](https://github.com/prezero/hermes-sf/compare/v4.198.0...v4.199.0) (2025-04-15)


### Features

* **HERMES-2357:** file name change ([#2874](https://github.com/prezero/hermes-sf/issues/2874)) ([cd2f8ef](https://github.com/prezero/hermes-sf/commit/cd2f8efc2dc2b01796731f9886c21ea150496f77))
* **HERMES-368:** changed identifier ([#2875](https://github.com/prezero/hermes-sf/issues/2875)) ([5ad3001](https://github.com/prezero/hermes-sf/commit/5ad30016e77ee51fe0bcb1dea7fd5008899bff5f))

## [4.198.0](https://github.com/prezero/hermes-sf/compare/v4.197.0...v4.198.0) (2025-04-14)


### Features

* Update german and dutch configs ([#2872](https://github.com/prezero/hermes-sf/issues/2872)) ([35c8f0b](https://github.com/prezero/hermes-sf/commit/35c8f0ba428d408922dcf6d92790904c28248524))

## [4.197.0](https://github.com/prezero/hermes-sf/compare/v4.196.0...v4.197.0) (2025-04-14)


### Features

* Update pz config ([#2869](https://github.com/prezero/hermes-sf/issues/2869)) ([6cc9cb0](https://github.com/prezero/hermes-sf/commit/6cc9cb03bcc5c4a9879776ca9fe76d5a85f10496))


### Bug Fixes

* **HERMES-2806:** changed order of actions in service, added test ([#2871](https://github.com/prezero/hermes-sf/issues/2871)) ([0419d0d](https://github.com/prezero/hermes-sf/commit/0419d0da11aa999de45caf3107252fdd22f530d9))

## [4.196.0](https://github.com/prezero/hermes-sf/compare/v4.195.1...v4.196.0) (2025-04-11)


### Features

* **HERMES-2844:** changed filtering before writing session-bookings for the portal ([#2862](https://github.com/prezero/hermes-sf/issues/2862)) ([2e0b193](https://github.com/prezero/hermes-sf/commit/2e0b19363f69d9abc075617abd0134649230b493))

## [4.195.1](https://github.com/prezero/hermes-sf/compare/v4.195.0...v4.195.1) (2025-04-11)


### Bug Fixes

* **HERMES-3158:** Handle events only on main request ([#2860](https://github.com/prezero/hermes-sf/issues/2860)) ([55e3bb1](https://github.com/prezero/hermes-sf/commit/55e3bb1d9c1bc86d391c0bcabdacc387e9706a3c))

## [4.195.0](https://github.com/prezero/hermes-sf/compare/v4.194.0...v4.195.0) (2025-04-11)


### Features

* **HERMES-3155:** changes file-list-item for portal ([#2859](https://github.com/prezero/hermes-sf/issues/2859)) ([5fef8db](https://github.com/prezero/hermes-sf/commit/5fef8db6d9ba54f9d5d8bb3a3099b5b85e9daa4c))
* Update nl config ([#2855](https://github.com/prezero/hermes-sf/issues/2855)) ([2d8c248](https://github.com/prezero/hermes-sf/commit/2d8c2487230dac059fb1ce26780aff6beeeae58f))


### Bug Fixes

* **deps:** update dependency martin-georgiev/postgresql-for-doctrine to v3 ([#2804](https://github.com/prezero/hermes-sf/issues/2804)) ([a099560](https://github.com/prezero/hermes-sf/commit/a099560f98ec668062350b239e04199d3974976e))
* **deps:** update update composer packages to non-major versions ([#2773](https://github.com/prezero/hermes-sf/issues/2773)) ([a5996ec](https://github.com/prezero/hermes-sf/commit/a5996eceec2c71292029e11b20168b7121394e20))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak:26.1.4 docker digest to cfbcd98 ([#2802](https://github.com/prezero/hermes-sf/issues/2802)) ([ac8a062](https://github.com/prezero/hermes-sf/commit/ac8a0624e6abb91115389213e3191f32031df31b))
* **deps:** update update local docker-compose env dependencies ([#2803](https://github.com/prezero/hermes-sf/issues/2803)) ([219c489](https://github.com/prezero/hermes-sf/commit/219c489f450248d2f5d16699c244f69d9d708e2a))


### Continuous Integration

* renovate adjustments for vcs lookup ([#2858](https://github.com/prezero/hermes-sf/issues/2858)) ([c7f6bd8](https://github.com/prezero/hermes-sf/commit/c7f6bd86357b7219def1d41f2083d9681b216e88))

## [4.194.0](https://github.com/prezero/hermes-sf/compare/v4.193.0...v4.194.0) (2025-04-10)


### Features

* **HERMES-3100:** Tour info files & additional information refactoring ([#2852](https://github.com/prezero/hermes-sf/issues/2852)) ([c36388e](https://github.com/prezero/hermes-sf/commit/c36388e80523a72d4b35de1e61af7b64ef8abfce))


### Bug Fixes

* **HERMES-3149:** changed status-source in description generation for task ([#2854](https://github.com/prezero/hermes-sf/issues/2854)) ([d6c5c8a](https://github.com/prezero/hermes-sf/commit/d6c5c8a955d9061e13eb482802ce590951364683))

## [4.193.0](https://github.com/prezero/hermes-sf/compare/v4.192.1...v4.193.0) (2025-04-10)


### Features

* **HERMES-3095:** added filter to ep ([#2845](https://github.com/prezero/hermes-sf/issues/2845)) ([ed884ec](https://github.com/prezero/hermes-sf/commit/ed884ec0c91e797bc18a3e5a549645ada5c06952))
* **HERMES-3113:** converting nl-signatures ([#2850](https://github.com/prezero/hermes-sf/issues/2850)) ([00cc508](https://github.com/prezero/hermes-sf/commit/00cc508353408cc98a76e1f832ff6f889b0fafa4))
* Update translations to version 99 ([#2853](https://github.com/prezero/hermes-sf/issues/2853)) ([f6641fa](https://github.com/prezero/hermes-sf/commit/f6641fa7f5d82358bd3693288dfb4b8400bdfd1e))


### Bug Fixes

* **HERMES-2976:** Adjust filtering to find tours spanning multiple days ([#2844](https://github.com/prezero/hermes-sf/issues/2844)) ([96c82ca](https://github.com/prezero/hermes-sf/commit/96c82ca5a76f132fbea9cdd5812f5ac8c353044d))
* **HERMES-3096:** customer info ([#2843](https://github.com/prezero/hermes-sf/issues/2843)) ([9e4c9e9](https://github.com/prezero/hermes-sf/commit/9e4c9e944874d1db973ee9e580de0ab968c8be69))

## [4.192.1](https://github.com/prezero/hermes-sf/compare/v4.192.0...v4.192.1) (2025-04-10)


### Bug Fixes

* Response event priorities ([#2848](https://github.com/prezero/hermes-sf/issues/2848)) ([912a3eb](https://github.com/prezero/hermes-sf/commit/912a3eb97527ecf625d3026c41fcec90cb8faae1))

## [4.192.0](https://github.com/prezero/hermes-sf/compare/v4.191.0...v4.192.0) (2025-04-09)


### Features

* translation update ([#2846](https://github.com/prezero/hermes-sf/issues/2846)) ([c6da1cb](https://github.com/prezero/hermes-sf/commit/c6da1cb80080cb916f9f8a4faa623fca40224a5e))

## [4.191.0](https://github.com/prezero/hermes-sf/compare/v4.190.0...v4.191.0) (2025-04-09)


### Features

* **HERMES-3014:** doc schema names ([#2840](https://github.com/prezero/hermes-sf/issues/2840)) ([e149c18](https://github.com/prezero/hermes-sf/commit/e149c182b9762bf8b7c1c04381e0d35bc98845f7))


### Bug Fixes

* **HERMES-3126:** mt wp info ([#2842](https://github.com/prezero/hermes-sf/issues/2842)) ([48f307d](https://github.com/prezero/hermes-sf/commit/48f307d50856ad53ccc190919d6420a5a1ffa0fa))

## [4.190.0](https://github.com/prezero/hermes-sf/compare/v4.189.0...v4.190.0) (2025-04-08)


### Features

* Update translations to version 97 ([#2838](https://github.com/prezero/hermes-sf/issues/2838)) ([9eebca7](https://github.com/prezero/hermes-sf/commit/9eebca7490e13caecf9df50e21c93f5530c250f6))


### Bug Fixes

* external add info kept ([#2839](https://github.com/prezero/hermes-sf/issues/2839)) ([961aaff](https://github.com/prezero/hermes-sf/commit/961aaff32c6bc07fe5e9a091764afb7213cd0cc3))
* Fixtures after device access changes ([#2836](https://github.com/prezero/hermes-sf/issues/2836)) ([e3d9b88](https://github.com/prezero/hermes-sf/commit/e3d9b88c984bd755f9f67699cab7ea45c0614c1b))

## [4.189.0](https://github.com/prezero/hermes-sf/compare/v4.188.0...v4.189.0) (2025-04-08)


### Features

* **HERMES-3063:** disposal materials ([#2829](https://github.com/prezero/hermes-sf/issues/2829)) ([dbc192a](https://github.com/prezero/hermes-sf/commit/dbc192af80bfac51052df46324790fed51ae07a3))

## [4.188.0](https://github.com/prezero/hermes-sf/compare/v4.187.0...v4.188.0) (2025-04-07)


### Features

* **HERMES-3079:** New Portal Device Access endpoint ([#2832](https://github.com/prezero/hermes-sf/issues/2832)) ([ffa6dce](https://github.com/prezero/hermes-sf/commit/ffa6dcecd9aa8e40719b85b1d959e62a97c68887))

## [4.187.0](https://github.com/prezero/hermes-sf/compare/v4.186.2...v4.187.0) (2025-04-07)


### Features

* **HERMES-3077:** Portal password reset trigger ([#2825](https://github.com/prezero/hermes-sf/issues/2825)) ([77739d0](https://github.com/prezero/hermes-sf/commit/77739d097e7fcb5ca4ced2534f2a057a84eb0297))

## [4.186.2](https://github.com/prezero/hermes-sf/compare/v4.186.1...v4.186.2) (2025-04-07)


### Bug Fixes

* document migration ([#2830](https://github.com/prezero/hermes-sf/issues/2830)) ([73b0de6](https://github.com/prezero/hermes-sf/commit/73b0de6bff4394fe6286e97ec89925aa8dd1bb92))

## [4.186.1](https://github.com/prezero/hermes-sf/compare/v4.186.0...v4.186.1) (2025-04-07)


### Bug Fixes

* **HERMES-3094:** Messaging fixes & modified at update fix ([#2827](https://github.com/prezero/hermes-sf/issues/2827)) ([c932037](https://github.com/prezero/hermes-sf/commit/c932037ade2e3382a894ffdc526edabd4b7f7dd7))

## [4.186.0](https://github.com/prezero/hermes-sf/compare/v4.185.0...v4.186.0) (2025-04-04)


### Features

* **HERMES-3079:** Document migration & tour documents & additional information ([#2821](https://github.com/prezero/hermes-sf/issues/2821)) ([6b680d1](https://github.com/prezero/hermes-sf/commit/6b680d1c99d9402f13e9b54360cc47b296a001c1))

## [4.185.0](https://github.com/prezero/hermes-sf/compare/v4.184.0...v4.185.0) (2025-04-04)


### Features

* added recipient string for app ([#2823](https://github.com/prezero/hermes-sf/issues/2823)) ([62691f5](https://github.com/prezero/hermes-sf/commit/62691f596b845cf3721e407a295bc96e2e2af3f0))

## [4.184.0](https://github.com/prezero/hermes-sf/compare/v4.183.0...v4.184.0) (2025-04-04)


### Features

* **HERMES-3074:** App Equipment list ep limit ([#2819](https://github.com/prezero/hermes-sf/issues/2819)) ([4dda601](https://github.com/prezero/hermes-sf/commit/4dda6016c46af43a5b4022b256ceb0107cd2f9c8))
* **HERMES-3087:** added recipient string for app ([#2822](https://github.com/prezero/hermes-sf/issues/2822)) ([d44e310](https://github.com/prezero/hermes-sf/commit/d44e310f4cbd5b418a41ebae296e098ccb9114f0))

## [4.183.0](https://github.com/prezero/hermes-sf/compare/v4.182.0...v4.183.0) (2025-04-03)


### Features

* **HERMES-3075:** Allow skipped from unassigned task ([#2817](https://github.com/prezero/hermes-sf/issues/2817)) ([303c593](https://github.com/prezero/hermes-sf/commit/303c593d369b0d4f28616ebb2ffc93f63a469a0c))
* Update lux int config ([#2816](https://github.com/prezero/hermes-sf/issues/2816)) ([ba1b693](https://github.com/prezero/hermes-sf/commit/ba1b6935c581ea0279ba4e591921f5f47dfcf38e))

## [4.182.0](https://github.com/prezero/hermes-sf/compare/v4.181.0...v4.182.0) (2025-04-03)


### Features

* es config update ([#2812](https://github.com/prezero/hermes-sf/issues/2812)) ([2ac2e88](https://github.com/prezero/hermes-sf/commit/2ac2e885fb261ce743b50aefdbdec49f19c9ab80))
* **HERMES-2080:** Centrifugo switch from camel case to snake_case ([#2807](https://github.com/prezero/hermes-sf/issues/2807)) ([53770d7](https://github.com/prezero/hermes-sf/commit/53770d78909fea3776d34e8657581b84a506a1eb))
* **HERMES-2924:** Block dako file upload if process is not in the ri… ([#2810](https://github.com/prezero/hermes-sf/issues/2810)) ([960ed33](https://github.com/prezero/hermes-sf/commit/960ed334bd0087bb43af3d9dd116c7b3a06fa392))
* **HERMES-3043:** Removing Lux Implementation (party) ([#2805](https://github.com/prezero/hermes-sf/issues/2805)) ([17e4bbe](https://github.com/prezero/hermes-sf/commit/17e4bbe2e7467d1b33b5efd7927ef35d9e6c2ccc))
* **HERMES-3062:** Mastertour and tracking changes ([#2811](https://github.com/prezero/hermes-sf/issues/2811)) ([fdc8c3b](https://github.com/prezero/hermes-sf/commit/fdc8c3beee773eac5f2c168f2a931421824283c4))
* **HERMES-3068:** dako abort transition ([#2815](https://github.com/prezero/hermes-sf/issues/2815)) ([910788f](https://github.com/prezero/hermes-sf/commit/910788f5fe4a1a0ad3b177c175188462543aa146))
* Update german config ([#2813](https://github.com/prezero/hermes-sf/issues/2813)) ([ac26e56](https://github.com/prezero/hermes-sf/commit/ac26e56f686c9eed3aabca2c41fe81541d7383a1))
* Update translations to version 96 ([#2814](https://github.com/prezero/hermes-sf/issues/2814)) ([732c45b](https://github.com/prezero/hermes-sf/commit/732c45b8cc17320dfc0121bcf833a679aab4d85a))


### Bug Fixes

* **HERMES-3303:** order doc tos spacing ([#2809](https://github.com/prezero/hermes-sf/issues/2809)) ([092c2ac](https://github.com/prezero/hermes-sf/commit/092c2acde77eb5d1a70beb856546403f0d9d8e4f))


### Tests

* fails in nl-tracking ([#2808](https://github.com/prezero/hermes-sf/issues/2808)) ([8855f77](https://github.com/prezero/hermes-sf/commit/8855f7710a5e89cac5804bcafe560e22e12d5e6e))

## [4.181.0](https://github.com/prezero/hermes-sf/compare/v4.180.2...v4.181.0) (2025-04-01)


### Features

* **HERMES-2815:** Mastertour template branch external id ([#2796](https://github.com/prezero/hermes-sf/issues/2796)) ([fec71c9](https://github.com/prezero/hermes-sf/commit/fec71c977ac792c6f2ebde84472e9c6b5bf855c8))

## [4.180.2](https://github.com/prezero/hermes-sf/compare/v4.180.1...v4.180.2) (2025-04-01)


### Bug Fixes

* using constatnt in expecation ([#2799](https://github.com/prezero/hermes-sf/issues/2799)) ([d48fa49](https://github.com/prezero/hermes-sf/commit/d48fa49f44e91ea77ecf9f92a28d97c896ce2957))

## [4.180.1](https://github.com/prezero/hermes-sf/compare/v4.180.0...v4.180.1) (2025-04-01)


### Bug Fixes

* shortened session-length ([#2797](https://github.com/prezero/hermes-sf/issues/2797)) ([b34e458](https://github.com/prezero/hermes-sf/commit/b34e458f6caa7b271664b593efe50478cc3b78ff))

## [4.180.0](https://github.com/prezero/hermes-sf/compare/v4.179.0...v4.180.0) (2025-04-01)


### Features

* **HERMES-3011:** Redesign message system, include send to equipments of given type in branch ([#2793](https://github.com/prezero/hermes-sf/issues/2793)) ([26f51fa](https://github.com/prezero/hermes-sf/commit/26f51fa84ddcc878c08b4eec6d2728b4c10f4464))
* **HERMES-3024:** changed filtering ([#2791](https://github.com/prezero/hermes-sf/issues/2791)) ([06e4eb1](https://github.com/prezero/hermes-sf/commit/06e4eb1d2784108a7cc25a291aefc5f555970c07))


### Bug Fixes

* Time calculations ([#2795](https://github.com/prezero/hermes-sf/issues/2795)) ([971d769](https://github.com/prezero/hermes-sf/commit/971d769d21e2b22e4c8f82aa4298e982cc5af193))

## [4.179.0](https://github.com/prezero/hermes-sf/compare/v4.178.0...v4.179.0) (2025-03-31)


### Features

* Update dutch config ([#2790](https://github.com/prezero/hermes-sf/issues/2790)) ([bca68f3](https://github.com/prezero/hermes-sf/commit/bca68f3de6c8491a9c7ee21cf6a4f024877dacd1))

## [4.178.0](https://github.com/prezero/hermes-sf/compare/v4.177.0...v4.178.0) (2025-03-31)


### Features

* **HERMES-2950:** dako wf complete skip ([#2785](https://github.com/prezero/hermes-sf/issues/2785)) ([c44a9ba](https://github.com/prezero/hermes-sf/commit/c44a9bae8d741ffbea8b9c27929814fbb920391f))

## [4.177.0](https://github.com/prezero/hermes-sf/compare/v4.176.0...v4.177.0) (2025-03-31)


### Features

* Update lux int config ([#2787](https://github.com/prezero/hermes-sf/issues/2787)) ([8a69a60](https://github.com/prezero/hermes-sf/commit/8a69a60e187539f2733cbb3d66e13cac6f2fbea6))

## [4.176.0](https://github.com/prezero/hermes-sf/compare/v4.175.0...v4.176.0) (2025-03-31)


### Features

* Fixture update ([#2784](https://github.com/prezero/hermes-sf/issues/2784)) ([d4c6e18](https://github.com/prezero/hermes-sf/commit/d4c6e18b35bdc938adcc5c1ac4c0f065a52eac89))

## [4.175.0](https://github.com/prezero/hermes-sf/compare/v4.174.1...v4.175.0) (2025-03-31)


### Features

* **HERMES-2318:** Portal equipment ep bugfix ([#2782](https://github.com/prezero/hermes-sf/issues/2782)) ([fd348e8](https://github.com/prezero/hermes-sf/commit/fd348e8bbaf501ab4f6c8ecb21e742fc40e7ba6a))


### Bug Fixes

* **HERMES-3026:** adding linebreak in text ([#2781](https://github.com/prezero/hermes-sf/issues/2781)) ([2545753](https://github.com/prezero/hermes-sf/commit/2545753d64aeb0d431367f93a534dfd0368cad85))

## [4.174.1](https://github.com/prezero/hermes-sf/compare/v4.174.0...v4.174.1) (2025-03-28)


### Bug Fixes

* German SAP Docs ([#2779](https://github.com/prezero/hermes-sf/issues/2779)) ([f143e4a](https://github.com/prezero/hermes-sf/commit/f143e4acfeee9b2aa885d7b5e5f3d7b0e9b8e5ea))

## [4.174.0](https://github.com/prezero/hermes-sf/compare/v4.173.0...v4.174.0) (2025-03-28)


### Features

* **HERMES-3009:** Messaging system changes ([#2778](https://github.com/prezero/hermes-sf/issues/2778)) ([7b456ac](https://github.com/prezero/hermes-sf/commit/7b456accf7e722ec855b2c6c63e1c8416dd31162))
* **HERMES-3015:** pdf format change ([#2772](https://github.com/prezero/hermes-sf/issues/2772)) ([312b70f](https://github.com/prezero/hermes-sf/commit/312b70f03cda3974c36b914a4720cb3ec7a744f2))
* **HERMES-3022:** helper command for debugging dako ([#2774](https://github.com/prezero/hermes-sf/issues/2774)) ([8bd1553](https://github.com/prezero/hermes-sf/commit/8bd1553d794f4d797a52430448595ac0bce4a098))
* Update lux international config ([#2776](https://github.com/prezero/hermes-sf/issues/2776)) ([296264b](https://github.com/prezero/hermes-sf/commit/296264b7307a217e9a0269551b64b5c893f57cff))

## [4.173.0](https://github.com/prezero/hermes-sf/compare/v4.172.0...v4.173.0) (2025-03-28)


### Features

* Update pz and lux config ([#2775](https://github.com/prezero/hermes-sf/issues/2775)) ([3d6dcbd](https://github.com/prezero/hermes-sf/commit/3d6dcbde38af481331237e404bd4214a5d9affed))
* Update pz config ([#2769](https://github.com/prezero/hermes-sf/issues/2769)) ([8a53214](https://github.com/prezero/hermes-sf/commit/8a53214416dbf2e3231d288b3c1584b1c5266353))
* Update translations to version 94 ([#2771](https://github.com/prezero/hermes-sf/issues/2771)) ([8a07106](https://github.com/prezero/hermes-sf/commit/8a07106f80301ec5ceaec4e452d870e9016a65ad))

## [4.172.0](https://github.com/prezero/hermes-sf/compare/v4.171.0...v4.172.0) (2025-03-27)


### Features

* Update translation fixtures to version 93 ([#2767](https://github.com/prezero/hermes-sf/issues/2767)) ([5e78b83](https://github.com/prezero/hermes-sf/commit/5e78b833a02565eb292828957e5303e690f8fce9))

## [4.171.0](https://github.com/prezero/hermes-sf/compare/v4.170.0...v4.171.0) (2025-03-27)


### Features

* **HERMES-2961:** Portal api staff list ([#2765](https://github.com/prezero/hermes-sf/issues/2765)) ([4f51758](https://github.com/prezero/hermes-sf/commit/4f5175838cd278b6d89e742d66e94a30c1b0e779))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.342.14 ([#2619](https://github.com/prezero/hermes-sf/issues/2619)) ([187f53d](https://github.com/prezero/hermes-sf/commit/187f53dff66313a880991917a68f3170ee5ac2bf))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.1.4 ([#2702](https://github.com/prezero/hermes-sf/issues/2702)) ([a7bdc62](https://github.com/prezero/hermes-sf/commit/a7bdc621addf084bb56e85c52cb1110ba21b8bcd))
* **deps:** update docker/login-action digest to 74a5d14 ([#2712](https://github.com/prezero/hermes-sf/issues/2712)) ([3700088](https://github.com/prezero/hermes-sf/commit/370008868a638b7488d492f49838db83ba39f9b2))
* **deps:** update quay.io/minio/minio:latest docker digest to 46b3009 ([#2698](https://github.com/prezero/hermes-sf/issues/2698)) ([1df239c](https://github.com/prezero/hermes-sf/commit/1df239c2b11c070233bf1b40437f7714f6c22565))

## [4.170.0](https://github.com/prezero/hermes-sf/compare/v4.169.0...v4.170.0) (2025-03-26)


### Features

* Adding portal schema validation in tests, improving portal & hermes documentation ([#2764](https://github.com/prezero/hermes-sf/issues/2764)) ([8acd8b0](https://github.com/prezero/hermes-sf/commit/8acd8b06ddc4015c6ce079b5b375ea3c5498420c))
* **HERMES-2960:** Portal ep Equipment Type ([#2756](https://github.com/prezero/hermes-sf/issues/2756)) ([c5e91b0](https://github.com/prezero/hermes-sf/commit/c5e91b0061e5a279e2ddfc4146d87e790e19b41d))

## [4.169.0](https://github.com/prezero/hermes-sf/compare/v4.168.0...v4.169.0) (2025-03-25)


### Features

* **HERMES-2925:** dako access country ([#2750](https://github.com/prezero/hermes-sf/issues/2750)) ([e6a1d3e](https://github.com/prezero/hermes-sf/commit/e6a1d3eaa03c07a2c8059194ec9d9a44aff8a04f))
* **HERMES-2974:** Extract Api Bundle ([#2760](https://github.com/prezero/hermes-sf/issues/2760)) ([04e46d1](https://github.com/prezero/hermes-sf/commit/04e46d1b22287f1f29cb20f5cd8bcba5340a5dc1))


### Bug Fixes

* Dockerfile ([f9f37ca](https://github.com/prezero/hermes-sf/commit/f9f37ca6936b9cdc807b4e231e44452fb8d80440))
* Dockerfile ([bf26f8d](https://github.com/prezero/hermes-sf/commit/bf26f8df0bc92c0b80136658692930365bf6ea91))
* Dockerfile ([11c7553](https://github.com/prezero/hermes-sf/commit/11c75539d6d3fdc59a32adebfd8c67a88deb324a))

## [4.168.0](https://github.com/prezero/hermes-sf/compare/v4.167.1...v4.168.0) (2025-03-25)


### Features

* **HERMES-2825:** Portal list ep mastertour progress ([#2749](https://github.com/prezero/hermes-sf/issues/2749)) ([56f9062](https://github.com/prezero/hermes-sf/commit/56f9062d1ea63e0af97642d95d15da5a71103703))


### Bug Fixes

* **HERMES-2982:** added test and fix ([#2752](https://github.com/prezero/hermes-sf/issues/2752)) ([cf4e9a8](https://github.com/prezero/hermes-sf/commit/cf4e9a84158ef95e9bc97ef7514a95258438d1a5))
* **HERMES-2984:** Adding dummy description to non-completed tasks in their session bookings ([#2759](https://github.com/prezero/hermes-sf/issues/2759)) ([09b38de](https://github.com/prezero/hermes-sf/commit/09b38de2bddcb95305b0a6b08d83318578c961c9))


### Tests

* **HERMES-2984:** test for reproduction of the error ([#2758](https://github.com/prezero/hermes-sf/issues/2758)) ([95e12bc](https://github.com/prezero/hermes-sf/commit/95e12bc9be7206c6cef30d309e7e922410fb098d))

## [4.167.1](https://github.com/prezero/hermes-sf/compare/v4.167.0...v4.167.1) (2025-03-24)


### Bug Fixes

* new dump based on current master ([#2754](https://github.com/prezero/hermes-sf/issues/2754)) ([a95ba1a](https://github.com/prezero/hermes-sf/commit/a95ba1a6db9f1a9474861b8919591672024aeee2))

## [4.167.0](https://github.com/prezero/hermes-sf/compare/v4.166.0...v4.167.0) (2025-03-24)


### Features

* **HERMES-2928:** Open api connected devices doc ([#2748](https://github.com/prezero/hermes-sf/issues/2748)) ([393b6c8](https://github.com/prezero/hermes-sf/commit/393b6c8f173a1b52521261f1b8156ba0e494d833))
* Update nl config ([#2747](https://github.com/prezero/hermes-sf/issues/2747)) ([548ef87](https://github.com/prezero/hermes-sf/commit/548ef8712e540a39e742a3e7fbf1d1460fa232bc))
* Update translations to version 92 ([#2753](https://github.com/prezero/hermes-sf/issues/2753)) ([b1957bc](https://github.com/prezero/hermes-sf/commit/b1957bc9926a29d671de63d377817a26beab3c91))

## [4.166.0](https://github.com/prezero/hermes-sf/compare/v4.165.0...v4.166.0) (2025-03-20)


### Features

* **HERMES-2357:** Taskgroup title in file label ([#2742](https://github.com/prezero/hermes-sf/issues/2742)) ([2d06602](https://github.com/prezero/hermes-sf/commit/2d0660215f0deaf92ebe240a5641b2555566c065))
* **HERMES-2926:** checking cards for expired ([#2746](https://github.com/prezero/hermes-sf/issues/2746)) ([3d6525d](https://github.com/prezero/hermes-sf/commit/3d6525da7012871f90d2aa6e5eae289689d583ac))
* **HERMES-2964:** Sap Ingest endpoints to API Bundle ([#2745](https://github.com/prezero/hermes-sf/issues/2745)) ([0c8f70b](https://github.com/prezero/hermes-sf/commit/0c8f70b182352131c79e5b77224ae08b0bbbbd4d))


### Bug Fixes

* **HERMES-2931:** nl message fail ([#2741](https://github.com/prezero/hermes-sf/issues/2741)) ([dd45d54](https://github.com/prezero/hermes-sf/commit/dd45d542674e7c9cb83366730322d31d0ba8a85c))


### Tests

* **HERMES-2776:** new dako-scenario for fail durinng process ([#2743](https://github.com/prezero/hermes-sf/issues/2743)) ([d40acb8](https://github.com/prezero/hermes-sf/commit/d40acb8b839d9bee5a42faf6b95000726814a0ef))

## [4.165.0](https://github.com/prezero/hermes-sf/compare/v4.164.0...v4.165.0) (2025-03-20)


### Features

* Add bluetooth dual connection type ([#2739](https://github.com/prezero/hermes-sf/issues/2739)) ([25b9f28](https://github.com/prezero/hermes-sf/commit/25b9f28a748f1feed22009667cca829105033ef3))
* **HERMES-2853:** Tracking and bookings portal endpoints restructuring ([#2705](https://github.com/prezero/hermes-sf/issues/2705)) ([9de8226](https://github.com/prezero/hermes-sf/commit/9de822677653290a7d946e16590a017f9160b921))
* **HERMES-2923:** dako file upload ([#2735](https://github.com/prezero/hermes-sf/issues/2735)) ([d6e5ddc](https://github.com/prezero/hermes-sf/commit/d6e5ddc4f804a9dc61c7136d029e8029cd6682cb))
* **HERMES-2928:** Portal Tour Endpoint ([#2738](https://github.com/prezero/hermes-sf/issues/2738)) ([9b1ed5a](https://github.com/prezero/hermes-sf/commit/9b1ed5afda9fa4454faa25c3b1794c7a1a61728f))
* **HERMES-2949:** dako file amount determination ([#2736](https://github.com/prezero/hermes-sf/issues/2736)) ([4b23f6b](https://github.com/prezero/hermes-sf/commit/4b23f6bbc12596c817b34d75988cb460fba747ca))

## [4.164.0](https://github.com/prezero/hermes-sf/compare/v4.163.0...v4.164.0) (2025-03-19)


### Features

* **HERMES-2913:** Bugfix Cest ([#2734](https://github.com/prezero/hermes-sf/issues/2734)) ([d30d0ef](https://github.com/prezero/hermes-sf/commit/d30d0efa219355504e2601bcac6aae7056cb62a4))
* **HERMES-2913:** Portal Equipment get Endpoints ([#2731](https://github.com/prezero/hermes-sf/issues/2731)) ([c588e01](https://github.com/prezero/hermes-sf/commit/c588e01baea6dfcf8949f85fa090e694e518d3ad))
* Update pz config ([#2732](https://github.com/prezero/hermes-sf/issues/2732)) ([048035f](https://github.com/prezero/hermes-sf/commit/048035f3438aa0d8b7484e7b91f7b734ab6af15a))


### Bug Fixes

* removal and setting of devices only if not null ([#2737](https://github.com/prezero/hermes-sf/issues/2737)) ([b5221b5](https://github.com/prezero/hermes-sf/commit/b5221b5e1a6275bf49fda4692c48c6d2bc93c87c))

## [4.163.0](https://github.com/prezero/hermes-sf/compare/v4.162.2...v4.163.0) (2025-03-18)


### Features

* **HERMES-2909:** dako process abort ([#2728](https://github.com/prezero/hermes-sf/issues/2728)) ([fc8e09c](https://github.com/prezero/hermes-sf/commit/fc8e09c6d6d0567f5471fea535e2ed81bc0e0bbb))
* **HERMES-2925:** new permission for dako-access in portal ([#2729](https://github.com/prezero/hermes-sf/issues/2729)) ([addcfb5](https://github.com/prezero/hermes-sf/commit/addcfb5733090ff6e7693ae137c1667c37e13a59))
* Update translations to version 91 ([#2726](https://github.com/prezero/hermes-sf/issues/2726)) ([c48324e](https://github.com/prezero/hermes-sf/commit/c48324e1d3df4c83168b586b0c35625a86dfe153))


### Bug Fixes

* **HERMES-2930:** datetime-format in pdf ([#2730](https://github.com/prezero/hermes-sf/issues/2730)) ([092b3e0](https://github.com/prezero/hermes-sf/commit/092b3e0c894b0a085bd63b57174083a5d831efd6))

## [4.162.2](https://github.com/prezero/hermes-sf/compare/v4.162.1...v4.162.2) (2025-03-18)


### Bug Fixes

* **HERMES-2912:** scale document print ([#2719](https://github.com/prezero/hermes-sf/issues/2719)) ([ea28821](https://github.com/prezero/hermes-sf/commit/ea288218f50dc5ead64a8b65de297672cd0f0fda))

## [4.162.1](https://github.com/prezero/hermes-sf/compare/v4.162.0...v4.162.1) (2025-03-18)


### Bug Fixes

* migration table-name ([#2722](https://github.com/prezero/hermes-sf/issues/2722)) ([3528db9](https://github.com/prezero/hermes-sf/commit/3528db90d4c250ee902f70f0aaaff942809cb4cd))
* Update welvaarts touch name ([#2724](https://github.com/prezero/hermes-sf/issues/2724)) ([1555227](https://github.com/prezero/hermes-sf/commit/15552270af01e42f7756880f04372debcaf91e53))

## [4.162.0](https://github.com/prezero/hermes-sf/compare/v4.161.0...v4.162.0) (2025-03-18)


### Features

* **HERMES-2847:** Tour EP External ID Insensitive ([#2718](https://github.com/prezero/hermes-sf/issues/2718)) ([bcad067](https://github.com/prezero/hermes-sf/commit/bcad06775f6c07864aebb4c84d6863dd407eb01a))

## [4.161.0](https://github.com/prezero/hermes-sf/compare/v4.160.0...v4.161.0) (2025-03-18)


### Features

* Add hardware type for welvaarts touch kranwaage ([#2720](https://github.com/prezero/hermes-sf/issues/2720)) ([56fbbb1](https://github.com/prezero/hermes-sf/commit/56fbbb146f2f5a7e518e378adf6a286f12ab39fa))
* **HERMES-2824:** Properly serialize json responses ([#2716](https://github.com/prezero/hermes-sf/issues/2716)) ([3b42b46](https://github.com/prezero/hermes-sf/commit/3b42b4614f6c58b6af6850390a28d5405a5d488f))
* **HERMES-2847:** Tour ep external id insensitive ([#2711](https://github.com/prezero/hermes-sf/issues/2711)) ([426690d](https://github.com/prezero/hermes-sf/commit/426690d071489c3cb84f629298cd2e6ffb4046b0))
* **HERMES-2850:** multi-file-flow ([#2715](https://github.com/prezero/hermes-sf/issues/2715)) ([1a99906](https://github.com/prezero/hermes-sf/commit/1a999069135d2f291b2fdfd7bd74f2152bc65b6d))
* **HERMES-2910:** Tour workflow processing order ([#2714](https://github.com/prezero/hermes-sf/issues/2714)) ([c7b7053](https://github.com/prezero/hermes-sf/commit/c7b7053272405136f8af9c25b80b2ed2f801ef4f))

## [4.160.0](https://github.com/prezero/hermes-sf/compare/v4.159.0...v4.160.0) (2025-03-14)


### Features

* **HERMES-2827:** Equipment ep 500 error branch ([#2706](https://github.com/prezero/hermes-sf/issues/2706)) ([6d5951e](https://github.com/prezero/hermes-sf/commit/6d5951e1cdbf788ef7facb87be0f0e0cb6fee796))


### Bug Fixes

* **HERMES-2823:** Response documentation fix ([#2710](https://github.com/prezero/hermes-sf/issues/2710)) ([3d332bf](https://github.com/prezero/hermes-sf/commit/3d332bf5bcdd6d0c758f20f2116e64e323571528))

## [4.159.0](https://github.com/prezero/hermes-sf/compare/v4.158.0...v4.159.0) (2025-03-14)


### Features

* **HERMES-2868:** dako string encoding ([#2707](https://github.com/prezero/hermes-sf/issues/2707)) ([de332b2](https://github.com/prezero/hermes-sf/commit/de332b2a5ed78d6e10e15ad5045670619e6015d7))

## [4.158.0](https://github.com/prezero/hermes-sf/compare/v4.157.0...v4.158.0) (2025-03-14)


### Features

* **HERMES-2404:** Portal EP Tracking Based on Role ([#2700](https://github.com/prezero/hermes-sf/issues/2700)) ([53fe358](https://github.com/prezero/hermes-sf/commit/53fe358e637aa344a1b0196164abe1fd31be3328))
* **HERMES-2814:** Control attribute tour config ([#2695](https://github.com/prezero/hermes-sf/issues/2695)) ([01daeab](https://github.com/prezero/hermes-sf/commit/01daeab8e196638bba0375b1b4223e8e92db0275))
* **HERMES-2856:** dako user process change ([#2704](https://github.com/prezero/hermes-sf/issues/2704)) ([763de64](https://github.com/prezero/hermes-sf/commit/763de64a7ed049812d356f7c434851dbf940ad86))
* Update NL Config ([#2703](https://github.com/prezero/hermes-sf/issues/2703)) ([e9540e5](https://github.com/prezero/hermes-sf/commit/e9540e52ee1b65984e3e636bf65d0ed876130c7e))

## [4.157.0](https://github.com/prezero/hermes-sf/compare/v4.156.0...v4.157.0) (2025-03-13)


### Features

* **HERMES-1941:** Broadcast message ([#2684](https://github.com/prezero/hermes-sf/issues/2684)) ([89988d0](https://github.com/prezero/hermes-sf/commit/89988d0bfef070c1bb67c566013e833da6ec9306))
* **HERMES-2774:** FTP-Upload ([#2689](https://github.com/prezero/hermes-sf/issues/2689)) ([7dfe52e](https://github.com/prezero/hermes-sf/commit/7dfe52e9aff25cbf2a395d801aacad7a40e9cacf))
* **HERMES-2811:** User password change flow ([#2693](https://github.com/prezero/hermes-sf/issues/2693)) ([e971c57](https://github.com/prezero/hermes-sf/commit/e971c579f62588c59aec06098cca19a8e72f3d67))
* Update translations ([#2690](https://github.com/prezero/hermes-sf/issues/2690)) ([a6df371](https://github.com/prezero/hermes-sf/commit/a6df371d04cdfdc9485ee05cc23f81c73cd4b55a))


### Bug Fixes

* fixed wrong task-selection ([#2697](https://github.com/prezero/hermes-sf/issues/2697)) ([e1fca8b](https://github.com/prezero/hermes-sf/commit/e1fca8bb229a7669b1c809e8e2af0f0b10297611))
* **HERMES-2831:** element-treatment ([#2694](https://github.com/prezero/hermes-sf/issues/2694)) ([31ac728](https://github.com/prezero/hermes-sf/commit/31ac728b7a7766fca8308d46839767e4b38bdea5))
* **HERMES-2846:** doc fixed, improved  test-data ([#2696](https://github.com/prezero/hermes-sf/issues/2696)) ([4cb7af3](https://github.com/prezero/hermes-sf/commit/4cb7af309ab12c45a8b7f85aeefc66121e07ec0d))


### Tests

* Fixing device message tests with no response from create endpoint ([#2692](https://github.com/prezero/hermes-sf/issues/2692)) ([b06a084](https://github.com/prezero/hermes-sf/commit/b06a0840471a9947d511f150adfb959758bc9418))

## [4.156.0](https://github.com/prezero/hermes-sf/compare/v4.155.0...v4.156.0) (2025-03-11)


### Features

* **HERMES-2775:** dako staff process ([#2683](https://github.com/prezero/hermes-sf/issues/2683)) ([94436db](https://github.com/prezero/hermes-sf/commit/94436db181c70749aa33f3a4a81dc21291c1d8fd))
* **HERMES-2784:** Translatable portal error message ([#2686](https://github.com/prezero/hermes-sf/issues/2686)) ([fc91b9d](https://github.com/prezero/hermes-sf/commit/fc91b9da710b83dca5593581bc9d621452b9330f))
* **HERMES-2817:** Taking nginx request start time as server time when adjusting timestamps ([#2685](https://github.com/prezero/hermes-sf/issues/2685)) ([22e799d](https://github.com/prezero/hermes-sf/commit/22e799d37374c62760fa4e6ac9ab8aacb0bdacf2))


### Miscellaneous Chores

* **deps:** Updating project libraries ([#2688](https://github.com/prezero/hermes-sf/issues/2688)) ([630b0f5](https://github.com/prezero/hermes-sf/commit/630b0f5be71395dcd76d553f5feb25a6d9262108))

## [4.155.0](https://github.com/prezero/hermes-sf/compare/v4.154.0...v4.155.0) (2025-03-11)


### Features

* **HERMES-2808:** added count-attribute ([#2680](https://github.com/prezero/hermes-sf/issues/2680)) ([2b0bab3](https://github.com/prezero/hermes-sf/commit/2b0bab39f12dff56b3f0d199c64603e01d12cf36))


### Bug Fixes

* **HERMES-2810:** changed names to camel-case ([#2682](https://github.com/prezero/hermes-sf/issues/2682)) ([9b70da9](https://github.com/prezero/hermes-sf/commit/9b70da95a7706948649d534be3f46530271e64cc))

## [4.154.0](https://github.com/prezero/hermes-sf/compare/v4.153.0...v4.154.0) (2025-03-10)


### Features

* **HERMES-2805:** equipment draft ([#2676](https://github.com/prezero/hermes-sf/issues/2676)) ([c749b1f](https://github.com/prezero/hermes-sf/commit/c749b1f6b60c82535a0e333d70a66fe434284e2f))


### Bug Fixes

* Spain order info files does not send SAP data, do not need to check for SAP disabled flag ([#2679](https://github.com/prezero/hermes-sf/issues/2679)) ([15616d9](https://github.com/prezero/hermes-sf/commit/15616d9dab304e4aa1e40ad7dd6bd2cdb3744b08))

## [4.153.0](https://github.com/prezero/hermes-sf/compare/v4.152.0...v4.153.0) (2025-03-10)


### Features

* **HERMES-2736:** Filter mastertour templates by name and external ID with full search index ([#2675](https://github.com/prezero/hermes-sf/issues/2675)) ([c1ee809](https://github.com/prezero/hermes-sf/commit/c1ee80988b6c05ea0258e6ddb2114b10609031a1))

## [4.152.0](https://github.com/prezero/hermes-sf/compare/v4.151.0...v4.152.0) (2025-03-10)


### Features

* **HERMES-2793:** Master tour external ID handling ([#2673](https://github.com/prezero/hermes-sf/issues/2673)) ([5b5e6c0](https://github.com/prezero/hermes-sf/commit/5b5e6c0684d2decbd248b5938c497cf2212b10a8))

## [4.151.0](https://github.com/prezero/hermes-sf/compare/v4.150.0...v4.151.0) (2025-03-10)


### Features

* config update ([#2671](https://github.com/prezero/hermes-sf/issues/2671)) ([ea4ffbc](https://github.com/prezero/hermes-sf/commit/ea4ffbc00735aa617a960a3b86eb53d6ad364f8d))
* **HERMES-2787:** Faq Update Bug ([#2669](https://github.com/prezero/hermes-sf/issues/2669)) ([ad9af83](https://github.com/prezero/hermes-sf/commit/ad9af83df851197e916b9ab18c4d3edd305b7e47))

## [4.150.0](https://github.com/prezero/hermes-sf/compare/v4.149.0...v4.150.0) (2025-03-07)


### Features

* **HERMES-2773:** added field, included in test ([#2666](https://github.com/prezero/hermes-sf/issues/2666)) ([dc9b2cf](https://github.com/prezero/hermes-sf/commit/dc9b2cfd458e80ba8d92e34e51bef285fbbd5f8d))
* **HERMES-2776:** dako error scenarios ([#2667](https://github.com/prezero/hermes-sf/issues/2667)) ([de6e989](https://github.com/prezero/hermes-sf/commit/de6e989fd44badfd600009da53eb556a6a7ab7d3))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.1.3 ([#2647](https://github.com/prezero/hermes-sf/issues/2647)) ([326b5b8](https://github.com/prezero/hermes-sf/commit/326b5b864ce28c79c8f5e237f25ac7f069a5cacd))
* **deps:** update update github actions ([#2635](https://github.com/prezero/hermes-sf/issues/2635)) ([df2ad3d](https://github.com/prezero/hermes-sf/commit/df2ad3dc38decff6097f907983a4c7c77c099e83))
* **deps:** update update local docker-compose env dependencies ([#2629](https://github.com/prezero/hermes-sf/issues/2629)) ([b1f40e3](https://github.com/prezero/hermes-sf/commit/b1f40e3b45a23f8bf289319ffc0ae510bee89bf7))
* **deps:** Updating project libraries ([36d50a5](https://github.com/prezero/hermes-sf/commit/36d50a538b2956b17d3aabad02c04223123ade75))

## [4.149.0](https://github.com/prezero/hermes-sf/compare/v4.148.0...v4.149.0) (2025-03-06)


### Features

* **HERMES-2576:** Add sending of trackrecordings to sap for netherlands ([#2661](https://github.com/prezero/hermes-sf/issues/2661)) ([a7a3401](https://github.com/prezero/hermes-sf/commit/a7a3401d0d0eb64e0fed7be934428678ab6a356f))
* **HERMES-2679:** Data protection notice ([#2663](https://github.com/prezero/hermes-sf/issues/2663)) ([318c9cb](https://github.com/prezero/hermes-sf/commit/318c9cbbe0d891ccb18cdfebf3896d67ab92943c))
* **HERMES-2681:** dako process start ([#2642](https://github.com/prezero/hermes-sf/issues/2642)) ([e4c54e3](https://github.com/prezero/hermes-sf/commit/e4c54e383e2383fb94bec9755c4458705cdb19c0))
* **HERMES-2737:** Master tour process coordinates in portal ([#2665](https://github.com/prezero/hermes-sf/issues/2665)) ([fda8f5f](https://github.com/prezero/hermes-sf/commit/fda8f5fa61f4efb55c17db6ff28bb629925907b6))

## [4.148.0](https://github.com/prezero/hermes-sf/compare/v4.147.0...v4.148.0) (2025-03-06)


### Features

* **HERMES-2665:** Equipment endpoint changes ([#2653](https://github.com/prezero/hermes-sf/issues/2653)) ([9a932ee](https://github.com/prezero/hermes-sf/commit/9a932eea699b8f0f94fa6aa1a09ef044b1feef73))

## [4.147.0](https://github.com/prezero/hermes-sf/compare/v4.146.3...v4.147.0) (2025-03-05)


### Features

* **HERMES-2738:** Netherlands PDF ([#2659](https://github.com/prezero/hermes-sf/issues/2659)) ([61adffc](https://github.com/prezero/hermes-sf/commit/61adffc61f5e63a911f6bd64c3dbfb724804bd4c))
* Update translations to version 87 ([#2657](https://github.com/prezero/hermes-sf/issues/2657)) ([e738b2c](https://github.com/prezero/hermes-sf/commit/e738b2c81b2d94b26d7a76f4d456877b640b4ff9))


### Bug Fixes

* fixed length assert ([#2660](https://github.com/prezero/hermes-sf/issues/2660)) ([2151b8b](https://github.com/prezero/hermes-sf/commit/2151b8b24847afa5b2725d382ce570d6cb899c75))
* release notes updating ([56a5b70](https://github.com/prezero/hermes-sf/commit/56a5b70294e62298eb704494b24133f6ed0dbb06))

## [4.146.3](https://github.com/prezero/hermes-sf/compare/v4.146.2...v4.146.3) (2025-03-04)


### Bug Fixes

* **HERMES-2734:** faq iso empty ([#2654](https://github.com/prezero/hermes-sf/issues/2654)) ([5e44c97](https://github.com/prezero/hermes-sf/commit/5e44c97a88a21b6e965017db7b866db325d88462))

## [4.146.2](https://github.com/prezero/hermes-sf/compare/v4.146.1...v4.146.2) (2025-03-04)


### Bug Fixes

* **HERMES-2654:** Last equipment session fix ([#2655](https://github.com/prezero/hermes-sf/issues/2655)) ([479705d](https://github.com/prezero/hermes-sf/commit/479705dfe13f6e2a9517244d4eb76972f26a96f1))
* **HERMES-2717:** Fixing calling async tour end on session cleanup ([#2651](https://github.com/prezero/hermes-sf/issues/2651)) ([c2633ac](https://github.com/prezero/hermes-sf/commit/c2633ac4bc71a5af2a8174880acdc94b46fd23d6))

## [4.146.1](https://github.com/prezero/hermes-sf/compare/v4.146.0...v4.146.1) (2025-03-03)


### Bug Fixes

* fixed mime-type ([#2649](https://github.com/prezero/hermes-sf/issues/2649)) ([061d800](https://github.com/prezero/hermes-sf/commit/061d8008c4f74bbce80fd13e7c786e3808d6f0e5))

## [4.146.0](https://github.com/prezero/hermes-sf/compare/v4.145.0...v4.146.0) (2025-03-03)


### Features

* **HERMES-2667:** Update welvaarts hardware types ([#2645](https://github.com/prezero/hermes-sf/issues/2645)) ([ef5918b](https://github.com/prezero/hermes-sf/commit/ef5918b6f1c08d50a25eb5d7b5dad2409a7665aa))
* **HERMES-2706:** Bugfix NL Upload Client ([#2648](https://github.com/prezero/hermes-sf/issues/2648)) ([7dc01c6](https://github.com/prezero/hermes-sf/commit/7dc01c6a197545841b43b49f7fcefff5a0af2cd2))
* Moving to self hosted runner, adding action-lint ([c94a27d](https://github.com/prezero/hermes-sf/commit/c94a27d97b05051ba0d18278ec20372230e10ac9))

## [4.145.0](https://github.com/prezero/hermes-sf/compare/v4.144.0...v4.145.0) (2025-02-28)


### Features

* config and translation update ([#2641](https://github.com/prezero/hermes-sf/issues/2641)) ([4993b48](https://github.com/prezero/hermes-sf/commit/4993b4845c623320cf2825b74c56ea6c621a455a))
* **HERMES-2662:** dako company ([#2636](https://github.com/prezero/hermes-sf/issues/2636)) ([fb6006f](https://github.com/prezero/hermes-sf/commit/fb6006ff4d340a7aa682d4602989db5a1e606489))
* **HERMES-2667:** Add new hardware type for welvaarts delta 10 ([#2644](https://github.com/prezero/hermes-sf/issues/2644)) ([4605261](https://github.com/prezero/hermes-sf/commit/4605261a657bc207af97b5b7bc48956fe3d755ba))


### Bug Fixes

* database dev dump ([#2643](https://github.com/prezero/hermes-sf/issues/2643)) ([d22f0bf](https://github.com/prezero/hermes-sf/commit/d22f0bfb89e8e3db10d6e038fb769ac19f78ad2a))


### Miscellaneous Chores

* **deps:** Updating project libraries ([4d5d023](https://github.com/prezero/hermes-sf/commit/4d5d0238056e91b938c204b02061bd51006146fb))

## [4.144.0](https://github.com/prezero/hermes-sf/compare/v4.143.0...v4.144.0) (2025-02-27)


### Features

* **HERMES-2672:** changed endpoint ([#2638](https://github.com/prezero/hermes-sf/issues/2638)) ([bb65174](https://github.com/prezero/hermes-sf/commit/bb651741c4c5f0caecb48e5128f116298d1a3c97))

## [4.143.0](https://github.com/prezero/hermes-sf/compare/v4.142.0...v4.143.0) (2025-02-27)


### Features

* **HERMES-2549:** Meta data image upload ([#2631](https://github.com/prezero/hermes-sf/issues/2631)) ([57b9263](https://github.com/prezero/hermes-sf/commit/57b9263d46366c24f86a7c34a0f77b537f7bd1aa))
* **HERMES-2644:** order document scale ([#2628](https://github.com/prezero/hermes-sf/issues/2628)) ([e1e0092](https://github.com/prezero/hermes-sf/commit/e1e0092bc0bfbe54b0339af853f0af7be0f3805a))
* **HERMES-2649:** changed subject to text ([#2625](https://github.com/prezero/hermes-sf/issues/2625)) ([9284e97](https://github.com/prezero/hermes-sf/commit/9284e977a8a6684255f7e2422c83ee1cf3e8025a))

## [4.142.0](https://github.com/prezero/hermes-sf/compare/v4.141.0...v4.142.0) (2025-02-26)


### Features

* **HERMES-2574:** Dev deployment updated ([36be9ad](https://github.com/prezero/hermes-sf/commit/36be9ad74651a557a0e2344594b1adc8e5bcc541))
* **HERMES-2574:** e2e and prod deployments ([33ae88d](https://github.com/prezero/hermes-sf/commit/33ae88d3165fa72748aefebe723c417234ac4a9b))

## [4.141.0](https://github.com/prezero/hermes-sf/compare/v4.140.0...v4.141.0) (2025-02-26)


### Features

* **HERMES-2574:** QA Deployment ([ab3aecd](https://github.com/prezero/hermes-sf/commit/ab3aecd35262d1c1340ddaca9b28bd1600716e08))

## [4.140.0](https://github.com/prezero/hermes-sf/compare/v4.139.0...v4.140.0) (2025-02-26)


### Features

* **HERMES-2574:** Deploy to new env ([f9e7465](https://github.com/prezero/hermes-sf/commit/f9e74653830b1374721fdb31a1e0f5a595de4a96))
* **HERMES-2574:** QA Deployment ([158f6f7](https://github.com/prezero/hermes-sf/commit/158f6f7d94124788a84982003a46ff3592b90b29))
* **HERMES-2574:** Run tests in the correct namespace ([da888f0](https://github.com/prezero/hermes-sf/commit/da888f027016d79dc6458a03205aa6567f9e4e2e))


### Bug Fixes

* image digest ([f5c55ab](https://github.com/prezero/hermes-sf/commit/f5c55ab466d9e30349dd6539fec913d15ca19a32))
* migration set empty value before not null ([#2627](https://github.com/prezero/hermes-sf/issues/2627)) ([bfcb7a8](https://github.com/prezero/hermes-sf/commit/bfcb7a8d6f7786571d6521770eecc27ed39d507b))
* new image ([#2630](https://github.com/prezero/hermes-sf/issues/2630)) ([e609426](https://github.com/prezero/hermes-sf/commit/e609426a44f978d77b8a11d48aa3accabe2d4561))

## [4.139.0](https://github.com/prezero/hermes-sf/compare/v4.138.0...v4.139.0) (2025-02-25)


### Features

* **HERMES-1937:** Add description to booking portal endpoint ([#2617](https://github.com/prezero/hermes-sf/issues/2617)) ([64dde48](https://github.com/prezero/hermes-sf/commit/64dde48d7f9272ba1a51140d12f54c77213a581c))

## [4.138.0](https://github.com/prezero/hermes-sf/compare/v4.137.1...v4.138.0) (2025-02-25)


### Features

* **HERMES-2622:** multi add service ([#2622](https://github.com/prezero/hermes-sf/issues/2622)) ([cc2833f](https://github.com/prezero/hermes-sf/commit/cc2833fc3dd0d32f897682be0b382cf33bf5e94e))

## [4.137.1](https://github.com/prezero/hermes-sf/compare/v4.137.0...v4.137.1) (2025-02-24)


### Miscellaneous Chores

* **deps:** update update prezero docker images to v4.9.4 ([#2620](https://github.com/prezero/hermes-sf/issues/2620)) ([4ee7531](https://github.com/prezero/hermes-sf/commit/4ee7531e0f1a1a1fa004b89dfe564c19b9f49b5a))


### Tests

* fixed old order test ([#2618](https://github.com/prezero/hermes-sf/issues/2618)) ([3fb38da](https://github.com/prezero/hermes-sf/commit/3fb38da2ab31835e6f598ecdaa9a2e7dd07f8813))

## [4.137.0](https://github.com/prezero/hermes-sf/compare/v4.136.0...v4.137.0) (2025-02-21)


### Features

* **HERMES-2617:** Available filter in equipments ([#2612](https://github.com/prezero/hermes-sf/issues/2612)) ([05d0178](https://github.com/prezero/hermes-sf/commit/05d01781808d8b542b474f424e1f673cb9cd724e))
* **HERMES-965:** Booking value objects ([#2508](https://github.com/prezero/hermes-sf/issues/2508)) ([2fde9b2](https://github.com/prezero/hermes-sf/commit/2fde9b2bc8992ea6d15a2edce2bfab052c425894))


### Bug Fixes

* **deps:** update update composer packages to non-major versions ([#2601](https://github.com/prezero/hermes-sf/issues/2601)) ([855074e](https://github.com/prezero/hermes-sf/commit/855074e4d451b6462f4c5ca4519fb1a21845c279))
* **HERMES-2621:** add service no input elements ([#2616](https://github.com/prezero/hermes-sf/issues/2616)) ([f2c3a2c](https://github.com/prezero/hermes-sf/commit/f2c3a2c387d5a6f317e537805891adf8079da378))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak:26.1.2 docker digest to f7fbc4e ([#2591](https://github.com/prezero/hermes-sf/issues/2591)) ([942e0eb](https://github.com/prezero/hermes-sf/commit/942e0ebf240f0e3b1a3e39ea296ac45b22bf8a8f))
* **deps:** update docker/build-push-action digest to 0adf995 ([#2614](https://github.com/prezero/hermes-sf/issues/2614)) ([441e8fd](https://github.com/prezero/hermes-sf/commit/441e8fd6b0d5824e3bd59c4f1deb227c99f7ae12))
* **deps:** update update local docker-compose env dependencies ([#2600](https://github.com/prezero/hermes-sf/issues/2600)) ([a151a04](https://github.com/prezero/hermes-sf/commit/a151a04e7a7ee530c062e81f71a4cb46c5f72a0a))
* **deps:** update update prezero docker images to v4.9.3 ([#2615](https://github.com/prezero/hermes-sf/issues/2615)) ([f51c58f](https://github.com/prezero/hermes-sf/commit/f51c58f65c73a7662e4cddefa3b34202a7fa3ae6))
* **deps:** Updating renovate config ([208c900](https://github.com/prezero/hermes-sf/commit/208c900c023c30e73dd6f1f706b7920fb1bf89d2))
* **deps:** Updating renovate config ([4271b58](https://github.com/prezero/hermes-sf/commit/4271b58705dd5086bca8136407ef5b652f5fea0a))
* **deps:** Updating renovate config ([4e29143](https://github.com/prezero/hermes-sf/commit/4e291439e4458028fcf4606958f6e8b0264390a5))
* **deps:** Updating renovate config ([f1c84d8](https://github.com/prezero/hermes-sf/commit/f1c84d87afd2ecff258483f1f8537b7e5917d131))

## [4.136.0](https://github.com/prezero/hermes-sf/compare/v4.135.0...v4.136.0) (2025-02-20)


### Features

* pz config and translation update ([#2608](https://github.com/prezero/hermes-sf/issues/2608)) ([ae6111b](https://github.com/prezero/hermes-sf/commit/ae6111bf0b2b06cffaae561b6c269a1b10d3b397))

## [4.135.0](https://github.com/prezero/hermes-sf/compare/v4.134.0...v4.135.0) (2025-02-20)


### Features

* **HERMES-2341:** Portal Endpoint Suffix ([#2593](https://github.com/prezero/hermes-sf/issues/2593)) ([9f6dfa9](https://github.com/prezero/hermes-sf/commit/9f6dfa921d1daad2b44f67cd5cce59d2536ae41f))
* **HERMES-2429:** Add and edit equipment from portal, branch security checks optimization ([#2597](https://github.com/prezero/hermes-sf/issues/2597)) ([f596f30](https://github.com/prezero/hermes-sf/commit/f596f308beb50bba45bbe36743cd524fac2afaff))


### Bug Fixes

* **HERMES-2597:** action time gap ([#2606](https://github.com/prezero/hermes-sf/issues/2606)) ([42a2508](https://github.com/prezero/hermes-sf/commit/42a2508acb8032ac40587e7f917c8a2866ff5e87))


### Miscellaneous Chores

* **deps:** Updating renovate config ([0fbeec2](https://github.com/prezero/hermes-sf/commit/0fbeec27601d28ade3999ef1289f4ccfc580209d))
* **deps:** Updating renovate config ([80abe84](https://github.com/prezero/hermes-sf/commit/80abe84ed480d83ee4c739d61d66b6c8fd9762a8))

## [4.134.0](https://github.com/prezero/hermes-sf/compare/v4.133.0...v4.134.0) (2025-02-19)


### Features

* Update pz config ([#2602](https://github.com/prezero/hermes-sf/issues/2602)) ([7dfa704](https://github.com/prezero/hermes-sf/commit/7dfa704b9a595d91034415162a72f0045957004b))


### Bug Fixes

* **HERMES-2608:** Remove blanks in translation tags ([#2598](https://github.com/prezero/hermes-sf/issues/2598)) ([f2ab27c](https://github.com/prezero/hermes-sf/commit/f2ab27c995f49b00233af12e8ac7768e07d43c36))


### Miscellaneous Chores

* **deps:** Updating renovate config ([30bc0c0](https://github.com/prezero/hermes-sf/commit/30bc0c0374f7863e749d30d58b240e859029a1df))
* **deps:** Updating renovate config, adding grouping ([ba2b3dd](https://github.com/prezero/hermes-sf/commit/ba2b3dd510b16c57c0721d096165d58e17f93263))

## [4.133.0](https://github.com/prezero/hermes-sf/compare/v4.132.0...v4.133.0) (2025-02-19)


### Features

* **HERMES-2585:** Sort tours on endpoint ([#2588](https://github.com/prezero/hermes-sf/issues/2588)) ([435ba6f](https://github.com/prezero/hermes-sf/commit/435ba6fdf39ca37aa6e209eecd90313240680aef))
* Update nl config ([#2596](https://github.com/prezero/hermes-sf/issues/2596)) ([59d534f](https://github.com/prezero/hermes-sf/commit/59d534fa96a0d960342caa86c34cc52d958b3117))
* Update pz config ([#2589](https://github.com/prezero/hermes-sf/issues/2589)) ([f855a2a](https://github.com/prezero/hermes-sf/commit/f855a2a6f3c5f1db7b2ba331f77e209e610f8882))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.339.15 ([d963008](https://github.com/prezero/hermes-sf/commit/d9630080219727bfd2401479513e789ad8cef03f))
* **deps:** update dependency nelmio/api-doc-bundle to v4.37.1 ([f17970d](https://github.com/prezero/hermes-sf/commit/f17970d5ccbc06ca74c16d35490b858ee7372bc6))
* **deps:** update dependency twig/twig to v3.20.0 ([3541954](https://github.com/prezero/hermes-sf/commit/35419544bcf3586b62a079142b34f183ad69f69a))


### Miscellaneous Chores

* **deps:** update atmoz/sftp:alpine docker digest to 9879db5 ([8c8d1f0](https://github.com/prezero/hermes-sf/commit/8c8d1f087110479a418cffbb1f00b3e18ae86097))
* **deps:** update bitnami/keycloak:26.1.2 docker digest to 0546f31 ([098fe69](https://github.com/prezero/hermes-sf/commit/098fe69c51e7b602e52c9190f7ff232b7069d2b8))
* **deps:** update caddy:2.9.1-alpine docker digest to b4e3952 ([bc461a2](https://github.com/prezero/hermes-sf/commit/bc461a27278b1404952ff67d27da443dfc5a9037))
* **deps:** update datalust/seq:latest docker digest to f0153b0 ([4cc989a](https://github.com/prezero/hermes-sf/commit/4cc989ac4fc42e8bea6efa382ec30db3fa88193b))
* **deps:** update dependency phpat/phpat to v0.11.3 ([3d809a2](https://github.com/prezero/hermes-sf/commit/3d809a22b2d2f012573b370a38526e6215e5741c))
* **deps:** update postgres:16-alpine3.21 docker digest to 97a14a1 ([22d1540](https://github.com/prezero/hermes-sf/commit/22d1540bca0f9779410b1fe95004179f50f696cd))
* **deps:** Updating base images ([57ae67f](https://github.com/prezero/hermes-sf/commit/57ae67f257cac8e30cb6cf16ea3ef39613ec1e64))
* **deps:** Updating dependencies ([8b5fea5](https://github.com/prezero/hermes-sf/commit/8b5fea520714820870017ebb47ca460dac30b30a))
* **deps:** Updating dependencies ([66debe2](https://github.com/prezero/hermes-sf/commit/66debe2ff57b876b7153b68cd98820281953f0c5))

## [4.132.0](https://github.com/prezero/hermes-sf/compare/v4.131.0...v4.132.0) (2025-02-18)


### Features

* **HERMES-2582:** Remove v2 Namespace ([#2581](https://github.com/prezero/hermes-sf/issues/2581)) ([9994715](https://github.com/prezero/hermes-sf/commit/9994715e1c0824d807bd048eb25267b52a9b2fc2))
* Update es fixtures ([#2584](https://github.com/prezero/hermes-sf/issues/2584)) ([9b25aa8](https://github.com/prezero/hermes-sf/commit/9b25aa82eca32b6c024a6d895b65e05c8018ec81))
* Update pz/es configs and translations ([#2587](https://github.com/prezero/hermes-sf/issues/2587)) ([b4b8f8b](https://github.com/prezero/hermes-sf/commit/b4b8f8b2bba33dfb603f8e21677ecec6abb9e71d))

## [4.131.0](https://github.com/prezero/hermes-sf/compare/v4.130.0...v4.131.0) (2025-02-17)


### Features

* Adding query parameters with support for multiple values ([#2582](https://github.com/prezero/hermes-sf/issues/2582)) ([c641d3e](https://github.com/prezero/hermes-sf/commit/c641d3edaa9cbd27be338ff8e3b5d36dff9eac0c))
* **HERMES-2584:** endpoint and test ([#2585](https://github.com/prezero/hermes-sf/issues/2585)) ([9292c97](https://github.com/prezero/hermes-sf/commit/9292c9722f3b2ffc5c75ab8702a90647c4d421d1))

## [4.130.0](https://github.com/prezero/hermes-sf/compare/v4.129.0...v4.130.0) (2025-02-17)


### Features

* nl config update ([#2575](https://github.com/prezero/hermes-sf/issues/2575)) ([5793912](https://github.com/prezero/hermes-sf/commit/5793912c727510215001d4aeceabb59b48ec5241))
* Update pz config ([#2578](https://github.com/prezero/hermes-sf/issues/2578)) ([642451a](https://github.com/prezero/hermes-sf/commit/642451ab4395bbe0258ad19463ec98389caa9cd6))


### Bug Fixes

* **HERMES-2581:** Time correction fix for async processing ([#2580](https://github.com/prezero/hermes-sf/issues/2580)) ([72222fc](https://github.com/prezero/hermes-sf/commit/72222fc476eabefacc91f06cedf71b9ec44e8205))

## [4.129.0](https://github.com/prezero/hermes-sf/compare/v4.128.0...v4.129.0) (2025-02-13)


### Features

* Update es config ([9b7e4fb](https://github.com/prezero/hermes-sf/commit/9b7e4fb4d545ad51b42a5408470c1745b64a7957))
* Update german config ([aab52f8](https://github.com/prezero/hermes-sf/commit/aab52f8655406420240784208704f316f5aca477))

## [4.128.0](https://github.com/prezero/hermes-sf/compare/v4.127.0...v4.128.0) (2025-02-13)


### Features

* **HERMES-2546:** Async tour end event processing from SAP ([#2567](https://github.com/prezero/hermes-sf/issues/2567)) ([39dd801](https://github.com/prezero/hermes-sf/commit/39dd801cfef22c1a2489bd68037ec0f2eb5ff624))
* Update translations to version 82 ([#2569](https://github.com/prezero/hermes-sf/issues/2569)) ([b0bb16e](https://github.com/prezero/hermes-sf/commit/b0bb16e4bfa116956578d2b8ebccabf3ef0a4b36))


### Bug Fixes

* Debug log for IOT ([6fa66dc](https://github.com/prezero/hermes-sf/commit/6fa66dc3849817158bdd5cd718b56cb1fa181a64))
* **deps:** update dependency aws/aws-sdk-php to v3.339.12 ([8d22a75](https://github.com/prezero/hermes-sf/commit/8d22a75f4a1d02ac8300fcbbed9509310cd5a3b6))
* **deps:** update dependency nelmio/api-doc-bundle to v4.36.2 ([7068b8b](https://github.com/prezero/hermes-sf/commit/7068b8b1a792b38ab6f76c3792073db68be084a0))
* **deps:** update dependency phpstan/phpdoc-parser to v2.0.1 ([ea2e959](https://github.com/prezero/hermes-sf/commit/ea2e959009e6c951511821622d9c07d269e6ca5d))
* **deps:** update dependency twig/extra-bundle to v3.20.0 ([9dbd482](https://github.com/prezero/hermes-sf/commit/9dbd482b6e9e5eaae9e51e228628171445bfdaf2))
* **HERMES-2559:** IOT fixes ([#2568](https://github.com/prezero/hermes-sf/issues/2568)) ([c982663](https://github.com/prezero/hermes-sf/commit/c982663410bff173800f6862aeae72f4bc8919a2))


### Miscellaneous Chores

* **deps:** update atmoz/sftp:alpine docker digest to c7c505d ([be29d88](https://github.com/prezero/hermes-sf/commit/be29d88f06cc9b2166fe50810b582f661e056579))
* **deps:** update bitnami/keycloak docker tag to v26.1.2 ([531a298](https://github.com/prezero/hermes-sf/commit/531a298b96af342c8cd5e541729b8191088e222b))
* **deps:** update dependency captainhook/captainhook to v5.25.0 ([fd70fc3](https://github.com/prezero/hermes-sf/commit/fd70fc30f974b7b3d0adf36e725ac51171979de7))
* **deps:** update dependency phpstan/phpstan to v2.1.5 ([106474e](https://github.com/prezero/hermes-sf/commit/106474eef41ddaa0859b7be923f7051b0a9d8167))
* **deps:** update rabbitmq:4.0-management docker digest to c2f5153 ([a9e2ad7](https://github.com/prezero/hermes-sf/commit/a9e2ad7ced04e6488d775404ffa33f405a201bf5))

## [4.127.0](https://github.com/prezero/hermes-sf/compare/v4.126.0...v4.127.0) (2025-02-12)


### Features

* Translation update ([#2554](https://github.com/prezero/hermes-sf/issues/2554)) ([c166e54](https://github.com/prezero/hermes-sf/commit/c166e5434441ecc3a4e9de9c579aebc765fee5a5))
* Update pz config ([#2561](https://github.com/prezero/hermes-sf/issues/2561)) ([2567194](https://github.com/prezero/hermes-sf/commit/25671948356c736c7a56593701f13e989c7ab341))

## [4.126.0](https://github.com/prezero/hermes-sf/compare/v4.125.0...v4.126.0) (2025-02-12)


### Features

* **HERMES-2547:** IOT Feature Flag + bug fixes to allow retrying via the dead letter queue ([#2559](https://github.com/prezero/hermes-sf/issues/2559)) ([bc38365](https://github.com/prezero/hermes-sf/commit/bc38365c6a57ced852b169f40fddd5a87a2894cf))
* Update pz config ([#2557](https://github.com/prezero/hermes-sf/issues/2557)) ([086bbe0](https://github.com/prezero/hermes-sf/commit/086bbe09cac866bd816e277445da5ac514567028))

## [4.125.0](https://github.com/prezero/hermes-sf/compare/v4.124.1...v4.125.0) (2025-02-11)


### Features

* Update pz translations and config ([#2552](https://github.com/prezero/hermes-sf/issues/2552)) ([3c8495d](https://github.com/prezero/hermes-sf/commit/3c8495dba3f4600c688584a7b8dac50331e16ae2))

## [4.124.1](https://github.com/prezero/hermes-sf/compare/v4.124.0...v4.124.1) (2025-02-10)


### Bug Fixes

* **HERMES-2502:** Fix portal country endpoint ([#2550](https://github.com/prezero/hermes-sf/issues/2550)) ([8663477](https://github.com/prezero/hermes-sf/commit/86634774802da9642d9ccbf9b21a5a0b3b97b6a2))

## [4.124.0](https://github.com/prezero/hermes-sf/compare/v4.123.0...v4.124.0) (2025-02-10)


### Features

* **HERMES-2405:** Annihilate v1 ([#2539](https://github.com/prezero/hermes-sf/issues/2539)) ([c00cbb4](https://github.com/prezero/hermes-sf/commit/c00cbb487a7f9422d7bea96246fba73c3ddbdc1e))
* **HERMES-2421:** SIAM users no password update ([#2533](https://github.com/prezero/hermes-sf/issues/2533)) ([98fbb33](https://github.com/prezero/hermes-sf/commit/98fbb3396bbf5a2c96283f896b6267169fbc6252))
* **HERMES-2485:** Order obsolete workflow change ([#2548](https://github.com/prezero/hermes-sf/issues/2548)) ([401a880](https://github.com/prezero/hermes-sf/commit/401a880d1652e951b9f1777c653bda3476ce3630))


### Bug Fixes

* Adding message class on async consumer logs ([87b9d64](https://github.com/prezero/hermes-sf/commit/87b9d6491c5eede928c8e7ceea6bd0f55d049a06))
* **deps:** update dependency aws/aws-sdk-php to v3.339.7 ([#2527](https://github.com/prezero/hermes-sf/issues/2527)) ([a6417dc](https://github.com/prezero/hermes-sf/commit/a6417dc0bb22ac63b747ba32b6df1f83a70a8458))
* **deps:** update dependency aws/aws-sdk-php to v3.339.8 ([#2536](https://github.com/prezero/hermes-sf/issues/2536)) ([f2851eb](https://github.com/prezero/hermes-sf/commit/f2851eb6fd4f7d72134ced4b8c1d1a017b05ac53))
* **deps:** update dependency doctrine/orm to v3.3.2 ([#2529](https://github.com/prezero/hermes-sf/issues/2529)) ([d5dfacd](https://github.com/prezero/hermes-sf/commit/d5dfacda857ab756851178e6a0f4fdaa032f0214))
* **HERMES-2455:** Fixing microtime recording of equipment positions ([adf0e3f](https://github.com/prezero/hermes-sf/commit/adf0e3f775faefd12b56dbcd09304f6dcc595e85))


### Miscellaneous Chores

* **deps:** Pin dependency version ([b4acf5f](https://github.com/prezero/hermes-sf/commit/b4acf5f3dce6528d0bf775d2b19f3a63cb4ac77d))
* **deps:** update atmoz/sftp:alpine docker digest to 3084918 ([#2549](https://github.com/prezero/hermes-sf/issues/2549)) ([1e27d05](https://github.com/prezero/hermes-sf/commit/1e27d05ad75f842de9318996b066157ece04c83e))
* **deps:** update atmoz/sftp:alpine docker digest to 4c1e9ea ([#2544](https://github.com/prezero/hermes-sf/issues/2544)) ([8449570](https://github.com/prezero/hermes-sf/commit/8449570ad64c77cdf0df8f277c0f935eadead192))
* **deps:** update bitnami/keycloak docker tag to v26.1.1 ([#2531](https://github.com/prezero/hermes-sf/issues/2531)) ([697ca2c](https://github.com/prezero/hermes-sf/commit/697ca2cdeee28a4fe0a4cf632ea8970dd373e46e))
* **deps:** update dependency phpstan/phpstan to v2.1.4 ([#2540](https://github.com/prezero/hermes-sf/issues/2540)) ([7a5f003](https://github.com/prezero/hermes-sf/commit/7a5f003bf1b20267d987a82a1910f7180ff21516))
* **deps:** update dependency phpunit/phpunit to v11.5.7 ([#2534](https://github.com/prezero/hermes-sf/issues/2534)) ([7011be4](https://github.com/prezero/hermes-sf/commit/7011be48a0f3d4e3e7b3608691b7e4cf505fbb43))
* **deps:** update dependency rector/rector to v2.0.8 ([#2535](https://github.com/prezero/hermes-sf/issues/2535)) ([d45930f](https://github.com/prezero/hermes-sf/commit/d45930f42b87a9dc86689b10e9a8f2ea0233d3f1))
* **deps:** update dependency rector/rector to v2.0.9 ([#2547](https://github.com/prezero/hermes-sf/issues/2547)) ([adf5b7c](https://github.com/prezero/hermes-sf/commit/adf5b7cf5f322a03ab1ab67dd9b5d38de0535558))
* **deps:** update postgres:16-alpine3.21 docker digest to 1d04b9b ([#2528](https://github.com/prezero/hermes-sf/issues/2528)) ([aa9d78a](https://github.com/prezero/hermes-sf/commit/aa9d78a36e54e4085e248ee4091ed25ffb7b6460))
* **deps:** update quay.io/minio/minio:latest docker digest to 640c227 ([#2545](https://github.com/prezero/hermes-sf/issues/2545)) ([7ce9690](https://github.com/prezero/hermes-sf/commit/7ce96905d77923d0154a7d447d3e8d2b0dfb9b51))
* **deps:** update rabbitmq:4.0-management docker digest to 697da87 ([#2537](https://github.com/prezero/hermes-sf/issues/2537)) ([0753a29](https://github.com/prezero/hermes-sf/commit/0753a2907b5b74a26bc8e3b3450abefc7985b0b0))
* **deps:** update rabbitmq:4.0-management docker digest to f8e04e1 ([#2526](https://github.com/prezero/hermes-sf/issues/2526)) ([995bf02](https://github.com/prezero/hermes-sf/commit/995bf02c86f80e67e87edc4e52ec52cd65950513))
* **deps:** Updating dependencies ([6f73452](https://github.com/prezero/hermes-sf/commit/6f734524027e09be11d58e7a9e935e86c521aa46))

## [4.123.0](https://github.com/prezero/hermes-sf/compare/v4.122.1...v4.123.0) (2025-02-04)


### Features

* Hermes translations v78 ([b7efcb0](https://github.com/prezero/hermes-sf/commit/b7efcb0fd66db6bef8d4ad4c87f59e0bef948b17))
* Updating local keycloak image to include Hermes App client ([a8b95bd](https://github.com/prezero/hermes-sf/commit/a8b95bdff99917c19b19d2a1ed628dded2ef7f36))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.339.5 ([#2521](https://github.com/prezero/hermes-sf/issues/2521)) ([4e60c00](https://github.com/prezero/hermes-sf/commit/4e60c00d3beb16fe7582534cbf22d57ea4976360))


### Miscellaneous Chores

* **deps:** update quay.io/minio/minio:latest docker digest to a62e44a ([#2522](https://github.com/prezero/hermes-sf/issues/2522)) ([bcd5b88](https://github.com/prezero/hermes-sf/commit/bcd5b880885c6aea8b1de73be2e4e24726783d03))
* **deps:** update rabbitmq:4.0-management docker digest to 1848f46 ([#2525](https://github.com/prezero/hermes-sf/issues/2525)) ([6e89505](https://github.com/prezero/hermes-sf/commit/6e895058d217cbbdbb4cdf1ad3bd87c33b3f20a6))
* **deps:** update rabbitmq:4.0-management docker digest to cc4c4f7 ([#2523](https://github.com/prezero/hermes-sf/issues/2523)) ([82ff67f](https://github.com/prezero/hermes-sf/commit/82ff67fe15f474c73969a5e610b36d329dedb5b1))

## [4.122.1](https://github.com/prezero/hermes-sf/compare/v4.122.0...v4.122.1) (2025-02-03)


### Bug Fixes

* DB new dump + openapi reference fix ([619f046](https://github.com/prezero/hermes-sf/commit/619f046f35215fc9429270d5760d5a616126eb24))
* Remove manual fixture reload, they are reloaded automatically when tests start ([1d617c7](https://github.com/prezero/hermes-sf/commit/1d617c7c856aaa0c72ec6b8985bb109b2fc07e5a))

## [4.122.0](https://github.com/prezero/hermes-sf/compare/v4.121.0...v4.122.0) (2025-02-03)


### Features

* Update translations ([#2518](https://github.com/prezero/hermes-sf/issues/2518)) ([7de46dd](https://github.com/prezero/hermes-sf/commit/7de46dd78c02cbe192815d5aa299ec3161c166bb))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.339.4 ([#2515](https://github.com/prezero/hermes-sf/issues/2515)) ([bca30de](https://github.com/prezero/hermes-sf/commit/bca30defdd56825978e90830c0bef28ec9bc297b))
* **deps:** update dependency intervention/image to v3.11.1 ([#2516](https://github.com/prezero/hermes-sf/issues/2516)) ([931039d](https://github.com/prezero/hermes-sf/commit/931039ddc7ed094bdd2358ad3adb241839355640))


### Miscellaneous Chores

* **deps:** update centrifugo/centrifugo:v6 docker digest to fd25ae7 ([#2517](https://github.com/prezero/hermes-sf/issues/2517)) ([8c1c06f](https://github.com/prezero/hermes-sf/commit/8c1c06f06046930029e3ee13beeddd26573ff321))

## [4.121.0](https://github.com/prezero/hermes-sf/compare/v4.120.0...v4.121.0) (2025-01-31)


### Features

* **HERMES-2400:** Open API Spec validation v2 ([#2509](https://github.com/prezero/hermes-sf/issues/2509)) ([ccd50cb](https://github.com/prezero/hermes-sf/commit/ccd50cbb4827c96e658fb207789038e83b0e9ec5))
* Update nl config ([#2513](https://github.com/prezero/hermes-sf/issues/2513)) ([e75f374](https://github.com/prezero/hermes-sf/commit/e75f3748d9c40e2be715b3f077cfcab15e78fd7e))
* Update translations to version 76 ([#2506](https://github.com/prezero/hermes-sf/issues/2506)) ([468d7af](https://github.com/prezero/hermes-sf/commit/468d7af87f17a61d72abcbbc5171394c5e96d2d5))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.339.2 ([#2504](https://github.com/prezero/hermes-sf/issues/2504)) ([99114b4](https://github.com/prezero/hermes-sf/commit/99114b42370471f29cb273c3ebc6f37f82ddab7f))
* **deps:** update dependency aws/aws-sdk-php to v3.339.3 ([#2511](https://github.com/prezero/hermes-sf/issues/2511)) ([1d5dec0](https://github.com/prezero/hermes-sf/commit/1d5dec0c7347bf56df03e07a0e12f125c7c3b48e))
* removing deprecation messages from parallel tests ([#2514](https://github.com/prezero/hermes-sf/issues/2514)) ([781b55a](https://github.com/prezero/hermes-sf/commit/781b55a4b735a7888629fa7cf6e1fc3a90640c4a))


### Miscellaneous Chores

* **deps:** update dependency friendsofphp/php-cs-fixer to v3.68.4 ([#2483](https://github.com/prezero/hermes-sf/issues/2483)) ([8e962dd](https://github.com/prezero/hermes-sf/commit/8e962ddc6373516f992f3445c1a2fabd7781af99))
* **deps:** update dependency friendsofphp/php-cs-fixer to v3.68.5 ([#2507](https://github.com/prezero/hermes-sf/issues/2507)) ([c215f8c](https://github.com/prezero/hermes-sf/commit/c215f8cdfa48c42aefa2483f2a82d7a5be003944))
* **deps:** update dependency phpunit/phpunit to v11.5.5 ([#2503](https://github.com/prezero/hermes-sf/issues/2503)) ([02009c8](https://github.com/prezero/hermes-sf/commit/02009c89d13ab41b1ffd9a61b1788cbfe5e2f471))
* **deps:** update dependency phpunit/phpunit to v11.5.6 ([#2512](https://github.com/prezero/hermes-sf/issues/2512)) ([c5bb44a](https://github.com/prezero/hermes-sf/commit/c5bb44aaae85798ab07102faa9e266d35e37b5fc))


### Tests

* **HERMES-2057:** parallel test fails ([#2510](https://github.com/prezero/hermes-sf/issues/2510)) ([15b85a7](https://github.com/prezero/hermes-sf/commit/15b85a7237aa9bb8eecaa51cf672377d201265cf))

## [4.120.0](https://github.com/prezero/hermes-sf/compare/v4.119.0...v4.120.0) (2025-01-29)


### Features

* **HERMES-2326:** Migrate to v2 and Cleanup v1 ([#2491](https://github.com/prezero/hermes-sf/issues/2491)) ([32cb01a](https://github.com/prezero/hermes-sf/commit/32cb01a7848909275df701ba0f60859d140cdb9f))
* **HERMES-2391:** Adding schema to all tables, so we can use pgbouncer in transaction mode ([#2500](https://github.com/prezero/hermes-sf/issues/2500)) ([b1ccfda](https://github.com/prezero/hermes-sf/commit/b1ccfdadfe7aee46cfd08b25646ea92e30e60417))
* **HERMES-2394:** Reverting tour completion back to sync mode due to app issues ([#2501](https://github.com/prezero/hermes-sf/issues/2501)) ([1e349ee](https://github.com/prezero/hermes-sf/commit/1e349ee1a5402ba6686baed8f498f22153398281))
* **HERMES-2395:** Rename road name to address ([#2502](https://github.com/prezero/hermes-sf/issues/2502)) ([5d0864b](https://github.com/prezero/hermes-sf/commit/5d0864b3b3bf0dec00f423ac2f6bbe070b0bb3cd))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.339.1 ([#2477](https://github.com/prezero/hermes-sf/issues/2477)) ([254cee8](https://github.com/prezero/hermes-sf/commit/254cee8c15f3b0eda823d7cd4ed24c0bdd1879a3))
* **deps:** update dependency blackfire/php-sdk to v2.5.7 ([#2490](https://github.com/prezero/hermes-sf/issues/2490)) ([7eb052d](https://github.com/prezero/hermes-sf/commit/7eb052d383752764ef5a2938e760811bba250c94))
* **deps:** update dependency twig/extra-bundle to v3.19.0 ([#2497](https://github.com/prezero/hermes-sf/issues/2497)) ([9f13e88](https://github.com/prezero/hermes-sf/commit/9f13e888306cbda5df487b65eefd8d98466d42fa))
* **deps:** update dependency twig/twig to v3.19.0 ([#2498](https://github.com/prezero/hermes-sf/issues/2498)) ([0c3d365](https://github.com/prezero/hermes-sf/commit/0c3d365df2b95dc311b8ff5fbb045685ff38395c))
* **deps:** update symfony packages to v7.2.3 ([#2499](https://github.com/prezero/hermes-sf/issues/2499)) ([fec5587](https://github.com/prezero/hermes-sf/commit/fec5587a0ced5fa61f1c9a15c3e1a761863ababf))


### Miscellaneous Chores

* code style refactoring, rector ([#2496](https://github.com/prezero/hermes-sf/issues/2496)) ([e077b8f](https://github.com/prezero/hermes-sf/commit/e077b8f6d45029c5147c540eb9f83060c7c060ac))
* **deps:** update bitnami/keycloak:26.1.0 docker digest to 70528fa ([#2494](https://github.com/prezero/hermes-sf/issues/2494)) ([55ce2e7](https://github.com/prezero/hermes-sf/commit/55ce2e7c9ccc7d9969facede5427fdc9ab9d9b5d))
* **deps:** update blackfire/blackfire:2 docker digest to bc34d45 ([#2489](https://github.com/prezero/hermes-sf/issues/2489)) ([a8d60c9](https://github.com/prezero/hermes-sf/commit/a8d60c94332660f42645516020213df97b43e417))
* **deps:** update dependency phpunit/phpunit to v11.5.4 ([#2492](https://github.com/prezero/hermes-sf/issues/2492)) ([282018d](https://github.com/prezero/hermes-sf/commit/282018d87bf38e9e8b9bce2ea685da2ea855070a))
* **deps:** update rabbitmq:4.0-management docker digest to f100517 ([#2495](https://github.com/prezero/hermes-sf/issues/2495)) ([4b057b6](https://github.com/prezero/hermes-sf/commit/4b057b6e47798df363b0a19666e10bab22a49a78))

## [4.119.0](https://github.com/prezero/hermes-sf/compare/v4.118.0...v4.119.0) (2025-01-28)


### Features

* **HERMES-2379:** Do not send containers to IOT if location is null ([#2487](https://github.com/prezero/hermes-sf/issues/2487)) ([688ff2b](https://github.com/prezero/hermes-sf/commit/688ff2b347953fee433f60dd6c768994684afc0a))


### Bug Fixes

* **HERMES-2387:** Fix missing logs ([#2488](https://github.com/prezero/hermes-sf/issues/2488)) ([7f08fce](https://github.com/prezero/hermes-sf/commit/7f08fce2a28e1a5952de6314d7e41ceadbba7bd4))
* increasing argocd rollout timeout ([8d06c08](https://github.com/prezero/hermes-sf/commit/8d06c0824b13ab542ebfc5c44372b38f39b737de))


### Miscellaneous Chores

* **deps:** pin postgres docker tag to aba1fab ([#2484](https://github.com/prezero/hermes-sf/issues/2484)) ([9010ac1](https://github.com/prezero/hermes-sf/commit/9010ac129b794270065506d1cab58843525de8c5))
* **deps:** Updating dependencies ([#2486](https://github.com/prezero/hermes-sf/issues/2486)) ([56b55d4](https://github.com/prezero/hermes-sf/commit/56b55d4a381de9333faaad9f452fc17acd07a7d7))
* **deps:** Updating postgres local to v16 ([60dd847](https://github.com/prezero/hermes-sf/commit/60dd847b27d57c30081e7ad2961415f9d3d39e46))
* Updating kubectl on actions & database version in config ([#2481](https://github.com/prezero/hermes-sf/issues/2481)) ([b719514](https://github.com/prezero/hermes-sf/commit/b71951499a516e47244d1a1074b9b5c8da705fa0))


### Tests

* **HERMES-2057:** parallel fails ([#2485](https://github.com/prezero/hermes-sf/issues/2485)) ([ebb2059](https://github.com/prezero/hermes-sf/commit/ebb2059dc270c3b197aac996c39ceeec1f9b1358))

## [4.118.0](https://github.com/prezero/hermes-sf/compare/v4.117.0...v4.118.0) (2025-01-27)


### Features

* **HERMES-2361:** apiv2 cests part 6 ([#2471](https://github.com/prezero/hermes-sf/issues/2471)) ([ef8cd27](https://github.com/prezero/hermes-sf/commit/ef8cd27638589dd1b5831dc26098b21a6883cd10))


### Miscellaneous Chores

* **deps:** Updating base image, postgre client & dump ([#2480](https://github.com/prezero/hermes-sf/issues/2480)) ([e820a61](https://github.com/prezero/hermes-sf/commit/e820a6125386010ce91a5e0ea86d5b182bd70964))

## [4.117.0](https://github.com/prezero/hermes-sf/compare/v4.116.0...v4.117.0) (2025-01-27)


### Features

* **HERMES-2240:** Tour end and termination to async processing ([#2454](https://github.com/prezero/hermes-sf/issues/2454)) ([c1d6181](https://github.com/prezero/hermes-sf/commit/c1d61819687c0cfbd2c50ff4aa798428826ce1a3))
* **HERMES-2307:** apiv2 cests part 1 ([#2465](https://github.com/prezero/hermes-sf/issues/2465)) ([18034b0](https://github.com/prezero/hermes-sf/commit/18034b04c2bb17d89c8e0c548fcea408fee4b664))
* **HERMES-2319:** Enabling renovate, updating libraries ([#2441](https://github.com/prezero/hermes-sf/issues/2441)) ([c245d3c](https://github.com/prezero/hermes-sf/commit/c245d3cbadfbb1c8aec6e4f74bc761103b1fd8ef))
* **HERMES-2343:** test migration v2 part4 ([#2467](https://github.com/prezero/hermes-sf/issues/2467)) ([53d8f3f](https://github.com/prezero/hermes-sf/commit/53d8f3f7d01b8da4c6c55f770ae669142112aafd))
* **HERMES-2360:** IOT Updated DTO ([#2472](https://github.com/prezero/hermes-sf/issues/2472)) ([90665f0](https://github.com/prezero/hermes-sf/commit/90665f081723e6b64d0bbe65609e534202c63c88))


### Bug Fixes

* Add status filter for tour to tracking list endpoint ([#2448](https://github.com/prezero/hermes-sf/issues/2448)) ([68b719b](https://github.com/prezero/hermes-sf/commit/68b719be5cfc9fd6c54d196157e5f4fee566ebca))
* centrifugo v6 config ([8db2b4f](https://github.com/prezero/hermes-sf/commit/8db2b4f2c3ed321ec82a0401366c09806bbd9e45))
* composer updated ([#2475](https://github.com/prezero/hermes-sf/issues/2475)) ([7d8d1de](https://github.com/prezero/hermes-sf/commit/7d8d1de29e13428db44ccc589749207f411b131e))
* **deps:** update dependency aws/aws-sdk-php to v3.338.2 ([#2446](https://github.com/prezero/hermes-sf/issues/2446)) ([d436e96](https://github.com/prezero/hermes-sf/commit/d436e962fdac8e0250d4ec41816d58fa33160f95))
* **deps:** update dependency phpoffice/phpspreadsheet to v3.9.0 ([#2473](https://github.com/prezero/hermes-sf/issues/2473)) ([fd4c78a](https://github.com/prezero/hermes-sf/commit/fd4c78acdbb6bda18e7541335c8e809d5ac60180))
* **deps:** update dependency tecnickcom/tcpdf to v6.8.2 ([#2474](https://github.com/prezero/hermes-sf/issues/2474)) ([d00482e](https://github.com/prezero/hermes-sf/commit/d00482e694faa0c1ff5d43aace4c3a3408b33e24))
* **HERMES-2348:** Remove obsolete tours from portal tour list endpoint ([#2466](https://github.com/prezero/hermes-sf/issues/2466)) ([40af6cf](https://github.com/prezero/hermes-sf/commit/40af6cf87fc9f87616246f09ed0857883aaba396))
* library versions ([#2445](https://github.com/prezero/hermes-sf/issues/2445)) ([06bea1d](https://github.com/prezero/hermes-sf/commit/06bea1d3123059e259e6d61e081384c3c0724d0e))


### Miscellaneous Chores

* **deps:** pin dependencies ([#2442](https://github.com/prezero/hermes-sf/issues/2442)) ([e7e6912](https://github.com/prezero/hermes-sf/commit/e7e69126f4ef00c086cf7e3a0f9925f2d41b48d1))
* **deps:** update actions/checkout action to v4 ([#2452](https://github.com/prezero/hermes-sf/issues/2452)) ([11e424f](https://github.com/prezero/hermes-sf/commit/11e424f28a3d20431a1900851af23a286aa9f655))
* **deps:** update azure/setup-kubectl action to v4 ([#2455](https://github.com/prezero/hermes-sf/issues/2455)) ([35cfc81](https://github.com/prezero/hermes-sf/commit/35cfc81f5ae387d2876406dfa496828ed1e0b6eb))
* **deps:** update caddy docker tag to v2.9.1 ([#2447](https://github.com/prezero/hermes-sf/issues/2447)) ([aec309e](https://github.com/prezero/hermes-sf/commit/aec309e88e47563aebcc4d782d78df18d66afcf9))
* **deps:** update centrifugo/centrifugo docker tag to v6 ([#2456](https://github.com/prezero/hermes-sf/issues/2456)) ([fd07eb9](https://github.com/prezero/hermes-sf/commit/fd07eb91315d7bedc06ac83f6d17167432efbfa8))
* **deps:** update clowdhaus/argo-cd-action action to v2.3.0 ([#2449](https://github.com/prezero/hermes-sf/issues/2449)) ([7c3bf52](https://github.com/prezero/hermes-sf/commit/7c3bf528a1df9689bcbcc36725ba72f611239f49))
* **deps:** update docker/build-push-action action to v6 ([#2457](https://github.com/prezero/hermes-sf/issues/2457)) ([418345c](https://github.com/prezero/hermes-sf/commit/418345c18b46003b4c54b8719a60ac1189f7e330))
* **deps:** update docker/build-push-action digest to ca877d9 ([#2470](https://github.com/prezero/hermes-sf/issues/2470)) ([22d3922](https://github.com/prezero/hermes-sf/commit/22d3922963f6317c429a160959c16c250fe23b45))
* **deps:** update docker/login-action action to v3 ([#2458](https://github.com/prezero/hermes-sf/issues/2458)) ([db0369d](https://github.com/prezero/hermes-sf/commit/db0369d51219e7dbfa02880d50fdcbdd476a81f9))
* **deps:** update docker/metadata-action action to v5 ([#2459](https://github.com/prezero/hermes-sf/issues/2459)) ([33453ce](https://github.com/prezero/hermes-sf/commit/33453ce36e492052071360a2f7e5a273b79e311b))
* **deps:** update jtalk/url-health-check-action action to v4 ([#2460](https://github.com/prezero/hermes-sf/issues/2460)) ([9749e44](https://github.com/prezero/hermes-sf/commit/9749e4424b196c2056acb0c95c047181938298d1))
* **deps:** update postgres docker tag to v15.10 ([#2450](https://github.com/prezero/hermes-sf/issues/2450)) ([224b04d](https://github.com/prezero/hermes-sf/commit/224b04d0b7a906e99b9468d9958aa64be0cf506d))
* **deps:** update rabbitmq docker tag to v3.13 ([#2451](https://github.com/prezero/hermes-sf/issues/2451)) ([d17c469](https://github.com/prezero/hermes-sf/commit/d17c469108acae9c644d00131662aa3873fdb0a7))
* **deps:** update rabbitmq docker tag to v4 ([#2463](https://github.com/prezero/hermes-sf/issues/2463)) ([9880a27](https://github.com/prezero/hermes-sf/commit/9880a27c4efa10cd9f17d752b7c6ce44ad373305))
* **deps:** update redis docker tag to v7 ([#2464](https://github.com/prezero/hermes-sf/issues/2464)) ([a1bb3e6](https://github.com/prezero/hermes-sf/commit/a1bb3e6aa0f1185351226111ca9d7f8434f08800))
* **deps:** Updating kubectl version to match cluster version ([e29a582](https://github.com/prezero/hermes-sf/commit/e29a582aff94f8e4d9e028b81dfa7d8d00b02ce5))


### Tests

* **HERMES-2057:** parallel tests fails ([#2438](https://github.com/prezero/hermes-sf/issues/2438)) ([043c0b9](https://github.com/prezero/hermes-sf/commit/043c0b90c27f249b3601df8ff11e1e90ba7ac29e))
* **HERMES-2321:** order-tests for v2 ([#2440](https://github.com/prezero/hermes-sf/issues/2440)) ([8901b86](https://github.com/prezero/hermes-sf/commit/8901b8683c7f7cf5232f2be53100251edb51c352))
* **HERMES-2325:** v2 tests interruptions ([#2453](https://github.com/prezero/hermes-sf/issues/2453)) ([04d47df](https://github.com/prezero/hermes-sf/commit/04d47dfa91be6796e93406441c61941034e26332))
* **HERMES-2345:** sapgermany v2 ([#2468](https://github.com/prezero/hermes-sf/issues/2468)) ([147f304](https://github.com/prezero/hermes-sf/commit/147f304511af6961ad30f49900b7532e2f9b4c59))
* **HERMES-2362:** moved portal-tests to V2 ([#2469](https://github.com/prezero/hermes-sf/issues/2469)) ([8a183df](https://github.com/prezero/hermes-sf/commit/8a183df8bd71aabdd56da3ee0affbda1eecf443b))


### Continuous Integration

* Added docker access to renovate config ([#2476](https://github.com/prezero/hermes-sf/issues/2476)) ([feb28be](https://github.com/prezero/hermes-sf/commit/feb28bedb6e69f03700732c3889a3a8b5ab338e6))

## [4.116.0](https://github.com/prezero/hermes-sf/compare/v4.115.0...v4.116.0) (2025-01-21)


### Features

* Order mastertour import by sequence field ([#2435](https://github.com/prezero/hermes-sf/issues/2435)) ([976a801](https://github.com/prezero/hermes-sf/commit/976a8017787e6ccdd7967e7f2a0dc5c04b69345a))
* Update translations and pz config ([#2437](https://github.com/prezero/hermes-sf/issues/2437)) ([a093003](https://github.com/prezero/hermes-sf/commit/a0930034df470a32a9ec6b2347f9014240e1a7a7))

## [4.115.0](https://github.com/prezero/hermes-sf/compare/v4.114.0...v4.115.0) (2025-01-20)


### Features

* **HERMES-2295:** sorting order-notes by creation ([#2433](https://github.com/prezero/hermes-sf/issues/2433)) ([34a77c1](https://github.com/prezero/hermes-sf/commit/34a77c17a20450b4458c9da2b4a10ef53a46ea30))

## [4.114.0](https://github.com/prezero/hermes-sf/compare/v4.113.0...v4.114.0) (2025-01-20)


### Features

* **HERMES-2273:** IOT Pre Check ([#2429](https://github.com/prezero/hermes-sf/issues/2429)) ([92951f3](https://github.com/prezero/hermes-sf/commit/92951f3d68b06f90f303ff74b3861d147556bcfc))
* Update pz config and translations ([#2432](https://github.com/prezero/hermes-sf/issues/2432)) ([cfa3bdb](https://github.com/prezero/hermes-sf/commit/cfa3bdb0c093be04c5f3eb8af637a594b6f16088))


### Tests

* **HERMES-2227:** tests keep deviceid in cache ([#2431](https://github.com/prezero/hermes-sf/issues/2431)) ([204531c](https://github.com/prezero/hermes-sf/commit/204531cecff0c8104d9c7c36688e07e6a0d3b221))

## [4.113.0](https://github.com/prezero/hermes-sf/compare/v4.112.0...v4.113.0) (2025-01-16)


### Features

* **HERMES-2245:** Netherlands-document-false-alerts ([#2418](https://github.com/prezero/hermes-sf/issues/2418)) ([44bf711](https://github.com/prezero/hermes-sf/commit/44bf711ce2166e5097b2c0382117dbea0cb7022b))
* **HERMES-2258:** decoupled ftp-upload from message ([#2425](https://github.com/prezero/hermes-sf/issues/2425)) ([6ff9533](https://github.com/prezero/hermes-sf/commit/6ff95337f267522cf2889d54e02c5f2e7ef221de))
* **HERMES-2265:** IOT Changes ([#2426](https://github.com/prezero/hermes-sf/issues/2426)) ([6353d28](https://github.com/prezero/hermes-sf/commit/6353d28c743776d3aad6db45a1ecb6209bc68505))
* **HERMES-2269:** Keycloak -&gt; Schwarz SIAM integration ([#2424](https://github.com/prezero/hermes-sf/issues/2424)) ([766edcb](https://github.com/prezero/hermes-sf/commit/766edcb401c21e9679970f05d8fc21608f67535d))
* **HERMES-2272:** new attribute for app ([#2427](https://github.com/prezero/hermes-sf/issues/2427)) ([2e6c2bd](https://github.com/prezero/hermes-sf/commit/2e6c2bde0481d4e9069d4e5e45f2285df54718d1))
* Update pz config and translations ([#2428](https://github.com/prezero/hermes-sf/issues/2428)) ([08c5ba3](https://github.com/prezero/hermes-sf/commit/08c5ba3b63b21bc5f9044f278a69bf41e09ecd08))

## [4.112.0](https://github.com/prezero/hermes-sf/compare/v4.111.1...v4.112.0) (2025-01-16)


### Features

* Update pz config and translations ([#2421](https://github.com/prezero/hermes-sf/issues/2421)) ([62cc606](https://github.com/prezero/hermes-sf/commit/62cc6062beb1dc7ddf3421138ad20c9e2dc3026d))

## [4.111.1](https://github.com/prezero/hermes-sf/compare/v4.111.0...v4.111.1) (2025-01-15)


### Bug Fixes

* **HERMES-2253:** fixed output-type of endpoint ([#2419](https://github.com/prezero/hermes-sf/issues/2419)) ([ba53e4e](https://github.com/prezero/hermes-sf/commit/ba53e4e878a6a97ed0f8a8a18dbe1c8e0e3f6ed1))

## [4.111.0](https://github.com/prezero/hermes-sf/compare/v4.110.0...v4.111.0) (2025-01-15)


### Features

* **HERMES-2239:** Hardware Type ([#2415](https://github.com/prezero/hermes-sf/issues/2415)) ([83f73a9](https://github.com/prezero/hermes-sf/commit/83f73a9e13d43322446928a0763a1e91562cbeaa))
* pz config update ([#2417](https://github.com/prezero/hermes-sf/issues/2417)) ([b64e678](https://github.com/prezero/hermes-sf/commit/b64e6788e7a51294473b65ee13be676e17775e37))

## [4.110.0](https://github.com/prezero/hermes-sf/compare/v4.109.0...v4.110.0) (2025-01-14)


### Features

* Update pz config and translations ([#2413](https://github.com/prezero/hermes-sf/issues/2413)) ([cf676d3](https://github.com/prezero/hermes-sf/commit/cf676d35a37fd5f06291b11956c9d1498949d9fd))

## [4.109.0](https://github.com/prezero/hermes-sf/compare/v4.108.0...v4.109.0) (2025-01-13)


### Features

* **HERMES-2181:** Improving deployment labels for backend ([#2410](https://github.com/prezero/hermes-sf/issues/2410)) ([cded746](https://github.com/prezero/hermes-sf/commit/cded74603d5eb40df02d0e39344ee4fb2a931838))

## [4.108.0](https://github.com/prezero/hermes-sf/compare/v4.107.0...v4.108.0) (2025-01-13)


### Features

* **HERMES-2151:** Iot tenant change ([#2409](https://github.com/prezero/hermes-sf/issues/2409)) ([73678cb](https://github.com/prezero/hermes-sf/commit/73678cb7dfdd16464c4aa91d40c4b8e222b011ee))

## [4.107.0](https://github.com/prezero/hermes-sf/compare/v4.106.0...v4.107.0) (2025-01-13)


### Features

* **HERMES-2151:** Iot tenant change ([#2406](https://github.com/prezero/hermes-sf/issues/2406)) ([9c661ea](https://github.com/prezero/hermes-sf/commit/9c661ea451843960121e2ed183d4b2e86afa377b))


### Tests

* portal tracking test sometimes fails ([#2408](https://github.com/prezero/hermes-sf/issues/2408)) ([6d66266](https://github.com/prezero/hermes-sf/commit/6d662665d8aaace2c732630269926aa3eb7c4445))

## [4.106.0](https://github.com/prezero/hermes-sf/compare/v4.105.0...v4.106.0) (2025-01-13)


### Features

* changes detail-ep for tracking in portal ([#2405](https://github.com/prezero/hermes-sf/issues/2405)) ([6843f0d](https://github.com/prezero/hermes-sf/commit/6843f0d34d030d6860a7f05937f25ebc10d8d9ab))
* **HERMES-2119:** Tracking force push ([#2402](https://github.com/prezero/hermes-sf/issues/2402)) ([e015797](https://github.com/prezero/hermes-sf/commit/e015797da8b816583d98b18bcb96450b9912aaed))
* **HERMES-2126:** New tour flow ([#2404](https://github.com/prezero/hermes-sf/issues/2404)) ([6b9dcfe](https://github.com/prezero/hermes-sf/commit/6b9dcfe6ddac5390b3d84196042ec8913e627a7d))

## [4.105.0](https://github.com/prezero/hermes-sf/compare/v4.104.0...v4.105.0) (2025-01-10)


### Features

* **HERMES-2103:** mt import wp reduction ([#2398](https://github.com/prezero/hermes-sf/issues/2398)) ([579c5f1](https://github.com/prezero/hermes-sf/commit/579c5f1ca80f99de808f85c362ffada7d861e57c))
* Update translations to v71 ([#2400](https://github.com/prezero/hermes-sf/issues/2400)) ([619893d](https://github.com/prezero/hermes-sf/commit/619893d0e9fa9afb8f92269ea88f60f7d19954cd))

## [4.104.0](https://github.com/prezero/hermes-sf/compare/v4.103.0...v4.104.0) (2025-01-09)


### Features

* **HERMES-1943:** Dead letter queue ([#2396](https://github.com/prezero/hermes-sf/issues/2396)) ([ccd6a9f](https://github.com/prezero/hermes-sf/commit/ccd6a9f2549077e29fee736e6ad2cacac7d527d7))

## [4.103.0](https://github.com/prezero/hermes-sf/compare/v4.102.0...v4.103.0) (2025-01-08)


### Features

* **HERMES-1942:** Iot container action ([#2375](https://github.com/prezero/hermes-sf/issues/2375)) ([3549fe7](https://github.com/prezero/hermes-sf/commit/3549fe75c8e2a5e0567b8cf35ff93ecff423be1a))
* **HERMES-1949:** SAP Idempotency Key in header ([#2394](https://github.com/prezero/hermes-sf/issues/2394)) ([5213266](https://github.com/prezero/hermes-sf/commit/5213266f8deb7a92f63313dabeedb4d95599d110))


### Bug Fixes

* add e2e env for doctrine configuration ([4c76d53](https://github.com/prezero/hermes-sf/commit/4c76d530d7ce232daba2c0348c85c9b2b990d748))

## [4.102.0](https://github.com/prezero/hermes-sf/compare/v4.101.5...v4.102.0) (2025-01-08)


### Features

* **HERMES-2007:** adding text to image before ftp-upload ([#2392](https://github.com/prezero/hermes-sf/issues/2392)) ([4035c75](https://github.com/prezero/hermes-sf/commit/4035c75bd782753abbf7a153101028c6d331cdd7))
* **HERMES-2058:** Removing element entities ([#2390](https://github.com/prezero/hermes-sf/issues/2390)) ([2911406](https://github.com/prezero/hermes-sf/commit/29114069686e99a530af095328ac85bc634f6f4b))
* **HERMES-2078:** PHPStan migration ([#2385](https://github.com/prezero/hermes-sf/issues/2385)) ([236ea40](https://github.com/prezero/hermes-sf/commit/236ea40e4205604fd66fb015c859f79d7e8c15b4))
* **HERMES-2085:** new values for formulas ([#2389](https://github.com/prezero/hermes-sf/issues/2389)) ([4ae2a45](https://github.com/prezero/hermes-sf/commit/4ae2a450b6bf8d3dd54d530b504711ecdd402768))
* **HERMES-2088:** Update PHP base image, Symfony framework & libraries ([#2391](https://github.com/prezero/hermes-sf/issues/2391)) ([56fdd17](https://github.com/prezero/hermes-sf/commit/56fdd170dbcb6ce3fc657eb1e46b11ba843a5dbb))
* **HERMES-2099:** tracking list device ([#2393](https://github.com/prezero/hermes-sf/issues/2393)) ([8d3ea7d](https://github.com/prezero/hermes-sf/commit/8d3ea7d68cfe5def01040299f45fa6cbbcda0f3d))


### Tests

* **HERMES-439:** test refactorings ([#2386](https://github.com/prezero/hermes-sf/issues/2386)) ([96b0e56](https://github.com/prezero/hermes-sf/commit/96b0e568981ae975c5f6201c7fcd71988f711a60))
* **HERMES-439:** test refactorings ([#2388](https://github.com/prezero/hermes-sf/issues/2388)) ([de94e1a](https://github.com/prezero/hermes-sf/commit/de94e1a6c26f2ec1604782c5a7fcdc50e887592f))

## [4.101.5](https://github.com/prezero/hermes-sf/compare/v4.101.4...v4.101.5) (2025-01-02)


### Bug Fixes

* **HERMES-2082:** exception for calls before push-connection ([#2383](https://github.com/prezero/hermes-sf/issues/2383)) ([7bbac27](https://github.com/prezero/hermes-sf/commit/7bbac2728cc8c23d2c64113fa8cc3a7e13441bf1))

## [4.101.4](https://github.com/prezero/hermes-sf/compare/v4.101.3...v4.101.4) (2025-01-02)


### Bug Fixes

* **HERMES-2081:** added response-type ([#2381](https://github.com/prezero/hermes-sf/issues/2381)) ([13aeaa0](https://github.com/prezero/hermes-sf/commit/13aeaa0ff29b45e1317546030b5a6804fdbc8cc7))

## [4.101.3](https://github.com/prezero/hermes-sf/compare/v4.101.2...v4.101.3) (2025-01-02)


### Bug Fixes

* **HERMES-2079:** reenabled ftp-upload ([#2379](https://github.com/prezero/hermes-sf/issues/2379)) ([1335cb9](https://github.com/prezero/hermes-sf/commit/1335cb903492d224d8766bbe38eef20836d5aba0))

## [4.101.2](https://github.com/prezero/hermes-sf/compare/v4.101.1...v4.101.2) (2025-01-02)


### Bug Fixes

* **HERMES-2059:** Url param fix ([#2378](https://github.com/prezero/hermes-sf/issues/2378)) ([64931fd](https://github.com/prezero/hermes-sf/commit/64931fd68a205fdb3f09c69189dbe6b44dfaaea2))


### Tests

* **HERMES-1939:** refactored eq/staff-tests ([#2347](https://github.com/prezero/hermes-sf/issues/2347)) ([100155a](https://github.com/prezero/hermes-sf/commit/100155aaf1b9f381a7b25180f38e3e57ef625884))

## [4.101.1](https://github.com/prezero/hermes-sf/compare/v4.101.0...v4.101.1) (2024-12-20)


### Bug Fixes

* **HERMES-2029:** fixed eq-interruption doc ([#2372](https://github.com/prezero/hermes-sf/issues/2372)) ([54be33a](https://github.com/prezero/hermes-sf/commit/54be33a3cc18d7c46f76dc79ccb6a5d18dd15047))

## [4.101.0](https://github.com/prezero/hermes-sf/compare/v4.100.0...v4.101.0) (2024-12-19)


### Features

* **HERMES-1976:** tour update msg queue ([#2360](https://github.com/prezero/hermes-sf/issues/2360)) ([f0794be](https://github.com/prezero/hermes-sf/commit/f0794be1cc44012b48af0b77d48ab82ebd9298f2))
* **HERMES-2035:** changed order of calls ([#2371](https://github.com/prezero/hermes-sf/issues/2371)) ([eb28cef](https://github.com/prezero/hermes-sf/commit/eb28cef176d18ca22a461674c765635e3828f7e2))
* **HERMES-2039:** allowed 5 as per-page and added it to doc ([#2368](https://github.com/prezero/hermes-sf/issues/2368)) ([3b41947](https://github.com/prezero/hermes-sf/commit/3b41947b72b6d9ab3492433a82cc4f2e92ed9f54))
* Update translations to version 70 ([#2374](https://github.com/prezero/hermes-sf/issues/2374)) ([7bbd8f1](https://github.com/prezero/hermes-sf/commit/7bbd8f1f73eb862dd3f56271627f058dde1eff9a))


### Tests

* **HERMES-2037:** fixed ts for tracking-tests ([#2367](https://github.com/prezero/hermes-sf/issues/2367)) ([644e101](https://github.com/prezero/hermes-sf/commit/644e1019be1fc6d4de7aa5bbd06dc591e9cdf3b4))

## [4.100.0](https://github.com/prezero/hermes-sf/compare/v4.99.0...v4.100.0) (2024-12-18)


### Features

* Update pz-config ([#2369](https://github.com/prezero/hermes-sf/issues/2369)) ([0015763](https://github.com/prezero/hermes-sf/commit/00157639876a1b7ea9adb880cb79390580fb32f2))

## [4.99.0](https://github.com/prezero/hermes-sf/compare/v4.98.0...v4.99.0) (2024-12-18)


### Features

* **HERMES-1979:** portal track ep ([#2365](https://github.com/prezero/hermes-sf/issues/2365)) ([992d358](https://github.com/prezero/hermes-sf/commit/992d358e524277ac6b01879652d0ad8e66921a22))

## [4.98.0](https://github.com/prezero/hermes-sf/compare/v4.97.0...v4.98.0) (2024-12-18)


### Features

* Update pz config ([#2363](https://github.com/prezero/hermes-sf/issues/2363)) ([b2598b3](https://github.com/prezero/hermes-sf/commit/b2598b3ded05e16fce1a8c90621bc6a8b705f18b))

## [4.97.0](https://github.com/prezero/hermes-sf/compare/v4.96.1...v4.97.0) (2024-12-17)


### Features

* Update pz config ([#2361](https://github.com/prezero/hermes-sf/issues/2361)) ([9559fd2](https://github.com/prezero/hermes-sf/commit/9559fd2eb4327ec5cb8dba1d3eb261a4a5720079))

## [4.96.1](https://github.com/prezero/hermes-sf/compare/v4.96.0...v4.96.1) (2024-12-17)


### Bug Fixes

* **HERMES-2033:** api v2 doc params ([#2357](https://github.com/prezero/hermes-sf/issues/2357)) ([2580e2a](https://github.com/prezero/hermes-sf/commit/2580e2ae55f92cab96026231bc3e90fafc967ae2))
* **HERMES-2033:** api v2 doc params ([#2359](https://github.com/prezero/hermes-sf/issues/2359)) ([84e906d](https://github.com/prezero/hermes-sf/commit/84e906d6d909ebadf1d84b4298eca3b2e801eefa))

## [4.96.0](https://github.com/prezero/hermes-sf/compare/v4.95.0...v4.96.0) (2024-12-16)


### Features

* **HERMES-1923:** v2 hermers app API ([#2345](https://github.com/prezero/hermes-sf/issues/2345)) ([b2dea3c](https://github.com/prezero/hermes-sf/commit/b2dea3cbdcf49f16308cb5d4c3dfc7055c890473))

## [4.95.0](https://github.com/prezero/hermes-sf/compare/v4.94.0...v4.95.0) (2024-12-12)


### Features

* **HERMES-1294:** sap fail queue ([#2330](https://github.com/prezero/hermes-sf/issues/2330)) ([3985426](https://github.com/prezero/hermes-sf/commit/3985426c9404b94617bc94d7a53e8bb6f4fa052b))
* **HERMES-1854:** Generate staff in tests ([#2339](https://github.com/prezero/hermes-sf/issues/2339)) ([ffb74df](https://github.com/prezero/hermes-sf/commit/ffb74df8a016e15dd62e393f49f122c2cb45c76f))
* Update pz config ([#2355](https://github.com/prezero/hermes-sf/issues/2355)) ([d97b1bd](https://github.com/prezero/hermes-sf/commit/d97b1bd957e7ee990afa38236aabfeee18326c23))

## [4.94.0](https://github.com/prezero/hermes-sf/compare/v4.93.0...v4.94.0) (2024-12-09)


### Features

* **HERMES-1940:** Portal Equipment filters ([#2350](https://github.com/prezero/hermes-sf/issues/2350)) ([f0b8102](https://github.com/prezero/hermes-sf/commit/f0b81021575cab0b61c8c8670c3fb874e4c2cc71))
* **HERMES-1970:** Ignore invalid mastertour external ids during import ([#2352](https://github.com/prezero/hermes-sf/issues/2352)) ([3a3d261](https://github.com/prezero/hermes-sf/commit/3a3d26101d14e0d4b058a67ebd2245bb057ec3ad))


### Bug Fixes

* Use the same server time for each app time correction ([#2353](https://github.com/prezero/hermes-sf/issues/2353)) ([b914fc0](https://github.com/prezero/hermes-sf/commit/b914fc04adba036dd9a9e18acc330b2a5a0c054d))


### Tests

* **HERMES-1920:** changed spain test-tour to dynamic staff and geotab-device ([#2346](https://github.com/prezero/hermes-sf/issues/2346)) ([c48ec47](https://github.com/prezero/hermes-sf/commit/c48ec4714b72507ab83cf49187cd784917eb043a))

## [4.93.0](https://github.com/prezero/hermes-sf/compare/v4.92.1...v4.93.0) (2024-12-05)


### Features

* Update translations to version 69 ([#2348](https://github.com/prezero/hermes-sf/issues/2348)) ([cade061](https://github.com/prezero/hermes-sf/commit/cade061e2353145f8762b6d73c2a77ab2554f30a))

## [4.92.1](https://github.com/prezero/hermes-sf/compare/v4.92.0...v4.92.1) (2024-11-29)


### Bug Fixes

* **HERMES-1922:** Tenant switching operations fix ([#2343](https://github.com/prezero/hermes-sf/issues/2343)) ([93894f4](https://github.com/prezero/hermes-sf/commit/93894f40cc32c2461a86cc73d677cdc679b54beb))


### Tests

* **HERMES-1903:** Testing running tests in parallel ([#2337](https://github.com/prezero/hermes-sf/issues/2337)) ([25550a1](https://github.com/prezero/hermes-sf/commit/25550a17732ed7722703eae117fee6494c7fd449))

## [4.92.0](https://github.com/prezero/hermes-sf/compare/v4.91.0...v4.92.0) (2024-11-28)


### Features

* Update translations to version 68 ([#2340](https://github.com/prezero/hermes-sf/issues/2340)) ([a0c0102](https://github.com/prezero/hermes-sf/commit/a0c0102afb6adfd256d516faf2048a575d1708eb))


### Bug Fixes

* **HERMES-1907:** fixed mapping ([#2342](https://github.com/prezero/hermes-sf/issues/2342)) ([00efab7](https://github.com/prezero/hermes-sf/commit/00efab78ce2ba64c00bc563160aad40b1e61366a))

## [4.91.0](https://github.com/prezero/hermes-sf/compare/v4.90.0...v4.91.0) (2024-11-28)


### Features

* **HERMES-1129:** Allow setting email in user update ([#2137](https://github.com/prezero/hermes-sf/issues/2137)) ([36f4781](https://github.com/prezero/hermes-sf/commit/36f4781235a5b68f8db72d6b2aa002c47d9358f4))
* **HERMES-1902:** Migrate elements into tasks command ([#2336](https://github.com/prezero/hermes-sf/issues/2336)) ([4ee21a3](https://github.com/prezero/hermes-sf/commit/4ee21a3b64fbd574d670d9dcf72813d3bd2d3374))

## [4.90.0](https://github.com/prezero/hermes-sf/compare/v4.89.0...v4.90.0) (2024-11-27)


### Features

* **HERMES-1854:** Dynamic staff in tests ([#2316](https://github.com/prezero/hermes-sf/issues/2316)) ([137b0a1](https://github.com/prezero/hermes-sf/commit/137b0a1e616c2a36ba88fdd59271883651706a42))
* **HERMES-1856:** Embedding elements into tasks ([#2318](https://github.com/prezero/hermes-sf/issues/2318)) ([11b295f](https://github.com/prezero/hermes-sf/commit/11b295f415c5f24485e71c6bfb69c16c13c199d7))

## [4.89.0](https://github.com/prezero/hermes-sf/compare/v4.88.0...v4.89.0) (2024-11-26)


### Features

* **HERMES-1891:** Equipment positions from tracking data ([#2333](https://github.com/prezero/hermes-sf/issues/2333)) ([12e298a](https://github.com/prezero/hermes-sf/commit/12e298a84988bbe04998334296eb8ee098d20715))

## [4.88.0](https://github.com/prezero/hermes-sf/compare/v4.87.0...v4.88.0) (2024-11-26)


### Features

* **HERMES-1890:** Changing the equipment start date sent to SAP logic to match staffs ([#2331](https://github.com/prezero/hermes-sf/issues/2331)) ([1c638eb](https://github.com/prezero/hermes-sf/commit/1c638ebf1a112fe3fbc11fdaab33a9079dbd53c6))

## [4.87.0](https://github.com/prezero/hermes-sf/compare/v4.86.0...v4.87.0) (2024-11-25)


### Features

* **HERMES-1888:** Removing opentelemetry tracings in favor of blackfire ([#2328](https://github.com/prezero/hermes-sf/issues/2328)) ([f693f87](https://github.com/prezero/hermes-sf/commit/f693f87efb865283b1c65376b9b48d4dc456542c))

## [4.86.0](https://github.com/prezero/hermes-sf/compare/v4.85.0...v4.86.0) (2024-11-25)


### Features

* **HERMES-1867:** Maintenance mode + small namespace refactoring ([#2325](https://github.com/prezero/hermes-sf/issues/2325)) ([6ada0bb](https://github.com/prezero/hermes-sf/commit/6ada0bb39910bcf9324164dc629c554ed808fb05))
* **HERMES-1881:** Waypoint color enum ([#2326](https://github.com/prezero/hermes-sf/issues/2326)) ([a794515](https://github.com/prezero/hermes-sf/commit/a7945158dc136fc4084c2da830b8b60dc795dbd4))

## [4.85.0](https://github.com/prezero/hermes-sf/compare/v4.84.0...v4.85.0) (2024-11-22)


### Features

* **HERMES-Translation:** New Translation ([#2323](https://github.com/prezero/hermes-sf/issues/2323)) ([e8767d0](https://github.com/prezero/hermes-sf/commit/e8767d0ed990256164b6bbf582cb821cffe8bf90))

## [4.84.0](https://github.com/prezero/hermes-sf/compare/v4.83.1...v4.84.0) (2024-11-22)


### Features

* new translations for app ([#2321](https://github.com/prezero/hermes-sf/issues/2321)) ([9b226e5](https://github.com/prezero/hermes-sf/commit/9b226e53e7ad9b951c8623dd5b068074be78eeee))

## [4.83.1](https://github.com/prezero/hermes-sf/compare/v4.83.0...v4.83.1) (2024-11-21)


### Bug Fixes

* **HERMES-1877:** removed waypoint-id from import-output ([#2319](https://github.com/prezero/hermes-sf/issues/2319)) ([0d61bd8](https://github.com/prezero/hermes-sf/commit/0d61bd8bbffe4666518f95db5439fa4aeddc32a4))

## [4.83.0](https://github.com/prezero/hermes-sf/compare/v4.82.0...v4.83.0) (2024-11-20)


### Features

* **HERMES-1808:** Documentation Dashboard ([#2313](https://github.com/prezero/hermes-sf/issues/2313)) ([8516172](https://github.com/prezero/hermes-sf/commit/8516172573828d28cfe02d9b7b18d64ebe5e68dd))
* **HERMES-1864:** mastertour import ([#2317](https://github.com/prezero/hermes-sf/issues/2317)) ([bf18814](https://github.com/prezero/hermes-sf/commit/bf1881419b3f7c7f1ed32a355a0e96383ef7075c))


### Tests

* **HERMES-1833:** test session error ([#2314](https://github.com/prezero/hermes-sf/issues/2314)) ([00a0fc8](https://github.com/prezero/hermes-sf/commit/00a0fc8e3c7fff392e401a5bda283da589c01d8a))

## [4.82.0](https://github.com/prezero/hermes-sf/compare/v4.81.0...v4.82.0) (2024-11-15)


### Features

* **HERMES-1829:** Session equipment check ([#2308](https://github.com/prezero/hermes-sf/issues/2308)) ([e91b662](https://github.com/prezero/hermes-sf/commit/e91b66286b5f21f3dd84c2d1adf92807b2ea7a7e))
* **HERMES-1841:** Script Api test security ([#2311](https://github.com/prezero/hermes-sf/issues/2311)) ([ad1a754](https://github.com/prezero/hermes-sf/commit/ad1a7540d185180fa3bba08ea5b2b4238955c2e3))
* **HERMES-571:** Dynamic staff in test ([#2303](https://github.com/prezero/hermes-sf/issues/2303)) ([da2464d](https://github.com/prezero/hermes-sf/commit/da2464d2dde7d573bb1bb53e6f1ca0854ecb8fda))


### Bug Fixes

* **HERMES-1845:** setting timestamp for last login ([#2310](https://github.com/prezero/hermes-sf/issues/2310)) ([08751ee](https://github.com/prezero/hermes-sf/commit/08751ee70e7755db222664484779b275970f5144))

## [4.81.0](https://github.com/prezero/hermes-sf/compare/v4.80.1...v4.81.0) (2024-11-14)


### Features

* **HERMES-1822:** Recording mileage ([#2302](https://github.com/prezero/hermes-sf/issues/2302)) ([7d93c6d](https://github.com/prezero/hermes-sf/commit/7d93c6d3a4af0b5fafb00a4c808107c834cc1dc1))


### Bug Fixes

* **HERMES-1835:** nl mileage ([#2307](https://github.com/prezero/hermes-sf/issues/2307)) ([dd52a4d](https://github.com/prezero/hermes-sf/commit/dd52a4d62909aab62c36eb57e49fb7e330dbe7b6))

## [4.80.1](https://github.com/prezero/hermes-sf/compare/v4.80.0...v4.80.1) (2024-11-14)


### Bug Fixes

* change in lu-int-config ([#2304](https://github.com/prezero/hermes-sf/issues/2304)) ([01506c5](https://github.com/prezero/hermes-sf/commit/01506c51826f14b85cafc09c9db6cc946d82c7f1))

## [4.80.0](https://github.com/prezero/hermes-sf/compare/v4.79.0...v4.80.0) (2024-11-13)


### Features

* **HERMES-1795:** manual logout action to async ([#2298](https://github.com/prezero/hermes-sf/issues/2298)) ([e2851e7](https://github.com/prezero/hermes-sf/commit/e2851e731a4f70d191690b9fd63c193df3b3508f))
* **HERMES-1816:** implemented nl sap-calls ([#2300](https://github.com/prezero/hermes-sf/issues/2300)) ([e19f517](https://github.com/prezero/hermes-sf/commit/e19f51733e4b935409c37e4a3152bbd64957a12e))

## [4.79.0](https://github.com/prezero/hermes-sf/compare/v4.78.0...v4.79.0) (2024-11-13)


### Features

* **HERMES-1773:** accessible cleanup ([#2281](https://github.com/prezero/hermes-sf/issues/2281)) ([199a341](https://github.com/prezero/hermes-sf/commit/199a341b48664367225f66269047fc0b2e6defce))
* **HERMES-1775:** Cleanup old tours ([#2280](https://github.com/prezero/hermes-sf/issues/2280)) ([c8aa141](https://github.com/prezero/hermes-sf/commit/c8aa1416c1a555d394b38d3e2f522e91f7b5c607))
* **HERMES-1776:** Cleanup old sessions ([#2283](https://github.com/prezero/hermes-sf/issues/2283)) ([6ee83eb](https://github.com/prezero/hermes-sf/commit/6ee83eb8b08698102d3367241eba3743548259af))
* **HERMES-1778:** Removing older than 3 months bookings ([#2288](https://github.com/prezero/hermes-sf/issues/2288)) ([976c57c](https://github.com/prezero/hermes-sf/commit/976c57c7254f52be646cf61ab91e8565419a22e1))
* **HERMES-1791:** delete tour transition error ([#2290](https://github.com/prezero/hermes-sf/issues/2290)) ([8b1bd55](https://github.com/prezero/hermes-sf/commit/8b1bd55d1e4eade8f16af6acc779865429aec01c))
* **HERMES-1792:** Staff user start time to SAP adjustment ([#2289](https://github.com/prezero/hermes-sf/issues/2289)) ([5d9e791](https://github.com/prezero/hermes-sf/commit/5d9e79124df7102953a05c2e6141f35ba5c94885))

## [4.78.0](https://github.com/prezero/hermes-sf/compare/v4.77.1...v4.78.0) (2024-11-12)


### Features

* Add moba to hardware types ([#2296](https://github.com/prezero/hermes-sf/issues/2296)) ([0faaebb](https://github.com/prezero/hermes-sf/commit/0faaebb10082c2e541badc1e52b5c35c45fdd742))

## [4.77.1](https://github.com/prezero/hermes-sf/compare/v4.77.0...v4.77.1) (2024-11-12)


### Bug Fixes

* **HERMES-1814:** fixed case ([#2292](https://github.com/prezero/hermes-sf/issues/2292)) ([3307007](https://github.com/prezero/hermes-sf/commit/3307007723aa3a70a6a17301a61642c1352cd254))

## [4.77.0](https://github.com/prezero/hermes-sf/compare/v4.76.0...v4.77.0) (2024-11-12)


### Features

* checking env before executing ([#2291](https://github.com/prezero/hermes-sf/issues/2291)) ([7675add](https://github.com/prezero/hermes-sf/commit/7675add8fcea8bbd8a97ca75b09602858ae1f415))
* Update translations ([#2293](https://github.com/prezero/hermes-sf/issues/2293)) ([0101dd0](https://github.com/prezero/hermes-sf/commit/0101dd03877b64c655446da8f22fcaf48308841b))

## [4.76.0](https://github.com/prezero/hermes-sf/compare/v4.75.0...v4.76.0) (2024-11-11)


### Features

* **HERMES-1772:** User creation pw change ([#2282](https://github.com/prezero/hermes-sf/issues/2282)) ([c5353b0](https://github.com/prezero/hermes-sf/commit/c5353b0ae445f447d5c91382866b1a85366ed608))
* Update pz config fixtures ([#2286](https://github.com/prezero/hermes-sf/issues/2286)) ([eb884e3](https://github.com/prezero/hermes-sf/commit/eb884e36145a48d98491ea8c537c9296e0e81a4f))

## [4.75.0](https://github.com/prezero/hermes-sf/compare/v4.74.0...v4.75.0) (2024-11-08)


### Features

* **HERMES-1798:** district in location-info ([#2284](https://github.com/prezero/hermes-sf/issues/2284)) ([7c85402](https://github.com/prezero/hermes-sf/commit/7c85402bfe7d424c2d6762beb0dca12395d35a66))

## [4.74.0](https://github.com/prezero/hermes-sf/compare/v4.73.0...v4.74.0) (2024-11-08)


### Features

* pz config update ([#2278](https://github.com/prezero/hermes-sf/issues/2278)) ([3ab295e](https://github.com/prezero/hermes-sf/commit/3ab295e59b3c608d4e81ad9aab89a358d62dd917))

## [4.73.0](https://github.com/prezero/hermes-sf/compare/v4.72.0...v4.73.0) (2024-11-08)


### Features

* **HERMES-1793:** updated lux product mapping ([#2276](https://github.com/prezero/hermes-sf/issues/2276)) ([6a59222](https://github.com/prezero/hermes-sf/commit/6a59222e5056b6ad70fa21a99ba94f51e8ae3b00))


### Bug Fixes

* **HERMES-1784:** added system to output ([#2274](https://github.com/prezero/hermes-sf/issues/2274)) ([85fe76f](https://github.com/prezero/hermes-sf/commit/85fe76ffc46a3a6767aeddc9758e80c25b92c7a2))

## [4.72.0](https://github.com/prezero/hermes-sf/compare/v4.71.0...v4.72.0) (2024-11-08)


### Features

* **HERMES-1755:** Equipment blocked for booking ([#2267](https://github.com/prezero/hermes-sf/issues/2267)) ([8a0c5bf](https://github.com/prezero/hermes-sf/commit/8a0c5bfcb9aeeffa91d5661255a375c791eee269))
* **HERMES-1782:** Add fallback to municipal_collection taskgroup ([#2272](https://github.com/prezero/hermes-sf/issues/2272)) ([6f21d39](https://github.com/prezero/hermes-sf/commit/6f21d396ccec14bfb076407065b5340e951f5d36))

## [4.71.0](https://github.com/prezero/hermes-sf/compare/v4.70.0...v4.71.0) (2024-11-08)


### Features

* **HERMES-1777:** Cleanup schedule, old versions, reducing versions queries ([#2269](https://github.com/prezero/hermes-sf/issues/2269)) ([51ea4a4](https://github.com/prezero/hermes-sf/commit/51ea4a485c95b98d98682a1d681b7e57ba012c91))
* **HERMES-1784:** waypoint translations ([#2270](https://github.com/prezero/hermes-sf/issues/2270)) ([4204fd5](https://github.com/prezero/hermes-sf/commit/4204fd5da5ca942e987f04d7931a428c028e00ad))

## [4.70.0](https://github.com/prezero/hermes-sf/compare/v4.69.0...v4.70.0) (2024-11-07)


### Features

* **HERMES-1255:** element options ([#2254](https://github.com/prezero/hermes-sf/issues/2254)) ([6b12991](https://github.com/prezero/hermes-sf/commit/6b1299190f6f0da83bd41c6d1acb0a33c9e2954b))
* **HERMES-1774:** Removes obsolete additional information entities ([#2265](https://github.com/prezero/hermes-sf/issues/2265)) ([9601407](https://github.com/prezero/hermes-sf/commit/960140738e5f23c61a3f78dffdb0255a732f941d))
* Update translations to version 63 ([#2266](https://github.com/prezero/hermes-sf/issues/2266)) ([facdf27](https://github.com/prezero/hermes-sf/commit/facdf27ce5b703d76a44201a4c62366c1da4db42))


### Bug Fixes

* **HERMES-1770:** pdf overlap ([#2261](https://github.com/prezero/hermes-sf/issues/2261)) ([cb7d282](https://github.com/prezero/hermes-sf/commit/cb7d282a2841209ec565d6259d0c3ef6023e6b59))
* set mem-limit ([#2264](https://github.com/prezero/hermes-sf/issues/2264)) ([4f9698d](https://github.com/prezero/hermes-sf/commit/4f9698d6c422ad04da78531f212e7b764e5d1592))


### Tests

* **HERMES-1748:** Improving tests isolation ([#2263](https://github.com/prezero/hermes-sf/issues/2263)) ([d75acae](https://github.com/prezero/hermes-sf/commit/d75acae23e1f5619766b603c3cb4f11b3e4325c2))

## [4.69.0](https://github.com/prezero/hermes-sf/compare/v4.68.1...v4.69.0) (2024-11-06)


### Features

* Update es config ([#2260](https://github.com/prezero/hermes-sf/issues/2260)) ([b602b1f](https://github.com/prezero/hermes-sf/commit/b602b1f0c404ccea6736ebb2d160dec69c4a2f90))
* Update lux-int fixtures ([#2257](https://github.com/prezero/hermes-sf/issues/2257)) ([4f8b81f](https://github.com/prezero/hermes-sf/commit/4f8b81fbaec1380fbbb7d47f351e0640d63c8806))
* Update packages with vulnerabilities ([#2259](https://github.com/prezero/hermes-sf/issues/2259)) ([e22a772](https://github.com/prezero/hermes-sf/commit/e22a77224bf9d23b7308253a0d6c4e1099bea98a))


### Bug Fixes

* lux tests ([#2255](https://github.com/prezero/hermes-sf/issues/2255)) ([4b991a5](https://github.com/prezero/hermes-sf/commit/4b991a562e2b164f037eacec42ff2fd89f2116f3))
* remove cf deploy ([#2256](https://github.com/prezero/hermes-sf/issues/2256)) ([92390ca](https://github.com/prezero/hermes-sf/commit/92390ca1a8970abcf4e7baed6231fe8562ddc71e))

## [4.68.1](https://github.com/prezero/hermes-sf/compare/v4.68.0...v4.68.1) (2024-11-06)


### Bug Fixes

* lux order product dto ([#2252](https://github.com/prezero/hermes-sf/issues/2252)) ([8100073](https://github.com/prezero/hermes-sf/commit/8100073a44f5b6e8d0d138bf73e5cae9929811ce))

## [4.68.0](https://github.com/prezero/hermes-sf/compare/v4.67.1...v4.68.0) (2024-11-05)


### Features

* lux int config update ([#2249](https://github.com/prezero/hermes-sf/issues/2249)) ([11ce22c](https://github.com/prezero/hermes-sf/commit/11ce22c085f468daa1036f566cd0845ff55d1ec8))

## [4.67.1](https://github.com/prezero/hermes-sf/compare/v4.67.0...v4.67.1) (2024-11-05)


### Bug Fixes

* **HERMES-1762:** fixed location-service ([#2247](https://github.com/prezero/hermes-sf/issues/2247)) ([6a5faa1](https://github.com/prezero/hermes-sf/commit/6a5faa130f68d29b15af287f15f50bd4a3802bd8))

## [4.67.0](https://github.com/prezero/hermes-sf/compare/v4.66.0...v4.67.0) (2024-11-05)


### Features

* **HERMES-1664:** New waypoint attribute road_name ([#2244](https://github.com/prezero/hermes-sf/issues/2244)) ([ea2c05e](https://github.com/prezero/hermes-sf/commit/ea2c05e158cb508d7ac0962dbb9927a6c12d1531))


### Bug Fixes

* **HERMES-1759:** Skip duplicate order processing from SAP ([#2246](https://github.com/prezero/hermes-sf/issues/2246)) ([f7a0c29](https://github.com/prezero/hermes-sf/commit/f7a0c2909420de0fc19ed643c6e99746f303f73e))

## [4.66.0](https://github.com/prezero/hermes-sf/compare/v4.65.0...v4.66.0) (2024-11-04)


### Features

* Update es config ([#2242](https://github.com/prezero/hermes-sf/issues/2242)) ([f41f7cd](https://github.com/prezero/hermes-sf/commit/f41f7cdabb11108627f1b4a41ddaab4e30450baa))

## [4.65.0](https://github.com/prezero/hermes-sf/compare/v4.64.0...v4.65.0) (2024-11-04)


### Features

* **HERMES-1663:** Mastertour template distance in meters ([#2238](https://github.com/prezero/hermes-sf/issues/2238)) ([55c7e22](https://github.com/prezero/hermes-sf/commit/55c7e224bd9dedb0bfd6cd00bdcbbc14642d86c7))
* **HERMES-1751:** added ts to confirmation tasks ([#2240](https://github.com/prezero/hermes-sf/issues/2240)) ([1b6c796](https://github.com/prezero/hermes-sf/commit/1b6c79658e61080f41dd1426b8e6d065ab0d7382))

## [4.64.0](https://github.com/prezero/hermes-sf/compare/v4.63.1...v4.64.0) (2024-11-04)


### Features

* **HERMES-1696:** Lux order products store in local DB ([#2233](https://github.com/prezero/hermes-sf/issues/2233)) ([a63c8dd](https://github.com/prezero/hermes-sf/commit/a63c8dd7f8656e561e2f5b17f1a80db9c2020df4))


### Bug Fixes

* **HERMES-1745:** added type ([#2239](https://github.com/prezero/hermes-sf/issues/2239)) ([af244c8](https://github.com/prezero/hermes-sf/commit/af244c8fbb44119724a963e205486c9bb3e9016f))

## [4.63.1](https://github.com/prezero/hermes-sf/compare/v4.63.0...v4.63.1) (2024-11-01)


### Bug Fixes

* missing additional information ([#2235](https://github.com/prezero/hermes-sf/issues/2235)) ([8de831f](https://github.com/prezero/hermes-sf/commit/8de831ffb5ea42eebf18c8e72973bd6a61725622))

## [4.63.0](https://github.com/prezero/hermes-sf/compare/v4.62.0...v4.63.0) (2024-10-31)


### Features

* **HERMES-1662:** App api mastertour progress ([#2227](https://github.com/prezero/hermes-sf/issues/2227)) ([64c7922](https://github.com/prezero/hermes-sf/commit/64c79226994fb1d5a66b2a50e4a831bd6911bda2))
* Update es config ([#2234](https://github.com/prezero/hermes-sf/issues/2234)) ([20b0c33](https://github.com/prezero/hermes-sf/commit/20b0c33cefada3c8b83a8a526cca85c0ff24138b))

## [4.62.0](https://github.com/prezero/hermes-sf/compare/v4.61.0...v4.62.0) (2024-10-31)


### Features

* **HERMES-1660:** Embed additional information as value objects ([#2226](https://github.com/prezero/hermes-sf/issues/2226)) ([d30424b](https://github.com/prezero/hermes-sf/commit/d30424b60966eb74879d19e55b9f250a7d98d725))

## [4.61.0](https://github.com/prezero/hermes-sf/compare/v4.60.0...v4.61.0) (2024-10-31)


### Features

* tour status translations ([#2228](https://github.com/prezero/hermes-sf/issues/2228)) ([4bb95bd](https://github.com/prezero/hermes-sf/commit/4bb95bdad960ec2d745761d8240a80a2686d5690))
* Update lux-int config ([#2230](https://github.com/prezero/hermes-sf/issues/2230)) ([1abd0e1](https://github.com/prezero/hermes-sf/commit/1abd0e10d0180a928f547333fa6a797d3e7e270e))

## [4.60.0](https://github.com/prezero/hermes-sf/compare/v4.59.0...v4.60.0) (2024-10-30)


### Features

* Update lux-int config ([75296ae](https://github.com/prezero/hermes-sf/commit/75296ae96ed3627f25c4d381969a9a612313b172))

## [4.59.0](https://github.com/prezero/hermes-sf/compare/v4.58.0...v4.59.0) (2024-10-30)


### Features

* Update lux int config ([#2223](https://github.com/prezero/hermes-sf/issues/2223)) ([4bb7935](https://github.com/prezero/hermes-sf/commit/4bb7935393e4748a2d939eda06a128b54e8ced73))

## [4.58.0](https://github.com/prezero/hermes-sf/compare/v4.57.0...v4.58.0) (2024-10-30)


### Features

* **HERMES-1644:** orderdoc infotext ([#2217](https://github.com/prezero/hermes-sf/issues/2217)) ([84649b1](https://github.com/prezero/hermes-sf/commit/84649b16d1d5f287ff6fdabde58ac6b075c0be60))
* Update es config ([#2222](https://github.com/prezero/hermes-sf/issues/2222)) ([e9cd85a](https://github.com/prezero/hermes-sf/commit/e9cd85a393c3d87c3687e9362f17d8f62209eac5))

## [4.57.0](https://github.com/prezero/hermes-sf/compare/v4.56.0...v4.57.0) (2024-10-29)


### Features

* **HERMES-1642:** Accessible Task Type ([#2216](https://github.com/prezero/hermes-sf/issues/2216)) ([226f74a](https://github.com/prezero/hermes-sf/commit/226f74a52b67a57cde6e0362752b4f492054516e))
* **HERMES-1642:** Info file name text ([#2219](https://github.com/prezero/hermes-sf/issues/2219)) ([f8eca11](https://github.com/prezero/hermes-sf/commit/f8eca111488a2a3f8c284899b07f7c3427c626e5))


### Bug Fixes

* added new dump ([#2220](https://github.com/prezero/hermes-sf/issues/2220)) ([2525fa3](https://github.com/prezero/hermes-sf/commit/2525fa3546785323fb17b7b05e52454fb51f980a))

## [4.56.0](https://github.com/prezero/hermes-sf/compare/v4.55.0...v4.56.0) (2024-10-29)


### Features

* **HERMES-1630:** Task location ([#2214](https://github.com/prezero/hermes-sf/issues/2214)) ([4efd97f](https://github.com/prezero/hermes-sf/commit/4efd97f0ec786ab6e41f50b4872539db30cf80ca))


### Bug Fixes

* **HERMES-1635:** changed source to enum instead of string ([#2211](https://github.com/prezero/hermes-sf/issues/2211)) ([eb9ba41](https://github.com/prezero/hermes-sf/commit/eb9ba41f88d3fc0b2f304463e77e0c774cbac12c))

## [4.55.0](https://github.com/prezero/hermes-sf/compare/v4.54.0...v4.55.0) (2024-10-29)


### Features

* **HERMES-1588:** Test refactoring ([#2198](https://github.com/prezero/hermes-sf/issues/2198)) ([a16e146](https://github.com/prezero/hermes-sf/commit/a16e146b02321c97bd9ceb0878716e43bec3510f))


### Bug Fixes

* Fix setting of rule defaults in disposal site drives ([#2212](https://github.com/prezero/hermes-sf/issues/2212)) ([45f0fb3](https://github.com/prezero/hermes-sf/commit/45f0fb3c429a2c31c424a4b4afe786233d8d83df))

## [4.54.0](https://github.com/prezero/hermes-sf/compare/v4.53.1...v4.54.0) (2024-10-28)


### Features

* Update lux-int config ([#2209](https://github.com/prezero/hermes-sf/issues/2209)) ([7f5667a](https://github.com/prezero/hermes-sf/commit/7f5667a72bebaff0af615fe95783af2587d6c4b9))

## [4.53.1](https://github.com/prezero/hermes-sf/compare/v4.53.0...v4.53.1) (2024-10-28)


### Bug Fixes

* basic auth ([#2207](https://github.com/prezero/hermes-sf/issues/2207)) ([a3e7adc](https://github.com/prezero/hermes-sf/commit/a3e7adc97f723cd17f534610d76e1577e8a5182a))

## [4.53.0](https://github.com/prezero/hermes-sf/compare/v4.52.0...v4.53.0) (2024-10-28)


### Features

* Update es config ([#2205](https://github.com/prezero/hermes-sf/issues/2205)) ([5e5c99e](https://github.com/prezero/hermes-sf/commit/5e5c99ea336255cddf4b857d73fc2c5404078428))

## [4.52.0](https://github.com/prezero/hermes-sf/compare/v4.51.0...v4.52.0) (2024-10-28)


### Features

* **HERMES-1629:** changed email-locale to user-country ([#2203](https://github.com/prezero/hermes-sf/issues/2203)) ([0f6140c](https://github.com/prezero/hermes-sf/commit/0f6140ccfb10942545bedb5680870292167b0d0f))

## [4.51.0](https://github.com/prezero/hermes-sf/compare/v4.50.0...v4.51.0) (2024-10-28)


### Features

* Update es config ([#2201](https://github.com/prezero/hermes-sf/issues/2201)) ([8d45498](https://github.com/prezero/hermes-sf/commit/8d45498e16b0ca17b61711faa2175f6c83885f86))

## [4.50.0](https://github.com/prezero/hermes-sf/compare/v4.49.0...v4.50.0) (2024-10-25)


### Features

* es config update ([#2200](https://github.com/prezero/hermes-sf/issues/2200)) ([e87257c](https://github.com/prezero/hermes-sf/commit/e87257c84bf2244ea52c6ae768c055f4ea7dabd1))


### Bug Fixes

* added missing tenant prefix for spain ([#2197](https://github.com/prezero/hermes-sf/issues/2197)) ([1618e37](https://github.com/prezero/hermes-sf/commit/1618e3793045d3fe3fbe9623e44ede809f9146e6))

## [4.49.0](https://github.com/prezero/hermes-sf/compare/v4.48.0...v4.49.0) (2024-10-25)


### Features

* Hermes 1222 portal mt progress ([#2190](https://github.com/prezero/hermes-sf/issues/2190)) ([dc41071](https://github.com/prezero/hermes-sf/commit/dc4107109f29daa800b8ba88675cdfe63d6eddde))
* **HERMES-1384:** Luxembourg complete message of order ([#2189](https://github.com/prezero/hermes-sf/issues/2189)) ([3c640a9](https://github.com/prezero/hermes-sf/commit/3c640a947444587b65d172b02ec535a22f3c3ae6))
* **HERMES-1580:** user tenant interface ([#2194](https://github.com/prezero/hermes-sf/issues/2194)) ([7d2a2e0](https://github.com/prezero/hermes-sf/commit/7d2a2e0f8fa33f8ba7d653cef7a25d7e9afb29b1))
* **HERMES-1589:** S3 access debuginfo ([#2196](https://github.com/prezero/hermes-sf/issues/2196)) ([8a5dbc4](https://github.com/prezero/hermes-sf/commit/8a5dbc45103c106208b3a800bf4fa6092965d7f6))
* Update es config ([#2195](https://github.com/prezero/hermes-sf/issues/2195)) ([0660b40](https://github.com/prezero/hermes-sf/commit/0660b40d1730d7ceff8434001756b60b988dd54c))

## [4.48.0](https://github.com/prezero/hermes-sf/compare/v4.47.1...v4.48.0) (2024-10-23)


### Features

* lux int config update ([#2191](https://github.com/prezero/hermes-sf/issues/2191)) ([1bc501f](https://github.com/prezero/hermes-sf/commit/1bc501fe4ec29cd482d7c0eebce22f5dbab4b065))

## [4.47.1](https://github.com/prezero/hermes-sf/compare/v4.47.0...v4.47.1) (2024-10-23)


### Bug Fixes

* **HERMES-1573:** user tenant filter ([#2186](https://github.com/prezero/hermes-sf/issues/2186)) ([3ce1ff5](https://github.com/prezero/hermes-sf/commit/3ce1ff5739adb928045f2d8a587e0aa102ed2a12))
* **HERMES-1579:** changed type fitting to current SAP ([#2188](https://github.com/prezero/hermes-sf/issues/2188)) ([95f1445](https://github.com/prezero/hermes-sf/commit/95f14454505c9b255b76a2226d43db17cdc480b7))

## [4.47.0](https://github.com/prezero/hermes-sf/compare/v4.46.1...v4.47.0) (2024-10-22)


### Features

* **HERMES-1567:** vehiclecheck translations ([#2182](https://github.com/prezero/hermes-sf/issues/2182)) ([ced63cf](https://github.com/prezero/hermes-sf/commit/ced63cf0bb77f9db63a384f3e3e256753ac279ef))
* **HERMES-1572:** changed language-switch for delivery-note ([#2181](https://github.com/prezero/hermes-sf/issues/2181)) ([ad2ef13](https://github.com/prezero/hermes-sf/commit/ad2ef137889f89bcc52df29d933f696f2e18d800))
* Update lux_int config ([#2185](https://github.com/prezero/hermes-sf/issues/2185)) ([dd888d6](https://github.com/prezero/hermes-sf/commit/dd888d6731d9e059e59f7813627bfc87a452fbf7))


### Bug Fixes

* **HERMES-1574:** added spain tenant-shortcut ([#2184](https://github.com/prezero/hermes-sf/issues/2184)) ([454d856](https://github.com/prezero/hermes-sf/commit/454d8566cb9858c265bdea4a09ff92ddf500f8cf))

## [4.46.1](https://github.com/prezero/hermes-sf/compare/v4.46.0...v4.46.1) (2024-10-22)


### Bug Fixes

* **HERMES-1567:** vehiclecheck translations ([#2179](https://github.com/prezero/hermes-sf/issues/2179)) ([f5a3fbe](https://github.com/prezero/hermes-sf/commit/f5a3fbe7bc92cb87f1be84976fa77d854e86997f))

## [4.46.0](https://github.com/prezero/hermes-sf/compare/v4.45.1...v4.46.0) (2024-10-22)


### Features

* **HERMES-1567:** new translations v51 ([#2177](https://github.com/prezero/hermes-sf/issues/2177)) ([702a3c9](https://github.com/prezero/hermes-sf/commit/702a3c9b69c3e582157bcfe433892cbc6b8de9e8))

## [4.45.1](https://github.com/prezero/hermes-sf/compare/v4.45.0...v4.45.1) (2024-10-21)


### Bug Fixes

* fixed event trigger ([#2174](https://github.com/prezero/hermes-sf/issues/2174)) ([3afb2d6](https://github.com/prezero/hermes-sf/commit/3afb2d6de228c0d0df0dd60628e0093a6eb4441a))

## [4.45.0](https://github.com/prezero/hermes-sf/compare/v4.44.0...v4.45.0) (2024-10-21)


### Features

* **HERMES-1469:** delivery note translation ([#2172](https://github.com/prezero/hermes-sf/issues/2172)) ([dfaa51b](https://github.com/prezero/hermes-sf/commit/dfaa51babb5405c0fa8ea01bb83ed3caaf7ac91b))

## [4.44.0](https://github.com/prezero/hermes-sf/compare/v4.43.0...v4.44.0) (2024-10-21)


### Features

* **HERMES-1223:** Additional information file sap ([#2147](https://github.com/prezero/hermes-sf/issues/2147)) ([c2caf64](https://github.com/prezero/hermes-sf/commit/c2caf64cc733114e5cf2b00f6030b3bc729d86bc))
* **HERMES-1287:** Mastertour template description attribute ([#2170](https://github.com/prezero/hermes-sf/issues/2170)) ([1426ea4](https://github.com/prezero/hermes-sf/commit/1426ea49989e4424e9269a5fcd9f15f05ae52e9f))
* Update es config ([#2168](https://github.com/prezero/hermes-sf/issues/2168)) ([37f1f01](https://github.com/prezero/hermes-sf/commit/37f1f01783144673c005ef85d67e8747df3096af))
* Update lux_int fixtures ([#2173](https://github.com/prezero/hermes-sf/issues/2173)) ([e09da11](https://github.com/prezero/hermes-sf/commit/e09da11c786e4a73c72e77448a72efcf77cd951c))


### Bug Fixes

* used env-vars for test ([#2171](https://github.com/prezero/hermes-sf/issues/2171)) ([795d19e](https://github.com/prezero/hermes-sf/commit/795d19ea8d75dd460496fc7683caac512be919fd))

## [4.43.0](https://github.com/prezero/hermes-sf/compare/v4.42.1...v4.43.0) (2024-10-17)


### Features

* Update es config ([#2166](https://github.com/prezero/hermes-sf/issues/2166)) ([41bee22](https://github.com/prezero/hermes-sf/commit/41bee22022635c880b7a16c0f3271ca242ee3d82))

## [4.42.1](https://github.com/prezero/hermes-sf/compare/v4.42.0...v4.42.1) (2024-10-17)


### Bug Fixes

* **HERMES-1455:** added rule-mapping to eq/staff ([#2164](https://github.com/prezero/hermes-sf/issues/2164)) ([a233f30](https://github.com/prezero/hermes-sf/commit/a233f308a12756023ef73e57d4e4e9451e85269e))

## [4.42.0](https://github.com/prezero/hermes-sf/compare/v4.41.0...v4.42.0) (2024-10-17)


### Features

* Update translations to v49 ([#2163](https://github.com/prezero/hermes-sf/issues/2163)) ([1818b19](https://github.com/prezero/hermes-sf/commit/1818b19ff21d0573416c0f9a7a5b920c17910735))


### Bug Fixes

* **HERMES-1410:** changed to external ids ([#2160](https://github.com/prezero/hermes-sf/issues/2160)) ([fbd371a](https://github.com/prezero/hermes-sf/commit/fbd371a49a5b7b57661963cce2208c6ef609ed95))

## [4.41.0](https://github.com/prezero/hermes-sf/compare/v4.40.0...v4.41.0) (2024-10-16)


### Features

* **HERMES-1126:** Removing softdeletable, updating doctrine ([#2155](https://github.com/prezero/hermes-sf/issues/2155)) ([ddc162c](https://github.com/prezero/hermes-sf/commit/ddc162c7a5292bcb159d0c6349baafd6144156b6))
* **HERMES-1352:** Order Document Delivery Service Unit ([#2159](https://github.com/prezero/hermes-sf/issues/2159)) ([cd44ef8](https://github.com/prezero/hermes-sf/commit/cd44ef84019d1e88af81894e4b5b4e62586f6aab))


### Bug Fixes

* Add nullable to order name ([#2157](https://github.com/prezero/hermes-sf/issues/2157)) ([81f16d3](https://github.com/prezero/hermes-sf/commit/81f16d389dfa88ec1c80ad8042db76b2e55f057c))
* Fix migration class name ([#2158](https://github.com/prezero/hermes-sf/issues/2158)) ([b71c20b](https://github.com/prezero/hermes-sf/commit/b71c20b775bd77741216892c7c0dace2a7ab41ab))
* Only load non config fixtures after database drop ([#2161](https://github.com/prezero/hermes-sf/issues/2161)) ([f765fae](https://github.com/prezero/hermes-sf/commit/f765fae38f575d6ca5743617fae557c35247528f))
* tourordertype translation ([#2154](https://github.com/prezero/hermes-sf/issues/2154)) ([0f74851](https://github.com/prezero/hermes-sf/commit/0f7485145bdf431a2d91d55c98ef31a8b5b94dd0))


### Miscellaneous Chores

* Adding hermes backend team as code owners ([8f732e3](https://github.com/prezero/hermes-sf/commit/8f732e30e90cd91346bb2ceadc8494a95bb71c4e))
* Adding pull request checklist ([f17bbf1](https://github.com/prezero/hermes-sf/commit/f17bbf1b39c372d172404e739714f76f18170528))

## [4.40.0](https://github.com/prezero/hermes-sf/compare/v4.39.0...v4.40.0) (2024-10-15)


### Features

* Update es config fixtures ([#2152](https://github.com/prezero/hermes-sf/issues/2152)) ([d27db54](https://github.com/prezero/hermes-sf/commit/d27db54b9e19e67ee157cc22d0178eda9f9dc1e4))

## [4.39.0](https://github.com/prezero/hermes-sf/compare/v4.38.0...v4.39.0) (2024-10-14)


### Features

* Redis adapter usage change ([cd8a2e6](https://github.com/prezero/hermes-sf/commit/cd8a2e621be9054554be976acf90b57f79a35bc2))
* Redis address change ([56cd346](https://github.com/prezero/hermes-sf/commit/56cd346b85641b02315428f6171138b104e322dd))
* Redis address change ([182711e](https://github.com/prezero/hermes-sf/commit/182711e01fc1c1f7f70b9362a20ebf73192106b6))
* Redis address change ([ebd9d05](https://github.com/prezero/hermes-sf/commit/ebd9d05c6df70a42c2908fc32b2608e2950047dd))

## [4.38.0](https://github.com/prezero/hermes-sf/compare/v4.37.0...v4.38.0) (2024-10-14)


### Features

* **HERMES-1283:** added filters and tests ([#2143](https://github.com/prezero/hermes-sf/issues/2143)) ([db1b827](https://github.com/prezero/hermes-sf/commit/db1b8271a156c3d75514379986a1e2473f45ddaf))
* **HERMES-1338:** removed gis-service ([#2148](https://github.com/prezero/hermes-sf/issues/2148)) ([f98029f](https://github.com/prezero/hermes-sf/commit/f98029fa936710c05cb91a07a050714d4eb24521))
* **HERMES-1339:** removed tasktype-check temp ([#2149](https://github.com/prezero/hermes-sf/issues/2149)) ([6e24294](https://github.com/prezero/hermes-sf/commit/6e2429480a4643a491ec0071cad9144dc9b954a6))
* **HERMES.1285:** additional-information in eq-check ([#2146](https://github.com/prezero/hermes-sf/issues/2146)) ([98f56f1](https://github.com/prezero/hermes-sf/commit/98f56f1d745db41856e2b6a24698672a6aa27c3b))

## [4.37.0](https://github.com/prezero/hermes-sf/compare/v4.36.0...v4.37.0) (2024-10-07)


### Features

* **HERMES-1286:** mastertour in app tour output ([#2144](https://github.com/prezero/hermes-sf/issues/2144)) ([51a59b6](https://github.com/prezero/hermes-sf/commit/51a59b6c69eda293d68a8c0e1c845c14690bd6a1))

## [4.36.0](https://github.com/prezero/hermes-sf/compare/v4.35.0...v4.36.0) (2024-10-04)


### Features

* **HERMES-1122:** Portal booking list endpoint ([#2136](https://github.com/prezero/hermes-sf/issues/2136)) ([9b119b7](https://github.com/prezero/hermes-sf/commit/9b119b7cc4437993ecbff73502b83eedb135c600))
* **HERMES-1221:** mt progress app ([#2138](https://github.com/prezero/hermes-sf/issues/2138)) ([fd228ee](https://github.com/prezero/hermes-sf/commit/fd228ee6c97c93e40cd92302d9f5906d3f637047))
* **HERMES-1292:** RabbitMQ quorum queue size ([#2142](https://github.com/prezero/hermes-sf/issues/2142)) ([d7e5060](https://github.com/prezero/hermes-sf/commit/d7e50608349dc0dbd5d6e6c0a7e744a1ab90bd0d))

## [4.35.0](https://github.com/prezero/hermes-sf/compare/v4.34.1...v4.35.0) (2024-10-02)


### Features

* lux config update ([#2139](https://github.com/prezero/hermes-sf/issues/2139)) ([8b0966c](https://github.com/prezero/hermes-sf/commit/8b0966c8b9ea67d329fefd48905af1d62df5c3bc))

## [4.34.1](https://github.com/prezero/hermes-sf/compare/v4.34.0...v4.34.1) (2024-09-30)


### Bug Fixes

* setting default ([#2134](https://github.com/prezero/hermes-sf/issues/2134)) ([01e2a1e](https://github.com/prezero/hermes-sf/commit/01e2a1ef606b50b2379a5512d9027ddbe3a53e7f))

## [4.34.0](https://github.com/prezero/hermes-sf/compare/v4.33.0...v4.34.0) (2024-09-30)


### Features

* **HERMES-1225:** Last tour update notification ([#2131](https://github.com/prezero/hermes-sf/issues/2131)) ([2f91f39](https://github.com/prezero/hermes-sf/commit/2f91f3936107412393e0ba400059a1fa9e3a313e))
* **HERMES.1265:** ep for branches ([#2132](https://github.com/prezero/hermes-sf/issues/2132)) ([7f72d7a](https://github.com/prezero/hermes-sf/commit/7f72d7a8f5200f11878cdb97c89898e9f4a75c55))

## [4.33.0](https://github.com/prezero/hermes-sf/compare/v4.32.0...v4.33.0) (2024-09-30)


### Features

* **HERMES-1220:** Mastertour template branch ([#2127](https://github.com/prezero/hermes-sf/issues/2127)) ([6ac1a6c](https://github.com/prezero/hermes-sf/commit/6ac1a6cfedcd7e125d7da4938e9a62e731c5894a))

## [4.32.0](https://github.com/prezero/hermes-sf/compare/v4.31.0...v4.32.0) (2024-09-30)


### Features

* nl config update ([#2128](https://github.com/prezero/hermes-sf/issues/2128)) ([30ea55e](https://github.com/prezero/hermes-sf/commit/30ea55e668b867b2962baa809a8ce9a2fa1a6b32))

## [4.31.0](https://github.com/prezero/hermes-sf/compare/v4.30.0...v4.31.0) (2024-09-27)


### Features

* Update es config ([#2125](https://github.com/prezero/hermes-sf/issues/2125)) ([e28b4f1](https://github.com/prezero/hermes-sf/commit/e28b4f1bd139c2aede840bfd2650f91abc8e1ccc))

## [4.30.0](https://github.com/prezero/hermes-sf/compare/v4.29.0...v4.30.0) (2024-09-26)


### Features

* **HERMES-1198:** Change tour endpoint ([#2119](https://github.com/prezero/hermes-sf/issues/2119)) ([3c01791](https://github.com/prezero/hermes-sf/commit/3c0179133bcec5df8dd782af58f32108b28dadec))
* **HERMES-1216:** new roles ([#2120](https://github.com/prezero/hermes-sf/issues/2120)) ([99cfe76](https://github.com/prezero/hermes-sf/commit/99cfe76882106ccc35c390172071657d8a3617a6))
* **HERMES-1237:** toggle taskgroups from sap in started orders ([#2124](https://github.com/prezero/hermes-sf/issues/2124)) ([540a29c](https://github.com/prezero/hermes-sf/commit/540a29ca408217c14a6ac6df7c18f70506b72fcc))
* Update nl config ([#2123](https://github.com/prezero/hermes-sf/issues/2123)) ([d32636f](https://github.com/prezero/hermes-sf/commit/d32636f02bc13f5f1e76f712f219ee037da2f86a))


### Bug Fixes

* **HERMES-1241:** initializing waypoints empty ([#2121](https://github.com/prezero/hermes-sf/issues/2121)) ([a64e18f](https://github.com/prezero/hermes-sf/commit/a64e18fbb002fd6450eb9b1bafcdfdff2f2669f6))

## [4.29.0](https://github.com/prezero/hermes-sf/compare/v4.28.0...v4.29.0) (2024-09-25)


### Features

* Update es config and translations ([#2117](https://github.com/prezero/hermes-sf/issues/2117)) ([99e8de9](https://github.com/prezero/hermes-sf/commit/99e8de97e821af225d56304fe4fb1e1ff411c247))

## [4.28.0](https://github.com/prezero/hermes-sf/compare/v4.27.0...v4.28.0) (2024-09-25)


### Features

* **HERMES-1139:** Get master tour by external id endpoint ([#2110](https://github.com/prezero/hermes-sf/issues/2110)) ([467c2f7](https://github.com/prezero/hermes-sf/commit/467c2f7560b8c03a7facce8e37f2109e79bf6e03))
* **HERMES-1140:** Assigning master tours from SAP Germany to order ([#2113](https://github.com/prezero/hermes-sf/issues/2113)) ([c566aa8](https://github.com/prezero/hermes-sf/commit/c566aa8e8077d22fe85991a9ea34abe12b739319))
* **HERMES-1142:** Portal track data ([#2112](https://github.com/prezero/hermes-sf/issues/2112)) ([a517cee](https://github.com/prezero/hermes-sf/commit/a517cee651f71e9b302e324bb1d53e9e28b2a580))


### Bug Fixes

* Allow null bearing for tracking ([#2116](https://github.com/prezero/hermes-sf/issues/2116)) ([b0cbb10](https://github.com/prezero/hermes-sf/commit/b0cbb1099604c9f21cb2b855896193f3a731e852))
* Moved master tours to from order dto to tour dto ([#2115](https://github.com/prezero/hermes-sf/issues/2115)) ([f0c5245](https://github.com/prezero/hermes-sf/commit/f0c524500d777e5c47b05d8e32e383171bae5853))


### Tests

* **HERMES-1217:** time adjust tests ([#2114](https://github.com/prezero/hermes-sf/issues/2114)) ([399f2c9](https://github.com/prezero/hermes-sf/commit/399f2c91e9ea551a82c1003550eb0761bc262690))

## [4.27.0](https://github.com/prezero/hermes-sf/compare/v4.26.0...v4.27.0) (2024-09-24)


### Features

* **HERMES-1138:** mastertour portal ep ([#2105](https://github.com/prezero/hermes-sf/issues/2105)) ([c565df3](https://github.com/prezero/hermes-sf/commit/c565df31865ef7bdc3b2864ae9378a9674da01da))
* Update es config ([#2109](https://github.com/prezero/hermes-sf/issues/2109)) ([e0e7384](https://github.com/prezero/hermes-sf/commit/e0e7384c508b755dbd742bf1017b6e5cb42fa968))
* Update taskgroup type names ([#2108](https://github.com/prezero/hermes-sf/issues/2108)) ([b1a080a](https://github.com/prezero/hermes-sf/commit/b1a080a8c51329ce0f94550e0ef1c2dfa1648084))


### Bug Fixes

* **HERMES-1199:** length fix ([#2104](https://github.com/prezero/hermes-sf/issues/2104)) ([caa7b7d](https://github.com/prezero/hermes-sf/commit/caa7b7d9fe4eb41afa33f26e35aa11e49277470a))
* set tracking date as immutable ([#2106](https://github.com/prezero/hermes-sf/issues/2106)) ([2e49651](https://github.com/prezero/hermes-sf/commit/2e496513808c656a1ea1cd86653517ac4bc0179b))

## [4.26.0](https://github.com/prezero/hermes-sf/compare/v4.25.0...v4.26.0) (2024-09-23)


### Features

* added date as criterium ([#2100](https://github.com/prezero/hermes-sf/issues/2100)) ([0431d5e](https://github.com/prezero/hermes-sf/commit/0431d5eca7611d295cc09847524c010d6e65fc5b))
* es config update ([#2103](https://github.com/prezero/hermes-sf/issues/2103)) ([eb12e70](https://github.com/prezero/hermes-sf/commit/eb12e702c326877ef28c4ae3d4ef14e87dbe1c33))
* **HERMES-1141:** Store tracking data from mobile app ([#2096](https://github.com/prezero/hermes-sf/issues/2096)) ([2f3c90f](https://github.com/prezero/hermes-sf/commit/2f3c90f3882696ecbd3a6012a1c632b1037ac162))
* **HERMES-1146:** Order list tour ([#2101](https://github.com/prezero/hermes-sf/issues/2101)) ([b824f42](https://github.com/prezero/hermes-sf/commit/b824f42b58d980f2fbc3fc9edc7ab64f928e3d1d))
* increased default ([b3761b0](https://github.com/prezero/hermes-sf/commit/b3761b093df040d12af34ddf8a4d0de287e02ea2))
* increased per-page-default ([#2102](https://github.com/prezero/hermes-sf/issues/2102)) ([c853100](https://github.com/prezero/hermes-sf/commit/c853100c2206ee801f0a6e945a02c6dc03c7acc8))

## [4.25.0](https://github.com/prezero/hermes-sf/compare/v4.24.0...v4.25.0) (2024-09-23)


### Features

* Update nl config fixtures ([#2097](https://github.com/prezero/hermes-sf/issues/2097)) ([8bd8664](https://github.com/prezero/hermes-sf/commit/8bd8664dee17c1832ebd301020555e8186657e1e))

## [4.24.0](https://github.com/prezero/hermes-sf/compare/v4.23.2...v4.24.0) (2024-09-20)


### Features

* **HERMES-1119:** Point of interest management ([#2090](https://github.com/prezero/hermes-sf/issues/2090)) ([6c64b21](https://github.com/prezero/hermes-sf/commit/6c64b216b7250daa3cf99f0d0d0175756b31211f))


### Bug Fixes

* app timeref bug ([#2095](https://github.com/prezero/hermes-sf/issues/2095)) ([a81aba1](https://github.com/prezero/hermes-sf/commit/a81aba1bdc9d3ad535a2829230df500cedfafc44))

## [4.23.2](https://github.com/prezero/hermes-sf/compare/v4.23.1...v4.23.2) (2024-09-20)


### Bug Fixes

* Offset time in correct direction ([#2091](https://github.com/prezero/hermes-sf/issues/2091)) ([e70b4d2](https://github.com/prezero/hermes-sf/commit/e70b4d26900c3e5797734822e3f7235a62a5a933))

## [4.23.1](https://github.com/prezero/hermes-sf/compare/v4.23.0...v4.23.1) (2024-09-17)


### Tests

* fixed log-call ([#2088](https://github.com/prezero/hermes-sf/issues/2088)) ([7f5d71b](https://github.com/prezero/hermes-sf/commit/7f5d71be0b55d04266dddf383a442f2244d3b484))

## [4.23.0](https://github.com/prezero/hermes-sf/compare/v4.22.0...v4.23.0) (2024-09-17)


### Features

* **HERMES-1087:** Sync database to BigQuery ([#2085](https://github.com/prezero/hermes-sf/issues/2085)) ([2fc88e8](https://github.com/prezero/hermes-sf/commit/2fc88e8e48a1051ec0f2033e0de26576286ba245))


### Tests

* adding log ([#2087](https://github.com/prezero/hermes-sf/issues/2087)) ([c712ae2](https://github.com/prezero/hermes-sf/commit/c712ae22a85d55feb7e2ddb48ae82259c8a97843))

## [4.22.0](https://github.com/prezero/hermes-sf/compare/v4.21.2...v4.22.0) (2024-09-17)


### Features

* Hermes vehicle control ([#2071](https://github.com/prezero/hermes-sf/issues/2071)) ([f2920ed](https://github.com/prezero/hermes-sf/commit/f2920eda799558b603cabe1b0188b8f0a19b53aa))

## [4.21.2](https://github.com/prezero/hermes-sf/compare/v4.21.1...v4.21.2) (2024-09-16)


### Tests

* comparing structure ([#2082](https://github.com/prezero/hermes-sf/issues/2082)) ([7fc359f](https://github.com/prezero/hermes-sf/commit/7fc359f9e6bcf49984b95dfa843b4f4ac2373dc8))

## [4.21.1](https://github.com/prezero/hermes-sf/compare/v4.21.0...v4.21.1) (2024-09-13)


### Bug Fixes

* ref problems mapping ([#2080](https://github.com/prezero/hermes-sf/issues/2080)) ([c528037](https://github.com/prezero/hermes-sf/commit/c5280376740475189478bd1f899e3a7e3dc83134))

## [4.21.0](https://github.com/prezero/hermes-sf/compare/v4.20.0...v4.21.0) (2024-09-13)


### Features

* Update translations to v45 ([#2078](https://github.com/prezero/hermes-sf/issues/2078)) ([2042716](https://github.com/prezero/hermes-sf/commit/2042716cd8d8675e46e25560b46f553d16b637d5))

## [4.20.0](https://github.com/prezero/hermes-sf/compare/v4.19.1...v4.20.0) (2024-09-12)


### Features

* Update translations to v44 ([#2076](https://github.com/prezero/hermes-sf/issues/2076)) ([f9ce4bc](https://github.com/prezero/hermes-sf/commit/f9ce4bc9c687d1da28a7802574c4e935a8aa2dfd))

## [4.19.1](https://github.com/prezero/hermes-sf/compare/v4.19.0...v4.19.1) (2024-09-12)


### Bug Fixes

* username uppercase ([#2074](https://github.com/prezero/hermes-sf/issues/2074)) ([a293d85](https://github.com/prezero/hermes-sf/commit/a293d851410d2c3b2dce9fc67f431d15afcc841c))

## [4.19.0](https://github.com/prezero/hermes-sf/compare/v4.18.0...v4.19.0) (2024-09-11)


### Features

* **HERMES-1088:** Ensure modified at is always present ([#2072](https://github.com/prezero/hermes-sf/issues/2072)) ([786ef6b](https://github.com/prezero/hermes-sf/commit/786ef6b9ed055f1e6c34e965230e8b6a9fac5f81))

## [4.18.0](https://github.com/prezero/hermes-sf/compare/v4.17.0...v4.18.0) (2024-09-10)


### Features

* Update translation tags ([#2069](https://github.com/prezero/hermes-sf/issues/2069)) ([f231694](https://github.com/prezero/hermes-sf/commit/f2316942abad2c0a1e215030dd93d8e19dc1e444))

## [4.17.0](https://github.com/prezero/hermes-sf/compare/v4.16.0...v4.17.0) (2024-09-10)


### Features

* Add equipment types for es skiplifter and hooklifter ([#2067](https://github.com/prezero/hermes-sf/issues/2067)) ([5b212a7](https://github.com/prezero/hermes-sf/commit/5b212a737f5aa0922373568040f68b16f88b8150))

## [4.16.0](https://github.com/prezero/hermes-sf/compare/v4.15.0...v4.16.0) (2024-09-09)


### Features

* Update es config ([#2065](https://github.com/prezero/hermes-sf/issues/2065)) ([487ab0e](https://github.com/prezero/hermes-sf/commit/487ab0e4486c4acba7f36d5350027843d023fca3))

## [4.15.0](https://github.com/prezero/hermes-sf/compare/v4.14.0...v4.15.0) (2024-09-09)


### Features

* Update es config ([#2063](https://github.com/prezero/hermes-sf/issues/2063)) ([3295de7](https://github.com/prezero/hermes-sf/commit/3295de7667fc7bfaf17ba6a0e5a229dfe67192c0))

## [4.14.0](https://github.com/prezero/hermes-sf/compare/v4.13.0...v4.14.0) (2024-09-09)


### Features

* Update es config fixtures ([#2062](https://github.com/prezero/hermes-sf/issues/2062)) ([ef2fe0d](https://github.com/prezero/hermes-sf/commit/ef2fe0d6b0821f0bfebe140bb5fe2b4ac2b3f563))
* Update translations to v42 ([#2059](https://github.com/prezero/hermes-sf/issues/2059)) ([11a171a](https://github.com/prezero/hermes-sf/commit/11a171a1ba8dcde13f6d0383dfba1e84075c10ed))


### Bug Fixes

* translation v42 ([#2061](https://github.com/prezero/hermes-sf/issues/2061)) ([896c45c](https://github.com/prezero/hermes-sf/commit/896c45cda22d684010c9c40d489fb6dda0346165))

## [4.13.0](https://github.com/prezero/hermes-sf/compare/v4.12.0...v4.13.0) (2024-09-06)


### Features

* Set order type to translation tag ([#2057](https://github.com/prezero/hermes-sf/issues/2057)) ([e23e756](https://github.com/prezero/hermes-sf/commit/e23e756732c34b47d1ec400bc3d48f755101bbbe))

## [4.12.0](https://github.com/prezero/hermes-sf/compare/v4.11.1...v4.12.0) (2024-09-05)


### Features

* Update es config ([#2054](https://github.com/prezero/hermes-sf/issues/2054)) ([6055f18](https://github.com/prezero/hermes-sf/commit/6055f1894abeee700d34e873b7c51a882203b20b))


### Code Refactoring

* **HERMES-1028:** Use symfony attributes for both serialization & documentation ([#2055](https://github.com/prezero/hermes-sf/issues/2055)) ([219cc23](https://github.com/prezero/hermes-sf/commit/219cc232b549489a6fba0b52a13b80d7959c0f46))

## [4.11.1](https://github.com/prezero/hermes-sf/compare/v4.11.0...v4.11.1) (2024-09-04)


### Code Refactoring

* **HERMES-998:** Get tour endpoint ([#2052](https://github.com/prezero/hermes-sf/issues/2052)) ([f1974a2](https://github.com/prezero/hermes-sf/commit/f1974a23645c338a9b888137e583d2e842afeb7b))

## [4.11.0](https://github.com/prezero/hermes-sf/compare/v4.10.0...v4.11.0) (2024-09-03)


### Features

* nl config update ([#2050](https://github.com/prezero/hermes-sf/issues/2050)) ([d846c66](https://github.com/prezero/hermes-sf/commit/d846c66bb1b657b40d36c82208f35f066e40f28c))


### Bug Fixes

* **HERMES-1006:** fixed annotation ([#2049](https://github.com/prezero/hermes-sf/issues/2049)) ([5fd252c](https://github.com/prezero/hermes-sf/commit/5fd252cfaeeb12605b920cb0813488cc1da508bc))

## [4.10.0](https://github.com/prezero/hermes-sf/compare/v4.9.1...v4.10.0) (2024-09-02)


### Features

* Added local log monitoring ([#2046](https://github.com/prezero/hermes-sf/issues/2046)) ([81ca0a9](https://github.com/prezero/hermes-sf/commit/81ca0a94808e99e9956d860d978f9bcad6d4d252))
* **HERMES-971:** API Filters implementation ([#2044](https://github.com/prezero/hermes-sf/issues/2044)) ([20f1a20](https://github.com/prezero/hermes-sf/commit/20f1a20f0edaad1d2d4e41c7f870a07f652b3007))
* **HERMES-991:** Feedback content files ([#2047](https://github.com/prezero/hermes-sf/issues/2047)) ([b51cc8e](https://github.com/prezero/hermes-sf/commit/b51cc8ed9a2abbfc6570f2537ffe5c792e0c85a8))


### Bug Fixes

* **HERMES-992:** user file input ([#2048](https://github.com/prezero/hermes-sf/issues/2048)) ([b5d311b](https://github.com/prezero/hermes-sf/commit/b5d311b30552120fce6cec065d5807d338e6cd34))

## [4.9.1](https://github.com/prezero/hermes-sf/compare/v4.9.0...v4.9.1) (2024-08-30)


### Bug Fixes

* adding postgre search path after connection ([#2041](https://github.com/prezero/hermes-sf/issues/2041)) ([be135b9](https://github.com/prezero/hermes-sf/commit/be135b972196e690233aeebacd9e9e621d1e0008))
* adding table schema ([#2040](https://github.com/prezero/hermes-sf/issues/2040)) ([e1089e6](https://github.com/prezero/hermes-sf/commit/e1089e66867f344c9ed15416d8539f9c489e20bd))
* **HERMES-795:** filtering out ended session-equipments ([#2037](https://github.com/prezero/hermes-sf/issues/2037)) ([1004cfc](https://github.com/prezero/hermes-sf/commit/1004cfca5f5fc6a1a977d990bd24d1312ebd9ff4))
* removing doctrine middleware ([7ab8874](https://github.com/prezero/hermes-sf/commit/7ab8874478589e11ccfba433cfb2f12b6a02d4a7))


### Tests

* **HERMES-985:** test book and count equipment correctly ([#2043](https://github.com/prezero/hermes-sf/issues/2043)) ([6802fc5](https://github.com/prezero/hermes-sf/commit/6802fc5226c355b55cedd669f29a77a547772b5a))
* **HERMES-985:** truck for new list-test ([#2042](https://github.com/prezero/hermes-sf/issues/2042)) ([d9e5afa](https://github.com/prezero/hermes-sf/commit/d9e5afa743071cd27364c5550791781cf9982338))

## [4.9.0](https://github.com/prezero/hermes-sf/compare/v4.8.0...v4.9.0) (2024-08-29)


### Features

* **HERMES-981:** Delete in two stages ([#2035](https://github.com/prezero/hermes-sf/issues/2035)) ([da5edab](https://github.com/prezero/hermes-sf/commit/da5edab896a52d3e31e8cda188b45a481aaaf209))


### Bug Fixes

* development deploy ([4185f8c](https://github.com/prezero/hermes-sf/commit/4185f8cdfc4d5150ee1d0a1c7ace23331e8ddca2))
* do not remove tests on dev build ([f4c5980](https://github.com/prezero/hermes-sf/commit/f4c5980fe0680651fdf94d058920abca39b0b365))

## [4.8.0](https://github.com/prezero/hermes-sf/compare/v4.7.3...v4.8.0) (2024-08-29)


### Features

* **HERMES-921:** Tour delete command ([#2003](https://github.com/prezero/hermes-sf/issues/2003)) ([0d2f8f7](https://github.com/prezero/hermes-sf/commit/0d2f8f7b59d87702cfa4abb0414364aea1cedd77))
* **HERMES-980:** Migrate communicator functionality to backend ([#2034](https://github.com/prezero/hermes-sf/issues/2034)) ([400037f](https://github.com/prezero/hermes-sf/commit/400037f9ba202bc9eb889614f60fff036f94ece3))

## [4.7.3](https://github.com/prezero/hermes-sf/compare/v4.7.2...v4.7.3) (2024-08-27)


### Bug Fixes

* update deployment & fixtures ([2847e24](https://github.com/prezero/hermes-sf/commit/2847e249485b0cd39fcc8b9f5ba741c592205c60))

## [4.7.2](https://github.com/prezero/hermes-sf/compare/v4.7.1...v4.7.2) (2024-08-26)


### Bug Fixes

* k8s prod deployment ([04547e4](https://github.com/prezero/hermes-sf/commit/04547e42f53e37918c88f7347ada32fa415e4cf4))
* update base php images ([aab6f9b](https://github.com/prezero/hermes-sf/commit/aab6f9b812466a478c06dfa559e1d2763657969e))

## [4.7.1](https://github.com/prezero/hermes-sf/compare/v4.7.0...v4.7.1) (2024-08-23)


### Bug Fixes

* new dump ([#2028](https://github.com/prezero/hermes-sf/issues/2028)) ([5723b62](https://github.com/prezero/hermes-sf/commit/5723b62ca4db1b06095bc87c6aaadd5b642663af))

## [4.7.0](https://github.com/prezero/hermes-sf/compare/v4.6.1...v4.7.0) (2024-08-23)


### Features

* **HERMES-535:** removed pending file handling ([#2026](https://github.com/prezero/hermes-sf/issues/2026)) ([b5bd25d](https://github.com/prezero/hermes-sf/commit/b5bd25d8eea0e85ea6a85cabb2ce987f6954ced4))

## [4.6.1](https://github.com/prezero/hermes-sf/compare/v4.6.0...v4.6.1) (2024-08-22)


### Bug Fixes

* update base php images ([1e0d672](https://github.com/prezero/hermes-sf/commit/1e0d6729fbbadeadc0f4af529aa6b659bdb4c73f))

## [4.6.0](https://github.com/prezero/hermes-sf/compare/v4.5.0...v4.6.0) (2024-08-22)


### Features

* increase queue shards ([#2024](https://github.com/prezero/hermes-sf/issues/2024)) ([47d4bc1](https://github.com/prezero/hermes-sf/commit/47d4bc14c40723a9a2e7e9ae9c5d4fc9a12baa13))


### Bug Fixes

* argo deploy wait only for applications to be synced ([a40db06](https://github.com/prezero/hermes-sf/commit/a40db067614e4df138738c6c6be086b85f86dc4b))


### Reverts

* argo deploy wait only for applications to be synced ([f6853bf](https://github.com/prezero/hermes-sf/commit/f6853bfa3b8e0f3200b63dcf1bd35cd8320a7f86))

## [4.5.0](https://github.com/prezero/hermes-sf/compare/v4.4.0...v4.5.0) (2024-08-21)


### Features

* **HERMES-970:** tour-status in dashboard ([#2020](https://github.com/prezero/hermes-sf/issues/2020)) ([16dca59](https://github.com/prezero/hermes-sf/commit/16dca59a5a0d8d0b7b6c77c10b411d7064e930f4))

## [4.4.0](https://github.com/prezero/hermes-sf/compare/v4.3.0...v4.4.0) (2024-08-21)


### Features

* **HERMES-953:** portal endpoint messagelist filter ([#2015](https://github.com/prezero/hermes-sf/issues/2015)) ([0b9effa](https://github.com/prezero/hermes-sf/commit/0b9effaceb965ecf63747fc0077bcf627b1e3eb2))
* **HERMES-961:** Thread first message excerpt limit ([#2018](https://github.com/prezero/hermes-sf/issues/2018)) ([c32eaa2](https://github.com/prezero/hermes-sf/commit/c32eaa23ef81371ed98580d32f8b74d977c2891a))


### Bug Fixes

* centrifugo urls ([fa031dc](https://github.com/prezero/hermes-sf/commit/fa031dc0d753c7ce3e92e8d5b91c7e740bd58555))

## [4.3.0](https://github.com/prezero/hermes-sf/compare/v4.2.6...v4.3.0) (2024-08-21)


### Features

* **HERMES-939:** user edit access ([#2014](https://github.com/prezero/hermes-sf/issues/2014)) ([96ad94a](https://github.com/prezero/hermes-sf/commit/96ad94a64bfac9cb7fc4e5ae2bc5c7a85de73a24))


### Bug Fixes

* **HERMES-989:** fixed scale-string to SAP ([#2016](https://github.com/prezero/hermes-sf/issues/2016)) ([2e8420b](https://github.com/prezero/hermes-sf/commit/2e8420bf5f0468be1717cec9ed3db12d18f1c740))

## [4.2.6](https://github.com/prezero/hermes-sf/compare/v4.2.5...v4.2.6) (2024-08-19)


### Bug Fixes

* adding e2e deployment workflow ([cfb7abd](https://github.com/prezero/hermes-sf/commit/cfb7abd2e8ea3688d78408f10beaf0ba06d29e83))

## [4.2.5](https://github.com/prezero/hermes-sf/compare/v4.2.4...v4.2.5) (2024-08-19)


### Bug Fixes

* backend update ([d67a8c1](https://github.com/prezero/hermes-sf/commit/d67a8c15f6d6de5de0e744cb5b492d83d61c3c1d))
* OIDC key rename ([278379c](https://github.com/prezero/hermes-sf/commit/278379cf0c45f84028b7829cd85ad11d71c549e7))

## [4.2.4](https://github.com/prezero/hermes-sf/compare/v4.2.3...v4.2.4) (2024-08-16)


### Bug Fixes

* k8s qa deployment ([b166d45](https://github.com/prezero/hermes-sf/commit/b166d459ca984bd2b59da8db084b260e9c0361c1))

## [4.2.3](https://github.com/prezero/hermes-sf/compare/v4.2.2...v4.2.3) (2024-08-16)


### Bug Fixes

* k8s qa deployment ([10dc8b9](https://github.com/prezero/hermes-sf/commit/10dc8b97d92d6db453f1532490d96e1475fd3510))
* k8s qa deployment ([878d4ab](https://github.com/prezero/hermes-sf/commit/878d4ab3f13b43e0cb63edfddaeda6780a947a84))
* k8s qa deployment ([69d5a33](https://github.com/prezero/hermes-sf/commit/69d5a33c4ea47596b5435a3b9702ba4ae89f2d76))
* k8s qa deployment ([6e43f2a](https://github.com/prezero/hermes-sf/commit/6e43f2abc0720f11510c072819bcb4ba2afacb8c))

## [4.2.2](https://github.com/prezero/hermes-sf/compare/v4.2.1...v4.2.2) (2024-08-16)


### Bug Fixes

* k8s qa deployment ([db21c98](https://github.com/prezero/hermes-sf/commit/db21c98d92d15434b028d318e1d27ea50945ca36))

## [4.2.1](https://github.com/prezero/hermes-sf/compare/v4.2.0...v4.2.1) (2024-08-16)


### Bug Fixes

* k8s qa deployment ([f464eb3](https://github.com/prezero/hermes-sf/commit/f464eb39382abda44eba8c353b09ac8482579fbf))

## [4.2.0](https://github.com/prezero/hermes-sf/compare/v4.1.0...v4.2.0) (2024-08-16)


### Features

* k8s qa deployment ([8259c0f](https://github.com/prezero/hermes-sf/commit/8259c0fd97068057dbede70dbe71682cf16fa591))


### Bug Fixes

* k8s qa deployment ([816a7f9](https://github.com/prezero/hermes-sf/commit/816a7f99a487b70aa6752aaa92504a774dab02e1))

## [4.1.0](https://github.com/prezero/hermes-sf/compare/v4.0.0...v4.1.0) (2024-08-16)


### Features

* k8s qa deployment ([3142099](https://github.com/prezero/hermes-sf/commit/3142099e5fba56c73382dd760a69426c0f73e625))
* k8s qa deployment ([691fea6](https://github.com/prezero/hermes-sf/commit/691fea63502e07f7b3187ac1f63fc9b0c21882c1))

## [4.0.0](https://github.com/prezero/hermes-sf/compare/v3.53.0...v4.0.0) (2024-08-16)


### ⚠ BREAKING CHANGES

* **HERMES-370:** Migrate to k8s ([#1995](https://github.com/prezero/hermes-sf/issues/1995))

### Features

* **HERMES-370:** Migrate to k8s ([#1995](https://github.com/prezero/hermes-sf/issues/1995)) ([96f2701](https://github.com/prezero/hermes-sf/commit/96f2701a2fbb6bbd9c3445a9ce568e8b6d5a0ff8))

## [3.53.0](https://github.com/prezero/hermes-sf/compare/v3.52.0...v3.53.0) (2024-08-16)


### Features

* **HERMES-943:** faq access ([#2002](https://github.com/prezero/hermes-sf/issues/2002)) ([f65d742](https://github.com/prezero/hermes-sf/commit/f65d7422ebaf05a776ee6d279e14141cbf948368))

## [3.52.0](https://github.com/prezero/hermes-sf/compare/v3.51.0...v3.52.0) (2024-08-16)


### Features

* **HERMES-948:** country branch ep pagination ([#1998](https://github.com/prezero/hermes-sf/issues/1998)) ([df2f5dd](https://github.com/prezero/hermes-sf/commit/df2f5dda0ea9a09c0b29633df95108cf22fc2348))


### Bug Fixes

* fixed possible gap in array-index ([#1999](https://github.com/prezero/hermes-sf/issues/1999)) ([cb06f7d](https://github.com/prezero/hermes-sf/commit/cb06f7de21f33afcb5fc4a0e37fc382fdbef876d))

## [3.51.0](https://github.com/prezero/hermes-sf/compare/v3.50.1...v3.51.0) (2024-08-16)


### Features

* **HERMES-938:** 938 942 user patch ([#1996](https://github.com/prezero/hermes-sf/issues/1996)) ([301a48d](https://github.com/prezero/hermes-sf/commit/301a48d37cfa120305a3988c29b1f029736ce4c4))

## [3.50.1](https://github.com/prezero/hermes-sf/compare/v3.50.0...v3.50.1) (2024-08-12)


### Bug Fixes

* fixed translation ([#1993](https://github.com/prezero/hermes-sf/issues/1993)) ([2811c0a](https://github.com/prezero/hermes-sf/commit/2811c0a05089e61eaa725c3fdce160cf20b2198c))

## [3.50.0](https://github.com/prezero/hermes-sf/compare/v3.49.0...v3.50.0) (2024-08-12)


### Features

* updated translation, geotab-test-account ([#1991](https://github.com/prezero/hermes-sf/issues/1991)) ([16c991b](https://github.com/prezero/hermes-sf/commit/16c991be078bb8238796ae8ff6237b07e4bb8cb0))

## [3.49.0](https://github.com/prezero/hermes-sf/compare/v3.48.0...v3.49.0) (2024-08-12)


### Features

* new translations ([#1989](https://github.com/prezero/hermes-sf/issues/1989)) ([c994153](https://github.com/prezero/hermes-sf/commit/c994153a97aeb369d96bb261d4f1ee7904b36f24))

## [3.48.0](https://github.com/prezero/hermes-sf/compare/v3.47.1...v3.48.0) (2024-08-12)


### Features

* new translations ([#1987](https://github.com/prezero/hermes-sf/issues/1987)) ([fe6e45a](https://github.com/prezero/hermes-sf/commit/fe6e45adf07b30a9d64921c0a352da6c73e3e86f))

## [3.47.1](https://github.com/prezero/hermes-sf/compare/v3.47.0...v3.47.1) (2024-08-12)


### Bug Fixes

* adding debug log message for session invalid ([#1985](https://github.com/prezero/hermes-sf/issues/1985)) ([9cd0a21](https://github.com/prezero/hermes-sf/commit/9cd0a2101f1ad11d40fc0f18fcf0c44eaf07572f))

## [3.47.0](https://github.com/prezero/hermes-sf/compare/v3.46.1...v3.47.0) (2024-08-08)


### Features

* **HERMES-896:** Spain FTP Upload functionality ([3eaf434](https://github.com/prezero/hermes-sf/commit/3eaf4349f41adf93c50e7814f959e225481fd892))

## [3.46.1](https://github.com/prezero/hermes-sf/compare/v3.46.0...v3.46.1) (2024-08-08)


### Bug Fixes

* missing branch & country access for user details endpoint ([9b7efa3](https://github.com/prezero/hermes-sf/commit/9b7efa3329ef5fa410e42ffb9b47d376a3b2d102))

## [3.46.0](https://github.com/prezero/hermes-sf/compare/v3.45.0...v3.46.0) (2024-08-08)


### Features

* **HERMES-915:** Case insensitive search for portal equipment & staff ([#1981](https://github.com/prezero/hermes-sf/issues/1981)) ([8bfffce](https://github.com/prezero/hermes-sf/commit/8bfffceb2af4e349ecf5ca99d1c6be69b9767ad2))

## [3.45.0](https://github.com/prezero/hermes-sf/compare/v3.44.0...v3.45.0) (2024-08-07)


### Features

* **HERMES-912:** Optimzing portal equipment & staff collection endpoints ([#1979](https://github.com/prezero/hermes-sf/issues/1979)) ([cd1d6ae](https://github.com/prezero/hermes-sf/commit/cd1d6ae01912ad1fff3ddab8b24a96fad9fa2140))

## [3.44.0](https://github.com/prezero/hermes-sf/compare/v3.43.1...v3.44.0) (2024-08-07)


### Features

* **HERMES-896:** Spain FTP Upload functionality ([#1976](https://github.com/prezero/hermes-sf/issues/1976)) ([6fe4e8d](https://github.com/prezero/hermes-sf/commit/6fe4e8d47f4be741f20d43c6c5b1eeabbeb7e9e9))
* **HERMES-911:** Include necessary files in portal order files endpoint ([#1978](https://github.com/prezero/hermes-sf/issues/1978)) ([f13e2f2](https://github.com/prezero/hermes-sf/commit/f13e2f2c064bd928787c4feaa6fb9ab6e0d59afb))

## [3.43.1](https://github.com/prezero/hermes-sf/compare/v3.43.0...v3.43.1) (2024-08-06)


### Bug Fixes

* portal response to json ([830b118](https://github.com/prezero/hermes-sf/commit/830b118c841c498bfe34e8498189cd0a833c72e7))
* response log from portal api ([cb56062](https://github.com/prezero/hermes-sf/commit/cb56062db7d0b80227b7f945ec09fde9fc2e76ca))

## [3.43.0](https://github.com/prezero/hermes-sf/compare/v3.42.1...v3.43.0) (2024-08-05)


### Features

* **HERMES-867:** Hermes device message role ([#1973](https://github.com/prezero/hermes-sf/issues/1973)) ([cb89b8d](https://github.com/prezero/hermes-sf/commit/cb89b8d14853f015da818d1bd626e4ea0158e692))

## [3.42.1](https://github.com/prezero/hermes-sf/compare/v3.42.0...v3.42.1) (2024-08-05)


### Bug Fixes

* adding skipped to the allowed initial state for abandon task ([2008a87](https://github.com/prezero/hermes-sf/commit/2008a87f4f832392a175abcc518959bbe748fde5))
* user profile endpoints doc ([1a3aa28](https://github.com/prezero/hermes-sf/commit/1a3aa284fa936c219f1363518f5d69b348cb1751))

## [3.42.0](https://github.com/prezero/hermes-sf/compare/v3.41.0...v3.42.0) (2024-08-02)


### Features

* Update es config ([#1970](https://github.com/prezero/hermes-sf/issues/1970)) ([b34c35b](https://github.com/prezero/hermes-sf/commit/b34c35b842eb645857950023d233a3279e986fa0))

## [3.41.0](https://github.com/prezero/hermes-sf/compare/v3.40.0...v3.41.0) (2024-08-02)


### Features

* **HERMES-864:** Device message thread search & excerpt ([#1969](https://github.com/prezero/hermes-sf/issues/1969)) ([ce59554](https://github.com/prezero/hermes-sf/commit/ce59554682022966111b2cdebc4af8c4f87dcf94))
* Spain config and translations ([#1968](https://github.com/prezero/hermes-sf/issues/1968)) ([143ec15](https://github.com/prezero/hermes-sf/commit/143ec15815f50fa648d3600565dc73b74014bcfc))
* Update datetime format for date and time elements ([#1966](https://github.com/prezero/hermes-sf/issues/1966)) ([f96f360](https://github.com/prezero/hermes-sf/commit/f96f36088e33859a0b5062580137a1d0db0c6125))

## [3.40.0](https://github.com/prezero/hermes-sf/compare/v3.39.0...v3.40.0) (2024-08-02)


### Features

* **HERMES-813:** Access checks on portal endpoints ([#1965](https://github.com/prezero/hermes-sf/issues/1965)) ([61a8ce4](https://github.com/prezero/hermes-sf/commit/61a8ce4467f17e0bf74da5b9f737f82418bb4917))
* **HERMES-818:** Search tours by date in portal ([#1959](https://github.com/prezero/hermes-sf/issues/1959)) ([dd3dd6c](https://github.com/prezero/hermes-sf/commit/dd3dd6c5931efe8c9363a833a1cc29f1b1de7a52))
* **HERMES-819:** Get list of orders inside a tour from portal ([#1962](https://github.com/prezero/hermes-sf/issues/1962)) ([48eb0d6](https://github.com/prezero/hermes-sf/commit/48eb0d6705cb777dbad27e8c52668b2faf217e1b))
* **HERMES-820:** Order file list for portal ([#1963](https://github.com/prezero/hermes-sf/issues/1963)) ([499162f](https://github.com/prezero/hermes-sf/commit/499162febb13987d2908d233da7f81d3866956e2))
* Update pz fixtures ([#1964](https://github.com/prezero/hermes-sf/issues/1964)) ([188d7fc](https://github.com/prezero/hermes-sf/commit/188d7fcdcac9ebc05eb80b3d1dc9f887f685151e))
* Update translations to v37 ([#1961](https://github.com/prezero/hermes-sf/issues/1961)) ([e7307c6](https://github.com/prezero/hermes-sf/commit/e7307c64562faab8c1f602b231a915a310d76960))

## [3.39.0](https://github.com/prezero/hermes-sf/compare/v3.38.0...v3.39.0) (2024-08-01)


### Features

* **HERMES-838:** Device message creation sync endpoint for app ([#1958](https://github.com/prezero/hermes-sf/issues/1958)) ([1fe6acf](https://github.com/prezero/hermes-sf/commit/1fe6acf19487a84690459edc073beef9192b7244))
* **HERMES-848:** Adding started from field in the device message thread ([#1954](https://github.com/prezero/hermes-sf/issues/1954)) ([8c53c04](https://github.com/prezero/hermes-sf/commit/8c53c0414d10d0ee2101b5a8ae6ce31ee9b65f55))
* **HERMES-850:** Mark messages as read when seen from portal ([#1956](https://github.com/prezero/hermes-sf/issues/1956)) ([843cfdd](https://github.com/prezero/hermes-sf/commit/843cfdd537b743e318b5e0adc4b186f2f8514fe1))
* Update translations to v36 ([#1953](https://github.com/prezero/hermes-sf/issues/1953)) ([c6d4c99](https://github.com/prezero/hermes-sf/commit/c6d4c9941b3616e02d76dc0f599222686c93d9b6))


### Bug Fixes

* **HERMES-851:** Attachment validation on device message creation ([#1957](https://github.com/prezero/hermes-sf/issues/1957)) ([cd45a3c](https://github.com/prezero/hermes-sf/commit/cd45a3ccebfb3e5a6d4927352c9777630d036596))

## [3.38.0](https://github.com/prezero/hermes-sf/compare/v3.37.0...v3.38.0) (2024-07-31)


### Features

* **HERMES-837:** Adding search capabilities to staff and equipment ([#1950](https://github.com/prezero/hermes-sf/issues/1950)) ([9261bc5](https://github.com/prezero/hermes-sf/commit/9261bc56949c3eed39a1125ada3516304abd8dcd))
* **HERMES-843:** Endpoints for checking if portal username or email exist ([#1948](https://github.com/prezero/hermes-sf/issues/1948)) ([fe39ccd](https://github.com/prezero/hermes-sf/commit/fe39ccd504e3e71b14a5c49e25c4e585a11095ff))
* scaledefinition update ([#1952](https://github.com/prezero/hermes-sf/issues/1952)) ([536adf2](https://github.com/prezero/hermes-sf/commit/536adf224643a2d071630c838f5b0b049e0a6ca0))


### Bug Fixes

* **HERMES-849:** Fix country admin access for device message threads ([#1951](https://github.com/prezero/hermes-sf/issues/1951)) ([048f5ad](https://github.com/prezero/hermes-sf/commit/048f5ad069a1a9c9b42fe408176ffe513dbad93b))

## [3.37.0](https://github.com/prezero/hermes-sf/compare/v3.36.0...v3.37.0) (2024-07-30)


### Features

* **HERMES-788:** Portal users get & list endpoints, permissions ([#1939](https://github.com/prezero/hermes-sf/issues/1939)) ([036f18f](https://github.com/prezero/hermes-sf/commit/036f18f66bda46d6c7877d711d374bc520ea25b2))
* **HERMES-812:** Edit portal user endpoint ([#1944](https://github.com/prezero/hermes-sf/issues/1944)) ([e22f200](https://github.com/prezero/hermes-sf/commit/e22f20035535002185c266d5489b5f5b0d54669f))
* **HERMES-834:** Portal file upload ([#1942](https://github.com/prezero/hermes-sf/issues/1942)) ([d21ba75](https://github.com/prezero/hermes-sf/commit/d21ba75dee6f3bd6f07aac268b38fa02effef7af))
* **HERMES-836:** Adding message origin as from, different from username ([#1945](https://github.com/prezero/hermes-sf/issues/1945)) ([d62cb60](https://github.com/prezero/hermes-sf/commit/d62cb60f5ca93023117f8158b2eeccbeba666b92))
* Update es config and translations ([#1941](https://github.com/prezero/hermes-sf/issues/1941)) ([a18544e](https://github.com/prezero/hermes-sf/commit/a18544efa40a8b1a808a49201b9b896e78933c0a))
* Update nl config fixtures ([#1946](https://github.com/prezero/hermes-sf/issues/1946)) ([4ba3193](https://github.com/prezero/hermes-sf/commit/4ba319340bd70b4bc55c829219c6929693f80bec))
* Update pz fixtures ([#1938](https://github.com/prezero/hermes-sf/issues/1938)) ([6d07990](https://github.com/prezero/hermes-sf/commit/6d07990634277ea51d67b4ef9685fe6440189f95))
* Update scale definition ([#1943](https://github.com/prezero/hermes-sf/issues/1943)) ([95ba027](https://github.com/prezero/hermes-sf/commit/95ba0275411d507616b758569842226a1a31b5da))


### Bug Fixes

* update scaledefinition var names ([#1947](https://github.com/prezero/hermes-sf/issues/1947)) ([e9fc78e](https://github.com/prezero/hermes-sf/commit/e9fc78e817166bfa6f10e953deb59ddba60b173b))

## [3.36.0](https://github.com/prezero/hermes-sf/compare/v3.35.0...v3.36.0) (2024-07-29)


### Features

* **HERMES-780:** Ensure username & email are unique ([#1936](https://github.com/prezero/hermes-sf/issues/1936)) ([839e884](https://github.com/prezero/hermes-sf/commit/839e8844de917855db2d00706030be522e76f081))
* **HERMES-811:** filter equipment position list ([#1933](https://github.com/prezero/hermes-sf/issues/1933)) ([d80bb49](https://github.com/prezero/hermes-sf/commit/d80bb490a9a0e166946d9702e2b29a5f7a10f193))


### Bug Fixes

* element type rename ([#1937](https://github.com/prezero/hermes-sf/issues/1937)) ([dfd69b5](https://github.com/prezero/hermes-sf/commit/dfd69b59303c6f17796d447707ab024eb4b768f6))

## [3.35.0](https://github.com/prezero/hermes-sf/compare/v3.34.0...v3.35.0) (2024-07-26)


### Features

* **HERMES-679:** Mark massages as read from app ([#1925](https://github.com/prezero/hermes-sf/issues/1925)) ([1987050](https://github.com/prezero/hermes-sf/commit/1987050ef5174995649f4e700826b91b54185812))
* **HERMES-756:** portal user creation ([#1923](https://github.com/prezero/hermes-sf/issues/1923)) ([814f7f6](https://github.com/prezero/hermes-sf/commit/814f7f6f8d17741f019735b67caf2cfd6fd4fb0b))
* **HERMES-805:** Staff filtering ([#1926](https://github.com/prezero/hermes-sf/issues/1926)) ([ed310bd](https://github.com/prezero/hermes-sf/commit/ed310bd657751f94a372aa6ca170982142d6ad44))
* **HERMES-806:** Equipment filtering ([#1929](https://github.com/prezero/hermes-sf/issues/1929)) ([31a0bf9](https://github.com/prezero/hermes-sf/commit/31a0bf953b026ead42aebe71dd5ad1771ec80704))
* **HERMES-815:** Adding user permissions filtering for portal dashboard results ([#1932](https://github.com/prezero/hermes-sf/issues/1932)) ([1ec2dc3](https://github.com/prezero/hermes-sf/commit/1ec2dc3653479a8f2e15331b5b552bca164ab412))
* **HERMES-817:** changed container action ([#1930](https://github.com/prezero/hermes-sf/issues/1930)) ([8cab567](https://github.com/prezero/hermes-sf/commit/8cab56711030bc6022339bce5f4eced3240a1041))
* Update es config ([#1934](https://github.com/prezero/hermes-sf/issues/1934)) ([20ec11e](https://github.com/prezero/hermes-sf/commit/20ec11e9a6ef3c53c56f072d3862858e3a3ea21b))
* Update pz and lux config ([#1931](https://github.com/prezero/hermes-sf/issues/1931)) ([443ba5d](https://github.com/prezero/hermes-sf/commit/443ba5d806a71e3509dc338df7f6655e6533c017))


### Bug Fixes

* Adding more data to centrifugo device message notifications ([#1928](https://github.com/prezero/hermes-sf/issues/1928)) ([d21c865](https://github.com/prezero/hermes-sf/commit/d21c86505cccea8b75056da45f9de9ee3406c3f7))

## [3.34.0](https://github.com/prezero/hermes-sf/compare/v3.33.0...v3.34.0) (2024-07-25)


### Features

* Update es config ([#1924](https://github.com/prezero/hermes-sf/issues/1924)) ([2ec5df6](https://github.com/prezero/hermes-sf/commit/2ec5df6bd3568f7e1d3aac8679ae443cfc7f174a))
* Update translations to version 35 ([#1920](https://github.com/prezero/hermes-sf/issues/1920)) ([507c16d](https://github.com/prezero/hermes-sf/commit/507c16db43e9fc825be0574505b4c49755c60047))


### Bug Fixes

* fix base image (realpath cache setting overwritten) ([#1922](https://github.com/prezero/hermes-sf/issues/1922)) ([fc79047](https://github.com/prezero/hermes-sf/commit/fc7904741d0af8aa590e640da60e375803d72dd9))

## [3.33.0](https://github.com/prezero/hermes-sf/compare/v3.32.0...v3.33.0) (2024-07-25)


### Features

* **HERMES-673:** Device messages threads list endpoint ([#1915](https://github.com/prezero/hermes-sf/issues/1915)) ([75ebbe0](https://github.com/prezero/hermes-sf/commit/75ebbe03dd41371ce5038f6e99802c86cf8cda47))
* **HERMES-675:** List of messages inside a thread ([#1916](https://github.com/prezero/hermes-sf/issues/1916)) ([dcc90ab](https://github.com/prezero/hermes-sf/commit/dcc90ab9478cb92989376e22b5920f22f72bab26))
* **HERMES-724:** portal userlist ([#1914](https://github.com/prezero/hermes-sf/issues/1914)) ([93a4805](https://github.com/prezero/hermes-sf/commit/93a4805315ca9b7231f00e72a57aad90aefa728d))
* **HERMES-754:** Updating php, libraries & configuration ([#1919](https://github.com/prezero/hermes-sf/issues/1919)) ([99c2839](https://github.com/prezero/hermes-sf/commit/99c2839b2bed9e8aaef74301856ba8020603c90d))
* **HERMES-759:** Device message history endpoint ([#1917](https://github.com/prezero/hermes-sf/issues/1917)) ([998b154](https://github.com/prezero/hermes-sf/commit/998b1548661b79526a00722d9452f342c3b5ad4d))
* **HERMES-786:** Portal user group ([#1918](https://github.com/prezero/hermes-sf/issues/1918)) ([8b07481](https://github.com/prezero/hermes-sf/commit/8b07481a737aa1788c01885c1be77469cd4309ed))


### Performance Improvements

* **HERMES-731:** Optimize user & tenant queries ([#1909](https://github.com/prezero/hermes-sf/issues/1909)) ([45af4c7](https://github.com/prezero/hermes-sf/commit/45af4c746695d1cec6050951d5ce8bdd91d322ff))

## [3.32.0](https://github.com/prezero/hermes-sf/compare/v3.31.0...v3.32.0) (2024-07-23)


### Features

* **HERMES-764:** group-translator ([#1910](https://github.com/prezero/hermes-sf/issues/1910)) ([eaa5050](https://github.com/prezero/hermes-sf/commit/eaa5050a2b3e9aa98782058da647d2fd18564817))
* Spain fixture update ([#1912](https://github.com/prezero/hermes-sf/issues/1912)) ([898136c](https://github.com/prezero/hermes-sf/commit/898136c56e720ef2a20c5f0d152f43b93d86d605))

## [3.31.0](https://github.com/prezero/hermes-sf/compare/v3.30.0...v3.31.0) (2024-07-23)


### Features

* **HERMES-682:** Mobile app endpoint for sending messages from device ([#1908](https://github.com/prezero/hermes-sf/issues/1908)) ([b144034](https://github.com/prezero/hermes-sf/commit/b1440347a7a04962f53faa8989a49d4405607a60))
* **HERMES-712:** Portal endpoint branch list ([#1906](https://github.com/prezero/hermes-sf/issues/1906)) ([5661076](https://github.com/prezero/hermes-sf/commit/56610769594fe6c668c8dff23764b2c307fe448f))
* **HERMES-720:** permission objects ([#1907](https://github.com/prezero/hermes-sf/issues/1907)) ([ccd188f](https://github.com/prezero/hermes-sf/commit/ccd188fcb5d4b8a8f0ad2cc7f83a67edeae24305))


### Bug Fixes

* **HERMES-634:** Notification access endpoint ([#1903](https://github.com/prezero/hermes-sf/issues/1903)) ([1437182](https://github.com/prezero/hermes-sf/commit/1437182a0b05bdf7c6b06a6a54663186b594242b))
* **HERMES-735:** Redis persistent connections ([#1904](https://github.com/prezero/hermes-sf/issues/1904)) ([bcc93f8](https://github.com/prezero/hermes-sf/commit/bcc93f8083de78560dde63757d144e6da86bd313))

## [3.30.0](https://github.com/prezero/hermes-sf/compare/v3.29.1...v3.30.0) (2024-07-22)


### Features

* **Hermes-639:** user roles ([#1893](https://github.com/prezero/hermes-sf/issues/1893)) ([6c8ddc4](https://github.com/prezero/hermes-sf/commit/6c8ddc4bab40f8bcbcce2ccaeac84b1c2b0f369d))
* **HERMES-671:** Dispatcher creating and replying to message threads from the portal ([#1896](https://github.com/prezero/hermes-sf/issues/1896)) ([bda614c](https://github.com/prezero/hermes-sf/commit/bda614c395a39548206f37982c8e73aa40e75a68))
* **HERMES-713:** country list ([#1897](https://github.com/prezero/hermes-sf/issues/1897)) ([9781a4c](https://github.com/prezero/hermes-sf/commit/9781a4c88aa0c673ae4f252dd4a5b0812074d85a))


### Bug Fixes

* **HERMES-698:** Improve error logging ([#1901](https://github.com/prezero/hermes-sf/issues/1901)) ([e757b89](https://github.com/prezero/hermes-sf/commit/e757b89d466b76fb8b50f580ed045721f7c57793))
* **HERMES-705:** Removing logme integration ([#1902](https://github.com/prezero/hermes-sf/issues/1902)) ([5f7f908](https://github.com/prezero/hermes-sf/commit/5f7f90893bb8ef52048a512a402b8bdd7cdde8b1))

## [3.29.1](https://github.com/prezero/hermes-sf/compare/v3.29.0...v3.29.1) (2024-07-22)


### Bug Fixes

* removed old entity-tables from command ([#1898](https://github.com/prezero/hermes-sf/issues/1898)) ([2534c40](https://github.com/prezero/hermes-sf/commit/2534c40d3d809ab973fafdb7fe9731065e795ece))

## [3.29.0](https://github.com/prezero/hermes-sf/compare/v3.28.0...v3.29.0) (2024-07-19)


### Features

* **HERMES-625:** Rules to value objects ([#1858](https://github.com/prezero/hermes-sf/issues/1858)) ([8a5a1bf](https://github.com/prezero/hermes-sf/commit/8a5a1bf2636807d6cbe6f4925bdb10ee8bc1e223))
* **HERMES-628:** Centrifugo PoC ([#1867](https://github.com/prezero/hermes-sf/issues/1867)) ([af4e748](https://github.com/prezero/hermes-sf/commit/af4e748605bb18f8857aeb3b2a80c3cdf07ea99a))
* **HERMES-634:** Blackfire integration ([#1889](https://github.com/prezero/hermes-sf/issues/1889)) ([c04f54a](https://github.com/prezero/hermes-sf/commit/c04f54a6993326eb2f3e609a988cac2b44f69d96))
* **HERMES-634:** Blackfire integration ([#1892](https://github.com/prezero/hermes-sf/issues/1892)) ([1f82431](https://github.com/prezero/hermes-sf/commit/1f82431d466da7fc4c2e4cdedf4385c045d6ab97))
* **HERMES-641:** Caching last version data in versioned entities in the main entity ([#1881](https://github.com/prezero/hermes-sf/issues/1881)) ([ece2806](https://github.com/prezero/hermes-sf/commit/ece280685b4d4b46956cc744f07bd76edc323ec4))
* **HERMES-642:** User management branch has country ([#1895](https://github.com/prezero/hermes-sf/issues/1895)) ([38b2f73](https://github.com/prezero/hermes-sf/commit/38b2f73b9e6252926ad42fecac89046d9105a800))
* **HERMES-644:** Tracking External Ids ([#1890](https://github.com/prezero/hermes-sf/issues/1890)) ([af304fb](https://github.com/prezero/hermes-sf/commit/af304fb3064534ec0ec1de46650ac77b6b658686))
* **HERMES-659:** Automatic session closing ([#1882](https://github.com/prezero/hermes-sf/issues/1882)) ([3f1f461](https://github.com/prezero/hermes-sf/commit/3f1f46121dad448278839f89d3b91857fcbc6913))
* prod rules converted ([#1891](https://github.com/prezero/hermes-sf/issues/1891)) ([1bde1fb](https://github.com/prezero/hermes-sf/commit/1bde1fb66639c4b0cb593f3fc9c3af9c4a8385b6))


### Bug Fixes

* added missing env vars for centrifugo ([#1888](https://github.com/prezero/hermes-sf/issues/1888)) ([2567ee5](https://github.com/prezero/hermes-sf/commit/2567ee5de31d8c6c9d3a1662e6bc17d53c6248f4))
* migrations ([#1887](https://github.com/prezero/hermes-sf/issues/1887)) ([becb0d6](https://github.com/prezero/hermes-sf/commit/becb0d6decc2c8df5b953b31fba9d5f51d9ecf2f))

## [3.28.0](https://github.com/prezero/hermes-sf/compare/v3.27.1...v3.28.0) (2024-07-16)


### Features

* Update translations to v34 ([#1883](https://github.com/prezero/hermes-sf/issues/1883)) ([907772c](https://github.com/prezero/hermes-sf/commit/907772cf1d56fc8af206fed9548ccf26580d820e))

## [3.27.1](https://github.com/prezero/hermes-sf/compare/v3.27.0...v3.27.1) (2024-07-12)


### Bug Fixes

* **HERMES-645:** Min string length 0 ([#1879](https://github.com/prezero/hermes-sf/issues/1879)) ([3bf8afd](https://github.com/prezero/hermes-sf/commit/3bf8afd7dbef2f75b826fe51b7c5005c67e893a9))

## [3.27.0](https://github.com/prezero/hermes-sf/compare/v3.26.3...v3.27.0) (2024-07-12)


### Features

* **HERMES-645:** SAP DTOs documentation & validation ([#1875](https://github.com/prezero/hermes-sf/issues/1875)) ([8d89acc](https://github.com/prezero/hermes-sf/commit/8d89acc00e53fd9d5f9f76c895ac332a35d69b72))
* **HERMES-646:** tour name in dashboard ([#1874](https://github.com/prezero/hermes-sf/issues/1874)) ([10920d1](https://github.com/prezero/hermes-sf/commit/10920d168d8ee590926241d66cba0956dd5e5c91))
* **HERMES-654:** made client-phonenumber optional ([#1878](https://github.com/prezero/hermes-sf/issues/1878)) ([a6fbc96](https://github.com/prezero/hermes-sf/commit/a6fbc962e2b5c0efb2fa591afefe295c5fd4f83f))


### Bug Fixes

* **HERMES-653:** removed obsolete token from api-doc ([#1876](https://github.com/prezero/hermes-sf/issues/1876)) ([e7a8c81](https://github.com/prezero/hermes-sf/commit/e7a8c81722b38707614ff9ac8f7504af9eb2a05d))

## [3.26.3](https://github.com/prezero/hermes-sf/compare/v3.26.2...v3.26.3) (2024-07-11)


### Bug Fixes

* fixture-dump contained unexecuted migration ([#1872](https://github.com/prezero/hermes-sf/issues/1872)) ([332ee97](https://github.com/prezero/hermes-sf/commit/332ee972b6318c173670f6edd0888bc2e74a3016))

## [3.26.2](https://github.com/prezero/hermes-sf/compare/v3.26.1...v3.26.2) (2024-07-11)


### Bug Fixes

* better logging if tour ingest cannot be deserialized ([#1869](https://github.com/prezero/hermes-sf/issues/1869)) ([3c0c830](https://github.com/prezero/hermes-sf/commit/3c0c830fad2a935c94f33cc4da1b9ad129c5f092))


### Tests

* fixed id for connected device to test ([#1871](https://github.com/prezero/hermes-sf/issues/1871)) ([4965a10](https://github.com/prezero/hermes-sf/commit/4965a10218aef4ab3941d6bef4db7c21be2e2719))

## [3.26.1](https://github.com/prezero/hermes-sf/compare/v3.26.0...v3.26.1) (2024-07-11)


### Bug Fixes

* **HERMES-635:** Fix overlapping sap tour import ([#1863](https://github.com/prezero/hermes-sf/issues/1863)) ([e37e43b](https://github.com/prezero/hermes-sf/commit/e37e43b99c99d7796992b15e4bb72049e3d9bd9f))

## [3.26.0](https://github.com/prezero/hermes-sf/compare/v3.25.1...v3.26.0) (2024-07-10)


### Features

* **HERMES-622:** connected devices endpoint ([#1864](https://github.com/prezero/hermes-sf/issues/1864)) ([269a557](https://github.com/prezero/hermes-sf/commit/269a557343a25b202ddbcb1c02a3825834f59bf5))


### Bug Fixes

* **HERMES-638:** obsolete tg patch ([#1866](https://github.com/prezero/hermes-sf/issues/1866)) ([baf9a97](https://github.com/prezero/hermes-sf/commit/baf9a9781bd38954484af1da9c91677dcf728fd3))

## [3.25.1](https://github.com/prezero/hermes-sf/compare/v3.25.0...v3.25.1) (2024-07-09)


### Bug Fixes

* disable captainhook on image building ([64d0d2a](https://github.com/prezero/hermes-sf/commit/64d0d2a3ea9fb3518b1b1c3f39f27cf14639fb12))
* Fixing SapSpainObjectConfirmationMessage ([#1861](https://github.com/prezero/hermes-sf/issues/1861)) ([36123a0](https://github.com/prezero/hermes-sf/commit/36123a0bea604defaaa6b0ae609fa36a25f8af66))
* **HERMES-626:** better githook library ([#1859](https://github.com/prezero/hermes-sf/issues/1859)) ([cf87169](https://github.com/prezero/hermes-sf/commit/cf87169c8580b87125a40629004f4481a0fc5a00))

## [3.25.0](https://github.com/prezero/hermes-sf/compare/v3.24.0...v3.25.0) (2024-07-08)


### Features

* **HERMES-617:** FAQ List Global ([#1856](https://github.com/prezero/hermes-sf/issues/1856)) ([dfb2410](https://github.com/prezero/hermes-sf/commit/dfb2410d18090e39f8bc3911acd3002148b43a0d))

## [3.24.0](https://github.com/prezero/hermes-sf/compare/v3.23.0...v3.24.0) (2024-07-02)


### Features

* **HERMES-577:** scale element ([#1853](https://github.com/prezero/hermes-sf/issues/1853)) ([1341f2f](https://github.com/prezero/hermes-sf/commit/1341f2fd56f792d969338d3adbc40c5b6093cf4a))


### Bug Fixes

* **HERMES-618:** Pagination Bug Fix ([#1854](https://github.com/prezero/hermes-sf/issues/1854)) ([107027d](https://github.com/prezero/hermes-sf/commit/107027d47937c33e0703645f8c6b5dd152edef0d))

## [3.23.0](https://github.com/prezero/hermes-sf/compare/v3.22.1...v3.23.0) (2024-07-02)


### Features

* **HERMES-611:** Map Trip Removal ([#1852](https://github.com/prezero/hermes-sf/issues/1852)) ([15ca5fd](https://github.com/prezero/hermes-sf/commit/15ca5fdf124192982f4289da898e1c549c56194b))
* Update translations to version 33 ([#1850](https://github.com/prezero/hermes-sf/issues/1850)) ([75165a8](https://github.com/prezero/hermes-sf/commit/75165a81b03c0668d767365d44aee97aa3d968f4))

## [3.22.1](https://github.com/prezero/hermes-sf/compare/v3.22.0...v3.22.1) (2024-06-28)


### Bug Fixes

* **HERMES-600:** Portal dashboard date check fix ([#1848](https://github.com/prezero/hermes-sf/issues/1848)) ([9ae072c](https://github.com/prezero/hermes-sf/commit/9ae072c310017ee5dab4cdb982f7e4eb40964801))

## [3.22.0](https://github.com/prezero/hermes-sf/compare/v3.21.0...v3.22.0) (2024-06-28)


### Features

* **HERMES-586:** spain status call ([#1841](https://github.com/prezero/hermes-sf/issues/1841)) ([68fabe1](https://github.com/prezero/hermes-sf/commit/68fabe1f915ba8fc5458eae1a3a940925a6fa585))
* **HERMES-608:** changed ep-pattern for spain ([#1842](https://github.com/prezero/hermes-sf/issues/1842)) ([1a2e34d](https://github.com/prezero/hermes-sf/commit/1a2e34d2bfe895ece5751c0851504477f3ebd12a))


### Bug Fixes

* **HERMES-606:** Put back Redis resillency and refactor some session calls ([#1844](https://github.com/prezero/hermes-sf/issues/1844)) ([da26d2a](https://github.com/prezero/hermes-sf/commit/da26d2acdca6f8ad8710810151d9d7ced94732f8))

## [3.21.0](https://github.com/prezero/hermes-sf/compare/v3.20.1...v3.21.0) (2024-06-28)


### Features

* removing unnecessary storing of sap jsons in S3 ([#1845](https://github.com/prezero/hermes-sf/issues/1845)) ([2c9e7a1](https://github.com/prezero/hermes-sf/commit/2c9e7a14f40f13873e2d8f33237e1662133b52a1))
* Update pz stl config ([#1846](https://github.com/prezero/hermes-sf/issues/1846)) ([7755d47](https://github.com/prezero/hermes-sf/commit/7755d4795f5f3fb038ae08208724bd85a9a16be7))


### Bug Fixes

* **HERMES-609:** Here Service Token Fix ([#1843](https://github.com/prezero/hermes-sf/issues/1843)) ([930ed22](https://github.com/prezero/hermes-sf/commit/930ed223a38d980511d4d1e04d5eece25296873a))


### Code Refactoring

* json file endpoint ([#1839](https://github.com/prezero/hermes-sf/issues/1839)) ([591f4eb](https://github.com/prezero/hermes-sf/commit/591f4eb702e034515ff7301beded9ea2eb2a47aa))

## [3.20.1](https://github.com/prezero/hermes-sf/compare/v3.20.0...v3.20.1) (2024-06-26)


### Bug Fixes

* **HERMES-602:** Import lux tours sync via the test http call ([#1837](https://github.com/prezero/hermes-sf/issues/1837)) ([e652f4f](https://github.com/prezero/hermes-sf/commit/e652f4fc492cdd66aeff7bd8044054913b9ec7d4))

## [3.20.0](https://github.com/prezero/hermes-sf/compare/v3.19.0...v3.20.0) (2024-06-26)


### Features

* **HERMES-546:** Serializer & DTO Refactoring ([#1834](https://github.com/prezero/hermes-sf/issues/1834)) ([a516d77](https://github.com/prezero/hermes-sf/commit/a516d77af6ba44dee9c9f6f9a74c624b3bb3c90a))
* **HERMES-597:** alternative approach to tour caching ([#1829](https://github.com/prezero/hermes-sf/issues/1829)) ([267c214](https://github.com/prezero/hermes-sf/commit/267c214e04d7d3b71bb06ae31690e088f5778b34))
* **HERMES-604:** equipment status ([#1832](https://github.com/prezero/hermes-sf/issues/1832)) ([6b1d980](https://github.com/prezero/hermes-sf/commit/6b1d980a2b77e1df9db3ad253bab637d49a9091a))
* Update translations to version 32 ([#1835](https://github.com/prezero/hermes-sf/issues/1835)) ([375d5e4](https://github.com/prezero/hermes-sf/commit/375d5e4562565e76d9ca598a5f7f017cc81d2871))


### Bug Fixes

* catching messenger deserialization errors ([#1836](https://github.com/prezero/hermes-sf/issues/1836)) ([2e975e3](https://github.com/prezero/hermes-sf/commit/2e975e3f764b68d77b25f5512017ebc0fc0eaa55))

## [3.19.0](https://github.com/prezero/hermes-sf/compare/v3.18.1...v3.19.0) (2024-06-24)


### Features

* Update pz config ([#1828](https://github.com/prezero/hermes-sf/issues/1828)) ([c322330](https://github.com/prezero/hermes-sf/commit/c32233025bb311285795a55348abe57bc7bcd50d))


### Bug Fixes

* **HERMES-590:** lux double tour new user ([#1820](https://github.com/prezero/hermes-sf/issues/1820)) ([603979a](https://github.com/prezero/hermes-sf/commit/603979a0acfcc0a53bc22bb7f4219d28050b1d24))

## [3.18.1](https://github.com/prezero/hermes-sf/compare/v3.18.0...v3.18.1) (2024-06-20)


### Bug Fixes

* re-enabled tour-cache ([#1824](https://github.com/prezero/hermes-sf/issues/1824)) ([800e16b](https://github.com/prezero/hermes-sf/commit/800e16b926993a846255abc33da6bd1deae29442))

## [3.18.0](https://github.com/prezero/hermes-sf/compare/v3.17.1...v3.18.0) (2024-06-20)


### Features

* **HERMES-564:** Adding portal tour dashboard endpoint ([#1809](https://github.com/prezero/hermes-sf/issues/1809)) ([9309f8d](https://github.com/prezero/hermes-sf/commit/9309f8d9bfca0fd3640b5437098f437e2e53db94))
* **Hermes-565:** equ tour dashboard ([#1814](https://github.com/prezero/hermes-sf/issues/1814)) ([4c06d45](https://github.com/prezero/hermes-sf/commit/4c06d45149570451e32c75627c4af9b1c949d54c))
* **Hermes-568:** tour status dashboard ([#1813](https://github.com/prezero/hermes-sf/issues/1813)) ([8edd6e2](https://github.com/prezero/hermes-sf/commit/8edd6e2acf10ecaf7f3aa089c9bd2a2a0693054f))
* **HERMES-576:** element source attribute ([#1808](https://github.com/prezero/hermes-sf/issues/1808)) ([86cc8fd](https://github.com/prezero/hermes-sf/commit/86cc8fdf1f6b9c587e5b850e6c80e24345057149))
* translations v31 ([#1823](https://github.com/prezero/hermes-sf/issues/1823)) ([fb04c39](https://github.com/prezero/hermes-sf/commit/fb04c39d434d273360492b14af5b72d34fa3eb9e))


### Bug Fixes

* disabled tour-cache ([#1822](https://github.com/prezero/hermes-sf/issues/1822)) ([b61e14a](https://github.com/prezero/hermes-sf/commit/b61e14a17bb80f9c540e948394878452c5ccfd0b))

## [3.17.1](https://github.com/prezero/hermes-sf/compare/v3.17.0...v3.17.1) (2024-06-18)


### Bug Fixes

* **HERMES-591:** fixed filtering on json-values ([#1818](https://github.com/prezero/hermes-sf/issues/1818)) ([aaef6a6](https://github.com/prezero/hermes-sf/commit/aaef6a69853009a7f5319fac78f6a32e9f92cae8))

## [3.17.0](https://github.com/prezero/hermes-sf/compare/v3.16.0...v3.17.0) (2024-06-17)


### Features

* translations v30 ([b2d7a80](https://github.com/prezero/hermes-sf/commit/b2d7a80eba859b5d96ddf847e6684fa7d79ca013))

## [3.16.0](https://github.com/prezero/hermes-sf/compare/v3.15.0...v3.16.0) (2024-06-17)


### Features

* translations v29 ([#1815](https://github.com/prezero/hermes-sf/issues/1815)) ([ef2f848](https://github.com/prezero/hermes-sf/commit/ef2f848816e3ad2de576d54144186410e37bc2fd))

## [3.15.0](https://github.com/prezero/hermes-sf/compare/v3.14.0...v3.15.0) (2024-06-14)


### Features

* Update pz and pzl configs ([#1810](https://github.com/prezero/hermes-sf/issues/1810)) ([f37ef9e](https://github.com/prezero/hermes-sf/commit/f37ef9ed819281768fad5b44ff408a2367ff6964))


### Bug Fixes

* Update lux config ([#1812](https://github.com/prezero/hermes-sf/issues/1812)) ([21ef2d1](https://github.com/prezero/hermes-sf/commit/21ef2d1d62c60d4565d0095322b7ce7d5ac3e5b6))

## [3.14.0](https://github.com/prezero/hermes-sf/compare/v3.13.0...v3.14.0) (2024-06-13)


### Features

* **HERMES-545:** Update Symfony to 7.1 ([#1807](https://github.com/prezero/hermes-sf/issues/1807)) ([3ea8a81](https://github.com/prezero/hermes-sf/commit/3ea8a81657d2f3de154c3878d8eda5320537529f))
* Update translations to v28 ([#1805](https://github.com/prezero/hermes-sf/issues/1805)) ([1d6d58d](https://github.com/prezero/hermes-sf/commit/1d6d58db8d0db7dbe2fe9550b78ac183282e2b32))

## [3.13.0](https://github.com/prezero/hermes-sf/compare/v3.12.0...v3.13.0) (2024-06-12)


### Features

* Update translations to v27 ([#1802](https://github.com/prezero/hermes-sf/issues/1802)) ([dde9e64](https://github.com/prezero/hermes-sf/commit/dde9e64acbe018c5a47b5e6cacaaa8ef3e305ce8))

## [3.12.0](https://github.com/prezero/hermes-sf/compare/v3.11.0...v3.12.0) (2024-06-11)


### Features

* **HERMES-507:** changed mapping of ask/gak ([#1800](https://github.com/prezero/hermes-sf/issues/1800)) ([e269fd0](https://github.com/prezero/hermes-sf/commit/e269fd0854272b5204e61ce7db8489d0101a4970))

## [3.11.0](https://github.com/prezero/hermes-sf/compare/v3.10.1...v3.11.0) (2024-06-11)


### Features

* Update lux config ([#1798](https://github.com/prezero/hermes-sf/issues/1798)) ([9d26f43](https://github.com/prezero/hermes-sf/commit/9d26f43e5d607e80b5954844f99c605ad8a79849))

## [3.10.1](https://github.com/prezero/hermes-sf/compare/v3.10.0...v3.10.1) (2024-06-10)


### Bug Fixes

* **HERMES-569:** task status assign toggle ([#1796](https://github.com/prezero/hermes-sf/issues/1796)) ([46854be](https://github.com/prezero/hermes-sf/commit/46854bea7209dd9e3d75e8421605402a634f39b5))

## [3.10.0](https://github.com/prezero/hermes-sf/compare/v3.9.1...v3.10.0) (2024-06-10)


### Features

* **HERMES-541:** translation language switch ([#1793](https://github.com/prezero/hermes-sf/issues/1793)) ([49f148f](https://github.com/prezero/hermes-sf/commit/49f148f25b5b7eaa29ec6f84d325a23504fb64df))
* Update translations to v26 ([#1795](https://github.com/prezero/hermes-sf/issues/1795)) ([cb61997](https://github.com/prezero/hermes-sf/commit/cb61997f30a87f385ea7405777c148d1c78c480a))

## [3.9.1](https://github.com/prezero/hermes-sf/compare/v3.9.0...v3.9.1) (2024-06-07)


### Bug Fixes

* fixed config ([#1791](https://github.com/prezero/hermes-sf/issues/1791)) ([9e00fce](https://github.com/prezero/hermes-sf/commit/9e00fce5e70dcca021e02b7a5e832322f1bf3f7c))

## [3.9.0](https://github.com/prezero/hermes-sf/compare/v3.8.0...v3.9.0) (2024-06-06)


### Features

* **HERMES-512:** etat materiel translation ([#1786](https://github.com/prezero/hermes-sf/issues/1786)) ([4bafdc1](https://github.com/prezero/hermes-sf/commit/4bafdc1d93099d5ae6435f6159b521c4ea92dc8e))


### Bug Fixes

* updated element-options ([#1790](https://github.com/prezero/hermes-sf/issues/1790)) ([9f5c347](https://github.com/prezero/hermes-sf/commit/9f5c3472828b53059942b8a38bea805e367f2b1f))

## [3.8.0](https://github.com/prezero/hermes-sf/compare/v3.7.0...v3.8.0) (2024-06-06)


### Features

* **HERMES-543:** Removing redundant disposal sites ([#1783](https://github.com/prezero/hermes-sf/issues/1783)) ([c9496d5](https://github.com/prezero/hermes-sf/commit/c9496d57b6d079a18625decb919677a7a323eb45))


### Bug Fixes

* set defaults in migration ([#1788](https://github.com/prezero/hermes-sf/issues/1788)) ([6dd6c7c](https://github.com/prezero/hermes-sf/commit/6dd6c7c4ce4ab139619ca6415759b6cef8d99b83))

## [3.7.0](https://github.com/prezero/hermes-sf/compare/v3.6.0...v3.7.0) (2024-06-05)


### Features

* Increase system memory (=more CPU) and consumer count ([#1784](https://github.com/prezero/hermes-sf/issues/1784)) ([11fc209](https://github.com/prezero/hermes-sf/commit/11fc2099453c120eb4444a7bf667c39a10fece7b))

## [3.6.0](https://github.com/prezero/hermes-sf/compare/v3.5.0...v3.6.0) (2024-06-05)


### Features

* **HERMES-544:** Abandon tours command ([#1780](https://github.com/prezero/hermes-sf/issues/1780)) ([6c1c027](https://github.com/prezero/hermes-sf/commit/6c1c02732baf18dc119a23aa409a602f8f6c7a15))
* updated fixtures ([#1781](https://github.com/prezero/hermes-sf/issues/1781)) ([ebd6a79](https://github.com/prezero/hermes-sf/commit/ebd6a796f91b3472d455f8de269933d3b9e5f235))

## [3.5.0](https://github.com/prezero/hermes-sf/compare/v3.4.0...v3.5.0) (2024-06-05)


### Features

* added new type ([#1779](https://github.com/prezero/hermes-sf/issues/1779)) ([7d69be8](https://github.com/prezero/hermes-sf/commit/7d69be8a4943fe52b89dcb238406e76af13baafa))
* **HERMES-519:** Add requests to SAP Spain on changes in Hermes ([#1776](https://github.com/prezero/hermes-sf/issues/1776)) ([22cbdad](https://github.com/prezero/hermes-sf/commit/22cbdad77a026782240cd2eda6079d5819df3597))
* **HERMES-520:** Element options and values to value objects ([#1773](https://github.com/prezero/hermes-sf/issues/1773)) ([a052640](https://github.com/prezero/hermes-sf/commit/a052640f0a714ffb62a8f142be6e068cccdc749b))
* **HERMES-528:** Gis resolution & tour caching ([#1778](https://github.com/prezero/hermes-sf/issues/1778)) ([ad65b90](https://github.com/prezero/hermes-sf/commit/ad65b90d79eced79d2b78c4b76b8e9f22caffd62))

## [3.4.0](https://github.com/prezero/hermes-sf/compare/v3.3.2...v3.4.0) (2024-06-04)


### Features

* new config for lux ([#1774](https://github.com/prezero/hermes-sf/issues/1774)) ([0463dcc](https://github.com/prezero/hermes-sf/commit/0463dcca333f5dc44cd292397d7cc3c61dd83e95))

## [3.3.2](https://github.com/prezero/hermes-sf/compare/v3.3.1...v3.3.2) (2024-06-03)


### Bug Fixes

* **HERMES-510:** no question for not existing elements with correct ref-type ([#1772](https://github.com/prezero/hermes-sf/issues/1772)) ([9319e72](https://github.com/prezero/hermes-sf/commit/9319e7244d4bbff8a10fa20c7150188ac40cac60))
* **HERMES-521:** File permissions after deploy ([#1769](https://github.com/prezero/hermes-sf/issues/1769)) ([bea3d95](https://github.com/prezero/hermes-sf/commit/bea3d95fc029ce0f2dbe3b1d1931a73dc864be8c))

## [3.3.1](https://github.com/prezero/hermes-sf/compare/v3.3.0...v3.3.1) (2024-05-31)


### Bug Fixes

* **HERMES-515:** Adding missing exception handling ([#1766](https://github.com/prezero/hermes-sf/issues/1766)) ([bfc8eb1](https://github.com/prezero/hermes-sf/commit/bfc8eb1e7e3507b11e8dfb558d08458170be6d6a))
* **HERMES-517:** CORS headers ([#1767](https://github.com/prezero/hermes-sf/issues/1767)) ([c4ec038](https://github.com/prezero/hermes-sf/commit/c4ec038bb56c38a7179cf249ac28df25f9658113))

## [3.3.0](https://github.com/prezero/hermes-sf/compare/v3.2.1...v3.3.0) (2024-05-29)


### Features

* fixture update ([#1762](https://github.com/prezero/hermes-sf/issues/1762)) ([79acd32](https://github.com/prezero/hermes-sf/commit/79acd328c859392816e8cb7efee526f4ba051334))
* **HERMES-499:** Tour get endpoint caching ([#1761](https://github.com/prezero/hermes-sf/issues/1761)) ([4fb7ece](https://github.com/prezero/hermes-sf/commit/4fb7ece5fe3da5d0e09ece4a301361ca222e1e90))
* **HERMES-507:** Change container prefix logic for lux ([#1765](https://github.com/prezero/hermes-sf/issues/1765)) ([97dc610](https://github.com/prezero/hermes-sf/commit/97dc610751fdb4aadcf0599e8ca7ef35f05b930b))


### Bug Fixes

* **HERMES-501:** order type toggle task assign ([#1764](https://github.com/prezero/hermes-sf/issues/1764)) ([6f77c99](https://github.com/prezero/hermes-sf/commit/6f77c99e0a01972773dc1811da20db4b311717c4))

## [3.2.1](https://github.com/prezero/hermes-sf/compare/v3.2.0...v3.2.1) (2024-05-28)


### Bug Fixes

* **HERMES-500:** Remove unused order by ([#1759](https://github.com/prezero/hermes-sf/issues/1759)) ([2b87d69](https://github.com/prezero/hermes-sf/commit/2b87d698d28e6ddf0a145959db6f89cfb1abeaa6))

## [3.2.0](https://github.com/prezero/hermes-sf/compare/v3.1.2...v3.2.0) (2024-05-27)


### Features

* fixture update ([#1758](https://github.com/prezero/hermes-sf/issues/1758)) ([02d93d8](https://github.com/prezero/hermes-sf/commit/02d93d8b81c0adb96961f6be74c0ca45be0e73b5))


### Code Refactoring

* **HERMES-494:** Change onlyForAssignment to activatedBySapData ([#1757](https://github.com/prezero/hermes-sf/issues/1757)) ([148a866](https://github.com/prezero/hermes-sf/commit/148a86693b42cb999bbe0d4f5f79e5136caac629))


### Tests

* added order-termination config ([#1755](https://github.com/prezero/hermes-sf/issues/1755)) ([d9e7db1](https://github.com/prezero/hermes-sf/commit/d9e7db168f277811db5c218a6eb4bbaae17d5092))

## [3.1.2](https://github.com/prezero/hermes-sf/compare/v3.1.1...v3.1.2) (2024-05-23)


### Bug Fixes

* **HERMES-489:** Keycloak check if user exists fix ([#1753](https://github.com/prezero/hermes-sf/issues/1753)) ([cd7d0ff](https://github.com/prezero/hermes-sf/commit/cd7d0ff225d207bb90c26c6909846b86da9743ba))

## [3.1.1](https://github.com/prezero/hermes-sf/compare/v3.1.0...v3.1.1) (2024-05-22)


### Bug Fixes

* **HERMES-448:** tour interruption rule map ([#1751](https://github.com/prezero/hermes-sf/issues/1751)) ([71a36bf](https://github.com/prezero/hermes-sf/commit/71a36bfffe358ed378e48523208f2092713df455))

## [3.1.0](https://github.com/prezero/hermes-sf/compare/v3.0.0...v3.1.0) (2024-05-22)


### Features

* Update german config ([#1749](https://github.com/prezero/hermes-sf/issues/1749)) ([eec4b0b](https://github.com/prezero/hermes-sf/commit/eec4b0b46ec7295209345fb89a53205b4a77bddd))

## [3.0.0](https://github.com/prezero/hermes-sf/compare/v2.12.0...v3.0.0) (2024-05-21)


### ⚠ BREAKING CHANGES

* **HERMES-461:** rule structure ([#1733](https://github.com/prezero/hermes-sf/issues/1733))

### Features

* **HERMES-461:** rule structure ([#1733](https://github.com/prezero/hermes-sf/issues/1733)) ([8aff8be](https://github.com/prezero/hermes-sf/commit/8aff8be901b5edcf618b9ca6ae178b2e83aa54e1))
* **HERMES-470:** equipment position license plate ([#1740](https://github.com/prezero/hermes-sf/issues/1740)) ([dd777c3](https://github.com/prezero/hermes-sf/commit/dd777c3acb04fc096d9902240b818e94dcd88c91))
* **HERMES-481:** Update framework and libraries ([#1746](https://github.com/prezero/hermes-sf/issues/1746)) ([9dd5623](https://github.com/prezero/hermes-sf/commit/9dd56232d74fee236702ca428fe5b96f4d79865b))
* lux fixtures ([#1748](https://github.com/prezero/hermes-sf/issues/1748)) ([7821c24](https://github.com/prezero/hermes-sf/commit/7821c24fc79317e472e7fd9ccf45a4c4a250307e))
* Update es fixtures ([#1744](https://github.com/prezero/hermes-sf/issues/1744)) ([8a83af9](https://github.com/prezero/hermes-sf/commit/8a83af97838b0af44c944ac5fe1b24a4548e6978))


### Bug Fixes

* dump testdata ([#1742](https://github.com/prezero/hermes-sf/issues/1742)) ([f5cc6d2](https://github.com/prezero/hermes-sf/commit/f5cc6d2613590a1c5ad483a3e77206d61d0fbf02))
* dump testdata ([#1743](https://github.com/prezero/hermes-sf/issues/1743)) ([68a8d1a](https://github.com/prezero/hermes-sf/commit/68a8d1aeb78f0b38a2286f4f3daecbfc0b971de2))
* **HERMES-459:** Adding request id to open telemetry spans ([#1745](https://github.com/prezero/hermes-sf/issues/1745)) ([8e624c9](https://github.com/prezero/hermes-sf/commit/8e624c9ff153c6867e52fd0a78f2210ee4ee8a20))
* **HERMES-460:** Add separate logging per infrastructure endpoints, separation of concerns ([#1738](https://github.com/prezero/hermes-sf/issues/1738)) ([9dbd3c8](https://github.com/prezero/hermes-sf/commit/9dbd3c83e29489bc7f70021820eeef01ababdbaf))
* updated dump with testdata ([#1741](https://github.com/prezero/hermes-sf/issues/1741)) ([c4f536e](https://github.com/prezero/hermes-sf/commit/c4f536e2dc23b67ccf55e75a315c2da44a48b756))


### Tests

* **HERMES-482:** Improve lux tests by not reloading the DB ([#1747](https://github.com/prezero/hermes-sf/issues/1747)) ([035b763](https://github.com/prezero/hermes-sf/commit/035b763315812bc01614446ec1bc77424c8a7f7d))

## [2.12.0](https://github.com/prezero/hermes-sf/compare/v2.11.0...v2.12.0) (2024-05-16)


### Features

* **HERMES-447:** Adding local DB admin interface for postgres ([#1734](https://github.com/prezero/hermes-sf/issues/1734)) ([2aef21a](https://github.com/prezero/hermes-sf/commit/2aef21a7dfc3d4b0a04f1ca87b5f500ec1fda47e))


### Bug Fixes

* check for existing user ([#1737](https://github.com/prezero/hermes-sf/issues/1737)) ([3903b0c](https://github.com/prezero/hermes-sf/commit/3903b0cea802340ebc7d39510cb0a765ab93b57c))
* **HERMES-477:** Expected element should point to newly created option ID ([#1736](https://github.com/prezero/hermes-sf/issues/1736)) ([6e44304](https://github.com/prezero/hermes-sf/commit/6e443044e33e79801f3b5c55a0d9b7e26434def2))

## [2.11.0](https://github.com/prezero/hermes-sf/compare/v2.10.3...v2.11.0) (2024-05-16)


### Features

* **HERMES-447:** Migration to postgres sql ([#1723](https://github.com/prezero/hermes-sf/issues/1723)) ([e29d4e8](https://github.com/prezero/hermes-sf/commit/e29d4e86c386ec2da65c8ec3e613e502b5d621fb))
* **HERMES-447:** Migration to postgres sql for qa and prod ([#1731](https://github.com/prezero/hermes-sf/issues/1731)) ([6c12a05](https://github.com/prezero/hermes-sf/commit/6c12a05a54e9aefc01906016a6c89b0fb07c49de))
* **HERMES-467:** PreZero keycloak theme for local env ([#1726](https://github.com/prezero/hermes-sf/issues/1726)) ([ae5ee10](https://github.com/prezero/hermes-sf/commit/ae5ee104aadf04c2ab0e9441ddba906b1ba4ad83))
* Update translations ([#1732](https://github.com/prezero/hermes-sf/issues/1732)) ([f37af34](https://github.com/prezero/hermes-sf/commit/f37af34980ae575963854c880d256076bdc03782))


### Bug Fixes

* **HERMES-447:** Fixing xdebug mode ([7fbd79d](https://github.com/prezero/hermes-sf/commit/7fbd79db84328332e82e9a5e337c32cadd3b189e))
* **HERMES-447:** Missing DB URL on consumer ([25eec34](https://github.com/prezero/hermes-sf/commit/25eec344d99a755ff999a168006866addfc0b8c4))
* **HERMES-447:** New dump with public db only ([9d96ce6](https://github.com/prezero/hermes-sf/commit/9d96ce6afc12ae4e61c621d2e21a88d0db78d1ba))
* **HERMES-447:** Reloading DB schema ([89b7b5b](https://github.com/prezero/hermes-sf/commit/89b7b5bb2a9564bd50405d1b82d1b3e7dd841e48))
* **HERMES-447:** Reloading only DB schema, not dropping the DB ([3741644](https://github.com/prezero/hermes-sf/commit/37416445753cefe7725e75d77dd76581f0a78309))
* **HERMES-447:** Reloading only public DB schema inside the database ([33c3e40](https://github.com/prezero/hermes-sf/commit/33c3e40fee9345bd152450357979fe4813a189e9))
* **HERMES-447:** Remove dump & reload of owners and privileges ([a5cf2d0](https://github.com/prezero/hermes-sf/commit/a5cf2d0c6fc62eac56341a2a2070c4a99966294f))
* **HERMES-474:** fix getting last equipment positions ([#1730](https://github.com/prezero/hermes-sf/issues/1730)) ([4fc0b27](https://github.com/prezero/hermes-sf/commit/4fc0b27041aed2a21136aa72dbd6dd8fdeaaaff1))

## [2.10.3](https://github.com/prezero/hermes-sf/compare/v2.10.2...v2.10.3) (2024-05-14)


### Bug Fixes

* changed exception-type ([#1727](https://github.com/prezero/hermes-sf/issues/1727)) ([eb3d014](https://github.com/prezero/hermes-sf/commit/eb3d014cfb3a4a764014a1ed078b410dc1462c43))

## [2.10.2](https://github.com/prezero/hermes-sf/compare/v2.10.1...v2.10.2) (2024-05-14)


### Bug Fixes

* **HERMES-457:** kc user create ([#1724](https://github.com/prezero/hermes-sf/issues/1724)) ([8a4bbd8](https://github.com/prezero/hermes-sf/commit/8a4bbd8ca809d12e6f2f9aca41dca402da9bc5c6))

## [2.10.1](https://github.com/prezero/hermes-sf/compare/v2.10.0...v2.10.1) (2024-05-14)


### Bug Fixes

* **HERMES-342:** removed tombstones, calls are needed there ([#1720](https://github.com/prezero/hermes-sf/issues/1720)) ([49257c0](https://github.com/prezero/hermes-sf/commit/49257c0a09db2aa33d45fc774648255ed0e315d5))
* missing truckactivity for lux ([#1722](https://github.com/prezero/hermes-sf/issues/1722)) ([2295d8d](https://github.com/prezero/hermes-sf/commit/2295d8d7d5e24cfc9c9ffcb8772e18d211b341e1))

## [2.10.0](https://github.com/prezero/hermes-sf/compare/v2.9.0...v2.10.0) (2024-05-13)


### Features

* **HERMES-137:** Return session uuid for session start call ([#1717](https://github.com/prezero/hermes-sf/issues/1717)) ([c3376a5](https://github.com/prezero/hermes-sf/commit/c3376a56b8e9c3354fa1cd0319529dd4a439cd28))
* **HERMES-440:** lux toilet questions ([#1719](https://github.com/prezero/hermes-sf/issues/1719)) ([bd9f8aa](https://github.com/prezero/hermes-sf/commit/bd9f8aaaa3d7616c052413c86ee6b97ac70b57fe))


### Bug Fixes

* **HERMES-342:** Tombstone possible unused methods ([#1718](https://github.com/prezero/hermes-sf/issues/1718)) ([a066774](https://github.com/prezero/hermes-sf/commit/a0667743a7148ca85ac2b7166caa7763be820d55))


### Code Refactoring

* **HERMES-389:** Architecture refactoring part 2 ([#1694](https://github.com/prezero/hermes-sf/issues/1694)) ([70b278d](https://github.com/prezero/hermes-sf/commit/70b278d1115c333059a7c2b78181ef253bc90024))


### Continuous Integration

* fix new deployment restarts ([#1715](https://github.com/prezero/hermes-sf/issues/1715)) ([01c6eb4](https://github.com/prezero/hermes-sf/commit/01c6eb4f7401c6bda5a68806ac75634631b77d05))

## [2.9.0](https://github.com/prezero/hermes-sf/compare/v2.8.1...v2.9.0) (2024-05-08)


### Features

* lux toilet disposal ([#1713](https://github.com/prezero/hermes-sf/issues/1713)) ([cd9a040](https://github.com/prezero/hermes-sf/commit/cd9a0409cc84b12d2e322c8868f2b2e0d748f8ce))


### Bug Fixes

* **HERMES-432:** moved setting of last-login for app-users ([#1712](https://github.com/prezero/hermes-sf/issues/1712)) ([d3dba05](https://github.com/prezero/hermes-sf/commit/d3dba05df1896dfce26885a9812d2c9db9e0c389))

## [2.8.1](https://github.com/prezero/hermes-sf/compare/v2.8.0...v2.8.1) (2024-05-07)


### Bug Fixes

* set correct key ([#1710](https://github.com/prezero/hermes-sf/issues/1710)) ([84c49d9](https://github.com/prezero/hermes-sf/commit/84c49d9208d8263f622b63047235e0d14c64754d))

## [2.8.0](https://github.com/prezero/hermes-sf/compare/v2.7.0...v2.8.0) (2024-05-07)


### Features

* **HERMES-423:** new eq-type ([#1707](https://github.com/prezero/hermes-sf/issues/1707)) ([63b172d](https://github.com/prezero/hermes-sf/commit/63b172dbd88dd66869beeff822b8562e8f54d117))
* **HERMES-428:** increased length ([#1708](https://github.com/prezero/hermes-sf/issues/1708)) ([41c1f27](https://github.com/prezero/hermes-sf/commit/41c1f270ee120092e2c2872920ec09dd7f04103c))
* **HERMES-429:** added username to dto ([#1709](https://github.com/prezero/hermes-sf/issues/1709)) ([1d7fbe6](https://github.com/prezero/hermes-sf/commit/1d7fbe6d4f0b59e5d82f7c6714087d21f748a040))


### Bug Fixes

* **HERMES-419:** changed unit-entry for std to uppercase ([#1705](https://github.com/prezero/hermes-sf/issues/1705)) ([f1684cd](https://github.com/prezero/hermes-sf/commit/f1684cd7fb7b451ab0eb9537d510e3a46c281b3a))

## [2.7.0](https://github.com/prezero/hermes-sf/compare/v2.6.1...v2.7.0) (2024-05-06)


### Features

* fixture config updates ([#1704](https://github.com/prezero/hermes-sf/issues/1704)) ([914382d](https://github.com/prezero/hermes-sf/commit/914382d45976fb1ace0691f02a589ba9c3bcc7ba))
* **HERMES-391:** added new order-type for lux ([#1703](https://github.com/prezero/hermes-sf/issues/1703)) ([5944330](https://github.com/prezero/hermes-sf/commit/59443303732cd1a9a03ee0752700c09febd747af))
* **HERMES-407:** 407 lux country extraction ([#1700](https://github.com/prezero/hermes-sf/issues/1700)) ([73579c0](https://github.com/prezero/hermes-sf/commit/73579c0ef839cf69721671437d482ee296a1a7bb))
* Update translations ([#1701](https://github.com/prezero/hermes-sf/issues/1701)) ([ffa700c](https://github.com/prezero/hermes-sf/commit/ffa700c584d6183478effa42bcbd365b408b2a3a))

## [2.6.1](https://github.com/prezero/hermes-sf/compare/v2.6.0...v2.6.1) (2024-05-02)


### Bug Fixes

* fix paths in truncate-command and some fixtures ([46a1411](https://github.com/prezero/hermes-sf/commit/46a1411e4173e685199fc6064d4ee5b01c16d76a))

## [2.6.0](https://github.com/prezero/hermes-sf/compare/v2.5.1...v2.6.0) (2024-05-02)


### Features

* Update translations for lux ([#1697](https://github.com/prezero/hermes-sf/issues/1697)) ([0afc5ff](https://github.com/prezero/hermes-sf/commit/0afc5ffb7a9033579a43c4cc934f40bbece1cc9e))

## [2.5.1](https://github.com/prezero/hermes-sf/compare/v2.5.0...v2.5.1) (2024-05-02)


### Bug Fixes

* **HERMES-396:** fixed sequence of workflow-transitions ([#1696](https://github.com/prezero/hermes-sf/issues/1696)) ([29359d3](https://github.com/prezero/hermes-sf/commit/29359d3ddcc05c358f7d3bba356d3d70544eedd3))


### Code Refactoring

* **HERMES-389:** Infrastructure improvements ([#1692](https://github.com/prezero/hermes-sf/issues/1692)) ([8185bf7](https://github.com/prezero/hermes-sf/commit/8185bf725a6f06018ff53c78cfae8b734565773d))

## [2.5.0](https://github.com/prezero/hermes-sf/compare/v2.4.0...v2.5.0) (2024-04-29)


### Features

* Add es config ([#1690](https://github.com/prezero/hermes-sf/issues/1690)) ([305585d](https://github.com/prezero/hermes-sf/commit/305585d49c34c0b523e787d4b39426d9e66bdf96))
* **HERMES-139:** Remove detail flag ([#1688](https://github.com/prezero/hermes-sf/issues/1688)) ([a0734d8](https://github.com/prezero/hermes-sf/commit/a0734d86490d419336d561685e6bfb8aa0e49d04))
* Update lux config ([#1689](https://github.com/prezero/hermes-sf/issues/1689)) ([af09f28](https://github.com/prezero/hermes-sf/commit/af09f28f6ec6fa342c186822891c7587b9eb4013))


### Bug Fixes

* **HERMES-385:** Last tour update ([#1686](https://github.com/prezero/hermes-sf/issues/1686)) ([ac6e7f0](https://github.com/prezero/hermes-sf/commit/ac6e7f082ae5c4350f64e2b92fc5c694229c32d7))
* **HERMES-387:** 387 interruption on return ([#1691](https://github.com/prezero/hermes-sf/issues/1691)) ([a4e8325](https://github.com/prezero/hermes-sf/commit/a4e83251448ce0b369348b46b2f1279e0b131ea9))

## [2.4.0](https://github.com/prezero/hermes-sf/compare/v2.3.0...v2.4.0) (2024-04-25)


### Features

* **HERMES-131:** Last tourupdate meta ([#1675](https://github.com/prezero/hermes-sf/issues/1675)) ([2db7cbf](https://github.com/prezero/hermes-sf/commit/2db7cbfb1491926c2511e072b11c520d345c767a))

## [2.3.0](https://github.com/prezero/hermes-sf/compare/v2.2.0...v2.3.0) (2024-04-25)


### Features

* Update translations ([#1684](https://github.com/prezero/hermes-sf/issues/1684)) ([03bcf0c](https://github.com/prezero/hermes-sf/commit/03bcf0cfab074512ad146405f0ec03397c607985))


### Bug Fixes

* **HERMES-380:** password mails for staff ([#1682](https://github.com/prezero/hermes-sf/issues/1682)) ([0d25548](https://github.com/prezero/hermes-sf/commit/0d255483137bb687d50704828294afe9d3ed33f9))

## [2.2.0](https://github.com/prezero/hermes-sf/compare/v2.1.0...v2.2.0) (2024-04-24)


### Features

* **HERMES-361:** portal user me ([#1677](https://github.com/prezero/hermes-sf/issues/1677)) ([608cdfa](https://github.com/prezero/hermes-sf/commit/608cdfa3adad037fd9f1d9d1a968e5ef7926b67b))

## [2.1.0](https://github.com/prezero/hermes-sf/compare/v2.0.2...v2.1.0) (2024-04-24)


### Features

* Adding pcntl extension & enabling grpc support for it ([4cb8919](https://github.com/prezero/hermes-sf/commit/4cb891959c77f7e16746269bbe1e226e864f1cc6))

## [2.0.2](https://github.com/prezero/hermes-sf/compare/v2.0.1...v2.0.2) (2024-04-24)


### Bug Fixes

* disabling debug on prod ([70228b0](https://github.com/prezero/hermes-sf/commit/70228b0b794db725134dfb74bed2e11e636ddd81))

## [2.0.1](https://github.com/prezero/hermes-sf/compare/v2.0.0...v2.0.1) (2024-04-24)


### Bug Fixes

* disabling debug on qa ([b1cce9d](https://github.com/prezero/hermes-sf/commit/b1cce9d044c990d3e641041b8c127c616b3e98e4))

## [2.0.0](https://github.com/prezero/hermes-sf/compare/v1.70.0...v2.0.0) (2024-04-23)


### ⚠ BREAKING CHANGES

* **HERMES-308:** Keycloak POC
* **HERMES-308:** Keycloak POC ([#1664](https://github.com/prezero/hermes-sf/issues/1664))

### Features

* changed manifest for keycloak-qa ([#1676](https://github.com/prezero/hermes-sf/issues/1676)) ([e5200ab](https://github.com/prezero/hermes-sf/commit/e5200ab841d8e424f1ab9044727b1a8054db6932))
* **HERMES-308:** Keycloak POC ([8352504](https://github.com/prezero/hermes-sf/commit/83525044dea13538aca828e46c83cd946d0eac5f))
* **HERMES-308:** Keycloak POC ([#1664](https://github.com/prezero/hermes-sf/issues/1664)) ([95b7608](https://github.com/prezero/hermes-sf/commit/95b7608758cb2b3d79c28d1f46f50ee68ccd8e98))
* **HERMES-360:** Update php version, removing imagick ([#1670](https://github.com/prezero/hermes-sf/issues/1670)) ([ede094a](https://github.com/prezero/hermes-sf/commit/ede094ad30d32e348ad3bff26d37e225498a960d))
* **HERMES-362:** spain tenant ([#1669](https://github.com/prezero/hermes-sf/issues/1669)) ([e289fdb](https://github.com/prezero/hermes-sf/commit/e289fdb2a0e59cd4b15cd6102513dc4635f23ddd))
* **HERMES-370:** k8s images ([d54be0c](https://github.com/prezero/hermes-sf/commit/d54be0cbe4ad6874d86f97d7238a2ccb499f4381))
* **HERMES-371:** equipment measurements in list ([#1673](https://github.com/prezero/hermes-sf/issues/1673)) ([8e2089b](https://github.com/prezero/hermes-sf/commit/8e2089bf5f0a2e6dabbeedf2e7598ce8df791673))


### Bug Fixes

* fixed spain var names ([#1671](https://github.com/prezero/hermes-sf/issues/1671)) ([dbc7a54](https://github.com/prezero/hermes-sf/commit/dbc7a54224eb26a3d69e7f80e45d7fdbb507cbaa))
* hardcoding OIDC_KEY ([e98ed5d](https://github.com/prezero/hermes-sf/commit/e98ed5dfc5e50e1227b96c3ad1b5ff0f0a4d3379))
* hardcoding OIDC_KEY ([4e4249f](https://github.com/prezero/hermes-sf/commit/4e4249f32a62fe9e5f77b7740bc13ffa6b3c55b5))
* **HERMES-308:** Removing old listener ([6ef08cd](https://github.com/prezero/hermes-sf/commit/6ef08cd123e11feb44766ab6e37d5ea1d24b9be3))
* **HERMES-374:** set staff-status on leaving session on login ([#1674](https://github.com/prezero/hermes-sf/issues/1674)) ([d5de67e](https://github.com/prezero/hermes-sf/commit/d5de67eb7206b4d36d2cae623099f45335acbae4))
* removing k8s images from master ([94da642](https://github.com/prezero/hermes-sf/commit/94da64201daf775d2ae6828aea0f4474dc1c0dda))


### Code Refactoring

* **HERMES-368:** obsolete classes ([#1672](https://github.com/prezero/hermes-sf/issues/1672)) ([f630059](https://github.com/prezero/hermes-sf/commit/f6300591152cec3afc80812b646e88957081756d))


### Continuous Integration

* enable retention ([#1665](https://github.com/prezero/hermes-sf/issues/1665)) ([660c3f8](https://github.com/prezero/hermes-sf/commit/660c3f84fe3aa4736e0c7cc13553860ae7a1f3ed))

## [1.70.0](https://github.com/prezero/hermes-sf/compare/v1.69.0...v1.70.0) (2024-04-16)


### Features

* new translations ([#1666](https://github.com/prezero/hermes-sf/issues/1666)) ([12d07de](https://github.com/prezero/hermes-sf/commit/12d07deb1b9898445a5c1132058d365105dce95b))

## [1.69.0](https://github.com/prezero/hermes-sf/compare/v1.68.0...v1.69.0) (2024-04-11)


### Features

* **HERMES-329:** reduced level to notice ([#1661](https://github.com/prezero/hermes-sf/issues/1661)) ([171f3cc](https://github.com/prezero/hermes-sf/commit/171f3cc407d66d9b7453c356af9b6670cf003b56))


### Bug Fixes

* **HERMES-334:** allow abandon terminated orders like regularly completed ones ([#1663](https://github.com/prezero/hermes-sf/issues/1663)) ([dbf6a45](https://github.com/prezero/hermes-sf/commit/dbf6a455aa24e637c03c2e908b9db616c4cc8016))


### Continuous Integration

* clean up e2e kubernetes tests ([#1660](https://github.com/prezero/hermes-sf/issues/1660)) ([a2126b7](https://github.com/prezero/hermes-sf/commit/a2126b704125160a50a72809f5a397398f17633b))

## [1.68.0](https://github.com/prezero/hermes-sf/compare/v1.67.0...v1.68.0) (2024-04-08)


### Features

* new translations ([#1658](https://github.com/prezero/hermes-sf/issues/1658)) ([3332dc7](https://github.com/prezero/hermes-sf/commit/3332dc7e45ebe747e6029d8c17c148c99e7b0729))

## [1.67.0](https://github.com/prezero/hermes-sf/compare/v1.66.0...v1.67.0) (2024-04-05)


### Features

* **HERMES-312:** 312 lux postalcodes ([#1656](https://github.com/prezero/hermes-sf/issues/1656)) ([ee1dd2e](https://github.com/prezero/hermes-sf/commit/ee1dd2e15273b0c1ba8f73313f8b446ce7dbe281))


### Bug Fixes

* **HERMES-310:** removed flag for barcode-doc ([#1653](https://github.com/prezero/hermes-sf/issues/1653)) ([d711eed](https://github.com/prezero/hermes-sf/commit/d711eedb187d15f102e70afe9b4d95d535ceb14f))


### Continuous Integration

* add kubernetes dev tests ([#1652](https://github.com/prezero/hermes-sf/issues/1652)) ([24f00d0](https://github.com/prezero/hermes-sf/commit/24f00d0f4fc96a84c460fd261f41f2e1a5a13deb))
* fix kubernetes action call ([#1655](https://github.com/prezero/hermes-sf/issues/1655)) ([fe7d470](https://github.com/prezero/hermes-sf/commit/fe7d470f3ce05552dfb922fc555264572c3809ed))
* fix kubernetese2e test ([#1657](https://github.com/prezero/hermes-sf/issues/1657)) ([e5c2916](https://github.com/prezero/hermes-sf/commit/e5c2916daeb7177d669d7e8edd2b671e9677878f))

## [1.66.0](https://github.com/prezero/hermes-sf/compare/v1.65.0...v1.66.0) (2024-04-05)


### Features

* request log prio ([#1650](https://github.com/prezero/hermes-sf/issues/1650)) ([5d86d8d](https://github.com/prezero/hermes-sf/commit/5d86d8d75d2093e45e008ce9d93b6d4e8ce50806))
* translation update ([#1651](https://github.com/prezero/hermes-sf/issues/1651)) ([0cd98c7](https://github.com/prezero/hermes-sf/commit/0cd98c7d7f80d5bfe0b8d6903b67d873f8eede31))
* Update translation fixtures ([#1648](https://github.com/prezero/hermes-sf/issues/1648)) ([ee1ae55](https://github.com/prezero/hermes-sf/commit/ee1ae55202d97a89c032e0847c9702d3b4e88486))

## [1.65.0](https://github.com/prezero/hermes-sf/compare/v1.64.2...v1.65.0) (2024-04-03)


### Features

* **HERMES-276:** Exposing ports in such order that web port is exposed first ([b32f173](https://github.com/prezero/hermes-sf/commit/b32f17379a8e4bd4484acb4cbb1dfc6573342985))
* **HERMES-299:** Run tour return workflow on logout, if you're last active user in session ([#1646](https://github.com/prezero/hermes-sf/issues/1646)) ([fb67dcb](https://github.com/prezero/hermes-sf/commit/fb67dcb1f1267f6f13dbd38a3a9b3c205ac1d60c))


### Bug Fixes

* release please ([1f96a1f](https://github.com/prezero/hermes-sf/commit/1f96a1f0d886ff2d8c740f0124087fd05630e314))


### Tests

* **HERMES-298:** removed headers from reset-fixtures-call ([#1644](https://github.com/prezero/hermes-sf/issues/1644)) ([1a00285](https://github.com/prezero/hermes-sf/commit/1a00285e8be229514146f0d8bab009649fe39a8b))


### Continuous Integration

* only pin major version ([#1645](https://github.com/prezero/hermes-sf/issues/1645)) ([2e7c960](https://github.com/prezero/hermes-sf/commit/2e7c9600610fb33922527be5b60bf3cdcb9ee4d6))
* test ci commit ([c1561ce](https://github.com/prezero/hermes-sf/commit/c1561cee24ea086ac762939de76513109caafd66))
* test ci commit ([85d7f8e](https://github.com/prezero/hermes-sf/commit/85d7f8e4faee2b66903e3a74a741199118da118f))
* test ci commit ([29fd3e5](https://github.com/prezero/hermes-sf/commit/29fd3e585223bb8fa0994d9fd167a022a61aeb82))
* trying to fix missing release please sections ([670c71b](https://github.com/prezero/hermes-sf/commit/670c71bfe0fc371bdc4dd899a12180e7f8716036))

## [1.64.2](https://github.com/prezero/hermes-sf/compare/v1.64.1...v1.64.2) (2024-04-03)


### Bug Fixes

* **HERMES-297:** SapLuxembourgGenerateOrderBarcodeDocumentPdfMessage should not require device ([#1642](https://github.com/prezero/hermes-sf/issues/1642)) ([77093c3](https://github.com/prezero/hermes-sf/commit/77093c3aeba329c852865ceb5b875deeb3e11640))

## [1.64.1](https://github.com/prezero/hermes-sf/compare/v1.64.0...v1.64.1) (2024-04-02)


### Bug Fixes

* **HERMES-296:** Reduce SAP Lux 404 logging severity ([#1640](https://github.com/prezero/hermes-sf/issues/1640)) ([62f048c](https://github.com/prezero/hermes-sf/commit/62f048c58b516e29093fc536e1db461acf68fd39))

## [1.64.0](https://github.com/prezero/hermes-sf/compare/v1.63.0...v1.64.0) (2024-04-02)


### Features

* 264 element checksum ([#1630](https://github.com/prezero/hermes-sf/issues/1630)) ([bec4cbd](https://github.com/prezero/hermes-sf/commit/bec4cbd31405bc23b48672c2b494617179218572))
* **HERMES-223:** Update tables to utf8mb4 ([#1636](https://github.com/prezero/hermes-sf/issues/1636)) ([86953a1](https://github.com/prezero/hermes-sf/commit/86953a1cdf6c939df58868ca7998d4044124265f))
* **HERMES-241:** FAQ list for given language ([#1631](https://github.com/prezero/hermes-sf/issues/1631)) ([4d7a9e6](https://github.com/prezero/hermes-sf/commit/4d7a9e6ac62441a85e3861ba604714c9bf086336))
* **HERMES-244:** FAQ Overview endpoint ([#1632](https://github.com/prezero/hermes-sf/issues/1632)) ([903a2b2](https://github.com/prezero/hermes-sf/commit/903a2b21cddc9aec7c2b8814a39f565da0bb8b64))
* **HERMES-246:** Portal CRUD for FAQs ([#1634](https://github.com/prezero/hermes-sf/issues/1634)) ([8559ab9](https://github.com/prezero/hermes-sf/commit/8559ab9ade183e2837be81465301b51ab02e35ed))
* **HERMES-250:** 250 app feedback ([#1626](https://github.com/prezero/hermes-sf/issues/1626)) ([20ec7a0](https://github.com/prezero/hermes-sf/commit/20ec7a04e9f9affe2ca65e5df40dd0d53dfae1f8))
* **HERMES-253:** Device queue sharding with dedicated consumers for FIFO handling ([#1624](https://github.com/prezero/hermes-sf/issues/1624)) ([c68bff1](https://github.com/prezero/hermes-sf/commit/c68bff1e8f81ee28a23ee0a7b34df8361ce445e4))
* **HERMES-256:** Device access app API ([#1637](https://github.com/prezero/hermes-sf/issues/1637)) ([6350b7d](https://github.com/prezero/hermes-sf/commit/6350b7d71fc00855b2e6a94870f30b6fe5a65496))
* **HERMES-259:** Portal Device Access API ([#1638](https://github.com/prezero/hermes-sf/issues/1638)) ([509de07](https://github.com/prezero/hermes-sf/commit/509de0734ece61d358d6c28aa3cd3b639cd272c1))
* **HERMES-268:** 268 app equipment device ([#1633](https://github.com/prezero/hermes-sf/issues/1633)) ([94b40e3](https://github.com/prezero/hermes-sf/commit/94b40e3bd5efa4eac5e5aa8029952877b3804c90))


### Bug Fixes

* **HERMES-237:** portaluser tenant ([#1628](https://github.com/prezero/hermes-sf/issues/1628)) ([f41b1df](https://github.com/prezero/hermes-sf/commit/f41b1df32dc8bbe9b62a3fa7639a4a00eb076ad1))
* **HERMES-274:** Option to dial down stdout logs on manual commands ([#1627](https://github.com/prezero/hermes-sf/issues/1627)) ([94a84de](https://github.com/prezero/hermes-sf/commit/94a84def0e5712b503482e281f9cff3997e220be))
* **HERMES-276:** Update base image & ports ([423524e](https://github.com/prezero/hermes-sf/commit/423524e95b707532791cfdd313dfc1270d2183c1))
* updated dump containing all migrations ([#1629](https://github.com/prezero/hermes-sf/issues/1629)) ([eef2a3f](https://github.com/prezero/hermes-sf/commit/eef2a3fb3143a3f46491716b4fa98dc506ea6283))

## [1.63.0](https://github.com/prezero/hermes-sf/compare/v1.62.1...v1.63.0) (2024-03-21)


### Features

* **HERMES-261:** 261 lux finish photos ([#1622](https://github.com/prezero/hermes-sf/issues/1622)) ([e57e5bc](https://github.com/prezero/hermes-sf/commit/e57e5bc3bd7d1d6814433c89b09f664444445c32))

## [1.62.1](https://github.com/prezero/hermes-sf/compare/v1.62.0...v1.62.1) (2024-03-21)


### Bug Fixes

* fixed config for departure-tasks in lux ([#1620](https://github.com/prezero/hermes-sf/issues/1620)) ([cc01acf](https://github.com/prezero/hermes-sf/commit/cc01acffc301cef26bfff145753faf1bc52e1458))
* fixed form-part-logging for multiple photos ([#1619](https://github.com/prezero/hermes-sf/issues/1619)) ([ac753f6](https://github.com/prezero/hermes-sf/commit/ac753f61a860b2af66e0be9c5f03e9cd56ef93e5))

## [1.62.0](https://github.com/prezero/hermes-sf/compare/v1.61.0...v1.62.0) (2024-03-20)


### Features

* **HERMES-252:** Enable opentracing on all environments ([#1616](https://github.com/prezero/hermes-sf/issues/1616)) ([bf2bf45](https://github.com/prezero/hermes-sf/commit/bf2bf45a4703edb22d7d68dd56a4d707d0eb4b82))
* Update lux config ([#1617](https://github.com/prezero/hermes-sf/issues/1617)) ([71e9bec](https://github.com/prezero/hermes-sf/commit/71e9bec40a92b97199a3feb6c51e5e67dcb97a22))


### Bug Fixes

* **HERMES-135:** 135 user eq transitions ([#1613](https://github.com/prezero/hermes-sf/issues/1613)) ([7baaccb](https://github.com/prezero/hermes-sf/commit/7baaccb58c2b027acea568cab35bb1b8b3afd8fa))
* **HERMES-209:** 209 lux action calls ([#1618](https://github.com/prezero/hermes-sf/issues/1618)) ([d70369b](https://github.com/prezero/hermes-sf/commit/d70369b38bdfd7495943b2fe223cff3dce9a81ff))
* **HERMES-234:** Stream logs to stderr, bind app to logme on dev, qa and prod ([#1614](https://github.com/prezero/hermes-sf/issues/1614)) ([4857a30](https://github.com/prezero/hermes-sf/commit/4857a304963a1e1a0c720733805dab450ce2d93e))

## [1.61.0](https://github.com/prezero/hermes-sf/compare/v1.60.1...v1.61.0) (2024-03-19)


### Features

* **HERMES-127:** maptrip navigation token ([#1589](https://github.com/prezero/hermes-sf/issues/1589)) ([ee1979f](https://github.com/prezero/hermes-sf/commit/ee1979ffd8435914bc4c21ccd1e0e87f855e31ef))


### Bug Fixes

* **HERMES-215:** removing bootstrap-sha ([9fe8f3b](https://github.com/prezero/hermes-sf/commit/9fe8f3b9462881fe9df5966afe98731ca3496047))

## [1.60.1](https://github.com/prezero/hermes-sf/compare/v1.60.0...v1.60.1) (2024-03-19)


### Bug Fixes

* test fix commit ([023e9e8](https://github.com/prezero/hermes-sf/commit/023e9e86d87e24924d6c7abc768ec13b2ba29c50))

## [1.60.0](https://github.com/prezero/hermes-sf/compare/v1.59.1...v1.60.0) (2024-03-18)


### Features

* Remove sap luxembourg user fixtures from prod fixtures ([#1609](https://github.com/prezero/hermes-sf/issues/1609)) ([f612efd](https://github.com/prezero/hermes-sf/commit/f612efd8fb5b180112ba62f3eac9aaa73bfb8007))

## [1.59.1](https://github.com/prezero/hermes-sf/compare/v1.59.0...v1.59.1) (2024-03-18)


### Bug Fixes

* added env var for qa and prod ([#1607](https://github.com/prezero/hermes-sf/issues/1607)) ([4f169ff](https://github.com/prezero/hermes-sf/commit/4f169ff3dca1c97d65a0dea1863ea20535ebd772))

## [1.59.0](https://github.com/prezero/hermes-sf/compare/v1.58.0...v1.59.0) (2024-03-18)


### Features

* **HERMES-231:** update lux boundary names ([#1604](https://github.com/prezero/hermes-sf/issues/1604)) ([b924b98](https://github.com/prezero/hermes-sf/commit/b924b984e29fadae92b085731e930f69958e196d))
* **HERMES-232:** Controlling whether SAP Lux is enabled via env var ([#1605](https://github.com/prezero/hermes-sf/issues/1605)) ([ad9b76e](https://github.com/prezero/hermes-sf/commit/ad9b76e260c862ff81c440c2d5408085d29a5675))

## [1.58.0](https://github.com/prezero/hermes-sf/compare/v1.57.0...v1.58.0) (2024-03-18)


### Features

* sap outgoing requests log ([#1602](https://github.com/prezero/hermes-sf/issues/1602)) ([0edc112](https://github.com/prezero/hermes-sf/commit/0edc1127c4606a6c2e29b888fa71c650e208f7b6))


### Bug Fixes

* check for pdf on s3 ([#1603](https://github.com/prezero/hermes-sf/issues/1603)) ([159bc06](https://github.com/prezero/hermes-sf/commit/159bc06f3ca4ccee9cf2e4ffb0c13f4af3be6446))
* **HERMES-228:** Redis exceptions should not break login process ([#1600](https://github.com/prezero/hermes-sf/issues/1600)) ([ff066f2](https://github.com/prezero/hermes-sf/commit/ff066f29730178bc92604a3816a7bffbb426be7a))
* when there is an exception during message handling, result is nullable ([#1601](https://github.com/prezero/hermes-sf/issues/1601)) ([e26ef15](https://github.com/prezero/hermes-sf/commit/e26ef159445ede92b4b858483a3eaa1fc81d61d1))


### Code Refactoring

* **HERMES-214:** update rector config & use more strict code quality ([#1595](https://github.com/prezero/hermes-sf/issues/1595)) ([b562230](https://github.com/prezero/hermes-sf/commit/b562230f0b746200548222c634b6261837653d69))


### Tests

* auto complete options ([#1586](https://github.com/prezero/hermes-sf/issues/1586)) ([dc33e9e](https://github.com/prezero/hermes-sf/commit/dc33e9e4c7db9d27a84a5bbcb37b4dfa21f51eb6))

## [1.57.0](https://github.com/prezero/hermes-sf/compare/v1.56.1...v1.57.0) (2024-03-15)


### Features

* **HERMES-229:** checked timestamp-formats ([#1596](https://github.com/prezero/hermes-sf/issues/1596)) ([3084b62](https://github.com/prezero/hermes-sf/commit/3084b62be3804675155c553a013af8e49762c46a))

## [1.56.1](https://github.com/prezero/hermes-sf/compare/v1.56.0...v1.56.1) (2024-03-15)


### Bug Fixes

* **HERMES-229:** setting timestamps for lux-scheduler-messages ([#1593](https://github.com/prezero/hermes-sf/issues/1593)) ([1dee85d](https://github.com/prezero/hermes-sf/commit/1dee85d5d309a363c24d62c53b9df2f74de32d7a))

## [1.56.0](https://github.com/prezero/hermes-sf/compare/v1.55.0...v1.56.0) (2024-03-14)


### Features

* 224 lux order ignore ([#1591](https://github.com/prezero/hermes-sf/issues/1591)) ([1a41433](https://github.com/prezero/hermes-sf/commit/1a4143359175f1e289e4fe8ae2900d38c1573054))


### Bug Fixes

* **HERMES-227:** Opentelemetry should not call Redis methods, because if it's down - it errors out ([#1590](https://github.com/prezero/hermes-sf/issues/1590)) ([3d8bd8a](https://github.com/prezero/hermes-sf/commit/3d8bd8adfbeeb00cc386992c33bcea3ec489f718))

## [1.55.0](https://github.com/prezero/hermes-sf/compare/v1.54.1...v1.55.0) (2024-03-14)


### Features

* **HERMES-188:** Safeguard against S3 outages ([#1583](https://github.com/prezero/hermes-sf/issues/1583)) ([f234bb7](https://github.com/prezero/hermes-sf/commit/f234bb76b6329e6734b8d517e8e6350a661ebc4b))
* **HERMES-221:** 221 lux weighing null condition ([#1584](https://github.com/prezero/hermes-sf/issues/1584)) ([e65b98e](https://github.com/prezero/hermes-sf/commit/e65b98ebf6f005adecae009d8d962b706091714c))
* **HERMES-226:** lux client present fixed true ([#1587](https://github.com/prezero/hermes-sf/issues/1587)) ([f28dd44](https://github.com/prezero/hermes-sf/commit/f28dd44fd5da2c54b5413bcdc9c54401ef46133a))


### Bug Fixes

* **HERMES-225:** Health-checks rerun option fix ([#1588](https://github.com/prezero/hermes-sf/issues/1588)) ([b2489ff](https://github.com/prezero/hermes-sf/commit/b2489ff9f0a568f5248030bd30d3bd7bf98ff936))

## [1.54.1](https://github.com/prezero/hermes-sf/compare/v1.54.0...v1.54.1) (2024-03-14)


### Bug Fixes

* **HERMES-194:** changed timestamp format for lux ([#1580](https://github.com/prezero/hermes-sf/issues/1580)) ([419fd5f](https://github.com/prezero/hermes-sf/commit/419fd5fd926082b4b3b0a079ab5ddf98009c2f6f))
* **HERMES-222:** switched lux-container-mapping ([#1582](https://github.com/prezero/hermes-sf/issues/1582)) ([5abdee4](https://github.com/prezero/hermes-sf/commit/5abdee439fc836c6e8019150c804356b046fef44))

## [1.54.0](https://github.com/prezero/hermes-sf/compare/v1.53.0...v1.54.0) (2024-03-13)


### Features

* **HERMES-218:** SAP Luxembourg files should not be base64 encoded when sent to the SAP ([#1577](https://github.com/prezero/hermes-sf/issues/1577)) ([d5ad8de](https://github.com/prezero/hermes-sf/commit/d5ad8de05129ee647e2e3928b8f5bebfb9da9b24))
* **HERMES-219:** removed termination-calls for lux-tours ([#1578](https://github.com/prezero/hermes-sf/issues/1578)) ([1dbf49b](https://github.com/prezero/hermes-sf/commit/1dbf49b35dcdef1ea1026d6f3f7b9e1976849fa9))

## [1.53.0](https://github.com/prezero/hermes-sf/compare/v1.52.0...v1.53.0) (2024-03-13)


### Features

* **HERMES-194:** 194 lux timestamps millis ([#1572](https://github.com/prezero/hermes-sf/issues/1572)) ([1b1c782](https://github.com/prezero/hermes-sf/commit/1b1c782ea902feb1c95f1993dd404d9b90f05318))
* **HERMES-208:** Added current session staff users to equipment get-by-id endpoint ([#1573](https://github.com/prezero/hermes-sf/issues/1573)) ([2f29a93](https://github.com/prezero/hermes-sf/commit/2f29a937b20bf5d15e4eee0d0898c956ec990cc1))
* **HERMES-211:** 211 disposalsite containers ([#1576](https://github.com/prezero/hermes-sf/issues/1576)) ([86f96ad](https://github.com/prezero/hermes-sf/commit/86f96adee24298f76cc867bd33f0f02fb55bf56d))


### Bug Fixes

* **HERMES-210:** fixed Redis exception during metrics gathering bringing the system down ([#1575](https://github.com/prezero/hermes-sf/issues/1575)) ([239dbd1](https://github.com/prezero/hermes-sf/commit/239dbd1d860d32694b9d2c90ce8d12cd3dc375ae))

## [1.52.0](https://github.com/prezero/hermes-sf/compare/v1.51.2...v1.52.0) (2024-03-13)


### Features

* **HERMES-189:** Opentelemetry for local & dev env ([#1566](https://github.com/prezero/hermes-sf/issues/1566)) ([ceb9683](https://github.com/prezero/hermes-sf/commit/ceb968370cfeb029769e4934c288881d4428615b))
* **HERMES-196:** 196 ger termination document ([#1571](https://github.com/prezero/hermes-sf/issues/1571)) ([fee1cfc](https://github.com/prezero/hermes-sf/commit/fee1cfc3eaafa829aeb71744520dffa0b57e846d))
* Update lux config and translations ([#1569](https://github.com/prezero/hermes-sf/issues/1569)) ([73b3efc](https://github.com/prezero/hermes-sf/commit/73b3efcf4d1d6955bcbf51ef692984f71f45e4bc))

## [1.51.2](https://github.com/prezero/hermes-sf/compare/v1.51.1...v1.51.2) (2024-03-11)


### Bug Fixes

* fixed task-rule in lux-prod-config ([#1567](https://github.com/prezero/hermes-sf/issues/1567)) ([e841aa7](https://github.com/prezero/hermes-sf/commit/e841aa7a404de185c4ce73a481b51d4cb36daf29))

## [1.51.1](https://github.com/prezero/hermes-sf/compare/v1.51.0...v1.51.1) (2024-03-08)


### Bug Fixes

* **HERMES-192:** Fix SAP germany worker looping on Redis Exception ([#1564](https://github.com/prezero/hermes-sf/issues/1564)) ([a2b7226](https://github.com/prezero/hermes-sf/commit/a2b72268e2296bedb104fe4fd2b404562a80d9c2))

## [1.51.0](https://github.com/prezero/hermes-sf/compare/v1.50.0...v1.51.0) (2024-03-08)


### Features

* **HERMES-123:** SAP Lux order non-compliance endpoint ([#1556](https://github.com/prezero/hermes-sf/issues/1556)) ([dede24b](https://github.com/prezero/hermes-sf/commit/dede24b16cc5a608b51a072564669fdd78a2a550))
* **HERMES-149:** setting fixed names without index for files ([#1558](https://github.com/prezero/hermes-sf/issues/1558)) ([cb6dfbd](https://github.com/prezero/hermes-sf/commit/cb6dfbdcdd913c8d9dbabe220acab2046b36c329))
* **HERMES-151:** equipment position ([#1554](https://github.com/prezero/hermes-sf/issues/1554)) ([ab05aa0](https://github.com/prezero/hermes-sf/commit/ab05aa02b4187d5d6b47a2bf80d244df78e4d053))
* **HERMES-154:** Adding filename extension to SAP Lux requests ([#1560](https://github.com/prezero/hermes-sf/issues/1560)) ([5cb1147](https://github.com/prezero/hermes-sf/commit/5cb1147a120bf9cb54447e7fe9dd206b678aebb4))
* **HERMES-155:** 155 populating autocomplete for luxsig ([#1563](https://github.com/prezero/hermes-sf/issues/1563)) ([41c2c83](https://github.com/prezero/hermes-sf/commit/41c2c83a07e78c16abd4cac8d6d82d944c0f4901))
* **HERMES-174:** Adding protobuf extension to speedup traces exporting ([#1561](https://github.com/prezero/hermes-sf/issues/1561)) ([2872ec4](https://github.com/prezero/hermes-sf/commit/2872ec4aed829881ea0a817452c31d3c7fe2f3ef))
* **HERMES-187:** Install open-telemetry supporting libraries ([#1562](https://github.com/prezero/hermes-sf/issues/1562)) ([1547e1c](https://github.com/prezero/hermes-sf/commit/1547e1c88c77333fc9e51b8f85609595ae507fea))


### Bug Fixes

* **HERMES-150:** Fixing the multipart request names ([#1559](https://github.com/prezero/hermes-sf/issues/1559)) ([49e9010](https://github.com/prezero/hermes-sf/commit/49e901087adfcc618077ea80c284d9c7391645e6))
* **HERMES-158:** 158 booking message ([#1557](https://github.com/prezero/hermes-sf/issues/1557)) ([369f27a](https://github.com/prezero/hermes-sf/commit/369f27a1a1607609248bd6043d4b8afbf0083c80))

## [1.50.0](https://github.com/prezero/hermes-sf/compare/v1.49.0...v1.50.0) (2024-03-06)


### Features

* 122 lux weighing endpoint ([#1551](https://github.com/prezero/hermes-sf/issues/1551)) ([5b613c0](https://github.com/prezero/hermes-sf/commit/5b613c0e67db5ccae92007e315b605ae1d5dc5ac))
* **HERMES-154:** Updating php, libraries & code styles ([#1552](https://github.com/prezero/hermes-sf/issues/1552)) ([446b70f](https://github.com/prezero/hermes-sf/commit/446b70f40ca76e433d6ffb7ec6a25623922ac716))
* interruption externalid ([#1440](https://github.com/prezero/hermes-sf/issues/1440)) ([#1548](https://github.com/prezero/hermes-sf/issues/1548)) ([fe76f8a](https://github.com/prezero/hermes-sf/commit/fe76f8addde1baaa656e228c84a19a36ba1d0549))
* Update pz config and translations ([#1550](https://github.com/prezero/hermes-sf/issues/1550)) ([8aca76e](https://github.com/prezero/hermes-sf/commit/8aca76ec4862460bfbf130ecbc2795dc27ec1ed0))


### Bug Fixes

* 153 order document eq return ([#1553](https://github.com/prezero/hermes-sf/issues/1553)) ([f0f60e2](https://github.com/prezero/hermes-sf/commit/f0f60e2818a289e977adba79a6194f01933cd7a5))

## [1.49.0](https://github.com/prezero/hermes-sf/compare/v1.48.0...v1.49.0) (2024-03-05)


### Features

* Update translations, pz and pzl config ([#1546](https://github.com/prezero/hermes-sf/issues/1546)) ([79f9d45](https://github.com/prezero/hermes-sf/commit/79f9d45bc1efb55a9999e7c5a96edb743c6b89dd))

## [1.48.0](https://github.com/prezero/hermes-sf/compare/v1.47.0...v1.48.0) (2024-03-04)


### Features

* 1529 repeated attribute ([#1542](https://github.com/prezero/hermes-sf/issues/1542)) ([fe03064](https://github.com/prezero/hermes-sf/commit/fe0306443ebb6912ed8c9f556f37d559e9e47bf3))
* Update lux config and translations ([#1545](https://github.com/prezero/hermes-sf/issues/1545)) ([121aaea](https://github.com/prezero/hermes-sf/commit/121aaea3ecfe6e029313a4d0027f8920efb7c5a7))
* Update pz config ([#1544](https://github.com/prezero/hermes-sf/issues/1544)) ([c2dca29](https://github.com/prezero/hermes-sf/commit/c2dca295d8e536ec9d83b43173f7ba1c54edca22))

## [1.47.0](https://github.com/prezero/hermes-sf/compare/v1.46.0...v1.47.0) (2024-03-01)


### Features

* bundled calls for lux in message-handlers ([#1538](https://github.com/prezero/hermes-sf/issues/1538)) ([a7060c7](https://github.com/prezero/hermes-sf/commit/a7060c7044cadc2feae1bc9991785f40ecfed96a))
* locking user login for max 30 seconds ([5db1f46](https://github.com/prezero/hermes-sf/commit/5db1f46278293e4bd220a631effb083cda55636e))
* user login process lock ([#1537](https://github.com/prezero/hermes-sf/issues/1537)) ([a199043](https://github.com/prezero/hermes-sf/commit/a199043acfd9aabf54de691632487a54e9479785))


### Bug Fixes

* portal get file mimetype ([#1541](https://github.com/prezero/hermes-sf/issues/1541)) ([e4b16eb](https://github.com/prezero/hermes-sf/commit/e4b16eb23ce25bf85bd4069376915d1ac401a160))

## [1.46.0](https://github.com/prezero/hermes-sf/compare/v1.45.0...v1.46.0) (2024-02-29)


### Features

* 1521 lux no file wait ([#1534](https://github.com/prezero/hermes-sf/issues/1534)) ([40c667e](https://github.com/prezero/hermes-sf/commit/40c667e410dfbf2649ad27f188b0c77ba794482b))
* removed obsolete termination-info from lux-order-edit ([#1518](https://github.com/prezero/hermes-sf/issues/1518)) ([4b19781](https://github.com/prezero/hermes-sf/commit/4b1978114861fe5ac9da0f55e779a119dd45343d))
* Update lux config ([#1531](https://github.com/prezero/hermes-sf/issues/1531)) ([694e796](https://github.com/prezero/hermes-sf/commit/694e7960c49c96a071c89e00c4196ead5297cb5c))

## [1.45.0](https://github.com/prezero/hermes-sf/compare/v1.44.0...v1.45.0) (2024-02-27)


### Features

* **#1499:** Using Redis as prometheus metrics storage ([#1508](https://github.com/prezero/hermes-sf/issues/1508)) ([7ae66aa](https://github.com/prezero/hermes-sf/commit/7ae66aa638a63ba19a80a0c678de9e022f00946c))
* 1324 return transition ([#1481](https://github.com/prezero/hermes-sf/issues/1481)) ([adba148](https://github.com/prezero/hermes-sf/commit/adba1486248e5cbf8a70ca916780035907161dc9))
* 1484 lux container id mapping ([#1514](https://github.com/prezero/hermes-sf/issues/1514)) ([e9d12ff](https://github.com/prezero/hermes-sf/commit/e9d12ffd339a1b47889cfde759078159bffa1c3d))
* 1486 lux order end switch ([#1515](https://github.com/prezero/hermes-sf/issues/1515)) ([e627a9e](https://github.com/prezero/hermes-sf/commit/e627a9e4a57160a3f3ada08aecacb78d4d31fd06))
* new config for germany ([#1517](https://github.com/prezero/hermes-sf/issues/1517)) ([978e10c](https://github.com/prezero/hermes-sf/commit/978e10c2a752116399c634e57a7caf6ee7cdfe90))
* Remove implicit start of tour taskgroups ([#1509](https://github.com/prezero/hermes-sf/issues/1509)) ([#1510](https://github.com/prezero/hermes-sf/issues/1510)) ([7bb1036](https://github.com/prezero/hermes-sf/commit/7bb103641822c27c29c99b4e13c503dfd15e2eb4))


### Bug Fixes

* **#1511:** StackIT Redis readonly workaround ([#1512](https://github.com/prezero/hermes-sf/issues/1512)) ([2984fa7](https://github.com/prezero/hermes-sf/commit/2984fa7662f5702f00a100700825f1ae6dcff36a))

## [1.44.0](https://github.com/prezero/hermes-sf/compare/v1.43.1...v1.44.0) (2024-02-26)


### Features

* **#1377:** Luxembourg SAP waiting time endpoint ([#1495](https://github.com/prezero/hermes-sf/issues/1495)) ([9b0d2ad](https://github.com/prezero/hermes-sf/commit/9b0d2ad15b410ca1523114dd1356f9f40c4575e8))
* **#1500:** Enhance logging to include instance ID and command ([#1502](https://github.com/prezero/hermes-sf/issues/1502)) ([d001a75](https://github.com/prezero/hermes-sf/commit/d001a753204808fd76a53e0f8a133ebcc15e38e2))
* new translations ([#1507](https://github.com/prezero/hermes-sf/issues/1507)) ([48565db](https://github.com/prezero/hermes-sf/commit/48565db472f7809fb4ca2295af6168ac1f6f9b08))


### Bug Fixes

* 1504 datetime app format ([#1506](https://github.com/prezero/hermes-sf/issues/1506)) ([58a5116](https://github.com/prezero/hermes-sf/commit/58a5116298dbe79763df9dd04d865a2085dc87f2))

## [1.43.1](https://github.com/prezero/hermes-sf/compare/v1.43.0...v1.43.1) (2024-02-26)


### Bug Fixes

* **#1496:** Fixing not handling correctly situations when lock is not acquired ([#1497](https://github.com/prezero/hermes-sf/issues/1497)) ([3b51e9a](https://github.com/prezero/hermes-sf/commit/3b51e9ae8cb226622144385f9408c17f6b5d846c))

## [1.43.0](https://github.com/prezero/hermes-sf/compare/v1.42.0...v1.43.0) (2024-02-23)


### Features

* **#1461:** Handle situations in which Redis is not available ([#1467](https://github.com/prezero/hermes-sf/issues/1467)) ([76b5ea1](https://github.com/prezero/hermes-sf/commit/76b5ea1c4e155b1d9ac7f2d0b161ac791048850e))
* **#1464:** cleanup of stateful objects ([#1476](https://github.com/prezero/hermes-sf/issues/1476)) ([2e51ece](https://github.com/prezero/hermes-sf/commit/2e51ece3eb27987f86a0d78101dba4902f22a298))
* **#1473:** Change the implementation of lux order finish call according to the new spec ([#1480](https://github.com/prezero/hermes-sf/issues/1480)) ([d89fcff](https://github.com/prezero/hermes-sf/commit/d89fcff4aa1899cc0e9b8a02a16d561fd27b462f))
* Set productId as externalid for weighing tasks ([#1475](https://github.com/prezero/hermes-sf/issues/1475)) ([#1487](https://github.com/prezero/hermes-sf/issues/1487)) ([99e6426](https://github.com/prezero/hermes-sf/commit/99e642640ce2053f25a75e7e7e53b386792e9172))


### Bug Fixes

* 1490 lux tour terminations ([#1492](https://github.com/prezero/hermes-sf/issues/1492)) ([fadbe77](https://github.com/prezero/hermes-sf/commit/fadbe779e091e75336e7de2200215d4bb612a0bc))
* select-value mapped to ids ([#1488](https://github.com/prezero/hermes-sf/issues/1488)) ([9f85f56](https://github.com/prezero/hermes-sf/commit/9f85f567395e97dd798e7a0eab421515ad6de948))


### Code Refactoring

* **#1471:** Improve consumer transactions ([#1472](https://github.com/prezero/hermes-sf/issues/1472)) ([95945c8](https://github.com/prezero/hermes-sf/commit/95945c84d36faec6e4e169909bb07393a4bd4d51))


### Tests

* filling out lux interruption-signature ([#1478](https://github.com/prezero/hermes-sf/issues/1478)) ([cba2c3f](https://github.com/prezero/hermes-sf/commit/cba2c3fcd0d3adff3a4039b584691d83eeb0a15c))
* sync lux tours synchronously during tests ([#1477](https://github.com/prezero/hermes-sf/issues/1477)) ([632e6d2](https://github.com/prezero/hermes-sf/commit/632e6d22a2906d71470b65d91b6410f08ef62420))

## [1.42.0](https://github.com/prezero/hermes-sf/compare/v1.41.0...v1.42.0) (2024-02-19)


### Features

* Config update ([#1468](https://github.com/prezero/hermes-sf/issues/1468)) ([3f07a3a](https://github.com/prezero/hermes-sf/commit/3f07a3ad803f399365adaf041626a85bd4ec7429))

## [1.41.0](https://github.com/prezero/hermes-sf/compare/v1.40.0...v1.41.0) (2024-02-19)


### Features

* **#1451:** Order finish handling for SAP lux ([#1460](https://github.com/prezero/hermes-sf/issues/1460)) ([58ab2fa](https://github.com/prezero/hermes-sf/commit/58ab2fac55b815df7f26797b68393a1260ba94ef))
* Update pz config rules ([#1466](https://github.com/prezero/hermes-sf/issues/1466)) ([a189f17](https://github.com/prezero/hermes-sf/commit/a189f178d8346564eb860174adba1d7580e94893))

## [1.40.0](https://github.com/prezero/hermes-sf/compare/v1.39.1...v1.40.0) (2024-02-16)


### Features

* Update german config ([#1462](https://github.com/prezero/hermes-sf/issues/1462)) ([24acdd2](https://github.com/prezero/hermes-sf/commit/24acdd2ff3b170d4ae3fa2a9a4e6e5aadbedcfb0))

## [1.39.1](https://github.com/prezero/hermes-sf/compare/v1.39.0...v1.39.1) (2024-02-16)


### Bug Fixes

* Add dummy weighingnote tasks for lux ([#1458](https://github.com/prezero/hermes-sf/issues/1458)) ([e04f5a3](https://github.com/prezero/hermes-sf/commit/e04f5a387bbb1d102e310fa7921a82e333c25334))

## [1.39.0](https://github.com/prezero/hermes-sf/compare/v1.38.0...v1.39.0) (2024-02-14)


### Features

* lux weighing depot required ([#1454](https://github.com/prezero/hermes-sf/issues/1454)) ([1eb366c](https://github.com/prezero/hermes-sf/commit/1eb366cf57539fccb6ffe14c2525ec0aa8f0d8c5))

## [1.38.0](https://github.com/prezero/hermes-sf/compare/v1.37.0...v1.38.0) (2024-02-14)


### Features

* Portal language endpoint ([#1407](https://github.com/prezero/hermes-sf/issues/1407)) ([#1445](https://github.com/prezero/hermes-sf/issues/1445)) ([8660bee](https://github.com/prezero/hermes-sf/commit/8660bee110b018931bab73f317b0e1a282ee3da7))
* Update lux config ([#1453](https://github.com/prezero/hermes-sf/issues/1453)) ([ee775e8](https://github.com/prezero/hermes-sf/commit/ee775e8f60a16ef26a8bf1b4017052730e4ef84c))

## [1.37.0](https://github.com/prezero/hermes-sf/compare/v1.36.0...v1.37.0) (2024-02-13)


### Features

* session token revoke ([#1442](https://github.com/prezero/hermes-sf/issues/1442)) ([c19b39c](https://github.com/prezero/hermes-sf/commit/c19b39c46f9b539bdda635ba1f76879294e446c5))

## [1.36.0](https://github.com/prezero/hermes-sf/compare/v1.35.1...v1.36.0) (2024-02-13)


### Features

* **#1427:** Luxembourg questions into order DTO task ([#1432](https://github.com/prezero/hermes-sf/issues/1432)) ([159c93d](https://github.com/prezero/hermes-sf/commit/159c93df3ebbc0a71777cae44beaafab72652ffb))
* 1430 lux order edit question ([#1438](https://github.com/prezero/hermes-sf/issues/1438)) ([e5fe3a6](https://github.com/prezero/hermes-sf/commit/e5fe3a6f060c7a2e4003e9a5b45a2794281e792a))


### Tests

* 1431 lux questions ([#1433](https://github.com/prezero/hermes-sf/issues/1433)) ([93df20d](https://github.com/prezero/hermes-sf/commit/93df20d6f82dbdc8ef36eb6b67a019f3b73aedbc))

## [1.35.1](https://github.com/prezero/hermes-sf/compare/v1.35.0...v1.35.1) (2024-02-12)


### Bug Fixes

* fixed decimal seperator in regex ([#1435](https://github.com/prezero/hermes-sf/issues/1435)) ([80b9b9b](https://github.com/prezero/hermes-sf/commit/80b9b9bc42d08e8c0dce1e578f5d3a63c95bc592))

## [1.35.0](https://github.com/prezero/hermes-sf/compare/v1.34.1...v1.35.0) (2024-02-09)


### Features

* Update lux config ([#1367](https://github.com/prezero/hermes-sf/issues/1367)) ([#1428](https://github.com/prezero/hermes-sf/issues/1428)) ([8d1ea31](https://github.com/prezero/hermes-sf/commit/8d1ea31d23905bdb0bb562a6c94ad2177e117677))

## [1.34.1](https://github.com/prezero/hermes-sf/compare/v1.34.0...v1.34.1) (2024-02-08)


### Bug Fixes

* changed sf-session-storage to redis ([#1425](https://github.com/prezero/hermes-sf/issues/1425)) ([8949277](https://github.com/prezero/hermes-sf/commit/89492775da89dcc90b8fd4bfe96fac04cead45ba))

## [1.34.0](https://github.com/prezero/hermes-sf/compare/v1.33.0...v1.34.0) (2024-02-08)


### Features

* **#1323:** Invalidated access tokens stored to Redis ([#1417](https://github.com/prezero/hermes-sf/issues/1417)) ([7d2f146](https://github.com/prezero/hermes-sf/commit/7d2f14658cefc2da5f1f3f14b8b9c8e25c065534))
* 1406 lux barcode doc ([#1422](https://github.com/prezero/hermes-sf/issues/1422)) ([f98733c](https://github.com/prezero/hermes-sf/commit/f98733c25b4c9633a6b8fbe81e25949c70c5a0f8))
* Automatically abandon all open taskgroups on equipment end book… ([#1416](https://github.com/prezero/hermes-sf/issues/1416)) ([f22db69](https://github.com/prezero/hermes-sf/commit/f22db69bd8f288f25742f1e3b21f5dcc0a341b1f))
* Update lux fixtures ([#1367](https://github.com/prezero/hermes-sf/issues/1367)) ([#1424](https://github.com/prezero/hermes-sf/issues/1424)) ([5de77b2](https://github.com/prezero/hermes-sf/commit/5de77b2d4a301b63365d41ff86e63dd49e7facab))


### Bug Fixes

* Fix lux taskrelations ([#1419](https://github.com/prezero/hermes-sf/issues/1419)) ([a01fefb](https://github.com/prezero/hermes-sf/commit/a01fefb70886aff413d432e0970bb41da01ec886))

## [1.33.0](https://github.com/prezero/hermes-sf/compare/v1.32.0...v1.33.0) (2024-02-07)


### Features

* 1412 client phonenumber ([#1415](https://github.com/prezero/hermes-sf/issues/1415)) ([220e954](https://github.com/prezero/hermes-sf/commit/220e954667c275d40b77d2484adb2f6cda9b6141))


### Bug Fixes

* argument order resulting in json decode errors ([#1413](https://github.com/prezero/hermes-sf/issues/1413)) ([f5bbc9e](https://github.com/prezero/hermes-sf/commit/f5bbc9e7c9ee0a813b77a66659eba110abb69c9e))

## [1.32.0](https://github.com/prezero/hermes-sf/compare/v1.31.1...v1.32.0) (2024-02-07)


### Features

* changed posnr-mapping for lux ([#1411](https://github.com/prezero/hermes-sf/issues/1411)) ([b3e1314](https://github.com/prezero/hermes-sf/commit/b3e13147d3d9af580dbd6fe7e14f34fb838ad548))
* Update lux config ([#1367](https://github.com/prezero/hermes-sf/issues/1367)) ([#1408](https://github.com/prezero/hermes-sf/issues/1408)) ([f9aefd8](https://github.com/prezero/hermes-sf/commit/f9aefd80159ad2372759966e047a99ed7f37e546))


### Bug Fixes

* staff_id name fix and added branch_external_id ([#1398](https://github.com/prezero/hermes-sf/issues/1398)) ([74e752f](https://github.com/prezero/hermes-sf/commit/74e752f5eb5232ec2fe89bd2c81d76c8c28fbc52))
* template tg filepath ([#1404](https://github.com/prezero/hermes-sf/issues/1404)) ([60e2765](https://github.com/prezero/hermes-sf/commit/60e2765995949e218e672a041902d714dca7e172))
* user setting dto enum ([#1402](https://github.com/prezero/hermes-sf/issues/1402)) ([3d3ba18](https://github.com/prezero/hermes-sf/commit/3d3ba1883cf4203b92e88013d1a9fa7b92110500))


### Tests

* created fresh dump on current master-fixtures ([#1409](https://github.com/prezero/hermes-sf/issues/1409)) ([86ed64d](https://github.com/prezero/hermes-sf/commit/86ed64d568bfee9d191de68c0449bf34947aba71))


### Continuous Integration

* scaling - dev 1 instance, qa 2 instances, prod 3 instances, migrations after deploy ([c508830](https://github.com/prezero/hermes-sf/commit/c50883095c49285e98d51ae7eae016538fe5da9f))
* scaling dev web backend to 3 instances, executing migrations after deployment ([b77cc68](https://github.com/prezero/hermes-sf/commit/b77cc68d1778432d4a67f46f564578a27b7950ec))

## [1.31.1](https://github.com/prezero/hermes-sf/compare/v1.31.0...v1.31.1) (2024-02-05)


### Bug Fixes

* 1391 lux tour termination ([#1400](https://github.com/prezero/hermes-sf/issues/1400)) ([f7df491](https://github.com/prezero/hermes-sf/commit/f7df49135a50daab428da21d1e2ad009112c7b64))

## [1.31.0](https://github.com/prezero/hermes-sf/compare/v1.30.0...v1.31.0) (2024-02-05)


### Features

* 1372 user setting button ([#1395](https://github.com/prezero/hermes-sf/issues/1395)) ([75e0c8e](https://github.com/prezero/hermes-sf/commit/75e0c8e346a9b2bd923fabe70957ea057b36dc94))


### Bug Fixes

* **#1393:** Proper context cleanup after message handling ([#1397](https://github.com/prezero/hermes-sf/issues/1397)) ([22c8ea4](https://github.com/prezero/hermes-sf/commit/22c8ea456719b943ea9bbbc37010ba886cc47ff2))

## [1.30.0](https://github.com/prezero/hermes-sf/compare/v1.29.0...v1.30.0) (2024-02-05)


### Features

* **#1363:** Enable logout with unfinished taskgroups ([#1389](https://github.com/prezero/hermes-sf/issues/1389)) ([3eb09f9](https://github.com/prezero/hermes-sf/commit/3eb09f98a0af76cf193b6e636e72e7fc2e34c6d2))
* **#1388:** Async import of lux tours, memory limits adjusted ([#1392](https://github.com/prezero/hermes-sf/issues/1392)) ([737a02d](https://github.com/prezero/hermes-sf/commit/737a02d99367f026a411fc3574b0e98f9c40eb3c))

## [1.29.0](https://github.com/prezero/hermes-sf/compare/v1.28.0...v1.29.0) (2024-02-02)


### Features

* **#1382:** Added photo to lux interruption end SAP waiting_time call ([#1384](https://github.com/prezero/hermes-sf/issues/1384)) ([c43cd16](https://github.com/prezero/hermes-sf/commit/c43cd16eadb3d18b18637262d1705337b76cd338))


### Bug Fixes

* Revert flush individual lux tours during import ([#1383](https://github.com/prezero/hermes-sf/issues/1383)) ([#1387](https://github.com/prezero/hermes-sf/issues/1387)) ([2821409](https://github.com/prezero/hermes-sf/commit/28214096d75fe37d54a88a01e39dcedbaba62ad3))

## [1.28.0](https://github.com/prezero/hermes-sf/compare/v1.27.0...v1.28.0) (2024-02-02)


### Features

* **#1360:** show additional input flag on signature element ([#1379](https://github.com/prezero/hermes-sf/issues/1379)) ([1246cf1](https://github.com/prezero/hermes-sf/commit/1246cf1b5e4ce53534d5c57d36d01044e8f7ede0))


### Bug Fixes

* flush individual lux tours during import ([#1383](https://github.com/prezero/hermes-sf/issues/1383)) ([97358e9](https://github.com/prezero/hermes-sf/commit/97358e963c398b57f6c888ec6e2ff57ff9ae69c0))

## [1.27.0](https://github.com/prezero/hermes-sf/compare/v1.26.1...v1.27.0) (2024-02-01)


### Features

* **#1365:** Adding autocomplete options to number of elements ([#1378](https://github.com/prezero/hermes-sf/issues/1378)) ([90d5f5a](https://github.com/prezero/hermes-sf/commit/90d5f5a2dfb2387150e592015e588ead723ad339))
* **#1371:** lux interruption end call to waiting_time endpoint ([#1374](https://github.com/prezero/hermes-sf/issues/1374)) ([e013a02](https://github.com/prezero/hermes-sf/commit/e013a024ee4cd2d1d2a4127ff975849a6e604811))
* **#958:** addtional information to tour, tour config, order, order config & interruption ([#1373](https://github.com/prezero/hermes-sf/issues/1373)) ([f562df0](https://github.com/prezero/hermes-sf/commit/f562df0b5c803e8cb54130d89b1061e5c0afe496))
* added 'status' to taskgroups of orders ([#1350](https://github.com/prezero/hermes-sf/issues/1350)) ([c6bbbf7](https://github.com/prezero/hermes-sf/commit/c6bbbf70c4d4f69b2da9ebbe4d5d4a2e1eca15ac))
* interruption lux start ([#1369](https://github.com/prezero/hermes-sf/issues/1369)) ([5bfda19](https://github.com/prezero/hermes-sf/commit/5bfda19deee284ba3d8b3fc7226649a4bb1dac30))
* Update germany fixtures ([#1368](https://github.com/prezero/hermes-sf/issues/1368)) ([7f3066e](https://github.com/prezero/hermes-sf/commit/7f3066e97c13cc6ad9cbc7d0dcd964d5175e9853))
* Update lux fixtures ([#1380](https://github.com/prezero/hermes-sf/issues/1380)) ([0ce815e](https://github.com/prezero/hermes-sf/commit/0ce815e85cdbc03e743035b307095cb054c0c485))

## [1.26.1](https://github.com/prezero/hermes-sf/compare/v1.26.0...v1.26.1) (2024-01-29)


### Bug Fixes

* restored config ([#1358](https://github.com/prezero/hermes-sf/issues/1358)) ([c89608c](https://github.com/prezero/hermes-sf/commit/c89608c436376c6decf7a033c00751cc2369231b))

## [1.26.0](https://github.com/prezero/hermes-sf/compare/v1.25.1...v1.26.0) (2024-01-29)


### Features

* **#1353:** Unique tasks per tg type sent to SAP Germany confirmation DTO ([#1354](https://github.com/prezero/hermes-sf/issues/1354)) ([757b40a](https://github.com/prezero/hermes-sf/commit/757b40a63bbb27aed4dc8b98e9624b49965eb6f0))


### Bug Fixes

* config pz without km ([#1357](https://github.com/prezero/hermes-sf/issues/1357)) ([08b6bc1](https://github.com/prezero/hermes-sf/commit/08b6bc11cf4892bcabe8c57e979c0f5ba7281baf))

## [1.25.1](https://github.com/prezero/hermes-sf/compare/v1.25.0...v1.25.1) (2024-01-29)


### Bug Fixes

* changed identifier for backend-doc to work with download-endpoint ([#1349](https://github.com/prezero/hermes-sf/issues/1349)) ([c0ccd2b](https://github.com/prezero/hermes-sf/commit/c0ccd2b755674340d87001c3b64983e5c3595c54))

## [1.25.0](https://github.com/prezero/hermes-sf/compare/v1.24.0...v1.25.0) (2024-01-26)


### Features

* **#1342:** Removing old additional informations and leftover document files on order update ([#1348](https://github.com/prezero/hermes-sf/issues/1348)) ([b45183c](https://github.com/prezero/hermes-sf/commit/b45183c3c9fd3d4f2f4774b453d62df91debcccd))
* 1282 file endpoint ([#1311](https://github.com/prezero/hermes-sf/issues/1311)) ([419905d](https://github.com/prezero/hermes-sf/commit/419905d8e2c120c8ed171e7e6913a799f6d5995c))
* 1343 unit labels ([#1347](https://github.com/prezero/hermes-sf/issues/1347)) ([49757be](https://github.com/prezero/hermes-sf/commit/49757be30226d18ad692b030141efc2049d90b3b))


### Bug Fixes

* fixed some translations ([#1345](https://github.com/prezero/hermes-sf/issues/1345)) ([8dbadab](https://github.com/prezero/hermes-sf/commit/8dbadabe4d061022ddcde77b21bfcbdc637be823))

## [1.24.0](https://github.com/prezero/hermes-sf/compare/v1.23.3...v1.24.0) (2024-01-25)


### Features

* **#1308:** file download endpoint ([#1340](https://github.com/prezero/hermes-sf/issues/1340)) ([b5e7460](https://github.com/prezero/hermes-sf/commit/b5e746043bef89f988aae0b6e655e383fa900176))
* **#1325:** Adding different pagination support, tags, refactoring existing portal endpoints ([#1339](https://github.com/prezero/hermes-sf/issues/1339)) ([b5e1380](https://github.com/prezero/hermes-sf/commit/b5e13804478d4a0b4dbaf60b75a101f3e51f082a))
* 1326 lux interruptions ([#1341](https://github.com/prezero/hermes-sf/issues/1341)) ([74d865e](https://github.com/prezero/hermes-sf/commit/74d865ef624037700c8dfc138eb8bf418207bd70))


### Bug Fixes

* docker-compose unified ([#1337](https://github.com/prezero/hermes-sf/issues/1337)) ([6e8ed83](https://github.com/prezero/hermes-sf/commit/6e8ed836a5050a33f4cda114b6ea33b924a499da))

## [1.23.3](https://github.com/prezero/hermes-sf/compare/v1.23.2...v1.23.3) (2024-01-24)


### Bug Fixes

* Add docker compose file for MacOs ([#1331](https://github.com/prezero/hermes-sf/issues/1331)) ([3fab494](https://github.com/prezero/hermes-sf/commit/3fab4948aedff92a2e5de88ef3dafcea1247aeb0))
* macos docker compose file ([#1336](https://github.com/prezero/hermes-sf/issues/1336)) ([f923fd1](https://github.com/prezero/hermes-sf/commit/f923fd1ecfb0999fb5c302dbdad811adfd0570e0))
* updated new translations ([#1333](https://github.com/prezero/hermes-sf/issues/1333)) ([f9fd61d](https://github.com/prezero/hermes-sf/commit/f9fd61d8f2fab04e9c4162200841f44d3d273925))

## [1.23.2](https://github.com/prezero/hermes-sf/compare/v1.23.1...v1.23.2) (2024-01-24)


### Bug Fixes

* added hopefully helpful logs ([#1329](https://github.com/prezero/hermes-sf/issues/1329)) ([3d4178c](https://github.com/prezero/hermes-sf/commit/3d4178c728407a48509af1eeb1dca95b68460d08))

## [1.23.1](https://github.com/prezero/hermes-sf/compare/v1.23.0...v1.23.1) (2024-01-24)


### Bug Fixes

* lux container dd deactivated (memory-prob) ([#1327](https://github.com/prezero/hermes-sf/issues/1327)) ([b7e048b](https://github.com/prezero/hermes-sf/commit/b7e048bdbc43eef43fb75c799c03d838f0d776f8))

## [1.23.0](https://github.com/prezero/hermes-sf/compare/v1.22.1...v1.23.0) (2024-01-23)


### Features

* added endpoint for json-file-list from S3 ([#1312](https://github.com/prezero/hermes-sf/issues/1312)) ([bcd1f81](https://github.com/prezero/hermes-sf/commit/bcd1f815593d96cfbf80b70c3716e8850900742d))
* Backend document endpoint ([#1315](https://github.com/prezero/hermes-sf/issues/1315)) ([d477bdf](https://github.com/prezero/hermes-sf/commit/d477bdfe700451d2d9a311006216d14f1e40a91f))
* Mapping lux order-type from lux-type ([#1319](https://github.com/prezero/hermes-sf/issues/1319)) ([1506ac6](https://github.com/prezero/hermes-sf/commit/1506ac6777e93f703187542d5710f56963c12842))
* renamed additional-info detail ([#1318](https://github.com/prezero/hermes-sf/issues/1318)) ([afe3c09](https://github.com/prezero/hermes-sf/commit/afe3c09a7a66f8c84f696bc15d6d3fdfb491a0a5))


### Bug Fixes

* fixed repeatable, changed testdata to auto-test it ([#1307](https://github.com/prezero/hermes-sf/issues/1307)) ([7d3117e](https://github.com/prezero/hermes-sf/commit/7d3117e375265466d11185e1fd7287d847d510a4))

## [1.22.1](https://github.com/prezero/hermes-sf/compare/v1.22.0...v1.22.1) (2024-01-19)


### Bug Fixes

* new fixtures for config ([#1305](https://github.com/prezero/hermes-sf/issues/1305)) ([4e154ff](https://github.com/prezero/hermes-sf/commit/4e154ff9b058218721451eb718665bba253e1305))

## [1.22.0](https://github.com/prezero/hermes-sf/compare/v1.21.0...v1.22.0) (2024-01-18)


### Features

* portal api staff endpoint ([#1297](https://github.com/prezero/hermes-sf/issues/1297)) ([a126b32](https://github.com/prezero/hermes-sf/commit/a126b32e82819838de9b125a95d08b578be2ffaf))


### Bug Fixes

* single user token ([#1303](https://github.com/prezero/hermes-sf/issues/1303)) ([fefa8a6](https://github.com/prezero/hermes-sf/commit/fefa8a6738e08798d280861342d06499e4db3e95))

## [1.21.0](https://github.com/prezero/hermes-sf/compare/v1.20.0...v1.21.0) (2024-01-18)


### Features

* map endpoint ([#1280](https://github.com/prezero/hermes-sf/issues/1280)) ([#1300](https://github.com/prezero/hermes-sf/issues/1300)) ([fd547f4](https://github.com/prezero/hermes-sf/commit/fd547f4c883e2bd65e8d2fecbd7801944cb734b4))

## [1.20.0](https://github.com/prezero/hermes-sf/compare/v1.19.0...v1.20.0) (2024-01-18)


### Features

* adjusting lux schedule, combining schedulers into areas ([#1295](https://github.com/prezero/hermes-sf/issues/1295)) ([5d2b82c](https://github.com/prezero/hermes-sf/commit/5d2b82cd59da169e604bdc56a20497fd4e3a00ce))


### Bug Fixes

* change in local consumer-conf ([#1299](https://github.com/prezero/hermes-sf/issues/1299)) ([3561c0c](https://github.com/prezero/hermes-sf/commit/3561c0cb4c9041615f6b19b1244224abc70152a9))

## [1.19.0](https://github.com/prezero/hermes-sf/compare/v1.18.0...v1.19.0) (2024-01-17)


### Features

* parsing durch order-document-date if possible ([#1293](https://github.com/prezero/hermes-sf/issues/1293)) ([025c001](https://github.com/prezero/hermes-sf/commit/025c001547b74ca8efba338451fcfa4a6321c13f))

## [1.18.0](https://github.com/prezero/hermes-sf/compare/v1.17.1...v1.18.0) (2024-01-17)


### Features

* 1284 element option source ([#1289](https://github.com/prezero/hermes-sf/issues/1289)) ([64a09e2](https://github.com/prezero/hermes-sf/commit/64a09e286153d2ae6a6825cfade101294f9d55f0))
* API abstraction ([#1288](https://github.com/prezero/hermes-sf/issues/1288)) ([094b804](https://github.com/prezero/hermes-sf/commit/094b8040484b93a0a4e7559e0bc774e09284467d))
* changed mapping for incoming options ([#1286](https://github.com/prezero/hermes-sf/issues/1286)) ([a758f8d](https://github.com/prezero/hermes-sf/commit/a758f8d1d5062a9939c16f5880cfc2f10c47b010))

## [1.17.1](https://github.com/prezero/hermes-sf/compare/v1.17.0...v1.17.1) (2024-01-15)


### Bug Fixes

* 1277 equipment deadlock ([#1278](https://github.com/prezero/hermes-sf/issues/1278)) ([1898f44](https://github.com/prezero/hermes-sf/commit/1898f449c2b738b0c4e7872fed1babb76a45ecc8))

## [1.17.0](https://github.com/prezero/hermes-sf/compare/v1.16.1...v1.17.0) (2024-01-12)


### Features

* portal api equipment get endpoints ([#1274](https://github.com/prezero/hermes-sf/issues/1274)) ([c7c8a73](https://github.com/prezero/hermes-sf/commit/c7c8a73c2f4cbc9ed8cab5cc567e2c0aa9b3a9c7))

## [1.16.1](https://github.com/prezero/hermes-sf/compare/v1.16.0...v1.16.1) (2024-01-12)


### Bug Fixes

* Prevent list with index keys ([#1268](https://github.com/prezero/hermes-sf/issues/1268)) ([#1272](https://github.com/prezero/hermes-sf/issues/1272)) ([3f78ee6](https://github.com/prezero/hermes-sf/commit/3f78ee629e86c73fa36ca7106a02f912fa482c5c))

## [1.16.0](https://github.com/prezero/hermes-sf/compare/v1.15.1...v1.16.0) (2024-01-12)


### Features

* [#1253](https://github.com/prezero/hermes-sf/issues/1253) container serial number adding after tour is processed ([#1262](https://github.com/prezero/hermes-sf/issues/1262)) ([50df12f](https://github.com/prezero/hermes-sf/commit/50df12f77687fa1996a96a3dca9d158f694af41f))
* 1260 double tg master check ([#1267](https://github.com/prezero/hermes-sf/issues/1267)) ([c9df32b](https://github.com/prezero/hermes-sf/commit/c9df32b7a5af8573fd26b5cd982a1b8ebbbf95d2))
* 1269 tour tg config ([#1271](https://github.com/prezero/hermes-sf/issues/1271)) ([4b039d7](https://github.com/prezero/hermes-sf/commit/4b039d77edd04f2b596f19945476ad7bbfd57c35))
* changed container-endpoint ([#1270](https://github.com/prezero/hermes-sf/issues/1270)) ([f9fb555](https://github.com/prezero/hermes-sf/commit/f9fb55587c9a9bc0342cd297c56e60b705c012fe))


### Bug Fixes

* changed fixtures to prepare [#1253](https://github.com/prezero/hermes-sf/issues/1253) ([#1254](https://github.com/prezero/hermes-sf/issues/1254)) ([b676abf](https://github.com/prezero/hermes-sf/commit/b676abf293f07370537a9d1eefdef1618fb15874))
* ignoring softdelete on truncating config-tables ([#1259](https://github.com/prezero/hermes-sf/issues/1259)) ([771b6b8](https://github.com/prezero/hermes-sf/commit/771b6b882faf09994bfb5668fe57b0027f6c0277))

## [1.15.1](https://github.com/prezero/hermes-sf/compare/v1.15.0...v1.15.1) (2024-01-08)


### Bug Fixes

* replaced file-id with file-path for order-document-pdf-created ([#1249](https://github.com/prezero/hermes-sf/issues/1249)) ([d244a1f](https://github.com/prezero/hermes-sf/commit/d244a1f3da2380f575ec8655336ee449753c187a))

## [1.15.0](https://github.com/prezero/hermes-sf/compare/v1.14.0...v1.15.0) (2024-01-05)


### Features

* command for resetting lux-staff-user-pws ([#1246](https://github.com/prezero/hermes-sf/issues/1246)) ([9b0bd90](https://github.com/prezero/hermes-sf/commit/9b0bd90e604a6e9ba2756b154024c31b086051a4))

## [1.14.0](https://github.com/prezero/hermes-sf/compare/v1.13.0...v1.14.0) (2024-01-05)


### Features

* s3 folders ([#1223](https://github.com/prezero/hermes-sf/issues/1223)) ([e51247a](https://github.com/prezero/hermes-sf/commit/e51247aa209f890bf026be7a4a34deb54ca818ac))

## [1.13.0](https://github.com/prezero/hermes-sf/compare/v1.12.0...v1.13.0) (2024-01-04)


### Features

* [#1232](https://github.com/prezero/hermes-sf/issues/1232) changes in how lux container sap request is constructed ([#1241](https://github.com/prezero/hermes-sf/issues/1241)) ([1a45046](https://github.com/prezero/hermes-sf/commit/1a45046f4729f2534df06d369896012182d60212))

## [1.12.0](https://github.com/prezero/hermes-sf/compare/v1.11.1...v1.12.0) (2024-01-04)


### Features

* [#1227](https://github.com/prezero/hermes-sf/issues/1227) adding tenant scopes into JWT after authentication ([#1228](https://github.com/prezero/hermes-sf/issues/1228)) ([85660b4](https://github.com/prezero/hermes-sf/commit/85660b4197dd5f4f465d8717444aabc0baf43fe2))
* [#1231](https://github.com/prezero/hermes-sf/issues/1231) Addint order/end call on order completion/termination for lux ([#1237](https://github.com/prezero/hermes-sf/issues/1237)) ([9e112f7](https://github.com/prezero/hermes-sf/commit/9e112f7844242b3f46273676414f4ab6b5ad084c))
* updated fixed config for lux ([#1239](https://github.com/prezero/hermes-sf/issues/1239)) ([4e7966a](https://github.com/prezero/hermes-sf/commit/4e7966a9347e47d3b963cde3c34c78c9003ab5d8))


### Bug Fixes

* [#1230](https://github.com/prezero/hermes-sf/issues/1230) correcting timestamps sent to lux sap via the status calls ([#1236](https://github.com/prezero/hermes-sf/issues/1236)) ([56c28a7](https://github.com/prezero/hermes-sf/commit/56c28a77b20fc8eed7ec545f46ac4d0d48f2b74e))
* [#1233](https://github.com/prezero/hermes-sf/issues/1233) order edit call for lux fix ([#1238](https://github.com/prezero/hermes-sf/issues/1238)) ([4e6c3f5](https://github.com/prezero/hermes-sf/commit/4e6c3f5ec6be94e20f4fb354ff06c55085be19ca))
* set the attribute for manual creation for template-taskgroup ([#1235](https://github.com/prezero/hermes-sf/issues/1235)) ([d5ead42](https://github.com/prezero/hermes-sf/commit/d5ead420a17b305d671474ab60ef43d49f75c2b6))

## [1.11.1](https://github.com/prezero/hermes-sf/compare/v1.11.0...v1.11.1) (2024-01-02)


### Bug Fixes

* message context setting when the message is created, not when it's sent ([#1225](https://github.com/prezero/hermes-sf/issues/1225)) ([f42bc62](https://github.com/prezero/hermes-sf/commit/f42bc6289c3e449269601098a89357dca5c42dd7))

## [1.11.0](https://github.com/prezero/hermes-sf/compare/v1.10.1...v1.11.0) (2023-12-22)


### Features

* Abandon equipment taskgroups on abandoning session of equipment… ([#1218](https://github.com/prezero/hermes-sf/issues/1218)) ([39232c7](https://github.com/prezero/hermes-sf/commit/39232c741bb82d94e71beffc2a1b1125515a4873))


### Bug Fixes

* checked metadata microtime result before setting ([#1222](https://github.com/prezero/hermes-sf/issues/1222)) ([854a0df](https://github.com/prezero/hermes-sf/commit/854a0df0f0614ace1037a0481ad12ff097d5ea46))

## [1.10.1](https://github.com/prezero/hermes-sf/compare/v1.10.0...v1.10.1) (2023-12-21)


### Bug Fixes

* changed middleware switch to include redis sync redirected envel… ([#1208](https://github.com/prezero/hermes-sf/issues/1208)) ([b386e3e](https://github.com/prezero/hermes-sf/commit/b386e3e239d06f81d4af0d0a1e9afe302315c387))
* settting tenant after injection in cronjob ([#1219](https://github.com/prezero/hermes-sf/issues/1219)) ([af681e2](https://github.com/prezero/hermes-sf/commit/af681e2f3481d8a4db2b5570c44448881c58b3cc))

## [1.10.0](https://github.com/prezero/hermes-sf/compare/v1.9.0...v1.10.0) (2023-12-20)


### Features

* set default taskgroup ([#1180](https://github.com/prezero/hermes-sf/issues/1180)) ([#1203](https://github.com/prezero/hermes-sf/issues/1203)) ([bba22fe](https://github.com/prezero/hermes-sf/commit/bba22fe1a32d128e6bfbfc6c2a76b61a61cc691e))


### Bug Fixes

* 1198 s3 filelist quickfix ([#1207](https://github.com/prezero/hermes-sf/issues/1207)) ([6581cf8](https://github.com/prezero/hermes-sf/commit/6581cf87dbb427f8524c951cc4cf3981ed3aaee7))

## [1.9.0](https://github.com/prezero/hermes-sf/compare/v1.8.0...v1.9.0) (2023-12-18)


### Features

* 1194 server side pagination ([#1199](https://github.com/prezero/hermes-sf/issues/1199)) ([7f7cfc8](https://github.com/prezero/hermes-sf/commit/7f7cfc88e32eb84e23cb9ad627b7e38879a6d859))


### Bug Fixes

* 1195 lamesch status call ([#1197](https://github.com/prezero/hermes-sf/issues/1197)) ([58175b9](https://github.com/prezero/hermes-sf/commit/58175b996a3fcec682ee095cea05d935d3c03ade))

## [1.8.0](https://github.com/prezero/hermes-sf/compare/v1.7.1...v1.8.0) (2023-12-14)


### Features

* 1174 lux nl json storage ([#1183](https://github.com/prezero/hermes-sf/issues/1183)) ([2236fb6](https://github.com/prezero/hermes-sf/commit/2236fb6175cdb994af28c5c84681c84650f70799))


### Bug Fixes

* docker-compose image-tags, minor fix in disposalsite-creation ([#1190](https://github.com/prezero/hermes-sf/issues/1190)) ([9bc9813](https://github.com/prezero/hermes-sf/commit/9bc98137f10da00fc965bdde94c6c2ca7a4f2ee0))
* removed html, splitted content, removed whitspace-sequences ([#1192](https://github.com/prezero/hermes-sf/issues/1192)) ([19fb359](https://github.com/prezero/hermes-sf/commit/19fb359e0b7567f54aaac5605a7cecd9c6b3f01f))


### Build System

* update base image tags ([#1189](https://github.com/prezero/hermes-sf/issues/1189)) ([125135a](https://github.com/prezero/hermes-sf/commit/125135a82da4971237211b066678c236534adf47))

## [1.7.1](https://github.com/prezero/hermes-sf/compare/v1.7.0...v1.7.1) (2023-12-13)


### Bug Fixes

* add tenant to s3 prefixname ([#1176](https://github.com/prezero/hermes-sf/issues/1176)) ([d3bc503](https://github.com/prezero/hermes-sf/commit/d3bc503eecba7e6923c3b4a3454966f64001d2cf))
* sap-lux-mapping continues at all exceptions during tour-creation ([#1179](https://github.com/prezero/hermes-sf/issues/1179)) ([2c04808](https://github.com/prezero/hermes-sf/commit/2c04808de7d6d18902311b78db21d430b55a6b37))

## [1.7.0](https://github.com/prezero/hermes-sf/compare/v1.6.0...v1.7.0) (2023-12-11)


### Features

* Add upload of raw json recieved from lux api to s3 ([#1164](https://github.com/prezero/hermes-sf/issues/1164)) ([#1172](https://github.com/prezero/hermes-sf/issues/1172)) ([a6950f4](https://github.com/prezero/hermes-sf/commit/a6950f448a55d6e75cf99db128c7c014e07eb5e1))

## [1.6.0](https://github.com/prezero/hermes-sf/compare/v1.5.0...v1.6.0) (2023-12-11)


### Features

* [#1160](https://github.com/prezero/hermes-sf/issues/1160) Symfony update to 6.4, symfony recipes update, libraries update ([#1162](https://github.com/prezero/hermes-sf/issues/1162)) ([8d547b7](https://github.com/prezero/hermes-sf/commit/8d547b7aed71c8c9409eb8cc0d1d9bd1a3e06c0b))
* [#1161](https://github.com/prezero/hermes-sf/issues/1161): Order Confirmation to SAP Germany deferred DTO construction ([#1166](https://github.com/prezero/hermes-sf/issues/1166)) ([6d6ea11](https://github.com/prezero/hermes-sf/commit/6d6ea117743e3ebd8f32e19f6f13bf7e98e25e88))
* integrate file access & map visualization ([#1118](https://github.com/prezero/hermes-sf/issues/1118)) ([8e4221b](https://github.com/prezero/hermes-sf/commit/8e4221b5360cee06d6ae4194e2af8aa01b837d14))


### Bug Fixes

* [#1156](https://github.com/prezero/hermes-sf/issues/1156): fix SAP requests order for taskgroups and tasks ([#1157](https://github.com/prezero/hermes-sf/issues/1157)) ([66e8806](https://github.com/prezero/hermes-sf/commit/66e8806dda4d344ce5cf69d74378575c1d8c7e52))
* [#1169](https://github.com/prezero/hermes-sf/issues/1169) added access check on order endpoints ([#1170](https://github.com/prezero/hermes-sf/issues/1170)) ([a71079a](https://github.com/prezero/hermes-sf/commit/a71079a884c17428f4b6ec056d6f02839c6d2d8b))
* Refactor attribute name containerType to containerTypeList ([#1154](https://github.com/prezero/hermes-sf/issues/1154)) ([#1167](https://github.com/prezero/hermes-sf/issues/1167)) ([d01c6c9](https://github.com/prezero/hermes-sf/commit/d01c6c9955f9eed997b9213b5f644c921743809e))

## [1.5.0](https://github.com/prezero/hermes-sf/compare/v1.4.1...v1.5.0) (2023-12-05)


### Features

* lux equipment-weight as float ([#1152](https://github.com/prezero/hermes-sf/issues/1152)) ([636ebae](https://github.com/prezero/hermes-sf/commit/636ebae8e7bae91124ad93d4431d99baf6761b6a))

## [1.4.1](https://github.com/prezero/hermes-sf/compare/v1.4.0...v1.4.1) (2023-12-04)


### Bug Fixes

* restored correct config-fixtures for germany, removed pw-reset-c… ([#1147](https://github.com/prezero/hermes-sf/issues/1147)) ([6712e10](https://github.com/prezero/hermes-sf/commit/6712e103c68b1662d70668d8b02548f53b83bc21))

## [1.4.0](https://github.com/prezero/hermes-sf/compare/v1.3.1...v1.4.0) (2023-12-04)


### Features

* 1145 lux endpoint change ([#1148](https://github.com/prezero/hermes-sf/issues/1148)) ([83ce1e4](https://github.com/prezero/hermes-sf/commit/83ce1e4523bc803b710346abb3655f01048c9d32))
* changed lux-endpoint back to one client ([#1146](https://github.com/prezero/hermes-sf/issues/1146)) ([64cc8e1](https://github.com/prezero/hermes-sf/commit/64cc8e1aacf304f21db539d596b29ad9a3dda3c9))


### Bug Fixes

* [#1143](https://github.com/prezero/hermes-sf/issues/1143) configure galera cluster to wait for master sync before executing select statements ([#1144](https://github.com/prezero/hermes-sf/issues/1144)) ([7c1915f](https://github.com/prezero/hermes-sf/commit/7c1915f124ca738a08b2264e271374711754a5ed))


### Continuous Integration

* production deploy config ([#1141](https://github.com/prezero/hermes-sf/issues/1141)) ([7a01a66](https://github.com/prezero/hermes-sf/commit/7a01a66e4511a8f8935a3560a82c122d8f59ed39))

## [1.3.1](https://github.com/prezero/hermes-sf/compare/v1.3.0...v1.3.1) (2023-11-30)


### Bug Fixes

* command to reset lux-passwords for staff ([#1139](https://github.com/prezero/hermes-sf/issues/1139)) ([9856a42](https://github.com/prezero/hermes-sf/commit/9856a42c83db36f333dcd44f0857e1b26763fce6))
* dispatch messages after current handling [#1136](https://github.com/prezero/hermes-sf/issues/1136) ([#1137](https://github.com/prezero/hermes-sf/issues/1137)) ([da044ba](https://github.com/prezero/hermes-sf/commit/da044ba17559b912f86a50d3662a4b0162e4337d))

## [1.3.0](https://github.com/prezero/hermes-sf/compare/v1.2.0...v1.3.0) (2023-11-30)


### Features

* added wrong secteur-content to errormessage for easier analysis ([#1133](https://github.com/prezero/hermes-sf/issues/1133)) ([ded7c7e](https://github.com/prezero/hermes-sf/commit/ded7c7e551238b9878b7b5b71923b14767d22bd8))

## [1.2.0](https://github.com/prezero/hermes-sf/compare/v1.1.0...v1.2.0) (2023-11-29)


### Features

* SAP-lux working with users displayNumber instead of staffId ([#1126](https://github.com/prezero/hermes-sf/issues/1126)) ([1e38af9](https://github.com/prezero/hermes-sf/commit/1e38af977628c5c974bcc7118072d8bce6bedadf))
* sending en_GB as default laguage, when user has no setting ([#1127](https://github.com/prezero/hermes-sf/issues/1127)) ([e253b61](https://github.com/prezero/hermes-sf/commit/e253b61c27391621afd228701e530dfbb62f1bc5))


### Continuous Integration

* changed database service name for dev ([#1130](https://github.com/prezero/hermes-sf/issues/1130)) ([86b3601](https://github.com/prezero/hermes-sf/commit/86b360131b8b46cfa8cfe1dc527cfbb8f9a8479e))
* changed database service name for dev ([#1132](https://github.com/prezero/hermes-sf/issues/1132)) ([fdde635](https://github.com/prezero/hermes-sf/commit/fdde635cac2a8d5242c47a5e6e6b4cebb35e6460))
* prevent deployments to same env to run over each other ([#1131](https://github.com/prezero/hermes-sf/issues/1131)) ([82d81af](https://github.com/prezero/hermes-sf/commit/82d81af9ce8c61df637286c00113c2cd2f480521))
* release please configuration for separate release branch ([#1128](https://github.com/prezero/hermes-sf/issues/1128)) ([6e9bc30](https://github.com/prezero/hermes-sf/commit/6e9bc30b0b7ca9e0cbe9cc7401cdcfcd24a279db))

## [1.1.0](https://github.com/prezero/hermes-sf/compare/v1.0.0...v1.1.0) (2023-11-28)


### Features

* new endpoint for sap-lux-equipment, new endpoint for sap-lux-containers ([0365d8c](https://github.com/prezero/hermes-sf/commit/0365d8c6da76edb93fcdeb0425af75c87b6d7d42))


### Continuous Integration

* added grumphp pre commit checks ([#1120](https://github.com/prezero/hermes-sf/issues/1120)) ([da8b951](https://github.com/prezero/hermes-sf/commit/da8b951936fc02cff2ee22dea7513d4382d8553b))
* fixing qa deployment ([ad7ad08](https://github.com/prezero/hermes-sf/commit/ad7ad087bb4b0ad6fa97c1e2283b5853f1574a2b))
* updating github prezero credentials ([#1121](https://github.com/prezero/hermes-sf/issues/1121)) ([293e8b5](https://github.com/prezero/hermes-sf/commit/293e8b50a59aaaae3947bc2a7b77f6bffcd901d4))

## [1.0.0](https://github.com/prezero/hermes-sf/compare/0.3.1...v1.0.0) (2023-11-28)


### Features

* conventional commits based releases ([fedc6ff](https://github.com/prezero/hermes-sf/commit/fedc6ff9d54fd8b4245e88bdde80aa50005def8d))


### Bug Fixes

* adding yarn cache clean after installing packages (per hadolint suggestion) ([60ca032](https://github.com/prezero/hermes-sf/commit/60ca032f0efd43e8e6fe27b8e08c8371e7724e1f))
* label replaced by name, removed wrong alert-reason from sig-pdf-gen ([507061a](https://github.com/prezero/hermes-sf/commit/507061aeb4511770955fa49a574e3e84d2385ecd))


### Continuous Integration

* adding conventional commits config, image building workflows and deployment to dev and qa ([c4776e8](https://github.com/prezero/hermes-sf/commit/c4776e850b475330273a1d58d710dc9f25fcba14))
* fixing workflows ([dd9582d](https://github.com/prezero/hermes-sf/commit/dd9582db37ad89587f0bba5826a194e8ed42c564))
