name: Clean Up Container Registry

on:
  schedule:
    - cron: '0 0 * * *'  # Runs every day at midnight

  workflow_dispatch:


jobs:
  cleanup:
    runs-on: prezero-github-runner
    strategy:
        matrix:
          image: [hermes-backend, hermes-consumer]
    steps:
    - name: Delete old tags
      uses: peterstolz/containercrop@b041efa0d4da17fbfb2caa39cbc6b67f3c6abb88 # v1
      with:
        image-name: ${{ matrix.image }}
        cut-off: 'a week ago UTC'
        token: ${{ secrets.PREZERO_GITHUB_TOKEN }}
        filter-tags: "*.*.*"
        keep-at-least: '25'
        dry-run: 'false'

    - name: Delete old untagged images
      uses: peterstolz/containercrop@b041efa0d4da17fbfb2caa39cbc6b67f3c6abb88 # v1
      with:
        image-name: ${{ matrix.image }}
        cut-off: 'two days ago UTC'
        token: ${{ secrets.PREZERO_GITHUB_TOKEN }}
        untagged-only: 'true'
        dry-run: 'false'
