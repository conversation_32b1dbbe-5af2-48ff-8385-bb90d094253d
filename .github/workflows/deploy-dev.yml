name: DEV Deployment to k8s

on:
    push:
        branches:
            - master
    workflow_call:
    workflow_dispatch:

env:
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    COMPOSER_AUTH: '{"github-oauth":{"github.com":"${{ secrets.PREZERO_GITHUB_TOKEN }}"}}'

concurrency:
    group: hermes-backend-deployment-dev
    cancel-in-progress: false

jobs:
    check-github-action-syntax:
        runs-on: prezero-github-runner
        steps:
            -   name: Get project files
                uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
            -   name: Check workflow files
                run: |
                    bash <(curl https://raw.githubusercontent.com/rhysd/actionlint/main/scripts/download-actionlint.bash)
                    ./actionlint -color
                shell: bash
    build-docker-image:
        name: Build Container Image
        uses: prezero/workflows/.github/workflows/docker-build.yaml@97fee31f2416ee4224ca1436128639373e79ab92 # 1.34.1
        secrets: inherit
        with:
            ENV: ""
            DOCKERFILE: "docker/dev-k8s/Dockerfile"
            IMAGE: "ghcr.io/prezero/hermes-backend-k8s"
            TAG: "dev"
            TARGET_LAYER: ""
            RELEASE_CREATED: false
            BUILD_ARGS: ""
            # Re-enable when they release a new version
            enable_dockerfile_lint: "false"

    deployment:
        runs-on: prezero-github-runner
        needs: [ build-docker-image ]
        environment: dev

        steps:
            -   name: Argo CD Login
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: login argocd.dev.hermes.runs.onstackit.cloud
                    options: --username admin --password ${{ secrets.ARGOCD_PASSWORD }}

            -   name: Argo CD Sync Backend
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app sync hermes-backend
                    options: --prune --label 'argo-deploy=backend'

            -   name: Argo CD Sync External-Secrets
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app actions run hermes-backend
                    options: refresh --kind ExternalSecret --all

            -   name: Argo CD Restart All Deployments
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app actions run hermes-backend
                    options: restart --kind Deployment --all

            -   name: Argo CD Restart All StatefulSets
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app actions run hermes-backend
                    options: restart --kind StatefulSet --all

            -   name: Argo CD wait for healthy Backend
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app wait hermes-backend
                    options: --health -l 'argo-deploy=backend'

            -   name: Set up Kubectl
                uses: azure/setup-kubectl@776406bce94f63e41d621b960d78ee25c8b76ede # v4
                with:
                    version: 'v1.31.4' # Specify the kubectl version that you want to use

            -   name: Configure Kubernetes Context
                uses: azure/k8s-set-context@212a19233d93f03eceaac31ae5a1d1acf650b6ef # v4
                with:
                    method: kubeconfig
                    kubeconfig: '${{ secrets.KUBECONFIG }}'

            -   name: Wait for backend deployment to be ready
                run: kubectl rollout status deployment -l argo-deploy=backend -n hermes-backend --timeout=5m

            -   name: Run migrations
                run: kubectl exec -n hermes-backend -it deployment/backend -- bin/console doctrine:migrations:migrate --no-interaction -vv

    health-checks:
        runs-on: prezero-github-runner
        needs: [ deployment ]
        environment: dev
        steps:
            -   name: Check the deployed service URL
                uses: jtalk/url-health-check-action@b716ccb6645355dd9fcce8002ce460e5474f7f00 # v4
                with:
                    url: 'https://backend.dev.hermes.runs.onstackit.cloud/monitor/health/http_status_checks'
                    follow-redirect: false
                    max-attempts: 15 # Optional, defaults to 1
                    retry-delay: 5s # Optional, only applicable to max-attempts > 1
                    retry-all: true

    test:
        runs-on: prezero-github-runner
        needs: [ health-checks ]
        environment: dev
        steps:
            -   name: Set up Kubectl
                uses: azure/setup-kubectl@776406bce94f63e41d621b960d78ee25c8b76ede # v4
                with:
                    version: 'v1.31.4' # Specify the kubectl version that you want to use

            -   name: Configure Kubernetes Context
                uses: azure/k8s-set-context@212a19233d93f03eceaac31ae5a1d1acf650b6ef # v4
                with:
                    method: kubeconfig
                    kubeconfig: '${{ secrets.KUBECONFIG }}'

            -   name: Execute Tests
                run: kubectl exec -n hermes-backend deployment/backend -- bash ./bin/api-tests
