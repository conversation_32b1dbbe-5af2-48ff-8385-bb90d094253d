name: "Pull Request checks"

on:
    workflow_dispatch:
    push:
        branches:
            - master
    pull_request:
        types:
            - opened
            - edited
            - reopened
            - synchronize

env:
    PREZERO_NPM_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    COMPOSER_AUTH: '{"github-oauth":{"github.com":"${{ secrets.PREZERO_GITHUB_TOKEN }}"}}'
    SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
    SNYK_ORG: ${{ vars.SNYK_ORG }}
    SNYK_ODJ_PRODUCT_ID: ${{ vars.SNYK_ODJ_PRODUCT_ID }}
    SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

jobs:
    validate-pr-title:
        name: Validate PR title
        if: github.event_name == 'pull_request'
        runs-on: prezero-github-runner
        steps:
            - uses: amannn/action-semantic-pull-request@fdd4d3ddf614fbcd8c29e4b106d3bbe0cb2c605d # v6
              env:
                  GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}

    code-analysis:
        runs-on: prezero-github-runner
        if: github.event_name == 'pull_request'
        container:
            image: ghcr.io/prezero/hermes-backend-images/base-php-dev:4.11.0@sha256:8d87dc8f39efd35da7ce176a531abe1f5816bc51990f08177ee986e7cdfb5674
            credentials:
                username: ${{ github.actor }}
                password: ${{ secrets.PREZERO_GITHUB_TOKEN }}
        env:
            APP_ENV: localdev
        steps:
            -   name: Get project files
                uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
            -   name: Composer check platform requirements
                run: composer check-platform-reqs
            -   name: Validate composer.json
                run: composer validate --strict
            -   name: Install dependencies
                run: composer install
            -   name: Check coding standards
                run: vendor/bin/php-cs-fixer fix --diff --dry-run --using-cache=no
                env:
                    PHP_CS_FIXER_IGNORE_ENV: 1
            -   name: Static code analysis & Architecture validation
                run: vendor/bin/phpstan analyse --memory-limit=1G -v
            -   name: Security check
                run: composer audit
            -   name: Check workflow files
                run: |
                    bash <(curl https://raw.githubusercontent.com/rhysd/actionlint/main/scripts/download-actionlint.bash)
                    ./actionlint -color
                shell: bash
    code-security-quality:
        runs-on: prezero-github-runner
        continue-on-error: true
        steps:
            -   name: Get project files
                uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
                with:
                    fetch-depth: 0
            -   name: Snyk Scan Code
                if: ${{ !cancelled() && env.SNYK_TOKEN != null && env.SNYK_ORG != null && env.SNYK_ODJ_PRODUCT_ID != null }}
                continue-on-error: true
                env:
                    SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
                    SNYK_INTEGRATION_NAME: GITHUB_ACTIONS
                    SNYK_INTEGRATION_VERSION: php
                    PROJECT_NAME: ${{ github.event.repository.name }}-app
                    ORG_ID: ${{ vars.SNYK_ORG }}
                    PROJECT_TAGS: odj-type=code,odj-product=${{ vars.SNYK_ODJ_PRODUCT_ID }},odj-component=${{ github.event.repository.name }},odj-runenv-provider=stackit,build-id=${{github.run_number}},git-commit-id=${{ github.sha }}
                run: |
                    set -eux
                    export SNYK_DOING="test"
                    export SNYK_ARGS="--severity-threshold=low"
                    if [ "${{ github.ref}}" = "refs/heads/${{ github.event.repository.default_branch }}" ]; then
                      export SNYK_DOING="monitor"
                      export SNYK_ARGS=""
                    fi
                    snyk ${SNYK_DOING}  ${{ github.workspace }} ${SNYK_ARGS} --target-reference="${{ github.ref}}" --project-name=${PROJECT_NAME} --org=${ORG_ID} --package-manager=composer --file=${{ github.workspace }}/composer.lock --project-tags="${PROJECT_TAGS},build-date=$(date -Iseconds | sed 's/[+Z].*//')" --policy-path=${{ github.workspace }}/.snyk --project-lifecycle=production
            -   name: SonarQube Scan
                if: ${{ !cancelled() && env.SONAR_TOKEN != null && env.SONAR_HOST_URL != null && hashFiles('**/sonar-project.properties') != '' }}
                continue-on-error: true
                id: sonarqube-scan
                uses: SonarSource/sonarqube-scan-action@8c71dc039c2dd71d3821e89a2b58ecc7fee6ced9 # v5
                with:
                  args: -Dsonar.qualitygate.wait=true
                env:
                    SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
                    SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
