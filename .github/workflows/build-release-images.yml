on:
    workflow_dispatch:
        inputs:
            tag:
                description: 'Tag to apply to the generated docker images'
                required: false
                type: string

            release_created:
                description: 'Whether a release was created'
                required: true
                type: boolean

    workflow_call:
        inputs:
            tag:
                description: 'Tag to apply to the generated docker images'
                required: false
                type: string

            release_created:
                description: 'Whether a release was created'
                required: true
                type: boolean

name: Build images

env:
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    COMPOSER_AUTH: '{"github-oauth":{"github.com":"${{ secrets.PREZERO_GITHUB_TOKEN }}"}}'


jobs:
    build-image:
        if: ${{ inputs.release_created }}
        name: Build Container Image
        uses: prezero/workflows/.github/workflows/docker-build.yaml@97fee31f2416ee4224ca1436128639373e79ab92 # 1.34.1
        secrets: inherit
        with:
            ENV: ""
            DOCKERFILE: "docker/prod-k8s/Dockerfile"
            IMAGE: "ghcr.io/prezero/hermes-backend-k8s"
            TAG: ${{ inputs.tag }}
            TARGET_LAYER: ""
            RELEASE_CREATED: true
            BUILD_ARGS: ""
            # Re-enable when they release a new version
            enable_dockerfile_lint: "false"
